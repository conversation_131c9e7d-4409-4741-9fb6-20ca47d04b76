(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[2364],{48473:e=>{e.exports={en:["Real-time data for {symbolName}"],hu_HU:["Real-time data for {symbolName}"]}},84455:e=>{e.exports={en:["is provided by {exchange} exchange."],hu_HU:["is provided by {exchange} exchange."]}},97349:e=>{e.exports={en:["Fr"],hu_HU:["P"]}},30961:e=>{e.exports={en:["Mo"],hu_HU:["H"]}},94748:e=>{e.exports={en:["Sa"],hu_HU:["Szo"]}},75005:e=>{e.exports={en:["Su"],hu_HU:["V"]}},92578:e=>{e.exports={en:["We"],hu_HU:["Sze"]}},8765:e=>{e.exports={en:["Th"],hu_HU:["Cs"]}},9135:e=>{e.exports={en:["Tu"],hu_HU:["K"]}},43206:e=>{e.exports={en:["Could not get Pine source code."],hu_HU:["Nem kapható Pine forráskód."]}},65495:e=>{e.exports={en:["Collapse pane"],hu_HU:["Collapse pane"]}},81605:e=>{e.exports={en:["Confirm Remove Study Tree"],hu_HU:["Tanulmányfa Eltávolításának Jóváhagyása"]}},40225:e=>{e.exports={en:["Continuous futures contracts"],hu_HU:["Continuous futures contracts"]}},78162:e=>{e.exports={en:["Continuous futures contracts are synthetic instruments combining individual contracts. The 1! contract represents the front-month (nearest expiration) while the 2! represents the second nearest expiration."],hu_HU:["Continuous futures contracts are synthetic instruments combining individual contracts. The 1! contract represents the front-month (nearest expiration) while the 2! represents the second nearest expiration."]}},58796:e=>{e.exports={en:["Cboe One"],hu_HU:["Cboe One"]}},29151:e=>{e.exports={en:["Change description"],hu_HU:["Change description"]}},63245:e=>{e.exports={en:["Change symbol"],hu_HU:["Szimbólum módosítása"]}},45639:e=>{e.exports={en:["Chart values"],hu_HU:["Chart values"]}},28214:e=>{e.exports={en:["Create a free account"],hu_HU:["Create a free account"]}},53357:e=>{e.exports={en:["All's well — Market is open."],hu_HU:["All's well — Market is open."]}},28896:e=>{e.exports={en:["April"],hu_HU:["Április"]}},11081:e=>{e.exports={en:["August"],hu_HU:["Augusztus"]}},10842:e=>{e.exports={en:["Bar change values"],hu_HU:["Bar Change Values"]}},70032:e=>{e.exports={en:["Buy real-time data"],hu_HU:["Buy real-time data"]}},54480:e=>{e.exports={en:["Go to Editor"],hu_HU:["Go to Editor"]}},77174:e=>{e.exports={en:["Do you really want to delete study and all of it's children?"],hu_HU:["Biztos, hogy törölni akarod a tanulmányt?"]}},13930:e=>{e.exports={en:["Double click"],hu_HU:["Double click"]}},78992:e=>{e.exports={en:["Data error"],hu_HU:["Data error"]}},32925:e=>{e.exports={en:["Data is updated once a day."],hu_HU:["Data is updated once a day."]}},33039:e=>{e.exports={en:["Data is updated once per second, even if there are more updates on the market."],hu_HU:["Data is updated once per second, even if there are more updates on the market."]}},43348:e=>{e.exports={en:["Data is delayed"],hu_HU:["Data is delayed"]}},38368:e=>{e.exports={en:["Data on our Basic plan is updated once per second, even if there are more updates on the market."],
hu_HU:["Data on our Basic plan is updated once per second, even if there are more updates on the market."]}},90082:e=>{e.exports={en:["December"],hu_HU:["December"]}},66260:e=>{e.exports={en:["Delete pane"],hu_HU:["Delete pane"]}},54602:e=>{e.exports={en:["Delisted"],hu_HU:["Delisted"]}},31683:e=>{e.exports={en:["Delisted alert"],hu_HU:["Delisted alert"]}},50035:e=>{e.exports={en:["Derived Data"],hu_HU:["Derived Data"]}},45321:e=>{e.exports={en:["Derived Data refers to financial indicators which are created by combining and/or processing raw data supplied from various sources."],hu_HU:["Derived Data refers to financial indicators which are created by combining and/or processing raw data supplied from various sources."]}},5805:e=>{e.exports={en:["End of day data"],hu_HU:["End of day data"]}},57335:e=>{e.exports={en:["Error"],hu_HU:["Hiba"]}},23302:e=>{e.exports={en:["Evening. Market is open for post-market trading."],hu_HU:["Evening. Market is open for post-market trading."]}},63538:e=>{e.exports={en:["Exchange timezone"],hu_HU:["Exchange timezone"]}},81069:e=>{e.exports={en:["February"],hu_HU:["Február"]}},5447:e=>{e.exports={en:["Fill out Exchange Agreements"],hu_HU:["Fill out Exchange Agreements"]}},44454:e=>{e.exports={en:["Flag Symbol"],hu_HU:["Flag Symbol"]}},22928:e=>{e.exports={en:["Fri"],hu_HU:["Pén"]}},3570:e=>{e.exports={en:["Friday"],hu_HU:["Péntek"]}},87845:e=>{e.exports={en:["Holiday"],hu_HU:["Holiday"]}},75119:e=>{e.exports={en:["Halal symbol"],hu_HU:["Halal symbol"]}},44036:e=>{e.exports={en:["Indicator arguments"],hu_HU:["Indikátor Argumentumok"]}},7511:e=>{e.exports={en:["Indicator titles"],hu_HU:["Indikátor Címkék"]}},51353:e=>{e.exports={en:["Indicator values"],hu_HU:["Indikátor Értékek"]}},95400:e=>{e.exports={en:["If you'd like {listedExchange} real-time data you'll need to complete an Exchange Agreement. Don't worry, it only takes a few clicks"],hu_HU:["If you'd like {listedExchange} real-time data you'll need to complete an Exchange Agreement. Don't worry, it only takes a few clicks"]}},81509:e=>{e.exports={en:["It'll go to post-market trading in {remainingTime}."],hu_HU:["It'll go to post-market trading in {remainingTime}."]}},58470:e=>{e.exports={en:["It'll open for pre-market trading in {remainingTime}."],hu_HU:["It'll open for pre-market trading in {remainingTime}."]}},200:e=>{e.exports={en:["January"],hu_HU:["Január"]}},6608:e=>{e.exports={en:["July"],hu_HU:["Július"]}},61487:e=>{e.exports={en:["June"],hu_HU:["Június"]}},91006:e=>{e.exports={en:["One update per second"],hu_HU:["One update per second"]}},37997:e=>{e.exports={en:["October"],hu_HU:["Október"]}},25765:e=>{e.exports={en:["Open market status"],hu_HU:["Open market status"]}},54316:e=>{e.exports={en:["Opened in Editor"],hu_HU:["Opened in Editor"]}},28632:e=>{e.exports={en:["Opened in detached Editor"],hu_HU:["Opened in detached Editor"]}},72423:e=>{e.exports={en:["Last day change values"],hu_HU:["Last day change values"]}},27741:e=>{e.exports={en:["Learn more"],hu_HU:["Tudj meg többet"]}},74079:e=>{e.exports={
en:["Move pane down"],hu_HU:["Move pane down"]}},7310:e=>{e.exports={en:["Move pane up"],hu_HU:["Move pane up"]}},37150:e=>{e.exports={en:["Mon"],hu_HU:["Hét"]}},19573:e=>{e.exports={en:["Monday"],hu_HU:["Hétfő"]}},37117:e=>{e.exports={en:["More"],hu_HU:["Több"]}},65420:e=>{e.exports={en:["Morning. Market is open for pre-market trading."],hu_HU:["Morning. Market is open for pre-market trading."]}},61206:e=>{e.exports={en:["Maximize chart"],hu_HU:["Maximize chart"]}},90165:e=>{e.exports={en:["Maximize pane"],hu_HU:["Maximize pane"]}},25734:e=>{e.exports={en:["May"],hu_HU:["Május"]}},75018:e=>{e.exports={en:["Manage panes"],hu_HU:["Manage panes"]}},93878:e=>{e.exports={en:["March"],hu_HU:["Március"]}},80086:e=>{e.exports={en:["Market open"],hu_HU:["Market open"]}},5371:e=>{e.exports={en:["Market opens in {remainingTime}."],hu_HU:["Market opens in {remainingTime}."]}},62464:e=>{e.exports={en:["Market closed"],hu_HU:["Market closed"]}},18643:e=>{e.exports={en:["Market closes in {remainingTime}."],hu_HU:["Market closes in {remainingTime}."]}},41392:e=>{e.exports={en:["Market is currently on holiday. Lucky them."],hu_HU:["Market is currently on holiday. Lucky them."]}},4607:e=>{e.exports={en:["November"],hu_HU:["November"]}},87142:e=>{e.exports={en:["Source code"],hu_HU:["Forráskód"]}},32273:e=>{e.exports={en:["Sat"],hu_HU:["Szom"]}},30348:e=>{e.exports={en:["Saturday"],hu_HU:["Szombat"]}},90761:e=>{e.exports={en:["Scroll to the left"],hu_HU:["Scroll to the left"]}},83040:e=>{e.exports={en:["Scroll to the most recent bar"],hu_HU:["Scroll to the most recent bar"]}},25131:e=>{e.exports={en:["Scroll to the right"],hu_HU:["Scroll to the right"]}},32179:e=>{e.exports={en:["September"],hu_HU:["Szeptember"]}},85786:e=>{e.exports={en:["Show Object Tree"],hu_HU:["Show Object Tree"]}},74759:e=>{e.exports={en:["Show interval settings"],hu_HU:["Show interval settings"]}},86158:e=>{e.exports={en:["Study Error"],hu_HU:["Study Error"]}},77493:e=>{e.exports={en:["Sun"],hu_HU:["Vas"]}},61480:e=>{e.exports={en:["Sunday"],hu_HU:["Vasárnap"]}},23079:e=>{e.exports={en:["Symbol price source"],hu_HU:["Symbol price source"]}},14771:e=>{e.exports={en:["Symbol title"],hu_HU:["Symbol title"]}},44138:e=>{e.exports={en:["Synthetic symbol"],hu_HU:["Synthetic symbol"]}},73897:e=>{e.exports={en:["Post-market"],hu_HU:["Post-market"]}},85996:e=>{e.exports={en:["Paid plans feature faster data updates."],hu_HU:["Paid plans feature faster data updates."]}},36018:e=>{e.exports={en:["Pre-market"],hu_HU:["Pre-market"]}},94972:e=>{e.exports={en:["Primary listing"],hu_HU:["Primary listing"]}},20987:e=>{e.exports={en:["Real-time data for this symbol is not supported right now. We may support it in the future."],hu_HU:["Real-time data for this symbol is not supported right now. We may support it in the future."]}},31539:e=>{e.exports={en:["Real-time data for {symbolName} is provided by {exchange} exchange."],hu_HU:["Real-time data for {symbolName} is provided by {exchange} exchange."]}},31142:e=>{e.exports={en:["Restore chart"],hu_HU:["Restore chart"]}},12486:e=>{
e.exports={en:["Restore pane"],hu_HU:["Restore pane"]}},11532:e=>{e.exports={en:["Wed"],hu_HU:["Szer"]}},94226:e=>{e.exports={en:["Wednesday"],hu_HU:["Szerda"]}},7281:e=>{e.exports={en:["To get real-time data for {description}, please buy the real-time data package."],hu_HU:["To get real-time data for {description}, please buy the real-time data package."]}},71388:e=>{e.exports={en:["Thu"],hu_HU:["Cs"]}},79137:e=>{e.exports={en:["Thursday"],hu_HU:["Csütörtök"]}},95246:e=>{e.exports={en:["The main, or first, stock exchange where a company's stock is listed and traded."],hu_HU:["The main, or first, stock exchange where a company's stock is listed and traded."]}},25608:e=>{e.exports={en:["The source code of this script version is open in the Pine Editor."],hu_HU:["The source code of this script version is open in the Pine Editor."]}},33161:e=>{e.exports={en:["The source code of this script version is open in the detached Pine Editor."],hu_HU:["The source code of this script version is open in the detached Pine Editor."]}},24669:e=>{e.exports={en:["This data is real-time, but it may be slightly different to its official counterpart coming from primary exchanges."],hu_HU:["This data is real-time, but it may be slightly different to its official counterpart coming from primary exchanges."]}},52668:e=>{e.exports={en:["This data is real-time, but it may be slightly different to its official counterpart coming from {exchange}."],hu_HU:["This data is real-time, but it may be slightly different to its official counterpart coming from {exchange}."]}},67607:e=>{e.exports={en:["This is a shariah-compliant stock, meaning that it follows Islamic law. This company does not charge or receive interest, and does not engage with certain sectors (gambling, alcohol, tobacco, pork products)."],hu_HU:["This is a shariah-compliant stock, meaning that it follows Islamic law. This company does not charge or receive interest, and does not engage with certain sectors (gambling, alcohol, tobacco, pork products)."]}},83556:e=>{e.exports={en:["This is no longer publicly trading so no new data will be added. But you can explore the historicals here."],hu_HU:["This is no longer publicly trading so no new data will be added. But you can explore the historicals here."]}},44492:e=>{e.exports={en:["This real-time data is provided by {originalExchange} exchange. It may be slightly different from the official data directly from {exchange}. If this difference is crucial for you, you'll need to purchase real-time data from the primary exchange, which we can help with."],hu_HU:["This real-time data is provided by {originalExchange} exchange. It may be slightly different from the official data directly from {exchange}. If this difference is crucial for you, you'll need to purchase real-time data from the primary exchange, which we can help with."]}},53272:e=>{e.exports={en:["This symbol doesn't exist, please pick another one."],hu_HU:["This symbol doesn't exist, please pick another one."]}},90589:e=>{e.exports={
en:["This symbol is calculated by TradingView using the rate from other exchanges."],hu_HU:["This symbol is calculated by TradingView using the rate from other exchanges."]}},52176:e=>{e.exports={en:["Time for a walk — this market is closed."],hu_HU:["Time for a walk — this market is closed."]}},11916:e=>{e.exports={en:["Tue"],hu_HU:["Ke"]}},82160:e=>{e.exports={en:["Tuesday"],hu_HU:["Kedd"]}},13865:e=>{e.exports={en:["Unflag Symbol"],hu_HU:["Unflag Symbol"]}},37644:e=>{e.exports={en:["Volume"],hu_HU:["Volumen"]}},97038:e=>{e.exports={en:["Zoom in"],hu_HU:["Nagyítás"]}},88710:e=>{e.exports={en:["Zoom out"],hu_HU:["Kicsinyítés"]}},96227:e=>{e.exports={en:["change open market status visibility"],hu_HU:["change open market status visibility"]}},27426:e=>{e.exports={en:["change bar change visibility"],hu_HU:["change bar change visibility"]}},79637:e=>{e.exports={en:["change chart values visibility"],hu_HU:["change chart values visibility"]}},63050:e=>{e.exports={en:["change indicator titles visibility"],hu_HU:["change indicator titles visibility"]}},49583:e=>{e.exports={en:["change indicator values visibility"],hu_HU:["change indicator values visibility"]}},78310:e=>{e.exports={en:["change indicator arguments visibility"],hu_HU:["change indicator arguments visibility"]}},66307:e=>{e.exports={en:["change last day change visibility"],hu_HU:["change last day change visibility"]}},88167:e=>{e.exports={en:["change symbol description visibility"],hu_HU:["change symbol description visibility"]}},12050:e=>{e.exports={en:["change symbol field visibility"],hu_HU:["change symbol field visibility"]}},96201:e=>{e.exports={en:["change volume values visibility"],hu_HU:["change volume values visibility"]}},59938:e=>{e.exports={en:["less than 1 minute"],hu_HU:["less than 1 minute"]}},51382:e=>{e.exports={en:["show {title}"],hu_HU:["show {title}"]}},51320:e=>{e.exports={en:["{days} and {hours}"],hu_HU:["{days} and {hours}"]}},55154:e=>{e.exports={en:["{exchange} by {originalExchange}"],hu_HU:["{exchange} by {originalExchange}"]}},83187:e=>{e.exports={en:["{hours} and {minutes}"],hu_HU:["{hours} and {minutes}"]}},51211:e=>{e.exports={en:["{listedExchange} real-time data is available for free to registered users."],hu_HU:["{listedExchange} real-time data is available for free to registered users."]}},89142:e=>{e.exports={en:["{symbolName} data is delayed by {time} minutes because of exchange requirements."],hu_HU:["{symbolName} data is delayed by {time} minutes because of exchange requirements."]}},51931:e=>{e.exports={en:["Data is updated once every {amount} second, even if there are more updates on the market.","Data is updated once every {amount} seconds, even if there are more updates on the market."],hu_HU:["Data is updated once every {amount} seconds, even if there are more updates on the market."]}},83978:e=>{e.exports={
en:["Data on our Basic plan is updated once every {amount} second, even if there are more updates on the market.","Data on our Basic plan is updated once every {amount} seconds, even if there are more updates on the market."],hu_HU:["Data on our Basic plan is updated once every {amount} seconds, even if there are more updates on the market."]}},46960:e=>{e.exports={en:["Hide indicator legend","Hide indicators legend"],hu_HU:["Hide indicators legend"]}},36050:e=>{e.exports={en:["One update every {amount} second","One update every {amount} seconds"],hu_HU:["One update every {amount} seconds"]}},36553:e=>{e.exports={en:["Show indicator legend","Show indicators legend"],hu_HU:["Show indicators legend"]}},39501:e=>{e.exports={en:["{number} day","{number} days"],hu_HU:["{number} days"]}},44646:e=>{e.exports={en:["{number} hour","{number} hours"],hu_HU:["{number} hours"]}},32547:e=>{e.exports={en:["{number} minute","{number} minutes"],hu_HU:["{number} minutes"]}}}]);