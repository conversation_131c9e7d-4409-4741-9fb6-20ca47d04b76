(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[101],{23970:e=>{e.exports={en:["days"],hu_HU:["nap"]}},35157:e=>{e.exports={en:["hours"],hu_HU:["óra"]}},18193:e=>{e.exports={en:["months"],hu_HU:["hónap"]}},79930:e=>{e.exports={en:["minutes"],hu_HU:["perc"]}},7938:e=>{e.exports={en:["weeks"],hu_HU:["hét"]}},4543:e=>{e.exports={en:["Cancel"],hu_HU:["Törlés"]}},47742:e=>{e.exports={en:["Close menu"],hu_HU:["Close menu"]}},42790:e=>{e.exports={en:["Add"],hu_HU:["Hozzáad"]}},17854:e=>{e.exports={en:["Interval"],hu_HU:["Időköz"]}},17889:e=>{e.exports={en:["Interval already exists, please use a different value"],hu_HU:["Interval already exists, please use a different value"]}},23323:e=>{e.exports={en:["Interval value is too big, please try again"],hu_HU:["Interval value is too big, please try again"]}},19295:e=>{e.exports={en:["Ok"],hu_HU:["Oké"]}},98413:e=>{e.exports={en:["Type"],hu_HU:["Típus"]}}}]);