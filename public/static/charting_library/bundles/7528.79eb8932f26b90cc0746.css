.nav-button-znwuaSC1 {
  border: none;
  box-sizing: initial;
  display: flex;
  outline: none;
  padding: 0;
  position: relative;
  --ui-lib-nav-button-default-color-content: var(
    --themed-color-text-primary,
    #131722
  );
  --ui-lib-nav-button-default-color-bg: #0000;
  background: #0000;
  color: var(
    --ui-lib-nav-button-color-content,
    var(--ui-lib-nav-button-default-color-content)
  );
}
html.theme-dark .nav-button-znwuaSC1 {
  --ui-lib-nav-button-default-color-content: var(
    --themed-color-text-primary,
    #d1d4dc
  );
}
.nav-button-znwuaSC1.link-znwuaSC1 {
  cursor: pointer;
}
@media (any-hover: hover) {
  .nav-button-znwuaSC1:hover {
    --ui-lib-nav-button-default-color-content: var(
      --themed-color-close-button-hover-text,
      #131722
    );
    --ui-lib-nav-button-default-color-bg: var(
      --themed-color-close-button-hover-bg,
      #f0f3fa
    );
  }
  html.theme-dark .nav-button-znwuaSC1:hover {
    --ui-lib-nav-button-default-color-bg: var(
      --themed-color-close-button-hover-bg,
      #2a2e39
    );
    --ui-lib-nav-button-default-color-content: var(
      --themed-color-close-button-hover-text,
      #fff
    );
  }
}
.nav-button-znwuaSC1:active {
  --ui-lib-nav-button-default-color-content: var(
    --themed-color-close-button-active-text,
    #131722
  );
  --ui-lib-nav-button-default-color-bg: var(
    --themed-color-close-button-active-bg,
    #e0e3eb
  );
}
html.theme-dark .nav-button-znwuaSC1:active {
  --ui-lib-nav-button-default-color-bg: var(
    --themed-color-close-button-active-bg,
    #363a45
  );
  --ui-lib-nav-button-default-color-content: var(
    --themed-color-close-button-active-text,
    #fff
  );
}
.nav-button-znwuaSC1:focus .background-znwuaSC1:after {
  display: block;
}
.nav-button-znwuaSC1:focus-visible .background-znwuaSC1:after {
  display: block;
}
.nav-button-znwuaSC1:focus:not(:focus-visible) .background-znwuaSC1:after {
  display: none;
}
.nav-button-znwuaSC1:disabled,
.nav-button-znwuaSC1[aria-disabled="true"] {
  --ui-lib-nav-button-default-color-content: var(
    --themed-color-content-disabled,
    #b2b5be
  );
  cursor: default;
}
html.theme-dark .nav-button-znwuaSC1:disabled,
html.theme-dark .nav-button-znwuaSC1[aria-disabled="true"] {
  --ui-lib-nav-button-default-color-content: var(
    --themed-color-content-disabled,
    #5d606b
  );
}
.nav-button-znwuaSC1:disabled .background-znwuaSC1,
.nav-button-znwuaSC1[aria-disabled="true"] .background-znwuaSC1 {
  display: none;
}
.background-znwuaSC1 {
  --ui-lib-nav-button-color-outline-default: #2962ff;
  background: var(
    --ui-lib-nav-button-color-bg,
    var(--ui-lib-nav-button-default-color-bg)
  );
  display: block;
  outline: none;
  overflow: visible;
  position: absolute;
}
.background-znwuaSC1:focus {
  outline: none;
}
.background-znwuaSC1:focus-visible {
  outline: none;
}
.background-znwuaSC1:after {
  border-color: var(
    --ui-lib-nav-button-color-outline,
    var(--ui-lib-nav-button-color-outline-default)
  );
  border-style: solid;
  border-width: 2px;
  box-sizing: border-box;
  content: "";
  display: none;
  height: calc(100% + 8px);
  left: -4px;
  pointer-events: none;
  position: absolute;
  top: -4px;
  width: calc(100% + 8px);
  z-index: 1;
}
.icon-znwuaSC1 {
  z-index: 0;
}
.icon-znwuaSC1,
.icon-znwuaSC1 svg {
  display: flex;
}
.flip-icon-znwuaSC1,
.flip-icon-znwuaSC1 svg {
  transform: rotate(90deg);
}
.size-large-znwuaSC1 {
  height: 24px;
  width: 24px;
  --ui-lib-nav-button-basic-offset: 8px;
  --ui-lib-nav-button-safe-offset: 12px;
}
.size-large-znwuaSC1.preserve-paddings-znwuaSC1 {
  padding: 8px;
}
.size-large-znwuaSC1 .background-znwuaSC1 {
  border-radius: 8px;
  height: calc(100% + 16px);
  left: -8px;
  top: -8px;
  width: calc(100% + 16px);
}
.size-large-znwuaSC1 .background-znwuaSC1:after {
  border-radius: 12px;
}
.size-large-znwuaSC1.preserve-paddings-znwuaSC1 .background-znwuaSC1 {
  height: 100%;
  left: 0;
  top: 0;
  width: 100%;
}
.size-medium-znwuaSC1 {
  height: 18px;
  width: 18px;
  --ui-lib-nav-button-basic-offset: 8px;
  --ui-lib-nav-button-safe-offset: 12px;
}
.size-medium-znwuaSC1.preserve-paddings-znwuaSC1 {
  padding: 8px;
}
.size-medium-znwuaSC1 .background-znwuaSC1 {
  border-radius: 8px;
  height: calc(100% + 16px);
  left: -8px;
  top: -8px;
  width: calc(100% + 16px);
}
.size-medium-znwuaSC1 .background-znwuaSC1:after {
  border-radius: 12px;
}
.size-medium-znwuaSC1.preserve-paddings-znwuaSC1 .background-znwuaSC1 {
  height: 100%;
  left: 0;
  top: 0;
  width: 100%;
}
.size-small-znwuaSC1 {
  height: 14px;
  width: 14px;
  --ui-lib-nav-button-basic-offset: 8px;
  --ui-lib-nav-button-safe-offset: 12px;
}
.size-small-znwuaSC1.preserve-paddings-znwuaSC1 {
  padding: 8px;
}
.size-small-znwuaSC1 .background-znwuaSC1 {
  border-radius: 6px;
  height: calc(100% + 16px);
  left: -8px;
  top: -8px;
  width: calc(100% + 16px);
}
.size-small-znwuaSC1 .background-znwuaSC1:after {
  border-radius: 10px;
}
.size-small-znwuaSC1.preserve-paddings-znwuaSC1 .background-znwuaSC1 {
  height: 100%;
  left: 0;
  top: 0;
  width: 100%;
}
.size-xsmall-znwuaSC1 {
  height: 12px;
  width: 12px;
  --ui-lib-nav-button-basic-offset: 4px;
  --ui-lib-nav-button-safe-offset: 8px;
}
.size-xsmall-znwuaSC1.preserve-paddings-znwuaSC1 {
  padding: 4px;
}
.size-xsmall-znwuaSC1 .background-znwuaSC1 {
  border-radius: 4px;
  height: calc(100% + 8px);
  left: -4px;
  top: -4px;
  width: calc(100% + 8px);
}
.size-xsmall-znwuaSC1 .background-znwuaSC1:after {
  border-radius: 8px;
}
.size-xsmall-znwuaSC1.preserve-paddings-znwuaSC1 .background-znwuaSC1 {
  height: 100%;
  left: 0;
  top: 0;
  width: 100%;
}
.size-xxsmall-znwuaSC1 {
  height: 10px;
  width: 10px;
  --ui-lib-nav-button-basic-offset: 4px;
  --ui-lib-nav-button-safe-offset: 8px;
}
.size-xxsmall-znwuaSC1.preserve-paddings-znwuaSC1 {
  padding: 4px;
}
.size-xxsmall-znwuaSC1 .background-znwuaSC1 {
  border-radius: 4px;
  height: calc(100% + 8px);
  left: -4px;
  top: -4px;
  width: calc(100% + 8px);
}
.size-xxsmall-znwuaSC1 .background-znwuaSC1:after {
  border-radius: 8px;
}
.size-xxsmall-znwuaSC1.preserve-paddings-znwuaSC1 .background-znwuaSC1 {
  height: 100%;
  left: 0;
  top: 0;
  width: 100%;
}
.visually-hidden-znwuaSC1 {
  border: 0;
  height: 1px;
  margin: -1px;
  padding: 0;
  position: absolute;
  width: 1px;
  clip: rect(0 0 0 0);
  overflow: hidden;
}
