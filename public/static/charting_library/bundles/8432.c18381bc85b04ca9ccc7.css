.button-iLKiGOdQ {
  align-items: center;
  background-color: var(--tv-list-item-button-background-color);
  border-radius: 4px;
  color: var(--tv-color-popup-element-toolbox-text, #787b86);
  display: inline-flex;
  font-size: 0;
  height: 22px;
  justify-content: center;
  min-width: 22px;
  width: 22px;
}
.button-iLKiGOdQ.hovered-iLKiGOdQ,
.button-iLKiGOdQ:active {
  background-color: var(
    --tv-color-popup-element-toolbox-background-hover,
    var(
      --tv-list-item-button-background-hover-color,
      var(--themed-color-popup-element-toolbox-background-hover, #e0e3eb)
    )
  );
  color: var(
    --tv-color-popup-element-toolbox-text-hover,
    var(--themed-color-popup-element-toolbox-text-hover, #131722)
  );
}
@media (any-hover: hover) {
  .button-iLKiGOdQ:hover {
    background-color: var(
      --tv-color-popup-element-toolbox-background-hover,
      var(
        --tv-list-item-button-background-hover-color,
        var(--themed-color-popup-element-toolbox-background-hover, #e0e3eb)
      )
    );
    color: var(
      --tv-color-popup-element-toolbox-text-hover,
      var(--themed-color-popup-element-toolbox-text-hover, #131722)
    );
  }
}
html.theme-dark .button-iLKiGOdQ.hovered-iLKiGOdQ,
html.theme-dark .button-iLKiGOdQ:active {
  background-color: var(
    --tv-color-popup-element-toolbox-background-hover,
    var(
      --tv-list-item-button-background-hover-color,
      var(--themed-color-popup-element-toolbox-background-hover, #363a45)
    )
  );
}
@media (any-hover: hover) {
  html.theme-dark .button-iLKiGOdQ:hover {
    background-color: var(
      --tv-color-popup-element-toolbox-background-hover,
      var(
        --tv-list-item-button-background-hover-color,
        var(--themed-color-popup-element-toolbox-background-hover, #363a45)
      )
    );
  }
}
html.theme-dark .button-iLKiGOdQ.hovered-iLKiGOdQ,
html.theme-dark .button-iLKiGOdQ:active {
  color: var(
    --tv-color-popup-element-toolbox-text-hover,
    var(--themed-color-popup-element-toolbox-text-hover, #d1d4dc)
  );
}
@media (any-hover: hover) {
  html.theme-dark .button-iLKiGOdQ:hover {
    color: var(
      --tv-color-popup-element-toolbox-text-hover,
      var(--themed-color-popup-element-toolbox-text-hover, #d1d4dc)
    );
  }
}
.button-iLKiGOdQ.disabled-iLKiGOdQ,
.button-iLKiGOdQ.disabled-iLKiGOdQ:active {
  background-color: var(
    --tv-list-item-button-disabled-background-color,
    var(--themed-color-force-transparent, #0000)
  );
}
@media (any-hover: hover) {
  .button-iLKiGOdQ.disabled-iLKiGOdQ:hover {
    background-color: var(
      --tv-list-item-button-disabled-background-color,
      var(--themed-color-force-transparent, #0000)
    );
  }
}
html.theme-dark .button-iLKiGOdQ.disabled-iLKiGOdQ,
html.theme-dark .button-iLKiGOdQ.disabled-iLKiGOdQ:active {
  background-color: var(
    --tv-list-item-button-disabled-background-color,
    var(--themed-color-force-transparent, #0000)
  );
}
@media (any-hover: hover) {
  html.theme-dark .button-iLKiGOdQ.disabled-iLKiGOdQ:hover {
    background-color: var(
      --tv-list-item-button-disabled-background-color,
      var(--themed-color-force-transparent, #0000)
    );
  }
}
.button-iLKiGOdQ.active-iLKiGOdQ,
html.theme-dark .button-iLKiGOdQ.active-iLKiGOdQ {
  color: var(
    --tv-color-popup-element-toolbox-text-active-hover,
    var(--themed-color-popup-element-toolbox-text-active-hover, #90bff9)
  );
}
.button-iLKiGOdQ.active-iLKiGOdQ.hovered-iLKiGOdQ,
.button-iLKiGOdQ.active-iLKiGOdQ:active {
  background-color: var(
    --tv-color-popup-element-toolbox-background-active-hover,
    var(--themed-color-popup-element-toolbox-background-active-hover, #1848cc)
  );
}
@media (any-hover: hover) {
  .button-iLKiGOdQ.active-iLKiGOdQ:hover {
    background-color: var(
      --tv-color-popup-element-toolbox-background-active-hover,
      var(--themed-color-popup-element-toolbox-background-active-hover, #1848cc)
    );
  }
}
html.theme-dark .button-iLKiGOdQ.active-iLKiGOdQ.hovered-iLKiGOdQ,
html.theme-dark .button-iLKiGOdQ.active-iLKiGOdQ:active {
  background-color: var(
    --tv-color-popup-element-toolbox-background-active-hover,
    var(--themed-color-popup-element-toolbox-background-active-hover, #1848cc)
  );
}
@media (any-hover: hover) {
  html.theme-dark .button-iLKiGOdQ.active-iLKiGOdQ:hover {
    background-color: var(
      --tv-color-popup-element-toolbox-background-active-hover,
      var(--themed-color-popup-element-toolbox-background-active-hover, #1848cc)
    );
  }
}
.hidden-iLKiGOdQ {
  visibility: hidden;
}
