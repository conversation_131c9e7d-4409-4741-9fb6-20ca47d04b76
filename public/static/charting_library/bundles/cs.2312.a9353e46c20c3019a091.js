(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[2312],{53310:e=>{e.exports={en:["Re"],cs:["Re"]}},94073:e=>{e.exports={en:["A"],cs:["A"]}},66384:e=>{e.exports={en:["L"],cs:["L"]}},85119:e=>{e.exports={en:["Dark"],cs:["Dark"]}},96870:e=>{e.exports={en:["Light"],cs:["Light"]}},85886:e=>{e.exports={en:["d"],cs:["d"]}},44634:e=>{e.exports={en:["h"],cs:["h"]}},5977:e=>{e.exports={en:["m"],cs:["m"]}},21492:e=>{e.exports={en:["s"],cs:["s"]}},97559:e=>{e.exports={en:["{title} copy"],cs:["{title} copy"]}},38691:e=>{e.exports={en:["D"],cs:["D"]}},77995:e=>{e.exports={en:["M"],cs:["M"]}},93934:e=>{e.exports={en:["R"],cs:["R"]}},82901:e=>{e.exports={en:["T"],cs:["T"]}},7408:e=>{e.exports={en:["W"],cs:["W"]}},38048:e=>{e.exports={en:["h"],cs:["h"]}},68430:e=>{e.exports={en:["m"],cs:["m"]}},68823:e=>{e.exports={en:["s"],cs:["s"]}},2696:e=>{e.exports={en:["C"],cs:["Z"]}},43253:e=>{e.exports={en:["H"],cs:["V"]}},61372:e=>{e.exports={en:["HL2"],cs:["HL2"]}},55096:e=>{e.exports={en:["HLC3"],cs:["HLC3"]}},94174:e=>{e.exports={en:["OHLC4"],cs:["OHLC4"]}},89923:e=>{e.exports={en:["L"],cs:["N"]}},46728:e=>{e.exports={en:["O"],cs:["O"]}},32856:e=>{e.exports=Object.create(null),e.exports.Close_input={en:["Close"],cs:["Close"]},e.exports.Back_input={en:["Back"],cs:["Back"]},e.exports.Minimize_input={en:["Minimize"],cs:["Minimize"]},e.exports["Hull MA_input"]={en:["Hull MA"],cs:["Hull MA"]},e.exports.from_input={en:["from"],cs:["from"]},e.exports.to_input={en:["to"],cs:["to"]},e.exports["{number} item_combobox_input"]={en:["{number} item","{number} items"],cs:["{number} item","{number} items"]},e.exports.Length_input={en:["Length"],cs:["Length"]},e.exports.Plot_input={en:["Plot"],cs:["Plot"]},e.exports.Zero_input={en:["Zero"],cs:["Zero"]},e.exports.Signal_input={en:["Signal"],cs:["Signal"]},e.exports.Long_input={en:["Long"],cs:["Long"]},e.exports.Short_input={en:["Short"],cs:["Short"]},e.exports.UpperLimit_input={en:["UpperLimit"],cs:["UpperLimit"]},e.exports.LowerLimit_input={en:["LowerLimit"],cs:["LowerLimit"]},e.exports.Offset_input={en:["Offset"],cs:["Offset"]},e.exports.length_input={en:["length"],cs:["length"]},e.exports.mult_input={en:["mult"],cs:["mult"]},e.exports.short_input={en:["short"],cs:["short"]},e.exports.long_input={en:["long"],cs:["long"]},e.exports.Limit_input={en:["Limit"],cs:["Limit"]},e.exports.Move_input={en:["Move"],cs:["Move"]},e.exports.Value_input={en:["Value"],cs:["Value"]},e.exports.Method_input={en:["Method"],cs:["Method"]},e.exports["Values in status line_input"]={en:["Values in status line"],cs:["Values in status line"]},e.exports["Labels on price scale_input"]={en:["Labels on price scale"],cs:["Labels on price scale"]},e.exports["Accumulation/Distribution_input"]={en:["Accumulation/Distribution"],cs:["Accumulation/Distribution"]},e.exports.ADR_B_input={en:["ADR_B"],cs:["ADR_B"]},e.exports["Equality Line_input"]={en:["Equality Line"],cs:["Equality Line"]},e.exports["Window Size_input"]={en:["Window Size"],cs:["Window Size"]},e.exports.Sigma_input={en:["Sigma"],cs:["Sigma"]},
e.exports["Aroon Up_input"]={en:["Aroon Up"],cs:["Aroon Up"]},e.exports["Aroon Down_input"]={en:["Aroon Down"],cs:["Aroon Down"]},e.exports.Upper_input={en:["Upper"],cs:["Upper"]},e.exports.Lower_input={en:["Lower"],cs:["Lower"]},e.exports.Deviation_input={en:["Deviation"],cs:["Deviation"]},e.exports["Levels Format_input"]={en:["Levels Format"],cs:["Levels Format"]},e.exports["Labels Position_input"]={en:["Labels Position"],cs:["Labels Position"]},e.exports["0 Level Color_input"]={en:["0 Level Color"],cs:["0 Level Color"]},e.exports["0.236 Level Color_input"]={en:["0.236 Level Color"],cs:["0.236 Level Color"]},e.exports["0.382 Level Color_input"]={en:["0.382 Level Color"],cs:["0.382 Level Color"]},e.exports["0.5 Level Color_input"]={en:["0.5 Level Color"],cs:["0.5 Level Color"]},e.exports["0.618 Level Color_input"]={en:["0.618 Level Color"],cs:["0.618 Level Color"]},e.exports["0.65 Level Color_input"]={en:["0.65 Level Color"],cs:["0.65 Level Color"]},e.exports["0.786 Level Color_input"]={en:["0.786 Level Color"],cs:["0.786 Level Color"]},e.exports["1 Level Color_input"]={en:["1 Level Color"],cs:["1 Level Color"]},e.exports["1.272 Level Color_input"]={en:["1.272 Level Color"],cs:["1.272 Level Color"]},e.exports["1.414 Level Color_input"]={en:["1.414 Level Color"],cs:["1.414 Level Color"]},e.exports["1.618 Level Color_input"]={en:["1.618 Level Color"],cs:["1.618 Level Color"]},e.exports["1.65 Level Color_input"]={en:["1.65 Level Color"],cs:["1.65 Level Color"]},e.exports["2.618 Level Color_input"]={en:["2.618 Level Color"],cs:["2.618 Level Color"]},e.exports["2.65 Level Color_input"]={en:["2.65 Level Color"],cs:["2.65 Level Color"]},e.exports["3.618 Level Color_input"]={en:["3.618 Level Color"],cs:["3.618 Level Color"]},e.exports["3.65 Level Color_input"]={en:["3.65 Level Color"],cs:["3.65 Level Color"]},e.exports["4.236 Level Color_input"]={en:["4.236 Level Color"],cs:["4.236 Level Color"]},e.exports["-0.236 Level Color_input"]={en:["-0.236 Level Color"],cs:["-0.236 Level Color"]},e.exports["-0.382 Level Color_input"]={en:["-0.382 Level Color"],cs:["-0.382 Level Color"]},e.exports["-0.618 Level Color_input"]={en:["-0.618 Level Color"],cs:["-0.618 Level Color"]},e.exports["-0.65 Level Color_input"]={en:["-0.65 Level Color"],cs:["-0.65 Level Color"]},e.exports.ADX_input={en:["ADX"],cs:["ADX"]},e.exports["ADX Smoothing_input"]={en:["ADX Smoothing"],cs:["ADX Smoothing"]},e.exports["DI Length_input"]={en:["DI Length"],cs:["DI Length"]},e.exports.Smoothing_input={en:["Smoothing"],cs:["Smoothing"]},e.exports.ATR_input={en:["ATR"],cs:["ATR"]},e.exports.Growing_input={en:["Growing"],cs:["Growing"]},e.exports.Falling_input={en:["Falling"],cs:["Falling"]},e.exports["Color 0_input"]={en:["Color 0"],cs:["Color 0"]},e.exports["Color 1_input"]={en:["Color 1"],cs:["Color 1"]},e.exports.Source_input={en:["Source"],cs:["Source"]},e.exports.StdDev_input={en:["StdDev"],cs:["StdDev"]},e.exports.Basis_input={en:["Basis"],cs:["Basis"]},e.exports.Median_input={en:["Median"],cs:["Median"]},e.exports["Bollinger Bands %B_input"]={
en:["Bollinger Bands %B"],cs:["Bollinger Bands %B"]},e.exports.Overbought_input={en:["Overbought"],cs:["Overbought"]},e.exports.Oversold_input={en:["Oversold"],cs:["Oversold"]},e.exports["Bollinger Bands Width_input"]={en:["Bollinger Bands Width"],cs:["Bollinger Bands Width"]},e.exports["RSI Length_input"]={en:["RSI Length"],cs:["RSI Length"]},e.exports["UpDown Length_input"]={en:["UpDown Length"],cs:["UpDown Length"]},e.exports["ROC Length_input"]={en:["ROC Length"],cs:["ROC Length"]},e.exports.MF_input={en:["MF"],cs:["MF"]},e.exports.resolution_input={en:["resolution"],cs:["resolution"]},e.exports["Fast Length_input"]={en:["Fast Length"],cs:["Fast Length"]},e.exports["Slow Length_input"]={en:["Slow Length"],cs:["Slow Length"]},e.exports["Chaikin Oscillator_input"]={en:["Chaikin Oscillator"],cs:["Chaikin Oscillator"]},e.exports.P_input={en:["P"],cs:["P"]},e.exports.X_input={en:["X"],cs:["X"]},e.exports.Q_input={en:["Q"],cs:["Q"]},e.exports.p_input={en:["p"],cs:["p"]},e.exports.x_input={en:["x"],cs:["x"]},e.exports.q_input={en:["q"],cs:["q"]},e.exports.Price_input={en:["Price"],cs:["Price"]},e.exports["Chande MO_input"]={en:["Chande MO"],cs:["Chande MO"]},e.exports["Zero Line_input"]={en:["Zero Line"],cs:["Zero Line"]},e.exports["Color 2_input"]={en:["Color 2"],cs:["Color 2"]},e.exports["Color 3_input"]={en:["Color 3"],cs:["Color 3"]},e.exports["Color 4_input"]={en:["Color 4"],cs:["Color 4"]},e.exports["Color 5_input"]={en:["Color 5"],cs:["Color 5"]},e.exports["Color 6_input"]={en:["Color 6"],cs:["Color 6"]},e.exports["Color 7_input"]={en:["Color 7"],cs:["Color 7"]},e.exports["Color 8_input"]={en:["Color 8"],cs:["Color 8"]},e.exports.CHOP_input={en:["CHOP"],cs:["CHOP"]},e.exports["Upper Band_input"]={en:["Upper Band"],cs:["Upper Band"]},e.exports["Lower Band_input"]={en:["Lower Band"],cs:["Lower Band"]},e.exports.CCI_input={en:["CCI"],cs:["CCI"]},e.exports["Smoothing Line_input"]={en:["Smoothing Line"],cs:["Smoothing Line"]},e.exports["Smoothing Length_input"]={en:["Smoothing Length"],cs:["Smoothing Length"]},e.exports["WMA Length_input"]={en:["WMA Length"],cs:["WMA Length"]},e.exports["Long RoC Length_input"]={en:["Long RoC Length"],cs:["Long RoC Length"]},e.exports["Short RoC Length_input"]={en:["Short RoC Length"],cs:["Short RoC Length"]},e.exports.sym_input={en:["sym"],cs:["sym"]},e.exports.Symbol_input={en:["Symbol"],cs:["Symbol"]},e.exports.Correlation_input={en:["Correlation"],cs:["Correlation"]},e.exports.Period_input={en:["Period"],cs:["Period"]},e.exports.Centered_input={en:["Centered"],cs:["Centered"]},e.exports["Detrended Price Oscillator_input"]={en:["Detrended Price Oscillator"],cs:["Detrended Price Oscillator"]},e.exports.isCentered_input={en:["isCentered"],cs:["isCentered"]},e.exports.DPO_input={en:["DPO"],cs:["DPO"]},e.exports["ADX smoothing_input"]={en:["ADX smoothing"],cs:["ADX smoothing"]},e.exports["+DI_input"]={en:["+DI"],cs:["+DI"]},e.exports["-DI_input"]={en:["-DI"],cs:["-DI"]},e.exports.DEMA_input={en:["DEMA"],cs:["DEMA"]},e.exports["Multi timeframe_input"]={en:["Multi timeframe"],
cs:["Multi timeframe"]},e.exports.Timeframe_input={en:["Timeframe"],cs:["Timeframe"]},e.exports["Wait for timeframe closes_input"]={en:["Wait for timeframe closes"],cs:["Wait for timeframe closes"]},e.exports.Divisor_input={en:["Divisor"],cs:["Divisor"]},e.exports.EOM_input={en:["EOM"],cs:["EOM"]},e.exports["Elder's Force Index_input"]={en:["Elder's Force Index"],cs:["Elder's Force Index"]},e.exports.Percent_input={en:["Percent"],cs:["Percent"]},e.exports.Exponential_input={en:["Exponential"],cs:["Exponential"]},e.exports.Average_input={en:["Average"],cs:["Average"]},e.exports["Upper Percentage_input"]={en:["Upper Percentage"],cs:["Upper Percentage"]},e.exports["Lower Percentage_input"]={en:["Lower Percentage"],cs:["Lower Percentage"]},e.exports.Fisher_input={en:["Fisher"],cs:["Fisher"]},e.exports.Trigger_input={en:["Trigger"],cs:["Trigger"]},e.exports.Level_input={en:["Level"],cs:["Level"]},e.exports["Trader EMA 1 length_input"]={en:["Trader EMA 1 length"],cs:["Trader EMA 1 length"]},e.exports["Trader EMA 2 length_input"]={en:["Trader EMA 2 length"],cs:["Trader EMA 2 length"]},e.exports["Trader EMA 3 length_input"]={en:["Trader EMA 3 length"],cs:["Trader EMA 3 length"]},e.exports["Trader EMA 4 length_input"]={en:["Trader EMA 4 length"],cs:["Trader EMA 4 length"]},e.exports["Trader EMA 5 length_input"]={en:["Trader EMA 5 length"],cs:["Trader EMA 5 length"]},e.exports["Trader EMA 6 length_input"]={en:["Trader EMA 6 length"],cs:["Trader EMA 6 length"]},e.exports["Investor EMA 1 length_input"]={en:["Investor EMA 1 length"],cs:["Investor EMA 1 length"]},e.exports["Investor EMA 2 length_input"]={en:["Investor EMA 2 length"],cs:["Investor EMA 2 length"]},e.exports["Investor EMA 3 length_input"]={en:["Investor EMA 3 length"],cs:["Investor EMA 3 length"]},e.exports["Investor EMA 4 length_input"]={en:["Investor EMA 4 length"],cs:["Investor EMA 4 length"]},e.exports["Investor EMA 5 length_input"]={en:["Investor EMA 5 length"],cs:["Investor EMA 5 length"]},e.exports["Investor EMA 6 length_input"]={en:["Investor EMA 6 length"],cs:["Investor EMA 6 length"]},e.exports.HV_input={en:["HV"],cs:["HV"]},e.exports["Conversion Line Periods_input"]={en:["Conversion Line Periods"],cs:["Conversion Line Periods"]},e.exports["Base Line Periods_input"]={en:["Base Line Periods"],cs:["Base Line Periods"]},e.exports["Lagging Span_input"]={en:["Lagging Span"],cs:["Lagging Span"]},e.exports["Conversion Line_input"]={en:["Conversion Line"],cs:["Conversion Line"]},e.exports["Base Line_input"]={en:["Base Line"],cs:["Base Line"]},e.exports["Leading Span A_input"]={en:["Leading Span A"],cs:["Lead 1"]},e.exports["Leading Span B_input"]={en:["Leading Span B"],cs:["Lead 2"]},e.exports["Plots Background_input"]={en:["Plots Background"],cs:["Plots Background"]},e.exports["yay Color 0_input"]={en:["yay Color 0"],cs:["yay Color 0"]},e.exports["yay Color 1_input"]={en:["yay Color 1"],cs:["yay Color 1"]},e.exports.Multiplier_input={en:["Multiplier"],cs:["Multiplier"]},e.exports["Bands style_input"]={en:["Bands style"],cs:["Bands style"]},e.exports.Middle_input={
en:["Middle"],cs:["Middle"]},e.exports.useTrueRange_input={en:["useTrueRange"],cs:["useTrueRange"]},e.exports.ROCLen1_input={en:["ROCLen1"],cs:["ROCLen1"]},e.exports.ROCLen2_input={en:["ROCLen2"],cs:["ROCLen2"]},e.exports.ROCLen3_input={en:["ROCLen3"],cs:["ROCLen3"]},e.exports.ROCLen4_input={en:["ROCLen4"],cs:["ROCLen4"]},e.exports.SMALen1_input={en:["SMALen1"],cs:["SMALen1"]},e.exports.SMALen2_input={en:["SMALen2"],cs:["SMALen2"]},e.exports.SMALen3_input={en:["SMALen3"],cs:["SMALen3"]},e.exports.SMALen4_input={en:["SMALen4"],cs:["SMALen4"]},e.exports.SigLen_input={en:["SigLen"],cs:["SigLen"]},e.exports.KST_input={en:["KST"],cs:["KST"]},e.exports.Sig_input={en:["Sig"],cs:["Sig"]},e.exports.roclen1_input={en:["roclen1"],cs:["roclen1"]},e.exports.roclen2_input={en:["roclen2"],cs:["roclen2"]},e.exports.roclen3_input={en:["roclen3"],cs:["roclen3"]},e.exports.roclen4_input={en:["roclen4"],cs:["roclen4"]},e.exports.smalen1_input={en:["smalen1"],cs:["smalen1"]},e.exports.smalen2_input={en:["smalen2"],cs:["smalen2"]},e.exports.smalen3_input={en:["smalen3"],cs:["smalen3"]},e.exports.smalen4_input={en:["smalen4"],cs:["smalen4"]},e.exports.siglen_input={en:["siglen"],cs:["siglen"]},e.exports["Upper Deviation_input"]={en:["Upper Deviation"],cs:["Upper Deviation"]},e.exports["Lower Deviation_input"]={en:["Lower Deviation"],cs:["Lower Deviation"]},e.exports["Use Upper Deviation_input"]={en:["Use Upper Deviation"],cs:["Use Upper Deviation"]},e.exports["Use Lower Deviation_input"]={en:["Use Lower Deviation"],cs:["Use Lower Deviation"]},e.exports.Count_input={en:["Count"],cs:["Count"]},e.exports.Crosses_input={en:["Crosses"],cs:["Crosses"]},e.exports.MOM_input={en:["MOM"],cs:["MOM"]},e.exports.MA_input={en:["MA"],cs:["MA"]},e.exports["Length EMA_input"]={en:["Length EMA"],cs:["Length EMA"]},e.exports["Length MA_input"]={en:["Length MA"],cs:["Length MA"]},e.exports["Fast length_input"]={en:["Fast length"],cs:["Fast length"]},e.exports["Slow length_input"]={en:["Slow length"],cs:["Slow length"]},e.exports["Signal smoothing_input"]={en:["Signal smoothing"],cs:["Signal smoothing"]},e.exports["Simple ma(oscillator)_input"]={en:["Simple ma(oscillator)"],cs:["Simple ma(oscillator)"]},e.exports["Simple ma(signal line)_input"]={en:["Simple ma(signal line)"],cs:["Simple ma(signal line)"]},e.exports.Histogram_input={en:["Histogram"],cs:["Histogram"]},e.exports.MACD_input={en:["MACD"],cs:["MACD"]},e.exports.fastLength_input={en:["fastLength"],cs:["fastLength"]},e.exports.slowLength_input={en:["slowLength"],cs:["slowLength"]},e.exports.signalLength_input={en:["signalLength"],cs:["signalLength"]},e.exports.NV_input={en:["NV"],cs:["NV"]},e.exports.OnBalanceVolume_input={en:["OnBalanceVolume"],cs:["OnBalanceVolume"]},e.exports.Start_input={en:["Start"],cs:["Začít"]},e.exports.Increment_input={en:["Increment"],cs:["Increment"]},e.exports["Max value_input"]={en:["Max value"],cs:["Max value"]},e.exports.ParabolicSAR_input={en:["ParabolicSAR"],cs:["ParabolicSAR"]},e.exports.start_input={en:["start"],cs:["start"]},e.exports.increment_input={
en:["increment"],cs:["increment"]},e.exports.maximum_input={en:["maximum"],cs:["maximum"]},e.exports["Short length_input"]={en:["Short length"],cs:["Short length"]},e.exports["Long length_input"]={en:["Long length"],cs:["Long length"]},e.exports.OSC_input={en:["OSC"],cs:["OSC"]},e.exports.shortlen_input={en:["shortlen"],cs:["shortlen"]},e.exports.longlen_input={en:["longlen"],cs:["longlen"]},e.exports.PVT_input={en:["PVT"],cs:["PVT"]},e.exports.ROC_input={en:["ROC"],cs:["ROC"]},e.exports.RSI_input={en:["RSI"],cs:["RSI"]},e.exports.RVGI_input={en:["RVGI"],cs:["RVGI"]},e.exports.RVI_input={en:["RVI"],cs:["RVI"]},e.exports["Long period_input"]={en:["Long period"],cs:["Long period"]},e.exports["Short period_input"]={en:["Short period"],cs:["Short period"]},e.exports["Signal line period_input"]={en:["Signal line period"],cs:["Signal line period"]},e.exports.SMI_input={en:["SMI"],cs:["SMI"]},e.exports["SMI Ergodic Oscillator_input"]={en:["SMI Ergodic Oscillator"],cs:["SMI Ergodic Oscillator"]},e.exports.Indicator_input={en:["Indicator"],cs:["Indicator"]},e.exports.Oscillator_input={en:["Oscillator"],cs:["Oscillator"]},e.exports.K_input={en:["K"],cs:["K"]},e.exports.D_input={en:["D"],cs:["D"]},e.exports.smoothK_input={en:["smoothK"],cs:["smoothK"]},e.exports.smoothD_input={en:["smoothD"],cs:["smoothD"]},e.exports["%K_input"]={en:["%K"],cs:["%K"]},e.exports["%D_input"]={en:["%D"],cs:["%D"]},e.exports["Stochastic Length_input"]={en:["Stochastic Length"],cs:["Stochastic Length"]},e.exports["RSI Source_input"]={en:["RSI Source"],cs:["RSI Source"]},e.exports.lengthRSI_input={en:["lengthRSI"],cs:["lengthRSI"]},e.exports.lengthStoch_input={en:["lengthStoch"],cs:["lengthStoch"]},e.exports.TRIX_input={en:["TRIX"],cs:["TRIX"]},e.exports.TEMA_input={en:["TEMA"],cs:["TEMA"]},e.exports["Long Length_input"]={en:["Long Length"],cs:["Long Length"]},e.exports["Short Length_input"]={en:["Short Length"],cs:["Short Length"]},e.exports["Signal Length_input"]={en:["Signal Length"],cs:["Signal Length"]},e.exports.Length1_input={en:["Length1"],cs:["Length1"]},e.exports.Length2_input={en:["Length2"],cs:["Length2"]},e.exports.Length3_input={en:["Length3"],cs:["Length3"]},e.exports.length7_input={en:["length7"],cs:["length7"]},e.exports.length14_input={en:["length14"],cs:["length14"]},e.exports.length28_input={en:["length28"],cs:["length28"]},e.exports.UO_input={en:["UO"],cs:["UO"]},e.exports.VWMA_input={en:["VWMA"],cs:["VWMA"]},e.exports.len_input={en:["len"],cs:["len"]},e.exports["VI +_input"]={en:["VI +"],cs:["VI +"]},e.exports["VI -_input"]={en:["VI -"],cs:["VI -"]},e.exports["%R_input"]={en:["%R"],cs:["%R"]},e.exports["Jaw Length_input"]={en:["Jaw Length"],cs:["Jaw Length"]},e.exports["Teeth Length_input"]={en:["Teeth Length"],cs:["Teeth Length"]},e.exports["Lips Length_input"]={en:["Lips Length"],cs:["Lips Length"]},e.exports.Jaw_input={en:["Jaw"],cs:["Jaw"]},e.exports.Teeth_input={en:["Teeth"],cs:["Teeth"]},e.exports.Lips_input={en:["Lips"],cs:["Lips"]},e.exports["Jaw Offset_input"]={en:["Jaw Offset"],cs:["Jaw Offset"]},
e.exports["Teeth Offset_input"]={en:["Teeth Offset"],cs:["Teeth Offset"]},e.exports["Lips Offset_input"]={en:["Lips Offset"],cs:["Lips Offset"]},e.exports["Down fractals_input"]={en:["Down fractals"],cs:["Down fractals"]},e.exports["Up fractals_input"]={en:["Up fractals"],cs:["Up fractals"]},e.exports.Periods_input={en:["Periods"],cs:["Periods"]},e.exports.Shapes_input={en:["Shapes"],cs:["Shapes"]},e.exports["show MA_input"]={en:["show MA"],cs:["show MA"]},e.exports["MA Length_input"]={en:["MA Length"],cs:["MA Length"]},e.exports["Color based on previous close_input"]={en:["Color based on previous close"],cs:["Color based on previous close"]},e.exports["Rows Layout_input"]={en:["Rows Layout"],cs:["Rows Layout"]},e.exports["Row Size_input"]={en:["Row Size"],cs:["Row Size"]},e.exports.Volume_input={en:["Volume"],cs:["Volume"]},e.exports["Value Area volume_input"]={en:["Value Area volume"],cs:["Value Area volume"]},e.exports["Extend Right_input"]={en:["Extend Right"],cs:["Extend Right"]},e.exports["Extend POC Right_input"]={en:["Extend POC Right"],cs:["Extend POC Right"]},e.exports["Extend VAH Right_input"]={en:["Extend VAH Right"],cs:["Extend VAH Right"]},e.exports["Extend VAL Right_input"]={en:["Extend VAL Right"],cs:["Extend VAL Right"]},e.exports["Value Area Volume_input"]={en:["Value Area Volume"],cs:["Value Area Volume"]},e.exports.Placement_input={en:["Placement"],cs:["Placement"]},e.exports.POC_input={en:["POC"],cs:["POC"]},e.exports["Developing Poc_input"]={en:["Developing Poc"],cs:["Developing Poc"]},e.exports["Up Volume_input"]={en:["Up Volume"],cs:["Up Volume"]},e.exports["Down Volume_input"]={en:["Down Volume"],cs:["Down Volume"]},e.exports["Value Area_input"]={en:["Value Area"],cs:["Value Area"]},e.exports["Histogram Box_input"]={en:["Histogram Box"],cs:["Histogram Box"]},e.exports["Value Area Up_input"]={en:["Value Area Up"],cs:["Value Area Up"]},e.exports["Value Area Down_input"]={en:["Value Area Down"],cs:["Value Area Down"]},e.exports["Number Of Rows_input"]={en:["Number Of Rows"],cs:["Number Of Rows"]},e.exports["Ticks Per Row_input"]={en:["Ticks Per Row"],cs:["Ticks Per Row"]},e.exports["Up/Down_input"]={en:["Up/Down"],cs:["Up/Down"]},e.exports.Total_input={en:["Total"],cs:["Total"]},e.exports.Delta_input={en:["Delta"],cs:["Delta"]},e.exports.Bar_input={en:["Bar"],cs:["Bar"]},e.exports.Day_input={en:["Day"],cs:["Day"]},e.exports["Deviation (%)_input"]={en:["Deviation (%)"],cs:["Deviation (%)"]},e.exports.Depth_input={en:["Depth"],cs:["Depth"]},e.exports["Extend to last bar_input"]={en:["Extend to last bar"],cs:["Extend to last bar"]},e.exports.Simple_input={en:["Simple"],cs:["Simple"]},e.exports.Weighted_input={en:["Weighted"],cs:["Weighted"]},e.exports["Wilder's Smoothing_input"]={en:["Wilder's Smoothing"],cs:["Wilder's Smoothing"]},e.exports["1st Period_input"]={en:["1st Period"],cs:["1st Period"]},e.exports["2nd Period_input"]={en:["2nd Period"],cs:["2nd Period"]},e.exports["3rd Period_input"]={en:["3rd Period"],cs:["3rd Period"]},e.exports["4th Period_input"]={en:["4th Period"],cs:["4th Period"]
},e.exports["5th Period_input"]={en:["5th Period"],cs:["5th Period"]},e.exports["6th Period_input"]={en:["6th Period"],cs:["6th Period"]},e.exports["Rate of Change Lookback_input"]={en:["Rate of Change Lookback"],cs:["Rate of Change Lookback"]},e.exports["Instrument 1_input"]={en:["Instrument 1"],cs:["Instrument 1"]},e.exports["Instrument 2_input"]={en:["Instrument 2"],cs:["Instrument 2"]},e.exports["Rolling Period_input"]={en:["Rolling Period"],cs:["Rolling Period"]},e.exports["Standard Errors_input"]={en:["Standard Errors"],cs:["Standard Errors"]},e.exports["Averaging Periods_input"]={en:["Averaging Periods"],cs:["Averaging Periods"]},e.exports["Days Per Year_input"]={en:["Days Per Year"],cs:["Days Per Year"]},e.exports["Market Closed Percentage_input"]={en:["Market Closed Percentage"],cs:["Market Closed Percentage"]},e.exports["ATR Mult_input"]={en:["ATR Mult"],cs:["ATR Mult"]},e.exports.VWAP_input={en:["VWAP"],cs:["VWAP"]},e.exports["Anchor Period_input"]={en:["Anchor Period"],cs:["Anchor Period"]},e.exports.Session_input={en:["Session"],cs:["Session"]},e.exports.Week_input={en:["Week"],cs:["Week"]},e.exports.Month_input={en:["Month"],cs:["Month"]},e.exports.Year_input={en:["Year"],cs:["Year"]},e.exports.Decade_input={en:["Decade"],cs:["Decade"]},e.exports.Century_input={en:["Century"],cs:["Century"]},e.exports.Sessions_input={en:["Sessions"],cs:["Sessions"]},e.exports["Each (pre-market, market, post-market)_input"]={en:["Each (pre-market, market, post-market)"],cs:["Each (pre-market, market, post-market)"]},e.exports["Pre-market only_input"]={en:["Pre-market only"],cs:["Pre-market only"]},e.exports["Market only_input"]={en:["Market only"],cs:["Market only"]},e.exports["Post-market only_input"]={en:["Post-market only"],cs:["Post-market only"]},e.exports["Main chart symbol_input"]={en:["Main chart symbol"],cs:["Main chart symbol"]},e.exports["Another symbol_input"]={en:["Another symbol"],cs:["Another symbol"]},e.exports.Line_input={en:["Line"],cs:["Čára"]},e.exports["Nothing selected_combobox_input"]={en:["Nothing selected"],cs:["Nothing selected"]},e.exports["All items_combobox_input"]={en:["All items"],cs:["All items"]},e.exports.Cancel_input={en:["Cancel"],cs:["Cancel"]},e.exports.Open_input={en:["Open"],cs:["Open"]},e.exports.MM_month_input={en:["MM"],cs:["MM"]},e.exports.YY_year_input={en:["YY"],cs:["YY"]},e.exports.Style_input={en:["Style"],cs:["Style"]},e.exports["Box size assignment method_input"]={en:["Box size assignment method"],cs:["Box size assignment method"]},e.exports["Color bars based on previous close_input"]={en:["Color bars based on previous close"],cs:["Color bars based on previous close"]},e.exports.Candles_input={en:["Candles"],cs:["Candles"]},e.exports.Borders_input={en:["Borders"],cs:["Borders"]},e.exports.Wick_input={en:["Wick"],cs:["Wick"]},e.exports["HLC bars_input"]={en:["HLC bars"],cs:["HLC bars"]},e.exports["Price source_input"]={en:["Price source"],cs:["Price source"]},e.exports.Type_input={en:["Type"],cs:["Type"]},
e.exports["Show real prices on price scale (instead of Heikin-Ashi price)_input"]={en:["Show real prices on price scale (instead of Heikin-Ashi price)"],cs:["Show real prices on price scale (instead of Heikin-Ashi price)"]},e.exports["Up bars_input"]={en:["Up bars"],cs:["Up bars"]},e.exports["Down bars_input"]={en:["Down bars"],cs:["Down bars"]},e.exports["Projection up bars_input"]={en:["Projection up bars"],cs:["Projection up bars"]},e.exports["Projection down bars_input"]={en:["Projection down bars"],cs:["Projection down bars"]},e.exports["Projection up color_input"]={en:["Projection up color"],cs:["Projection up color"]},e.exports["Projection down color_input"]={en:["Projection down color"],cs:["Projection down color"]},e.exports.Fill_input={en:["Fill"],cs:["Fill"]},e.exports["Up color_input"]={en:["Up color"],cs:["Up color"]},e.exports["Down color_input"]={en:["Down color"],cs:["Down color"]},e.exports.Traditional_input={en:["Traditional"],cs:["Traditional"]},e.exports.PercentageLTP_input={en:["PercentageLTP"],cs:["PercentageLTP"]},e.exports["Box size_input"]={en:["Box size"],cs:["Box size"]},e.exports["Number of line_input"]={en:["Number of line"],cs:["Number of line"]},e.exports["ATR length_input"]={en:["ATR length"],cs:["ATR length"]},e.exports.Percentage_input={en:["Percentage"],cs:["Percentage"]},e.exports["Reversal amount_input"]={en:["Reversal amount"],cs:["Reversal amount"]},e.exports["Phantom bars_input"]={en:["Phantom bars"],cs:["Phantom bars"]},e.exports["One step back building_input"]={en:["One step back building"],cs:["One step back building"]},e.exports.Wicks_input={en:["Wicks"],cs:["Wicks"]},e.exports.Range_input={en:["Range"],cs:["Range"]},e.exports.All_input={en:["All"],cs:["All"]},e.exports.Custom_input={en:["Custom"],cs:["Custom"]},e.exports["Leading Span Periods_input"]={en:["Leading Span Periods"],cs:["Lagging Span 2 Periods"]},e.exports["Lagging Span Periods_input"]={en:["Lagging Span Periods"],cs:["Lagging Span Periods"]},e.exports["Leading Shift Periods_input"]={en:["Leading Shift Periods"],cs:["Leading Shift Periods"]},e.exports["ATR({atrValue})_input"]={en:["ATR({atrValue})"],cs:["ATR({atrValue})"]},e.exports["PercentageLTP({percentageLTPValue}%)_input"]={en:["PercentageLTP({percentageLTPValue}%)"],cs:["PercentageLTP({percentageLTPValue}%)"]}},50873:e=>{e.exports={en:["ATR({atrValue})"],cs:["ATR({atrValue})"]}},28037:e=>{e.exports={en:["PercentageLTP({percentageLTPValue}%)"],cs:["PercentageLTP({percentageLTPValue}%)"]}},40566:e=>{e.exports={en:["Traditional"],cs:["Traditional"]}},75163:e=>{e.exports={en:["Invert scale"],cs:["Invert Scale"]}},35210:e=>{e.exports={en:["Indexed to 100"],cs:["Indexed to 100"]}},31340:e=>{e.exports={en:["Logarithmic"],cs:["Logarithmic"]}},19405:e=>{e.exports={en:["No overlapping labels"],cs:["No Overlapping Labels"]}},34954:e=>{e.exports={en:["Percent"],cs:["Percent"]}},55300:e=>{e.exports={en:["Regular"],cs:["Regular"]}},8029:e=>{e.exports={en:["ETH"],cs:["ETH"]}},34647:e=>{e.exports={en:["Electronic trading hours"],cs:["Electronic trading hours"]}},
36862:e=>{e.exports={en:["Extended trading hours"],cs:["Extended trading hours"]}},7807:e=>{e.exports={en:["POST"],cs:["POST"]}},46273:e=>{e.exports={en:["PRE"],cs:["PRE"]}},50434:e=>{e.exports={en:["Postmarket"],cs:["Postmarket"]}},59330:e=>{e.exports={en:["Premarket"],cs:["Premarket"]}},35342:e=>{e.exports={en:["RTH"],cs:["RTH"]}},84246:e=>{e.exports={en:["Regular trading hours"],cs:["Regular trading hours"]}},13132:e=>{e.exports={en:["May"],cs:["Květen"]}},83477:e=>{e.exports=Object.create(null),e.exports.Technicals_study={en:["Technicals"],cs:["Technicals"]},e.exports["Average Day Range_study"]={en:["Average Day Range"],cs:["Average Day Range"]},e.exports["Bull Bear Power_study"]={en:["Bull Bear Power"],cs:["Bull Bear Power"]},e.exports["Capital expenditures_study"]={en:["Capital expenditures"],cs:["Capital expenditures"]},e.exports["Cash to debt ratio_study"]={en:["Cash to debt ratio"],cs:["Cash to debt ratio"]},e.exports["Debt to EBITDA ratio_study"]={en:["Debt to EBITDA ratio"],cs:["Debt to EBITDA ratio"]},e.exports["Directional Movement Index_study"]={en:["Directional Movement Index"],cs:["Directional Movement Index"]},e.exports.DMI_study={en:["DMI"],cs:["DMI"]},e.exports["Dividend payout ratio %_study"]={en:["Dividend payout ratio %"],cs:["Dividend payout ratio %"]},e.exports["Equity to assets ratio_study"]={en:["Equity to assets ratio"],cs:["Equity to assets ratio"]},e.exports["Enterprise value to EBIT ratio_study"]={en:["Enterprise value to EBIT ratio"],cs:["Enterprise value to EBIT ratio"]},e.exports["Enterprise value to EBITDA ratio_study"]={en:["Enterprise value to EBITDA ratio"],cs:["Enterprise value to EBITDA ratio"]},e.exports["Enterprise value to revenue ratio_study"]={en:["Enterprise value to revenue ratio"],cs:["Enterprise value to revenue ratio"]},e.exports["Goodwill, net_study"]={en:["Goodwill, net"],cs:["Goodwill, net"]},e.exports["Ichimoku Cloud_study"]={en:["Ichimoku Cloud"],cs:["Ichimoku Cloud"]},e.exports.Ichimoku_study={en:["Ichimoku"],cs:["Ichimoku"]},e.exports["Moving Average Convergence Divergence_study"]={en:["Moving Average Convergence Divergence"],cs:["Moving Average Convergence Divergence"]},e.exports["Operating income_study"]={en:["Operating income"],cs:["Operating income"]},e.exports["Price to book ratio_study"]={en:["Price to book ratio"],cs:["Price to book ratio"]},e.exports["Price to cash flow ratio_study"]={en:["Price to cash flow ratio"],cs:["Price to cash flow ratio"]},e.exports["Price to earnings ratio_study"]={en:["Price to earnings ratio"],cs:["Price to earnings ratio"]},e.exports["Price to free cash flow ratio_study"]={en:["Price to free cash flow ratio"],cs:["Price to free cash flow ratio"]},e.exports["Price to sales ratio_study"]={en:["Price to sales ratio"],cs:["Price to sales ratio"]},e.exports["Float shares outstanding_study"]={en:["Float shares outstanding"],cs:["Float shares outstanding"]},e.exports["Total common shares outstanding_study"]={en:["Total common shares outstanding"],cs:["Total common shares outstanding"]},
e.exports["Volume Weighted Average Price_study"]={en:["Volume Weighted Average Price"],cs:["Volume Weighted Average Price"]},e.exports["Volume Weighted Moving Average_study"]={en:["Volume Weighted Moving Average"],cs:["Volume Weighted Moving Average"]},e.exports["Williams Percent Range_study"]={en:["Williams Percent Range"],cs:["Williams Percent Range"]},e.exports.Doji_study={en:["Doji"],cs:["Doji"]},e.exports["Spinning Top Black_study"]={en:["Spinning Top Black"],cs:["Spinning Top Black"]},e.exports["Spinning Top White_study"]={en:["Spinning Top White"],cs:["Spinning Top White"]},e.exports["Accounts payable_study"]={en:["Accounts payable"],cs:["Accounts payable"]},e.exports["Accounts receivables, gross_study"]={en:["Accounts receivables, gross"],cs:["Accounts receivables, gross"]},e.exports["Accounts receivable - trade, net_study"]={en:["Accounts receivable - trade, net"],cs:["Accounts receivable - trade, net"]},e.exports.Accruals_study={en:["Accruals"],cs:["Accruals"]},e.exports["Accrued payroll_study"]={en:["Accrued payroll"],cs:["Accrued payroll"]},e.exports["Accumulated depreciation, total_study"]={en:["Accumulated depreciation, total"],cs:["Accumulated depreciation, total"]},e.exports["Additional paid-in capital/Capital surplus_study"]={en:["Additional paid-in capital/Capital surplus"],cs:["Additional paid-in capital/Capital surplus"]},e.exports["After tax other income/expense_study"]={en:["After tax other income/expense"],cs:["After tax other income/expense"]},e.exports["Altman Z-score_study"]={en:["Altman Z-score"],cs:["Altman Z-score"]},e.exports.Amortization_study={en:["Amortization"],cs:["Amortization"]},e.exports["Amortization of intangibles_study"]={en:["Amortization of intangibles"],cs:["Amortization of intangibles"]},e.exports["Amortization of deferred charges_study"]={en:["Amortization of deferred charges"],cs:["Amortization of deferred charges"]},e.exports["Asset turnover_study"]={en:["Asset turnover"],cs:["Asset turnover"]},e.exports["Average basic shares outstanding_study"]={en:["Average basic shares outstanding"],cs:["Average basic shares outstanding"]},e.exports["Bad debt / Doubtful accounts_study"]={en:["Bad debt / Doubtful accounts"],cs:["Bad debt / Doubtful accounts"]},e.exports["Basic EPS_study"]={en:["Basic EPS"],cs:["Basic EPS"]},e.exports["Basic earnings per share (Basic EPS)_study"]={en:["Basic earnings per share (Basic EPS)"],cs:["Basic earnings per share (Basic EPS)"]},e.exports["Beneish M-score_study"]={en:["Beneish M-score"],cs:["Beneish M-score"]},e.exports["Book value per share_study"]={en:["Book value per share"],cs:["Book value per share"]},e.exports["Buyback yield %_study"]={en:["Buyback yield %"],cs:["Buyback yield %"]},e.exports["Capital and operating lease obligations_study"]={en:["Capital and operating lease obligations"],cs:["Capital and operating lease obligations"]},e.exports["Capital expenditures - fixed assets_study"]={en:["Capital expenditures - fixed assets"],cs:["Capital expenditures - fixed assets"]},e.exports["Capital expenditures - other assets_study"]={
en:["Capital expenditures - other assets"],cs:["Capital expenditures - other assets"]},e.exports["Capitalized lease obligations_study"]={en:["Capitalized lease obligations"],cs:["Capitalized lease obligations"]},e.exports["Cash and short term investments_study"]={en:["Cash and short term investments"],cs:["Cash and short term investments"]},e.exports["Cash conversion cycle_study"]={en:["Cash conversion cycle"],cs:["Cash conversion cycle"]},e.exports["Cash & equivalents_study"]={en:["Cash & equivalents"],cs:["Cash & equivalents"]},e.exports["Cash from financing activities_study"]={en:["Cash from financing activities"],cs:["Cash from financing activities"]},e.exports["Cash from investing activities_study"]={en:["Cash from investing activities"],cs:["Cash from investing activities"]},e.exports["Cash from operating activities_study"]={en:["Cash from operating activities"],cs:["Cash from operating activities"]},e.exports["Change in accounts payable_study"]={en:["Change in accounts payable"],cs:["Change in accounts payable"]},e.exports["Change in accounts receivable_study"]={en:["Change in accounts receivable"],cs:["Change in accounts receivable"]},e.exports["Change in accrued expenses_study"]={en:["Change in accrued expenses"],cs:["Change in accrued expenses"]},e.exports["Change in inventories_study"]={en:["Change in inventories"],cs:["Change in inventories"]},e.exports["Change in other assets/liabilities_study"]={en:["Change in other assets/liabilities"],cs:["Change in other assets/liabilities"]},e.exports["Change in taxes payable_study"]={en:["Change in taxes payable"],cs:["Change in taxes payable"]},e.exports["Changes in working capital_study"]={en:["Changes in working capital"],cs:["Changes in working capital"]},e.exports["COGS to revenue ratio_study"]={en:["COGS to revenue ratio"],cs:["COGS to revenue ratio"]},e.exports["Common dividends paid_study"]={en:["Common dividends paid"],cs:["Common dividends paid"]},e.exports["Common equity, total_study"]={en:["Common equity, total"],cs:["Common equity, total"]},e.exports["Common stock par/Carrying value_study"]={en:["Common stock par/Carrying value"],cs:["Common stock par/Carrying value"]},e.exports["Cost of goods_study"]={en:["Cost of goods"],cs:["Cost of goods"]},e.exports["Cost of goods sold_study"]={en:["Cost of goods sold"],cs:["Cost of goods sold"]},e.exports["Current portion of LT debt and capital leases_study"]={en:["Current portion of LT debt and capital leases"],cs:["Current portion of LT debt and capital leases"]},e.exports["Current ratio_study"]={en:["Current ratio"],cs:["Current ratio"]},e.exports["Days inventory_study"]={en:["Days inventory"],cs:["Days inventory"]},e.exports["Days payable_study"]={en:["Days payable"],cs:["Days payable"]},e.exports["Days sales outstanding_study"]={en:["Days sales outstanding"],cs:["Days sales outstanding"]},e.exports["Debt to assets ratio_study"]={en:["Debt to assets ratio"],cs:["Debt to assets ratio"]},e.exports["Debt to equity ratio_study"]={en:["Debt to equity ratio"],cs:["Debt to equity ratio"]},
e.exports["Debt to revenue ratio_study"]={en:["Debt to revenue ratio"],cs:["Debt to revenue ratio"]},e.exports["Deferred income, current_study"]={en:["Deferred income, current"],cs:["Deferred income, current"]},e.exports["Deferred income, non-current_study"]={en:["Deferred income, non-current"],cs:["Deferred income, non-current"]},e.exports["Deferred tax assets_study"]={en:["Deferred tax assets"],cs:["Deferred tax assets"]},e.exports["Deferred taxes (cash flow)_study"]={en:["Deferred taxes (cash flow)"],cs:["Deferred taxes (cash flow)"]},e.exports["Deferred tax liabilities_study"]={en:["Deferred tax liabilities"],cs:["Deferred tax liabilities"]},e.exports.Depreciation_study={en:["Depreciation"],cs:["Depreciation"]},e.exports["Deprecation and amortization_study"]={en:["Deprecation and amortization"],cs:["Deprecation and amortization"]},e.exports["Depreciation & amortization (cash flow)_study"]={en:["Depreciation & amortization (cash flow)"],cs:["Depreciation & amortization (cash flow)"]},e.exports["Depreciation/depletion_study"]={en:["Depreciation/depletion"],cs:["Depreciation/depletion"]},e.exports["Diluted EPS_study"]={en:["Diluted EPS"],cs:["Diluted EPS"]},e.exports["Diluted earnings per share (Diluted EPS)_study"]={en:["Diluted earnings per share (Diluted EPS)"],cs:["Diluted earnings per share (Diluted EPS)"]},e.exports["Diluted net income available to common stockholders_study"]={en:["Diluted net income available to common stockholders"],cs:["Diluted net income available to common stockholders"]},e.exports["Diluted shares outstanding_study"]={en:["Diluted shares outstanding"],cs:["Diluted shares outstanding"]},e.exports["Dilution adjustment_study"]={en:["Dilution adjustment"],cs:["Dilution adjustment"]},e.exports["Discontinued operations_study"]={en:["Discontinued operations"],cs:["Discontinued operations"]},e.exports["Dividends payable_study"]={en:["Dividends payable"],cs:["Dividends payable"]},e.exports["Dividends per share - common stock primary issue_study"]={en:["Dividends per share - common stock primary issue"],cs:["Dividends per share - common stock primary issue"]},e.exports["Dividend yield %_study"]={en:["Dividend yield %"],cs:["Dividend yield %"]},e.exports["Earnings yield_study"]={en:["Earnings yield"],cs:["Earnings yield"]},e.exports.EBIT_study={en:["EBIT"],cs:["EBIT"]},e.exports.EBITDA_study={en:["EBITDA"],cs:["EBITDA"]},e.exports["EBITDA margin %_study"]={en:["EBITDA margin %"],cs:["EBITDA margin %"]},e.exports["Effective interest rate on debt %_study"]={en:["Effective interest rate on debt %"],cs:["Effective interest rate on debt %"]},e.exports["Enterprise value_study"]={en:["Enterprise value"],cs:["Enterprise value"]},e.exports["EPS basic one year growth_study"]={en:["EPS basic one year growth"],cs:["EPS basic one year growth"]},e.exports["EPS diluted one year growth_study"]={en:["EPS diluted one year growth"],cs:["EPS diluted one year growth"]},e.exports["EPS estimates_study"]={en:["EPS estimates"],cs:["EPS estimates"]},e.exports["Equity in earnings_study"]={en:["Equity in earnings"],
cs:["Equity in earnings"]},e.exports["Financing activities – other sources_study"]={en:["Financing activities – other sources"],cs:["Financing activities – other sources"]},e.exports["Financing activities – other uses_study"]={en:["Financing activities – other uses"],cs:["Financing activities – other uses"]},e.exports["Free cash flow_study"]={en:["Free cash flow"],cs:["Free cash flow"]},e.exports["Free cash flow margin %_study"]={en:["Free cash flow margin %"],cs:["Free cash flow margin %"]},e.exports["Fulmer H factor_study"]={en:["Fulmer H factor"],cs:["Fulmer H factor"]},e.exports["Funds from operations_study"]={en:["Funds from operations"],cs:["Funds from operations"]},e.exports["Goodwill to assets ratio_study"]={en:["Goodwill to assets ratio"],cs:["Goodwill to assets ratio"]},e.exports["Graham's number_study"]={en:["Graham's number"],cs:["Graham's number"]},e.exports["Gross margin %_study"]={en:["Gross margin %"],cs:["Gross margin %"]},e.exports["Gross profit_study"]={en:["Gross profit"],cs:["Gross profit"]},e.exports["Gross profit to assets ratio_study"]={en:["Gross profit to assets ratio"],cs:["Gross profit to assets ratio"]},e.exports["Gross property/plant/equipment_study"]={en:["Gross property/plant/equipment"],cs:["Gross property/plant/equipment"]},e.exports.Impairments_study={en:["Impairments"],cs:["Impairments"]},e.exports["Income Tax Credits_study"]={en:["Income Tax Credits"],cs:["Income Tax Credits"]},e.exports["Income tax, current_study"]={en:["Income tax, current"],cs:["Income tax, current"]},e.exports["Income tax, current - domestic_study"]={en:["Income tax, current - domestic"],cs:["Income tax, current - domestic"]},e.exports["Income Tax, current - foreign_study"]={en:["Income Tax, current - foreign"],cs:["Income Tax, current - foreign"]},e.exports["Income tax, deferred_study"]={en:["Income tax, deferred"],cs:["Income tax, deferred"]},e.exports["Income tax, deferred - domestic_study"]={en:["Income tax, deferred - domestic"],cs:["Income tax, deferred - domestic"]},e.exports["Income tax, deferred - foreign_study"]={en:["Income tax, deferred - foreign"],cs:["Income tax, deferred - foreign"]},e.exports["Income tax payable_study"]={en:["Income tax payable"],cs:["Income tax payable"]},e.exports["Interest capitalized_study"]={en:["Interest capitalized"],cs:["Interest capitalized"]},e.exports["Interest coverage_study"]={en:["Interest coverage"],cs:["Interest coverage"]},e.exports["Interest expense, net of interest capitalized_study"]={en:["Interest expense, net of interest capitalized"],cs:["Interest expense, net of interest capitalized"]},e.exports["Interest expense on debt_study"]={en:["Interest expense on debt"],cs:["Interest expense on debt"]},e.exports["Inventories - finished goods_study"]={en:["Inventories - finished goods"],cs:["Inventories - finished goods"]},e.exports["Inventories - progress payments & other_study"]={en:["Inventories - progress payments & other"],cs:["Inventories - progress payments & other"]},e.exports["Inventories - raw materials_study"]={en:["Inventories - raw materials"],
cs:["Inventories - raw materials"]},e.exports["Inventories - work in progress_study"]={en:["Inventories - work in progress"],cs:["Inventories - work in progress"]},e.exports["Inventory to revenue ratio_study"]={en:["Inventory to revenue ratio"],cs:["Inventory to revenue ratio"]},e.exports["Inventory turnover_study"]={en:["Inventory turnover"],cs:["Inventory turnover"]},e.exports["Investing activities – other sources_study"]={en:["Investing activities – other sources"],cs:["Investing activities – other sources"]},e.exports["Investing activities – other uses_study"]={en:["Investing activities – other uses"],cs:["Investing activities – other uses"]},e.exports["Investments in unconsolidated subsidiaries_study"]={en:["Investments in unconsolidated subsidiaries"],cs:["Investments in unconsolidated subsidiaries"]},e.exports["Issuance of long term debt_study"]={en:["Issuance of long term debt"],cs:["Issuance of long term debt"]},e.exports["Issuance/retirement of debt, net_study"]={en:["Issuance/retirement of debt, net"],cs:["Issuance/retirement of debt, net"]},e.exports["Issuance/retirement of long term debt_study"]={en:["Issuance/retirement of long term debt"],cs:["Issuance/retirement of long term debt"]},e.exports["Issuance/retirement of other debt_study"]={en:["Issuance/retirement of other debt"],cs:["Issuance/retirement of other debt"]},e.exports["Issuance/retirement of short term debt_study"]={en:["Issuance/retirement of short term debt"],cs:["Issuance/retirement of short term debt"]},e.exports["Issuance/retirement of stock, net_study"]={en:["Issuance/retirement of stock, net"],cs:["Issuance/retirement of stock, net"]},e.exports["KZ index_study"]={en:["KZ index"],cs:["KZ index"]},e.exports["Legal claim expense_study"]={en:["Legal claim expense"],cs:["Legal claim expense"]},e.exports["Long term debt_study"]={en:["Long term debt"],cs:["Long term debt"]},e.exports["Long term debt excl. lease liabilities_study"]={en:["Long term debt excl. lease liabilities"],cs:["Long term debt excl. lease liabilities"]},e.exports["Long term debt to total assets ratio_study"]={en:["Long term debt to total assets ratio"],cs:["Long term debt to total assets ratio"]},e.exports["Long term debt to total equity ratio_study"]={en:["Long term debt to total equity ratio"],cs:["Long term debt to total equity ratio"]},e.exports["Long term investments_study"]={en:["Long term investments"],cs:["Long term investments"]},e.exports["Market capitalization_study"]={en:["Market capitalization"],cs:["Market capitalization"]},e.exports["Minority interest_study"]={en:["Minority interest"],cs:["Minority interest"]},e.exports["Miscellaneous non-operating expense_study"]={en:["Miscellaneous non-operating expense"],cs:["Miscellaneous non-operating expense"]},e.exports["Net current asset value per share_study"]={en:["Net current asset value per share"],cs:["Net current asset value per share"]},e.exports["Net debt_study"]={en:["Net debt"],cs:["Net debt"]},e.exports["Net income_study"]={en:["Net income"],cs:["Net income"]},
e.exports["Net income before discontinued operations_study"]={en:["Net income before discontinued operations"],cs:["Net income before discontinued operations"]},e.exports["Net income (cash flow)_study"]={en:["Net income (cash flow)"],cs:["Net income (cash flow)"]},e.exports["Net income per employee_study"]={en:["Net income per employee"],cs:["Net income per employee"]},e.exports["Net intangible assets_study"]={en:["Net intangible assets"],cs:["Net intangible assets"]},e.exports["Net margin %_study"]={en:["Net margin %"],cs:["Net margin %"]},e.exports["Net property/plant/equipment_study"]={en:["Net property/plant/equipment"],cs:["Net property/plant/equipment"]},e.exports["Non-cash items_study"]={en:["Non-cash items"],cs:["Non-cash items"]},e.exports["Non-controlling/minority interest_study"]={en:["Non-controlling/minority interest"],cs:["Non-controlling/minority interest"]},e.exports["Non-operating income, excl. interest expenses_study"]={en:["Non-operating income, excl. interest expenses"],cs:["Non-operating income, excl. interest expenses"]},e.exports["Non-operating income, total_study"]={en:["Non-operating income, total"],cs:["Non-operating income, total"]},e.exports["Non-operating interest income_study"]={en:["Non-operating interest income"],cs:["Non-operating interest income"]},e.exports["Note receivable - long term_study"]={en:["Note receivable - long term"],cs:["Note receivable - long term"]},e.exports["Notes payable_study"]={en:["Notes payable"],cs:["Notes payable"]},e.exports["Number of employees_study"]={en:["Number of employees"],cs:["Number of employees"]},e.exports["Number of shareholders_study"]={en:["Number of shareholders"],cs:["Number of shareholders"]},e.exports["Operating earnings yield %_study"]={en:["Operating earnings yield %"],cs:["Operating earnings yield %"]},e.exports["Operating expenses (excl. COGS)_study"]={en:["Operating expenses (excl. COGS)"],cs:["Operating expenses (excl. COGS)"]},e.exports["Operating lease liabilities_study"]={en:["Operating lease liabilities"],cs:["Operating lease liabilities"]},e.exports["Operating margin %_study"]={en:["Operating margin %"],cs:["Operating margin %"]},e.exports["Other COGS_study"]={en:["Other COGS"],cs:["Other COGS"]},e.exports["Other common equity_study"]={en:["Other common equity"],cs:["Other common equity"]},e.exports["Other current assets, total_study"]={en:["Other current assets, total"],cs:["Other current assets, total"]},e.exports["Other current liabilities_study"]={en:["Other current liabilities"],cs:["Other current liabilities"]},e.exports["Other cost of goods sold_study"]={en:["Other cost of goods sold"],cs:["Other cost of goods sold"]},e.exports["Other exceptional charges_study"]={en:["Other exceptional charges"],cs:["Other exceptional charges"]},e.exports["Other financing cash flow items, total_study"]={en:["Other financing cash flow items, total"],cs:["Other financing cash flow items, total"]},e.exports["Other intangibles, net_study"]={en:["Other intangibles, net"],cs:["Other intangibles, net"]},
e.exports["Other investing cash flow items, total_study"]={en:["Other investing cash flow items, total"],cs:["Other investing cash flow items, total"]},e.exports["Other investments_study"]={en:["Other investments"],cs:["Other investments"]},e.exports["Other liabilities, total_study"]={en:["Other liabilities, total"],cs:["Other liabilities, total"]},e.exports["Other long term assets, total_study"]={en:["Other long term assets, total"],cs:["Other long term assets, total"]},e.exports["Other non-current liabilities, total_study"]={en:["Other non-current liabilities, total"],cs:["Other non-current liabilities, total"]},e.exports["Other operating expenses, total_study"]={en:["Other operating expenses, total"],cs:["Other operating expenses, total"]},e.exports["Other receivables_study"]={en:["Other receivables"],cs:["Other receivables"]},e.exports["Other short term debt_study"]={en:["Other short term debt"],cs:["Other short term debt"]},e.exports["Paid in capital_study"]={en:["Paid in capital"],cs:["Paid in capital"]},e.exports["PEG ratio_study"]={en:["PEG ratio"],cs:["PEG ratio"]},e.exports["Piotroski F-score_study"]={en:["Piotroski F-score"],cs:["Piotroski F-score"]},e.exports["Preferred dividends_study"]={en:["Preferred dividends"],cs:["Preferred dividends"]},e.exports["Preferred dividends paid_study"]={en:["Preferred dividends paid"],cs:["Preferred dividends paid"]},e.exports["Preferred stock, carrying value_study"]={en:["Preferred stock, carrying value"],cs:["Preferred stock, carrying value"]},e.exports["Prepaid expenses_study"]={en:["Prepaid expenses"],cs:["Prepaid expenses"]},e.exports["Pretax equity in earnings_study"]={en:["Pretax equity in earnings"],cs:["Pretax equity in earnings"]},e.exports["Pretax income_study"]={en:["Pretax income"],cs:["Pretax income"]},e.exports["Price earnings ratio forward_study"]={en:["Price earnings ratio forward"],cs:["Price earnings ratio forward"]},e.exports["Price sales ratio forward_study"]={en:["Price sales ratio forward"],cs:["Price sales ratio forward"]},e.exports["Price to tangible book ratio_study"]={en:["Price to tangible book ratio"],cs:["Price to tangible book ratio"]},e.exports["Provision for risks & charge_study"]={en:["Provision for risks & charge"],cs:["Provision for risks & charge"]},e.exports["Purchase/acquisition of business_study"]={en:["Purchase/acquisition of business"],cs:["Purchase/acquisition of business"]},e.exports["Purchase of investments_study"]={en:["Purchase of investments"],cs:["Purchase of investments"]},e.exports["Purchase/sale of business, net_study"]={en:["Purchase/sale of business, net"],cs:["Purchase/sale of business, net"]},e.exports["Purchase/sale of investments, net_study"]={en:["Purchase/sale of investments, net"],cs:["Purchase/sale of investments, net"]},e.exports["Quality ratio_study"]={en:["Quality ratio"],cs:["Quality ratio"]},e.exports["Quick ratio_study"]={en:["Quick ratio"],cs:["Quick ratio"]},e.exports["Reduction of long term debt_study"]={en:["Reduction of long term debt"],cs:["Reduction of long term debt"]},
e.exports["Repurchase of common & preferred stock_study"]={en:["Repurchase of common & preferred stock"],cs:["Repurchase of common & preferred stock"]},e.exports["Research & development_study"]={en:["Research & development"],cs:["Research & development"]},e.exports["Research & development to revenue ratio_study"]={en:["Research & development to revenue ratio"],cs:["Research & development to revenue ratio"]},e.exports["Restructuring charge_study"]={en:["Restructuring charge"],cs:["Restructuring charge"]},e.exports["Retained earnings_study"]={en:["Retained earnings"],cs:["Retained earnings"]},e.exports["Return on assets %_study"]={en:["Return on assets %"],cs:["Return on assets %"]},e.exports["Return on equity %_study"]={en:["Return on equity %"],cs:["Return on equity %"]},e.exports["Return on equity adjusted to book value %_study"]={en:["Return on equity adjusted to book value %"],cs:["Return on equity adjusted to book value %"]},e.exports["Return on invested capital %_study"]={en:["Return on invested capital %"],cs:["Return on invested capital %"]},e.exports["Return on tangible assets %_study"]={en:["Return on tangible assets %"],cs:["Return on tangible assets %"]},e.exports["Return on tangible equity %_study"]={en:["Return on tangible equity %"],cs:["Return on tangible equity %"]},e.exports["Revenue estimates_study"]={en:["Revenue estimates"],cs:["Revenue estimates"]},e.exports["Revenue one year growth_study"]={en:["Revenue one year growth"],cs:["Revenue one year growth"]},e.exports["Revenue per employee_study"]={en:["Revenue per employee"],cs:["Revenue per employee"]},e.exports["Sale/maturity of investments_study"]={en:["Sale/maturity of investments"],cs:["Sale/maturity of investments"]},e.exports["Sale of common & preferred stock_study"]={en:["Sale of common & preferred stock"],cs:["Sale of common & preferred stock"]},e.exports["Sale of fixed assets & businesses_study"]={en:["Sale of fixed assets & businesses"],cs:["Sale of fixed assets & businesses"]},e.exports["Selling/general/admin expenses, other_study"]={en:["Selling/general/admin expenses, other"],cs:["Selling/general/admin expenses, other"]},e.exports["Selling/general/admin expenses, total_study"]={en:["Selling/general/admin expenses, total"],cs:["Selling/general/admin expenses, total"]},e.exports["Shareholders' equity_study"]={en:["Shareholders' equity"],cs:["Shareholders' equity"]},e.exports["Shares buyback ratio %_study"]={en:["Shares buyback ratio %"],cs:["Shares buyback ratio %"]},e.exports["Short term debt_study"]={en:["Short term debt"],cs:["Short term debt"]},e.exports["Short term debt excl. current portion of LT debt_study"]={en:["Short term debt excl. current portion of LT debt"],cs:["Short term debt excl. current portion of LT debt"]},e.exports["Short term investments_study"]={en:["Short term investments"],cs:["Short term investments"]},e.exports["Sloan ratio %_study"]={en:["Sloan ratio %"],cs:["Sloan ratio %"]},e.exports["Springate score_study"]={en:["Springate score"],cs:["Springate score"]},e.exports["Sustainable growth rate_study"]={
en:["Sustainable growth rate"],cs:["Sustainable growth rate"]},e.exports["Tangible book value per share_study"]={en:["Tangible book value per share"],cs:["Tangible book value per share"]},e.exports["Tangible common equity ratio_study"]={en:["Tangible common equity ratio"],cs:["Tangible common equity ratio"]},e.exports.Taxes_study={en:["Taxes"],cs:["Taxes"]},e.exports["Tobin's Q (approximate)_study"]={en:["Tobin's Q (approximate)"],cs:["Tobin's Q (approximate)"]},e.exports["Total assets_study"]={en:["Total assets"],cs:["Total assets"]},e.exports["Total cash dividends paid_study"]={en:["Total cash dividends paid"],cs:["Total cash dividends paid"]},e.exports["Total current assets_study"]={en:["Total current assets"],cs:["Total current assets"]},e.exports["Total current liabilities_study"]={en:["Total current liabilities"],cs:["Total current liabilities"]},e.exports["Total debt_study"]={en:["Total debt"],cs:["Total debt"]},e.exports["Total equity_study"]={en:["Total equity"],cs:["Total equity"]},e.exports["Total inventory_study"]={en:["Total inventory"],cs:["Total inventory"]},e.exports["Total liabilities_study"]={en:["Total liabilities"],cs:["Total liabilities"]},e.exports["Total liabilities & shareholders' equities_study"]={en:["Total liabilities & shareholders' equities"],cs:["Total liabilities & shareholders' equities"]},e.exports["Total non-current assets_study"]={en:["Total non-current assets"],cs:["Total non-current assets"]},e.exports["Total non-current liabilities_study"]={en:["Total non-current liabilities"],cs:["Total non-current liabilities"]},e.exports["Total operating expenses_study"]={en:["Total operating expenses"],cs:["Total operating expenses"]},e.exports["Total receivables, net_study"]={en:["Total receivables, net"],cs:["Total receivables, net"]},e.exports["Total revenue_study"]={en:["Total revenue"],cs:["Total revenue"]},e.exports["Treasury stock - common_study"]={en:["Treasury stock - common"],cs:["Treasury stock - common"]},e.exports["Unrealized gain/loss_study"]={en:["Unrealized gain/loss"],cs:["Unrealized gain/loss"]},e.exports["Unusual income/expense_study"]={en:["Unusual income/expense"],cs:["Unusual income/expense"]},e.exports["Zmijewski score_study"]={en:["Zmijewski score"],cs:["Zmijewski score"]},e.exports["Valuation ratios_study"]={en:["Valuation ratios"],cs:["Valuation ratios"]},e.exports["Profitability ratios_study"]={en:["Profitability ratios"],cs:["Profitability ratios"]},e.exports["Liquidity ratios_study"]={en:["Liquidity ratios"],cs:["Liquidity ratios"]},e.exports["Solvency ratios_study"]={en:["Solvency ratios"],cs:["Solvency ratios"]},e.exports["Key stats_study"]={en:["Key stats"],cs:["Key stats"]},e.exports["Accumulation/Distribution_study"]={en:["Accumulation/Distribution"],cs:["Akumulace / Distribuce"]},e.exports["Accumulative Swing Index_study"]={en:["Accumulative Swing Index"],cs:["Accumulative Swing Index"]},e.exports["Advance/Decline_study"]={en:["Advance/Decline"],cs:["Advance/Decline"]},e.exports["All Chart Patterns_study"]={en:["All Chart Patterns"],cs:["All Chart Patterns"]
},e.exports["Arnaud Legoux Moving Average_study"]={en:["Arnaud Legoux Moving Average"],cs:["Arnaud Legoux Moving Average"]},e.exports.Aroon_study={en:["Aroon"],cs:["Aroon"]},e.exports.ASI_study={en:["ASI"],cs:["ASI"]},e.exports["Average Directional Index_study"]={en:["Average Directional Index"],cs:["Average Directional Index"]},e.exports["Average True Range_study"]={en:["Average True Range"],cs:["Průměrný Rozsah Hodnot"]},e.exports["Awesome Oscillator_study"]={en:["Awesome Oscillator"],cs:["Skvělý Oscilátor"]},e.exports["Balance of Power_study"]={en:["Balance of Power"],cs:["Vyvážení Síly"]},e.exports["Bollinger Bands %B_study"]={en:["Bollinger Bands %B"],cs:["Bollingerova pásma %B"]},e.exports["Bollinger Bands Width_study"]={en:["Bollinger Bands Width"],cs:["Bollingerova pásma Šířka"]},e.exports["Bollinger Bands_study"]={en:["Bollinger Bands"],cs:["Bollingerova pásma"]},e.exports["Chaikin Money Flow_study"]={en:["Chaikin Money Flow"],cs:["Chaikin Money Flow"]},e.exports["Chaikin Oscillator_study"]={en:["Chaikin Oscillator"],cs:["Chaikin Oscillator"]},e.exports["Chande Kroll Stop_study"]={en:["Chande Kroll Stop"],cs:["Chande Kroll Stop"]},e.exports["Chande Momentum Oscillator_study"]={en:["Chande Momentum Oscillator"],cs:["Chande Momentum Oscillator"]},e.exports["Chop Zone_study"]={en:["Chop Zone"],cs:["Chop Zone"]},e.exports["Choppiness Index_study"]={en:["Choppiness Index"],cs:["Choppiness Index"]},e.exports["Commodity Channel Index_study"]={en:["Commodity Channel Index"],cs:["Commodity Channel Index"]},e.exports["Connors RSI_study"]={en:["Connors RSI"],cs:["Connors RSI"]},e.exports["Coppock Curve_study"]={en:["Coppock Curve"],cs:["Coppock Curve"]},e.exports["Correlation Coefficient_study"]={en:["Correlation Coefficient"],cs:["Correlation Coefficient"]},e.exports.CRSI_study={en:["CRSI"],cs:["CRSI"]},e.exports["Detrended Price Oscillator_study"]={en:["Detrended Price Oscillator"],cs:["Detrended Price Oscillator"]},e.exports["Directional Movement_study"]={en:["Directional Movement"],cs:["Directional Movement"]},e.exports["Donchian Channels_study"]={en:["Donchian Channels"],cs:["Donchian Channels"]},e.exports["Double EMA_study"]={en:["Double EMA"],cs:["Double EMA"]},e.exports["Ease Of Movement_study"]={en:["Ease Of Movement"],cs:["Ease Of Movement"]},e.exports["Elder Force Index_study"]={en:["Elder Force Index"],cs:["Elder's Force Index"]},e.exports["EMA Cross_study"]={en:["EMA Cross"],cs:["EMA Cross"]},e.exports.Envelopes_study={en:["Envelopes"],cs:["Envelopes"]},e.exports["Fisher Transform_study"]={en:["Fisher Transform"],cs:["Fisher Transform"]},e.exports["Fixed Range_study"]={en:["Fixed Range"],cs:["Fixed Range"]},e.exports["Fixed Range Volume Profile_study"]={en:["Fixed Range Volume Profile"],cs:["Fixed Range Volume Profile"]},e.exports["Guppy Multiple Moving Average_study"]={en:["Guppy Multiple Moving Average"],cs:["Guppy Multiple Moving Average"]},e.exports["Historical Volatility_study"]={en:["Historical Volatility"],cs:["Historical Volatility"]},e.exports["Hull Moving Average_study"]={
en:["Hull Moving Average"],cs:["Hull Moving Average"]},e.exports["Keltner Channels_study"]={en:["Keltner Channels"],cs:["Keltner Channels"]},e.exports["Klinger Oscillator_study"]={en:["Klinger Oscillator"],cs:["Klinger Oscillator"]},e.exports["Know Sure Thing_study"]={en:["Know Sure Thing"],cs:["Know Sure Thing"]},e.exports["Least Squares Moving Average_study"]={en:["Least Squares Moving Average"],cs:["Least Squares Moving Average"]},e.exports["Linear Regression Curve_study"]={en:["Linear Regression Curve"],cs:["Linear Regression Curve"]},e.exports["MA Cross_study"]={en:["MA Cross"],cs:["MA Cross"]},e.exports["MA with EMA Cross_study"]={en:["MA with EMA Cross"],cs:["MA with EMA Cross"]},e.exports["MA/EMA Cross_study"]={en:["MA/EMA Cross"],cs:["MA/EMA Cross"]},e.exports.MACD_study={en:["MACD"],cs:["MACD"]},e.exports["Mass Index_study"]={en:["Mass Index"],cs:["Mass Index"]},e.exports["McGinley Dynamic_study"]={en:["McGinley Dynamic"],cs:["McGinley Dynamic"]},e.exports.Median_study={en:["Median"],cs:["Střední"]},e.exports.Momentum_study={en:["Momentum"],cs:["Momentum"]},e.exports["Money Flow_study"]={en:["Money Flow"],cs:["Money Flow"]},e.exports["Moving Average Channel_study"]={en:["Moving Average Channel"],cs:["Moving Average Channel"]},e.exports["Moving Average Exponential_study"]={en:["Moving Average Exponential"],cs:["Exponenciální Pohyblivý Průměr"]},e.exports["Moving Average Weighted_study"]={en:["Moving Average Weighted"],cs:["Moving Average Weighted"]},e.exports["Moving Average Simple_study"]={en:["Moving Average Simple"],cs:["Moving Average Simple"]},e.exports["Net Volume_study"]={en:["Net Volume"],cs:["Čistý Objem"]},e.exports["On Balance Volume_study"]={en:["On Balance Volume"],cs:["On Balance Volume"]},e.exports["Parabolic SAR_study"]={en:["Parabolic SAR"],cs:["Parabolic SAR"]},e.exports["Pivot Points Standard_study"]={en:["Pivot Points Standard"],cs:["Pivot Points Standard"]},e.exports["Periodic Volume Profile_study"]={en:["Periodic Volume Profile"],cs:["Periodic Volume Profile"]},e.exports["Price Channel_study"]={en:["Price Channel"],cs:["Price Channel"]},e.exports["Price Oscillator_study"]={en:["Price Oscillator"],cs:["Price Oscillator"]},e.exports["Price Volume Trend_study"]={en:["Price Volume Trend"],cs:["Price Volume Trend"]},e.exports["Rate Of Change_study"]={en:["Rate Of Change"],cs:["Rate Of Change"]},e.exports["Relative Strength Index_study"]={en:["Relative Strength Index"],cs:["Relative Strength Index"]},e.exports["Relative Vigor Index_study"]={en:["Relative Vigor Index"],cs:["Relative Vigor Index"]},e.exports["Relative Volatility Index_study"]={en:["Relative Volatility Index"],cs:["Relative Volatility Index"]},e.exports["Relative Volume at Time_study"]={en:["Relative Volume at Time"],cs:["Relative Volume at Time"]},e.exports["Session Volume_study"]={en:["Session Volume"],cs:["Session Volume"]},e.exports["Session Volume HD_study"]={en:["Session Volume HD"],cs:["Session Volume"]},e.exports["Session Volume Profile_study"]={en:["Session Volume Profile"],cs:["Session Volume Profile"]},
e.exports["Session Volume Profile HD_study"]={en:["Session Volume Profile HD"],cs:["Session Volume Profile HD"]},e.exports["SMI Ergodic Indicator/Oscillator_study"]={en:["SMI Ergodic Indicator/Oscillator"],cs:["SMI Ergodic Indicator/Oscillator"]},e.exports["Smoothed Moving Average_study"]={en:["Smoothed Moving Average"],cs:["Smoothed Moving Average"]},e.exports["Stochastic Momentum Index_study"]={en:["Stochastic Momentum Index"],cs:["Stochastic Momentum Index"]},e.exports.Stoch_study={en:["Stoch"],cs:["Stoch"]},e.exports["Stochastic RSI_study"]={en:["Stochastic RSI"],cs:["Stochastic RSI"]},e.exports.Stochastic_study={en:["Stochastic"],cs:["Stochastic"]},e.exports["Time Weighted Average Price_study"]={en:["Time Weighted Average Price"],cs:["Time Weighted Average Price"]},e.exports["Triple EMA_study"]={en:["Triple EMA"],cs:["Triple EMA"]},e.exports.TRIX_study={en:["TRIX"],cs:["TRIX"]},e.exports["True Strength Indicator_study"]={en:["True Strength Indicator"],cs:["Pravdivý Indikátor Síly"]},e.exports["Ultimate Oscillator_study"]={en:["Ultimate Oscillator"],cs:["Ultimate Oscillator"]},e.exports["Visible Range_study"]={en:["Visible Range"],cs:["Visible Range"]},e.exports["Visible Range Volume Profile_study"]={en:["Visible Range Volume Profile"],cs:["Visible Range Volume Profile"]},e.exports["Volume Oscillator_study"]={en:["Volume Oscillator"],cs:["Volume Oscillator"]},e.exports.Volume_study={en:["Volume"],cs:["Objem"]},e.exports.Vol_study={en:["Vol"],cs:["Vol"]},e.exports["Vortex Indicator_study"]={en:["Vortex Indicator"],cs:["Vortex Indicator"]},e.exports.VWAP_study={en:["VWAP"],cs:["VWAP"]},e.exports.VWMA_study={en:["VWMA"],cs:["VWMA"]},e.exports["Williams %R_study"]={en:["Williams %R"],cs:["Williams %R"]},e.exports["Williams Alligator_study"]={en:["Williams Alligator"],cs:["Williams Alligator"]},e.exports["Williams Fractal_study"]={en:["Williams Fractal"],cs:["Williams Fractal"]},e.exports["Zig Zag_study"]={en:["Zig Zag"],cs:["Zig Zag"]},e.exports["24-hour Volume_study"]={en:["24-hour Volume"],cs:["24-hour Volume"]},e.exports["Ease of Movement_study"]={en:["Ease of Movement"],cs:["Ease Of Movement"]},e.exports["Elders Force Index_study"]={en:["Elders Force Index"],cs:["Elders Force Index"]},e.exports.Envelope_study={en:["Envelope"],cs:["Envelope"]},e.exports.Gaps_study={en:["Gaps"],cs:["Gaps"]},e.exports["Linear Regression Channel_study"]={en:["Linear Regression Channel"],cs:["Linear Regression Channel"]},e.exports["Moving Average Ribbon_study"]={en:["Moving Average Ribbon"],cs:["Moving Average Ribbon"]},e.exports["Multi-Time Period Charts_study"]={en:["Multi-Time Period Charts"],cs:["Multi-Time Period Charts"]},e.exports["Open Interest_study"]={en:["Open Interest"],cs:["Open Interest"]},e.exports["Rob Booker - Intraday Pivot Points_study"]={en:["Rob Booker - Intraday Pivot Points"],cs:["Rob Booker - Intraday Pivot Points"]},e.exports["Rob Booker - Knoxville Divergence_study"]={en:["Rob Booker - Knoxville Divergence"],cs:["Rob Booker - Knoxville Divergence"]},e.exports["Rob Booker - Missed Pivot Points_study"]={
en:["Rob Booker - Missed Pivot Points"],cs:["Rob Booker - Missed Pivot Points"]},e.exports["Rob Booker - Reversal_study"]={en:["Rob Booker - Reversal"],cs:["Rob Booker - Reversal"]},e.exports["Rob Booker - Ziv Ghost Pivots_study"]={en:["Rob Booker - Ziv Ghost Pivots"],cs:["Rob Booker - Ziv Ghost Pivots"]},e.exports.Supertrend_study={en:["Supertrend"],cs:["Supertrend"]},e.exports["Technical Ratings_study"]={en:["Technical Ratings"],cs:["Technical Ratings"]},e.exports["True Strength Index_study"]={en:["True Strength Index"],cs:["True Strength Index"]},e.exports["Up/Down Volume_study"]={en:["Up/Down Volume"],cs:["Up/Down Volume"]},e.exports["Visible Average Price_study"]={en:["Visible Average Price"],cs:["Visible Average Price"]},e.exports["Williams Fractals_study"]={en:["Williams Fractals"],cs:["Williams Fractals"]},e.exports["Keltner Channels Strategy_study"]={en:["Keltner Channels Strategy"],cs:["Keltner Channels Strategy"]},e.exports["Rob Booker - ADX Breakout_study"]={en:["Rob Booker - ADX Breakout"],cs:["Rob Booker - ADX Breakout"]},e.exports["Supertrend Strategy_study"]={en:["Supertrend Strategy"],cs:["Supertrend Strategy"]},e.exports["Technical Ratings Strategy_study"]={en:["Technical Ratings Strategy"],cs:["Technical Ratings Strategy"]},e.exports["Auto Anchored Volume Profile_study"]={en:["Auto Anchored Volume Profile"],cs:["Auto Anchored Volume Profile"]},e.exports["Auto Fib Extension_study"]={en:["Auto Fib Extension"],cs:["Auto Fib Extension"]},e.exports["Auto Fib Retracement_study"]={en:["Auto Fib Retracement"],cs:["Auto Fib Retracement"]},e.exports["Auto Pitchfork_study"]={en:["Auto Pitchfork"],cs:["Auto Pitchfork"]},e.exports["Bearish Flag Chart Pattern_study"]={en:["Bearish Flag Chart Pattern"],cs:["Bearish Flag Chart Pattern"]},e.exports["Bullish Flag Chart Pattern_study"]={en:["Bullish Flag Chart Pattern"],cs:["Bullish Flag Chart Pattern"]},e.exports["Bearish Pennant Chart Pattern_study"]={en:["Bearish Pennant Chart Pattern"],cs:["Bearish Pennant Chart Pattern"]},e.exports["Bullish Pennant Chart Pattern_study"]={en:["Bullish Pennant Chart Pattern"],cs:["Bullish Pennant Chart Pattern"]},e.exports["Double Bottom Chart Pattern_study"]={en:["Double Bottom Chart Pattern"],cs:["Double Bottom Chart Pattern"]},e.exports["Double Top Chart Pattern_study"]={en:["Double Top Chart Pattern"],cs:["Double Top Chart Pattern"]},e.exports["Elliott Wave Chart Pattern_study"]={en:["Elliott Wave Chart Pattern"],cs:["Elliott Wave Chart Pattern"]},e.exports["Falling Wedge Chart Pattern_study"]={en:["Falling Wedge Chart Pattern"],cs:["Falling Wedge Chart Pattern"]},e.exports["Head And Shoulders Chart Pattern_study"]={},e.exports["Inverse Head And Shoulders Chart Pattern_study"]={},e.exports["Rectangle Chart Pattern_study"]={en:["Rectangle Chart Pattern"],cs:["Rectangle Chart Pattern"]},e.exports["Rising Wedge Chart Pattern_study"]={en:["Rising Wedge Chart Pattern"],cs:["Rising Wedge Chart Pattern"]},e.exports["Triangle Chart Pattern_study"]={en:["Triangle Chart Pattern"],cs:["Triangle Chart Pattern"]},
e.exports["Triple Bottom Chart Pattern_study"]={en:["Triple Bottom Chart Pattern"],cs:["Triple Bottom Chart Pattern"]},e.exports["Triple Top Chart Pattern_study"]={en:["Triple Top Chart Pattern"],cs:["Triple Top Chart Pattern"]},e.exports["VWAP Auto Anchored_study"]={en:["VWAP Auto Anchored"],cs:["VWAP Auto Anchored"]},e.exports["*All Candlestick Patterns*_study"]={en:["*All Candlestick Patterns*"],cs:["*All Candlestick Patterns*"]},e.exports["Abandoned Baby - Bearish_study"]={en:["Abandoned Baby - Bearish"],cs:["Abandoned Baby - Bearish"]},e.exports["Abandoned Baby - Bullish_study"]={en:["Abandoned Baby - Bullish"],cs:["Abandoned Baby - Bullish"]},e.exports["Dark Cloud Cover - Bearish_study"]={en:["Dark Cloud Cover - Bearish"],cs:["Dark Cloud Cover - Bearish"]},e.exports["Doji Star - Bearish_study"]={en:["Doji Star - Bearish"],cs:["Doji Star - Bearish"]},e.exports["Doji Star - Bullish_study"]={en:["Doji Star - Bullish"],cs:["Doji Star - Bullish"]},e.exports["Downside Tasuki Gap - Bearish_study"]={en:["Downside Tasuki Gap - Bearish"],cs:["Downside Tasuki Gap - Bearish"]},e.exports["Dragonfly Doji - Bullish_study"]={en:["Dragonfly Doji - Bullish"],cs:["Dragonfly Doji - Bullish"]},e.exports["Engulfing - Bearish_study"]={en:["Engulfing - Bearish"],cs:["Engulfing - Bearish"]},e.exports["Engulfing - Bullish_study"]={en:["Engulfing - Bullish"],cs:["Engulfing - Bullish"]},e.exports["Evening Doji Star - Bearish_study"]={en:["Evening Doji Star - Bearish"],cs:["Evening Doji Star - Bearish"]},e.exports["Evening Star - Bearish_study"]={en:["Evening Star - Bearish"],cs:["Evening Star - Bearish"]},e.exports["Falling Three Methods - Bearish_study"]={en:["Falling Three Methods - Bearish"],cs:["Falling Three Methods - Bearish"]},e.exports["Falling Window - Bearish_study"]={en:["Falling Window - Bearish"],cs:["Falling Window - Bearish"]},e.exports["Gravestone Doji - Bearish_study"]={en:["Gravestone Doji - Bearish"],cs:["Gravestone Doji - Bearish"]},e.exports["Hammer - Bullish_study"]={en:["Hammer - Bullish"],cs:["Hammer - Bullish"]},e.exports["Hanging Man - Bearish_study"]={en:["Hanging Man - Bearish"],cs:["Hanging Man - Bearish"]},e.exports["Harami - Bearish_study"]={en:["Harami - Bearish"],cs:["Harami - Bearish"]},e.exports["Harami - Bullish_study"]={en:["Harami - Bullish"],cs:["Harami - Bullish"]},e.exports["Harami Cross - Bearish_study"]={en:["Harami Cross - Bearish"],cs:["Harami Cross - Bearish"]},e.exports["Harami Cross - Bullish_study"]={en:["Harami Cross - Bullish"],cs:["Harami Cross - Bullish"]},e.exports["Inverted Hammer - Bullish_study"]={en:["Inverted Hammer - Bullish"],cs:["Inverted Hammer - Bullish"]},e.exports["Kicking - Bearish_study"]={en:["Kicking - Bearish"],cs:["Kicking - Bearish"]},e.exports["Kicking - Bullish_study"]={en:["Kicking - Bullish"],cs:["Kicking - Bullish"]},e.exports["Long Lower Shadow - Bullish_study"]={en:["Long Lower Shadow - Bullish"],cs:["Long Lower Shadow - Bullish"]},e.exports["Long Upper Shadow - Bearish_study"]={en:["Long Upper Shadow - Bearish"],cs:["Long Upper Shadow - Bearish"]},
e.exports["Marubozu Black - Bearish_study"]={en:["Marubozu Black - Bearish"],cs:["Marubozu Black - Bearish"]},e.exports["Marubozu White - Bullish_study"]={en:["Marubozu White - Bullish"],cs:["Marubozu White - Bullish"]},e.exports["Morning Doji Star - Bullish_study"]={en:["Morning Doji Star - Bullish"],cs:["Morning Doji Star - Bullish"]},e.exports["Morning Star - Bullish_study"]={en:["Morning Star - Bullish"],cs:["Morning Star - Bullish"]},e.exports["On Neck - Bearish_study"]={en:["On Neck - Bearish"],cs:["On Neck - Bearish"]},e.exports["Piercing - Bullish_study"]={en:["Piercing - Bullish"],cs:["Piercing - Bullish"]},e.exports["Rising Three Methods - Bullish_study"]={en:["Rising Three Methods - Bullish"],cs:["Rising Three Methods - Bullish"]},e.exports["Rising Window - Bullish_study"]={en:["Rising Window - Bullish"],cs:["Rising Window - Bullish"]},e.exports["Shooting Star - Bearish_study"]={en:["Shooting Star - Bearish"],cs:["Shooting Star - Bearish"]},e.exports["Three Black Crows - Bearish_study"]={en:["Three Black Crows - Bearish"],cs:["Three Black Crows - Bearish"]},e.exports["Three White Soldiers - Bullish_study"]={en:["Three White Soldiers - Bullish"],cs:["Three White Soldiers - Bullish"]},e.exports["Tri-Star - Bearish_study"]={en:["Tri-Star - Bearish"],cs:["Tri-Star - Bearish"]},e.exports["Tri-Star - Bullish_study"]={en:["Tri-Star - Bullish"],cs:["Tri-Star - Bullish"]},e.exports["Tweezer Top - Bearish_study"]={en:["Tweezer Top - Bearish"],cs:["Tweezer Top - Bearish"]},e.exports["Upside Tasuki Gap - Bullish_study"]={en:["Upside Tasuki Gap - Bullish"],cs:["Upside Tasuki Gap - Bullish"]},e.exports.SuperTrend_study={en:["SuperTrend"],cs:["SuperTrend"]},e.exports["Average Price_study"]={en:["Average Price"],cs:["Average Price"]},e.exports["Typical Price_study"]={en:["Typical Price"],cs:["Typical Price"]},e.exports["Median Price_study"]={en:["Median Price"],cs:["Median Price"]},e.exports["Money Flow Index_study"]={en:["Money Flow Index"],cs:["Money Flow Index"]},e.exports["Moving Average Double_study"]={en:["Moving Average Double"],cs:["Moving Average Double"]},e.exports["Moving Average Triple_study"]={en:["Moving Average Triple"],cs:["Moving Average Triple"]},e.exports["Moving Average Adaptive_study"]={en:["Moving Average Adaptive"],cs:["Moving Average Adaptive"]},e.exports["Moving Average Hamming_study"]={en:["Moving Average Hamming"],cs:["Moving Average Hamming"]},e.exports["Moving Average Modified_study"]={en:["Moving Average Modified"],cs:["Moving Average Modified"]},e.exports["Moving Average Multiple_study"]={en:["Moving Average Multiple"],cs:["Moving Average Multiple"]},e.exports["Linear Regression Slope_study"]={en:["Linear Regression Slope"],cs:["Linear Regression Slope"]},e.exports["Standard Error_study"]={en:["Standard Error"],cs:["Standard Error"]},e.exports["Standard Error Bands_study"]={en:["Standard Error Bands"],cs:["Standard Error Bands"]},e.exports["Correlation - Log_study"]={en:["Correlation - Log"],cs:["Correlation - Log"]},e.exports["Standard Deviation_study"]={en:["Standard Deviation"],
cs:["Standard Deviation"]},e.exports["Chaikin Volatility_study"]={en:["Chaikin Volatility"],cs:["Chaikin Volatility"]},e.exports["Volatility Close-to-Close_study"]={en:["Volatility Close-to-Close"],cs:["Volatility Close-to-Close"]},e.exports["Volatility Zero Trend Close-to-Close_study"]={en:["Volatility Zero Trend Close-to-Close"],cs:["Volatility Zero Trend Close-to-Close"]},e.exports["Volatility O-H-L-C_study"]={en:["Volatility O-H-L-C"],cs:["Volatility O-H-L-C"]},e.exports["Volatility Index_study"]={en:["Volatility Index"],cs:["Volatility Index"]},e.exports["Trend Strength Index_study"]={en:["Trend Strength Index"],cs:["Trend Strength Index"]},e.exports["Majority Rule_study"]={en:["Majority Rule"],cs:["Majority Rule"]},e.exports["Advance Decline Line_study"]={en:["Advance Decline Line"],cs:["Advance Decline Line"]},e.exports["Advance Decline Ratio_study"]={en:["Advance Decline Ratio"],cs:["Advance Decline Ratio"]},e.exports["Advance/Decline Ratio (Bars)_study"]={en:["Advance/Decline Ratio (Bars)"],cs:["Advance/Decline Ratio (Bars)"]},e.exports["BarUpDn Strategy_study"]={en:["BarUpDn Strategy"],cs:["BarUpDn Strategy"]},e.exports["Bollinger Bands Strategy directed_study"]={en:["Bollinger Bands Strategy directed"],cs:["Bollinger Bands Strategy directed"]},e.exports["Bollinger Bands Strategy_study"]={en:["Bollinger Bands Strategy"],cs:["Bollinger Bands Strategy"]},e.exports.ChannelBreakOutStrategy_study={en:["ChannelBreakOutStrategy"],cs:["ChannelBreakOutStrategy"]},e.exports.Compare_study={en:["Compare"],cs:["Porovnat"]},e.exports["Conditional Expressions_study"]={en:["Conditional Expressions"],cs:["Conditional Expressions"]},e.exports.ConnorsRSI_study={en:["ConnorsRSI"],cs:["ConnorsRSI"]},e.exports["Consecutive Up/Down Strategy_study"]={en:["Consecutive Up/Down Strategy"],cs:["Consecutive Up/Down Strategy"]},e.exports["Cumulative Volume Index_study"]={en:["Cumulative Volume Index"],cs:["Cumulative Volume Index"]},e.exports["Divergence Indicator_study"]={en:["Divergence Indicator"],cs:["Divergence Indicator"]},e.exports["Greedy Strategy_study"]={en:["Greedy Strategy"],cs:["Greedy Strategy"]},e.exports["InSide Bar Strategy_study"]={en:["InSide Bar Strategy"],cs:["InSide Bar Strategy"]},e.exports["Keltner Channel Strategy_study"]={en:["Keltner Channel Strategy"],cs:["Keltner Channel Strategy"]},e.exports["Linear Regression_study"]={en:["Linear Regression"],cs:["Linear Regression"]},e.exports["MACD Strategy_study"]={en:["MACD Strategy"],cs:["MACD Strategy"]},e.exports["Momentum Strategy_study"]={en:["Momentum Strategy"],cs:["Momentum Strategy"]},e.exports["Moon Phases_study"]={en:["Moon Phases"],cs:["Moon Phases"]},e.exports["Moving Average Convergence/Divergence_study"]={en:["Moving Average Convergence/Divergence"],cs:["Moving Average Convergence/Divergence"]},e.exports["MovingAvg Cross_study"]={en:["MovingAvg Cross"],cs:["MovingAvg Cross"]},e.exports["MovingAvg2Line Cross_study"]={en:["MovingAvg2Line Cross"],cs:["MovingAvg2Line Cross"]},e.exports["OutSide Bar Strategy_study"]={en:["OutSide Bar Strategy"],
cs:["OutSide Bar Strategy"]},e.exports.Overlay_study={en:["Overlay"],cs:["Overlay"]},e.exports["Parabolic SAR Strategy_study"]={en:["Parabolic SAR Strategy"],cs:["Parabolic SAR Strategy"]},e.exports["Pivot Extension Strategy_study"]={en:["Pivot Extension Strategy"],cs:["Pivot Extension Strategy"]},e.exports["Pivot Points High Low_study"]={en:["Pivot Points High Low"],cs:["Pivot Points High Low"]},e.exports["Pivot Reversal Strategy_study"]={en:["Pivot Reversal Strategy"],cs:["Pivot Reversal Strategy"]},e.exports["Price Channel Strategy_study"]={en:["Price Channel Strategy"],cs:["Price Channel Strategy"]},e.exports["RSI Strategy_study"]={en:["RSI Strategy"],cs:["RSI Strategy"]},e.exports["SMI Ergodic Indicator_study"]={en:["SMI Ergodic Indicator"],cs:["SMI Ergodic Indicator"]},e.exports["SMI Ergodic Oscillator_study"]={en:["SMI Ergodic Oscillator"],cs:["SMI Ergodic Oscillator"]},e.exports["Stochastic Slow Strategy_study"]={en:["Stochastic Slow Strategy"],cs:["Stochastic Slow Strategy"]},e.exports["Volatility Stop_study"]={en:["Volatility Stop"],cs:["Volatility Stop"]},e.exports["Volty Expan Close Strategy_study"]={en:["Volty Expan Close Strategy"],cs:["Volty Expan Close Strategy"]},e.exports["Woodies CCI_study"]={en:["Woodies CCI"],cs:["Woodies CCI"]},e.exports["Anchored Volume Profile_study"]={en:["Anchored Volume Profile"],cs:["Anchored Volume Profile"]},e.exports["Trading Sessions_study"]={en:["Trading Sessions"],cs:["Trading Sessions"]},e.exports["Cup and Handle Chart Pattern_study"]={en:["Cup and Handle Chart Pattern"],cs:["Cup and Handle Chart Pattern"]},e.exports["Inverted Cup and Handle Chart Pattern_study"]={en:["Inverted Cup and Handle Chart Pattern"],cs:["Inverted Cup and Handle Chart Pattern"]},e.exports["Head and Shoulders Chart Pattern_study"]={en:["Head and Shoulders Chart Pattern"],cs:["Head and Shoulders Chart Pattern"]},e.exports["Inverted Head and Shoulders Chart Pattern_study"]={en:["Inverted Head and Shoulders Chart Pattern"],cs:["Inverted Head and Shoulders Chart Pattern"]}},89633:e=>{e.exports={en:["Anchored Volume Profile"],cs:["Anchored Volume Profile"]}},25705:e=>{e.exports={en:["Fixed Range Volume Profile"],cs:["Fixed Range Volume Profile"]}},24261:e=>{e.exports={en:["Vol"],cs:["Vol"]}},51077:e=>{e.exports={en:["Minor"],cs:["Minor"]}},922:e=>{e.exports={en:["Minute"],cs:["Minuta"]}},91405:e=>{e.exports={en:["Text"],cs:["Text"]}},78972:e=>{e.exports={en:["Couldn't copy"],cs:["Couldn't copy"]}},10615:e=>{e.exports={en:["Couldn't cut"],cs:["Couldn't cut"]}},81518:e=>{e.exports={en:["Couldn't paste"],cs:["Couldn't paste"]}},83140:e=>{e.exports={en:["Countdown to bar close"],cs:["Countdown To Bar Close"]}},10871:e=>{e.exports={en:["Colombo"],cs:["Colombo"]}},55761:e=>{e.exports={en:["Columns"],cs:["Sloupce"]}},9818:e=>{e.exports={en:["Comment"],cs:["Komentář"]}},53942:e=>{e.exports={en:["Compare or Add Symbol"],cs:["Porovnat nebo přidat symbol"]}},12086:e=>{e.exports={en:["Compilation error"],cs:["Compilation error"]}},48141:e=>{e.exports={en:["Confirm Inputs"],cs:["Confirm Inputs"]}},38917:e=>{
e.exports={en:["Copenhagen"],cs:["Copenhagen"]}},49680:e=>{e.exports={en:["Copy"],cs:["Kopírovat"]}},66134:e=>{e.exports={en:["Copy Chart Layout"],cs:["Copy Chart Layout"]}},63553:e=>{e.exports={en:["Copy price"],cs:["Copy price"]}},65736:e=>{e.exports={en:["Cairo"],cs:["Cairo"]}},25381:e=>{e.exports={en:["Callout"],cs:["Popisek"]}},45054:e=>{e.exports={en:["Candles"],cs:["Svíčky"]}},30948:e=>{e.exports={en:["Caracas"],cs:["Caracas"]}},70409:e=>{e.exports={en:["Casablanca"],cs:["Casablanca"]}},37276:e=>{e.exports={en:["Change"],cs:["Change"]}},85124:e=>{e.exports={en:["Change Symbol"],cs:["Změnit symbol"]}},2569:e=>{e.exports={en:["Change interval"],cs:["Změnit Interval"]}},9687:e=>{e.exports={en:["Change interval. Press number or comma"],cs:["Change interval. Press number or comma"]}},36332:e=>{e.exports={en:["Change symbol. Start typing symbol name"],cs:["Change symbol. Start typing symbol name"]}},48566:e=>{e.exports={en:["Change scale currency"],cs:["Change scale currency"]}},85110:e=>{e.exports={en:["Change scale unit"],cs:["Change scale unit"]}},56275:e=>{e.exports={en:["Chart #{index}"],cs:["Chart #{index}"]}},39950:e=>{e.exports={en:["Chart Properties"],cs:["Vlastnosti grafu"]}},98856:e=>{e.exports={en:["Chart by TradingView"],cs:["Chart by TradingView"]}},1136:e=>{e.exports={en:["Chart for {symbol}, {interval}"],cs:["Chart for {symbol}, {interval}"]}},69804:e=>{e.exports={en:["Chart image copied to clipboard {emoji}"],cs:["Chart image copied to clipboard {emoji}"]}},6655:e=>{e.exports={en:["Chart image embed code copied to clipboard {emoji}"],cs:["Chart image embed code copied to clipboard {emoji}"]}},36549:e=>{e.exports={en:["Chatham Islands"],cs:["Chatham Islands"]}},72452:e=>{e.exports={en:["Chicago"],cs:["Chicago"]}},50349:e=>{e.exports={en:["Chongqing"],cs:["Chongqing"]}},91944:e=>{e.exports={en:["Circle"],cs:["Circle"]}},14985:e=>{e.exports={en:["Click to set a point"],cs:["Klepnutím nastavte bod"]}},12537:e=>{e.exports={en:["Clone"],cs:["Klonovat"]}},62578:e=>{e.exports={en:["Close"],cs:["Zavřít"]}},264:e=>{e.exports={en:["Create limit order"],cs:["Create limit order"]}},6969:e=>{e.exports={en:["Cross"],cs:["Kříž"]}},74334:e=>{e.exports={en:["Cross Line"],cs:["Cross Line"]}},59396:e=>{e.exports={en:["Currencies"],cs:["Měny"]}},20177:e=>{e.exports={en:["Current interval and above"],cs:["Current interval and above"]}},494:e=>{e.exports={en:["Current interval and below"],cs:["Current interval and below"]}},60668:e=>{e.exports={en:["Current interval only"],cs:["Current interval only"]}},78609:e=>{e.exports={en:["Curve"],cs:["Curve"]}},87380:e=>{e.exports={en:["Cycle"],cs:["Cyklus"]}},84031:e=>{e.exports={en:["Cyclic Lines"],cs:["Cyklické linie"]}},93191:e=>{e.exports={en:["Cypher Pattern"],cs:["Cypher Pattern"]}},7219:e=>{e.exports={en:["A layout with that name already exists"],cs:["A layout with that name already exists"]}},67635:e=>{e.exports={en:["A layout with that name already exists. Do you want to overwrite it?"],cs:["A layout with that name already exists. Do you want to overwrite it?"]}},
46712:e=>{e.exports={en:["ABCD Pattern"],cs:["ABCD model"]}},36485:e=>{e.exports={en:["Amsterdam"],cs:["Amsterdam"]}},42630:e=>{e.exports={en:["Anchorage"],cs:["Anchorage"]}},63209:e=>{e.exports={en:["Anchored Note"],cs:["Ukotvena Poznámka"]}},42669:e=>{e.exports={en:["Anchored Text"],cs:["Ukotvený text"]}},84541:e=>{e.exports={en:["Anchored VWAP"],cs:["Anchored VWAP"]}},77401:e=>{e.exports={en:["Access error"],cs:["Access error"]}},46501:e=>{e.exports={en:["Add Symbol"],cs:["Přidat symbol"]}},69709:e=>{e.exports={en:["Add alert on {title}"],cs:["Add alert on {title}"]}},89295:e=>{e.exports={en:["Add alert on {title} at {price}"],cs:["Add alert on {title} at {price}"]}},2439:e=>{e.exports={en:["Add financial metric for {instrumentName}"],cs:["Add financial metric for {instrumentName}"]}},35088:e=>{e.exports={en:["Add indicator/strategy on {studyTitle}"],cs:["Add indicator/strategy on {studyTitle}"]}},39873:e=>{e.exports={en:["Add text note for {symbol}"],cs:["Add Text Note for {symbol}"]}},35679:e=>{e.exports={en:["Add this financial metric to entire layout"],cs:["Add this financial metric to entire layout"]}},10996:e=>{e.exports={en:["Add this financial metric to favorites"],cs:["Add this financial metric to favorites"]}},26090:e=>{e.exports={en:["Add this indicator to entire layout"],cs:["Add this indicator to entire layout"]}},92957:e=>{e.exports={en:["Add this indicator to favorites"],cs:["Add this indicator to favorites"]}},95754:e=>{e.exports={en:["Add this strategy to entire layout"],cs:["Add this strategy to entire layout"]}},39010:e=>{e.exports={en:["Add this symbol to entire layout"],cs:["Add this symbol to entire layout"]}},426:e=>{e.exports={en:["Adelaide"],cs:["Adelaide"]}},40452:e=>{e.exports={en:["Always invisible"],cs:["Always Invisible"]}},36299:e=>{e.exports={en:["Always visible"],cs:["Always Visible"]}},81442:e=>{e.exports={en:["All indicators and drawing tools"],cs:["Všechny indikátory a kreslící nástroje"]}},58026:e=>{e.exports={en:["All intervals"],cs:["All intervals"]}},78358:e=>{e.exports={en:["Apply default"],cs:["Použít výchozí"]}},22437:e=>{e.exports={en:["Apply these indicators to entire layout"],cs:["Apply these indicators to entire layout"]}},27072:e=>{e.exports={en:["Apr"],cs:["Duben"]}},59324:e=>{e.exports={en:["Arc"],cs:["Oblouk"]}},34456:e=>{e.exports={en:["Area"],cs:["Oblast"]}},11858:e=>{e.exports={en:["Arrow"],cs:["Šipka"]}},34247:e=>{e.exports={en:["Arrow Down"],cs:["Arrow Down"]}},36352:e=>{e.exports={en:["Arrow Marker"],cs:["Arrow Marker"]}},73193:e=>{e.exports={en:["Arrow Mark Down"],cs:["Šipka dolů"]}},1949:e=>{e.exports={en:["Arrow Mark Left"],cs:["Šipka doleva"]}},86275:e=>{e.exports={en:["Arrow Mark Right"],cs:["Šipka doprava"]}},62453:e=>{e.exports={en:["Arrow Mark Up"],cs:["Šipka nahoru"]}},77231:e=>{e.exports={en:["Arrow Up"],cs:["Arrow Up"]}},98128:e=>{e.exports={en:["Astana"],cs:["Astana"]}},63627:e=>{e.exports={en:["Ashgabat"],cs:["Ashkhabad"]}},72445:e=>{e.exports={en:["At close"],cs:["At close"]}},73702:e=>{e.exports={en:["Athens"],cs:["Athény"]}},21469:e=>{
e.exports={en:["Auto"],cs:["Auto"]}},24157:e=>{e.exports={en:["Auto (fits data to screen)"],cs:["Auto (Fits Data To Screen)"]}},46450:e=>{e.exports={en:["Aug"],cs:["Srpen"]}},21841:e=>{e.exports={en:["Average close price label"],cs:["Average close price label"]}},16138:e=>{e.exports={en:["Average close price line"],cs:["Average close price line"]}},73025:e=>{e.exports={en:["Avg"],cs:["Avg"]}},87580:e=>{e.exports={en:["Azores"]}},73905:e=>{e.exports={en:["Bogota"],cs:["Bogota"]}},90594:e=>{e.exports={en:["Bahrain"],cs:["Bahrain"]}},70540:e=>{e.exports={en:["Balloon"],cs:["Bublina"]}},47045:e=>{e.exports={en:["Bangkok"],cs:["Bangkok"]}},76651:e=>{e.exports={en:["Bar Replay isn't available for this chart type. Do you want to exit Bar Replay?"],cs:["Bar Replay isn't available for this chart type. Do you want to exit Bar Replay?"]}},68054:e=>{e.exports={en:["Bar Replay isn't available for this time interval. Do you want to exit Bar Replay?"],cs:["Bar Replay isn't available for this time interval. Do you want to exit Bar Replay?"]}},27377:e=>{e.exports={en:["Bars"],cs:["Sloupcový Graf"]}},81994:e=>{e.exports={en:["Bars Pattern"],cs:["Předloha grafu"]}},59213:e=>{e.exports={en:["Baseline"],cs:["Baseline"]}},71797:e=>{e.exports={en:["Belgrade"],cs:["Belgrade"]}},64313:e=>{e.exports={en:["Berlin"],cs:["Berlín"]}},43539:e=>{e.exports={en:["Brush"],cs:["Štětec"]}},91499:e=>{e.exports={en:["Brussels"],cs:["Brussels"]}},70876:e=>{e.exports={en:["Bratislava"],cs:["Bratislava"]}},55481:e=>{e.exports={en:["Bring forward"],cs:["Posunout dopředu"]}},17293:e=>{e.exports={en:["Bring to front"],cs:["Přenést do popředí"]}},79336:e=>{e.exports={en:["Brisbane"],cs:["Brisbane"]}},33672:e=>{e.exports={en:["Bucharest"],cs:["Bucharest"]}},20313:e=>{e.exports={en:["Budapest"],cs:["Budapest"]}},25282:e=>{e.exports={en:["Buenos Aires"],cs:["Buenos Aires"]}},46768:e=>{e.exports={en:["By TradingView"],cs:["By TradingView"]}},54280:e=>{e.exports={en:["Go to date"],cs:["Go to date"]}},74975:e=>{e.exports={en:["Go to {lineToolName}"],cs:["Go to {lineToolName}"]}},15462:e=>{e.exports={en:["Got it"],cs:["Got it"]}},47460:e=>{e.exports={en:["Gann Box"],cs:["Gann rámeček"]}},48683:e=>{e.exports={en:["Gann Fan"],cs:["Gann vějíř"]}},44763:e=>{e.exports={en:["Gann Square"],cs:["Gann čtverec"]}},60707:e=>{e.exports={en:["Gann Square Fixed"],cs:["Gann Square Fixed"]}},46808:e=>{e.exports={en:["Ghost Feed"],cs:["Ghost Feed"]}},57726:e=>{e.exports={en:["Grand supercycle"],cs:["Grand Supercycle"]}},34914:e=>{e.exports={en:["Do you really want to delete Study Template '{name}' ?"],cs:["Do you really want to delete Study Template '{name}' ?"]}},77125:e=>{e.exports={en:["Double Curve"],cs:["Double Curve"]}},9430:e=>{e.exports={en:["Double-click any edge to reset layout grid"],cs:["Double-click any edge to reset layout grid"]}},75296:e=>{e.exports={en:["Double-click to finish Path"],cs:["Double-click to finish Path"]}},17409:e=>{e.exports={en:["Double-click to finish Polyline"],cs:["Double-click to finish Polyline"]}},57131:e=>{e.exports={en:["Data Provided by"],
cs:["Data Provided by"]}},62154:e=>{e.exports={en:["Date"],cs:["Datum"]}},85444:e=>{e.exports={en:["Date Range"],cs:["Časové období"]}},47017:e=>{e.exports={en:["Date and Price Range"],cs:["Date and Price Range"]}},32084:e=>{e.exports={en:["Dec"],cs:["Prosinec"]}},23403:e=>{e.exports={en:["Degree"],cs:["Degree"]}},27358:e=>{e.exports={en:["Denver"],cs:["Denver"]}},24959:e=>{e.exports={en:["Dhaka"],cs:["Dhaka"]}},15179:e=>{e.exports={en:["Diamond"],cs:["Diamond"]}},91544:e=>{e.exports={en:["Disjoint Channel"],cs:["Disjoint Channel"]}},70132:e=>{e.exports={en:["Displacement"],cs:["Displacement"]}},93864:e=>{e.exports={en:["Drawings toolbar"],cs:["Drawings Toolbar"]}},96890:e=>{e.exports={en:["Draw Horizontal Line at"],cs:["Draw Horizontal Line at"]}},23650:e=>{e.exports={en:["Dubai"],cs:["Dubai"]}},79716:e=>{e.exports={en:["Dublin"],cs:["Dublin"]}},73456:e=>{e.exports={en:["Emoji"],cs:["Emoji"]}},9541:e=>{e.exports={en:["Enter a new chart layout name"],cs:["Enter a new chart layout name"]}},80943:e=>{e.exports={en:["Elliott Correction Wave (ABC)"],cs:["Elliott Correction Wave (ABC)"]}},75112:e=>{e.exports={en:["Elliott Double Combo Wave (WXY)"],cs:["Elliott Double Combo Wave (WXY)"]}},61114:e=>{e.exports={en:["Elliott Impulse Wave (12345)"],cs:["Elliott Impulse Wave (12345)"]}},72359:e=>{e.exports={en:["Elliott Triangle Wave (ABCDE)"],cs:["Elliott Triangle Wave (ABCDE)"]}},76129:e=>{e.exports={en:["Elliott Triple Combo Wave (WXYXZ)"],cs:["Elliott Triple Combo Wave (WXYXZ)"]}},78996:e=>{e.exports={en:["Ellipse"],cs:["Elipsa"]}},52788:e=>{e.exports={en:["Extended Line"],cs:["Extended Line"]}},86905:e=>{e.exports={en:["Exchange"],cs:["Směnárna"]}},19271:e=>{e.exports={en:["Existing pane above"],cs:["Existing Pane Above"]}},46545:e=>{e.exports={en:["Existing pane below"],cs:["Existing Pane Below"]}},20138:e=>{e.exports={en:["Forecast"],cs:["Předpověď"]}},2507:e=>{e.exports={en:["Feb"],cs:["Únor"]}},59005:e=>{e.exports={en:["Fib Channel"],cs:["Fibonacciho kanál"]}},82330:e=>{e.exports={en:["Fib Circles"],cs:["Fibonacciho kruhy"]}},55986:e=>{e.exports={en:["Fib Retracement"],cs:["Fibonacciho Retracement"]}},33880:e=>{e.exports={en:["Fib Speed Resistance Arcs"],cs:["Oblouková rychlost Fibonacciho rezistence"]}},2395:e=>{e.exports={en:["Fib Speed Resistance Fan"],cs:["Fibonacciho vějířová rychlost rezistence"]}},39014:e=>{e.exports={en:["Fib Spiral"],cs:["Fibonacciho spirála"]}},30622:e=>{e.exports={en:["Fib Time Zone"],cs:["Fibonacciho časové pásmo"]}},85042:e=>{e.exports={en:["Fib Wedge"],cs:["Fibonacciho klín"]}},33885:e=>{e.exports={en:["Flag"],cs:["Flag"]}},14600:e=>{e.exports={en:["Flag Mark"],cs:["Značka vlajky"]}},45051:e=>{e.exports={en:["Flat Top/Bottom"],cs:["Plochý vršek / spodek"]}},63271:e=>{e.exports={en:["Flipped"],cs:["Převrácený"]}},39643:e=>{e.exports={en:["Fraction part is invalid."],cs:["Fraction part is invalid."]}},24077:e=>{e.exports={en:["Fundamental studies are no longer available on charts"],cs:["Fundamental studies are no longer available on charts"]}},31561:e=>{e.exports={en:["Kolkata"],
cs:["Kolkata"]}},54533:e=>{e.exports={en:["Kathmandu"],cs:["Kathmandu"]}},83490:e=>{e.exports={en:["Kagi"],cs:["Kagi"]}},70913:e=>{e.exports={en:["Karachi"],cs:["Karachi"]}},76614:e=>{e.exports={en:["Kuwait"],cs:["Kuwait"]}},38561:e=>{e.exports={en:["Kuala Lumpur"],cs:["Kuala Lumpur"]}},99906:e=>{e.exports={en:["HLC area"],cs:["HLC area"]}},34491:e=>{e.exports={en:["Ho Chi Minh"],cs:["Ho Chi Minh"]}},13459:e=>{e.exports={en:["Hollow candles"],cs:["Duté Svíčky"]}},48861:e=>{e.exports={en:["Hong Kong"],cs:["Hong Kong"]}},79668:e=>{e.exports={en:["Honolulu"],cs:["Honolulu"]}},21795:e=>{e.exports={en:["Horizontal Line"],cs:["Horizontální linie"]}},25487:e=>{e.exports={en:["Horizontal Ray"],cs:["Horizontální paprsek"]}},21928:e=>{e.exports={en:["Head and Shoulders"],cs:["Head and Shoulders"]}},63876:e=>{e.exports={en:["Heikin Ashi"],cs:["Heikin Ashi"]}},48203:e=>{e.exports={en:["Helsinki"],cs:["Helsinki"]}},27298:e=>{e.exports={en:["Hide"],cs:["Skrýt"]}},47074:e=>{e.exports={en:["Hide all"],cs:["Hide all"]}},52563:e=>{e.exports={en:["Hide all drawings"],cs:["Hide all drawings"]}},90763:e=>{e.exports={en:["Hide all drawings and indicators"],cs:["Hide all drawings and indicators"]}},18216:e=>{e.exports={en:["Hide all drawings, indicators, positions & orders"],cs:["Hide all drawings, indicators, positions & orders"]}},78525:e=>{e.exports={en:["Hide all indicators"],cs:["Hide all indicators"]}},42164:e=>{e.exports={en:["Hide all positions & orders"],cs:["Hide all positions & orders"]}},3217:e=>{e.exports={en:["Hide drawings"],cs:["Hide drawings"]}},97878:e=>{e.exports={en:["Hide events on chart"],cs:["Hide Events on Chart"]}},72351:e=>{e.exports={en:["Hide indicators"],cs:["Hide indicators"]}},28345:e=>{e.exports={en:["Hide marks on bars"],cs:["Hide Marks On Bars"]}},92226:e=>{e.exports={en:["Hide positions & orders"],cs:["Hide positions & orders"]}},78254:e=>{e.exports={en:["High"],cs:["Vysoká"]}},98236:e=>{e.exports={en:["High-low"],cs:["High-low"]}},99479:e=>{e.exports={en:["High and low price labels"],cs:["High and low price labels"]}},33766:e=>{e.exports={en:["High and low price lines"],cs:["High and low price lines"]}},69476:e=>{e.exports={en:["Highlighter"],cs:["Highlighter"]}},72819:e=>{e.exports={en:["Histogram is too large, please increase {boldHighlightStart}Row Size{boldHighlightEnd} in the settings window."],cs:["Histogram is too large, please increase {boldHighlightStart}Row Size{boldHighlightEnd} in the settings window."]}},94966:e=>{e.exports={en:["Histogram is too large, please increase {boldHighlightStart}Ticks Per Row{boldHighlightEnd} in the settings window."],cs:["Histogram is too large, please increase {boldHighlightStart}Ticks Per Row{boldHighlightEnd} in the settings window."]}},66751:e=>{e.exports={en:["Histogram is too large, please reduce {boldHighlightStart}Row Size{boldHighlightEnd} in the settings window."],cs:["Histogram is too large, please reduce {boldHighlightStart}Row Size{boldHighlightEnd} in the settings window."]}},68065:e=>{e.exports={en:["Image"],cs:["Image"]}},80185:e=>{e.exports={
en:["Intervals less than {resolution} are not supported for {ticker}."],cs:["Intervals less than {resolution} are not supported for {ticker}."]}},10268:e=>{e.exports={en:["Intermediate"],cs:["Střední"]}},14285:e=>{e.exports={en:["Invalid Symbol"],cs:["Neplatný Symbol"]}},52969:e=>{e.exports={en:["Invalid symbol"],cs:["Invalid symbol"]}},37189:e=>{e.exports={en:["Invert scale"],cs:["Invert Scale"]}},89999:e=>{e.exports={en:["Indexed to 100"],cs:["Indexed to 100"]}},46850:e=>{e.exports={en:["Indicators value labels"],cs:["Indicators value labels"]}},54418:e=>{e.exports={en:["Indicators name labels"],cs:["Indicators name labels"]}},40490:e=>{e.exports={en:["Indicators, Metrics and Strategies. Press slash"],cs:["Indicators, Metrics and Strategies. Press slash"]}},15992:e=>{e.exports={en:["Info Line"],cs:["Info Line"]}},87829:e=>{e.exports={en:["Insert indicator"],cs:["Vložit indikátor"]}},91612:e=>{e.exports={en:["Inside"],cs:["Uvnitř"]}},41686:e=>{e.exports={en:["Inside Pitchfork"],cs:["Vnitřní vidle"]}},37913:e=>{e.exports={en:["Icon"],cs:["Ikona"]}},78326:e=>{e.exports={en:["Istanbul"],cs:["Istanbul"]}},39585:e=>{e.exports={en:["Johannesburg"],cs:["Johannesburg"]}},14995:e=>{e.exports={en:["Jakarta"],cs:["Jakarta"]}},62310:e=>{e.exports={en:["Jan"],cs:["Leden"]}},36057:e=>{e.exports={en:["Jerusalem"],cs:["Jerusalem"]}},53786:e=>{e.exports={en:["Jul"],cs:["Červenec"]}},429:e=>{e.exports={en:["Jun"],cs:["Červen"]}},67560:e=>{e.exports={en:["Juneau"],cs:["Juneau"]}},62329:e=>{e.exports={en:["On the left"],cs:["On the left"]}},55813:e=>{e.exports={en:["On the right"],cs:["On the right"]}},64818:e=>{e.exports={en:["Only {availableResolutions} intervals are supported for {ticker}."],cs:["Only {availableResolutions} intervals are supported for {ticker}."]}},21064:e=>{e.exports={en:["Oops!"],cs:["Oops!"]}},51221:e=>{e.exports={en:["Object Tree"],cs:["Object Tree"]}},12179:e=>{e.exports={en:["Oct"],cs:["Říjen"]}},16610:e=>{e.exports={en:["Open"],cs:["Otevřít"]}},46005:e=>{e.exports={en:["Original"],cs:["Původní"]}},75722:e=>{e.exports={en:["Oslo"],cs:["Oslo"]}},65318:e=>{e.exports={en:["Low"],cs:["Nízká"]}},55382:e=>{e.exports={en:["Load layout. Press period"],cs:["Load layout. Press period"]}},5837:e=>{e.exports={en:["Lock"],cs:["Zamknout"]}},79777:e=>{e.exports={en:["Lock/unlock"],cs:["Zamknout / Odemknout"]}},66005:e=>{e.exports={en:["Lock vertical cursor line by time"],cs:["Lock vertical cursor line by time"]}},14017:e=>{e.exports={en:["Lock price to bar ratio"],cs:["Lock Price To Bar Ratio"]}},16170:e=>{e.exports={en:["Logarithmic"],cs:["Logarithmic"]}},19439:e=>{e.exports={en:["London"],cs:["Londýn"]}},74832:e=>{e.exports={en:["Long Position"],cs:["Koupit"]}},28733:e=>{e.exports={en:["Los Angeles"],cs:["Los Angeles"]}},85924:e=>{e.exports={en:["Label Down"],cs:["Label Down"]}},52402:e=>{e.exports={en:["Label Up"],cs:["Label Up"]}},5119:e=>{e.exports={en:["Labels"],cs:["Popisky"]}},19931:e=>{e.exports={en:["Lagos"],cs:["Lagos"]}},63815:e=>{e.exports={en:["Last day change"],cs:["Last day change"]}},59444:e=>{e.exports={
en:["Lima"],cs:["Lima"]}},3554:e=>{e.exports={en:["Line"],cs:["Čára"]}},9394:e=>{e.exports={en:["Line with markers"],cs:["Se Značkami"]}},43588:e=>{e.exports={en:["Line break"],cs:["Line Break"]}},56982:e=>{e.exports={en:["Lines"],cs:["Lines"]}},37367:e=>{e.exports={en:["Link to the chart image copied to clipboard {emoji}"],cs:["Link to the chart image copied to clipboard {emoji}"]}},53375:e=>{e.exports={en:["Lisbon"],cs:["Lisbon"]}},81038:e=>{e.exports={en:["Luxembourg"],cs:["Luxembourg"]}},60663:e=>{e.exports={en:["Move the point to position the anchor then tap to place"],cs:["Move the point to position the anchor then tap to place"]}},35049:e=>{e.exports={en:["Move to"],cs:["Move to"]}},26493:e=>{e.exports={en:["Move scale to left"],cs:["Move scale to left"]}},40789:e=>{e.exports={en:["Move scale to right"],cs:["Move scale to right"]}},70382:e=>{e.exports={en:["Modified Schiff"],cs:["Upravené Schiff"]}},57681:e=>{e.exports={en:["Modified Schiff Pitchfork"],cs:["Upravené Schiff Pitchfork"]}},93907:e=>{e.exports={en:["More settings"]}},64039:e=>{e.exports={en:["Moscow"],cs:["Moskva"]}},52066:e=>{e.exports={en:["Madrid"],cs:["Madrid"]}},38365:e=>{e.exports={en:["Malta"],cs:["Malta"]}},48991:e=>{e.exports={en:["Manila"],cs:["Manila"]}},92767:e=>{e.exports={en:["Mar"],cs:["Březen"]}},73332:e=>{e.exports={en:["Mexico City"],cs:["Mexico City"]}},88314:e=>{e.exports={en:["Merge all scales into one"],cs:["Merge all scales into one"]}},54215:e=>{e.exports={en:["Mixed"],cs:["Mixed"]}},24866:e=>{e.exports={en:["Micro"],cs:["Micro"]}},87957:e=>{e.exports={en:["Millennium"],cs:["Millennium"]}},14724:e=>{e.exports={en:["Minuette"],cs:["Minuette"]}},78273:e=>{e.exports={en:["Minuscule"],cs:["Minuscule"]}},28941:e=>{e.exports={en:["Mirrored"],cs:["Zrcadlený"]}},9865:e=>{e.exports={en:["Muscat"],cs:["Muscat"]}},96935:e=>{e.exports={en:["N/A"],cs:["N/A"]}},36252:e=>{e.exports={en:["No data here"],cs:["No data here"]}},11254:e=>{e.exports={en:["No scale (fullscreen)"],cs:["No Scale (Fullscreen)"]}},9140:e=>{e.exports={en:["No sync"],cs:["No sync"]}},50910:e=>{e.exports={en:["No volume data"],cs:["No volume data"]}},94389:e=>{e.exports={en:["Note"],cs:["Poznámka"]}},26899:e=>{e.exports={en:["Nov"],cs:["Listopad"]}},67891:e=>{e.exports={en:["Norfolk Island"],cs:["Norfolk Island"]}},40977:e=>{e.exports={en:["Nairobi"],cs:["Nairobi"]}},40544:e=>{e.exports={en:["New York"],cs:["New York"]}},66103:e=>{e.exports={en:["New Zealand"],cs:["New Zealand"]}},15512:e=>{e.exports={en:["New pane above"],cs:["New pane above"]}},52160:e=>{e.exports={en:["New pane below"],cs:["New pane below"]}},15402:e=>{e.exports={en:["Next time you can use {shortcut} for quick paste"],cs:["Next time you can use {shortcut} for quick paste"]}},94600:e=>{e.exports={en:["Nicosia"],cs:["Nicosia"]}},73013:e=>{e.exports={en:["Something went wrong"],cs:["Something went wrong"]}},4509:e=>{e.exports={en:["Something went wrong. Please try again later."],cs:["Something went wrong. Please try again later."]}},43047:e=>{e.exports={en:["Save New Chart Layout"],
cs:["Save New Chart Layout"]}},76266:e=>{e.exports={en:["Save as"],cs:["Uložit jako"]}},55502:e=>{e.exports={en:["San Salvador"],cs:["San Salvador"]}},30231:e=>{e.exports={en:["Santiago"],cs:["Santiago"]}},91912:e=>{e.exports={en:["Sao Paulo"],cs:["Sao Paulo"]}},43931:e=>{e.exports={en:["Scale currency"],cs:["Scale currency"]}},43758:e=>{e.exports={en:["Scale price chart only"],cs:["Pouye Měřítko Ceny na Grafu"]}},40012:e=>{e.exports={en:["Scale unit"],cs:["Scale unit"]}},69904:e=>{e.exports={en:["Schiff"],cs:["Schiffovy"]}},42608:e=>{e.exports={en:["Schiff Pitchfork"],cs:["Schiffovy vidle"]}},76078:e=>{e.exports={en:["Script may be not updated if you leave the page."],cs:["Script may be not updated if you leave the page."]}},32514:e=>{e.exports={en:["Settings"],cs:["Nastavení"]}},70784:e=>{e.exports={en:["Second fraction part is invalid."],cs:["Second fraction part is invalid."]}},75594:e=>{e.exports={en:["Security info"],cs:["Security info"]}},21973:e=>{e.exports={en:["Send to back"],cs:["Přenést do pozadí"]}},71179:e=>{e.exports={en:["Send backward"],cs:["Posunout dozadu"]}},26820:e=>{e.exports={en:["Seoul"],cs:["Seoul"]}},6816:e=>{e.exports={en:["Sep"],cs:["Září"]}},94031:e=>{e.exports={en:["Session"],cs:["Session"]}},83298:e=>{e.exports={en:["Session volume profile"],cs:["Session volume profile"]}},66707:e=>{e.exports={en:["Session breaks"],cs:["Přerušit relaci"]}},1852:e=>{e.exports={en:["Shanghai"],cs:["Šanghaj"]}},8075:e=>{e.exports={en:["Short Position"],cs:["Prodat"]}},98334:e=>{e.exports={en:["Show"],cs:["Zobrazit"]}},85891:e=>{e.exports={en:["Show all drawings"],cs:["Show all drawings"]}},25881:e=>{e.exports={en:["Show all drawings and indicators"],cs:["Show all drawings and indicators"]}},86738:e=>{e.exports={en:["Show all drawings, indicators, positions & orders"],cs:["Show all drawings, indicators, positions & orders"]}},98753:e=>{e.exports={en:["Show all indicators"],cs:["Show all indicators"]}},55418:e=>{e.exports={en:["Show all ideas"],cs:["Show All Ideas"]}},20506:e=>{e.exports={en:["Show all positions & orders"],cs:["Show all positions & orders"]}},33158:e=>{e.exports={en:["Show continuous contract switch"],cs:["Show continuous contract switch"]}},81465:e=>{e.exports={en:["Show contract expiration"],cs:["Show contract expiration"]}},29449:e=>{e.exports={en:["Show dividends"],cs:["Show Dividends"]}},37113:e=>{e.exports={en:["Show earnings"],cs:["Show Earnings"]}},10261:e=>{e.exports={en:["Show ideas of followed users"],cs:["Show Ideas of Followed Users"]}},68112:e=>{e.exports={en:["Show latest news and Minds"],cs:["Show latest news and Minds"]}},44020:e=>{e.exports={en:["Show my ideas only"],cs:["Show My Ideas Only"]}},50849:e=>{e.exports={en:["Show splits"],cs:["Show Splits"]}},67751:e=>{e.exports={en:["Signpost"],cs:["Signpost"]}},77377:e=>{e.exports={en:["Singapore"],cs:["Singapur"]}},39090:e=>{e.exports={en:["Sine Line"],cs:["Sine Line"]}},66205:e=>{e.exports={en:["Square"],cs:["Square"]}},86146:e=>{e.exports={
en:["Studies limit exceeded: {number} studies per layout.\nPlease, remove some studies."],cs:["Studies limit exceeded: {number} studies per layout.\nPlease, remove some studies."]}},92516:e=>{e.exports={en:["Style"],cs:["Styl"]}},61507:e=>{e.exports={en:["Stack on the left"],cs:["Stack on the left"]}},97800:e=>{e.exports={en:["Stack on the right"],cs:["Stack on the right"]}},85166:e=>{e.exports={en:["Start using keyboard navigation mode. Press {shortcut}"],cs:["Start using keyboard navigation mode. Press {shortcut}"]}},4035:e=>{e.exports={en:["Stay in drawing mode"],cs:["Zůstat v režimu kreslení"]}},69217:e=>{e.exports={en:["Step line"],cs:["Step Line"]}},43114:e=>{e.exports={en:["Sticker"],cs:["Sticker"]}},86716:e=>{e.exports={en:["Stockholm"],cs:["Stockholm"]}},1145:e=>{e.exports={en:["Submicro"],cs:["Submicro"]}},63375:e=>{e.exports={en:["Submillennium"],cs:["Submillennium"]}},30585:e=>{e.exports={en:["Subminuette"],cs:["Subminuette"]}},67948:e=>{e.exports={en:["Supercycle"],cs:["Supercycle"]}},3348:e=>{e.exports={en:["Supermillennium"],cs:["Supermillennium"]}},18905:e=>{e.exports={en:["Switch to {resolution}"],cs:["Switch to {resolution}"]}},31622:e=>{e.exports={en:["Sydney"],cs:["Sydney"]}},70963:e=>{e.exports={en:["Symbol Error"],cs:["Symbol Error"]}},32390:e=>{e.exports={en:["Symbol name label"],cs:["Symbol Name Label"]}},10127:e=>{e.exports={en:["Symbol last price label"],cs:["Symbol Last Value Label"]}},39079:e=>{e.exports={en:["Sync globally"],cs:["Sync globally"]}},46607:e=>{e.exports={en:["Sync in layout"],cs:["Sync To All Charts"]}},76519:e=>{e.exports={en:["Point & figure"],cs:["Point & Figure ( O X )"]}},39949:e=>{e.exports={en:["Polyline"],cs:["Křivka"]}},371:e=>{e.exports={en:["Path"],cs:["Path"]}},59256:e=>{e.exports={en:["Parallel Channel"],cs:["Paralelní Kanál"]}},61879:e=>{e.exports={en:["Paris"],cs:["Paříž"]}},35140:e=>{e.exports={en:["Paste"],cs:["Paste"]}},6919:e=>{e.exports={en:["Percent"],cs:["Percent"]}},24436:e=>{e.exports={en:["Perth"],cs:["Perth"]}},14055:e=>{e.exports={en:["Phoenix"],cs:["Phoenix"]}},34156:e=>{e.exports={en:["Pitchfan"],cs:["Pitchfan"]}},19634:e=>{e.exports={en:["Pitchfork"],cs:["Vidle"]}},33110:e=>{e.exports={en:["Pin to new left scale"],cs:["Pin to new left scale"]}},28280:e=>{e.exports={en:["Pin to new right scale"],cs:["Pin to new right scale"]}},14115:e=>{e.exports={en:["Pin to left scale"],cs:["Pin to left scale"]}},72046:e=>{e.exports={en:["Pin to left scale (hidden)"],cs:["Pin to left scale (hidden)"]}},81054:e=>{e.exports={en:["Pin to right scale"],cs:["Pin to right scale"]}},16986:e=>{e.exports={en:["Pin to right scale (hidden)"],cs:["Pin to right scale (hidden)"]}},60035:e=>{e.exports={en:["Pin to scale (now left)"],cs:["Pin to scale (now left)"]}},94210:e=>{e.exports={en:["Pin to scale (now no scale)"],cs:["Pin to scale (now no scale)"]}},10761:e=>{e.exports={en:["Pin to scale (now right)"],cs:["Pin to scale (now right)"]}},76150:e=>{e.exports={en:["Pin to scale (now {label})"],cs:["Pin to scale (now {label})"]}},29436:e=>{e.exports={
en:["Pin to scale {label}"],cs:["Pin to scale {label}"]}},2165:e=>{e.exports={en:["Pin to scale {label} (hidden)"],cs:["Pin to scale {label} (hidden)"]}},90095:e=>{e.exports={en:["Pinned to left scale"],cs:["Pinned to left scale"]}},32538:e=>{e.exports={en:["Pinned to left scale (hidden)"],cs:["Pinned to left scale (hidden)"]}},44579:e=>{e.exports={en:["Pinned to right scale"],cs:["Pinned to right scale"]}},94559:e=>{e.exports={en:["Pinned to right scale (hidden)"],cs:["Pinned to right scale (hidden)"]}},12645:e=>{e.exports={en:["Pinned to scale {label}"],cs:["Pinned to scale {label}"]}},3564:e=>{e.exports={en:["Pinned to scale {label} (hidden)"],cs:["Pinned to scale {label} (hidden)"]}},71566:e=>{e.exports={en:["Plus button"],cs:["Plus button"]}},28298:e=>{e.exports={en:["Please give us a clipboard writing permission in your browser or press {keystroke}"],cs:["Please give us a clipboard writing permission in your browser or press {keystroke}"]}},81248:e=>{e.exports={en:["Prague"],cs:["Prague"]}},81712:e=>{e.exports={en:["Press and hold {key} while zooming to maintain the chart position"],cs:["Press and hold {key} while zooming to maintain the chart position"]}},91282:e=>{e.exports={en:["Price Label"],cs:["Cena"]}},97512:e=>{e.exports={en:["Price Note"],cs:["Price Note"]}},68941:e=>{e.exports={en:["Price Range"],cs:["Cenové rozpětí"]}},66123:e=>{e.exports={en:["Price format is invalid."],cs:["Price format is invalid."]}},72926:e=>{e.exports={en:["Price line"],cs:["Hladina ceny"]}},59189:e=>{e.exports={en:["Primary"],cs:["Primární"]}},75747:e=>{e.exports={en:["Projection"],cs:["Promítání"]}},55801:e=>{e.exports={en:["Published on {customer}, {date}"],cs:["Published on {customer}, {date}"]}},28756:e=>{e.exports={en:["Qatar"],cs:["Qatar"]}},57959:e=>{e.exports={en:["Quick search. Press {shortcut}"],cs:["Quick search. Press {shortcut}"]}},56820:e=>{e.exports={en:["Rotated Rectangle"],cs:["Otočený obdélník"]}},52961:e=>{e.exports={en:["Rome"],cs:["Rome"]}},50318:e=>{e.exports={en:["Ray"],cs:["Paprsek"]}},55169:e=>{e.exports={en:["Range"],cs:["Range"]}},13386:e=>{e.exports={en:["Reykjavik"],cs:["Reykjavik"]}},26001:e=>{e.exports={en:["Rectangle"],cs:["Obdélník"]}},48236:e=>{e.exports={en:["Redo"],cs:["Opakovat"]}},2460:e=>{e.exports={en:["Regression Trend"],cs:["Regresní trend"]}},67410:e=>{e.exports={en:["Remove"],cs:["Odstranit"]}},96374:e=>{e.exports={en:["Remove drawings"],cs:["Remove drawings"]}},99984:e=>{e.exports={en:["Remove indicators"],cs:["Remove Indicators"]}},3061:e=>{e.exports={en:["Remove this financial metric from favorites"],cs:["Remove this financial metric from favorites"]}},58764:e=>{e.exports={en:["Remove this indicator from favorites"],cs:["Remove this indicator from favorites"]}},22584:e=>{e.exports={en:["Rename Chart Layout"],cs:["Přejmenovat předlohy grafu"]}},88130:e=>{e.exports={en:["Renko"],cs:["Renko"]}},75246:e=>{e.exports={en:["Reset chart view"],cs:["Reset chart view"]}},88853:e=>{e.exports={en:["Reset points"],cs:["Reset points"]}},15332:e=>{e.exports={en:["Reset price scale"],
cs:["Reset Price Scale"]}},54170:e=>{e.exports={en:["Reset time scale"],cs:["Reset Time Scale"]}},37974:e=>{e.exports={en:["Riyadh"],cs:["Riyadh"]}},94022:e=>{e.exports={en:["Riga"],cs:["Riga"]}},60630:e=>{e.exports={en:["Runtime error"],cs:["Runtime error"]}},66719:e=>{e.exports={en:["Warning"],cs:["Warning"]}},5959:e=>{e.exports={en:["Warsaw"],cs:["Varšava"]}},94465:e=>{e.exports={en:["Toggle auto scale"],cs:["Toggle auto scale"]}},46992:e=>{e.exports={en:["Toggle log scale"],cs:["Toggle log scale"]}},98549:e=>{e.exports={en:["Tokelau"],cs:["Tokelau"]}},69122:e=>{e.exports={en:["Tokyo"],cs:["Tokio"]}},10095:e=>{e.exports={en:["Toronto"],cs:["Toronto"]}},11034:e=>{e.exports={en:["Taipei"],cs:["Taipei"]}},79995:e=>{e.exports={en:["Tallinn"],cs:["Tallinn"]}},6686:e=>{e.exports={en:["Tehran"],cs:["Teherán"]}},93553:e=>{e.exports={en:["Template"],cs:["Template"]}},81657:e=>{e.exports={en:["The data vendor doesn't provide volume data for this symbol."],cs:["The data vendor doesn't provide volume data for this symbol."]}},49947:e=>{e.exports={en:["The publication preview could not be loaded. Please disable your browser extensions and try again."],cs:["The publication preview could not be loaded. Please disable your browser extensions and try again."]}},93738:e=>{e.exports={en:["This file is too big. Max size is {value}."],cs:["This file is too big. Max size is {value}."]}},59519:e=>{e.exports={en:["This indicator cannot be applied to another indicator."],cs:["This indicator cannot be applied to another indicator."]}},18260:e=>{e.exports={en:["This script contains an error. Please contact its author."],cs:["This script contains an error. Please contact its author."]}},76989:e=>{e.exports={en:["This script is invite-only. To request access, please contact its author."],cs:["This script is invite-only. To request access, please contact its author."]}},47773:e=>{e.exports={en:["This symbol is only available on {linkStart}TradingView{linkEnd}."],cs:["The symbol available only on {linkStart}TradingView{linkEnd}."]}},46982:e=>{e.exports={en:["Three Drives Pattern"],cs:["Three Drives Pattern"]}},24821:e=>{e.exports={en:["Ticks"],cs:["Ticks"]}},80254:e=>{e.exports={en:["Tick-based intervals are not available for {ticker}."],cs:["Tick-based intervals are not available for {ticker}."]}},12806:e=>{e.exports={en:["Time"],cs:["Čas"]}},20909:e=>{e.exports={en:["Time zone"],cs:["Časové pásmo"]}},46852:e=>{e.exports={en:["Time Cycles"],cs:["Time Cycles"]}},17809:e=>{e.exports={en:["Time Price Opportunity"],cs:["Time Price Opportunity"]}},66823:e=>{e.exports={en:["Trade"],cs:["Trade"]}},7697:e=>{e.exports={en:["TradingView is interactive and has commands to use with a screen reader. The following is a list of keyboard commands available to interact on the platform"],cs:["TradingView is interactive and has commands to use with a screen reader. The following is a list of keyboard commands available to interact on the platform"]}},35757:e=>{e.exports={en:["Trend Angle"],cs:["Úhel trendu"]}},97339:e=>{e.exports={en:["Trend Line"],
cs:["Trendová linie"]}},80583:e=>{e.exports={en:["Trend-Based Fib Extension"],cs:["Vývoj trhu založený na Fibonacciho rozšíření"]}},72159:e=>{e.exports={en:["Trend-Based Fib Time"],cs:["Vývoj trhu založený na Fibonacciho období"]}},1671:e=>{e.exports={en:["Triangle"],cs:["Trojúhelník"]}},76152:e=>{e.exports={en:["Triangle Down"],cs:["Triangle Down"]}},90148:e=>{e.exports={en:["Triangle Pattern"],cs:["Trojúhelníkový model"]}},21236:e=>{e.exports={en:["Triangle Up"],cs:["Triangle Up"]}},21007:e=>{e.exports={en:["Tunis"],cs:["Tunis"]}},1833:e=>{e.exports={en:["UTC"],cs:["UTC"]}},14804:e=>{e.exports={en:["Undo"],cs:["Zpět"]}},15432:e=>{e.exports={en:["Units"],cs:["Units"]}},11768:e=>{e.exports={en:["Unknown error"],cs:["Unknown error"]}},99894:e=>{e.exports={en:["Unlock"],cs:["Unlock"]}},75546:e=>{e.exports={en:["Unsupported interval"],cs:["Unsupported interval"]}},8580:e=>{e.exports={en:["User-defined error"],cs:["User-defined error"]}},40693:e=>{e.exports={en:["Volume Profile Fixed Range"],cs:["Volume Profile Fixed Range"]}},39903:e=>{e.exports={en:["Volume Profile indicator available only on our upgraded plans."],cs:["Volume Profile indicator available only on our upgraded plans."]}},93722:e=>{e.exports={en:["Volume candles"],cs:["Volume candles"]}},69156:e=>{e.exports={en:["Volume data is not provided in BIST MIXED data plan."],cs:["Volume data is not provided in BIST MIXED data plan."]}},92763:e=>{e.exports={en:["Volume footprint"],cs:["Volume footprint"]}},32838:e=>{e.exports={en:["Vancouver"],cs:["Vancouver"]}},29535:e=>{e.exports={en:["Vertical Line"],cs:["Svislá linie"]}},23160:e=>{e.exports={en:["Vienna"],cs:["Vienna"]}},60534:e=>{e.exports={en:["Vilnius"],cs:["Vilnius"]}},40091:e=>{e.exports={en:["Visibility"],cs:["Visibility"]}},54853:e=>{e.exports={en:["Visibility on intervals"],cs:["Visibility on intervals"]}},10309:e=>{e.exports={en:["Visible on mouse over"],cs:["Visible on Mouse Over"]}},4077:e=>{e.exports={en:["Visual order"],cs:["Seřadit vizuálně"]}},11316:e=>{e.exports={en:["X Cross"],cs:["X Cross"]}},42231:e=>{e.exports={en:["XABCD Pattern"],cs:["XABCD model"]}},25059:e=>{e.exports={en:["You cannot see this pivot timeframe on this resolution"],cs:["You cannot see this pivot timeframe on this resolution"]}},53168:e=>{e.exports={en:["Yangon"],cs:["Yangon"]}},62859:e=>{e.exports={en:["Zurich"],cs:["Zurich"]}},47977:e=>{e.exports={en:["change Elliott degree"],cs:["change Elliott degree"]}},61557:e=>{e.exports={en:["change no overlapping labels"],cs:["change no overlapping labels"]}},76852:e=>{e.exports={en:["change average close price label visibility"],cs:["change average close price label visibility"]}},1022:e=>{e.exports={en:["change average close price line visibility"],cs:["change average close price line visibility"]}},69362:e=>{e.exports={en:["change bid and ask labels visibility"],cs:["change bid and ask labels visibility"]}},52919:e=>{e.exports={en:["change bid and ask lines visibility"],cs:["change bid and ask lines visibility"]}},32302:e=>{e.exports={en:["change currency"],
cs:["change currency"]}},68846:e=>{e.exports={en:["change chart layout to {title}"],cs:["change chart layout to {title}"]}},18867:e=>{e.exports={en:["change continuous contract switch visibility"],cs:["change continuous contract switch visibility"]}},39383:e=>{e.exports={en:["change countdown to bar close visibility"],cs:["change countdown to bar close visibility"]}},16979:e=>{e.exports={en:["change date range"],cs:["change date range"]}},53929:e=>{e.exports={en:["change dividends visibility"],cs:["change dividends visibility"]}},6119:e=>{e.exports={en:["change events visibility on chart"],cs:["change events visibility on chart"]}},6819:e=>{e.exports={en:["change earnings visibility"],cs:["change earnings visibility"]}},85532:e=>{e.exports={en:["change futures contract expiration visibility"],cs:["change futures contract expiration visibility"]}},24226:e=>{e.exports={en:["change high and low price labels visibility"],cs:["change high and low price labels visibility"]}},80692:e=>{e.exports={en:["change high and low price lines visibility"],cs:["change high and low price lines visibility"]}},24893:e=>{e.exports={en:["change indicators name labels visibility"],cs:["change indicators name labels visibility"]}},64729:e=>{e.exports={en:["change indicators value labels visibility"],cs:["change indicators value labels visibility"]}},50243:e=>{e.exports={en:["change latest news and Minds visibility"],cs:["change latest news and Minds visibility"]}},88849:e=>{e.exports={en:["change linking group"],cs:["change linking group"]}},14691:e=>{e.exports={en:["change pane height"],cs:["change pane height"]}},96379:e=>{e.exports={en:["change plus button visibility"],cs:["change plus button visibility"]}},30870:e=>{e.exports={en:["change pre/post market price label visibility"],cs:["change pre/post market price label visibility"]}},11718:e=>{e.exports={en:["change pre/post market price line visibility"],cs:["change pre/post market price line visibility"]}},58419:e=>{e.exports={en:["change previous close price line visibility"],cs:["change previous close price line visibility"]}},8662:e=>{e.exports={en:["change price line visibility"],cs:["change price line visibility"]}},2509:e=>{e.exports={en:["change price to bar ratio"],cs:["change price to bar ratio"]}},32829:e=>{e.exports={en:["change resolution"],cs:["Change Resolution"]}},35400:e=>{e.exports={en:["change symbol"],cs:["Change symbol"]}},73357:e=>{e.exports={en:["change symbol labels visibility"],cs:["change symbol labels visibility"]}},67453:e=>{e.exports={en:["change symbol last value visibility"],cs:["change symbol last value visibility"]}},4729:e=>{e.exports={en:["change symbol previous close value visibility"],cs:["change symbol previous close value visibility"]}},87041:e=>{e.exports={en:["change session"],cs:["change session"]}},38413:e=>{e.exports={en:["change session breaks visibility"],cs:["change session breaks visibility"]}},49965:e=>{e.exports={en:["change series style"],cs:["change series style"]}},47474:e=>{e.exports={en:["change splits visibility"],
cs:["change splits visibility"]}},20137:e=>{e.exports={en:["change timezone"],cs:["change timezone"]}},85975:e=>{e.exports={en:["change unit"],cs:["change unit"]}},1924:e=>{e.exports={en:["change visibility"],cs:["change visibility"]}},84331:e=>{e.exports={en:["change visibility at current interval"],cs:["change visibility at current interval"]}},45800:e=>{e.exports={en:["change visibility at current interval and above"],cs:["change visibility at current interval and above"]}},75645:e=>{e.exports={en:["change visibility at current interval and below"],cs:["change visibility at current interval and below"]}},57916:e=>{e.exports={en:["change visibility at all intervals"],cs:["change visibility at all intervals"]}},98463:e=>{e.exports={en:["change {title} style"],cs:["change {title} style"]}},57122:e=>{e.exports={en:["change {title} text"],cs:["change {title} text"]}},63058:e=>{e.exports={en:["change {pointIndex} point"],cs:["change {pointIndex} point"]}},94566:e=>{e.exports={en:["charts by TradingView"],cs:["charts by TradingView"]}},32943:e=>{e.exports={en:["clone line tools"],cs:["clone line tools"]}},46219:e=>{e.exports={en:["create line tools group"],cs:["create line tools group"]}},95394:e=>{e.exports={en:["create line tools group from selection"],cs:["create line tools group from selection"]}},12898:e=>{e.exports={en:["create {tool}"],cs:["create {tool}"]}},94227:e=>{e.exports={en:["cut sources"],cs:["cut sources"]}},11500:e=>{e.exports={en:["cut {title}"],cs:["cut {title}"]}},12570:e=>{e.exports={en:["add line tool {lineTool} to group {name}"],cs:["add line tool {lineTool} to group {name}"]}},21162:e=>{e.exports={en:["add line tool(s) to group {group}"],cs:["add line tool(s) to group {group}"]}},67608:e=>{e.exports={en:["add this financial metric to entire layout"],cs:["add this financial metric to entire layout"]}},96677:e=>{e.exports={en:["add this indicator to entire layout"],cs:["add this indicator to entire layout"]}},58156:e=>{e.exports={en:["add this strategy to entire layout"],cs:["add this strategy to entire layout"]}},79290:e=>{e.exports={en:["add this symbol to entire layout"],cs:["add this symbol to entire layout"]}},68231:e=>{e.exports={en:["apply chart theme"],cs:["apply chart theme"]}},99551:e=>{e.exports={en:["apply all chart properties"],cs:["apply all chart properties"]}},89720:e=>{e.exports={en:["apply drawing template"],cs:["apply drawing template"]}},27851:e=>{e.exports={en:["apply factory defaults to selected sources"],cs:["apply factory defaults to selected sources"]}},70507:e=>{e.exports={en:["apply indicators to entire layout"],cs:["apply indicators to entire layout"]}},69604:e=>{e.exports={en:["apply study template {template}"],cs:["apply study template {template}"]}},86708:e=>{e.exports={en:["apply toolbars theme"],cs:["apply toolbars theme"]}},1979:e=>{e.exports={en:["bring group {title} forward"],cs:["bring group {title} forward"]}},53159:e=>{e.exports={en:["bring {title} to front"],cs:["bring {title} to front"]}},41966:e=>{e.exports={en:["bring {title} forward"],
cs:["bring {title} forward"]}},44676:e=>{e.exports={en:["by TradingView"],cs:["by TradingView"]}},58850:e=>{e.exports={en:["date range lock"],cs:["date range lock"]}},35111:e=>{e.exports={en:["erase level line"],cs:["erase level line"]}},99395:e=>{e.exports={en:["exclude line tools from group {group}"],cs:["exclude line tools from group {group}"]}},82349:e=>{e.exports={en:["flip bars pattern"],cs:["flip bars pattern"]}},13017:e=>{e.exports={en:["hide {title}"],cs:["hide {title}"]}},62249:e=>{e.exports={en:["hide marks on bars"],cs:["Hide Marks On Bars"]}},56558:e=>{e.exports={en:["interval lock"],cs:["interval lock"]}},6830:e=>{e.exports={en:["invert scale"],cs:["Invert Scale"]}},48818:e=>{e.exports={en:["insert {title}"],cs:["insert {title}"]}},56307:e=>{e.exports={en:["insert {title} after {targetTitle}"],cs:["insert {title} after {targetTitle}"]}},32960:e=>{e.exports={en:["insert {title} after {target}"],cs:["insert {title} after {target}"]}},57106:e=>{e.exports={en:["insert {title} before {target}"],cs:["insert {title} before {target}"]}},46229:e=>{e.exports={en:["insert {title} before {targetTitle}"],cs:["insert {title} before {targetTitle}"]}},43364:e=>{e.exports={en:["load default drawing template"],cs:["load default drawing template"]}},62011:e=>{e.exports={en:["loading..."],cs:["načítá se ..."]}},76104:e=>{e.exports={en:["lock {title}"],cs:["lock {title}"]}},20453:e=>{e.exports={en:["lock group {group}"],cs:["lock group {group}"]}},18942:e=>{e.exports={en:["lock objects"],cs:["lock objects"]}},98277:e=>{e.exports={en:["move"],cs:["move"]}},58228:e=>{e.exports={en:["move {title} to new left scale"],cs:["move {title} to new left scale"]}},77482:e=>{e.exports={en:["move {title} to new right scale"],cs:["move {title} to new right scale"]}},64077:e=>{e.exports={en:["move all scales to left"],cs:["move all scales to left"]}},19013:e=>{e.exports={en:["move all scales to right"],cs:["move all scales to right"]}},52510:e=>{e.exports={en:["move drawing(s)"],cs:["move drawing(s)"]}},79209:e=>{e.exports={en:["move left"],cs:["move left"]}},60114:e=>{e.exports={en:["move right"],cs:["move right"]}},44854:e=>{e.exports={en:["move scale"],cs:["move scale"]}},10625:e=>{e.exports={en:["make {title} no scale (Full screen)"],cs:["make {title} no scale (Full screen)"]}},76709:e=>{e.exports={en:["make group {group} invisible"],cs:["make group {group} invisible"]}},45987:e=>{e.exports={en:["make group {group} visible"],cs:["make group {group} visible"]}},78055:e=>{e.exports={en:["merge down"],cs:["merge down"]}},41866:e=>{e.exports={en:["merge to pane"],cs:["merge to pane"]}},52458:e=>{e.exports={en:["merge up"],cs:["merge up"]}},20965:e=>{e.exports={en:["mirror bars pattern"],cs:["mirror bars pattern"]}},90091:e=>{e.exports={en:["n/a"],cs:["n/a"]}},94981:e=>{e.exports={en:["scale price"],cs:["scale price"]}},63796:e=>{e.exports={en:["scale price chart only"],cs:["Pouye Měřítko Ceny na Grafu"]}},70771:e=>{e.exports={en:["scale time"],cs:["scale time"]}},42070:e=>{e.exports={en:["scroll"],cs:["scroll"]}},87840:e=>{e.exports={
en:["scroll time"],cs:["scroll time"]}},82241:e=>{e.exports={en:["set price scale selection strategy to {title}"],cs:["set price scale selection strategy to {title}"]}},40962:e=>{e.exports={en:["send {title} backward"],cs:["send {title} backward"]}},5005:e=>{e.exports={en:["send {title} to back"],cs:["send {title} to back"]}},69546:e=>{e.exports={en:["send group {title} backward"],cs:["send group {title} backward"]}},63934:e=>{e.exports={en:["share line tools globally"],cs:["share line tools globally"]}},90221:e=>{e.exports={en:["share line tools in layout"],cs:["share line tools in layout"]}},13336:e=>{e.exports={en:["show all ideas"],cs:["show all ideas"]}},91395:e=>{e.exports={en:["show ideas of followed users"],cs:["show ideas of followed users"]}},57460:e=>{e.exports={en:["show my ideas only"],cs:["show my ideas only"]}},4114:e=>{e.exports={en:["stay in drawing mode"],cs:["stay in drawing mode"]}},3350:e=>{e.exports={en:["stop syncing drawing"],cs:["stop syncing drawing"]}},49183:e=>{e.exports={en:["stop syncing line tool(s)"],cs:["stop syncing line tool(s)"]}},53278:e=>{e.exports={en:["symbol lock"],cs:["symbol lock"]}},91677:e=>{e.exports={en:["sync time"],cs:["sync time"]}},3140:e=>{e.exports={en:["powered by"],cs:["powered by"]}},92800:e=>{e.exports={en:["powered by TradingView"],cs:["powered by TradingView"]}},62192:e=>{e.exports={en:["paste drawing"],cs:["paste drawing"]}},1064:e=>{e.exports={en:["paste indicator"],cs:["paste indicator"]}},57010:e=>{e.exports={en:["paste {title}"],cs:["paste {title}"]}},78690:e=>{e.exports={en:["pin to left scale"],cs:["pin to left scale"]}},7495:e=>{e.exports={en:["pin to right scale"],cs:["pin to right scale"]}},81566:e=>{e.exports={en:["pin to scale {label}"],cs:["pin to scale {label}"]}},2618:e=>{e.exports={en:["rearrange panes"],cs:["rearrange panes"]}},43172:e=>{e.exports={en:["remove all studies"],cs:["remove all studies"]}},56253:e=>{e.exports={en:["remove all studies and drawing tools"],cs:["remove all studies and drawing tools"]}},27171:e=>{e.exports={en:["remove deselected empty line tools"],cs:["remove deselected empty line tools"]}},30538:e=>{e.exports={en:["remove drawings"],cs:["remove drawings"]}},1193:e=>{e.exports={en:["remove drawings group"],cs:["remove drawings group"]}},38199:e=>{e.exports={en:["remove line data sources"],cs:["remove line data sources"]}},93333:e=>{e.exports={en:["remove pane"],cs:["remove pane"]}},94543:e=>{e.exports={en:["remove {title}"],cs:["remove {title}"]}},41430:e=>{e.exports={en:["removing line tools group {name}"],cs:["removing line tools group {name}"]}},80491:e=>{e.exports={en:["rename group {group} to {newName}"],cs:["rename group {group} to {newName}"]}},85366:e=>{e.exports={en:["reset layout sizes"],cs:["reset layout sizes"]}},3323:e=>{e.exports={en:["reset scales"],cs:["reset scales"]}},17336:e=>{e.exports={en:["reset time scale"],cs:["Reset Time Scale"]}},47418:e=>{e.exports={en:["resize layout"],cs:["resize layout"]}},85815:e=>{e.exports={en:["restore defaults"],cs:["restore defaults"]}},96881:e=>{e.exports={
en:["restore study defaults"],cs:["restore study defaults"]}},42240:e=>{e.exports={en:["toggle auto scale"],cs:["toggle auto scale"]}},46054:e=>{e.exports={en:["toggle collapsed pane state"],cs:["toggle collapsed pane state"]}},24736:e=>{e.exports={en:["toggle indexed to 100 scale"],cs:["toggle indexed to 100 scale"]}},49695:e=>{e.exports={en:["toggle lock scale"],cs:["toggle lock scale"]}},49403:e=>{e.exports={en:["toggle log scale"],cs:["toggle log scale"]}},98994:e=>{e.exports={en:["toggle percentage scale"],cs:["Toggle Percentage Scale"]}},80688:e=>{e.exports={en:["toggle regular scale"],cs:["toggle regular scale"]}},46807:e=>{e.exports={en:["track time"],cs:["track time"]}},8040:e=>{e.exports={en:["turn line tools sharing off"],cs:["turn line tools sharing off"]}},23230:e=>{e.exports={en:["unlock objects"],cs:["unlock objects"]}},74590:e=>{e.exports={en:["unlock group {group}"],cs:["unlock group {group}"]}},12525:e=>{e.exports={en:["unlock {title}"],cs:["unlock {title}"]}},81576:e=>{e.exports={en:["unmerge to new bottom pane"],cs:["unmerge to new bottom pane"]}},79443:e=>{e.exports={en:["unmerge up"],cs:["unmerge up"]}},46453:e=>{e.exports={en:["unmerge down"],cs:["unmerge down"]}},94656:e=>{e.exports={en:["{chartStyle} chart type isn't currently available for tick-based intervals."],cs:["{chartStyle} chart type isn't currently available for tick-based intervals."]}},41643:e=>{e.exports={en:["{count} bars"],cs:["{count} Sloupcový graf"]}},83470:e=>{e.exports={en:["{symbol} financials by TradingView"],cs:["{symbol} financials by TradingView"]}},40947:e=>{e.exports={en:["{userName} published on {customer}, {date}"],cs:["{userName} published on {customer}, {date}"]}},91084:e=>{e.exports={en:["zoom"],cs:["zoom"]}},49856:e=>{e.exports={en:["zoom in"],cs:["zoom in"]}},73638:e=>{e.exports={en:["zoom out"],cs:["zoom out"]}},41807:e=>{e.exports={en:["day","days"],cs:["day","days"]}},42328:e=>{e.exports={en:["hour","hours"],cs:["hour","hours"]}},98393:e=>{e.exports={en:["month","months"],cs:["month","months"]}},78318:e=>{e.exports={en:["minute","minutes"],cs:["minute","minutes"]}},33232:e=>{e.exports={en:["second","seconds"],cs:["second","seconds"]}},89937:e=>{e.exports={en:["range","ranges"],cs:["range","ranges"]}},48898:e=>{e.exports={en:["week","weeks"],cs:["week","weeks"]}},11913:e=>{e.exports={en:["tick","ticks"],cs:["tick","ticks"]}},58590:e=>{e.exports={en:["{count}m","{count}m"],cs:["{count}m","{count}m"]}},47801:e=>{e.exports={en:["{count}d","{count}d"],cs:["{count}d","{count}d"]}},46766:e=>{e.exports={en:["{count}y","{count}y"],cs:["{count}y","{count}y"]}},56316:e=>{e.exports=Object.create(null),e.exports["#AAPL-symbol-description"]={en:["Apple Inc"],cs:["APPLE INC"]},e.exports["#AUDCAD-symbol-description"]={en:["Australian Dollar/Canadian Dollar"],cs:["Australský Dolar/Kanadský Dolar"]},e.exports["#AUDCHF-symbol-description"]={en:["Australian Dollar / Swiss Franc"],cs:["Australský Dolar/Švýcarský Frank"]},e.exports["#AUDJPY-symbol-description"]={en:["Australian Dollar / Japanese Yen"],
cs:["Australský Dolar/Japonský Jen"]},e.exports["#AUDNZD-symbol-description"]={en:["Australian Dollar / New Zealand Dollar"],cs:["Australský Dolar/Japonský Jen"]},e.exports["#AUDRUB-symbol-description"]={en:["Australian Dollar / Russian Ruble"],cs:["AUSTRALSKÝ DOLAR / RUSKÝ RUBL"]},e.exports["#AUDUSD-symbol-description"]={en:["Australian Dollar / U.S. Dollar"],cs:["Australský Dolar/Americký Dolar"]},e.exports["#BRLJPY-symbol-description"]={en:["Brazilian Real / Japanese Yen"],cs:["Brazilský Real / Japonský Jen"]},e.exports["#BTCCAD-symbol-description"]={en:["Bitcoin / Canadian Dollar"],cs:["Bitcoin / Kanadský Dolar"]},e.exports["#BTCCNY-symbol-description"]={en:["Bitcoin / Chinese Yuan"],cs:["Bitcoin / Čínský Jüan"]},e.exports["#BTCEUR-symbol-description"]={en:["Bitcoin / Euro"],cs:["Bitcoin / Euro"]},e.exports["#BTCKRW-symbol-description"]={en:["Bitcoin / South Korean Won"],cs:["Bitcoin / Jihokorejský Won"]},e.exports["#BTCRUR-symbol-description"]={en:["Bitcoin / Ruble"],cs:["Bitcoin / Rubl"]},e.exports["#BTCUSD-symbol-description"]={en:["Bitcoin / U.S. Dollar"],cs:["Bitcoin / Dolar"]},e.exports["#BVSP-symbol-description"]={en:["Brazil Bovespa Index"],cs:["Brazil Bovespa Index"]},e.exports["#CADJPY-symbol-description"]={en:["Canadian Dollar / Japanese Yen"],cs:["Kanadský Dolar/Japonský Jen"]},e.exports["#CHFJPY-symbol-description"]={en:["Swiss Franc / Japanese Yen"],cs:["Švýcarský Frank/Japonský Jen"]},e.exports["#COPPER-symbol-description"]={en:["CFDs on Copper"],cs:["Měď"]},e.exports["#ES1-symbol-description"]={en:["S&P 500 E-Mini Futures"],cs:["S&P 500 E-Mini Futures"]},e.exports["#ESP35-symbol-description"]={en:["IBEX 35 Index"],cs:["IBEX 35 Index"]},e.exports["#EUBUND-symbol-description"]={en:["Euro Bund"],cs:["Euro Bund"]},e.exports["#EURAUD-symbol-description"]={en:["Euro / Australian Dollar"],cs:["Euro Fx/Australian Dollar"]},e.exports["#EURBRL-symbol-description"]={en:["Euro / Brazilian Real"],cs:["Euro / Brazilian Real"]},e.exports["#EURCAD-symbol-description"]={en:["Euro / Canadian Dollar"],cs:["Euro Fx/Canadian Dollar"]},e.exports["#EURCHF-symbol-description"]={en:["Euro / Swiss Franc"],cs:["Euro Fx/Swiss Franc"]},e.exports["#EURGBP-symbol-description"]={en:["Euro / British Pound"],cs:["Euro Fx/British Pound"]},e.exports["#EURJPY-symbol-description"]={en:["Euro / Japanese Yen"],cs:["Euro Fx/Japanese Yen"]},e.exports["#EURNZD-symbol-description"]={en:["Euro / New Zealand Dollar"],cs:["Euro Fx/New Zealand Dollar"]},e.exports["#EURRUB-symbol-description"]={en:["Euro / Russian Ruble"],cs:["EURO / RUSSIAN RUBLE"]},e.exports["#EURRUB_TOM-symbol-description"]={en:["Euro / Russian Ruble TOM"],cs:["EUR/RUB TOM"]},e.exports["#EURSEK-symbol-description"]={en:["Euro / Swedish Krona"],cs:["Euro / Swedish Krona"]},e.exports["#EURTRY-symbol-description"]={en:["Euro / Turkish Lira"],cs:["Euro Fx/Turkish New Lira"]},e.exports["#EURUSD-symbol-description"]={en:["Euro / U.S. Dollar"],cs:["Euro Fx/U.S. Dollar"]},e.exports["#EUSTX50-symbol-description"]={en:["Euro Stoxx 50 Index"],
cs:["Euro Stoxx 50 index of European listed shares"]},e.exports["#FRA40-symbol-description"]={en:["CAC 40 Index"],cs:["CAC 40 Index"]},e.exports["#GB10-symbol-description"]={en:["UK Government Bonds 10 yr"],cs:["UK Government Bonds 10 yr"]},e.exports["#GBPAUD-symbol-description"]={en:["British Pound / Australian Dollar"],cs:["Britská Libra/Australský Dolar"]},e.exports["#GBPCAD-symbol-description"]={en:["British Pound / Canadian Dollar"],cs:["Britská Libra/Kanadský Dolar"]},e.exports["#GBPCHF-symbol-description"]={en:["British Pound / Swiss Franc"],cs:["Britská Libra/Švýcarský Frank"]},e.exports["#GBPEUR-symbol-description"]={en:["British Pound / Euro"],cs:["POUND STERLING / EURO"]},e.exports["#GBPJPY-symbol-description"]={en:["British Pound / Japanese Yen"],cs:["Britská Libra/Japonský Jen"]},e.exports["#GBPNZD-symbol-description"]={en:["British Pound / New Zealand Dollar"],cs:["Britská Libra/Novozélandský Dolar"]},e.exports["#GBPRUB-symbol-description"]={en:["British Pound / Russian Ruble"],cs:["Britská Libra/Ruský Rubl"]},e.exports["#GBPUSD-symbol-description"]={en:["British Pound / U.S. Dollar"],cs:["Britská Libra/Americký Dolar"]},e.exports["#GER30-symbol-description"]={en:["DAX Index"],cs:["DAX index of German listed shares"]},e.exports["#GOOGL-symbol-description"]={en:["Alphabet Inc (Google) Class A"],cs:["GOOGLE INC"]},e.exports["#ITA40-symbol-description"]={en:["FTSE MIB Index"],cs:["FTSE MIB index of Italian listed shares"]},e.exports["#JPN225-symbol-description"]={en:["Nikkei 225 Index"],cs:["Nikkei 225 Index"]},e.exports["#JPYKRW-symbol-description"]={en:["Japanese Yen / South Korean Won"],cs:["YEN / WON"]},e.exports["#JPYRUB-symbol-description"]={en:["Japanese Yen / Russian Ruble"],cs:["JEN / RUSKÝ RUBL"]},e.exports["#KA1-symbol-description"]={en:["Sugar #11 Futures"],cs:["Sugar #11 Futures"]},e.exports["#KG1-symbol-description"]={en:["Cotton Futures"],cs:["Cotton Futures"]},e.exports["#KT1-symbol-description"]={en:["Key Tronic Corр."],cs:["Key Tronic Corр."]},e.exports["#LKOH-symbol-description"]={en:["LUKOIL"],cs:["LUKOIL"]},e.exports["#LTCBTC-symbol-description"]={en:["Litecoin / Bitcoin"],cs:["Litecoin / Bitcoin"]},e.exports["#MGNT-symbol-description"]={en:["Magnit"],cs:["MAGNIT"]},e.exports["#MICEX-symbol-description"]={en:["MICEX Index"],cs:["MICEX INDEX"]},e.exports["#MNOD_ME.EQRP-symbol-description"]={en:["ADR GMK NORILSKIYNIKEL ORD SHS [REPO]"],cs:["ADR GMK NORILSKIYNIKEL ORD SHS [REPO]"]},e.exports["#MSFT-symbol-description"]={en:["Microsoft Corp."],cs:["MICROSOFT CORP"]},e.exports["#NAS100-symbol-description"]={en:["US 100 Cash CFD"],cs:["NASDAQ 100 index of US listed shares"]},e.exports["#NGAS-symbol-description"]={en:["Natural Gas (Henry Hub)"],cs:["Natural Gas (Henry Hub)"]},e.exports["#NKY-symbol-description"]={en:["Nikkei 225 Index"],cs:["Nikkei 225 Index"]},e.exports["#NZDJPY-symbol-description"]={en:["New Zealand Dollar / Japanese Yen"],cs:["New Zealand Dollar/Japanese Yen"]},e.exports["#NZDUSD-symbol-description"]={en:["New Zealand Dollar / U.S. Dollar"],
cs:["New Zealand Dollar/U.S. Dollar"]},e.exports["#RB1-symbol-description"]={en:["RBOB Gasoline Futures"],cs:["RBOB Gasoline Futures"]},e.exports["#RTS-symbol-description"]={en:["Russian RTS Index"],cs:["Russian RTS Index"]},e.exports["#SBER-symbol-description"]={en:["SBERBANK"],cs:["SBERBANK"]},e.exports["#SPX500-symbol-description"]={en:["S&P 500 Index"],cs:["S&P 500 index of US listed shares"]},e.exports["#TWTR-symbol-description"]={en:["Twitter Inc"],cs:["TWITTER INC"]},e.exports["#UK100-symbol-description"]={en:["FTSE 100 Index"],cs:["FTSE 100 Index"]},e.exports["#USDBRL-symbol-description"]={en:["U.S. Dollar / Brazilian Real"],cs:["Americký Dolar/Brazilský Real"]},e.exports["#USDCAD-symbol-description"]={en:["U.S. Dollar / Canadian Dollar"],cs:["Americký Dolar/Kanadský Dolar"]},e.exports["#USDCHF-symbol-description"]={en:["U.S. Dollar / Swiss Franc"],cs:["Americký Dolar/Švýcarský Frank"]},e.exports["#USDCNY-symbol-description"]={en:["U.S. Dollar / Chinese Yuan"],cs:["Americký Dolar/Yuan Renminbi"]},e.exports["#USDDKK-symbol-description"]={en:["U.S. Dollar / Danish Krone"],cs:["US DOLAR / DÁNSKÁ KORUNA"]},e.exports["#USDHKD-symbol-description"]={en:["U.S. Dollar / Hong Kong Dollar"],cs:["Americký Dolar/Hongkongský Dolar"]},e.exports["#USDIDR-symbol-description"]={en:["U.S. Dollar / Rupiah"],cs:["Americký Dolar / Rupie"]},e.exports["#USDINR-symbol-description"]={en:["U.S. Dollar / Indian Rupee"],cs:["Americký Dolar / Indická Rupie"]},e.exports["#USDJPY-symbol-description"]={en:["U.S. Dollar / Japanese Yen"],cs:["Americký Dolar/Japonský Jen"]},e.exports["#USDKRW-symbol-description"]={en:["U.S. Dollar / South Korean"],cs:["US DOLLAR / WON"]},e.exports["#USDMXN-symbol-description"]={en:["U.S. Dollar / Mexican Peso"],cs:["Americký Dolar/Mexické Peso"]},e.exports["#USDPHP-symbol-description"]={en:["U.S. Dollar / Philippine peso"],cs:["U.S. Dollar / Philippine peso"]},e.exports["#USDRUB-symbol-description"]={en:["U.S. Dollar / Russian Ruble"],cs:["US DOLLAR / RUSSIAN RUBLE"]},e.exports["#USDRUB_TOM-symbol-description"]={en:["U.S. Dollar / Russian Ruble TOM"],cs:["USD/RUB TOM"]},e.exports["#USDSEK-symbol-description"]={en:["U.S. Dollar / Swedish Krona"],cs:["U.S. Dollar/Swedish Krona"]},e.exports["#USDSGD-symbol-description"]={en:["U.S. Dollar / Singapore Dollar"],cs:["US DOLLAR / SINGAPORE DOLLAR"]},e.exports["#USDTRY-symbol-description"]={en:["U.S. Dollar / Turkish Lira"],cs:["U.S. Dollar/Turkish New Lira"]},e.exports["#VTBR-symbol-description"]={en:["VTB"],cs:["VTB"]},e.exports["#XAGUSD-symbol-description"]={en:["Silver / U.S. Dollar"],cs:["Silver/U.S. Dollar"]},e.exports["#XAUUSD-symbol-description"]={en:["Gold Spot / U.S. Dollar"],cs:["Zlato / Americký Dolar"]},e.exports["#XPDUSD-symbol-description"]={en:["CFDs on Palladium"],cs:["CFDs on Palladium"]},e.exports["#XPTUSD-symbol-description"]={en:["Platinum / U.S. Dollar"],cs:["Platina/Americký Dolar"]},e.exports["#ZS1-symbol-description"]={en:["Soybean Futures - ECBT"],cs:["Soybean Futures - ECBT"]},e.exports["#ZW1-symbol-description"]={en:["Wheat Futures - ECBT"],
cs:["Wheat Futures - ECBT"]},e.exports["#BTCGBP-symbol-description"]={en:["Bitcoin / British Pound"],cs:["Bitcoin / British Pound"]},e.exports["#MICEXINDEXCF-symbol-description"]={en:["MOEX Russia Index"],cs:["MICEX Index"]},e.exports["#BTCAUD-symbol-description"]={en:["Bitcoin / Australian Dollar"],cs:["Bitcoin / Australian Dollar"]},e.exports["#BTCJPY-symbol-description"]={en:["Bitcoin / Japanese Yen"],cs:["Bitcoin / Japanese Yen"]},e.exports["#BTCBRL-symbol-description"]={en:["Bitcoin / Brazilian Real"],cs:["Bitcoin / Brazilian Real"]},e.exports["#PT10-symbol-description"]={en:["Portugal Government Bonds 10 yr"],cs:["Portugal Government Bonds 10 yr"]},e.exports["#TXSX-symbol-description"]={en:["TSX 60 Index"],cs:["TSX 60 Index"]},e.exports["#VIXC-symbol-description"]={en:["TSX 60 VIX Index"],cs:["TSX 60 VIX"]},e.exports["#USDPLN-symbol-description"]={en:["U.S. Dollar / Polish Zloty"],cs:["USD/PLN"]},e.exports["#EURPLN-symbol-description"]={en:["Euro / Polish Zloty"],cs:["EUR/PLN"]},e.exports["#BTCPLN-symbol-description"]={en:["Bitcoin / Polish Zloty"],cs:["Bitcoin / Polish Zloty"]},e.exports["#CAC40-symbol-description"]={en:["CAC 40 Index"],cs:["CAC 40"]},e.exports["#XBTCAD-symbol-description"]={en:["Bitcoin / Canadian Dollar"],cs:["Bitcoin / Canadian Dollar"]},e.exports["#ITI2!-symbol-description"]={en:["Iron Ore Futures"],cs:["Iron Ore Futures"]},e.exports["#ITIF2018-symbol-description"]={en:["Iron Ore Futures"],cs:["Iron Ore Futures"]},e.exports["#ITIF2019-symbol-description"]={en:["Iron Ore Futures"],cs:["Iron Ore Futures"]},e.exports["#ITIF2020-symbol-description"]={en:["Iron Ore Futures"],cs:["Iron Ore Futures"]},e.exports["#ITIG2018-symbol-description"]={en:["Iron Ore Futures"],cs:["Iron Ore Futures"]},e.exports["#ITIG2019-symbol-description"]={en:["Iron Ore Futures"],cs:["Iron Ore Futures"]},e.exports["#ITIG2020-symbol-description"]={en:["Iron Ore Futures"],cs:["Iron Ore Futures"]},e.exports["#ITIH2018-symbol-description"]={en:["Iron Ore Futures"],cs:["Iron Ore Futures"]},e.exports["#ITIH2019-symbol-description"]={en:["Iron Ore Futures"],cs:["Iron Ore Futures"]},e.exports["#ITIH2020-symbol-description"]={en:["Iron Ore Futures"],cs:["Iron Ore Futures"]},e.exports["#ITIJ2018-symbol-description"]={en:["Iron Ore Futures"],cs:["Iron Ore Futures"]},e.exports["#ITIJ2019-symbol-description"]={en:["Iron Ore Futures"],cs:["Iron Ore Futures"]},e.exports["#ITIJ2020-symbol-description"]={en:["Iron Ore Futures"],cs:["Iron Ore Futures"]},e.exports["#ITIK2018-symbol-description"]={en:["Iron Ore Futures"],cs:["Iron Ore Futures"]},e.exports["#ITIK2019-symbol-description"]={en:["Iron Ore Futures"],cs:["Iron Ore Futures"]},e.exports["#ITIK2020-symbol-description"]={en:["Iron Ore Futures"],cs:["Iron Ore Futures"]},e.exports["#ITIM2017-symbol-description"]={en:["Iron Ore Futures"],cs:["Iron Ore Futures"]},e.exports["#ITIM2018-symbol-description"]={en:["Iron Ore Futures"],cs:["Iron Ore Futures"]},e.exports["#ITIM2019-symbol-description"]={en:["Iron Ore Futures"],cs:["Iron Ore Futures"]},
e.exports["#ITIM2020-symbol-description"]={en:["Iron Ore Futures"],cs:["Iron Ore Futures"]},e.exports["#ITIN2017-symbol-description"]={en:["Iron Ore Futures"],cs:["Iron Ore Futures"]},e.exports["#ITIN2018-symbol-description"]={en:["Iron Ore Futures"],cs:["Iron Ore Futures"]},e.exports["#ITIN2019-symbol-description"]={en:["Iron Ore Futures"],cs:["Iron Ore Futures"]},e.exports["#ITIN2020-symbol-description"]={en:["Iron Ore Futures"],cs:["Iron Ore Futures"]},e.exports["#ITIQ2017-symbol-description"]={en:["Iron Ore Futures"],cs:["Iron Ore Futures"]},e.exports["#ITIQ2018-symbol-description"]={en:["Iron Ore Futures"],cs:["Iron Ore Futures"]},e.exports["#ITIQ2019-symbol-description"]={en:["Iron Ore Futures"],cs:["Iron Ore Futures"]},e.exports["#ITIQ2020-symbol-description"]={en:["Iron Ore Futures"],cs:["Iron Ore Futures"]},e.exports["#ITIU2017-symbol-description"]={en:["Iron Ore Futures"],cs:["Iron Ore Futures"]},e.exports["#ITIU2018-symbol-description"]={en:["Iron Ore Futures"],cs:["Iron Ore Futures"]},e.exports["#ITIU2019-symbol-description"]={en:["Iron Ore Futures"],cs:["Iron Ore Futures"]},e.exports["#ITIU2020-symbol-description"]={en:["Iron Ore Futures"],cs:["Iron Ore Futures"]},e.exports["#ITIV2017-symbol-description"]={en:["Iron Ore Futures"],cs:["Iron Ore Futures"]},e.exports["#ITIV2018-symbol-description"]={en:["Iron Ore Futures"],cs:["Iron Ore Futures"]},e.exports["#ITIV2019-symbol-description"]={en:["Iron Ore Futures"],cs:["Iron Ore Futures"]},e.exports["#ITIV2020-symbol-description"]={en:["Iron Ore Futures"],cs:["Iron Ore Futures"]},e.exports["#ITIX2017-symbol-description"]={en:["Iron Ore Futures"],cs:["Iron Ore Futures"]},e.exports["#ITIX2018-symbol-description"]={en:["Iron Ore Futures"],cs:["Iron Ore Futures"]},e.exports["#ITIX2019-symbol-description"]={en:["Iron Ore Futures"],cs:["Iron Ore Futures"]},e.exports["#ITIX2020-symbol-description"]={en:["Iron Ore Futures"],cs:["Iron Ore Futures"]},e.exports["#ITIZ2017-symbol-description"]={en:["Iron Ore Futures"],cs:["Iron Ore Futures"]},e.exports["#ITIZ2018-symbol-description"]={en:["Iron Ore Futures"],cs:["Iron Ore Futures"]},e.exports["#ITIZ2019-symbol-description"]={en:["Iron Ore Futures"],cs:["Iron Ore Futures"]},e.exports["#ITIZ2020-symbol-description"]={en:["Iron Ore Futures"],cs:["Iron Ore Futures"]},e.exports["#AMEX:GXF-symbol-description"]={en:["Global x FTSE Nordic Region ETF"],cs:["Global x FTSE Nordic Region ETF"]},e.exports["#ASX:XAF-symbol-description"]={en:["S&P/ASX All Australian 50 Index"],cs:["S&P/ASX All Australian 50 Index"]},e.exports["#ASX:XAT-symbol-description"]={en:["S&P/ASX All Australian 200 Index"],cs:["S&P/ASX All Australian 200 Index"]},e.exports["#BIST:XU100-symbol-description"]={en:["BIST 100 Index"],cs:["BIST 100 Index"]},e.exports["#GPW:WIG20-symbol-description"]={en:["WIG20 Index"],cs:["WIG20 Index"]},e.exports["#INDEX:JKSE-symbol-description"]={en:["Jakarta Composite Index"],cs:["Jakarta Composite Index"]},e.exports["#INDEX:KLSE-symbol-description"]={en:["Bursa Malaysia KLCI Index"],cs:["Bursa Malaysia KLCI Index"]},
e.exports["#INDEX:NZD-symbol-description"]={en:["NZX 50 Index"],cs:["NZX 50 Index"]},e.exports["#INDEX:STI-symbol-description"]={en:["STI Index"],cs:["STI Index"]},e.exports["#INDEX:XLY0-symbol-description"]={en:["Shanghai Composite Index"],cs:["Shanghai Composite Index"]},e.exports["#MOEX:MICEXINDEXCF-symbol-description"]={en:["MOEX Russia Index"],cs:["MOEX Russia Index"]},e.exports["#NYMEX:KT1!-symbol-description"]={en:["Coffee Futures"],cs:["Coffee Futures"]},e.exports["#OANDA:NATGASUSD-symbol-description"]={en:["CFDs on Natural Gas"],cs:["CFDs on Natural Gas"]},e.exports["#OANDA:USDPLN-symbol-description"]={en:["U.S. Dollar / Polish Zloty"],cs:["U.S. Dollar / Polish Zloty"]},e.exports["#TSX:TX60-symbol-description"]={en:["S&P/TSX 60 Index"],cs:["S&P/TSX 60 Index"]},e.exports["#TSX:VBU-symbol-description"]={en:["Vanguard U.S. Aggregate Bond Index ETF (CAD-hedged) UN"],cs:["Vanguard U.S. Aggregate Bond Index ETF (CAD-hedged) UN"]},e.exports["#TSX:VIXC-symbol-description"]={en:["S&P/TSX 60 VIX Index"],cs:["S&P/TSX 60 VIX Index"]},e.exports["#TVC:CAC40-symbol-description"]={en:["CAC 40 Index"],cs:["CAC 40 Index"]},e.exports["#TVC:ES10-symbol-description"]={en:["Spain Government Bonds 10 YR"],cs:["Spain Government Bonds 10 YR"]},e.exports["#TVC:EUBUND-symbol-description"]={en:["Euro Bund"],cs:["Euro Bund"]},e.exports["#TVC:GB02-symbol-description"]={en:["UK Government Bonds 2 YR"],cs:["UK Government Bonds 2 YR"]},e.exports["#TVC:GB10-symbol-description"]={en:["UK Government Bonds 10 YR"],cs:["UK Government Bonds 10 YR"]},e.exports["#TVC:GOLD-symbol-description"]={en:["CFDs on Gold (US$ / OZ)"],cs:["CFDs on Gold (US$ / OZ)"]},e.exports["#TVC:ID03-symbol-description"]={en:["Indonesia Government Bonds 3 YR"],cs:["Indonesia Government Bonds 3 YR"]},e.exports["#TVC:ID10-symbol-description"]={en:["Indonesia Government Bonds 10 YR"],cs:["Indonesia Government Bonds 10 YR"]},e.exports["#TVC:PALLADIUM-symbol-description"]={en:["CFDs on Palladium (US$ / OZ)"],cs:["CFDs on Palladium (US$ / OZ)"]},e.exports["#TVC:PT10-symbol-description"]={en:["Portugal Government Bonds 10 YR"],cs:["Portugal Government Bonds 10 YR"]},e.exports["#TVC:SILVER-symbol-description"]={en:["CFDs on Silver (US$ / OZ)"],cs:["CFDs on Silver (US$ / OZ)"]},e.exports["#TSX:TSX-symbol-description"]={en:["S&P/TSX Composite Index"],cs:["S&P/TSX Composite Index"]},e.exports["#OANDA:CH20CHF-symbol-description"]={en:["Swiss 20 Index"],cs:["Swiss 20 Index"]},e.exports["#TVC:SHCOMP-symbol-description"]={en:["Shanghai Composite Index"],cs:["Shanghai Composite Index"]},e.exports["#NZX:ALLC-symbol-description"]={en:["S&P/NZX All Index (Capital Index)"],cs:["S&P/NZX All Index (Capital Index)"]},e.exports["#AMEX:SHYG-symbol-description"]={en:["Shares 0-5 YEAR High Yield Corporate Bond ETF"],cs:["Shares 0-5 YEAR High Yield Corporate Bond ETF"]},e.exports["#TVC:AU10-symbol-description"]={en:["Australia Government Bonds 10 YR"],cs:["Australia Government Bonds 10 YR"]},e.exports["#TVC:CN10-symbol-description"]={en:["China Government Bonds 10 YR"],
cs:["China Government Bonds 10 YR"]},e.exports["#TVC:KR10-symbol-description"]={en:["Korea Government Bonds 10 YR"],cs:["Korea Government Bonds 10 YR"]},e.exports["#NYMEX:RB1!-symbol-description"]={en:["RBOB Gasoline Futures"],cs:["RBOB Gasoline Futures"]},e.exports["#NYMEX:HO1!-symbol-description"]={en:["NY Harbor ULSD Futures"],cs:["NY Harbor ULSD Futures"]},e.exports["#NYMEX:AEZ1!-symbol-description"]={en:["NY Ethanol Futures"],cs:["NY Ethanol Futures"]},e.exports["#OANDA:XCUUSD-symbol-description"]={en:["CFDs on Copper (US$ / lb)"],cs:["CFDs on Copper (US$ / lb)"]},e.exports["#COMEX:ZA1!-symbol-description"]={en:["Zinc Futures"],cs:["Zinc Futures"]},e.exports["#CBOT:ZW1!-symbol-description"]={en:["Wheat Futures"],cs:["Wheat Futures"]},e.exports["#NYMEX:KA1!-symbol-description"]={en:["Sugar #11 Futures"],cs:["Sugar #11 Futures"]},e.exports["#CBOT:QBC1!-symbol-description"]={en:["Corn Futures"],cs:["Corn Futures"]},e.exports["#CME:E61!-symbol-description"]={en:["Euro Futures"],cs:["Euro Futures"]},e.exports["#CME:B61!-symbol-description"]={en:["British Pound Futures"],cs:["British Pound Futures"]},e.exports["#CME:QJY1!-symbol-description"]={en:["Japanese Yen Futures"],cs:["Japanese Yen Futures"]},e.exports["#CME:A61!-symbol-description"]={en:["Australian Dollar Futures"],cs:["Australian Dollar Futures"]},e.exports["#CME:D61!-symbol-description"]={en:["Canadian Dollar Futures"],cs:["Canadian Dollar Futures"]},e.exports["#CME:SP1!-symbol-description"]={en:["S&P 500 Futures"],cs:["S&P 500 Futures"]},e.exports["#CME_MINI:NQ1!-symbol-description"]={en:["NASDAQ 100 E-mini Futures"],cs:["NASDAQ 100 E-mini Futures"]},e.exports["#CBOT_MINI:YM1!-symbol-description"]={en:["E-mini Dow Jones ($5) Futures"],cs:["E-mini Dow Jones ($5) Futures"]},e.exports["#CME:NY1!-symbol-description"]={en:["NIKKEI 225 Futures"],cs:["NIKKEI 225 Futures"]},e.exports["#EUREX:DY1!-symbol-description"]={en:["DAX Index"],cs:["DAX Index"]},e.exports["#CME:IF1!-symbol-description"]={en:["IBOVESPA Index Futures-US$"],cs:["IBOVESPA Index Futures-US$"]},e.exports["#CBOT:TY1!-symbol-description"]={en:["10 Year T-Note Futures"],cs:["10 Year T-Note Futures"]},e.exports["#CBOT:FV1!-symbol-description"]={en:["5 Year T-Note Futures"],cs:["5 Year T-Note Futures"]},e.exports["#CBOT:ZE1!-symbol-description"]={en:["Treasury Notes - 3 Year Futures"],cs:["Treasury Notes - 3 Year Futures"]},e.exports["#CBOT:TU1!-symbol-description"]={en:["2 Year T-Note Futures"],cs:["2 Year T-Note Futures"]},e.exports["#CBOT:FF1!-symbol-description"]={en:["30-Day FED Funds Interest Rate Futures"],cs:["30-Day FED Funds Interest Rate Futures"]},e.exports["#CBOT:US1!-symbol-description"]={en:["T-Bond Futures"],cs:["T-Bond Futures"]},e.exports["#TVC:EXY-symbol-description"]={en:["Euro Currency Index"],cs:["Euro Currency Index"]},e.exports["#TVC:JXY-symbol-description"]={en:["Japanese Yen Currency Index"],cs:["Japanese Yen Currency Index"]},e.exports["#TVC:BXY-symbol-description"]={en:["British Pound Currency Index"],cs:["British Pound Currency Index"]},
e.exports["#TVC:AXY-symbol-description"]={en:["Australian Dollar Currency Index"],cs:["Australian Dollar Currency Index"]},e.exports["#TVC:CXY-symbol-description"]={en:["Canadian Dollar Currency Index"],cs:["Canadian Dollar Currency Index"]},e.exports["#FRED:GDP-symbol-description"]={en:["Gross Domestic Product, 1 Decimal"],cs:["Gross Domestic Product, 1 Decimal"]},e.exports["#FRED:UNRATE-symbol-description"]={en:["Civilian Unemployment Rate"],cs:["Civilian Unemployment Rate"]},e.exports["#FRED:POP-symbol-description"]={en:["Total Population: All Ages Including Armed Forces Overseas"],cs:["Total Population: All Ages Including Armed Forces Overseas"]},e.exports["#ETHUSD-symbol-description"]={en:["Ethereum / U.S. Dollar"],cs:["Ethereum / U.S. Dollar"]},e.exports["#BMFBOVESPA:IBOV-symbol-description"]={en:["Bovespa Index"],cs:["IBovespa Index"]},e.exports["#BMFBOVESPA:IBRA-symbol-description"]={en:["IBrasil Index"],cs:["IBrasil Index"]},e.exports["#BMFBOVESPA:IBXL-symbol-description"]={en:["Brazil 50 Index"],cs:["IBRX 50 Index"]},e.exports["#COMEX:HG1!-symbol-description"]={en:["Copper Futures"],cs:["Copper Futures"]},e.exports["#INDEX:HSCE-symbol-description"]={en:["Hang Seng China Enterprises Index"],cs:["Hang Seng China Enterprises Index"]},e.exports["#NYMEX:CL1!-symbol-description"]={en:["Light Crude Oil Futures"],cs:["Light Crude Oil Futures"]},e.exports["#OTC:IHRMF-symbol-description"]={en:["Ishares MSCI Japan SHS"],cs:["Ishares MSCI Japan SHS"]},e.exports["#TVC:DAX-symbol-description"]={en:["DAX Index"],cs:["DAX Index"]},e.exports["#TVC:DE10-symbol-description"]={en:["German Government Bonds 10 YR"],cs:["German Government Bonds 10 YR"]},e.exports["#TVC:DJI-symbol-description"]={en:["Dow Jones Industrial Average Index"],cs:["Dow Jones Industrial Average Index"]},e.exports["#TVC:DXY-symbol-description"]={en:["U.S. Dollar Index"],cs:["U.S. Dollar Currency Index"]},e.exports["#TVC:FR10-symbol-description"]={en:["France Government Bonds 10 YR"],cs:["France Government Bonds 10 YR"]},e.exports["#TVC:HSI-symbol-description"]={en:["Hang Seng Index"],cs:["Hang Seng Index"]},e.exports["#TVC:IBEX35-symbol-description"]={en:["IBEX 35 Index"],cs:["IBEX 35 Index"]},e.exports["#FX:AUS200-symbol-description"]={en:["S&P/ASX Index"],cs:["S&P/ASX Index"]},e.exports["#AMEX:SHY-symbol-description"]={en:["Ishares 1-3 Year Treasury Bond ETF"],cs:["Ishares 1-3 Year Treasury Bond ETF"]},e.exports["#ASX:XJO-symbol-description"]={en:["S&P/ASX 200 Index"],cs:["S&P/ASX 200 Index"]},e.exports["#BSE:SENSEX-symbol-description"]={en:["S&P BSE Sensex Index"],cs:["S&P BSE Sensex Index"]},e.exports["#INDEX:MIB-symbol-description"]={en:["MIB Index"],cs:["MIB Index"]},e.exports["#INDEX:MOY0-symbol-description"]={en:["Euro Stoxx 50 Index"],cs:["Euro Stoxx 50 Index"]},e.exports["#MOEX:RTSI-symbol-description"]={en:["RTS Index"],cs:["RTS Index"]},e.exports["#NSE:NIFTY-symbol-description"]={en:["Nifty 50 Index"],cs:["Nifty 50 Index"]},e.exports["#NYMEX:NG1!-symbol-description"]={en:["Natural Gas Futures"],cs:["Natural Gas Futures"]},
e.exports["#NYMEX:ZC1!-symbol-description"]={en:["Corn Futures"],cs:["Corn Futures"]},e.exports["#TVC:IN10-symbol-description"]={en:["India Government Bonds 10 YR"],cs:["India Government Bonds 10 YR"]},e.exports["#TVC:IT10-symbol-description"]={en:["Italy Government Bonds 10 YR"],cs:["Italy Government Bonds 10 YR"]},e.exports["#TVC:JP10-symbol-description"]={en:["Japan Government Bonds 10 YR"],cs:["Japan Government Bonds 10 YR"]},e.exports["#TVC:NDX-symbol-description"]={en:["US 100 Index"],cs:["NASDAQ 100 Index"]},e.exports["#TVC:NI225-symbol-description"]={en:["Nikkei 225 Index"],cs:["Nikkei 225 Index"]},e.exports["#TVC:SPX-symbol-description"]={en:["S&P 500 Index"],cs:["S&P 500 Index"]},e.exports["#TVC:SX5E-symbol-description"]={en:["Euro Stoxx 50 Index"],cs:["Euro Stoxx 50 Index"]},e.exports["#TVC:TR10-symbol-description"]={en:["Turkey Government Bonds 10 YR"],cs:["Turkey Government Bonds 10 YR"]},e.exports["#TVC:UKOIL-symbol-description"]={en:["CFDs on Brent Crude Oil"],cs:["CFDs on Brent Crude Oil"]},e.exports["#TVC:UKX-symbol-description"]={en:["UK 100 Index"],cs:["UK 100 Index"]},e.exports["#TVC:US02-symbol-description"]={en:["US Government Bonds 2 YR"],cs:["US Government Bonds 2 YR"]},e.exports["#TVC:US05-symbol-description"]={en:["US Government Bonds 5 YR"],cs:["US Government Bonds 5 YR"]},e.exports["#TVC:US10-symbol-description"]={en:["US Government Bonds 10 YR"],cs:["US Government Bonds 10 YR"]},e.exports["#TVC:USOIL-symbol-description"]={en:["CFDs on WTI Crude Oil"],cs:["CFDs on WTI Crude Oil"]},e.exports["#NYMEX:ITI1!-symbol-description"]={en:["Iron Ore Futures"],cs:["Iron Ore Futures"]},e.exports["#NASDAQ:SHY-symbol-description"]={en:["Ishares 1-3 Year Treasury Bond ETF"],cs:["Ishares 1-3 Year Treasury Bond ETF"]},e.exports["#AMEX:ALD-symbol-description"]={en:["WisdomTree Asia Local Debt ETF"],cs:["WisdomTree Asia Local Debt ETF"]},e.exports["#NASDAQ:AMD-symbol-description"]={en:["Advanced Micro Devices Inc"],cs:["Advanced Micro Devices Inc"]},e.exports["#NYSE:BABA-symbol-description"]={en:["Alibaba Group Holdings Ltd."],cs:["ALIBABA GROUP HLDG LTD"]},e.exports["#ICEEUR:CB-symbol-description"]={en:["Crude Oil Brent"],cs:["Crude Oil Brent"]},e.exports["#ICEEUR:CB1!-symbol-description"]={en:["Brent Crude Oil"],cs:["Brent Crude Oil"]},e.exports["#ICEUSA:CC-symbol-description"]={en:["Cocoa"],cs:["Kakao"]},e.exports["#NYMEX:CL-symbol-description"]={en:["Crude Oil WTI"],cs:["Crude Oil WTI"]},e.exports["#ICEUSA:CT-symbol-description"]={en:["Cotton #2"],cs:["Bavlna č. 2"]},e.exports["#NASDAQ:CTRV-symbol-description"]={en:["ContraVir Pharmaceuticals Inc"],cs:["CONTRAVIR PHARMACEUTICALS INC"]},e.exports["#CME:DL-symbol-description"]={en:["Class III Milk"],cs:["Class III Milk"]},e.exports["#NYSE:F-symbol-description"]={en:["FORD MTR CO DEL"],cs:["FORD MTR CO DEL"]},e.exports["#MOEX:GAZP-symbol-description"]={en:["GAZPROM"],cs:["GAZPROM"]},e.exports["#COMEX:GC-symbol-description"]={en:["Gold"],cs:["Zlato"]},e.exports["#CME:GF-symbol-description"]={en:["Feeder Cattle"],cs:["Feeder Cattle"]},
e.exports["#CME:HE-symbol-description"]={en:["Lean Hogs"],cs:["Lean Hogs"]},e.exports["#NASDAQ:IEF-symbol-description"]={en:["Ishares 7-10 Year Treasury Bond ETF"],cs:["Ishares 7-10 Year Treasury Bond ETF"]},e.exports["#NASDAQ:IEI-symbol-description"]={en:["Ishares 3-7 Year Treasury Bond ETF"],cs:["Ishares 3-7 Year Treasury Bond ETF"]},e.exports["#NYMEX:KA1-symbol-description"]={en:["Sugar #11 Futures"],cs:["Sugar #11 Futures"]},e.exports["#ICEUSA:KC-symbol-description"]={en:["Coffee"],cs:["Coffee"]},e.exports["#NYMEX:KG1-symbol-description"]={en:["Cotton Futures"],cs:["Cotton Futures"]},e.exports["#FWB:KT1-symbol-description"]={en:["Key Tronic Corр."],cs:["Key Tronic Corр."]},e.exports["#CME:LE-symbol-description"]={en:["Live Cattle"],cs:["Live Cattle"]},e.exports["#ICEEUR:LO-symbol-description"]={en:["ICE Heating Oil"],cs:["ICE Heating Oil"]},e.exports["#CME:LS-symbol-description"]={en:["Lumber"],cs:["Lumber"]},e.exports["#MOEX:MGNT-symbol-description"]={en:["MAGNIT"],cs:["MAGNIT"]},e.exports["#LSIN:MNOD-symbol-description"]={en:["ADR GMK NORILSKIYNIKEL ORD SHS [REPO]"],cs:["ADR GMK NORILSKIYNIKEL ORD SHS [REPO]"]},e.exports["#NYMEX:NG-symbol-description"]={en:["Natural Gas"],cs:["Zemní plyn"]},e.exports["#ICEUSA:OJ-symbol-description"]={en:["Orange Juice"],cs:["Pomerančový džus"]},e.exports["#NYMEX:PA-symbol-description"]={en:["Palladium"],cs:["Palladium"]},e.exports["#NYSE:PBR-symbol-description"]={en:["PETROLEO BRASILEIRO SA PETROBR"],cs:["PETROLEO BRASILEIRO SA PETROBR"]},e.exports["#NYMEX:PL-symbol-description"]={en:["Platinum"],cs:["Platina"]},e.exports["#COMEX_MINI:QC-symbol-description"]={en:["E-Mini Copper"],cs:["E-Mini Copper"]},e.exports["#NYMEX:RB-symbol-description"]={en:["Gasoline RBOB"],cs:["Gasoline RBOB"]},e.exports["#NYMEX:RB1-symbol-description"]={en:["RBOB Gasoline Futures"],cs:["RBOB Gasoline Futures"]},e.exports["#MOEX:SBER-symbol-description"]={en:["SBERBANK"],cs:["SBERBANK"]},e.exports["#AMEX:SCHO-symbol-description"]={en:["Schwab Short-Term U.S. Treasury ETF"],cs:["Schwab Short-Term U.S. Treasury ETF"]},e.exports["#COMEX:SI-symbol-description"]={en:["Silver"],cs:["Stříbro"]},e.exports["#NASDAQ:TLT-symbol-description"]={en:["Ishares 20+ Year Treasury Bond ETF"],cs:["Ishares 20+ Year Treasury Bond ETF"]},e.exports["#TVC:VIX-symbol-description"]={en:["Volatility S&P 500 Index"],cs:["Volatility S&P 500 Index"]},e.exports["#MOEX:VTBR-symbol-description"]={en:["VTB"],cs:["VTB"]},e.exports["#COMEX:ZA-symbol-description"]={en:["Zinc"],cs:["Zinc"]},e.exports["#CBOT:ZC-symbol-description"]={en:["Corn"],cs:["Kukuřice"]},e.exports["#CBOT:ZK-symbol-description"]={en:["Ethanol Futures"],cs:["Ethanol Futures"]},e.exports["#CBOT:ZL-symbol-description"]={en:["Soybean Oil"],cs:["Soybean Oil"]},e.exports["#CBOT:ZO-symbol-description"]={en:["Oats"],cs:["Oves"]},e.exports["#CBOT:ZR-symbol-description"]={en:["Rough Rice"],cs:["Hrubá rýže"]},e.exports["#CBOT:ZS-symbol-description"]={en:["Soybeans"],cs:["Sójové boby"]},e.exports["#CBOT:ZS1-symbol-description"]={en:["Soybean Futures"],cs:["Soybean Futures"]},
e.exports["#CBOT:ZW-symbol-description"]={en:["Wheat"],cs:["Pšenice"]},e.exports["#CBOT:ZW1-symbol-description"]={en:["Wheat Futures - ECBT"],cs:["Wheat Futures - ECBT"]},e.exports["#NASDAQ:ITI-symbol-description"]={en:["Iteris Inc"],cs:["Iteris Inc."]},e.exports["#NYMEX:ITI2!-symbol-description"]={en:["Iron Ore Futures"],cs:["Iron Ore Futures"]},e.exports["#CADUSD-symbol-description"]={en:["Canadian Dollar / U.S. Dollar"],cs:["Canadian Dollar / U.S. Dollar"]},e.exports["#CHFUSD-symbol-description"]={en:["Swiss Franc / U.S. Dollar"],cs:["Swiss Franc / U.S. Dollar"]},e.exports["#GPW:ACG-symbol-description"]={en:["Acautogaz"],cs:["Acautogaz"]},e.exports["#JPYUSD-symbol-description"]={en:["Japanese Yen / U.S. Dollar"],cs:["Japanese Yen / U.S. Dollar"]},e.exports["#USDAUD-symbol-description"]={en:["U.S. Dollar / Australian Dollar"],cs:["U.S. Dollar / Australian Dollar"]},e.exports["#USDEUR-symbol-description"]={en:["U.S. Dollar / Euro"],cs:["U.S. Dollar / Euro"]},e.exports["#USDGBP-symbol-description"]={en:["U.S. Dollar / Pound Sterling"],cs:["U.S. Dollar / Pound Sterling"]},e.exports["#USDNZD-symbol-description"]={en:["U.S. Dollar / New Zealand Dollar"],cs:["U.S. Dollar / New Zealand Dollar"]},e.exports["#UKOIL-symbol-description"]={en:["CFDs on Crude Oil (Brent)"],cs:["CFDs on Crude Oil (Brent)"]},e.exports["#USOIL-symbol-description"]={en:["CFDs on Crude Oil (WTI)"],cs:["CFDs on Crude Oil (WTI)"]},e.exports["#US30-symbol-description"]={en:["Dow Jones Industrial Average Index"],cs:["Dow Jones Industrial Average Index"]},e.exports["#BCHUSD-symbol-description"]={en:["Bitcoin Cash / U.S. Dollar"],cs:["Bitcoin Cash / U.S. Dollar"]},e.exports["#ETCUSD-symbol-description"]={en:["Ethereum Classic / U.S. Dollar"],cs:["Ethereum Classic / U.S. Dollar"]},e.exports["#GOOG-symbol-description"]={en:["Alphabet Inc (Google) Class C"],cs:["Alphabet Inc (Google) Class C"]},e.exports["#LTCUSD-symbol-description"]={en:["Litecoin / U.S. Dollar"],cs:["Litecoin / U.S. Dollar"]},e.exports["#XRPUSD-symbol-description"]={en:["XRP / U.S. Dollar"],cs:["Ripple / U.S. Dollar"]},e.exports["#SP:SPX-symbol-description"]={en:["S&P 500 Index"],cs:["S&P 500 Index"]},e.exports["#ETCBTC-symbol-description"]={en:["Ethereum Classic / Bitcoin"],cs:["Ethereum Classic / Bitcoin"]},e.exports["#ETHBTC-symbol-description"]={en:["Ethereum / Bitcoin"],cs:["Ethereum / Bitcoin"]},e.exports["#XRPBTC-symbol-description"]={en:["XRP / Bitcoin"],cs:["Ripple / Bitcoin"]},e.exports["#TVC:US30-symbol-description"]={en:["US Government Bonds 30 YR"],cs:["US Government Bonds 30 YR"]},e.exports["#COMEX:SI1!-symbol-description"]={en:["Silver Futures"],cs:["Silver Futures"]},e.exports["#BTGUSD-symbol-description"]={en:["Bitcoin Gold / U.S. Dollar"],cs:["Bitcoin Gold / U.S. Dollar"]},e.exports["#IOTUSD-symbol-description"]={en:["IOTA / U.S. Dollar"],cs:["IOTA / U.S. Dollar"]},e.exports["#CME:BTC1!-symbol-description"]={en:["Bitcoin CME Futures"],cs:["Bitcoin CME Futures"]},e.exports["#COMEX:GC1!-symbol-description"]={en:["Gold Futures"],cs:["Gold Futures"]},
e.exports["#CORNUSD-symbol-description"]={en:["CFDs on Corn"],cs:["CFDs on Corn"]},e.exports["#COTUSD-symbol-description"]={en:["CFDs on Cotton"],cs:["CFDs on Cotton"]},e.exports["#DJ:DJA-symbol-description"]={en:["Dow Jones Composite Average Index"],cs:["Dow Jones Composite Average Index"]},e.exports["#DJ:DJI-symbol-description"]={en:["Dow Jones Industrial Average Index"],cs:["Dow Jones Industrial Average Index"]},e.exports["#ETHEUR-symbol-description"]={en:["Ethereum / Euro"],cs:["Ethereum / Euro"]},e.exports["#ETHGBP-symbol-description"]={en:["Ethereum / British Pound"],cs:["Ethereum / British Pound"]},e.exports["#ETHJPY-symbol-description"]={en:["Ethereum / Japanese Yen"],cs:["Ethereum / Japanese Yen"]},e.exports["#EURNOK-symbol-description"]={en:["Euro / Norwegian Krone"],cs:["Euro / Norwegian Krone"]},e.exports["#GBPPLN-symbol-description"]={en:["British Pound / Polish Zloty"],cs:["British Pound / Polish Zloty"]},e.exports["#MOEX:BR1!-symbol-description"]={en:["Brent Oil Futures"],cs:["Brent Oil Futures"]},e.exports["#NYMEX:KG1!-symbol-description"]={en:["Cotton Futures"],cs:["Cotton Futures"]},e.exports["#NYMEX:PL1!-symbol-description"]={en:["Platinum Futures"],cs:["Platinum Futures"]},e.exports["#SOYBNUSD-symbol-description"]={en:["CFDs on Soybeans"],cs:["CFDs on Soybeans"]},e.exports["#SUGARUSD-symbol-description"]={en:["CFDs on Sugar"],cs:["CFDs on Sugar"]},e.exports["#TVC:IXIC-symbol-description"]={en:["US Composite Index"],cs:["NASDAQ Composite Index"]},e.exports["#TVC:RU-symbol-description"]={en:["Russell 1000 Index"],cs:["Russell 1000 Index"]},e.exports["#USDZAR-symbol-description"]={en:["U.S. Dollar / South African Rand"],cs:["U.S. Dollar / South African Rand"]},e.exports["#WHEATUSD-symbol-description"]={en:["CFDs on Wheat"],cs:["CFDs on Wheat"]},e.exports["#XRPEUR-symbol-description"]={en:["XRP / Euro"],cs:["Ripple / Euro"]},e.exports["#CBOT:S1!-symbol-description"]={en:["Soybean Futures"],cs:["Soybean Futures"]},e.exports["#SP:MID-symbol-description"]={en:["S&P 400 Index"],cs:["S&P 400 Index"]},e.exports["#TSX:XCUUSD-symbol-description"]={en:["CFDs on Copper"],cs:["CFDs on Copper"]},e.exports["#TVC:NYA-symbol-description"]={en:["NYSE Composite Index"],cs:["NYSE Composite Index"]},e.exports["#TVC:PLATINUM-symbol-description"]={en:["CFDs on Platinum (US$ / OZ)"],cs:["CFDs on Platinum (US$ / OZ)"]},e.exports["#TVC:SSMI-symbol-description"]={en:["Swiss Market Index"],cs:["Swiss Market Index"]},e.exports["#TVC:SXY-symbol-description"]={en:["Swiss Franc Currency Index"],cs:["Swiss Franc Currency Index"]},e.exports["#MOEX:RI1!-symbol-description"]={en:["RTS Index Futures"],cs:["RTS Index Futures"]},e.exports["#MOEX:MX1!-symbol-description"]={en:["MICEX Index Futures"],cs:["MICEX Index Futures"]},e.exports["#CBOE:BG1!-symbol-description"]={en:["Bitcoin CBOE Futures"],cs:["Bitcoin CBOE Futures"]},e.exports["#TVC:MY10-symbol-description"]={en:["Malaysia Government Bonds 10 YR"],cs:["Malaysia Government Bonds 10 YR"]},e.exports["#CME:S61!-symbol-description"]={en:["Swiss Franc Futures"],
cs:["Swiss Franc Futures"]},e.exports["#TVC:DEU30-symbol-description"]={en:["DAX Index"],cs:["DAX Index"]},e.exports["#BCHEUR-symbol-description"]={en:["Bitcoin Cash / Euro"],cs:["Bitcoin Cash / Euro"]},e.exports["#TVC:ZXY-symbol-description"]={en:["New Zealand Dollar Currency Index"],cs:["New Zealand Dollar Currency Index"]},e.exports["#MIL:FTSEMIB-symbol-description"]={en:["FTSE MIB Index"],cs:["FTSE MIB Index"]},e.exports["#XETR:DAX-symbol-description"]={en:["DAX Index"],cs:["DAX Index"]},e.exports["#MOEX:IMOEX-symbol-description"]={en:["MOEX Russia Index"],cs:["MOEX Russia Index"]},e.exports["#FX:US30-symbol-description"]={en:["Dow Jones Industrial Average Index"],cs:["Dow Jones Industrial Average Index"]},e.exports["#MOEX:RUAL-symbol-description"]={en:["United Company RUSAL PLC"],cs:["United Company RUSAL PLC"]},e.exports["#MOEX:MX2!-symbol-description"]={en:["MICEX Index Futures"],cs:["MICEX Index Futures"]},e.exports["#NEOUSD-symbol-description"]={en:["NEO / U.S. Dollar"],cs:["NEO / U.S. Dollar"]},e.exports["#XMRUSD-symbol-description"]={en:["Monero / U.S. Dollar"],cs:["Monero / U.S. Dollar"]},e.exports["#ZECUSD-symbol-description"]={en:["Zcash / U.S. Dollar"],cs:["Zcash / U.S. Dollar"]},e.exports["#TVC:CAC-symbol-description"]={en:["CAC 40 Index"],cs:["CAC 40 Index"]},e.exports["#NASDAQ:ZS-symbol-description"]={en:["Zscaler Inc"],cs:["Zscaler Inc"]},e.exports["#TVC:GB10Y-symbol-description"]={en:["UK Government Bonds 10 YR Yield"],cs:["UK Government Bonds 10 YR Yield"]},e.exports["#TVC:AU10Y-symbol-description"]={en:["Australia Government Bonds 10 YR Yield"],cs:["Australia Government Bonds 10 YR Yield"]},e.exports["#TVC:CN10Y-symbol-description"]={en:["China Government Bonds 10 YR Yield"],cs:["China Government Bonds 10 YR Yield"]},e.exports["#TVC:DE10Y-symbol-description"]={en:["German Government Bonds 10 YR Yield"],cs:["German Government Bonds 10 YR Yield"]},e.exports["#TVC:ES10Y-symbol-description"]={en:["Spain Government Bonds 10 YR Yield"],cs:["Spain Government Bonds 10 YR Yield"]},e.exports["#TVC:FR10Y-symbol-description"]={en:["France Government Bonds 10 YR Yield"],cs:["France Government Bonds 10 YR Yield"]},e.exports["#TVC:IN10Y-symbol-description"]={en:["India Government Bonds 10 YR Yield"],cs:["India Government Bonds 10 yr"]},e.exports["#TVC:IT10Y-symbol-description"]={en:["Italy Government Bonds 10 YR Yield"],cs:["Italy Government Bonds 10 yr"]},e.exports["#TVC:JP10Y-symbol-description"]={en:["Japan Government Bonds 10 YR Yield"],cs:["Japan Government Bonds 10 yr"]},e.exports["#TVC:KR10Y-symbol-description"]={en:["Korea Government Bonds 10 YR Yield"],cs:["Korea Government Bonds 10 YR Yield"]},e.exports["#TVC:MY10Y-symbol-description"]={en:["Malaysia Government Bonds 10 YR Yield"],cs:["Malaysia Government Bonds 10 YR Yield"]},e.exports["#TVC:PT10Y-symbol-description"]={en:["Portugal Government Bonds 10 YR Yield"],cs:["Portugal Government Bonds 10 YR Yield"]},e.exports["#TVC:TR10Y-symbol-description"]={en:["Turkey Government Bonds 10 YR Yield"],cs:["Turkey Government Bonds 10 YR"]},
e.exports["#TVC:US02Y-symbol-description"]={en:["US Government Bonds 2 YR Yield"],cs:["US Government Bonds 2 yr"]},e.exports["#TVC:US05Y-symbol-description"]={en:["US Government Bonds 5 YR Yield"],cs:["US Government Bonds 5 yr"]},e.exports["#TVC:US10Y-symbol-description"]={en:["US Government Bonds 10 YR Yield"],cs:["US Government Bonds 10 yr"]},e.exports["#INDEX:TWII-symbol-description"]={en:["Taiwan Weighted Index"],cs:["Taiwan Weighted Index"]},e.exports["#CME:J61!-symbol-description"]={en:["Japanese Yen Futures"],cs:["Japanese Yen Futures"]},e.exports["#CME_MINI:J71!-symbol-description"]={en:["Japanese Yen E-mini Futures"],cs:["Japanese Yen E-mini Futures"]},e.exports["#CME_MINI:WM1!-symbol-description"]={en:["E-micro Japanese Yen / U.S. Dollar Futures"],cs:["E-micro Japanese Yen / U.S. Dollar Futures"]},e.exports["#CME:M61!-symbol-description"]={en:["Mexican Peso Futures"],cs:["Mexican Peso Futures"]},e.exports["#CME:T61!-symbol-description"]={en:["South African Rand Futures"],cs:["South African Rand Futures"]},e.exports["#CME:SK1!-symbol-description"]={en:["Swedish Krona Futures"],cs:["Swedish Krona Futures"]},e.exports["#CME:QT1!-symbol-description"]={en:["Chinese Renminbi / U.S. Dollar Futures"],cs:["Chinese Renminbi / U.S. Dollar Futures"]},e.exports["#COMEX:AUP1!-symbol-description"]={en:["Aluminum MW U.S. Transaction Premium Platts (25MT) Futures"],cs:["Aluminum MW U.S. Transaction Premium Platts (25MT) Futures"]},e.exports["#CME:L61!-symbol-description"]={en:["Brazilian Real Futures"],cs:["Brazilian Real Futures"]},e.exports["#CME:WP1!-symbol-description"]={en:["Polish Zloty Futures"],cs:["Polish Zloty Futures"]},e.exports["#CME:N61!-symbol-description"]={en:["New Zealand Dollar Futures"],cs:["New Zealand Dollar Futures"]},e.exports["#CME_MINI:MG1!-symbol-description"]={en:["E-micro Australian Dollar / U.S. Dollar Futures"],cs:["E-micro Australian Dollar / U.S. Dollar Futures"]},e.exports["#CME_MINI:WN1!-symbol-description"]={en:["E-micro Swiss Franc / U.S. Dollar Futures"],cs:["E-micro Swiss Franc / U.S. Dollar Futures"]},e.exports["#CME_MINI:MF1!-symbol-description"]={en:["E-micro Euro / U.S. Dollar Futures"],cs:["E-micro Euro / U.S. Dollar Futures"]},e.exports["#CME_MINI:E71!-symbol-description"]={en:["Euro E-mini Futures"],cs:["Euro E-mini Futures"]},e.exports["#CBOT:ZK1!-symbol-description"]={en:["Denatured Fuel Ethanol Futures"],cs:["Denatured Fuel Ethanol Futures"]},e.exports["#CME_MINI:MB1!-symbol-description"]={en:["E-micro British Pound / U.S. Dollar Futures"],cs:["E-micro British Pound / U.S. Dollar Futures"]},e.exports["#NYMEX_MINI:QU1!-symbol-description"]={en:["E-mini Gasoline Futures"],cs:["E-mini Gasoline Futures"]},e.exports["#NYMEX_MINI:QX1!-symbol-description"]={en:["E-mini Heating Oil Futures"],cs:["E-mini Heating Oil Futures"]},e.exports["#COMEX_MINI:QC1!-symbol-description"]={en:["E-mini Copper Futures"],cs:["E-mini Copper Futures"]},e.exports["#NYMEX_MINI:QG1!-symbol-description"]={en:["E-mini Natural Gas Futures"],cs:["E-mini Natural Gas Futures"]},
e.exports["#CME:E41!-symbol-description"]={en:["U.S. Dollar / Turkish Lira Futures"],cs:["U.S. Dollar / Turkish Lira Futures"]},e.exports["#COMEX_MINI:QI1!-symbol-description"]={en:["Silver (Mini) Futures"],cs:["Silver (Mini) Futures"]},e.exports["#CME:DL1!-symbol-description"]={en:["Milk, Class III Futures"],cs:["Milk, Class III Futures"]},e.exports["#NYMEX:UX1!-symbol-description"]={en:["Uranium Futures"],cs:["Uranium Futures"]},e.exports["#CBOT:BO1!-symbol-description"]={en:["Soybean Oil Futures"],cs:["Soybean Oil Futures"]},e.exports["#CME:HE1!-symbol-description"]={en:["Lean Hogs Futures"],cs:["Lean Hogs Futures"]},e.exports["#NYMEX:IAC1!-symbol-description"]={en:["Newcastle Coal Futures"],cs:["Newcastle Coal Futures"]},e.exports["#NYMEX_MINI:QM1!-symbol-description"]={en:["E-mini Light Crude Oil Futures"],cs:["E-mini Light Crude Oil Futures"]},e.exports["#NYMEX:JMJ1!-symbol-description"]={en:["Mini Brent Financial Futures"],cs:["Mini Brent Financial Futures"]},e.exports["#COMEX:AEP1!-symbol-description"]={en:["Aluminium European Premium Futures"],cs:["Aluminium European Premium Futures"]},e.exports["#CBOT:ZQ1!-symbol-description"]={en:["30 Day Federal Funds Interest Rate Futures"],cs:["30 Day Federal Funds Interest Rate Futures"]},e.exports["#CME:LE1!-symbol-description"]={en:["Live Cattle Futures"],cs:["Live Cattle Futures"]},e.exports["#CME:UP1!-symbol-description"]={en:["Swiss Franc / Japanese Yen Futures"],cs:["Swiss Franc / Japanese Yen Futures"]},e.exports["#CBOT:ZN1!-symbol-description"]={en:["10 Year T-Note Futures"],cs:["10 Year T-Note Futures"]},e.exports["#CBOT:ZB1!-symbol-description"]={en:["T-Bond Futures"],cs:["T-Bond Futures"]},e.exports["#CME:GF1!-symbol-description"]={en:["Feeder Cattle Futures"],cs:["Feeder Cattle Futures"]},e.exports["#CBOT:UD1!-symbol-description"]={en:["Ultra T-Bond Futures"],cs:["Ultra T-Bond Futures"]},e.exports["#CME:I91!-symbol-description"]={en:["CME Housing Futures — Washington DC"],cs:["CME Housing Futures — Washington DC"]},e.exports["#CBOT:ZO1!-symbol-description"]={en:["Oat Futures"],cs:["Oat Futures"]},e.exports["#CBOT:ZM1!-symbol-description"]={en:["Soybean Meal Futures"],cs:["Soybean Meal Futures"]},e.exports["#CBOT_MINI:XN1!-symbol-description"]={en:["Corn Mini Futures"],cs:["Corn Mini Futures"]},e.exports["#CBOT:ZC1!-symbol-description"]={en:["Corn Futures"],cs:["Corn Futures"]},e.exports["#CME:LS1!-symbol-description"]={en:["Lumber Futures"],cs:["Lumber Futures"]},e.exports["#CBOT_MINI:XW1!-symbol-description"]={en:["Wheat Mini Futures"],cs:["Wheat Mini Futures"]},e.exports["#CBOT_MINI:XK1!-symbol-description"]={en:["Soybean Mini Futures"],cs:["Soybean Mini Futures"]},e.exports["#CBOT:ZS1!-symbol-description"]={en:["Soybean Futures"],cs:["Soybean Futures"]},e.exports["#NYMEX:PA1!-symbol-description"]={en:["Palladium Futures"],cs:["Palladium Futures"]},e.exports["#CME:FTU1!-symbol-description"]={en:["E-mini FTSE 100 Index USD Futures"],cs:["E-mini FTSE 100 Index USD Futures"]},e.exports["#CBOT:ZR1!-symbol-description"]={en:["Rice Futures"],cs:["Rice Futures"]
},e.exports["#COMEX_MINI:GR1!-symbol-description"]={en:["Gold (E-micro) Futures"],cs:["Gold (E-micro) Futures"]},e.exports["#COMEX_MINI:QO1!-symbol-description"]={en:["Gold (Mini) Futures"],cs:["Gold (Mini) Futures"]},e.exports["#CME_MINI:RL1!-symbol-description"]={en:["E-mini Russell 1000 Futures"],cs:["E-mini Russell 1000 Futures"]},e.exports["#CME_MINI:EW1!-symbol-description"]={en:["S&P 400 Midcap E-mini Futures"],cs:["S&P 400 Midcap E-mini Futures"]},e.exports["#COMEX:LD1!-symbol-description"]={en:["Lead Futures"],cs:["Lead Futures"]},e.exports["#CME_MINI:ES1!-symbol-description"]={en:["S&P 500 E-mini Futures"],cs:["S&P 500 E-mini Futures"]},e.exports["#TVC:SA40-symbol-description"]={en:["South Africa Top 40 Index"],cs:["South Africa Top 40 Index"]},e.exports["#BMV:ME-symbol-description"]={en:["S&P/BMV IPC Index"],cs:["IPC Mexico Index"]},e.exports["#BCBA:IMV-symbol-description"]={en:["S&P MERVAL Index"],cs:["MERVAL Index"]},e.exports["#HSI:HSI-symbol-description"]={en:["Hang Seng Index"],cs:["Hang Seng Index"]},e.exports["#BVL:SPBLPGPT-symbol-description"]={en:["S&P / BVL Peru General Index (PEN)"],cs:["S&P / BVL Peru General Index (PEN)"]},e.exports["#EGX:EGX30-symbol-description"]={en:["EGX 30 Index"],cs:["EGX 30 Price Return Index"]},e.exports["#BVC:IGBC-symbol-description"]={en:["Indice General de la Bolsa de Valores de Colombia"],cs:["Indice General de la Bolsa de Valores de Colombia"]},e.exports["#TWSE:TAIEX-symbol-description"]={en:["Taiwan Capitalization Weighted Stock Index"],cs:["Taiwan Capitalization Weighted Stock Index"]},e.exports["#QSE:GNRI-symbol-description"]={en:["QE Index"],cs:["QE Index"]},e.exports["#BME:IBC-symbol-description"]={en:["IBEX 35 Index"],cs:["IBEX 35 Index"]},e.exports["#NZX:NZ50G-symbol-description"]={en:["S&P / NZX 50 Index Gross"],cs:["S&P / NZX 50 Index Gross"]},e.exports["#SIX:SMI-symbol-description"]={en:["Swiss Market Index"],cs:["Swiss Market Index"]},e.exports["#SZSE:399001-symbol-description"]={en:["Shenzhen Component Index"],cs:["SZSE Component Index"]},e.exports["#TADAWUL:TASI-symbol-description"]={en:["Tadawul All Shares Index"],cs:["Tadawul All Shares Index"]},e.exports["#IDX:COMPOSITE-symbol-description"]={en:["IDX Composite Index"],cs:["IDX Composite Index"]},e.exports["#EURONEXT:PX1-symbol-description"]={en:["CAC 40 Index"],cs:["CAC 40 Index"]},e.exports["#OMXHEX:OMXH25-symbol-description"]={en:["OMX Helsinki 25 Index"],cs:["OMX Helsinki 25 Index"]},e.exports["#EURONEXT:BEL20-symbol-description"]={en:["BEL 20 Index"],cs:["BEL 20 Index"]},e.exports["#TVC:STI-symbol-description"]={en:["Straits Times Index"],cs:["Straits Times Index"]},e.exports["#DFM:DFMGI-symbol-description"]={en:["DFM Index"],cs:["DFM Index"]},e.exports["#TVC:KOSPI-symbol-description"]={en:["Korea Composite Stock Price Index"],cs:["Korea Composite Stock Price Index"]},e.exports["#FTSEMYX:FBMKLCI-symbol-description"]={en:["FTSE Bursa Malaysia KLCI Index"],cs:["FTSE Bursa Malaysia KLCI Index"]},e.exports["#TASE:TA35-symbol-description"]={en:["TA-35 Index"],cs:["TA-35 Index"]},
e.exports["#OMXSTO:OMXS30-symbol-description"]={en:["OMX Stockholm 30 Index"],cs:["OMX Stockholm 30 Index"]},e.exports["#OMXICE:OMXI8-symbol-description"]={en:["OMX Iceland 8 Index"],cs:["OMX Iceland 8 Index"]},e.exports["#NSENG:NSE30-symbol-description"]={en:["NSE 30 Index"],cs:["NSE 30 Index"]},e.exports["#BAHRAIN:BSEX-symbol-description"]={en:["Bahrain All Share Index"],cs:["Bahrain All Share Index"]},e.exports["#OMXTSE:OMXTGI-symbol-description"]={en:["OMX Tallinn Gross Index"],cs:["OMX Tallinn GI"]},e.exports["#OMXCOP:OMXC25-symbol-description"]={en:["OMX Copenhagen 25 Index"],cs:["OMX Copenhagen 25 Index"]},e.exports["#OMXRSE:OMXRGI-symbol-description"]={en:["OMX Riga Gross Index"],cs:["OMX Riga GI"]},e.exports["#BELEX:BELEX15-symbol-description"]={en:["BELEX 15 Index"],cs:["BELEX 15 Index"]},e.exports["#OMXVSE:OMXVGI-symbol-description"]={en:["OMX Vilnius Gross Index"],cs:["OMX Vilnius GI"]},e.exports["#EURONEXT:AEX-symbol-description"]={en:["AEX Index"],cs:["AEX Index"]},e.exports["#CBOE:VIX-symbol-description"]={en:["Volatility S&P 500 Index"],cs:["Volatility S&P 500 Index"]},e.exports["#NASDAQ:XAU-symbol-description"]={en:["PHLX Gold and Silver Sector Index"],cs:["PHLX Gold and Silver Sector Index"]},e.exports["#DJ:DJUSCL-symbol-description"]={en:["Dow Jones U.S. Coal Index"],cs:["Dow Jones U.S. Coal Index"]},e.exports["#DJ:DJCIKC-symbol-description"]={en:["Dow Jones Commodity Index Coffee"],cs:["Dow Jones Commodity Index Coffee"]},e.exports["#DJ:DJCIEN-symbol-description"]={en:["Dow Jones Commodity Index Energy"],cs:["Dow Jones Commodity Index Energy"]},e.exports["#NASDAQ:OSX-symbol-description"]={en:["PHLX Oil Service Sector Index"],cs:["PHLX Oil Service Sector Index"]},e.exports["#DJ:DJCISB-symbol-description"]={en:["Dow Jones Commodity Index Sugar"],cs:["Dow Jones Commodity Index Sugar"]},e.exports["#DJ:DJCICC-symbol-description"]={en:["Dow Jones Commodity Index Cocoa"],cs:["Dow Jones Commodity Index Cocoa"]},e.exports["#DJ:DJCIGR-symbol-description"]={en:["Dow Jones Commodity Index Grains"],cs:["Dow Jones Commodity Index Grains"]},e.exports["#DJ:DJCIAGC-symbol-description"]={en:["Dow Jones Commodity Index Agriculture Capped Component"],cs:["Dow Jones Commodity Index Agriculture Capped Component"]},e.exports["#DJ:DJCISI-symbol-description"]={en:["Dow Jones Commodity Index Silver"],cs:["Dow Jones Commodity Index Silver"]},e.exports["#DJ:DJCIIK-symbol-description"]={en:["Dow Jones Commodity Index Nickel"],cs:["Dow Jones Commodity Index Nickel"]},e.exports["#NASDAQ:HGX-symbol-description"]={en:["PHLX Housing Sector Index"],cs:["PHLX Housing Sector Index"]},e.exports["#DJ:DJCIGC-symbol-description"]={en:["Dow Jones Commodity Index Gold"],cs:["Dow Jones Commodity Index Gold"]},e.exports["#SP:SPGSCI-symbol-description"]={en:["S&P Goldman Sachs Commodity Index"],cs:["S&P Goldman Sachs Commodity Index"]},e.exports["#NASDAQ:UTY-symbol-description"]={en:["PHLX Utility Sector Index"],cs:["PHLX Utility Sector Index"]},e.exports["#DJ:DJU-symbol-description"]={en:["Dow Jones Utility Average Index"],
cs:["Dow Jones Utility Average Index"]},e.exports["#SP:SVX-symbol-description"]={en:["S&P 500 Value Index"],cs:["S&P 500 Value Index"]},e.exports["#SP:OEX-symbol-description"]={en:["S&P 100 Index"],cs:["S&P 100 Index"]},e.exports["#CBOE:OEX-symbol-description"]={en:["S&P 100 Index"],cs:["S&P 100 Index"]},e.exports["#NASDAQ:SOX-symbol-description"]={en:["Philadelphia Semiconductor Index"],cs:["Philadelphia Semiconductor Index"]},e.exports["#RUSSELL:RUI-symbol-description"]={en:["Russell 1000 Index"],cs:["Russell 1000 Index"]},e.exports["#RUSSELL:RUA-symbol-description"]={en:["Russell 3000 Index"],cs:["Russell 3000 Index"]},e.exports["#RUSSELL:RUT-symbol-description"]={en:["Russell 2000 Index"],cs:["Russell 2000 Index"]},e.exports["#NYSE:XMI-symbol-description"]={en:["NYSE ARCA Major Market Index"],cs:["NYSE ARCA Major Market Index"]},e.exports["#NYSE:XAX-symbol-description"]={en:["AMEX Composite Index"],cs:["AMEX Composite Index"]},e.exports["#NASDAQ:NDX-symbol-description"]={en:["Nasdaq 100 Index"],cs:["Nasdaq 100 Index"]},e.exports["#NASDAQ:IXIC-symbol-description"]={en:["Nasdaq Composite Index"],cs:["Nasdaq Composite Index"]},e.exports["#DJ:DJT-symbol-description"]={en:["Dow Jones Transportation Average Index"],cs:["Dow Jones Transportation Average Index"]},e.exports["#NYSE:NYA-symbol-description"]={en:["NYSE Composite Index"],cs:["NYSE Composite Index"]},e.exports["#NYMEX:CJ1!-symbol-description"]={en:["Cocoa Futures"],cs:["Cocoa Futures"]},e.exports["#USDILS-symbol-description"]={en:["U.S. Dollar / Israeli Shekel"],cs:["U.S. Dollar / Israeli Shekel"]},e.exports["#TSXV:F-symbol-description"]={en:["Fiore Gold Inc"],cs:["Fiore Gold Inc"]},e.exports["#SIX:F-symbol-description"]={en:["Ford Motor Company"],cs:["Ford Motor Company"]},e.exports["#BMV:F-symbol-description"]={en:["Ford Motor Company"],cs:["Ford Motor Company"]},e.exports["#TWII-symbol-description"]={en:["Taiwan Weighted Index"],cs:["Taiwan Weighted Index"]},e.exports["#TVC:PL10Y-symbol-description"]={en:["Poland Government Bonds 10 YR Yield"],cs:["Poland Government Bonds 10 YR Yield"]},e.exports["#TVC:PL05Y-symbol-description"]={en:["Poland Government Bonds 5 YR Yield"],cs:["Poland Government Bonds 5 YR Yield"]},e.exports["#SET:GC-symbol-description"]={en:["Global Connections Public Company"],cs:["Global Connections Public Company"]},e.exports["#TSX:GC-symbol-description"]={en:["Great Canadian Gaming Corporation"],cs:["Great Canadian Gaming Corporation"]},e.exports["#TVC:FTMIB-symbol-description"]={en:["Milano Italia Borsa Index"],cs:["Milano Italia Borsa Index"]},e.exports["#OANDA:SPX500USD-symbol-description"]={en:["S&P 500 Index"],cs:["S&P 500 Index"]},e.exports["#BMV:CT-symbol-description"]={en:["China SX20 RT"],cs:["China SX20 RT"]},e.exports["#TSXV:CT-symbol-description"]={en:["Centenera Mining Corporation"],cs:["Centenera Mining Corporation"]},e.exports["#BYBIT:ETHUSD-symbol-description"]={en:["ETHUSD Perpetual Contract"],cs:["ETHUSD Perpetual Contract"]},e.exports["#BYBIT:XRPUSD-symbol-description"]={en:["XRPUSD Perpetual Contract"],
cs:["XRPUSD Perpetual Contract"]},e.exports["#BYBIT:BTCUSD-symbol-description"]={en:["BTCUSD Perpetual Contract"],cs:["BTCUSD Perpetual Contract"]},e.exports["#BITMEX:ETHUSD-symbol-description"]={en:["ETHUSD Perpetual Futures Contract"],cs:["ETHUSD Perpetual Futures Contract"]},e.exports["#DERIBIT:BTCUSD-symbol-description"]={en:["BTCUSD Perpetual Futures Contract"],cs:["BTCUSD Perpetual Futures Contract"]},e.exports["#DERIBIT:ETHUSD-symbol-description"]={en:["ETHUSD Perpetual Futures Contract"],cs:["ETHUSD Perpetual Futures Contract"]},e.exports["#USDHUF-symbol-description"]={en:["U.S. Dollar / Hungarian Forint"],cs:["U.S. Dollar / Hungarian Forint"]},e.exports["#USDTHB-symbol-description"]={en:["U.S. Dollar / Thai Baht"],cs:["U.S. Dollar / Thai Baht"]},e.exports["#FOREXCOM:US2000-symbol-description"]={en:["US Small Cap 2000"],cs:["US Small Cap 2000"]},e.exports["#TSXV:PBR-symbol-description"]={en:["Para Resources Inc"],cs:["Para Resources Inc"]},e.exports["#NYSE:SI-symbol-description"]={en:["Silvergate Capital Corporation"],cs:["Silvergate Capital Corporation"]},e.exports["#NASDAQ:LE-symbol-description"]={en:["Lands' End Inc"],cs:["Lands' End Inc"]},e.exports["#CME:CB1!-symbol-description"]={en:["Butter Futures-Cash (Continuous: Current contract in front)"],cs:["Butter Futures-Cash (Continuous: Current contract in front)"]},e.exports["#LSE:SCHO-symbol-description"]={en:["Scholium Group Plc Ord 1P"],cs:["Scholium Group Plc Ord 1P"]},e.exports["#NEO:HE-symbol-description"]={en:["Hanwei Energy Services Corp."],cs:["Hanwei Energy Services Corp."]},e.exports["#NYSE:HE-symbol-description"]={en:["Hawaiian Electric Industries"],cs:["Hawaiian Electric Industries"]},e.exports["#OMXCOP:SCHO-symbol-description"]={en:["Schouw & Co A/S"],cs:["Schouw & Co A/S"]},e.exports["#TSX:HE-symbol-description"]={en:["Hanwei Energy Services Corp."],cs:["Hanwei Energy Services Corp."]},e.exports["#BSE:ITI-symbol-description"]={en:["ITI Ltd"],cs:["ITI Ltd"]},e.exports["#NSE:ITI-symbol-description"]={en:["Indian Telephone Industries Limited"],cs:["Indian Telephone Industries Limited"]},e.exports["#TSX:LS-symbol-description"]={en:["Middlefield Healthcare & Life Sciences Dividend Fund"],cs:["Middlefield Healthcare & Life Sciences Dividend Fund"]},e.exports["#BITMEX:XBT-symbol-description"]={en:["Bitcoin / U.S. Dollar Index"],cs:["Bitcoin / U.S. Dollar Index"]},e.exports["#CME_MINI:RTY1!-symbol-description"]={en:["E-Mini Russell 2000 Index Futures"],cs:["E-Mini Russell 2000 Index Futures"]},e.exports["#CRYPTOCAP:TOTAL-symbol-description"]={en:["Crypto Total Market Cap, $"],cs:["Crypto Total Market Cap, $"]},e.exports["#ICEUS:DX1!-symbol-description"]={en:["U.S. Dollar Index Futures"],cs:["U.S. Dollar Index Futures"]},e.exports["#NYMEX:TT1!-symbol-description"]={en:["Cotton Futures"],cs:["Cotton Futures"]},e.exports["#PHEMEX:BTCUSD-symbol-description"]={en:["BTC Perpetual Futures Contract"],cs:["BTC Perpetual Futures Contract"]},e.exports["#PHEMEX:ETHUSD-symbol-description"]={en:["ETH Perpetual Futures Contract"],
cs:["ETH Perpetual Futures Contract"]},e.exports["#PHEMEX:XRPUSD-symbol-description"]={en:["XRP Perpetual Futures Contract"],cs:["XRP Perpetual Futures Contract"]},e.exports["#PHEMEX:LTCUSD-symbol-description"]={en:["LTC Perpetual Futures Contract"],cs:["LTC Perpetual Futures Contract"]},e.exports["#BITCOKE:BCHUSD-symbol-description"]={en:["BCH Quanto Swap"],cs:["BCH Quanto Swap"]},e.exports["#BITCOKE:BTCUSD-symbol-description"]={en:["BTC Quanto Swap"],cs:["BTC Quanto Swap"]},e.exports["#BITCOKE:ETHUSD-symbol-description"]={en:["ETH Quanto Swap"],cs:["ETH Quanto Swap"]},e.exports["#BITCOKE:LTCUSD-symbol-description"]={en:["LTC Quanto Swap"],cs:["LTC Quanto Swap"]},e.exports["#TVC:CA10-symbol-description"]={en:["Canadian Government Bonds, 10 YR"],cs:["Canadian Government Bonds, 10 YR"]},e.exports["#TVC:CA10Y-symbol-description"]={en:["Canadian Government Bonds 10 YR Yield"],cs:["Canadian Government Bonds 10 YR Yield"]},e.exports["#TVC:ID10Y-symbol-description"]={en:["Indonesia Government Bonds 10 YR Yield"],cs:["Indonesia Government Bonds 10 YR Yield"]},e.exports["#TVC:NL10-symbol-description"]={en:["Netherlands Government Bonds, 10 YR"],cs:["Netherlands Government Bonds, 10 YR"]},e.exports["#TVC:NL10Y-symbol-description"]={en:["Netherlands Government Bonds 10 YR Yield"],cs:["Netherlands Government Bonds 10 YR Yield"]},e.exports["#TVC:NZ10-symbol-description"]={en:["New Zealand Government Bonds, 10 YR"],cs:["New Zealand Government Bonds, 10 YR"]},e.exports["#TVC:NZ10Y-symbol-description"]={en:["New Zealand Government Bonds 10 YR Yield"],cs:["New Zealand Government Bonds 10 YR Yield"]},e.exports["#SOLUSD-symbol-description"]={en:["Solana / U.S. Dollar"],cs:["Solana / U.S. Dollar"]},e.exports["#LUNAUSD-symbol-description"]={en:["Luna / U.S. Dollar"],cs:["Luna / U.S. Dollar"]},e.exports["#UNIUSD-symbol-description"]={en:["Uniswap / U.S. Dollar"],cs:["Uniswap / U.S. Dollar"]},e.exports["#LTCBRL-symbol-description"]={en:["Litecoin / Brazilian Real"],cs:["Litecoin / Brazilian Real"]},e.exports["#ETCEUR-symbol-description"]={en:["Ethereum Classic / Euro"],cs:["Ethereum Classic / Euro"]},e.exports["#ETHKRW-symbol-description"]={en:["Ethereum / South Korean Won"],cs:["Ethereum / South Korean Won"]},e.exports["#BTCRUB-symbol-description"]={en:["Bitcoin / Russian Ruble"],cs:["Bitcoin / Russian Ruble"]},e.exports["#BTCTHB-symbol-description"]={en:["Bitcoin / Thai Baht"],cs:["Bitcoin / Thai Baht"]},e.exports["#ETHTHB-symbol-description"]={en:["Ethereum / Thai Baht"],cs:["Ethereum / Thai Baht"]},e.exports["#TVC:EU10YY-symbol-description"]={en:["Euro Government Bonds 10 YR Yield"],cs:["Euro Government Bonds 10 YR Yield"]},e.exports["#NASDAQ:LCID-symbol-description"]={en:["Lucid Group, Inc."],cs:["Lucid Group, Inc."]},e.exports["#TADAWUL:2370-symbol-description"]={en:["Middle East Specialized Cables Co."],cs:["Middle East Specialized Cables Co."]},e.exports["#NASDAQ:GOOG-symbol-description"]={en:["#NASDAQ:GOOG-symbol-description"],cs:["#NASDAQ:GOOG-symbol-description"]},e.exports["#NASDAQ:GOOGL-symbol-description"]={
en:["#NASDAQ:GOOGL-symbol-description"],cs:["#NASDAQ:GOOGL-symbol-description"]}}}]);