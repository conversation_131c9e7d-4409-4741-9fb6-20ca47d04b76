(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[6822],{94920:s=>{s.exports={en:["ADJ"],cs:["adj"]}},16755:s=>{s.exports={en:["B-ADJ"],cs:["B-ADJ"]}},82631:s=>{s.exports={en:["SET"],cs:["SET"]}},22233:s=>{s.exports={en:["auto"],cs:["automatické"]}},4161:s=>{s.exports={en:["log"],cs:["log"]}},58221:s=>{s.exports={en:["All data"],cs:["All data"]}},19273:s=>{s.exports={en:["Year to day"],cs:["Year to day"]}},58426:s=>{s.exports={en:["{timePeriod} in {timeInterval}"],cs:["{timePeriod} in {timeInterval}"]}},93020:s=>{s.exports={en:["Adjust data for dividends"],cs:["Adjust data for dividends"]}},68921:s=>{s.exports={en:["Adjust for contract changes"],cs:["Adjust for contract changes"]}},42432:s=>{s.exports={en:["Go to"],cs:["Go to"]}},92966:s=>{s.exports={en:["Extended Hours is available only for intraday charts"],cs:["Extended Hours is available only for intraday charts"]}},61206:s=>{s.exports={en:["Maximize chart"],cs:["Maximize chart"]}},2031:s=>{s.exports={en:["Main symbol data is adjusted for dividends only"],cs:["Main symbol data is adjusted for dividends only"]}},95739:s=>{s.exports={en:["Main symbol data is adjusted for splits only"],cs:["Main symbol data is adjusted for splits only"]}},27665:s=>{s.exports={en:["Sessions"],cs:["Sessions"]}},31142:s=>{s.exports={en:["Restore chart"],cs:["Restore chart"]}},41888:s=>{s.exports={en:["Toggle Auto Scale"],cs:["Přepnout na Auto Stupnici"]}},1e4:s=>{s.exports={en:["Toggle Log Scale"],cs:["Přepnout Log Měřítko"]}},81649:s=>{s.exports={en:["Toggle Percentage"],cs:["Přepnout na Procenta"]}},77073:s=>{s.exports={en:["Timezone"],cs:["Časové pásmo"]}},49545:s=>{s.exports={en:["Use settlement as close on daily interval"],cs:["Use settlement as close on daily interval"]}},8586:s=>{s.exports={en:["ext"],cs:["ext"]}},63808:s=>{s.exports={en:["{str} day","{str} days"],cs:["{str} day","{str} days"]}},62368:s=>{s.exports={en:["{str} day","{str} days"],cs:["{str} day","{str} days"]}},561:s=>{s.exports={en:["{str} day intervals","{str} days intervals"],cs:["{str} day intervals","{str} days intervals"]}},72495:s=>{s.exports={en:["{str} hour","{str} hours"],cs:["{str} hour","{str} hours"]}},64963:s=>{s.exports={en:["{str} hour","{str} hours"],cs:["{str} hour","{str} hours"]}},14887:s=>{s.exports={en:["{str} hour intervals","{str} hours intervals"],cs:["{str} hour intervals","{str} hours intervals"]}},12752:s=>{s.exports={en:["{str} month","{str} months"],cs:["{str} month","{str} months"]}},20062:s=>{s.exports={en:["{str} month","{str} months"],cs:["{str} month","{str} months"]}},48514:s=>{s.exports={en:["{str} month intervals","{str} months intervals"],cs:["{str} month intervals","{str} months intervals"]}},95484:s=>{s.exports={en:["{str} minute","{str} minutes"],cs:["{str} minute","{str} minutes"]}},5926:s=>{s.exports={en:["{str} minute","{str} minutes"],cs:["{str} minute","{str} minutes"]}},15489:s=>{s.exports={en:["{str} minute intervals","{str} minutes intervals"],cs:["{str} minute intervals","{str} minutes intervals"]}},6088:s=>{s.exports={
en:["{str} week","{str} weeks"],cs:["{str} week","{str} weeks"]}},49306:s=>{s.exports={en:["{str} week","{str} weeks"],cs:["{str} week","{str} weeks"]}},60316:s=>{s.exports={en:["{str} week intervals","{str} weeks intervals"],cs:["{str} week intervals","{str} weeks intervals"]}},96325:s=>{s.exports={en:["{str} year","{str} years"],cs:["{str} year","{str} years"]}},91549:s=>{s.exports={en:["{str} year","{str} years"],cs:["{str} year","{str} years"]}},78971:s=>{s.exports={en:["{str} year intervals","{str} years intervals"],cs:["{str} year intervals","{str} years intervals"]}}}]);