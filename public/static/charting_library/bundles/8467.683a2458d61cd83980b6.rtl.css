.button-LkmyTVRc {
  all: unset;
  display: flex;
  position: relative;
}
.button-LkmyTVRc:before {
  border-radius: 4px;
  content: none;
  height: 100%;
  outline: 2px none #2962ff;
  outline-offset: -2px;
  pointer-events: none;
  position: absolute;
  right: 0;
  top: 0;
  width: 100%;
}
.button-LkmyTVRc:focus-visible:before {
  content: "";
  outline-style: solid;
}
.button-LkmyTVRc.active-LkmyTVRc:before {
  outline-color: #fff;
}
.item-zwyEh4hn {
  align-items: flex-start;
  font-size: 16px;
  padding-left: 20px;
  white-space: normal;
}
.label-zwyEh4hn {
  line-height: 24px;
  overflow: visible;
}
.labelRow-zwyEh4hn {
  margin-bottom: 2px;
  margin-top: 2px;
}
.toolbox-zwyEh4hn {
  margin-top: 3px;
}
