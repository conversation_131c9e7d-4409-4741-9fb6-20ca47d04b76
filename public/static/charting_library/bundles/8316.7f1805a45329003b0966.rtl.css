.itemRow-BadjY5sX {
  align-items: center;
  cursor: default;
  display: flex;
  height: 52px;
}
@media (any-hover: hover) {
  .itemRow-BadjY5sX:hover {
    background-color: var(--themed-color-item-row-bg-hover, #f0f3fa);
  }
  html.theme-dark .itemRow-BadjY5sX:hover {
    background-color: var(--themed-color-item-row-bg-hover, #2a2e39);
  }
}
.itemRow-BadjY5sX.active-BadjY5sX,
html.theme-dark .itemRow-BadjY5sX.active-BadjY5sX {
  background-color: var(--themed-color-brand, #2962ff);
}
.itemRow-BadjY5sX.selected-BadjY5sX {
  background-color: var(--themed-color-item-bg-selected, #bbd9fb);
}
html.theme-dark .itemRow-BadjY5sX.selected-BadjY5sX {
  background-color: var(--themed-color-item-bg-selected, #142e61);
}
.itemRow-BadjY5sX.mobile-BadjY5sX {
  height: 55px;
}
.itemRow-BadjY5sX .itemInfo-BadjY5sX {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding-left: 20px;
}
.itemRow-BadjY5sX .itemInfo-BadjY5sX .title-BadjY5sX {
  color: var(--themed-color-item-row-text, #131722);
  font-size: 14px;
  line-height: 21px;
  margin-bottom: 4px;
  overflow: hidden;
  white-space: nowrap;
}
html.theme-dark .itemRow-BadjY5sX .itemInfo-BadjY5sX .title-BadjY5sX {
  color: var(--themed-color-item-row-text, #d1d4dc);
}
.itemRow-BadjY5sX .itemInfo-BadjY5sX .title-BadjY5sX.active-BadjY5sX {
  color: var(--themed-color-item-active-text, #fff);
}
html.theme-dark
  .itemRow-BadjY5sX
  .itemInfo-BadjY5sX
  .title-BadjY5sX.active-BadjY5sX {
  color: var(--themed-color-item-active-text, #d1d4dc);
}
.itemRow-BadjY5sX .itemInfo-BadjY5sX .title-BadjY5sX.mobile-BadjY5sX {
  font-size: 16px;
  line-height: 24px;
  margin: 4px 0 2px;
}
.itemRow-BadjY5sX .itemInfo-BadjY5sX .details-BadjY5sX {
  color: var(--themed-color-default-gray, #6a6d78);
  font-size: 12px;
  line-height: 17px;
  overflow: hidden;
  white-space: nowrap;
}
html.theme-dark .itemRow-BadjY5sX .itemInfo-BadjY5sX .details-BadjY5sX {
  color: var(--themed-color-default-gray, #868993);
}
.itemRow-BadjY5sX .itemInfo-BadjY5sX .details-BadjY5sX.active-BadjY5sX {
  color: var(--themed-color-item-active-text, #fff);
}
html.theme-dark
  .itemRow-BadjY5sX
  .itemInfo-BadjY5sX
  .details-BadjY5sX.active-BadjY5sX {
  color: var(--themed-color-item-active-text, #d1d4dc);
}
.itemRow-BadjY5sX .itemInfo-BadjY5sX .details-BadjY5sX.mobile-BadjY5sX {
  margin-bottom: 8px;
}
.itemRow-BadjY5sX .itemInfoWithPadding-BadjY5sX {
  padding-right: 20px;
}
.itemRow-BadjY5sX .favoriteButton-BadjY5sX {
  margin: 0 6px 0 4px;
}
.itemRow-BadjY5sX .favoriteButton-BadjY5sX.favorite-BadjY5sX {
  opacity: 1;
}
.itemRow-BadjY5sX .removeButton-BadjY5sX {
  margin-left: 18px;
  margin-right: auto;
}
.feature-no-touch .favoriteButton-BadjY5sX,
.feature-no-touch .removeButton-BadjY5sX {
  opacity: 0;
}
@media (any-hover: hover) {
  .feature-no-touch .itemRow-BadjY5sX:hover .favoriteButton-BadjY5sX,
  .feature-no-touch .itemRow-BadjY5sX:hover .removeButton-BadjY5sX {
    opacity: 1;
  }
}
