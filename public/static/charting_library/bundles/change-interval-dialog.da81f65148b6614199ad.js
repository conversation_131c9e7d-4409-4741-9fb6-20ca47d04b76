(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[2077],{74581:e=>{e.exports={container:"container-WDZ0PRNh","container-xxsmall":"container-xxsmall-WDZ0PRNh","container-xsmall":"container-xsmall-WDZ0PRNh","container-small":"container-small-WDZ0PRNh","container-medium":"container-medium-WDZ0PRNh","container-large":"container-large-WDZ0PRNh","intent-default":"intent-default-WDZ0PRNh",focused:"focused-WDZ0PRNh",readonly:"readonly-WDZ0PRNh",disabled:"disabled-WDZ0PRNh","with-highlight":"with-highlight-WDZ0PRNh",grouped:"grouped-WDZ0PRNh","adjust-position":"adjust-position-WDZ0PRNh","first-row":"first-row-WDZ0PRNh","first-col":"first-col-WDZ0PRNh",stretch:"stretch-WDZ0PRNh","font-size-medium":"font-size-medium-WDZ0PRNh","font-size-large":"font-size-large-WDZ0PRNh","no-corner-top-left":"no-corner-top-left-WDZ0PRNh","no-corner-top-right":"no-corner-top-right-WDZ0PRNh","no-corner-bottom-right":"no-corner-bottom-right-WDZ0PRNh","no-corner-bottom-left":"no-corner-bottom-left-WDZ0PRNh","size-xxsmall":"size-xxsmall-WDZ0PRNh","size-xsmall":"size-xsmall-WDZ0PRNh","size-small":"size-small-WDZ0PRNh","size-medium":"size-medium-WDZ0PRNh","size-large":"size-large-WDZ0PRNh","intent-success":"intent-success-WDZ0PRNh","intent-warning":"intent-warning-WDZ0PRNh","intent-danger":"intent-danger-WDZ0PRNh","intent-primary":"intent-primary-WDZ0PRNh","border-none":"border-none-WDZ0PRNh","border-thin":"border-thin-WDZ0PRNh","border-thick":"border-thick-WDZ0PRNh",highlight:"highlight-WDZ0PRNh",shown:"shown-WDZ0PRNh"}},81329:e=>{e.exports={"inner-slot":"inner-slot-W53jtLjw",interactive:"interactive-W53jtLjw",icon:"icon-W53jtLjw","inner-middle-slot":"inner-middle-slot-W53jtLjw","before-slot":"before-slot-W53jtLjw","after-slot":"after-slot-W53jtLjw"}},55315:e=>{e.exports={input:"input-RUSovanF","size-xxsmall":"size-xxsmall-RUSovanF","size-xsmall":"size-xsmall-RUSovanF","size-small":"size-small-RUSovanF","size-medium":"size-medium-RUSovanF","size-large":"size-large-RUSovanF","with-start-slot":"with-start-slot-RUSovanF","with-end-slot":"with-end-slot-RUSovanF"}},86332:(e,t,n)=>{"use strict";n.d(t,{ControlGroupContext:()=>o});const o=n(50959).createContext({isGrouped:!1,cellState:{isTop:!0,isRight:!0,isBottom:!0,isLeft:!0}})},95604:(e,t,n)=>{"use strict";function o(e){let t=0;return e.isTop&&e.isLeft||(t+=1),e.isTop&&e.isRight||(t+=2),e.isBottom&&e.isLeft||(t+=8),e.isBottom&&e.isRight||(t+=4),t}n.d(t,{getGroupCellRemoveRoundBorders:()=>o})},67029:(e,t,n)=>{"use strict";n.d(t,{ControlSkeleton:()=>R,InputClasses:()=>g});var o=n(50959),r=n(97754),s=n(50151),l=n(38528),i=n(90186),a=n(86332),u=n(95604);var c=n(74581),d=n.n(c);function h(e){let t="";return 0!==e&&(1&e&&(t=r(t,d()["no-corner-top-left"])),2&e&&(t=r(t,d()["no-corner-top-right"])),4&e&&(t=r(t,d()["no-corner-bottom-right"])),8&e&&(t=r(t,d()["no-corner-bottom-left"]))),t}function m(e,t,n,o){const{removeRoundBorder:s,className:l,intent:i="default",borderStyle:a="thin",size:c,highlight:m,disabled:f,readonly:g,stretch:p,noReadonlyStyles:v,isFocused:R}=e,C=h(null!=s?s:(0,
u.getGroupCellRemoveRoundBorders)(n));return r(d().container,d()[`container-${c}`],d()[`intent-${i}`],d()[`border-${a}`],c&&d()[`size-${c}`],C,m&&d()["with-highlight"],f&&d().disabled,g&&!v&&d().readonly,R&&d().focused,p&&d().stretch,t&&d().grouped,!o&&d()["adjust-position"],n.isTop&&d()["first-row"],n.isLeft&&d()["first-col"],l)}function f(e,t,n){const{highlight:o,highlightRemoveRoundBorder:s}=e;if(!o)return d().highlight;const l=h(null!=s?s:(0,u.getGroupCellRemoveRoundBorders)(t));return r(d().highlight,d().shown,d()[`size-${n}`],l)}const g={FontSizeMedium:(0,s.ensureDefined)(d()["font-size-medium"]),FontSizeLarge:(0,s.ensureDefined)(d()["font-size-large"])},p={passive:!1};function v(e,t){const{style:n,id:r,role:s,onFocus:u,onBlur:c,onMouseOver:d,onMouseOut:h,onMouseDown:g,onMouseUp:v,onKeyDown:R,onClick:C,tabIndex:D,startSlot:N,middleSlot:w,endSlot:b,onWheel:S,onWheelNoPassive:W=null,size:P}=e,{isGrouped:x,cellState:M,disablePositionAdjustment:z=!1}=(0,o.useContext)(a.ControlGroupContext),y=function(e,t=null,n){const r=(0,o.useRef)(null),s=(0,o.useRef)(null),l=(0,o.useCallback)((()=>{if(null===r.current||null===s.current)return;const[e,t,n]=s.current;null!==t&&r.current.addEventListener(e,t,n)}),[]),i=(0,o.useCallback)((()=>{if(null===r.current||null===s.current)return;const[e,t,n]=s.current;null!==t&&r.current.removeEventListener(e,t,n)}),[]),a=(0,o.useCallback)((e=>{i(),r.current=e,l()}),[]);return(0,o.useEffect)((()=>(s.current=[e,t,n],l(),i)),[e,t,n]),a}("wheel",W,p);return o.createElement("span",{style:n,id:r,role:s,className:m(e,x,M,z),tabIndex:D,ref:(0,l.useMergedRefs)([t,y]),onFocus:u,onBlur:c,onMouseOver:d,onMouseOut:h,onMouseDown:g,onMouseUp:v,onKeyDown:R,onClick:C,onWheel:S,...(0,i.filterDataProps)(e),...(0,i.filterAriaProps)(e)},N,w,b,o.createElement("span",{className:f(e,M,P)}))}v.displayName="ControlSkeleton";const R=o.forwardRef(v)},78274:(e,t,n)=>{"use strict";n.d(t,{AfterSlot:()=>c,EndSlot:()=>u,MiddleSlot:()=>a,StartSlot:()=>i});var o=n(50959),r=n(97754),s=n(81329),l=n.n(s);function i(e){const{className:t,interactive:n=!0,icon:s=!1,children:i}=e;return o.createElement("span",{className:r(l()["inner-slot"],n&&l().interactive,s&&l().icon,t)},i)}function a(e){const{className:t,children:n}=e;return o.createElement("span",{className:r(l()["inner-slot"],l()["inner-middle-slot"],t)},n)}function u(e){const{className:t,interactive:n=!0,icon:s=!1,children:i}=e;return o.createElement("span",{className:r(l()["inner-slot"],n&&l().interactive,s&&l().icon,t)},i)}function c(e){const{className:t,children:n}=e;return o.createElement("span",{className:r(l()["after-slot"],t)},n)}},31261:(e,t,n)=>{"use strict";n.d(t,{InputControl:()=>R});var o=n(50959),r=n(97754),s=n(90186),l=n(47201),i=n(48907),a=n(38528),u=n(48027),c=n(29202),d=n(45812),h=n(67029),m=n(78274),f=n(55315),g=n.n(f);function p(e){return!(0,s.isAriaAttribute)(e)&&!(0,s.isDataAttribute)(e)}function v(e){
const{id:t,title:n,role:l,tabIndex:i,placeholder:a,name:u,type:c,value:d,defaultValue:f,draggable:v,autoComplete:R,autoFocus:C,maxLength:D,min:N,max:w,step:b,pattern:S,inputMode:W,onSelect:P,onFocus:x,onBlur:M,onKeyDown:z,onKeyUp:y,onKeyPress:E,onChange:Z,onDragStart:F,size:I="small",className:U,inputClassName:k,disabled:O,readonly:B,containerTabIndex:L,startSlot:j,endSlot:G,reference:A,containerReference:V,onContainerFocus:K,...H}=e,T=(0,s.filterProps)(H,p),$={...(0,s.filterAriaProps)(H),...(0,s.filterDataProps)(H),id:t,title:n,role:l,tabIndex:i,placeholder:a,name:u,type:c,value:d,defaultValue:f,draggable:v,autoComplete:R,autoFocus:C,maxLength:D,min:N,max:w,step:b,pattern:S,inputMode:W,onSelect:P,onFocus:x,onBlur:M,onKeyDown:z,onKeyUp:y,onKeyPress:E,onChange:Z,onDragStart:F};return o.createElement(h.ControlSkeleton,{...T,disabled:O,readonly:B,tabIndex:L,className:r(g().container,U),size:I,ref:V,onFocus:K,startSlot:j,middleSlot:o.createElement(m.MiddleSlot,null,o.createElement("input",{...$,className:r(g().input,g()[`size-${I}`],k,j&&g()["with-start-slot"],G&&g()["with-end-slot"]),disabled:O,readOnly:B,ref:A})),endSlot:G})}function R(e){e=(0,u.useControl)(e);const{disabled:t,autoSelectOnFocus:n,tabIndex:r=0,onFocus:s,onBlur:h,reference:m,containerReference:f=null}=e,g=(0,o.useRef)(null),p=(0,o.useRef)(null),[R,C]=(0,c.useFocus)(),D=t?void 0:R?-1:r,N=t?void 0:R?r:-1,{isMouseDown:w,handleMouseDown:b,handleMouseUp:S}=(0,d.useIsMouseDown)(),W=(0,l.createSafeMulticastEventHandler)(C.onFocus,(function(e){n&&!w.current&&(0,i.selectAllContent)(e.currentTarget)}),s),P=(0,l.createSafeMulticastEventHandler)(C.onBlur,h),x=(0,o.useCallback)((e=>{g.current=e,m&&("function"==typeof m&&m(e),"object"==typeof m&&(m.current=e))}),[g,m]);return o.createElement(v,{...e,isFocused:R,containerTabIndex:D,tabIndex:N,onContainerFocus:function(e){p.current===e.target&&null!==g.current&&g.current.focus()},onFocus:W,onBlur:P,reference:x,containerReference:(0,a.useMergedRefs)([p,f]),onMouseDown:b,onMouseUp:S})}},48027:(e,t,n)=>{"use strict";n.d(t,{useControl:()=>s});var o=n(47201),r=n(29202);function s(e){const{onFocus:t,onBlur:n,intent:s,highlight:l,disabled:i}=e,[a,u]=(0,r.useFocus)(void 0,i),c=(0,o.createSafeMulticastEventHandler)(i?void 0:u.onFocus,t),d=(0,o.createSafeMulticastEventHandler)(i?void 0:u.onBlur,n);return{...e,intent:s||(a?"primary":"default"),highlight:null!=l?l:a,onFocus:c,onBlur:d}}},29202:(e,t,n)=>{"use strict";n.d(t,{useFocus:()=>r});var o=n(50959);function r(e,t){const[n,r]=(0,o.useState)(!1);(0,o.useEffect)((()=>{t&&n&&r(!1)}),[t,n]);const s={onFocus:(0,o.useCallback)((function(t){void 0!==e&&e.current!==t.target||r(!0)}),[e]),onBlur:(0,o.useCallback)((function(t){void 0!==e&&e.current!==t.target||r(!1)}),[e])};return[n,s]}},45812:(e,t,n)=>{"use strict";n.d(t,{useIsMouseDown:()=>r});var o=n(50959);function r(){const e=(0,o.useRef)(!1),t=(0,o.useCallback)((()=>{e.current=!0}),[e]),n=(0,o.useCallback)((()=>{e.current=!1}),[e]);return{isMouseDown:e,handleMouseDown:t,handleMouseUp:n}}},38528:(e,t,n)=>{"use strict";n.d(t,{
useMergedRefs:()=>s});var o=n(50959),r=n(53017);function s(e){return(0,o.useCallback)((0,r.mergeRefs)(e),e)}},48907:(e,t,n)=>{"use strict";function o(e){null!==e&&e.setSelectionRange(0,e.value.length)}n.d(t,{selectAllContent:()=>o})},47201:(e,t,n)=>{"use strict";function o(...e){return t=>{for(const n of e)void 0!==n&&n(t)}}n.d(t,{createSafeMulticastEventHandler:()=>o})},51826:(e,t,n)=>{"use strict";n.d(t,{DialogsOpenerManager:()=>o,dialogsOpenerManager:()=>r});class o{constructor(){this._storage=new Map}setAsOpened(e,t){this._storage.set(e,t)}setAsClosed(e){this._storage.delete(e)}isOpened(e){return this._storage.has(e)}getDialogPayload(e){return this._storage.get(e)}}const r=new o},4237:(e,t,n)=>{"use strict";var o=n(32227);t.createRoot=o.createRoot,o.hydrateRoot},5362:e=>{e.exports={dialog:"dialog-UGdC69sw",dialogInner:"dialogInner-UGdC69sw",titleWrapper:"titleWrapper-UGdC69sw",title:"title-UGdC69sw",infoHint:"infoHint-UGdC69sw",form:"form-UGdC69sw",inputWrapper:"inputWrapper-UGdC69sw",input:"input-UGdC69sw",hint:"hint-UGdC69sw",error:"error-UGdC69sw"}},71090:(e,t,n)=>{"use strict";n.r(t),n.d(t,{showChangeIntervalDialog:()=>w});var o=n(50959),r=n(97754),s=n.n(r),l=n(11542),i=n(31261),a=n(67029),u=n(82992),c=n(82206),d=n(9745),h=n(85508);const m=l.t(null,void 0,n(51998)),f=l.t(null,void 0,n(96941));function g(e){const{className:t,isSecondsEnabled:n}=e;return o.createElement(d.Icon,{icon:h,className:s()("apply-common-tooltip",t),title:n?f:m})}var p=n(10074),v=n(85049);var R=n(5362);function C(e){const{initVal:t,selectOnInit:r,onClose:d}=e,h=(0,o.useRef)(null),[m,f]=(0,o.useState)(t.toUpperCase()),C=(0,o.useMemo)((()=>v.Interval.parse(m)),[m]),D=function(e,t){return(0,o.useMemo)((()=>{if(!t.isValid()||!(0,p.intervalIsSupported)(e))return!1;const n=v.Interval.normalize(e);return null!==n&&(0,p.isResolutionMultiplierValid)(n)}),[e,t])}(m,C),N=(0,o.useMemo)((()=>{if(!D)return null;return(0,p.getTranslatedResolutionModel)(C.value()).hint}),[D,C]);return(0,o.useLayoutEffect)((()=>{var e,t;r?null===(e=h.current)||void 0===e||e.select():null===(t=h.current)||void 0===t||t.focus()}),[r]),o.createElement(c.PopupDialog,{className:R.dialog,"data-dialog-name":"change-interval-dialog",isOpened:!0,onClickOutside:d,onFocus:function(){var e;null===(e=h.current)||void 0===e||e.focus()},onKeyDown:function(e){27===e.keyCode&&(null==d||d())}},o.createElement("div",{className:R.dialogInner},o.createElement("div",{className:R.titleWrapper},o.createElement("div",{className:R.title},l.t(null,void 0,n(2569))),o.createElement(g,{className:R.infoHint,isSecondsEnabled:(0,p.isSecondsEnabled)()})),o.createElement("form",{className:R.form,onSubmit:function(e){e.preventDefault();const t=u.linking.interval.value(),n=v.Interval.normalize(m);n&&t!==n&&D&&u.linking.interval.setValue(n);null==d||d()}},o.createElement(i.InputControl,{className:s()(R.inputWrapper,a.InputClasses.FontSizeLarge),inputClassName:R.input,type:"text",size:"large",reference:h,value:m,maxLength:8,intent:D?void 0:"danger",onChange:function(e){const{value:t}=e.target
;f(t.toUpperCase())}})),D?o.createElement("div",{className:R.hint},N):o.createElement("div",{className:s()(R.hint,R.error)},l.t(null,void 0,n(60731)))))}var D=n(51826),N=n(28124);function w(e){if(D.dialogsOpenerManager.isOpened("ChangeIntervalDialog")||D.dialogsOpenerManager.isOpened("SymbolSearch"))return;const t=document.createElement("div"),{initVal:n,selectOnInit:r,onClose:s}=e,l=o.createElement(C,{initVal:n,selectOnInit:r,onClose:function(){i.unmount(),D.dialogsOpenerManager.setAsClosed("ChangeIntervalDialog"),null==s||s()}}),i=(0,N.createReactRoot)(l,t);D.dialogsOpenerManager.setAsOpened("ChangeIntervalDialog")}},85508:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18" fill="none"><path stroke="currentColor" d="M8 8.5h1.5V14"/><circle fill="currentColor" cx="9" cy="5" r="1"/><path stroke="currentColor" d="M16.5 9a7.5 7.5 0 1 1-15 0 7.5 7.5 0 0 1 15 0z"/></svg>'},55698:(e,t,n)=>{"use strict";n.d(t,{nanoid:()=>o});let o=(e=21)=>crypto.getRandomValues(new Uint8Array(e)).reduce(((e,t)=>e+=(t&=63)<36?t.toString(36):t<62?(t-26).toString(36).toUpperCase():t>62?"-":"_"),"")}}]);