(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[2878],{59142:function(e,t){var o,n,i;n=[t],o=function(e){"use strict";function t(e){if(Array.isArray(e)){for(var t=0,o=Array(e.length);t<e.length;t++)o[t]=e[t];return o}return Array.from(e)}Object.defineProperty(e,"__esModule",{value:!0});var o=!1;if("undefined"!=typeof window){var n={get passive(){o=!0}};window.addEventListener("testPassive",null,n),window.removeEventListener("testPassive",null,n)}var i="undefined"!=typeof window&&window.navigator&&window.navigator.platform&&/iP(ad|hone|od)/.test(window.navigator.platform),l=[],s=!1,a=-1,r=void 0,c=void 0,u=function(e){return l.some((function(t){return!(!t.options.allowTouchMove||!t.options.allowTouchMove(e))}))},d=function(e){var t=e||window.event;return!!u(t.target)||1<t.touches.length||(t.preventDefault&&t.preventDefault(),!1)},h=function(){setTimeout((function(){void 0!==c&&(document.body.style.paddingRight=c,c=void 0),void 0!==r&&(document.body.style.overflow=r,r=void 0)}))};e.disableBodyScroll=function(e,n){if(i){if(!e)return void console.error("disableBodyScroll unsuccessful - targetElement must be provided when calling disableBodyScroll on IOS devices.");if(e&&!l.some((function(t){return t.targetElement===e}))){var h={targetElement:e,options:n||{}};l=[].concat(t(l),[h]),e.ontouchstart=function(e){1===e.targetTouches.length&&(a=e.targetTouches[0].clientY)},e.ontouchmove=function(t){var o,n,i,l;1===t.targetTouches.length&&(n=e,l=(o=t).targetTouches[0].clientY-a,!u(o.target)&&(n&&0===n.scrollTop&&0<l||(i=n)&&i.scrollHeight-i.scrollTop<=i.clientHeight&&l<0?d(o):o.stopPropagation()))},s||(document.addEventListener("touchmove",d,o?{passive:!1}:void 0),s=!0)}}else{v=n,setTimeout((function(){if(void 0===c){var e=!!v&&!0===v.reserveScrollBarGap,t=window.innerWidth-document.documentElement.clientWidth;e&&0<t&&(c=document.body.style.paddingRight,document.body.style.paddingRight=t+"px")}void 0===r&&(r=document.body.style.overflow,document.body.style.overflow="hidden")}));var m={targetElement:e,options:n||{}};l=[].concat(t(l),[m])}var v},e.clearAllBodyScrollLocks=function(){i?(l.forEach((function(e){e.targetElement.ontouchstart=null,e.targetElement.ontouchmove=null})),s&&(document.removeEventListener("touchmove",d,o?{passive:!1}:void 0),s=!1),l=[],a=-1):(h(),l=[])},e.enableBodyScroll=function(e){if(i){if(!e)return void console.error("enableBodyScroll unsuccessful - targetElement must be provided when calling enableBodyScroll on IOS devices.");e.ontouchstart=null,e.ontouchmove=null,l=l.filter((function(t){return t.targetElement!==e})),s&&0===l.length&&(document.removeEventListener("touchmove",d,o?{passive:!1}:void 0),s=!1)}else 1===l.length&&l[0].targetElement===e?(h(),l=[]):l=l.filter((function(t){return t.targetElement!==e}))}},void 0===(i="function"==typeof o?o.apply(t,n):o)||(e.exports=i)},3545:e=>{e.exports={"default-drawer-min-top-distance":"100px"}},98992:e=>{e.exports={favorite:"favorite-_FRQhM5Y",hovered:"hovered-_FRQhM5Y",disabled:"disabled-_FRQhM5Y",active:"active-_FRQhM5Y",
checked:"checked-_FRQhM5Y"}},69961:e=>{e.exports={item:"item-zwyEh4hn",label:"label-zwyEh4hn",labelRow:"labelRow-zwyEh4hn",toolbox:"toolbox-zwyEh4hn"}},71150:e=>{e.exports={separator:"separator-QjUlCDId",small:"small-QjUlCDId",normal:"normal-QjUlCDId",large:"large-QjUlCDId"}},67842:(e,t,o)=>{"use strict";o.d(t,{useResizeObserver:()=>s});var n=o(50959),i=o(43010),l=o(39416);function s(e,t=[]){const{callback:o,ref:s=null}=function(e){return"function"==typeof e?{callback:e}:e}(e),a=(0,n.useRef)(null),r=(0,n.useRef)(o);r.current=o;const c=(0,l.useFunctionalRefObject)(s),u=(0,n.useCallback)((e=>{c(e),null!==a.current&&(a.current.disconnect(),null!==e&&a.current.observe(e))}),[c,a]);return(0,i.useIsomorphicLayoutEffect)((()=>(a.current=new ResizeObserver(((e,t)=>{r.current(e,t)})),c.current&&u(c.current),()=>{var e;null===(e=a.current)||void 0===e||e.disconnect()})),[c,...t]),u}},47201:(e,t,o)=>{"use strict";function n(...e){return t=>{for(const o of e)void 0!==o&&o(t)}}o.d(t,{createSafeMulticastEventHandler:()=>n})},45601:(e,t,o)=>{"use strict";o.d(t,{Measure:()=>i});var n=o(67842);function i(e){const{children:t,onResize:o}=e;return t((0,n.useResizeObserver)(o||(()=>{}),[null===o]))}},50238:(e,t,o)=>{"use strict";o.d(t,{useRovingTabindexElement:()=>l});var n=o(50959),i=o(39416);function l(e,t=[]){const[o,l]=(0,n.useState)(!1),s=(0,i.useFunctionalRefObject)(e);return(0,n.useLayoutEffect)((()=>{const e=s.current;if(null===e)return;const t=e=>{switch(e.type){case"roving-tabindex:main-element":l(!0);break;case"roving-tabindex:secondary-element":l(!1)}};return e.addEventListener("roving-tabindex:main-element",t),e.addEventListener("roving-tabindex:secondary-element",t),()=>{e.removeEventListener("roving-tabindex:main-element",t),e.removeEventListener("roving-tabindex:secondary-element",t)}}),t),[s,o?0:-1]}},36189:(e,t,o)=>{"use strict";o.d(t,{FavoriteButton:()=>d});var n=o(11542),i=o(50959),l=o(97754),s=o(9745),a=o(39146),r=o(48010),c=o(98992);const u={add:n.t(null,void 0,o(69207)),remove:n.t(null,void 0,o(85106))};function d(e){const{className:t,isFilled:o,isActive:n,onClick:d,...h}=e;return i.createElement(s.Icon,{...h,className:l(c.favorite,"apply-common-tooltip",o&&c.checked,n&&c.active,t),icon:o?a:r,onClick:d,title:o?u.remove:u.add})}},78036:(e,t,o)=>{"use strict";o.d(t,{useEnsuredContext:()=>l});var n=o(50959),i=o(50151);function l(e){return(0,i.ensureNotNull)((0,n.useContext)(e))}},70412:(e,t,o)=>{"use strict";o.d(t,{hoverMouseEventFilter:()=>l,useAccurateHover:()=>s,useHover:()=>i});var n=o(50959);function i(){const[e,t]=(0,n.useState)(!1);return[e,{onMouseOver:function(e){l(e)&&t(!0)},onMouseOut:function(e){l(e)&&t(!1)}}]}function l(e){return!e.currentTarget.contains(e.relatedTarget)}function s(e){const[t,o]=(0,n.useState)(!1);return(0,n.useEffect)((()=>{const t=t=>{if(null===e.current)return;const n=e.current.contains(t.target);o(n)};return document.addEventListener("mouseover",t),()=>document.removeEventListener("mouseover",t)}),[]),t}},29006:(e,t,o)=>{"use strict";o.d(t,{useResizeObserver:()=>n.useResizeObserver})
;var n=o(67842)},29185:(e,t,o)=>{"use strict";o.d(t,{useWatchedValue:()=>i});var n=o(50959);const i=e=>{const[t,o]=(0,n.useState)(e.value());return(0,n.useEffect)((()=>{const t=e=>o(e);return e.subscribe(t),()=>e.unsubscribe(t)}),[e]),[t,t=>e.setValue(t)]}},81332:(e,t,o)=>{"use strict";o.d(t,{multilineLabelWithIconAndToolboxTheme:()=>s});var n=o(40173),i=o(2908),l=o(69961);const s=(0,n.mergeThemes)(i,l)},11684:(e,t,o)=>{"use strict";o.d(t,{PopupMenuSeparator:()=>r});var n,i=o(50959),l=o(97754),s=o.n(l),a=o(71150);function r(e){const{size:t="normal",className:o,ariaHidden:n=!1}=e;return i.createElement("div",{className:s()(a.separator,"small"===t&&a.small,"normal"===t&&a.normal,"large"===t&&a.large,o),role:"separator","aria-hidden":n})}!function(e){e.Small="small",e.Large="large",e.Normal="normal"}(n||(n={}))},86656:(e,t,o)=>{"use strict";o.d(t,{TouchScrollContainer:()=>c});var n=o(50959),i=o(59142),l=o(50151),s=o(49483);const a=CSS.supports("overscroll-behavior","none");let r=0;const c=(0,n.forwardRef)(((e,t)=>{const{children:o,...l}=e,c=(0,n.useRef)(null);return(0,n.useImperativeHandle)(t,(()=>c.current)),(0,n.useLayoutEffect)((()=>{if(s.CheckMobile.iOS())return r++,null!==c.current&&(a?1===r&&(document.body.style.overscrollBehavior="none"):(0,i.disableBodyScroll)(c.current,{allowTouchMove:u(c)})),()=>{r--,null!==c.current&&(a?0===r&&(document.body.style.overscrollBehavior=""):(0,i.enableBodyScroll)(c.current))}}),[]),n.createElement("div",{ref:c,...l},o)}));function u(e){return t=>{const o=(0,l.ensureNotNull)(e.current),n=document.activeElement;return!o.contains(t)||null!==n&&o.contains(n)&&n.contains(t)}}},6132:(e,t,o)=>{"use strict";var n=o(22134);function i(){}function l(){}l.resetWarningCache=i,e.exports=function(){function e(e,t,o,i,l,s){if(s!==n){var a=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw a.name="Invariant Violation",a}}function t(){return e}e.isRequired=e;var o={array:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:l,resetWarningCache:i};return o.PropTypes=o,o}},19036:(e,t,o)=>{e.exports=o(6132)()},22134:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},88259:e=>{e.exports={button:"button-LkmyTVRc",active:"active-LkmyTVRc"}},11121:e=>{e.exports={dropdown:"dropdown-pbhJWNrt",buttonWrap:"buttonWrap-pbhJWNrt",control:"control-pbhJWNrt",arrow:"arrow-pbhJWNrt",arrowIcon:"arrowIcon-pbhJWNrt",isOpened:"isOpened-pbhJWNrt",hover:"hover-pbhJWNrt",isGrayed:"isGrayed-pbhJWNrt",accessible:"accessible-pbhJWNrt"}},33532:e=>{e.exports={title:"title-u3QJgF_p"}},9243:e=>{e.exports={container:"container-Wp9adlfh",mirror:"mirror-Wp9adlfh",background:"background-Wp9adlfh",arrow:"arrow-Wp9adlfh"}},61685:e=>{e.exports={item:"item-uxNfqe_g",label:"label-uxNfqe_g"}},88436:e=>{e.exports={drawingToolbar:"drawingToolbar-BfVZxb4b",
isHidden:"isHidden-BfVZxb4b",inner:"inner-BfVZxb4b",group:"group-BfVZxb4b",lastGroup:"lastGroup-BfVZxb4b",fill:"fill-BfVZxb4b"}},43955:e=>{e.exports={toggleButton:"toggleButton-OhcB9eH7",collapsed:"collapsed-OhcB9eH7",background:"background-OhcB9eH7",arrow:"arrow-OhcB9eH7",accessible:"accessible-OhcB9eH7"}},91377:e=>{e.exports={item:"item-yfwdxbRo",hovered:"hovered-yfwdxbRo"}},76086:e=>{e.exports={desktopSize:"desktopSize-l1SzP6TV",smallSize:"smallSize-l1SzP6TV",tabs:"tabs-l1SzP6TV",categories:"categories-l1SzP6TV"}},32905:e=>{e.exports={sticker:"sticker-aZclaNCs"}},92131:e=>{e.exports={"tablet-small-breakpoint":"screen and (max-width: 430px)",stickerRow:"stickerRow-KUOIljqV"}},29261:e=>{e.exports={wrapper:"wrapper-FNeSdxed"}},59905:e=>{e.exports={drawer:"drawer-PzCssz1z",menuBox:"menuBox-PzCssz1z"}},48770:e=>{e.exports={toolButtonMagnet:"toolButtonMagnet-wg76fIbD",toolButtonMagnet__menuItem:"toolButtonMagnet__menuItem-wg76fIbD",toolButtonMagnet__hintPlaceholder:"toolButtonMagnet__hintPlaceholder-wg76fIbD"}},92986:e=>{e.exports={sectionTitle:"sectionTitle-Srvnqigs"}},78319:e=>{e.exports={wrap:"wrap-Z4M3tWHb",scrollWrap:"scrollWrap-Z4M3tWHb",noScrollBar:"noScrollBar-Z4M3tWHb",content:"content-Z4M3tWHb",icon:"icon-Z4M3tWHb",scrollBot:"scrollBot-Z4M3tWHb",scrollTop:"scrollTop-Z4M3tWHb",isVisible:"isVisible-Z4M3tWHb",iconWrap:"iconWrap-Z4M3tWHb",fadeBot:"fadeBot-Z4M3tWHb",fadeTop:"fadeTop-Z4M3tWHb"}},64630:e=>{e.exports={iconContainer:"iconContainer-dmpvVypS"}},57177:(e,t,o)=>{"use strict";var n;function i(e){e.dispatchEvent(new CustomEvent("roving-tabindex:main-element"))}function l(e){e.dispatchEvent(new CustomEvent("roving-tabindex:secondary-element"))}o.d(t,{becomeMainElement:()=>i,becomeSecondaryElement:()=>l}),function(e){e.MainElement="roving-tabindex:main-element",e.SecondaryElement="roving-tabindex:secondary-element"}(n||(n={}))},40894:(e,t,o)=>{"use strict";o.d(t,{MenuFavoriteButton:()=>c});var n=o(50959),i=o(97754),l=o.n(i),s=o(50238),a=o(36189),r=o(88259);function c(e){const{tooltip:t,onClick:o,...i}=e,[c,u]=(0,s.useRovingTabindexElement)(null);return n.createElement("button",{ref:c,tabIndex:u,onClick:o,className:l()(r.button,i.isActive&&r.active),type:"button"},n.createElement(a.FavoriteButton,{"aria-label":t,...i,"data-tooltip":t}))}},16829:(e,t,o)=>{"use strict";o.d(t,{ToolWidgetMenuSummary:()=>s});var n=o(50959),i=o(97754),l=o(33532);function s(e){return n.createElement("div",{className:i(e.className,l.title)},e.children)}},37159:(e,t,o)=>{"use strict";o.r(t),o.d(t,{DrawingToolbarRenderer:()=>_o});var n=o(50959),i=o(32227),l=o(50151),s=o(97754),a=o.n(s),r=o(32563),c=o(56840),u=o(56570),d=o(928),h=o(76422),m=o(52033),v=o(49483),g=o(84015),p=o(2627);class b{constructor(e){this._drawingsAccess=e||{tools:[],type:"black"}}isToolEnabled(e){const t=this._findTool(e);return!(!t||!t.grayed)||("black"===this._drawingsAccess.type?!t:!!t)}isToolGrayed(e){const t=this._findTool(e);return Boolean(t&&t.grayed)}_findTool(e){return this._drawingsAccess.tools.find((t=>t.name===e))}}
var f=o(59511),T=o(6307),w=o(9745),C=o(61119),_=o(78871),x=o(45601),E=o(78319),k=o(61380);class y extends n.PureComponent{constructor(e){super(e),this._scroll=null,this._handleScrollTop=()=>{this.animateTo(Math.max(0,this.currentPosition()-(this.state.heightWrap-50)))},this._handleScrollBot=()=>{this.animateTo(Math.min((this.state.heightContent||0)-(this.state.heightWrap||0),this.currentPosition()+(this.state.heightWrap-50)))},this._handleResizeWrap=([e])=>{this.setState({heightWrap:e.contentRect.height})},this._handleResizeContent=([e])=>{this.setState({heightContent:e.contentRect.height})},this._handleScroll=()=>{const{onScroll:e}=this.props;e&&e(this.currentPosition(),this.isAtTop(),this.isAtBot()),this._checkButtonsVisibility()},this._checkButtonsVisibility=()=>{const{isVisibleTopButton:e,isVisibleBotButton:t}=this.state,o=this.isAtTop(),n=this.isAtBot();o||e?o&&e&&this.setState({isVisibleTopButton:!1}):this.setState({isVisibleTopButton:!0}),n||t?n&&t&&this.setState({isVisibleBotButton:!1}):this.setState({isVisibleBotButton:!0})},this.state={heightContent:0,heightWrap:0,isVisibleBotButton:!1,isVisibleTopButton:!1}}componentDidMount(){this._checkButtonsVisibility()}componentDidUpdate(e,t){t.heightWrap===this.state.heightWrap&&t.heightContent===this.state.heightContent||this._handleScroll()}currentPosition(){return this._scroll?this._scroll.scrollTop:0}isAtTop(){return this.currentPosition()<=1}isAtBot(){return this.currentPosition()+this.state.heightWrap>=this.state.heightContent-1}animateTo(e,t=_.dur){const o=this._scroll;o&&(0,C.doAnimate)({onStep(e,t){o.scrollTop=t},from:o.scrollTop,to:Math.round(e),easing:_.easingFunc.easeInOutCubic,duration:t})}render(){const{children:e,isVisibleScrollbar:t,isVisibleFade:o,isVisibleButtons:i,onMouseOver:l,onMouseOut:s}=this.props,{heightContent:r,heightWrap:c,isVisibleBotButton:u,isVisibleTopButton:d}=this.state;return n.createElement(x.Measure,{onResize:this._handleResizeWrap},(h=>n.createElement("div",{className:E.wrap,onMouseOver:l,onMouseOut:s,ref:h},n.createElement("div",{className:a()(E.scrollWrap,{[E.noScrollBar]:!t}),onScroll:this._handleScroll,ref:e=>this._scroll=e},n.createElement(x.Measure,{onResize:this._handleResizeContent},(t=>n.createElement("div",{className:E.content,ref:t},e)))),o&&n.createElement("div",{className:a()(E.fadeTop,{[E.isVisible]:d&&r>c})}),o&&n.createElement("div",{className:a()(E.fadeBot,{[E.isVisible]:u&&r>c})}),i&&n.createElement("div",{className:a()(E.scrollTop,{[E.isVisible]:d&&r>c}),onClick:this._handleScrollTop},n.createElement("div",{className:E.iconWrap},n.createElement(w.Icon,{icon:k,className:E.icon}))),i&&n.createElement("div",{className:a()(E.scrollBot,{[E.isVisible]:u&&r>c}),onClick:this._handleScrollBot},n.createElement("div",{className:E.iconWrap},n.createElement(w.Icon,{icon:k,className:E.icon}))))))}}y.defaultProps={isVisibleScrollbar:!0};var F=o(4741),S=o(59064),L=o(29835),M=o(50238);function A(e){const[t,o]=(0,M.useRovingTabindexElement)(null);return n.createElement(L.ToolButton,{...e,ref:t,tag:"button",tabIndex:o})}
function B(e){const{id:t,action:o,isActive:i,isHidden:l,isTransparent:s,toolName:a}=e;return n.createElement(A,{id:t,icon:p.lineToolsInfo[a].icon,isActive:i,isHidden:l,isTransparent:s,onClick:o,tooltip:p.lineToolsInfo[a].localizedName,"data-name":a})}var I,W=o(11542),N=o(90186),D=o(29185),R=o(64147);!function(e){e.Icons="icons",e.Emojis="emojis",e.Stickers="stickers"}(I||(I={}));const V=c.getValue("ToolButtonIcons.LastCategory",I.Emojis),H=new R.WatchedValue(V);function O(){const[e,t]=(0,D.useWatchedValue)(H);return[e,(0,n.useCallback)((e=>{t(e),function(e){c.setValue("ToolButtonIcons.LastCategory",e)}(e)}),[t])]}var P=o(99616),z=o(29261);function j(e){return n.createElement("div",{className:z.wrapper},e.text)}var U=o(84243),Z=o(51609),G=o(22976),K=o(70616),J=o(18042),Q=o(44986),Y=o(83778),q=o(48748);const $=["0xF087","0xF088","0xF164","0xF165","0xF0A4","0xF0A5","0xF007","0xF0A6","0xF0A7","0xF118","0xF11A","0xF119","0xF183"],X=["0xF153","0xF154","0xF155","0xF156","0xF157","0xF158","0xF159","0xF195","0xF15A"],ee=["0xF060","0xF061","0xF062","0xF063","0xF053","0xF054","0xF077","0xF078","0xF07D","0xF07E","0xF0A9","0xF0AA","0xF0AB","0xF0D9","0xF0DA","0xF0D7","0xF0D8","0xF102","0xF103","0xF104","0xF105","0xF106","0xF107","0xF137","0xF139","0xF13A","0xF112","0xF064","0xF148","0xF149","0xF177","0xF178","0xF175","0xF176","0xF01A","0xF01B","0xF065","0xF066"],te=["0xF11D","0xF11E","0xF024","0xF004","0xF005","0xF006","0xF046","0xF00C","0xF00D","0xF011","0xF012","0xF021","0xF01E","0xF192","0xF041","0xF14A","0xF055","0xF056","0xF057","0xF059","0xF058","0xF05A","0xF05B","0xF05C","0xF05D","0xF05E","0xF067","0xF068","0xF069","0xF06A","0xF071","0xF06E","0xF070","0xF075","0xF08A","0xF0A3","0xF0E5","0xF110","0xF111","0xF123","0xF124","0xF10C","0xF128","0xF129","0xF12A","0xF140","0xF113","0xF17C","0xF179"],oe=["0xF06C","0xF185","0xF186","0xF188","0xF0E7"],ne=["0xF000","0xF002","0xF00E","0xF015","0xF017","0xF030","0xF013","0xF043","0xF06B","0xF072","0xF076","0xF080","0xF084","0xF040","0xF0A1","0xF0A2","0xF0D6","0xF0E3","0xF0EB","0xF0F3","0xF135","0xF13D","0xF2FE"],ie=[...$,...X,...ee,...te,...oe,...ne].map((e=>+e)),le=new Set(ie);const se=[{title:W.t(null,{context:"emoji_group"},o(88906)),emojis:[],content:n.createElement(P.IconItem,{icon:Q})},{title:W.t(null,{context:"emoji_group"},o(51853)),emojis:$,content:n.createElement(P.IconItem,{icon:Y})},{title:W.t(null,{context:"emoji_group"},o(33282)),emojis:te,content:n.createElement(P.IconItem,{icon:K})},{title:W.t(null,{context:"emoji_group"},o(10522)),emojis:oe,content:n.createElement(P.IconItem,{icon:q})},{title:W.t(null,{context:"emoji_group"},o(14143)),emojis:X,content:n.createElement(P.IconItem,{icon:G})},{title:W.t(null,{context:"emoji_group"},o(98355)),emojis:ne,content:n.createElement(P.IconItem,{icon:J})},{title:W.t(null,{context:"emoji_group"},o(74245)),emojis:ee,content:n.createElement(P.IconItem,{icon:Z})}],ae={[I.Icons]:U.drawingToolsIcons.heart,[I.Emojis]:U.drawingToolsIcons.smile,[I.Stickers]:U.drawingToolsIcons.sticker},re=[{title:I.Emojis,content:n.createElement(j,{
text:W.t(null,void 0,o(16290))})},{title:I.Stickers,content:n.createElement(j,{text:W.t(null,void 0,o(50428))})},{title:I.Icons,content:n.createElement(j,{text:W.t(null,void 0,o(73829))})}];var ce=o(3343),ue=o(20520),de=o(27317),he=o(76460),me=o(41590),ve=o(40173),ge=o(20243),pe=o(14665);const be=o(11121),fe=(0,n.forwardRef)(((e,t)=>{const{buttonActiveClass:o,buttonClass:i,buttonIcon:l,buttonTitle:a,buttonHotKey:c,dropdownTooltip:u,children:d,isActive:h,isGrayed:m,onClickWhenGrayed:v,checkable:g,isSmallTablet:p,theme:b=be,onClickButton:f,onArrowClick:T,openDropdownByClick:C,onMenuFocus:_=ge.handleAccessibleMenuFocus,onMenuKeyDown:x=ge.handleAccessibleMenuKeyDown,...E}=e,k=(0,ve.mergeThemes)(de.DEFAULT_MENU_THEME,{menuBox:b.menuBox}),[y,F]=(0,n.useState)(!1),[S,A]=(0,n.useState)(!1),B=(0,n.useRef)(null),I=(0,n.useRef)(null),W=(0,n.useRef)(null),N=(0,n.useRef)(0),D=(0,n.useRef)(0),[R,V]=(0,M.useRovingTabindexElement)(null),[H,O]=(0,M.useRovingTabindexElement)(null);return(0,n.useImperativeHandle)(t,(()=>({open:()=>F(!0)})),[]),n.createElement("div",{...E,className:s(b.dropdown,{[b.isGrayed]:m,[b.isActive]:h,[b.isOpened]:y}),onClick:m?v:void 0,onKeyDown:function(e){var t;if(e.defaultPrevented||!(e.target instanceof Node))return;const o=(0,ce.hashFromEvent)(e);if(e.currentTarget.contains(e.target)||27!==o)return;e.preventDefault(),P(!1),S&&(null===(t=null==H?void 0:H.current)||void 0===t||t.focus())},ref:B},n.createElement("div",{ref:I,className:b.control},n.createElement("div",{...function(){if(!m)return r.mobiletouch?g?{onTouchStart:U,onTouchEnd:G,onTouchMove:Z}:{onClick:j}:{onMouseDown:U,onMouseUp:K};return{}}(),className:s(b.buttonWrap,b.accessible)},n.createElement(L.ToolButton,{activeClass:o,className:s(i,b.button),icon:l,isActive:h,isGrayed:m,isTransparent:!g,ref:R,tag:"button",tabIndex:V,onClick:function(e){if(!(0,he.isKeyboardClick)(e))return;C?P(!0,!0):null==f||f()},tooltip:a,buttonHotKey:c,"data-tooltip-delay":1500,tooltipPosition:"vertical"})),!m&&!r.mobiletouch&&n.createElement("button",{className:s(b.arrow,u&&"apply-common-tooltip common-tooltip-vertical",b.accessible),onClick:function(e){null==T||T(),P(void 0,(0,he.isKeyboardClick)(e))},onKeyDown:function(e){if(e.defaultPrevented||!(e.target instanceof Node))return;const t=(0,ce.hashFromEvent)(e);if(e.currentTarget.contains(e.target))switch(t){case 39:if(y)return;e.preventDefault(),P(!0,!0);break;case 27:if(!y)return;e.preventDefault(),P(!1)}},type:"button",ref:H,tabIndex:O,"aria-pressed":h,"aria-label":u,"data-tooltip":u},n.createElement(w.Icon,{className:b.arrowIcon,icon:pe}))),!m&&(p?y&&n.createElement(me.Drawer,{className:b.drawer,onClose:z,position:"Bottom"},d):n.createElement(ue.PopupMenu,{theme:k,doNotCloseOn:function(){if(null===B.current)return[];return[B.current]},isOpened:y,onClose:z,position:function(){if(!I||!I.current)return{x:0,y:0};const e=I.current.getBoundingClientRect();return{x:e.left+e.width+1,y:e.top-6}},onKeyDown:x,onFocus:e=>_(e,H),controller:W,onOpen:function(){var e;null===(e=W.current)||void 0===e||e.focus()},tabIndex:-1},d)))
;function P(e,t=!1){const o=void 0!==e?e:!y;F(o),A(!!o&&t)}function z(){P(!1)}function j(){f&&f(),P()}function U(){if(r.mobiletouch&&!g)!D.current&&f&&f();else{if(N.current)return clearTimeout(N.current),N.current=0,void P(!0);N.current=setTimeout((()=>{N.current=0,!D.current&&f&&f()}),175)}D.current=setTimeout((()=>{D.current=0,P(!0)}),300)}function Z(){clearTimeout(D.current),D.current=0,clearTimeout(N.current),N.current=0}function G(e){e.cancelable&&e.preventDefault(),K()}function K(){D.current&&(clearTimeout(D.current),D.current=0,y?P(!1):g||y||r.mobiletouch||!h&&!C?!N.current&&f&&f():P(!0))}}));var Te=o(38297),we=o(85034),Ce=o(59063),_e=o(21097);class xe extends Ce.CommonJsonStoreService{constructor(e,t,o,n,i=18){super(_e.TVXWindowEvents,c,e,t,[]),this._onChangeDrawingState=()=>{const e=d[this._drawingType].value();this._promote(e)},this._sanitizer=o,this._drawingType=n,this._maxRecentCount=i,d[this._drawingType].subscribe(this._onChangeDrawingState)}destroy(){d[this._drawingType].unsubscribe(this._onChangeDrawingState),super.destroy()}_deserialize(e){const t=this._sanitizer(e);return this._removeUnavailableRecents(e,t)}_removeUnavailableRecents(e,t){return Array.isArray(e)?e.length>this._maxRecentCount&&(t=e.slice(0,this._maxRecentCount)):t=[],t}_promote(e){let t=[...this.get()];const o=t.indexOf(e);-1!==o&&t.splice(o,1),t=[e,...t.slice(0,this._maxRecentCount-1)],this.set(t)}}const Ee=new xe("RECENT_ICONS_CHANGED","linetoolicon.recenticons",(function(e){return e.filter((e=>le.has(e)))}),"iconTool");var ke=o(64630);function ye(e){const{fallback:t,...o}=e;return n.createElement(n.Suspense,{fallback:null!=t?t:null},n.createElement(Fe,{...o}))}const Fe=n.lazy((async()=>{const{getSvgContentForCharCode:e}=await o.e(7987).then(o.bind(o,25482));return{default:t=>{var o;const{charCode:i}=t,l=null!==(o=e(i))&&void 0!==o?o:void 0;return n.createElement(w.Icon,{icon:l,className:ke.iconContainer})}}}));var Se=o(91377);var Le=o(173);const Me=new xe("RECENT_EMOJIS_CHANGED","linetoolemoji.recents",Le.removeUnavailableEmoji,"emojiTool");var Ae;!function(e){e.Elon="elon",e.Doge="doge",e.Dislike="dislike",e.Yolo="yolo",e.Whale="whale",e.Wagmi="wagmi",e.Tendies="tendies",e.Short="short",e.Rugged="rugged",e.Shill="shill",e.Rekt="rekt",e.Sell="sell",e.PaperHands="paper-hands",e.Og="og",e.Fud="fud",e.Gm="gm",e.Ngmi="ngmi",e.Moon="moon",e.Love="love",e.Lambo="lambo",e.Ethereum="ethereum",e.Look="look",e.DiamondHand="diamond-hand",e.Leap="leap",e.Like="like",e.Few="few",e.Bitcoin="bitcoin",e.BagHolder="bag-holder",e.BuyTheDip="buy-the-dip",e.Buy="buy",e.Hodl="hodl"}(Ae||(Ae={}));const Be=["elon","doge","dislike","yolo","whale","wagmi","tendies","short","rugged","shill","rekt","sell","paper-hands","og","fud","gm","ngmi","moon","love","lambo","ethereum","look","diamond-hand","leap","like","few","bitcoin","bag-holder","buy-the-dip","buy","hodl"];var Ie=o(37603),We=o(90624);const Ne=new Set(Be);const De=[{title:W.t(null,{context:"emoji_group"},o(88906)),emojis:[],content:n.createElement(P.IconItem,{icon:Ie})},{title:"TradingView",
emojis:Be,content:n.createElement(P.IconItem,{icon:We})}],Re=new xe("RECENT_STICKERS_CHANGED","linetoolsticker.recents",(function(e){return e.filter((e=>Ne.has(e)))}),"stickerTool",3);var Ve=o(78036),He=o(47291),Oe=o(32905);var Pe,ze=o(26601),je=o(92131);!function(e){e.Icon="LineToolIcon",e.Emoji="LineToolEmoji",e.Sticker="LineToolSticker"}(Pe||(Pe={}));const Ue={[I.Icons]:{service:Ee,toolName:"LineToolIcon",ItemComponent:function(e){const{emoji:t,className:o}=e;return n.createElement("div",{className:a()(Se.item,o)},n.createElement(ye,{charCode:Number(t)}))},icons:se,onEmojiSelect:e=>{d.iconTool.setValue(Number(e)),d.tool.setValue("LineToolIcon")}},[I.Emojis]:{service:Me,toolName:"LineToolEmoji",icons:Le.emojiGroups,onEmojiSelect:e=>{d.emojiTool.setValue(e),d.tool.setValue("LineToolEmoji")}},[I.Stickers]:{service:Re,toolName:"LineToolSticker",ItemComponent:function(e){const{emoji:t}=e,{size:i}=(0,Ve.useEnsuredContext)(He.EmojiListContentContext),[l,s]=(0,n.useState)();return(0,n.useEffect)((()=>{o.e(5598).then(o.bind(o,47992)).then((({getSvgContentForSticker:e})=>{const o=e(t);o&&s(o)}))}),[]),n.createElement(w.Icon,{className:Oe.sticker,icon:null!==l?l:void 0,style:{width:`${i}px`,height:`${i}px`}})},RowComponent:function(e){return n.createElement(ze.EmojisRow,{...e,className:je.stickerRow})},icons:De,onEmojiSelect:e=>{d.stickerTool.setValue(e),d.tool.setValue("LineToolSticker")},getEmojiSize:e=>e?78:112}};var Ze=o(76086);function Ge(e){const{isSmallTablet:t,maxHeight:o,activeTab:i,setActiveTab:l}=e,a=Ue[i],{service:r,ItemComponent:c,RowComponent:u,onEmojiSelect:d,getEmojiSize:h}=a,m=h&&h(t),[v,g]=(0,n.useState)(Ke(a));return(0,n.useLayoutEffect)((()=>{const e={},t=()=>{const e=Ke(a);g(e)};return t(),r.getOnChange().subscribe(e,t),()=>{r.getOnChange().unsubscribeAll(e)}}),[a]),n.createElement("div",{style:{maxHeight:o}},n.createElement(Te.EmojiList,{className:s(Ze.desktopSize,t&&Ze.smallSize),emojis:v,onSelect:function(e){d(e),(0,S.globalCloseMenu)()},ItemComponent:c,RowComponent:u,height:o,category:i,emojiSize:m}),n.createElement(we.GroupTabs,{className:Ze.tabs,tabClassName:Ze.categories,tabs:re,activeTab:i,onTabClick:function(e){l(e)}}))}function Ke(e){const{icons:t,service:o}=e,n=[...t],i=o.get();return n[0].emojis=i.map((e=>String(e))),n.filter((e=>e.emojis.length))}var Je=o(19291),Qe=o(68335),Ye=o(3545),qe=o(59905);const $e={icon:W.t(null,void 0,o(37913)),dropdownTooltip:W.t(null,void 0,o(73829))},Xe=(0,ve.mergeThemes)(be,{menuBox:qe.menuBox,drawer:qe.drawer}),et=parseInt(Ye["default-drawer-min-top-distance"]);function tt(e){const{isGrayed:t,isSmallTablet:o}=e,i=(0,N.filterDataProps)(e),[l,s]=O(),[a]=(0,D.useWatchedValue)(d.tool),{toolName:r}=Ue[l];return n.createElement(fe,{theme:Xe,buttonIcon:ae[l],buttonTitle:$e.icon,dropdownTooltip:$e.dropdownTooltip,isActive:a===r,isGrayed:t,isSmallTablet:o,onClickButton:function(){c()},onClickWhenGrayed:()=>(0,h.emit)("onGrayedObjectClicked",{type:"drawing",name:p.lineToolsInfo[r].localizedName}),onArrowClick:function(){c("menu")},openDropdownByClick:!0,onMenuFocus:ot,
onMenuKeyDown:function(e){if(e.defaultPrevented)return;const t=(0,Qe.hashFromEvent)(e);9!==t&&t!==Qe.Modifiers.Shift+9||(0,Je.updateTabIndexes)()},...i},n.createElement(Ge,{isSmallTablet:o,maxHeight:o?Math.min(679,window.innerHeight-et):679,activeTab:l,setActiveTab:s}));function c(e){0}}function ot(e){if(!e.target)return;const t=e.currentTarget;e.target===t&&((0,Je.updateTabIndexes)(),setTimeout((()=>{if(document.activeElement!==t)return;const[e]=(0,Je.queryTabbableElements)(t).sort(Je.navigationOrderComparator);e&&e.focus()})))}var nt=o(55917);class it extends n.PureComponent{constructor(e){super(e),this._handleClick=()=>{this.props.saveDefaultOnChange&&(0,nt.allowSavingDefaults)(!0);const e=!this.props.property.value();this.props.property.setValue(e),this.props.saveDefaultOnChange&&(0,nt.allowSavingDefaults)(!1),this.props.onClick&&this.props.onClick(e)},this.state={isActive:this.props.property.value()}}componentDidMount(){this.props.property.subscribe(this,this._onChange)}componentWillUnmount(){this.props.property.unsubscribe(this,this._onChange)}render(){const{toolName:e}=this.props,{isActive:t}=this.state,o=p.lineToolsInfo[e];return n.createElement(A,{icon:t&&o.iconActive?o.iconActive:o.icon,isActive:t,onClick:this._handleClick,tooltip:o.localizedName,buttonHotKey:o.hotKey,"data-name":e})}_onChange(e){this.setState({isActive:e.value()})}}class lt extends n.PureComponent{constructor(e){super(e),this._handleClick=()=>{var e,t;d.tool.setValue(this.props.toolName),null===(t=(e=this.props).onClick)||void 0===t||t.call(e)},this._onChange=()=>{this.setState({isActive:d.tool.value()===this.props.toolName})},this.state={isActive:d.tool.value()===this.props.toolName}}componentDidMount(){d.tool.subscribe(this._onChange)}componentWillUnmount(){d.tool.unsubscribe(this._onChange)}render(){const{toolName:e}=this.props,{isActive:t}=this.state,o=p.lineToolsInfo[e];return n.createElement(A,{icon:p.lineToolsInfo[e].icon,isActive:t,isTransparent:!0,onClick:this._handleClick,tooltip:o.localizedName,buttonHotKey:o.hotKey,"data-name":e})}}class st extends n.PureComponent{constructor(e){super(e),this._boundUndoModel=null,this._handleClick=()=>{const e=this._activeChartWidget();e.hasModel()&&e.model().zoomFromViewport()},this._syncUnzoomButton=()=>{const e=this._activeChartWidget();let t=!1;if(e.hasModel()){const o=e.model();this._boundUndoModel!==o&&(this._boundUndoModel&&this._boundUndoModel.zoomStack().onChange().unsubscribe(null,this._syncUnzoomButton),o.zoomStack().onChange().subscribe(null,this._syncUnzoomButton),this._boundUndoModel=o),t=!o.zoomStack().isEmpty()}else e.withModel(null,this._syncUnzoomButton);this.setState({isVisible:t})},this.state={isVisible:!1}}componentDidMount(){this.props.chartWidgetCollection.activeChartWidget.subscribe(this._syncUnzoomButton,{callWithLast:!0})}componentWillUnmount(){this.props.chartWidgetCollection.activeChartWidget.unsubscribe(this._syncUnzoomButton)}render(){return this.state.isVisible?n.createElement(B,{action:this._handleClick,isTransparent:!0,toolName:"zoom-out"
}):n.createElement("div",null)}_activeChartWidget(){return this.props.chartWidgetCollection.activeChartWidget.value()}}var at=o(11684),rt=o(26744),ct=o(16829),ut=o(40894),dt=o(10838),ht=o(81332),mt=o(630),vt=o(92986);function gt(e){return"name"in e}class pt extends n.PureComponent{constructor(e){super(e),this._onChangeDrawingState=()=>{const e=this._getActiveToolName();this.setState({current:e||this.state.current,isActive:!!e})},this._handleClickButton=()=>{this._trackClick();const{current:e}=this.state;!v.CheckMobile.any()&&e&&this._selectTool(e)},this._handleClickItem=e=>{this._selectTool(e)},this._handleGrayedClick=e=>{(0,h.emit)("onGrayedObjectClicked",{type:"drawing",name:p.lineToolsInfo[e].localizedName})},this._handleClickFavorite=e=>{this.state.favState&&this.state.favState[e]?rt.LinetoolsFavoritesStore.removeFavorite(e):rt.LinetoolsFavoritesStore.addFavorite(e)},this._onAddFavorite=e=>{this.setState({favState:{...this.state.favState,[e]:!0}})},this._onRemoveFavorite=e=>{this.setState({favState:{...this.state.favState,[e]:!1}})},this._onSyncFavorites=()=>{this.setState({favState:this._composeFavState()})},this._handleArrowClick=()=>{this._trackClick("menu")},this._trackClick=e=>{const{trackLabel:t}=this.props};const t=this._getActiveToolName();this.state={current:t||this._firstNonGrayedTool(),favState:this._composeFavState(),isActive:!!t}}componentDidMount(){d.tool.subscribe(this._onChangeDrawingState),rt.LinetoolsFavoritesStore.favoriteAdded.subscribe(null,this._onAddFavorite),rt.LinetoolsFavoritesStore.favoriteRemoved.subscribe(null,this._onRemoveFavorite),rt.LinetoolsFavoritesStore.favoritesSynced.subscribe(null,this._onSyncFavorites)}componentWillUnmount(){d.tool.unsubscribe(this._onChangeDrawingState),rt.LinetoolsFavoritesStore.favoriteAdded.unsubscribe(null,this._onAddFavorite),rt.LinetoolsFavoritesStore.favoriteRemoved.unsubscribe(null,this._onRemoveFavorite),rt.LinetoolsFavoritesStore.favoritesSynced.unsubscribe(null,this._onSyncFavorites)}componentDidUpdate(e,t){e.lineTools!==this.props.lineTools&&this.setState({favState:this._composeFavState()})}render(){const{current:e,favState:t,isActive:o}=this.state;if(!e)return n.createElement(n.Fragment,null);const{favoriting:i,grayedTools:l,lineTools:s,dropdownTooltip:a,isSmallTablet:r}=this.props,c=this._showShortcuts(),u=p.lineToolsInfo[e],d=(0,N.filterDataProps)(this.props);return n.createElement("span",null,n.createElement(fe,{buttonIcon:u.icon,buttonTitle:u.localizedName,buttonHotKey:u.hotKey,dropdownTooltip:a,isActive:o,onClickButton:this._handleClickButton,onArrowClick:this._handleArrowClick,isSmallTablet:r,...d},s.map(((s,a)=>{var u,d;if("title"in s)return n.createElement(n.Fragment,{key:s.title},a>0?n.createElement(at.PopupMenuSeparator,null):null,n.createElement(ct.ToolWidgetMenuSummary,{className:vt.sectionTitle},s.title));const{name:h}=s,m=null===(d=null===(u=p.lineToolsInfo[h])||void 0===u?void 0:u.selectHotkey)||void 0===d?void 0:d.hash,v=p.lineToolsInfo[h],g=l[h];return n.createElement(dt.AccessibleMenuItem,{key:h,"data-name":h,
theme:r?ht.multilineLabelWithIconAndToolboxTheme:void 0,dontClosePopup:g,forceShowShortcuts:c,shortcut:!r&&m?(0,Qe.humanReadableHash)(m):void 0,icon:v.icon,isActive:o&&e===h,appearAsDisabled:g,label:v.localizedName,showToolboxOnFocus:!0,onClick:g?this._handleGrayedClick:this._handleClickItem,onClickArg:h,showToolboxOnHover:!t[h],toolbox:i&&!g?n.createElement(ut.MenuFavoriteButton,{isActive:o&&e===h,isFilled:t[h],onClick:()=>this._handleClickFavorite(h)}):void 0})}))))}_firstNonGrayedTool(){var e;const{grayedTools:t,lineTools:o}=this.props;return null===(e=o.find((e=>gt(e)&&!t[e.name])))||void 0===e?void 0:e.name}_showShortcuts(){return this.props.lineTools.some((e=>"hotkeyHash"in e))}_getActiveToolName(){var e;return null===(e=this.props.lineTools.find((e=>gt(e)&&e.name===d.tool.value())))||void 0===e?void 0:e.name}async _selectTool(e){d.tool.setValue(e)}_composeFavState(){const e={};return this.props.lineTools.forEach((t=>{gt(t)&&(e[t.name]=rt.LinetoolsFavoritesStore.isFavorite(t.name))})),e}}var bt=o(51768),ft=o(16396),Tt=o(61685);const wt=(0,ve.mergeThemes)(ft.DEFAULT_POPUP_MENU_ITEM_THEME,Tt);var Ct=o(72708);const _t=!1;class xt extends n.PureComponent{constructor(e){super(e),this._handleRemoveToolClick=()=>{r.mobiletouch||this._handleRemoveDrawings(),kt()},this._handleRemoveDrawings=()=>{Et("remove drawing"),this.props.chartWidgetCollection.activeChartWidget.value().removeAllDrawingTools()},this._handleRemoveStudies=()=>{Et("remove indicator"),this.props.chartWidgetCollection.activeChartWidget.value().removeAllStudies()},this._handleRemoveAll=()=>{Et("remove all"),this.props.chartWidgetCollection.activeChartWidget.value().removeAllStudiesDrawingTools()},this._handleActiveChartWidgetChanged=e=>{this._activeChartWidget&&this._unsubscribeToModelChanges(this._activeChartWidget),e&&this._subscribeToModelChanges(e),this._activeChartWidget=e,this._handleCollectionChanged()},this._handleCollectionChanged=()=>{this.setState(this._getActualState())},this._getActualState=()=>{if(!this._activeChartWidget||!this._activeChartWidget.hasModel())return{numOfDrawings:0,numOfIndicators:0};const e=this._activeChartWidget.model().dataSources(),t=e.filter(mt.isLineTool).filter((e=>e.isActualSymbol()&&e.isUserDeletable())),o=e.filter(Ct.isStudy).filter((e=>e.removeByRemoveAllStudies()));return{numOfDrawings:t.length,numOfIndicators:o.length}},this._activeChartWidget=this.props.chartWidgetCollection.activeChartWidget.value(),this.state=this._getActualState()}componentDidMount(){this.props.chartWidgetCollection.activeChartWidget.subscribe(this._handleActiveChartWidgetChanged,{callWithLast:!0})}componentWillUnmount(){this._activeChartWidget&&this._unsubscribeToModelChanges(this._activeChartWidget),this.props.chartWidgetCollection.activeChartWidget.unsubscribe(this._handleActiveChartWidgetChanged)}render(){const e=this.props.isSmallTablet?wt:void 0,{numOfDrawings:t,numOfIndicators:i}=this.state,l=W.t(null,{plural:"{amount} drawings",count:t,replace:{amount:t.toString()}},o(22299)),s=W.t(null,{plural:"{amount} indicators",count:i,replace:{
amount:i.toString()}},o(68984)),a=W.t(null,{replace:{drawings:l}},o(86285)),r=W.t(null,{replace:{indicators:s}},o(87797)),c=W.t(null,{replace:{drawings:l,indicators:s}},o(87796));return n.createElement(fe,{buttonIcon:p.lineToolsInfo[this.props.toolName].icon,buttonTitle:a,onClickButton:this._handleRemoveToolClick,dropdownTooltip:W.t(null,void 0,o(2671)),isSmallTablet:this.props.isSmallTablet,"data-name":this.props.toolName,onArrowClick:this._handleArrowClick,openDropdownByClick:_t},n.createElement(dt.AccessibleMenuItem,{"data-name":"remove-drawing-tools",label:a,onClick:this._handleRemoveDrawings,theme:e}),n.createElement(dt.AccessibleMenuItem,{"data-name":"remove-studies",label:r,onClick:this._handleRemoveStudies,theme:e}),n.createElement(dt.AccessibleMenuItem,{"data-name":"remove-all",label:c,onClick:this._handleRemoveAll,theme:e}))}_handleArrowClick(){kt("menu")}_subscribeToModelChanges(e){e.withModel(this,(()=>{this._handleCollectionChanged(),e.model().model().dataSourceCollectionChanged().subscribe(this,this._handleCollectionChanged)}))}_unsubscribeToModelChanges(e){e.hasModel()&&e.model().model().dataSourceCollectionChanged().unsubscribe(this,this._handleCollectionChanged),e.modelCreated().unsubscribeAll(this)}}function Et(e){(0,bt.trackEvent)("GUI","Chart Left Toolbar",e)}function kt(e){0}var yt=o(7029),Ft=o(90995),St=o(14881);const Lt=n.createContext({hideMode:"drawings",isActive:!1});function Mt(e){const{hideMode:t,option:{label:o,dataName:i,getBoxedValue:l},isSmallTablet:s,onClick:a}=e,{hideMode:r,isActive:c}=(0,n.useContext)(Lt),u=null==l?void 0:l();return"all"===t||u?n.createElement(dt.AccessibleMenuItem,{label:o,isActive:r===t&&c,onClick:function(){a(t,(0,Ft.toggleHideMode)(t))},"data-name":i,theme:s?wt:void 0}):n.createElement(n.Fragment,null)}const At={drawings:{active:U.drawingToolsIcons.hideAllDrawingToolsActive,inactive:U.drawingToolsIcons.hideAllDrawingTools},indicators:{active:U.drawingToolsIcons.hideAllIndicatorsActive,inactive:U.drawingToolsIcons.hideAllIndicators},positions:{active:U.drawingToolsIcons.hideAllPositionsToolsActive,inactive:U.drawingToolsIcons.hideAllPositionsTools},all:{active:U.drawingToolsIcons.hideAllDrawingsActive,inactive:U.drawingToolsIcons.hideAllDrawings}};function Bt(e){const{isSmallTablet:t}=e,[{isActive:i,hideMode:s},a]=(0,n.useState)((()=>({isActive:!1,hideMode:(0,Ft.getSavedHideMode)()})));(0,n.useEffect)((()=>(St.hideStateChange.subscribe(null,a),()=>{St.hideStateChange.unsubscribe(null,a)})),[]);const r=p.lineToolsInfo.hideAllDrawings,{trackLabel:c,tooltip:u,dataName:d}=(0,l.ensureDefined)((0,Ft.getHideOptions)().get(s)),h=At[s][i?"active":"inactive"],m=i?u.active:u.inactive;return n.createElement(fe,{buttonIcon:h,buttonTitle:m,buttonHotKey:r.hotKey,dropdownTooltip:yt.t(null,void 0,o(95343)),onClickButton:function(){(0,Ft.toggleHideMode)(s),It(c,!i),Wt(i?"on":"off")},isSmallTablet:t,isActive:i,checkable:!0,"data-name":"hide-all","data-type":d,onArrowClick:function(){Wt("menu")}},n.createElement(Lt.Provider,{value:{isActive:i,hideMode:s}},Array.from((0,
Ft.getHideOptions)()).map((([e,o])=>n.createElement(Mt,{key:e,hideMode:e,option:o,isSmallTablet:t,onClick:v})))));function v(e,t){It((0,l.ensureDefined)((0,Ft.getHideOptions)().get(e)).trackLabel,t)}}function It(e,t){(0,bt.trackEvent)("GUI","Chart Left Toolbar",`${e} ${t?"on":"off"}`)}function Wt(e){0}var Nt=o(9726),Dt=o(53573);const Rt=W.t(null,void 0,o(51465));class Vt extends n.PureComponent{constructor(){super(...arguments),this._instance=null,this._promise=null,this._bindedForceUpdate=()=>this.forceUpdate(),this._handleClick=()=>{null!==this._instance&&(this._instance.isVisible()?(this._instance.hideAndSaveSettingsValue(),this._trackClick(!1)):(this._instance.showAndSaveSettingsValue(),this._trackClick(!0)))}}componentDidMount(){const e=this._promise=(0,l.ensureNotNull)((0,Nt.getFavoriteDrawingToolbarPromise)());e.then((t=>{this._promise===e&&(this._instance=t,this._instance.canBeShown().subscribe(this._bindedForceUpdate),this._instance.visibility().subscribe(this._bindedForceUpdate),this.forceUpdate())}))}componentWillUnmount(){this._promise=null,null!==this._instance&&(this._instance.canBeShown().unsubscribe(this._bindedForceUpdate),this._instance.visibility().unsubscribe(this._bindedForceUpdate),this._instance=null)}render(){return null!==this._instance&&this._instance.canBeShown().value()?n.createElement(A,{id:this.props.id,icon:Dt,isActive:this._instance.isVisible(),onClick:this._handleClick,tooltip:Rt}):null}_trackClick(e){0}}var Ht=o(77975),Ot=o(92693),Pt=o(81171),zt=o(48770);const jt={[Ot.MagnetMode.WeakMagnet]:{id:Ot.MagnetMode.WeakMagnet,name:"weakMagnet",icon:U.drawingToolsIcons.magnet,localizedName:W.t(null,void 0,o(3519))},[Ot.MagnetMode.StrongMagnet]:{id:Ot.MagnetMode.StrongMagnet,name:"strongMagnet",icon:U.drawingToolsIcons.strongMagnet,localizedName:W.t(null,void 0,o(94593))}};function Ut(e){const{isSmallTablet:t}=e,i=(0,Ht.useWatchedValueReadonly)({watchedValue:(0,Pt.magnetEnabled)()}),l=(0,Ht.useWatchedValueReadonly)({watchedValue:(0,Pt.magnetMode)()});return n.createElement("div",{className:zt.toolButtonMagnet},n.createElement(fe,{"data-name":"magnet-button",buttonIcon:jt[l].icon,buttonTitle:p.lineToolsInfo.magnet.localizedName,dropdownTooltip:W.t(null,void 0,o(41964)),isActive:i,onClickButton:function(){const e=!i;(0,bt.trackEvent)("GUI","Chart Left Toolbar","magnet mode "+(e?"on":"off")),!1;(0,Pt.setIsMagnetEnabled)(e)},buttonHotKey:p.lineToolsInfo.magnet.hotKey,checkable:!0,isSmallTablet:t,onArrowClick:function(){0}},Object.values(jt).map((({id:e,name:o,localizedName:a,icon:r})=>n.createElement(dt.AccessibleMenuItem,{key:e,className:t?zt.toolButtonMagnet__menuItem:void 0,"data-name":o,icon:r,isActive:i&&l===e,label:a,onClick:s,onClickArg:e})))),!1);function s(e){void 0!==e&&((0,bt.trackEvent)("GUI","Magnet mode",e===Ot.MagnetMode.WeakMagnet?"Weak":"Strong"),(0,Pt.setMagnetMode)(e))}}var Zt;!function(e){e.Screenshot="drawing-toolbar-screenshot",e.FavoriteDrawings="drawing-toolbar-favorite-drawings",e.ObjectTree="drawing-toolbar-object-tree"}(Zt||(Zt={}))
;var Gt=o(70412),Kt=o(14729),Jt=o(27235),Qt=o(29197),Yt=o(6190),qt=o(9243);const $t=qt,Xt="http://www.w3.org/2000/svg";function eo(e){const{direction:t,theme:o=qt}=e;return n.createElement("svg",{xmlns:Xt,width:"9",height:"27",viewBox:"0 0 9 27",className:s(o.container,"right"===t?o.mirror:null),onContextMenu:Kt.preventDefault},n.createElement("g",{fill:"none",fillRule:"evenodd"},n.createElement("path",{className:o.background,d:"M4.5.5a4 4 0 0 1 4 4v18a4 4 0 1 1-8 0v-18a4 4 0 0 1 4-4z"}),n.createElement("path",{className:o.arrow,d:"M5.5 10l-2 3.5 2 3.5"})))}var to=o(39416),oo=o(7047),no=o(43955);const io=(0,ve.mergeThemes)($t,no),lo={hide:W.t(null,void 0,o(99838)),show:W.t(null,void 0,o(32579))},so=(0,n.forwardRef)(((e,t)=>{const{toolbarVisible:o,"data-name":i}=e,l=(0,to.useFunctionalRefObject)(t);return n.createElement("button",{...oo.MouseClickAutoBlurHandler.attributes(),ref:l,type:"button","aria-label":o?lo.hide:lo.show,"data-tooltip":o?lo.hide:lo.show,className:s(io.toggleButton,"apply-common-tooltip common-tooltip-vertical",!o&&io.collapsed,io.accessible),onClick:function(){T.isDrawingToolbarVisible.setValue(!T.isDrawingToolbarVisible.value())},"data-name":i,"data-value":o?"visible":"collapsed"},n.createElement(eo,{direction:o?"left":"right",theme:o?void 0:io}))}));var ao=o(37558),ro=o(24437),co=o(90692);const uo={chartWidgetCollection:o(19036).any.isRequired};var ho=o(77151),mo=o(88436);u.enabled("saveload_separate_drawings_storage");const vo=u.enabled("right_toolbar"),go=u.enabled("keep_object_tree_widget_in_right_toolbar"),po=(0,v.onWidget)(),bo=new m.Delegate,fo=bt.trackEvent.bind(null,"GUI","Chart Left Toolbar"),To=(e,t)=>fo(`${e} ${t?"on":"off"}`);class wo extends n.PureComponent{constructor(e){var t;super(e),this._grayedTools={},this._handleMeasureClick=()=>{Co("measure")},this._handleZoomInClick=()=>{Co("zoom in")},this._handleDrawingClick=e=>{To("drawing mode",e),Co("drawing mode",e?"on":"off")},this._handleLockClick=e=>{To("lock all drawing",e),Co("lock",e?"on":"off")},this._handleSyncClick=e=>{To("sync",e),Co("sync",e?"on":"off")},this._handleObjectsTreeClick=()=>{this._activeChartWidget().showObjectsTreeDialog(),Co("object tree")},this._handleMouseOver=e=>{(0,Gt.hoverMouseEventFilter)(e)&&this.setState({isHovered:!0})},this._handleMouseOut=e=>{(0,Gt.hoverMouseEventFilter)(e)&&this.setState({isHovered:!1})},this._handleChangeVisibility=e=>{this.setState({isVisible:e})},this._handleEsc=()=>{d.resetToCursor(!0)},this._handleWidgetbarSettled=e=>{var t;this.setState({isWidgetbarVisible:Boolean(null===(t=window.widgetbar)||void 0===t?void 0:t.visible().value()),widgetbarSettled:e})},this._handleWidgetbarVisible=e=>{this.setState({isWidgetbarVisible:e})},d.init(),this._toolsFilter=new b(this.props.drawingsAccess),this._filteredLineTools=f.lineTools.reduce(((e,t)=>{const{id:o,title:n,trackLabel:i}=t,l=e=>this._toolsFilter.isToolEnabled(p.lineToolsInfo[e.name].localizedName),s=[];return(0,f.isLineToolsGroupWithSections)(t)?t.sections.forEach((e=>{const t=e.items.filter(l);t.length&&s.push({title:e.title
},...t)})):s.push(...t.items.filter(l)),s.length&&e.push({id:o,title:n,trackLabel:i,items:s}),e}),[]),this._filteredLineTools.forEach((e=>{e.items.forEach((e=>{"title"in e||(this._grayedTools[e.name]=this._toolsFilter.isToolGrayed(p.lineToolsInfo[e.name].localizedName))}))})),this.state={isHovered:!1,isVisible:T.isDrawingToolbarVisible.value(),isWidgetbarVisible:Boolean(null===(t=window.widgetbar)||void 0===t?void 0:t.visible().value()),widgetbarSettled:void 0!==window.widgetbar},this._features={favoriting:!this.props.readOnly&&!po&&u.enabled("items_favoriting"),multicharts:u.enabled("support_multicharts"),tools:!po||u.enabled("charting_library_base")},this._registry={chartWidgetCollection:this.props.chartWidgetCollection},this._negotiateResizer()}componentDidMount(){var e;T.isDrawingToolbarVisible.subscribe(this._handleChangeVisibility),S.globalCloseDelegate.subscribe(this,this._handleGlobalClose),this._tool=d.tool.spawn(),this._tool.subscribe(this._updateHotkeys.bind(this)),this._initHotkeys(),this.props.widgetbarSettled&&(this.props.widgetbarSettled.subscribe(this,this._handleWidgetbarSettled),v.CheckMobile.any()&&(null===(e=window.widgetbar)||void 0===e||e.visible().subscribe(this._handleWidgetbarVisible)))}componentWillUnmount(){var e;null===(e=window.widgetbar)||void 0===e||e.visible().unsubscribe(this._handleWidgetbarVisible),T.isDrawingToolbarVisible.unsubscribe(this._handleChangeVisibility),S.globalCloseDelegate.unsubscribe(this,this._handleGlobalClose),this._tool.destroy(),this._hotkeys.destroy()}componentDidUpdate(e,t){var o;const{isVisible:n,widgetbarSettled:i}=this.state;n!==t.isVisible&&(h.emit("toggle_sidebar",!n),c.setValue("ChartDrawingToolbarWidget.visible",n),this._negotiateResizer()),t.widgetbarSettled!==i&&i&&v.CheckMobile.any()&&(null===(o=window.widgetbar)||void 0===o||o.visible().subscribe(this._handleWidgetbarVisible))}render(){const{bgColor:e,chartWidgetCollection:t,readOnly:o}=this.props,{isHovered:i,isVisible:l}=this.state,a={backgroundColor:e&&`#${e}`};let c;c=n.createElement(so,{toolbarVisible:l,"data-name":"toolbar-drawing-toggle-button"});const h=()=>!!this._features.tools&&!(!u.enabled("show_object_tree")||go&&!vo);return n.createElement(ho.RegistryProvider,{validation:uo,value:this._registry},n.createElement(Qt.CloseDelegateContext.Provider,{value:bo},n.createElement(ao.DrawerManager,null,n.createElement(co.MatchMedia,{rule:ro.DialogBreakpoints.TabletSmall},(e=>n.createElement(Yt.Toolbar,{id:"drawing-toolbar",className:s(mo.drawingToolbar,{[mo.isHidden]:!l}),style:a,onClick:this.props.onClick,onContextMenu:Kt.preventDefaultForContextMenu,orientation:"vertical"},n.createElement(y,{onScroll:this._handleGlobalClose,isVisibleFade:r.mobiletouch,isVisibleButtons:!r.mobiletouch&&i,isVisibleScrollbar:!1,onMouseOver:this._handleMouseOver,onMouseOut:this._handleMouseOut},n.createElement("div",{className:mo.inner},!o&&n.createElement("div",{className:mo.group,style:a},this._filteredLineTools.map((o=>n.createElement(pt,{key:o.id,"data-name":o.id,chartWidgetCollection:t,
favoriting:this._features.favoriting&&!("linetool-group-cursors"===o.id&&(0,g.isOnMobileAppPage)("any")),grayedTools:this._grayedTools,dropdownTooltip:o.title,lineTools:o.items,isSmallTablet:e,trackLabel:o.trackLabel}))),this._toolsFilter.isToolEnabled("Font Icons")&&n.createElement(tt,{"data-name":"linetool-group-font-icons",isGrayed:this._grayedTools["Font Icons"],isSmallTablet:e})),!o&&n.createElement("div",{className:mo.group,style:a},n.createElement(lt,{toolName:"measure",onClick:this._handleMeasureClick}),n.createElement(lt,{toolName:"zoom",onClick:this._handleZoomInClick}),n.createElement(st,{chartWidgetCollection:t})),!o&&n.createElement("div",{className:mo.group,style:a},n.createElement(Ut,{isSmallTablet:e}),this._features.tools&&n.createElement(it,{property:d.properties().childs().stayInDrawingMode,saveDefaultOnChange:!0,toolName:"drawginmode",onClick:this._handleDrawingClick}),this._features.tools&&n.createElement(it,{property:d.lockDrawings(),toolName:"lockAllDrawings",onClick:this._handleLockClick}),this._features.tools&&n.createElement(Bt,{isSmallTablet:e}),!1),!o&&this._features.tools&&n.createElement("div",{className:mo.group,style:a},n.createElement(xt,{chartWidgetCollection:t,isSmallTablet:e,toolName:"removeAllDrawingTools"})),n.createElement("div",{className:mo.fill,style:a}),!o&&(this._features.tools||!1)&&n.createElement("div",{className:s(mo.group,mo.lastGroup),style:a},!1,this._features.tools&&this._features.favoriting&&n.createElement(Vt,{id:Zt.FavoriteDrawings}),h()&&n.createElement(B,{id:Zt.ObjectTree,action:this._handleObjectsTreeClick,toolName:"showObjectsTree"}))))))),c)))}_activeChartWidget(){return this.props.chartWidgetCollection.activeChartWidget.value()}_negotiateResizer(){const e=Jt.TOOLBAR_WIDTH_COLLAPSED;this.props.resizerBridge.negotiateWidth(this.state.isVisible?Jt.TOOLBAR_WIDTH_EXPANDED:e)}_handleGlobalClose(){bo.fire()}_updateHotkeys(){this._hotkeys.promote()}_initHotkeys(){this._hotkeys=F.createGroup({desc:"Drawing Toolbar"}),this._hotkeys.add({desc:"Reset",hotkey:27,handler:()=>this._handleEsc(),isDisabled:()=>d.toolIsCursor(d.tool.value())})}}function Co(e,t){0}class _o{constructor(e,t){this._component=null,this._handleRef=e=>{this._component=e},this._container=e,i.render(n.createElement(wo,{...t,ref:this._handleRef}),this._container)}destroy(){i.unmountComponentAtNode(this._container)}getComponent(){return(0,l.ensureNotNull)(this._component)}}},59511:(e,t,o)=>{"use strict";o.d(t,{isLineToolsGroupWithSections:()=>r,lineTools:()=>a,lineToolsFlat:()=>c});var n=o(11542),i=o(49483),l=o(56570),s=o(37265);const a=[{id:"linetool-group-cursors",title:n.t(null,void 0,o(81578)),items:[{name:"cursor"},{name:"dot"},{name:"arrow"},{name:"eraser"},null].filter(s.isExistent),trackLabel:null},{id:"linetool-group-trend-line",title:n.t(null,void 0,o(48773)),sections:[{title:n.t(null,void 0,o(56982)),items:[{name:"LineToolTrendLine"},{name:"LineToolRay"},{name:"LineToolInfoLine"},{name:"LineToolExtended"},{name:"LineToolTrendAngle"},{name:"LineToolHorzLine"},{name:"LineToolHorzRay"},{
name:"LineToolVertLine"},{name:"LineToolCrossLine"}]},{title:n.t(null,void 0,o(59934)),items:[{name:"LineToolParallelChannel"},{name:"LineToolRegressionTrend"},{name:"LineToolFlatBottom"},{name:"LineToolDisjointAngle"}]},{title:n.t(null,void 0,o(36167)),items:[{name:"LineToolPitchfork"},{name:"LineToolSchiffPitchfork2"},{name:"LineToolSchiffPitchfork"},{name:"LineToolInsidePitchfork"}]}],trackLabel:null},{id:"linetool-group-gann-and-fibonacci",title:n.t(null,void 0,o(2654)),sections:[{title:n.t(null,void 0,o(26578)),items:[{name:"LineToolFibRetracement"},{name:"LineToolTrendBasedFibExtension"},{name:"LineToolFibChannel"},{name:"LineToolFibTimeZone"},{name:"LineToolFibSpeedResistanceFan"},{name:"LineToolTrendBasedFibTime"},{name:"LineToolFibCircles"},{name:"LineToolFibSpiral"},{name:"LineToolFibSpeedResistanceArcs"},{name:"LineToolFibWedge"},{name:"LineToolPitchfan"}]},{title:n.t(null,void 0,o(51494)),items:[{name:"LineToolGannSquare"},{name:"LineToolGannFixed"},{name:"LineToolGannComplex"},{name:"LineToolGannFan"}]}],trackLabel:null},{id:"linetool-group-patterns",title:n.t(null,void 0,o(46417)),sections:[{title:n.t(null,void 0,o(46417)),items:[{name:"LineTool5PointsPattern"},{name:"LineToolCypherPattern"},{name:"LineToolHeadAndShoulders"},{name:"LineToolABCD"},{name:"LineToolTrianglePattern"},{name:"LineToolThreeDrivers"}]},{title:n.t(null,void 0,o(44255)),items:[{name:"LineToolElliottImpulse"},{name:"LineToolElliottCorrection"},{name:"LineToolElliottTriangle"},{name:"LineToolElliottDoubleCombo"},{name:"LineToolElliottTripleCombo"}]},{title:n.t(null,void 0,o(77915)),items:[{name:"LineToolCircleLines"},{name:"LineToolTimeCycles"},{name:"LineToolSineLine"}]}],trackLabel:null},{id:"linetool-group-prediction-and-measurement",title:n.t(null,void 0,o(1410)),sections:[{title:n.t(null,void 0,o(75747)),items:[{name:"LineToolRiskRewardLong"},{name:"LineToolRiskRewardShort"},{name:"LineToolPrediction"},{name:"LineToolBarsPattern"},{name:"LineToolGhostFeed"},{name:"LineToolProjection"}].filter(s.isExistent)},{title:n.t(null,void 0,o(69260)),items:[{name:"LineToolAnchoredVWAP"},{name:"LineToolFixedRangeVolumeProfile"},null].filter(s.isExistent)},{title:n.t(null,void 0,o(97050)),items:[{name:"LineToolPriceRange"},{name:"LineToolDateRange"},{name:"LineToolDateAndPriceRange"}]}],trackLabel:null},{id:"linetool-group-geometric-shapes",title:n.t(null,void 0,o(22145)),sections:[{title:n.t(null,void 0,o(65695)),items:[{name:"LineToolBrush"},{name:"LineToolHighlighter"}]},{title:n.t(null,void 0,o(19147)),items:[{name:"LineToolArrowMarker"},{name:"LineToolArrow"},{name:"LineToolArrowMarkUp"},{name:"LineToolArrowMarkDown"},{name:"LineToolArrowMarkLeft"},{name:"LineToolArrowMarkRight"}].filter(s.isExistent)},{title:n.t(null,void 0,o(65781)),items:[{name:"LineToolRectangle"},{name:"LineToolRotatedRectangle"},{name:"LineToolPath"},{name:"LineToolCircle"},{name:"LineToolEllipse"},{name:"LineToolPolyline"},{name:"LineToolTriangle"},{name:"LineToolArc"},{name:"LineToolBezierQuadro"},{name:"LineToolBezierCubic"}]}],trackLabel:null},{
id:"linetool-group-annotation",title:n.t(null,void 0,o(32064)),sections:[{title:n.t(null,void 0,o(65831)),items:[{name:"LineToolText"},{name:"LineToolTextAbsolute"},{name:"LineToolNote"},{name:"LineToolNoteAbsolute"},{name:"LineToolCallout"},{name:"LineToolComment"},{name:"LineToolPriceLabel"},{name:"LineToolPriceNote"},{name:"LineToolSignpost"},{name:"LineToolFlagMark"}]},{title:n.t(null,void 0,o(93111)),items:[!(0,i.onWidget)()&&l.enabled("image_drawingtool")?{name:"LineToolImage"}:null,null,null].filter(s.isExistent)}],trackLabel:null}];function r(e){return"sections"in e}const c=a.map((function(e){return r(e)?e.sections.map((e=>e.items)).flat():e.items})).flat()},77151:(e,t,o)=>{"use strict";o.d(t,{RegistryProvider:()=>r,registryContextType:()=>c,validateRegistry:()=>a});var n=o(50959),i=o(19036),l=o.n(i);const s=n.createContext({});function a(e,t){l().checkPropTypes(t,e,"context","RegistryContext")}function r(e){const{validation:t,value:o}=e;return a(o,t),n.createElement(s.Provider,{value:o},e.children)}function c(){return s}},53573:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path fill="currentColor" fill-rule="evenodd" d="m17.13 9.74 7.37.9-5.44 5.06L20.4 23 14 19.38 7.6 23l1.34-7.3-5.44-5.06 7.37-.9L14 3l3.13 6.74Zm5.11 1.63-4.26 3.97 1.04 5.74L14 18.24l-5.02 2.84 1.04-5.74-4.26-3.97 5.79-.7L14 5.37l2.45 5.3 5.8.7Z"/></svg>'},61380:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 10" width="20" height="10"><path fill="none" stroke="currentColor" stroke-width="1.5" d="M2 1l8 8 8-8"/></svg>'},51609:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="M4.31 14.5a1.07 1.07 0 0 1 0-1.5L13 4.3c.42-.41 1.1-.41 1.52 0l.99 1c.42.42.41 1.11-.02 1.53l-5.38 5.12h12.83c.6 0 1.07.48 1.07 1.07v1.43c0 .6-.48 1.07-1.07 1.07H10.1l5.38 5.13c.44.41.45 1.1.02 1.53l-1 .99c-.41.42-1.1.42-1.5 0L4.3 14.5Zm7.97 9.38-8.67-8.67c-.81-.8-.82-2.12 0-2.93l8.68-8.67c.8-.81 2.12-.82 2.92 0l1 .99c.82.82.8 2.16-.04 2.96l-3.57 3.4h10.33c1.14 0 2.07.93 2.07 2.07v1.43c0 1.15-.93 2.07-2.07 2.07H12.6l3.57 3.4c.84.8.86 2.14.03 2.97l-.99.99c-.8.8-2.12.8-2.93 0Z"/></svg>'},22976:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M4.87 4.52a.5.5 0 0 1 .61.35L6.91 10h5.47l1.03-4.67c.14-.63 1.04-.63 1.18 0L15.62 10h5.47l1.43-5.13a.5.5 0 0 1 .96.26L22.13 10H25a.5.5 0 0 1 0 1h-3.15l-.83 3H25a.5.5 0 0 1 0 1h-4.26l-2.15 7.75c-.17.6-1.03.58-1.16-.03L15.7 15h-3.42l-1.72 7.72c-.13.6-1 .63-1.16.03L7.26 15H3a.5.5 0 1 1 0-1h3.98l-.83-3H3a.5.5 0 1 1 0-1h2.87L4.52 5.13a.5.5 0 0 1 .35-.61ZM7.19 11l.83 3h3.47l.66-3H7.2Zm5.99 0-.67 3h2.98l-.67-3h-1.64Zm1.42-1L14 7.3l-.6 2.7h1.2Zm1.25 1 .66 3h3.47l.83-3h-4.96Zm3.85 4h-2.97l1.32 5.94L19.7 15Zm-8.43 0H8.3l1.65 5.94L11.27 15Z"/></svg>'},70616:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path stroke="currentColor" d="M7.5 24v-5.5m0 0s2.7-1.1 4.5-1c2.1.12 2.9 1.88 5 2 1.8.1 4.5-1 4.5-1v-6m-14 6v-6m0 0v-6s2.7-1.1 4.5-1c2.1.12 2.9 1.88 5 2 1.8.1 4.5-1 4.5-1v6m-14 0s2.7-1.1 4.5-1c2.1.12 2.9 1.88 5 2 1.8.1 4.5-1 4.5-1"/></svg>'},48748:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M14.08 3.73c.1.16.1.37 0 .54a9.4 9.4 0 0 0 3.35 13.26 9.9 9.9 0 0 0 6.49 1.18.5.5 0 0 1 .5.76 10.67 10.67 0 0 1-3.83 3.64 10.91 10.91 0 0 1-14.28-3.3A10.44 10.44 0 0 1 8.69 5.56a10.86 10.86 0 0 1 4.9-2.06.5.5 0 0 1 .49.22Zm8.3 15.61v.5c-1.91 0-3.8-.5-5.45-1.44a10.64 10.64 0 0 1-3.95-3.97 10.4 10.4 0 0 1-.3-9.72 9.6 9.6 0 0 0-6.37 5.39 9.39 9.39 0 0 0 .83 9.14 9.7 9.7 0 0 0 3.6 3.17 9.92 9.92 0 0 0 12.21-2.59c-.19.02-.38.02-.57.02v-.5Z"/></svg>'},18042:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="M6 11.69C6 7.46 9.56 4 14 4c4.44 0 8 3.46 8 7.69 0 2.63-1.2 4.93-3.25 6.31H14.5v-5H18v-1h-8v1h3.5v5H9.14A8.06 8.06 0 0 1 6 11.69Zm2 6.67a9.1 9.1 0 0 1-3-6.67C5 6.87 9.05 3 14 3s9 3.87 9 8.69a8.51 8.51 0 0 1-3 6.62V22h-2v3h-8v-3H8v-3.64ZM11 22v2h6v-2h-6Zm-2-1v-2h10v2H9Z"/></svg>'},44986:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M6 14.5C6 9.78 9.78 6 14.5 6c4.72 0 8.5 3.78 8.5 8.5 0 4.72-3.78 8.5-8.5 8.5A8.46 8.46 0 0 1 6 14.5ZM14.5 5A9.46 9.46 0 0 0 5 14.5c0 5.28 4.22 9.5 9.5 9.5s9.5-4.22 9.5-9.5S19.78 5 14.5 5ZM14 16V9h1v6h4v1h-5Z"/></svg>'},83778:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M6 14.5C6 9.78 9.78 6 14.5 6c4.72 0 8.5 3.78 8.5 8.5 0 4.72-3.78 8.5-8.5 8.5A8.46 8.46 0 0 1 6 14.5ZM14.5 5A9.46 9.46 0 0 0 5 14.5c0 5.28 4.22 9.5 9.5 9.5s9.5-4.22 9.5-9.5S19.78 5 14.5 5ZM12 12a1 1 0 1 0 0 2 1 1 0 0 0 0-2Zm4 1a1 1 0 1 1 2 0 1 1 0 0 1-2 0Zm-6 4-.43.26v.01l.03.03a3.55 3.55 0 0 0 .3.4 5.7 5.7 0 0 0 9.22 0 5.42 5.42 0 0 0 .28-.4l.02-.03v-.01L19 17l-.43-.26v.02a2.45 2.45 0 0 1-.24.32c-.17.21-.43.5-.78.79a4.71 4.71 0 0 1-6.88-.8 4.32 4.32 0 0 1-.23-.31l-.01-.02L10 17Z"/></svg>'},90624:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 112 112" width="28" height="28"><path fill="#fff" d="M63.42 93.22a37.13 37.13 0 1 0 .01-74.27 37.13 37.13 0 0 0-.01 74.27Z"/><path fill="#fff" d="M45.48 48.85c-.71.04-1.96 0-3.17.2-2.36.41-4.72.85-7.03 1.51a30.65 30.65 0 0 0-4.87 2.02c-1.9.9-3.74 1.93-5.59 2.94-.66.36-.71.86-.16 1.39.53.53 1.1 1.01 1.7 1.44 2.43 1.63 4.91 3.15 7.3 4.85 2.77 1.95 5.86 3.03 8.95 4.03 3.5 1.14 7.15.85 10.72.38 4.05-.54 8.1-1.3 11.9-2.96 2.17-.95 4.21-2.22 6.27-3.44.88-.5.86-.86.08-1.5-1.59-1.28-3.16-2.6-4.82-3.78-3.73-2.66-7.65-4.85-12.05-6a29.47 29.47 0 0 0-9.23-1.08Zm6.56-21.95v8.8c0 1.1-.02 2.18-.03 3.27 0 .86.33 1.39 1.14 1.47.38.04.77.06 1.16.11 2.8.35 3.14.13 3.99-2.86.77-2.7 1.47-5.44 2.22-8.15.31-1.12.5-1.18 1.5-.79 1.98.78 3.95 1.58 5.94 2.32.77.29 1.03.6.7 1.56-.98 2.94-1.86 5.92-2.77 8.89-.09.28-.15.57-.21.86-.42 2.02-.37 2.12 1.37 2.8.25.1.5.21.74.34.51.3.91.26 1.38-.19 2.34-2.22 4.75-4.34 7.05-6.6.74-.73 1.57-.62 2.16-.04A83.06 83.06 0 0 1 82 42.52c.64.73.6 1.52-.04 2.3a273.4 273.4 0 0 1-4.69 5.62c-.46.53-.44.98-.02 1.44 1.46 1.55 2.93 3.1 4.4 4.63 1.1 1.13 2.21 2.24 3.3 3.37 1.05 1.07 1.12 1.67.06 2.77-1.44 1.5-2.86 3.08-4.51 4.23a87.09 87.09 0 0 1-10 6.28 32.38 32.38 0 0 1-12.28 3.5c-4.54.36-9.07.43-13.57-.15a59.04 59.04 0 0 1-9.69-2.07 38.4 38.4 0 0 1-8.35-3.83 51.59 51.59 0 0 1-5.8-4.13 73.78 73.78 0 0 1-6.18-5.38c-1.29-1.3-2.33-2.9-3.38-4.46-.58-.84-.06-1.55.59-2.1 1.14-.96 2.32-1.9 3.42-2.9.72-.65.95-.96 1.62-1.67.5-.53.43-1.02-.07-1.51-1.3-1.3-1.52-1.76-2.83-3.07-.6-.59-.74-1.1-.07-1.79 1.66-1.72 4.35-4.22 5.97-5.98.8-.86.9-.82 1.7.12 1.6 1.9 2.12 2.97 3.78 4.83.87.98 1.19 1.55 2.5 1.04 2.37-.95 1.76-.7 1.05-3.35-.64-2.37-1-2.96-1.72-5.3-.08-.26-.17-.5-.23-.75-.33-1.2-.3-1.33.8-1.7 2.06-.68 5.56-1.72 7.62-2.4.8-.27 1.16.18 1.39.93.73 2.55 1.01 3.38 1.77 5.92.2.72.48 1.41.84 2.05.7 1.18 1.13 1.4 2.27 1.36 1.96-.07 2.24-.3 2.24-2.45 0-3.1-.06-6.21-.14-9.32-.04-1.53-.07-1.62 1.34-1.66 2.3-.06 4.61-.02 6.96-.02"/><path fill="#2962FF" d="M63.42 90.92a34.26 34.26 0 1 0 .01-68.52 34.26 34.26 0 0 0-.01 68.52Z"/><path fill="#FF5200" d="M45.69 49.83c-.67.03-1.83 0-2.95.17-2.2.35-4.4.72-6.54 1.28-1.56.4-3.06 1.05-4.53 1.7-1.76.77-3.47 1.64-5.2 2.49-.6.3-.66.73-.15 1.17.5.45 1.03.86 1.59 1.22 2.26 1.37 4.56 2.66 6.79 4.1 2.57 1.64 5.45 2.55 8.31 3.4 3.26.96 6.65.72 9.98.32 3.76-.46 7.52-1.1 11.06-2.5 2.01-.8 3.92-1.88 5.82-2.9.82-.44.8-.74.08-1.27-1.48-1.09-2.94-2.2-4.48-3.2-3.47-2.25-7.11-4.1-11.2-5.06a30.03 30.03 0 0 0-8.59-.91v-.01Zm6.09-18.54v7.44l-.02 2.76c0 .72.3 1.17 1.05 1.24.36.03.73.05 1.08.1 2.6.29 2.92.1 3.71-2.43.72-2.28 1.37-4.59 2.07-6.88.29-.94.45-1 1.4-.66 1.84.66 3.66 1.33 5.52 1.95.7.25.95.52.64 1.32-.9 2.48-1.72 5-2.57 7.5-.08.25-.14.5-.2.74-.38 1.7-.34 1.79 1.28 2.37.23.08.47.17.7.28.47.26.84.22 1.27-.16 2.18-1.87 4.42-3.67 6.56-5.58.69-.61 1.46-.52 2-.03a73.41 73.41 0 0 1 3.37 3.24c.6.6.56 1.28-.03 1.94-1.44 1.6-2.89 3.18-4.37 4.74-.43.46-.4.83-.01 1.22a340.4 340.4 0 0 0 4.1 3.91c1 .96 2.04 1.9 3.06 2.85.97.9 1.03 1.41.05 2.34-1.34 1.26-2.66 2.6-4.2 3.57a82.59 82.59 0 0 1-9.29 5.3 32.44 32.44 0 0 1-11.42 2.97c-4.22.3-8.43.36-12.62-.13a59.71 59.71 0 0 1-9-1.75c-2.76-.77-5.3-1.91-7.77-3.24a48.2 48.2 0 0 1-5.39-3.49c-2-1.4-3.92-2.92-5.75-4.54-1.2-1.09-2.17-2.45-3.15-3.76-.53-.72-.05-1.31.55-1.78 1.06-.82 2.16-1.6 3.18-2.45.67-.55 1.27-1.17 1.9-1.77.46-.45.4-.86-.07-1.28l-3.64-3.32c-.55-.5-.68-.93-.05-1.51 1.53-1.46 3.01-2.98 4.52-4.46.74-.72.84-.7 1.58.1 1.5 1.61 2.98 3.24 4.51 4.8.82.84 1.75 1.09 2.96.65 2.21-.8 2.3-.73 1.63-2.97-.6-2-1.32-3.96-2-5.93-.07-.22-.16-.42-.21-.63-.3-1.02-.28-1.12.74-1.43 1.92-.59 3.85-1.11 5.77-1.69.75-.23 1.08.15 1.3.78.67 2.16 1.33 4.32 2.04 6.46.18.61.44 1.2.78 1.74.66 1 1.72.98 2.78.94 1.83-.06 2.09-.25 2.09-2.07 0-2.62-.06-5.25-.13-7.87-.04-1.3-.07-1.37 1.24-1.4 2.14-.06 4.29-.02 6.47-.02"/><path fill="#FDD600" d="m53.5 54.08.15-.32c-.5-.49-.91-1.15-1.5-1.44a9.83 9.83 0 0 0-6.84-.8c-1.95.5-3.23 1.92-4.14 3.57-.98 1.8-1.33 3.8-.09 5.64.54.8 1.38 1.44 2.16 2.04a6.98 6.98 0 0 0 10.61-2.68c.4-.87.27-1.18-.66-1.48-.98-.31-1.98-.59-2.96-.9-.65-.22-1.31-.44-1.31-1.3 0-.82.53-1.15 1.24-1.35 1.12-.3 2.23-.65 3.34-.97Zm-7.81-4.25c3.23-.15 5.9.29 8.58.92 4.08.96 7.73 2.8 11.21 5.06 1.54.99 3 2.1 4.48 3.2.72.53.74.82-.08 1.26-1.91 1.03-3.82 2.1-5.82 2.9-3.54 1.4-7.3 2.04-11.07 2.5-3.32.4-6.72.65-9.97-.31-2.87-.85-5.74-1.76-8.32-3.41-2.22-1.43-4.52-2.72-6.78-4.1a12 12 0 0 1-1.6-1.21c-.5-.45-.45-.86.17-1.18 1.72-.86 3.43-1.72 5.19-2.48 1.48-.65 2.97-1.3 4.52-1.7 2.16-.56 4.35-.93 6.55-1.28 1.12-.18 2.28-.14 2.94-.18"/><path fill="#1D1D1B" d="M53.5 54.08c-1.11.33-2.22.67-3.34.98-.71.19-1.24.52-1.24 1.34 0 .86.67 1.1 1.3 1.3.99.32 1.99.6 2.97.9.93.3 1.05.61.66 1.49a6.98 6.98 0 0 1-10.62 2.68 9.18 9.18 0 0 1-2.16-2.04c-1.24-1.85-.9-3.85.1-5.65.9-1.65 2.18-3.07 4.13-3.57a9.84 9.84 0 0 1 6.84.8c.6.3 1.01.95 1.5 1.44l-.15.33"/></svg>'
},14665:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 10 16" width="10" height="16"><path d="M.6 1.4l1.4-1.4 8 8-8 8-1.4-1.4 6.389-6.532-6.389-6.668z"/></svg>'},39146:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18" fill="none"><path fill="currentColor" d="M9 1l2.35 4.76 5.26.77-3.8 3.7.9 5.24L9 13l-4.7 2.47.9-5.23-3.8-3.71 5.25-.77L9 1z"/></svg>'},48010:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18" fill="none"><path stroke="currentColor" d="M9 2.13l1.903 3.855.116.236.26.038 4.255.618-3.079 3.001-.188.184.044.259.727 4.237-3.805-2L9 12.434l-.233.122-3.805 2.001.727-4.237.044-.26-.188-.183-3.079-3.001 4.255-.618.26-.038.116-.236L9 2.13z"/></svg>'}}]);