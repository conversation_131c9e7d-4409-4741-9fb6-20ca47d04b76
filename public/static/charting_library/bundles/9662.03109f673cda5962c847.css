html {
  -webkit-text-size-adjust: 100%;
}
body {
  color: var(--themed-color-text-primary, #131722);
  font-size: 14px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family:
    -apple-system,
    BlinkMacSystemFont,
    Trebuchet MS,
    Roboto,
    Ubuntu,
    sans-serif;
  font-feature-settings:
    "tnum" on,
    "lnum" on;
}
html.theme-dark body {
  color: var(--themed-color-text-primary, #d1d4dc);
}
html[lang="vi"] body {
  font-family: Arial, sans-serif;
}
body,
html {
  box-sizing: border-box;
}
body,
dir,
h1,
h2,
h3,
h4,
h5,
h6,
html,
li,
menu,
ol,
p,
ul {
  margin: 0;
  padding: 0;
}
a {
  text-decoration: none;
}
a:active {
  outline: 0;
}
@media (any-hover: hover) {
  a:hover {
    outline: 0;
  }
}
h1 {
  font-size: 2em;
}
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: initial;
}
sup {
  top: -0.25em;
}
sub {
  bottom: -0.25em;
}
figure {
  margin: 0;
}
pre {
  overflow: auto;
}
code,
kbd,
pre,
samp {
  font-family: monospace, monospace;
  font-size: 1em;
}
button,
input,
optgroup,
select,
textarea {
  color: inherit;
  font: inherit;
  margin: 0;
}
button,
select {
  text-transform: none;
}
button,
input[type="button"],
input[type="reset"],
input[type="submit"] {
  -webkit-appearance: button;
  cursor: default;
}
input[disabled] {
  cursor: default;
}
button::-moz-focus-inner,
input::-moz-focus-inner {
  border: 0;
  padding: 0;
}
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  height: auto;
}
input[type="search"] {
  -webkit-appearance: textfield;
  box-sizing: initial;
}
input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}
fieldset {
  border: none;
  margin: 0;
  padding: 0;
}
table {
  border-collapse: collapse;
  border-spacing: 0;
}
td,
th {
  padding: 0;
}
:root {
  --v-rhythm-header-1-space-phone: 56px;
  --v-rhythm-header-1-space-tablet: 80px;
  --v-rhythm-header-1-space-laptop: 100px;
  --v-rhythm-header-1-space-desktop: 120px;
  --v-rhythm-header-2-space-phone: 16px;
  --v-rhythm-header-2-space-tablet: 24px;
  --v-rhythm-header-2-space-laptop: 32px;
  --v-rhythm-header-2-space-desktop: 40px;
  --v-rhythm-header-3-space-phone: 16px;
  --v-rhythm-header-3-space-tablet: 16px;
  --v-rhythm-header-3-space-laptop: 24px;
  --v-rhythm-header-3-space-desktop: 24px;
  --v-rhythm-footer-1-space-phone: 56px;
  --v-rhythm-footer-1-space-tablet: 80px;
  --v-rhythm-footer-1-space-laptop: 100px;
  --v-rhythm-footer-1-space-desktop: 120px;
  --v-rhythm-footer-2-space-phone: 16px;
  --v-rhythm-footer-2-space-tablet: 24px;
  --v-rhythm-footer-2-space-laptop: 32px;
  --v-rhythm-footer-2-space-desktop: 40px;
  --v-rhythm-footer-3-space-phone: 16px;
  --v-rhythm-footer-3-space-tablet: 16px;
  --v-rhythm-footer-3-space-laptop: 24px;
  --v-rhythm-footer-3-space-desktop: 24px;
  --v-rhythm-spacing-1-phone: 80px;
  --v-rhythm-spacing-1-tablet: 120px;
  --v-rhythm-spacing-1-laptop: 160px;
  --v-rhythm-spacing-1-desktop: 200px;
  --v-rhythm-spacing-2-phone: 64px;
  --v-rhythm-spacing-2-tablet: 88px;
  --v-rhythm-spacing-2-laptop: 120px;
  --v-rhythm-spacing-2-desktop: 160px;
  --v-rhythm-spacing-3-phone: 48px;
  --v-rhythm-spacing-3-tablet: 64px;
  --v-rhythm-spacing-3-laptop: 88px;
  --v-rhythm-spacing-3-desktop: 120px;
  --v-rhythm-spacing-4-phone: 48px;
  --v-rhythm-spacing-4-tablet: 48px;
  --v-rhythm-spacing-4-laptop: 64px;
  --v-rhythm-spacing-4-desktop: 80px;
  --v-rhythm-spacing-5-phone: 32px;
  --v-rhythm-spacing-5-tablet: 32px;
  --v-rhythm-spacing-5-laptop: 40px;
  --v-rhythm-spacing-5-desktop: 48px;
  --v-rhythm-header-1-space: var(--v-rhythm-header-1-space-phone);
  --v-rhythm-header-2-space: var(--v-rhythm-header-2-space-phone);
  --v-rhythm-header-3-space: var(--v-rhythm-header-3-space-phone);
  --v-rhythm-footer-1-space: var(--v-rhythm-footer-1-space-phone);
  --v-rhythm-footer-2-space: var(--v-rhythm-footer-2-space-phone);
  --v-rhythm-footer-3-space: var(--v-rhythm-footer-3-space-phone);
  --v-rhythm-spacing-1: var(--v-rhythm-spacing-1-phone);
  --v-rhythm-spacing-2: var(--v-rhythm-spacing-2-phone);
  --v-rhythm-spacing-3: var(--v-rhythm-spacing-3-phone);
  --v-rhythm-spacing-4: var(--v-rhythm-spacing-4-phone);
  --v-rhythm-spacing-5: var(--v-rhythm-spacing-5-phone);
}
@media screen and (min-width: 768px) {
  :root {
    --v-rhythm-header-1-space: var(--v-rhythm-header-1-space-tablet);
    --v-rhythm-header-2-space: var(--v-rhythm-header-2-space-tablet);
    --v-rhythm-header-3-space: var(--v-rhythm-header-3-space-tablet);
    --v-rhythm-footer-1-space: var(--v-rhythm-footer-1-space-tablet);
    --v-rhythm-footer-2-space: var(--v-rhythm-footer-2-space-tablet);
    --v-rhythm-footer-3-space: var(--v-rhythm-footer-3-space-tablet);
    --v-rhythm-spacing-1: var(--v-rhythm-spacing-1-tablet);
    --v-rhythm-spacing-2: var(--v-rhythm-spacing-2-tablet);
    --v-rhythm-spacing-3: var(--v-rhythm-spacing-3-tablet);
    --v-rhythm-spacing-4: var(--v-rhythm-spacing-4-tablet);
    --v-rhythm-spacing-5: var(--v-rhythm-spacing-5-tablet);
  }
}
@media screen and (min-width: 1280px) {
  :root {
    --v-rhythm-header-1-space: var(--v-rhythm-header-1-space-laptop);
    --v-rhythm-header-2-space: var(--v-rhythm-header-2-space-laptop);
    --v-rhythm-header-3-space: var(--v-rhythm-header-3-space-laptop);
    --v-rhythm-footer-1-space: var(--v-rhythm-footer-1-space-laptop);
    --v-rhythm-footer-2-space: var(--v-rhythm-footer-2-space-laptop);
    --v-rhythm-footer-3-space: var(--v-rhythm-footer-3-space-laptop);
    --v-rhythm-spacing-1: var(--v-rhythm-spacing-1-laptop);
    --v-rhythm-spacing-2: var(--v-rhythm-spacing-2-laptop);
    --v-rhythm-spacing-3: var(--v-rhythm-spacing-3-laptop);
    --v-rhythm-spacing-4: var(--v-rhythm-spacing-4-laptop);
    --v-rhythm-spacing-5: var(--v-rhythm-spacing-5-laptop);
  }
}
@media screen and (min-width: 1440px) {
  :root {
    --v-rhythm-header-1-space: var(--v-rhythm-header-1-space-desktop);
    --v-rhythm-header-2-space: var(--v-rhythm-header-2-space-desktop);
    --v-rhythm-header-3-space: var(--v-rhythm-header-3-space-desktop);
    --v-rhythm-footer-1-space: var(--v-rhythm-footer-1-space-desktop);
    --v-rhythm-footer-2-space: var(--v-rhythm-footer-2-space-desktop);
    --v-rhythm-footer-3-space: var(--v-rhythm-footer-3-space-desktop);
    --v-rhythm-spacing-1: var(--v-rhythm-spacing-1-desktop);
    --v-rhythm-spacing-2: var(--v-rhythm-spacing-2-desktop);
    --v-rhythm-spacing-3: var(--v-rhythm-spacing-3-desktop);
    --v-rhythm-spacing-4: var(--v-rhythm-spacing-4-desktop);
    --v-rhythm-spacing-5: var(--v-rhythm-spacing-5-desktop);
  }
}
.tv-text ol,
.tv-text p,
.tv-text ul {
  color: var(--themed-color-text-primary, #131722);
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 12px;
}
html.theme-dark .tv-text ol,
html.theme-dark .tv-text p,
html.theme-dark .tv-text ul {
  color: var(--themed-color-text-primary, #d1d4dc);
}
.tv-text ol.tv-text__paragraph--additional-top-margin,
.tv-text p.tv-text__paragraph--additional-top-margin,
.tv-text ul.tv-text__paragraph--additional-top-margin {
  margin-top: 24px;
}
.tv-text ol.tv-text__paragraph--additional-top-margin_double,
.tv-text p.tv-text__paragraph--additional-top-margin_double,
.tv-text ul.tv-text__paragraph--additional-top-margin_double {
  margin-top: 48px;
}
.tv-text ol.tv-text__paragraph--additional-top-margin_double.link,
.tv-text p.tv-text__paragraph--additional-top-margin_double.link,
.tv-text ul.tv-text__paragraph--additional-top-margin_double.link {
  color: var(--themed-color-link-primary-default, #2962ff);
  fill: var(--themed-color-link-primary-default, #2962ff);
}
html.theme-dark
  .tv-text
  ol.tv-text__paragraph--additional-top-margin_double.link,
html.theme-dark
  .tv-text
  p.tv-text__paragraph--additional-top-margin_double.link,
html.theme-dark
  .tv-text
  ul.tv-text__paragraph--additional-top-margin_double.link {
  fill: var(--themed-color-link-primary-default, #5b9cf6);
  color: var(--themed-color-link-primary-default, #5b9cf6);
}
.tv-text ol.tv-text__paragraph--additional-top-margin_double.link:visited,
.tv-text p.tv-text__paragraph--additional-top-margin_double.link:visited,
.tv-text ul.tv-text__paragraph--additional-top-margin_double.link:visited {
  color: var(--themed-color-link-primary-default, #2962ff);
  fill: var(--themed-color-link-primary-default, #2962ff);
}
html.theme-dark
  .tv-text
  ol.tv-text__paragraph--additional-top-margin_double.link:visited,
html.theme-dark
  .tv-text
  p.tv-text__paragraph--additional-top-margin_double.link:visited,
html.theme-dark
  .tv-text
  ul.tv-text__paragraph--additional-top-margin_double.link:visited {
  fill: var(--themed-color-link-primary-default, #5b9cf6);
  color: var(--themed-color-link-primary-default, #5b9cf6);
}
@media (any-hover: hover) {
  .tv-text ol.tv-text__paragraph--additional-top-margin_double.link:hover,
  .tv-text p.tv-text__paragraph--additional-top-margin_double.link:hover,
  .tv-text ul.tv-text__paragraph--additional-top-margin_double.link:hover {
    color: var(--themed-color-link-primary-hover, #1e53e5);
    fill: var(--themed-color-link-primary-hover, #1e53e5);
  }
  html.theme-dark
    .tv-text
    ol.tv-text__paragraph--additional-top-margin_double.link:hover,
  html.theme-dark
    .tv-text
    p.tv-text__paragraph--additional-top-margin_double.link:hover,
  html.theme-dark
    .tv-text
    ul.tv-text__paragraph--additional-top-margin_double.link:hover {
    fill: var(--themed-color-link-primary-hover, #3179f5);
    color: var(--themed-color-link-primary-hover, #3179f5);
  }
}
.tv-text ol.tv-text__paragraph--additional-top-margin_double.link:active,
.tv-text p.tv-text__paragraph--additional-top-margin_double.link:active,
.tv-text ul.tv-text__paragraph--additional-top-margin_double.link:active {
  color: var(--themed-color-link-primary-active, #1848cc);
  fill: var(--themed-color-link-primary-active, #1848cc);
}
html.theme-dark
  .tv-text
  ol.tv-text__paragraph--additional-top-margin_double.link:active,
html.theme-dark
  .tv-text
  p.tv-text__paragraph--additional-top-margin_double.link:active,
html.theme-dark
  .tv-text
  ul.tv-text__paragraph--additional-top-margin_double.link:active {
  fill: var(--themed-color-link-primary-active, #2962ff);
  color: var(--themed-color-link-primary-active, #2962ff);
}
.tv-text ol.tv-text__paragraph--additional-top-margin_double.link:focus,
.tv-text p.tv-text__paragraph--additional-top-margin_double.link:focus,
.tv-text ul.tv-text__paragraph--additional-top-margin_double.link:focus {
  outline: auto;
  outline-offset: 2px;
}
.tv-text ol.tv-text__paragraph--additional-top-margin_double.link:focus-visible,
.tv-text p.tv-text__paragraph--additional-top-margin_double.link:focus-visible,
.tv-text
  ul.tv-text__paragraph--additional-top-margin_double.link:focus-visible {
  outline: auto;
  outline-offset: 2px;
}
.tv-text
  ol.tv-text__paragraph--additional-top-margin_double.link:focus:not(
    :focus-visible
  ),
.tv-text
  p.tv-text__paragraph--additional-top-margin_double.link:focus:not(
    :focus-visible
  ),
.tv-text
  ul.tv-text__paragraph--additional-top-margin_double.link:focus:not(
    :focus-visible
  ) {
  outline: none;
}
@media (any-hover: hover) {
  .tv-text ol.tv-text__paragraph--additional-top-margin_double.link:hover,
  .tv-text p.tv-text__paragraph--additional-top-margin_double.link:hover,
  .tv-text ul.tv-text__paragraph--additional-top-margin_double.link:hover {
    text-decoration: underline;
  }
}
.tv-text ol.tv-text__paragraph--additional-bottom-margin,
.tv-text p.tv-text__paragraph--additional-bottom-margin,
.tv-text ul.tv-text__paragraph--additional-bottom-margin {
  margin-bottom: 24px;
}
.tv-text ol.tv-text__paragraph--additional-bottom-margin_double,
.tv-text p.tv-text__paragraph--additional-bottom-margin_double,
.tv-text ul.tv-text__paragraph--additional-bottom-margin_double {
  margin-bottom: 48px;
}
.tv-text ol.tv-text__paragraph--additional-bottom-margin_double.link,
.tv-text p.tv-text__paragraph--additional-bottom-margin_double.link,
.tv-text ul.tv-text__paragraph--additional-bottom-margin_double.link {
  color: var(--themed-color-link-primary-default, #2962ff);
  fill: var(--themed-color-link-primary-default, #2962ff);
}
html.theme-dark
  .tv-text
  ol.tv-text__paragraph--additional-bottom-margin_double.link,
html.theme-dark
  .tv-text
  p.tv-text__paragraph--additional-bottom-margin_double.link,
html.theme-dark
  .tv-text
  ul.tv-text__paragraph--additional-bottom-margin_double.link {
  fill: var(--themed-color-link-primary-default, #5b9cf6);
  color: var(--themed-color-link-primary-default, #5b9cf6);
}
.tv-text ol.tv-text__paragraph--additional-bottom-margin_double.link:visited,
.tv-text p.tv-text__paragraph--additional-bottom-margin_double.link:visited,
.tv-text ul.tv-text__paragraph--additional-bottom-margin_double.link:visited {
  color: var(--themed-color-link-primary-default, #2962ff);
  fill: var(--themed-color-link-primary-default, #2962ff);
}
html.theme-dark
  .tv-text
  ol.tv-text__paragraph--additional-bottom-margin_double.link:visited,
html.theme-dark
  .tv-text
  p.tv-text__paragraph--additional-bottom-margin_double.link:visited,
html.theme-dark
  .tv-text
  ul.tv-text__paragraph--additional-bottom-margin_double.link:visited {
  fill: var(--themed-color-link-primary-default, #5b9cf6);
  color: var(--themed-color-link-primary-default, #5b9cf6);
}
@media (any-hover: hover) {
  .tv-text ol.tv-text__paragraph--additional-bottom-margin_double.link:hover,
  .tv-text p.tv-text__paragraph--additional-bottom-margin_double.link:hover,
  .tv-text ul.tv-text__paragraph--additional-bottom-margin_double.link:hover {
    color: var(--themed-color-link-primary-hover, #1e53e5);
    fill: var(--themed-color-link-primary-hover, #1e53e5);
  }
  html.theme-dark
    .tv-text
    ol.tv-text__paragraph--additional-bottom-margin_double.link:hover,
  html.theme-dark
    .tv-text
    p.tv-text__paragraph--additional-bottom-margin_double.link:hover,
  html.theme-dark
    .tv-text
    ul.tv-text__paragraph--additional-bottom-margin_double.link:hover {
    fill: var(--themed-color-link-primary-hover, #3179f5);
    color: var(--themed-color-link-primary-hover, #3179f5);
  }
}
.tv-text ol.tv-text__paragraph--additional-bottom-margin_double.link:active,
.tv-text p.tv-text__paragraph--additional-bottom-margin_double.link:active,
.tv-text ul.tv-text__paragraph--additional-bottom-margin_double.link:active {
  color: var(--themed-color-link-primary-active, #1848cc);
  fill: var(--themed-color-link-primary-active, #1848cc);
}
html.theme-dark
  .tv-text
  ol.tv-text__paragraph--additional-bottom-margin_double.link:active,
html.theme-dark
  .tv-text
  p.tv-text__paragraph--additional-bottom-margin_double.link:active,
html.theme-dark
  .tv-text
  ul.tv-text__paragraph--additional-bottom-margin_double.link:active {
  fill: var(--themed-color-link-primary-active, #2962ff);
  color: var(--themed-color-link-primary-active, #2962ff);
}
.tv-text ol.tv-text__paragraph--additional-bottom-margin_double.link:focus,
.tv-text p.tv-text__paragraph--additional-bottom-margin_double.link:focus,
.tv-text ul.tv-text__paragraph--additional-bottom-margin_double.link:focus {
  outline: auto;
  outline-offset: 2px;
}
.tv-text
  ol.tv-text__paragraph--additional-bottom-margin_double.link:focus-visible,
.tv-text
  p.tv-text__paragraph--additional-bottom-margin_double.link:focus-visible,
.tv-text
  ul.tv-text__paragraph--additional-bottom-margin_double.link:focus-visible {
  outline: auto;
  outline-offset: 2px;
}
.tv-text
  ol.tv-text__paragraph--additional-bottom-margin_double.link:focus:not(
    :focus-visible
  ),
.tv-text
  p.tv-text__paragraph--additional-bottom-margin_double.link:focus:not(
    :focus-visible
  ),
.tv-text
  ul.tv-text__paragraph--additional-bottom-margin_double.link:focus:not(
    :focus-visible
  ) {
  outline: none;
}
@media (any-hover: hover) {
  .tv-text ol.tv-text__paragraph--additional-bottom-margin_double.link:hover,
  .tv-text p.tv-text__paragraph--additional-bottom-margin_double.link:hover,
  .tv-text ul.tv-text__paragraph--additional-bottom-margin_double.link:hover {
    text-decoration: underline;
  }
}
.tv-text h1 {
  font-size: 45px;
  margin-bottom: 30px;
  margin-top: 30px;
}
@media screen and (max-width: 1019px) {
  .tv-text h1 {
    font-size: 38px;
  }
}
@media screen and (max-width: 767px) {
  .tv-text h1 {
    font-size: 32px;
  }
}
@media screen and (max-width: 479px) {
  .tv-text h1 {
    font-size: 28px;
  }
}
.tv-text h2 {
  font-size: 31px;
}
@media screen and (max-width: 1019px) {
  .tv-text h2 {
    font-size: 26px;
  }
}
@media screen and (max-width: 479px) {
  .tv-text h2 {
    font-size: 24px;
  }
}
.tv-text h3 {
  font-size: 17px;
}
@media screen and (max-width: 1019px) {
  .tv-text h3 {
    font-size: 16px;
  }
}
.tv-text h4 {
  font-size: 15px;
}
.tv-text h2,
.tv-text h3,
.tv-text h4 {
  margin-bottom: 20px;
  margin-top: 20px;
}
.tv-text h1:first-child,
.tv-text h2:first-child,
.tv-text h3:first-child,
.tv-text h4:first-child {
  margin-top: 0;
}
.tv-text ol,
.tv-text ul {
  list-style-position: inside;
}
.tv-text--position-outside ol,
.tv-text--position-outside ul {
  list-style-position: outside;
  padding-left: 17px;
}
.tv-text a:not(.tv-button):not(.tv-chart-view__description__tag) {
  color: var(--themed-color-link-primary-default, #2962ff);
  fill: var(--themed-color-link-primary-default, #2962ff);
}
html.theme-dark
  .tv-text
  a:not(.tv-button):not(.tv-chart-view__description__tag) {
  fill: var(--themed-color-link-primary-default, #5b9cf6);
  color: var(--themed-color-link-primary-default, #5b9cf6);
}
.tv-text a:not(.tv-button):not(.tv-chart-view__description__tag):visited {
  color: var(--themed-color-link-primary-default, #2962ff);
  fill: var(--themed-color-link-primary-default, #2962ff);
}
html.theme-dark
  .tv-text
  a:not(.tv-button):not(.tv-chart-view__description__tag):visited {
  fill: var(--themed-color-link-primary-default, #5b9cf6);
  color: var(--themed-color-link-primary-default, #5b9cf6);
}
@media (any-hover: hover) {
  .tv-text a:not(.tv-button):not(.tv-chart-view__description__tag):hover {
    color: var(--themed-color-link-primary-hover, #1e53e5);
    fill: var(--themed-color-link-primary-hover, #1e53e5);
  }
  html.theme-dark
    .tv-text
    a:not(.tv-button):not(.tv-chart-view__description__tag):hover {
    fill: var(--themed-color-link-primary-hover, #3179f5);
    color: var(--themed-color-link-primary-hover, #3179f5);
  }
}
.tv-text a:not(.tv-button):not(.tv-chart-view__description__tag):active {
  color: var(--themed-color-link-primary-active, #1848cc);
  fill: var(--themed-color-link-primary-active, #1848cc);
}
html.theme-dark
  .tv-text
  a:not(.tv-button):not(.tv-chart-view__description__tag):active {
  fill: var(--themed-color-link-primary-active, #2962ff);
  color: var(--themed-color-link-primary-active, #2962ff);
}
.tv-text a:not(.tv-button):not(.tv-chart-view__description__tag):focus {
  outline: auto;
  outline-offset: 2px;
}
.tv-text a:not(.tv-button):not(.tv-chart-view__description__tag):focus-visible {
  outline: auto;
  outline-offset: 2px;
}
.tv-text
  a:not(.tv-button):not(.tv-chart-view__description__tag):focus:not(
    :focus-visible
  ) {
  outline: none;
}
.tv-text__font.tv-text__font--size_semilarge {
  font-size: 15px;
}
@media screen and (max-width: 767px) {
  .tv-text__font.tv-text__font--size_semilarge {
    font-size: 14px;
  }
}
.tv-text__font.tv-text__font--size_large {
  font-size: 17px;
}
@media screen and (max-width: 767px) {
  .tv-text__font.tv-text__font--size_large {
    font-size: 16px;
  }
}
.tv-text__font.tv-text__font--size_xlarge {
  font-size: 19px;
}
@media screen and (max-width: 767px) {
  .tv-text__font.tv-text__font--size_xlarge {
    font-size: 18px;
  }
}
.tv-text__font--bold {
  font-weight: 700;
}
.tv-text__font--italic {
  font-style: italic;
}
.tv-text--darkbg,
.tv-text--darkbg ol,
.tv-text--darkbg p,
.tv-text--darkbg ul {
  color: #fff;
}
.js-hidden {
  display: none !important;
}
.js-no-pointer-events {
  pointer-events: none !important;
}
.aria-live-regions-wrapper {
  border: 0;
  height: 1px;
  margin: -1px;
  padding: 0;
  position: absolute;
  width: 1px;
  clip: rect(0, 0, 0, 0);
  overflow-x: hidden;
}
html {
  color-scheme: light;
}
html.theme-dark {
  color-scheme: dark;
}
html.theme-dark iframe {
  color-scheme: normal;
}
body {
  box-sizing: border-box;
  min-width: 320px;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  -webkit-tap-highlight-color: transparent;
}
body.i-no-scroll {
  overflow: hidden !important;
}
body.i-no-padding {
  padding: 0 !important;
}
::selection {
  background: var(--themed-color-selection-bg, #bbd9fb);
}
html.theme-dark ::selection {
  background: var(--themed-color-selection-bg, #143a87);
}
.i-hidden {
  display: none !important;
}
.i-invisible {
  visibility: hidden !important;
}
.i-clearfix:after {
  clear: both;
  content: "";
  display: table;
}
.i-align_left {
  text-align: left !important;
}
.i-align_right {
  text-align: right !important;
}
.i-align_center {
  text-align: center !important;
}
.i-float_left {
  float: left !important;
}
.i-float_right {
  float: right !important;
}
.i-float_none {
  float: none !important;
}
@media screen and (min-width: 1020px) {
  .i-device-only {
    display: none !important;
  }
}
@media screen and (max-width: 1019px) {
  .i-desktop-only {
    display: none !important;
  }
}
@media not screen and (max-width: 479px) {
  .i-phones-only {
    display: none !important;
  }
}
@media screen and (max-width: 479px) {
  .i-except-phones-only {
    display: none !important;
  }
}
.i-no-break {
  white-space: nowrap;
}
body {
  overflow-y: scroll;
}
.tv-layout-width {
  box-sizing: border-box;
  margin: 0 auto;
  padding: 0 20px;
  width: 1020px;
}
.tv-layout-width--no-padding {
  padding: 0;
  width: 980px;
}
.tv-layout-width--simple {
  padding: 0;
}
.tv-dialog .tv-layout-width {
  max-width: 100%;
}
.chart-page .tv-layout-width {
  width: 100%;
}
@media screen and (max-width: 1019px) {
  .tv-layout-width {
    width: auto;
  }
  .tv-layout-width .tv-feed {
    margin-left: -20px;
    margin-right: -20px;
  }
  .tv-layout-width .tv-feed--tablet-top-indent {
    margin-top: 48px;
  }
}
.tv-layout-padding {
  padding: 0 20px;
}
body.page-wide .tv-layout-width {
  width: 1520px;
}
.tv-main,
body.page-fullwidth .tv-layout-width {
  width: 100%;
}
.tv-main {
  display: flex;
  flex-direction: column;
  min-height: 100%;
}
.clear {
  clear: both;
}
.clearfix:after {
  clear: both;
  content: "";
  display: table;
}
.hide {
  display: none !important;
}
.show {
  display: block !important;
}
.tv-right {
  float: right !important;
}
.tv-left {
  float: left !important;
}
body {
  background-color: var(--themed-color-body-bg, #fff);
}
html.theme-dark body {
  background-color: var(--themed-color-body-bg, #000);
}
body.tv-body--bg_superdark,
html.theme-dark body.tv-body--bg_superdark {
  background: var(--themed-color-body-bg-superdark-legacy, #131722);
}
body.extension {
  min-width: 0;
  overflow: hidden;
}
img {
  border: none;
}
textarea {
  resize: none;
}
:focus {
  outline: none;
}
input,
textarea {
  border-radius: 0;
}
.tv-profile .tags,
.unselectable {
  -webkit-user-select: none;
  user-select: none;
}
.selectable,
input,
textarea {
  -webkit-user-select: text;
  user-select: text;
}
.text-center {
  text-align: center;
}
.loading-indicator {
  background: var(--themed-color-ui-loading-indicator-bg, #fff);
  height: 100%;
  left: 0;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 150;
}
html.theme-dark .loading-indicator {
  background: var(--themed-color-ui-loading-indicator-bg, #131722);
}
.falling,
.growing {
  color: #fff;
}
.growing {
  background: #42bda8;
}
.falling {
  background: #f7525f;
}
.tv-button {
  color: #787b86;
  display: inline-block;
  font-size: 14px;
  line-height: 32px;
  margin: 0;
  min-width: 40px;
  padding: 1px 22px;
  position: relative;
  text-align: center;
  text-decoration: none;
  -webkit-user-select: none;
  user-select: none;
  vertical-align: middle;
  white-space: nowrap;
  fill: currentColor;
  background-color: initial;
  border: none;
  border-radius: 4px;
  box-sizing: border-box;
  cursor: pointer;
  outline: 0;
  overflow: hidden;
  -webkit-tap-highlight-color: transparent;
  transition:
    background-color 0.35s ease,
    border-color 0.35s ease,
    color 0.35s ease;
}
.tv-button.tv-button--danger_ghost,
.tv-button.tv-button--default,
.tv-button.tv-button--default_ghost,
.tv-button.tv-button--primary_ghost,
.tv-button.tv-button--secondary_ghost,
.tv-button.tv-button--state,
.tv-button.tv-button--success_ghost,
.tv-button.tv-button--warning_ghost {
  padding: 0 21px;
}
.tv-button.i-active,
.tv-button.i-hover,
.tv-button:active {
  transition-duration: 60ms;
}
@media (any-hover: hover) {
  .tv-button:hover {
    transition-duration: 60ms;
  }
}
.tv-button svg {
  vertical-align: middle;
}
.tv-button--block {
  display: block;
  text-align: center;
  width: 100%;
}
.tv-button + .tv-button {
  margin-left: 15px;
}
.tv-button.tv-button--no-left-margin {
  margin-left: 0;
}
.tv-button__text {
  display: inline-block;
  position: relative;
}
.tv-button__text--full-height {
  align-items: center;
  display: flex;
  height: 100%;
  justify-content: center;
  white-space: normal;
  width: 100%;
  word-wrap: break-word;
  line-height: 1.2em;
  margin: 11px 5px;
}
.tv-button--default,
.tv-button--default_ghost,
a.tv-button--default:visited {
  background-color: var(--themed-color-body-bg, #fff);
  border-color: var(--themed-color-body-bg, #fff);
  color: #fff;
}
html.theme-dark .tv-button--default,
html.theme-dark .tv-button--default_ghost,
html.theme-dark a.tv-button--default:visited {
  background-color: var(--themed-color-body-bg, #000);
  border-color: var(--themed-color-body-bg, #000);
}
.tv-button--default_ghost {
  color: var(--themed-color-body-bg, #fff);
}
html.theme-dark .tv-button--default_ghost {
  color: var(--themed-color-body-bg, #000);
}
.tv-button--default_ghost.i-checked {
  background-color: var(--themed-color-body-bg, #fff);
  border-color: var(--themed-color-body-bg, #fff);
  color: #fff;
}
html.theme-dark .tv-button--default_ghost.i-checked {
  background-color: var(--themed-color-body-bg, #000);
  border-color: var(--themed-color-body-bg, #000);
}
.tv-button--default.i-active,
.tv-button--default:active,
.tv-button--default_ghost.i-active,
.tv-button--default_ghost:active {
  background-color: var(--themed-color-default-active-bg, #ececec);
  border-color: var(--themed-color-default-active-bg, #ececec);
  color: #fff;
  transform: translateY(1px);
}
html.theme-dark .tv-button--default.i-active,
html.theme-dark .tv-button--default:active,
html.theme-dark .tv-button--default_ghost.i-active,
html.theme-dark .tv-button--default_ghost:active {
  background-color: var(--themed-color-default-active-bg, #1e222d);
  border-color: var(--themed-color-default-active-bg, #1e222d);
}
.tv-button--default.i-hover,
.tv-button--default_ghost.i-hover {
  background-color: var(--themed-color-button-hover-bg, #e0e3eb);
  border-color: var(--themed-color-button-hover-bg, #e0e3eb);
  color: #fff;
}
@media (any-hover: hover) {
  .tv-button--default:hover,
  .tv-button--default_ghost:hover {
    background-color: var(--themed-color-button-hover-bg, #e0e3eb);
    border-color: var(--themed-color-button-hover-bg, #e0e3eb);
    color: #fff;
  }
}
html.theme-dark .tv-button--default.i-hover,
html.theme-dark .tv-button--default_ghost.i-hover {
  background-color: var(--themed-color-button-hover-bg, #1e222d);
}
@media (any-hover: hover) {
  html.theme-dark .tv-button--default:hover,
  html.theme-dark .tv-button--default_ghost:hover {
    background-color: var(--themed-color-button-hover-bg, #1e222d);
  }
}
html.theme-dark .tv-button--default.i-hover,
html.theme-dark .tv-button--default_ghost.i-hover {
  border-color: var(--themed-color-button-hover-bg, #1e222d);
}
@media (any-hover: hover) {
  html.theme-dark .tv-button--default:hover,
  html.theme-dark .tv-button--default_ghost:hover {
    border-color: var(--themed-color-button-hover-bg, #1e222d);
  }
}
.tv-button--default_ghost .tv-button__loader-item {
  background-color: var(--themed-color-button-hover-bg, #e0e3eb);
}
html.theme-dark .tv-button--default_ghost .tv-button__loader-item {
  background-color: var(--themed-color-button-hover-bg, #1e222d);
}
.tv-button--default_ghost.i-hover .tv-button__loader-item {
  background-color: #fff;
}
@media (any-hover: hover) {
  .tv-button--default_ghost:hover .tv-button__loader-item {
    background-color: #fff;
  }
}
.tv-button--default,
.tv-button--default.i-checked,
.tv-button--default_ghost,
.tv-button--default_ghost.i-checked {
  border: 1px solid;
  border-color: var(--themed-color-section-separator-border, #b2b5be);
  color: var(--themed-color-tv-button-checked, #6a6d78);
}
html.theme-dark .tv-button--default,
html.theme-dark .tv-button--default.i-checked,
html.theme-dark .tv-button--default_ghost,
html.theme-dark .tv-button--default_ghost.i-checked {
  border-color: var(--themed-color-section-separator-border, #363a45);
  color: var(--themed-color-tv-button-checked, #868993);
}
.tv-button--default.i-hover,
.tv-button--default_ghost.i-hover {
  border-color: var(--themed-color-section-separator-border, #b2b5be);
  color: var(--themed-color-tv-button-checked, #6a6d78);
}
@media (any-hover: hover) {
  .tv-button--default:hover,
  .tv-button--default_ghost:hover {
    border-color: var(--themed-color-section-separator-border, #b2b5be);
    color: var(--themed-color-tv-button-checked, #6a6d78);
  }
}
html.theme-dark .tv-button--default.i-hover,
html.theme-dark .tv-button--default_ghost.i-hover {
  border-color: var(--themed-color-section-separator-border, #363a45);
}
@media (any-hover: hover) {
  html.theme-dark .tv-button--default:hover,
  html.theme-dark .tv-button--default_ghost:hover {
    border-color: var(--themed-color-section-separator-border, #363a45);
  }
}
html.theme-dark .tv-button--default.i-hover,
html.theme-dark .tv-button--default_ghost.i-hover {
  color: var(--themed-color-tv-button-checked, #868993);
}
@media (any-hover: hover) {
  html.theme-dark .tv-button--default:hover,
  html.theme-dark .tv-button--default_ghost:hover {
    color: var(--themed-color-tv-button-checked, #868993);
  }
}
.tv-button--default.i-active,
.tv-button--default:active,
.tv-button--default_ghost.i-active,
.tv-button--default_ghost:active {
  border-color: var(--themed-color-section-separator-border, #b2b5be);
  color: var(--themed-color-tv-button-checked, #6a6d78);
}
html.theme-dark .tv-button--default.i-active,
html.theme-dark .tv-button--default:active,
html.theme-dark .tv-button--default_ghost.i-active,
html.theme-dark .tv-button--default_ghost:active {
  border-color: var(--themed-color-section-separator-border, #363a45);
  color: var(--themed-color-tv-button-checked, #868993);
}
.tv-button--primary,
.tv-button--primary_ghost,
a.tv-button--primary:visited {
  background-color: var(--themed-color-brand, #2962ff);
  border-color: var(--themed-color-brand, #2962ff);
  color: var(--themed-color-button-text-color, #fff);
}
html.theme-dark .tv-button--primary,
html.theme-dark .tv-button--primary_ghost,
html.theme-dark a.tv-button--primary:visited {
  background-color: var(--themed-color-brand, #2962ff);
  border-color: var(--themed-color-brand, #2962ff);
  color: var(--themed-color-button-text-color, #fff);
}
.tv-button--primary_ghost,
html.theme-dark .tv-button--primary_ghost {
  color: var(--themed-color-brand, #2962ff);
}
.tv-button--primary_ghost.i-checked {
  border-color: var(--themed-color-brand, #2962ff);
  color: var(--themed-color-button-text-color, #fff);
}
.tv-button--primary_ghost.i-checked,
html.theme-dark .tv-button--primary_ghost.i-checked {
  background-color: var(--themed-color-brand, #2962ff);
}
html.theme-dark .tv-button--primary_ghost.i-checked {
  border-color: var(--themed-color-brand, #2962ff);
  color: var(--themed-color-button-text-color, #fff);
}
.tv-button--primary.i-active,
.tv-button--primary:active,
.tv-button--primary_ghost.i-active,
.tv-button--primary_ghost:active {
  background-color: var(--themed-color-brand-active, #1848cc);
  border-color: var(--themed-color-brand-active, #1848cc);
  color: var(--themed-color-button-text-color, #fff);
  transform: translateY(1px);
}
html.theme-dark .tv-button--primary.i-active,
html.theme-dark .tv-button--primary:active,
html.theme-dark .tv-button--primary_ghost.i-active,
html.theme-dark .tv-button--primary_ghost:active {
  background-color: var(--themed-color-brand-active, #1848cc);
  border-color: var(--themed-color-brand-active, #1848cc);
  color: var(--themed-color-button-text-color, #fff);
}
.tv-button--primary.i-hover,
.tv-button--primary_ghost.i-hover {
  background-color: var(--themed-color-brand-hover, #1e53e5);
  border-color: var(--themed-color-brand-hover, #1e53e5);
  color: var(--themed-color-button-text-color, #fff);
}
@media (any-hover: hover) {
  .tv-button--primary:hover,
  .tv-button--primary_ghost:hover {
    background-color: var(--themed-color-brand-hover, #1e53e5);
    border-color: var(--themed-color-brand-hover, #1e53e5);
    color: var(--themed-color-button-text-color, #fff);
  }
}
html.theme-dark .tv-button--primary.i-hover,
html.theme-dark .tv-button--primary_ghost.i-hover {
  background-color: var(--themed-color-brand-hover, #1e53e5);
}
@media (any-hover: hover) {
  html.theme-dark .tv-button--primary:hover,
  html.theme-dark .tv-button--primary_ghost:hover {
    background-color: var(--themed-color-brand-hover, #1e53e5);
  }
}
html.theme-dark .tv-button--primary.i-hover,
html.theme-dark .tv-button--primary_ghost.i-hover {
  border-color: var(--themed-color-brand-hover, #1e53e5);
}
@media (any-hover: hover) {
  html.theme-dark .tv-button--primary:hover,
  html.theme-dark .tv-button--primary_ghost:hover {
    border-color: var(--themed-color-brand-hover, #1e53e5);
  }
}
html.theme-dark .tv-button--primary.i-hover,
html.theme-dark .tv-button--primary_ghost.i-hover {
  color: var(--themed-color-button-text-color, #fff);
}
@media (any-hover: hover) {
  html.theme-dark .tv-button--primary:hover,
  html.theme-dark .tv-button--primary_ghost:hover {
    color: var(--themed-color-button-text-color, #fff);
  }
}
.tv-button--primary_ghost .tv-button__loader-item,
html.theme-dark .tv-button--primary_ghost .tv-button__loader-item {
  background-color: var(--themed-color-brand-hover, #1e53e5);
}
.tv-button--primary_ghost.i-hover .tv-button__loader-item {
  background-color: var(--themed-color-button-text-color, #fff);
}
@media (any-hover: hover) {
  .tv-button--primary_ghost:hover .tv-button__loader-item {
    background-color: var(--themed-color-button-text-color, #fff);
  }
}
html.theme-dark .tv-button--primary_ghost.i-hover .tv-button__loader-item {
  background-color: var(--themed-color-button-text-color, #fff);
}
@media (any-hover: hover) {
  html.theme-dark .tv-button--primary_ghost:hover .tv-button__loader-item {
    background-color: var(--themed-color-button-text-color, #fff);
  }
}
.tv-button--secondary,
.tv-button--secondary_ghost,
a.tv-button--secondary:visited {
  background-color: #f0f3fa;
  border-color: #f0f3fa;
  color: #787b86;
}
.tv-button--secondary_ghost {
  color: #787b86;
}
.tv-button--secondary_ghost.i-checked {
  background-color: #f0f3fa;
  border-color: #f0f3fa;
  color: #787b86;
}
.tv-button--secondary.i-active,
.tv-button--secondary:active,
.tv-button--secondary_ghost.i-active,
.tv-button--secondary_ghost:active {
  background-color: #d1dbf0;
  border-color: #d1dbf0;
  color: #787b86;
  transform: translateY(1px);
}
.tv-button--secondary.i-hover,
.tv-button--secondary_ghost.i-hover {
  background-color: #e1e7f5;
  border-color: #e1e7f5;
  color: #787b86;
}
@media (any-hover: hover) {
  .tv-button--secondary:hover,
  .tv-button--secondary_ghost:hover {
    background-color: #e1e7f5;
    border-color: #e1e7f5;
    color: #787b86;
  }
}
.tv-button--secondary_ghost .tv-button__loader-item {
  background-color: #e1e7f5;
}
.tv-button--secondary_ghost.i-hover .tv-button__loader-item {
  background-color: #787b86;
}
@media (any-hover: hover) {
  .tv-button--secondary_ghost:hover .tv-button__loader-item {
    background-color: #787b86;
  }
}
.tv-button--success,
.tv-button--success_ghost,
a.tv-button--success:visited {
  background-color: var(--themed-color-success, #089981);
  border-color: var(--themed-color-success, #089981);
  color: var(--themed-color-button-text-color, #fff);
}
html.theme-dark .tv-button--success,
html.theme-dark .tv-button--success_ghost,
html.theme-dark a.tv-button--success:visited {
  background-color: var(--themed-color-success, #056656);
  border-color: var(--themed-color-success, #056656);
  color: var(--themed-color-button-text-color, #fff);
}
.tv-button--success_ghost {
  color: var(--themed-color-success, #089981);
}
html.theme-dark .tv-button--success_ghost {
  color: var(--themed-color-success, #056656);
}
.tv-button--success_ghost.i-checked {
  background-color: var(--themed-color-success, #089981);
  border-color: var(--themed-color-success, #089981);
  color: var(--themed-color-button-text-color, #fff);
}
html.theme-dark .tv-button--success_ghost.i-checked {
  background-color: var(--themed-color-success, #056656);
  border-color: var(--themed-color-success, #056656);
  color: var(--themed-color-button-text-color, #fff);
}
.tv-button--success.i-active,
.tv-button--success:active,
.tv-button--success_ghost.i-active,
.tv-button--success_ghost:active {
  background-color: var(--themed-color-success-active, #056656);
  border-color: var(--themed-color-success-active, #056656);
  color: var(--themed-color-button-text-color, #fff);
  transform: translateY(1px);
}
html.theme-dark .tv-button--success.i-active,
html.theme-dark .tv-button--success:active,
html.theme-dark .tv-button--success_ghost.i-active,
html.theme-dark .tv-button--success_ghost:active {
  background-color: var(--themed-color-success-active, #089981);
  border-color: var(--themed-color-success-active, #089981);
  color: var(--themed-color-button-text-color, #fff);
}
.tv-button--success.i-hover,
.tv-button--success_ghost.i-hover {
  background-color: var(--themed-color-success-hover, #06806b);
  border-color: var(--themed-color-success-hover, #06806b);
  color: var(--themed-color-button-text-color, #fff);
}
@media (any-hover: hover) {
  .tv-button--success:hover,
  .tv-button--success_ghost:hover {
    background-color: var(--themed-color-success-hover, #06806b);
    border-color: var(--themed-color-success-hover, #06806b);
    color: var(--themed-color-button-text-color, #fff);
  }
}
html.theme-dark .tv-button--success.i-hover,
html.theme-dark .tv-button--success_ghost.i-hover {
  background-color: var(--themed-color-success-hover, #06806b);
}
@media (any-hover: hover) {
  html.theme-dark .tv-button--success:hover,
  html.theme-dark .tv-button--success_ghost:hover {
    background-color: var(--themed-color-success-hover, #06806b);
  }
}
html.theme-dark .tv-button--success.i-hover,
html.theme-dark .tv-button--success_ghost.i-hover {
  border-color: var(--themed-color-success-hover, #06806b);
}
@media (any-hover: hover) {
  html.theme-dark .tv-button--success:hover,
  html.theme-dark .tv-button--success_ghost:hover {
    border-color: var(--themed-color-success-hover, #06806b);
  }
}
html.theme-dark .tv-button--success.i-hover,
html.theme-dark .tv-button--success_ghost.i-hover {
  color: var(--themed-color-button-text-color, #fff);
}
@media (any-hover: hover) {
  html.theme-dark .tv-button--success:hover,
  html.theme-dark .tv-button--success_ghost:hover {
    color: var(--themed-color-button-text-color, #fff);
  }
}
.tv-button--success_ghost .tv-button__loader-item,
html.theme-dark .tv-button--success_ghost .tv-button__loader-item {
  background-color: var(--themed-color-success-hover, #06806b);
}
.tv-button--success_ghost.i-hover .tv-button__loader-item {
  background-color: var(--themed-color-button-text-color, #fff);
}
@media (any-hover: hover) {
  .tv-button--success_ghost:hover .tv-button__loader-item {
    background-color: var(--themed-color-button-text-color, #fff);
  }
}
html.theme-dark .tv-button--success_ghost.i-hover .tv-button__loader-item {
  background-color: var(--themed-color-button-text-color, #fff);
}
@media (any-hover: hover) {
  html.theme-dark .tv-button--success_ghost:hover .tv-button__loader-item {
    background-color: var(--themed-color-button-text-color, #fff);
  }
}
.tv-button--danger,
.tv-button--danger_ghost,
a.tv-button--danger:visited {
  background-color: var(--themed-color-btn-danger, #f7525f);
  border-color: var(--themed-color-btn-danger, #f7525f);
  color: var(--themed-color-button-text-color, #fff);
}
html.theme-dark .tv-button--danger,
html.theme-dark .tv-button--danger_ghost,
html.theme-dark a.tv-button--danger:visited {
  background-color: var(--themed-color-btn-danger, #b22833);
  border-color: var(--themed-color-btn-danger, #b22833);
  color: var(--themed-color-button-text-color, #fff);
}
.tv-button--danger_ghost {
  color: var(--themed-color-btn-danger, #f7525f);
}
html.theme-dark .tv-button--danger_ghost {
  color: var(--themed-color-btn-danger, #b22833);
}
.tv-button--danger_ghost.i-checked {
  background-color: var(--themed-color-btn-danger, #f7525f);
  border-color: var(--themed-color-btn-danger, #f7525f);
  color: var(--themed-color-button-text-color, #fff);
}
html.theme-dark .tv-button--danger_ghost.i-checked {
  background-color: var(--themed-color-btn-danger, #b22833);
  border-color: var(--themed-color-btn-danger, #b22833);
  color: var(--themed-color-button-text-color, #fff);
}
.tv-button--danger.i-active,
.tv-button--danger:active,
.tv-button--danger_ghost.i-active,
.tv-button--danger_ghost:active {
  background-color: var(--themed-color-btn-danger-active, #b22833);
  border-color: var(--themed-color-btn-danger-active, #b22833);
  color: var(--themed-color-button-text-color, #fff);
  transform: translateY(1px);
}
html.theme-dark .tv-button--danger.i-active,
html.theme-dark .tv-button--danger:active,
html.theme-dark .tv-button--danger_ghost.i-active,
html.theme-dark .tv-button--danger_ghost:active {
  background-color: var(--themed-color-btn-danger-active, #f23645);
  border-color: var(--themed-color-btn-danger-active, #f23645);
  color: var(--themed-color-button-text-color, #fff);
}
.tv-button--danger.i-hover,
.tv-button--danger_ghost.i-hover {
  background-color: var(--themed-color-btn-danger-hover, #cc2f3c);
  border-color: var(--themed-color-btn-danger-hover, #cc2f3c);
  color: var(--themed-color-button-text-color, #fff);
}
@media (any-hover: hover) {
  .tv-button--danger:hover,
  .tv-button--danger_ghost:hover {
    background-color: var(--themed-color-btn-danger-hover, #cc2f3c);
    border-color: var(--themed-color-btn-danger-hover, #cc2f3c);
    color: var(--themed-color-button-text-color, #fff);
  }
}
html.theme-dark .tv-button--danger.i-hover,
html.theme-dark .tv-button--danger_ghost.i-hover {
  background-color: var(--themed-color-btn-danger-hover, #cc2f3c);
}
@media (any-hover: hover) {
  html.theme-dark .tv-button--danger:hover,
  html.theme-dark .tv-button--danger_ghost:hover {
    background-color: var(--themed-color-btn-danger-hover, #cc2f3c);
  }
}
html.theme-dark .tv-button--danger.i-hover,
html.theme-dark .tv-button--danger_ghost.i-hover {
  border-color: var(--themed-color-btn-danger-hover, #cc2f3c);
}
@media (any-hover: hover) {
  html.theme-dark .tv-button--danger:hover,
  html.theme-dark .tv-button--danger_ghost:hover {
    border-color: var(--themed-color-btn-danger-hover, #cc2f3c);
  }
}
html.theme-dark .tv-button--danger.i-hover,
html.theme-dark .tv-button--danger_ghost.i-hover {
  color: var(--themed-color-button-text-color, #fff);
}
@media (any-hover: hover) {
  html.theme-dark .tv-button--danger:hover,
  html.theme-dark .tv-button--danger_ghost:hover {
    color: var(--themed-color-button-text-color, #fff);
  }
}
.tv-button--danger_ghost .tv-button__loader-item,
html.theme-dark .tv-button--danger_ghost .tv-button__loader-item {
  background-color: var(--themed-color-btn-danger-hover, #cc2f3c);
}
.tv-button--danger_ghost.i-hover .tv-button__loader-item {
  background-color: var(--themed-color-button-text-color, #fff);
}
@media (any-hover: hover) {
  .tv-button--danger_ghost:hover .tv-button__loader-item {
    background-color: var(--themed-color-button-text-color, #fff);
  }
}
html.theme-dark .tv-button--danger_ghost.i-hover .tv-button__loader-item {
  background-color: var(--themed-color-button-text-color, #fff);
}
@media (any-hover: hover) {
  html.theme-dark .tv-button--danger_ghost:hover .tv-button__loader-item {
    background-color: var(--themed-color-button-text-color, #fff);
  }
}
.tv-button--warning,
.tv-button--warning_ghost,
a.tv-button--warning:visited {
  background-color: #ff9800;
  border-color: #ff9800;
  color: #fff;
}
.tv-button--warning_ghost {
  color: #ff9800;
}
.tv-button--warning_ghost.i-checked {
  background-color: #ff9800;
  border-color: #ff9800;
  color: #fff;
}
.tv-button--warning.i-active,
.tv-button--warning:active,
.tv-button--warning_ghost.i-active,
.tv-button--warning_ghost:active {
  background-color: #cc7014;
  border-color: #cc7014;
  color: #fff;
  transform: translateY(1px);
}
.tv-button--warning.i-hover,
.tv-button--warning_ghost.i-hover {
  background-color: #ff9100;
  border-color: #ff9100;
  color: #fff;
}
@media (any-hover: hover) {
  .tv-button--warning:hover,
  .tv-button--warning_ghost:hover {
    background-color: #ff9100;
    border-color: #ff9100;
    color: #fff;
  }
}
.tv-button--warning_ghost .tv-button__loader-item {
  background-color: #ff9100;
}
.tv-button--warning_ghost.i-hover .tv-button__loader-item {
  background-color: #fff;
}
@media (any-hover: hover) {
  .tv-button--warning_ghost:hover .tv-button__loader-item {
    background-color: #fff;
  }
}
.tv-button--link {
  color: var(--themed-color-link-primary-default, #2962ff);
  fill: var(--themed-color-link-primary-default, #2962ff);
}
html.theme-dark .tv-button--link {
  fill: var(--themed-color-link-primary-default, #5b9cf6);
  color: var(--themed-color-link-primary-default, #5b9cf6);
}
.tv-button--link:visited {
  color: var(--themed-color-link-primary-default, #2962ff);
  fill: var(--themed-color-link-primary-default, #2962ff);
}
html.theme-dark .tv-button--link:visited {
  fill: var(--themed-color-link-primary-default, #5b9cf6);
  color: var(--themed-color-link-primary-default, #5b9cf6);
}
@media (any-hover: hover) {
  .tv-button--link:hover {
    color: var(--themed-color-link-primary-hover, #1e53e5);
    fill: var(--themed-color-link-primary-hover, #1e53e5);
  }
  html.theme-dark .tv-button--link:hover {
    fill: var(--themed-color-link-primary-hover, #3179f5);
    color: var(--themed-color-link-primary-hover, #3179f5);
  }
}
.tv-button--link:active {
  color: var(--themed-color-link-primary-active, #1848cc);
  fill: var(--themed-color-link-primary-active, #1848cc);
}
html.theme-dark .tv-button--link:active {
  fill: var(--themed-color-link-primary-active, #2962ff);
  color: var(--themed-color-link-primary-active, #2962ff);
}
.tv-button--link:focus {
  outline: auto;
  outline-offset: 2px;
}
.tv-button--link:focus-visible {
  outline: auto;
  outline-offset: 2px;
}
.tv-button--link:focus:not(:focus-visible) {
  outline: none;
}
.tv-button--danger_ghost,
.tv-button--default_ghost,
.tv-button--primary_ghost,
.tv-button--secondary_ghost,
.tv-button--success_ghost,
.tv-button--warning_ghost {
  background-color: var(--themed-color-static-transparent, #0000);
  border-style: solid;
  border-width: 1px;
}
html.theme-dark .tv-button--danger_ghost,
html.theme-dark .tv-button--default_ghost,
html.theme-dark .tv-button--primary_ghost,
html.theme-dark .tv-button--secondary_ghost,
html.theme-dark .tv-button--success_ghost,
html.theme-dark .tv-button--warning_ghost {
  background-color: var(--themed-color-static-transparent, #0000);
}
.tv-button--danger_ghost.tv-button--size_large,
.tv-button--default_ghost.tv-button--size_large,
.tv-button--primary_ghost.tv-button--size_large,
.tv-button--secondary_ghost.tv-button--size_large,
.tv-button--success_ghost.tv-button--size_large,
.tv-button--warning_ghost.tv-button--size_large {
  border-width: 2px;
}
.tv-button--danger_ghost.tv-button--size_large.tv-button--thin-border,
.tv-button--default_ghost.tv-button--size_large.tv-button--thin-border,
.tv-button--primary_ghost.tv-button--size_large.tv-button--thin-border,
.tv-button--secondary_ghost.tv-button--size_large.tv-button--thin-border,
.tv-button--success_ghost.tv-button--size_large.tv-button--thin-border,
.tv-button--warning_ghost.tv-button--size_large.tv-button--thin-border {
  border-width: 1px;
}
@media (any-hover: hover) {
  .tv-button--default_ghost:hover {
    background-color: var(--themed-color-default-active-bg, #ececec);
  }
  html.theme-dark .tv-button--default_ghost:hover {
    background-color: var(--themed-color-default-active-bg, #1e222d);
  }
}
.tv-button.i-disabled,
.tv-button.i-disabled:active,
.tv-button:disabled,
.tv-button:disabled:active {
  background-color: var(--themed-color-btn-disabled-bg, #e0e3eb);
  border-color: var(--themed-color-btn-disabled-bg, #e0e3eb);
  color: var(--themed-color-text-disabled, #b2b5be);
  cursor: default;
}
@media (any-hover: hover) {
  .tv-button.i-disabled:hover,
  .tv-button:disabled:hover {
    background-color: var(--themed-color-btn-disabled-bg, #e0e3eb);
    border-color: var(--themed-color-btn-disabled-bg, #e0e3eb);
    color: var(--themed-color-text-disabled, #b2b5be);
    cursor: default;
  }
}
html.theme-dark .tv-button.i-disabled,
html.theme-dark .tv-button.i-disabled:active,
html.theme-dark .tv-button:disabled,
html.theme-dark .tv-button:disabled:active {
  background-color: var(--themed-color-btn-disabled-bg, #363a45);
}
@media (any-hover: hover) {
  html.theme-dark .tv-button.i-disabled:hover,
  html.theme-dark .tv-button:disabled:hover {
    background-color: var(--themed-color-btn-disabled-bg, #363a45);
  }
}
html.theme-dark .tv-button.i-disabled,
html.theme-dark .tv-button.i-disabled:active,
html.theme-dark .tv-button:disabled,
html.theme-dark .tv-button:disabled:active {
  border-color: var(--themed-color-btn-disabled-bg, #363a45);
}
@media (any-hover: hover) {
  html.theme-dark .tv-button.i-disabled:hover,
  html.theme-dark .tv-button:disabled:hover {
    border-color: var(--themed-color-btn-disabled-bg, #363a45);
  }
}
html.theme-dark .tv-button.i-disabled,
html.theme-dark .tv-button.i-disabled:active,
html.theme-dark .tv-button:disabled,
html.theme-dark .tv-button:disabled:active {
  color: var(--themed-color-text-disabled, #50535e);
}
@media (any-hover: hover) {
  html.theme-dark .tv-button.i-disabled:hover,
  html.theme-dark .tv-button:disabled:hover {
    color: var(--themed-color-text-disabled, #50535e);
  }
}
.tv-button.i-disabled:active,
.tv-button:disabled:active {
  transform: translateY(0);
}
.tv-button--size_xsmall {
  border-radius: 1px;
  font-size: 11px;
  font-weight: 400;
  line-height: 15px;
  padding: 2px 7px;
}
.tv-button--size_xsmall.tv-button--danger_ghost,
.tv-button--size_xsmall.tv-button--default,
.tv-button--size_xsmall.tv-button--default_ghost,
.tv-button--size_xsmall.tv-button--primary_ghost,
.tv-button--size_xsmall.tv-button--secondary_ghost,
.tv-button--size_xsmall.tv-button--state,
.tv-button--size_xsmall.tv-button--success_ghost,
.tv-button--size_xsmall.tv-button--warning_ghost {
  padding: 1px 6px;
}
.tv-button--size_xsmall + .tv-button--size_xsmall {
  margin-left: 10px;
}
.tv-button--size_small {
  font-size: 13px;
  line-height: 25px;
  padding: 1px 12px;
}
.tv-button--size_small.tv-button--danger_ghost,
.tv-button--size_small.tv-button--default,
.tv-button--size_small.tv-button--default_ghost,
.tv-button--size_small.tv-button--primary_ghost,
.tv-button--size_small.tv-button--secondary_ghost,
.tv-button--size_small.tv-button--state,
.tv-button--size_small.tv-button--success_ghost,
.tv-button--size_small.tv-button--warning_ghost {
  padding: 0 11px;
}
.tv-button--size_small + .tv-button--size_small {
  margin-left: 10px;
}
.tv-button--size_large {
  font-size: 17px;
  line-height: 44px;
  padding: 1px 30px;
}
.tv-button--size_large.tv-button--danger_ghost,
.tv-button--size_large.tv-button--default,
.tv-button--size_large.tv-button--default_ghost,
.tv-button--size_large.tv-button--primary_ghost,
.tv-button--size_large.tv-button--secondary_ghost,
.tv-button--size_large.tv-button--state,
.tv-button--size_large.tv-button--success_ghost,
.tv-button--size_large.tv-button--warning_ghost {
  padding: 0 29px;
}
.tv-button--size_promo {
  border-radius: 120px;
  font-size: 24px;
  line-height: 54px;
  padding: 1px 40px;
}
.tv-button--size_promo.tv-button--danger_ghost,
.tv-button--size_promo.tv-button--default,
.tv-button--size_promo.tv-button--default_ghost,
.tv-button--size_promo.tv-button--primary_ghost,
.tv-button--size_promo.tv-button--secondary_ghost,
.tv-button--size_promo.tv-button--state,
.tv-button--size_promo.tv-button--success_ghost,
.tv-button--size_promo.tv-button--warning_ghost {
  padding: 0 39px;
}
.tv-button--no-padding {
  padding: 1px;
}
.tv-button--no-padding.tv-button--danger_ghost,
.tv-button--no-padding.tv-button--default,
.tv-button--no-padding.tv-button--default_ghost,
.tv-button--no-padding.tv-button--primary_ghost,
.tv-button--no-padding.tv-button--secondary_ghost,
.tv-button--no-padding.tv-button--state,
.tv-button--no-padding.tv-button--success_ghost,
.tv-button--no-padding.tv-button--warning_ghost {
  padding: 0;
}
.tv-button--state {
  border-style: solid;
  border-width: 1px;
  text-align: center;
}
.tv-button--state,
html.theme-dark .tv-button--state {
  background: var(--themed-color-static-transparent, #0000);
}
.tv-button--state:after {
  content: "";
  display: inline-block;
}
.tv-button--state__checked,
.tv-button--state__uncheck-hint,
.tv-button--state__unchecked {
  display: block;
  height: 0;
  transition:
    opacity 0.2625s ease,
    transform 0.2625s ease;
}
.tv-button--state__ellipsis-text {
  display: block;
  overflow-x: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
@media (any-hover: hover) {
  .tv-button--state.i-checked:hover .tv-button--state__checked,
  .tv-button--state.i-checked:hover .tv-button--state__uncheck-hint,
  .tv-button--state.i-checked:hover .tv-button--state__unchecked {
    will-change: opacity, transform;
  }
}
.tv-button--state.i-checked .tv-button--state__unchecked,
.tv-button--state__checked,
.tv-button--state__uncheck-hint {
  opacity: 0;
}
@media (any-hover: hover) {
  .tv-button--state.i-checked:hover .tv-button--state__checked {
    opacity: 0;
  }
}
.tv-button--state.i-checked .tv-button--state__checked,
.tv-button--state__unchecked {
  opacity: 1;
}
@media (any-hover: hover) {
  .tv-button--state.i-checked:hover .tv-button--state__uncheck-hint {
    opacity: 1;
  }
  .tv-button--state.i-checked:hover .tv-button--state__checked {
    transform: translateY(-5px);
  }
}
.tv-button--state.i-checked .tv-button--state__unchecked,
.tv-button--state__checked,
.tv-button--state__uncheck-hint {
  transform: translateY(5px);
}
.tv-button--state.i-checked .tv-button--state__checked {
  transform: translateY(0);
}
@media (any-hover: hover) {
  .tv-button--state.i-checked:hover .tv-button--state__uncheck-hint {
    transform: translateY(0);
  }
}
.tv-button--state.tv-button--success {
  color: var(--themed-color-success, #089981);
}
.tv-button--state.tv-button--success,
html.theme-dark .tv-button--state.tv-button--success {
  background-color: var(--themed-color-static-transparent, #0000);
}
html.theme-dark .tv-button--state.tv-button--success {
  color: var(--themed-color-success, #056656);
}
.tv-button--state.tv-button--success.i-checked {
  background-color: var(--themed-color-success, #089981);
  color: var(--themed-color-button-text-color, #fff);
}
html.theme-dark .tv-button--state.tv-button--success.i-checked {
  background-color: var(--themed-color-success, #056656);
  color: var(--themed-color-button-text-color, #fff);
}
@media (any-hover: hover) {
  .tv-button--state.tv-button--success:hover {
    color: var(--themed-color-button-text-color, #fff);
  }
  .tv-button--state.tv-button--success:hover,
  html.theme-dark .tv-button--state.tv-button--success:hover {
    background-color: var(--themed-color-success-hover, #06806b);
  }
  html.theme-dark .tv-button--state.tv-button--success:hover {
    color: var(--themed-color-button-text-color, #fff);
  }
}
.tv-button--state.tv-button--success:active {
  background-color: var(--themed-color-success-active, #056656);
  color: var(--themed-color-button-text-color, #fff);
}
html.theme-dark .tv-button--state.tv-button--success:active {
  background-color: var(--themed-color-success-active, #089981);
  color: var(--themed-color-button-text-color, #fff);
}
.tv-button--state.tv-button--danger {
  color: var(--themed-color-btn-danger, #f7525f);
}
.tv-button--state.tv-button--danger,
html.theme-dark .tv-button--state.tv-button--danger {
  background-color: var(--themed-color-static-transparent, #0000);
}
html.theme-dark .tv-button--state.tv-button--danger {
  color: var(--themed-color-btn-danger, #b22833);
}
.tv-button--state.tv-button--danger.i-checked {
  background-color: #f7525f;
  color: #fff;
}
@media (any-hover: hover) {
  .tv-button--state.tv-button--danger:hover {
    background-color: #f7525f;
    color: #fff;
  }
}
.tv-button--state.tv-button--danger:active {
  background-color: #f23645;
  color: #fff;
}
.tv-button--state.tv-button--primary {
  color: var(--themed-color-brand, #2962ff);
}
.tv-button--state.tv-button--primary,
html.theme-dark .tv-button--state.tv-button--primary {
  background-color: var(--themed-color-static-transparent, #0000);
}
html.theme-dark .tv-button--state.tv-button--primary {
  color: var(--themed-color-brand, #2962ff);
}
.tv-button--state.tv-button--primary.i-checked {
  color: var(--themed-color-button-text-color, #fff);
}
.tv-button--state.tv-button--primary.i-checked,
html.theme-dark .tv-button--state.tv-button--primary.i-checked {
  background-color: var(--themed-color-brand, #2962ff);
}
html.theme-dark .tv-button--state.tv-button--primary.i-checked {
  color: var(--themed-color-button-text-color, #fff);
}
@media (any-hover: hover) {
  .tv-button--state.tv-button--primary:hover {
    color: var(--themed-color-button-text-color, #fff);
  }
  .tv-button--state.tv-button--primary:hover,
  html.theme-dark .tv-button--state.tv-button--primary:hover {
    background-color: var(--themed-color-brand-hover, #1e53e5);
  }
  html.theme-dark .tv-button--state.tv-button--primary:hover {
    color: var(--themed-color-button-text-color, #fff);
  }
}
.tv-button--state.tv-button--primary:active {
  color: var(--themed-color-button-text-color, #fff);
}
.tv-button--state.tv-button--primary:active,
html.theme-dark .tv-button--state.tv-button--primary:active {
  background-color: var(--themed-color-brand-active, #1848cc);
}
html.theme-dark .tv-button--state.tv-button--primary:active {
  color: var(--themed-color-button-text-color, #fff);
}
.tv-button--state.tv-button--primary_ghost-hover.i-checked {
  color: var(--themed-color-brand, #2962ff);
}
.tv-button--state.tv-button--primary_ghost-hover.i-checked,
html.theme-dark .tv-button--state.tv-button--primary_ghost-hover.i-checked {
  background-color: var(--themed-color-static-transparent, #0000);
}
html.theme-dark .tv-button--state.tv-button--primary_ghost-hover.i-checked {
  color: var(--themed-color-brand, #2962ff);
}
@media (any-hover: hover) {
  .tv-button--state.tv-button--primary_ghost-hover:hover {
    color: var(--themed-color-button-text-color, #fff);
  }
  .tv-button--state.tv-button--primary_ghost-hover:hover,
  html.theme-dark .tv-button--state.tv-button--primary_ghost-hover:hover {
    background-color: var(--themed-color-brand-hover, #1e53e5);
  }
  html.theme-dark .tv-button--state.tv-button--primary_ghost-hover:hover {
    color: var(--themed-color-button-text-color, #fff);
  }
}
.tv-button--state.tv-button--primary_ghost-hover:active {
  color: var(--themed-color-button-text-color, #fff);
}
.tv-button--state.tv-button--primary_ghost-hover:active,
html.theme-dark .tv-button--state.tv-button--primary_ghost-hover:active {
  background-color: var(--themed-color-brand-active, #1848cc);
}
html.theme-dark .tv-button--state.tv-button--primary_ghost-hover:active {
  color: var(--themed-color-button-text-color, #fff);
}
.tv-button--state.tv-button--secondary {
  background-color: initial;
  color: #787b86;
}
.tv-button--state.tv-button--secondary.i-checked {
  background-color: #f0f3fa;
  color: #787b86;
}
@media (any-hover: hover) {
  .tv-button--state.tv-button--secondary:hover {
    background-color: #e1e7f5;
    color: #787b86;
  }
}
.tv-button--state.tv-button--secondary:active {
  background-color: #d1dbf0;
  color: #787b86;
}
.tv-button--state.tv-button--warning {
  background-color: initial;
  color: #ff9800;
}
.tv-button--state.tv-button--warning.i-checked {
  background-color: #ff9800;
  color: #fff;
}
@media (any-hover: hover) {
  .tv-button--state.tv-button--warning:hover {
    background-color: #ff9100;
    color: #fff;
  }
}
.tv-button--state.tv-button--warning:active {
  background-color: #cc7014;
  color: #fff;
}
.tv-button--state.tv-button--icon-with-text svg {
  margin-bottom: -8px;
  margin-right: 6px;
  vertical-align: unset;
}
.tv-button--icon {
  align-items: center;
  display: inline-flex;
  height: 34px;
  justify-content: center;
  min-width: auto;
  padding: 0 !important;
  width: 34px;
}
.tv-button--icon.tv-button--size_xsmall {
  height: 19px;
  width: 19px;
}
.tv-button--icon.tv-button--size_small {
  height: 27px;
  width: 27px;
}
.tv-button--icon.tv-button--size_large {
  height: 46px;
  width: 46px;
}
.tv-button__icon {
  display: flex;
  margin-right: 7px;
}
.tv-button--loader .tv-button__text {
  transition:
    opacity 175ms ease,
    transform 175ms ease;
}
.tv-button--loader.i-start-load .tv-button__text {
  opacity: 0;
  transform: translateY(-5px);
}
.tv-button--loader.i-loading .tv-button__text {
  opacity: 0;
  transform: translateY(5px);
}
.tv-button--loader.i-stop-load .tv-button__text {
  opacity: 1;
  transform: translateY(0);
  transition-delay: 175ms;
}
.tv-button__loader {
  bottom: 0;
  font-size: 0;
  height: 100%;
  left: 0;
  margin: 0 auto;
  opacity: 0;
  position: absolute;
  right: 0;
  text-align: center;
  top: 0;
  transition: opacity 0.35s ease;
}
.tv-button__loader:after {
  content: "";
  display: inline-block;
  height: 100%;
  vertical-align: middle;
}
.tv-button--loader.i-loading .tv-button__loader,
.tv-button--loader.i-start-load .tv-button__loader {
  opacity: 1;
}
.tv-button--loader.i-stop-load .tv-button__loader {
  opacity: 0;
}
.tv-button__loader-item {
  background-color: #fff;
  border-radius: 100%;
  display: inline-block;
  height: 10px;
  margin-left: 2px;
  margin-right: 2px;
  opacity: 0;
  transform: translateY(12px) scale(0.6);
  transition:
    transform 0.35s cubic-bezier(0.68, -0.55, 0.265, 1.55),
    opacity 0.35s ease;
  vertical-align: middle;
  width: 10px;
}
.tv-button__loader-item:nth-child(2) {
  transition-delay: 0.11666667s;
}
.tv-button__loader-item:nth-child(3) {
  transition-delay: 233.33333ms;
}
.tv-button--default .tv-button__loader-item {
  background-color: #787b86;
}
.tv-button--loader.i-loading .tv-button__loader-item,
.tv-button--loader.i-start-load .tv-button__loader-item {
  opacity: 1;
}
.tv-button--loader.i-stop-load .tv-button__loader-item {
  opacity: 0;
}
.tv-button--loader.i-loading .tv-button__loader-item,
.tv-button--loader.i-start-load .tv-button__loader-item,
.tv-button--loader.i-stop-load .tv-button__loader-item {
  transform: translateY(0) scale(0.6);
}
.tv-button--loader.i-loading .tv-button__loader-item,
.tv-button--loader.i-stop-load .tv-button__loader-item {
  animation: tv-button-loader 0.96s ease-in-out infinite both;
}
.tv-button--loader.i-loading .tv-button__loader-item:nth-child(2),
.tv-button--loader.i-stop-load .tv-button__loader-item:nth-child(2) {
  animation-delay: 0.151s;
}
.tv-button--loader.i-loading .tv-button__loader-item:nth-child(3),
.tv-button--loader.i-stop-load .tv-button__loader-item:nth-child(3) {
  animation-delay: 0.32s;
}
.tv-button--no-border-radius {
  border-radius: 0;
}
.tv-button--no-border {
  border: none;
}
.tv-button--connect {
  border-radius: 0;
}
.tv-button--connect_left {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}
.tv-button--connect_right {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}
.tv-button--with-icon {
  align-items: center;
  display: inline-flex;
  transform: translate(0);
}
@keyframes tv-button-loader {
  0%,
  to {
    transform: scale(0.6);
  }
  50% {
    transform: scale(0.9);
  }
}
.tv-control-input {
  background-color: var(--themed-color-body-bg, #fff);
  border-color: var(--themed-color-border, #e0e3eb);
  border-radius: 2px;
  border-style: solid;
  border-width: 1px;
  box-sizing: border-box;
  color: var(--themed-color-text-regular, #434651);
  display: block;
  font-size: 13px;
  height: 34px;
  padding: 0 12px;
  width: 100%;
  -webkit-tap-highlight-color: transparent;
  -webkit-appearance: none;
  appearance: none;
  transition:
    border-color 0.35s ease,
    background-color 0.35s ease;
}
html.theme-dark .tv-control-input {
  background-color: var(--themed-color-body-bg, #000);
  border-color: var(--themed-color-border, #363a45);
  color: var(--themed-color-text-regular, #d1d4dc);
}
.tv-control-input::placeholder {
  color: var(--themed-color-placeholder, #a3a6af);
  opacity: 1;
}
html.theme-dark .tv-control-input::placeholder {
  color: var(--themed-color-placeholder, #50535e);
}
.tv-control-input:-webkit-autofill {
  -webkit-text-fill-color: #50535e !important;
  box-shadow: inset 0 0 0 1000px #fff !important;
}
.tv-control-input--size_xsmall {
  height: 19px;
}
.tv-control-input--size_small {
  height: 27px;
}
.tv-control-input--size_large {
  font-size: 16px;
  height: 48px;
}
.tv-control-input--connect {
  border-left: 0;
  border-radius: 0;
  border-right: 0;
}
.tv-control-input--connect_left {
  border-bottom-left-radius: 0;
  border-left: none;
  border-top-left-radius: 0;
}
.tv-control-input--connect_right {
  border-bottom-right-radius: 0;
  border-right: none;
  border-top-right-radius: 0;
}
@media (any-hover: hover) {
  .tv-control-input:hover {
    border-color: var(--themed-color-border-hover, #c1c4cd);
    transition-duration: 60ms;
  }
  html.theme-dark .tv-control-input:hover {
    border-color: var(--themed-color-border-hover, #50535e);
  }
}
.tv-control-input:focus {
  border-color: #2962ff !important;
  transition-duration: 60ms;
}
.tv-control-input[readonly] {
  border-color: #e0e3eb;
  color: #868993;
}
.tv-control-input[readonly]:focus {
  border-color: var(
    --themed-color-disabled-border-and-color,
    #e0e3eb
  ) !important;
}
@media (any-hover: hover) {
  .tv-control-input[readonly]:hover {
    border-color: var(
      --themed-color-disabled-border-and-color,
      #e0e3eb
    ) !important;
  }
}
html.theme-dark .tv-control-input[readonly]:focus {
  border-color: var(
    --themed-color-disabled-border-and-color,
    #2a2e39
  ) !important;
}
@media (any-hover: hover) {
  html.theme-dark .tv-control-input[readonly]:hover {
    border-color: var(
      --themed-color-disabled-border-and-color,
      #2a2e39
    ) !important;
  }
}
.tv-control-input--readonly_dark,
.tv-control-input--readonly_dark[readonly],
html.theme-dark .tv-control-input--readonly_dark,
html.theme-dark .tv-control-input--readonly_dark[readonly] {
  color: var(--themed-color-input-textarea-readonly, #50535e);
}
.tv-control-input.i-disabled,
.tv-control-input[disabled] {
  border-color: var(
    --themed-color-disabled-border-and-color,
    #e0e3eb
  ) !important;
  color: var(--themed-color-disabled-border-and-color, #e0e3eb) !important;
}
html.theme-dark .tv-control-input.i-disabled,
html.theme-dark .tv-control-input[disabled] {
  border-color: var(
    --themed-color-disabled-border-and-color,
    #2a2e39
  ) !important;
  color: var(--themed-color-disabled-border-and-color, #2a2e39) !important;
}
.tv-control-input.i-disabled::placeholder,
.tv-control-input[disabled]::placeholder {
  color: var(--themed-color-disabled-border-and-color, #e0e3eb) !important;
}
html.theme-dark .tv-control-input.i-disabled::placeholder,
html.theme-dark .tv-control-input[disabled]::placeholder {
  color: var(--themed-color-disabled-border-and-color, #2a2e39) !important;
}
@media (any-hover: hover) {
  .tv-control-input.i-disabled:hover,
  .tv-control-input[disabled]:hover {
    border-color: var(
      --themed-color-disabled-border-and-color,
      #e0e3eb
    ) !important;
  }
  html.theme-dark .tv-control-input.i-disabled:hover,
  html.theme-dark .tv-control-input[disabled]:hover {
    border-color: var(
      --themed-color-disabled-border-and-color,
      #2a2e39
    ) !important;
  }
}
.tv-control-input.i-error {
  border-color: #f7525f !important;
}
@media (any-hover: hover) {
  .tv-control-input.i-error:hover {
    border-color: #f7525f !important;
  }
}
.tv-control-input.i-error:focus {
  border-color: #f23645 !important;
}
.tv-control-input.i-success {
  border-color: var(--themed-color-success, #089981) !important;
}
html.theme-dark .tv-control-input.i-success {
  border-color: var(--themed-color-success, #056656) !important;
}
@media (any-hover: hover) {
  .tv-control-input.i-success:hover,
  html.theme-dark .tv-control-input.i-success:hover {
    border-color: var(--themed-color-success-hover, #06806b) !important;
  }
}
.tv-control-input.i-success:active,
.tv-control-input.i-success:focus {
  border-color: var(--themed-color-success-active, #056656) !important;
}
html.theme-dark .tv-control-input.i-success:active,
html.theme-dark .tv-control-input.i-success:focus {
  border-color: var(--themed-color-success-active, #089981) !important;
}
.tv-control-input--phone {
  direction: ltr;
  text-align: left;
}
body,
html {
  background: none;
  height: 100%;
  overflow: hidden;
  width: 100%;
}
body {
  min-width: 240px;
}
#library-container {
  background: #fff;
  border: 1px solid #d9dadb;
}
#library-container #showExtendedHoursLink {
  display: none;
}
.on-widget .open-popup {
  background: #6798bb;
  border-width: 0 !important;
  border: none;
  margin-left: 4px;
  padding: 7px;
}
@media (any-hover: hover) {
  .on-widget .open-popup:hover {
    background: #69a3cc;
  }
}
.on-widget .open-popup:active {
  background: #71acd6;
}
.on-widget .open-popup svg {
  display: block;
  height: 14px;
  width: 16px;
  fill: #fff;
}
.on-widget
  .widgetbar-widget-hotlist
  .widgetbar-widgetheader
  .widgetbar-headerspace {
  display: none;
}
.text .logo-highlighted {
  font-weight: 700;
  text-decoration: underline;
}
.on-cme-widget .symbol-edit-popup .filter,
.on-cme-widget .symbol-search-dialog .filter {
  display: none;
}
.on-cme-widget .symbol-search-dialog .results {
  height: 450px;
}
.load-chart .chart-search,
.load-chart .chart-search input {
  width: 100%;
}
@media only screen and (max-width: 750px) {
  .charts-popup-list .item.save-load-chart-title {
    display: block;
  }
}
.charts-popup-list .item .title,
.charts-popup-list .item .title-expanded {
  width: auto;
}
.common-tooltip-EJBD96zX {
  font-family:
    -apple-system,
    BlinkMacSystemFont,
    Trebuchet MS,
    Roboto,
    Ubuntu,
    sans-serif;
  font-feature-settings:
    "tnum" on,
    "lnum" on;
  font-style: normal;
  --ui-lib-typography-font-size: 13px;
  font-size: var(--ui-lib-typography-font-size);
  font-weight: 400;
  --ui-lib-typography-line-height: 18px;
  color: var(--themed-color-common-tooltip-text, #f0f3fa);
  display: inline-flex;
  line-height: var(--ui-lib-typography-line-height);
  opacity: 1;
  pointer-events: none;
  position: fixed;
  transition: opacity 0.15s linear;
  z-index: 1000;
}
.common-tooltip--hidden-EJBD96zX {
  opacity: 0;
}
.common-tooltip--horizontal-EJBD96zX {
  margin: 4px 0;
}
.common-tooltip--horizontal-EJBD96zX.common-tooltip--farther-EJBD96zX {
  margin: 8px 0;
}
.common-tooltip--vertical-EJBD96zX {
  margin: 0 4px;
}
.common-tooltip--vertical-EJBD96zX.common-tooltip-farther-EJBD96zX {
  margin: 0 8px;
}
.common-tooltip--direction_normal-EJBD96zX {
  flex-direction: row;
}
.common-tooltip--direction_normal-EJBD96zX .common-tooltip__body-EJBD96zX {
  border-bottom-left-radius: 2px;
  border-top-left-radius: 2px;
}
.common-tooltip--direction_normal-EJBD96zX
  .common-tooltip__body--no-buttons-EJBD96zX,
.common-tooltip--direction_normal-EJBD96zX
  .common-tooltip__button-container-EJBD96zX {
  border-bottom-right-radius: 2px;
  border-top-right-radius: 2px;
}
.common-tooltip--direction_normal-EJBD96zX
  .common-tooltip__button-EJBD96zX:not(:last-child) {
  margin-right: 1px;
}
.common-tooltip--direction_reversed-EJBD96zX {
  flex-direction: row-reverse;
}
.common-tooltip--direction_reversed-EJBD96zX .common-tooltip__body-EJBD96zX {
  border-bottom-right-radius: 2px;
  border-top-right-radius: 2px;
}
.common-tooltip--direction_reversed-EJBD96zX
  .common-tooltip__body--no-buttons-EJBD96zX,
.common-tooltip--direction_reversed-EJBD96zX
  .common-tooltip__button-container-EJBD96zX {
  border-bottom-left-radius: 2px;
  border-top-left-radius: 2px;
}
.common-tooltip--direction_reversed-EJBD96zX
  .common-tooltip__button-EJBD96zX:not(:first-child) {
  margin-left: 1px;
}
.common-tooltip__ear-holder-EJBD96zX {
  position: relative;
}
.common-tooltip__ear-holder-EJBD96zX:after {
  border: 0 solid;
  border-color: var(--themed-color-common-tooltip-bg, #2a2e39);
  box-sizing: border-box;
  content: "";
  display: block;
  height: 0;
  position: absolute;
  width: 0;
}
html.theme-dark .common-tooltip__ear-holder-EJBD96zX:after {
  border-color: var(--themed-color-common-tooltip-bg, #363a45);
}
.common-tooltip__ear-holder--above-EJBD96zX:after,
.common-tooltip__ear-holder--below-EJBD96zX:after {
  border-left: 6px solid;
  border-left-color: var(--themed-color-tooltip-force-transparent, #0000);
  border-right: 6px solid;
  border-right-color: var(--themed-color-tooltip-force-transparent, #0000);
  left: 50%;
  margin-left: -6px;
}
html.theme-dark .common-tooltip__ear-holder--above-EJBD96zX:after,
html.theme-dark .common-tooltip__ear-holder--below-EJBD96zX:after {
  border-left-color: var(--themed-color-tooltip-force-transparent, #0000);
  border-right-color: var(--themed-color-tooltip-force-transparent, #0000);
}
.common-tooltip__ear-holder--below-EJBD96zX:after {
  border-bottom-width: 4px;
  bottom: 100%;
}
.common-tooltip__ear-holder--above-EJBD96zX:after {
  border-top-width: 4px;
  top: 100%;
}
.common-tooltip__ear-holder--after-EJBD96zX:after,
.common-tooltip__ear-holder--before-EJBD96zX:after {
  border-bottom: 6px solid;
  border-bottom-color: var(--themed-color-tooltip-force-transparent, #0000);
  border-top: 6px solid;
  border-top-color: var(--themed-color-tooltip-force-transparent, #0000);
  margin-top: -6px;
  top: 50%;
}
html.theme-dark .common-tooltip__ear-holder--after-EJBD96zX:after,
html.theme-dark .common-tooltip__ear-holder--before-EJBD96zX:after {
  border-bottom-color: var(--themed-color-tooltip-force-transparent, #0000);
  border-top-color: var(--themed-color-tooltip-force-transparent, #0000);
}
.common-tooltip__ear-holder--before-EJBD96zX:after {
  border-right-width: 4px;
  right: 100%;
}
.common-tooltip__ear-holder--after-EJBD96zX:after {
  border-left-width: 4px;
  left: 100%;
}
.common-tooltip__body-EJBD96zX {
  background-color: var(--themed-color-common-tooltip-bg, #2a2e39);
  box-sizing: border-box;
  display: block;
  max-width: 310px;
  padding: 3px 8px;
  position: relative;
  white-space: pre-wrap;
  word-wrap: break-word;
  overflow: hidden;
  text-align: left;
}
html.theme-dark .common-tooltip__body-EJBD96zX {
  background-color: var(--themed-color-common-tooltip-bg, #363a45);
}
.common-tooltip__body--with-hotkey-EJBD96zX {
  display: flex;
  max-width: 420px;
  padding: 0;
}
.common-tooltip__body--width_wide-EJBD96zX {
  max-width: 640px;
}
.common-tooltip__body--width_narrow-EJBD96zX {
  max-width: 200px;
}
.common-tooltip__body--no-padding-EJBD96zX {
  padding: 0;
}
.common-tooltip__hotkey-block-EJBD96zX {
  align-items: center;
  color: var(--themed-color-common-tooltip-hotkey-text, #ff9800);
  display: inline-flex;
  flex: 1 0 auto;
  justify-content: center;
  line-height: 12px;
  padding: 4px 8px 5px;
}
.common-tooltip__hotkey-block--divider-EJBD96zX {
  border-left: 1px solid
    var(--themed-color-common-tooltip-hotkey-divider, #5d606b);
  margin-left: 8px;
}
html.theme-dark .common-tooltip__hotkey-block--divider-EJBD96zX {
  border-left: 1px solid
    var(--themed-color-common-tooltip-hotkey-divider, #868993);
}
.common-tooltip__hotkey-text-EJBD96zX {
  align-items: center;
  display: inline-flex;
  margin: 3px 0 3px 8px;
}
.common-tooltip__hotkey-button-EJBD96zX {
  align-items: center;
  border: 1px solid;
  border-radius: 2px;
  display: inline-flex;
  height: 13px;
  justify-content: center;
  min-width: 7px;
  padding: 0 3px;
}
.common-tooltip__plus-sign-EJBD96zX {
  height: 15px;
  line-height: 16px;
  text-align: center;
  width: 13px;
}
.common-tooltip__button-container-EJBD96zX {
  display: flex;
  overflow: hidden;
  position: relative;
}
.common-tooltip__button-EJBD96zX {
  align-items: center;
  background-color: #2962ff;
  color: #fff;
  display: flex;
  padding: 0 10px;
}
@media (any-hover: hover) {
  .common-tooltip__button-EJBD96zX:hover {
    background-color: #bbd9fb;
  }
}
.common-tooltip-EJBD96zX.theme-white {
  color: var(--themed-color-text-primary, #131722);
}
html.theme-dark .common-tooltip-EJBD96zX.theme-white {
  color: var(--themed-color-text-primary, #d1d4dc);
}
.common-tooltip-EJBD96zX.theme-white .common-tooltip__body-EJBD96zX {
  background-color: var(--themed-color-bg-primary, #fff);
  border-radius: 0;
}
html.theme-dark
  .common-tooltip-EJBD96zX.theme-white
  .common-tooltip__body-EJBD96zX {
  background-color: var(--themed-color-bg-primary, #1e222d);
}
.common-tooltip-EJBD96zX.theme-white .common-tooltip__ear-holder-EJBD96zX {
  border: 1px solid var(--themed-color-border, #e0e3eb);
}
html.theme-dark
  .common-tooltip-EJBD96zX.theme-white
  .common-tooltip__ear-holder-EJBD96zX {
  border: 1px solid var(--themed-color-border, #363a45);
}
.common-tooltip-EJBD96zX.theme-white
  .common-tooltip__ear-holder-EJBD96zX:after {
  border-color: var(--themed-color-bg-primary, #fff);
}
html.theme-dark
  .common-tooltip-EJBD96zX.theme-white
  .common-tooltip__ear-holder-EJBD96zX:after {
  border-color: var(--themed-color-bg-primary, #1e222d);
}
.common-tooltip-EJBD96zX.theme-white
  .common-tooltip__ear-holder--above-EJBD96zX:after,
.common-tooltip-EJBD96zX.theme-white
  .common-tooltip__ear-holder--below-EJBD96zX:after {
  border-left: 6px solid;
  border-left-color: var(
    --themed-color-common-tooltip-force-transparent,
    #0000
  );
  border-right: 6px solid;
  border-right-color: var(
    --themed-color-common-tooltip-force-transparent,
    #0000
  );
}
html.theme-dark
  .common-tooltip-EJBD96zX.theme-white
  .common-tooltip__ear-holder--above-EJBD96zX:after,
html.theme-dark
  .common-tooltip-EJBD96zX.theme-white
  .common-tooltip__ear-holder--below-EJBD96zX:after {
  border-left-color: var(
    --themed-color-common-tooltip-force-transparent,
    #0000
  );
  border-right-color: var(
    --themed-color-common-tooltip-force-transparent,
    #0000
  );
}
.common-tooltip-EJBD96zX.theme-white
  .common-tooltip__ear-holder--after-EJBD96zX:after,
.common-tooltip-EJBD96zX.theme-white
  .common-tooltip__ear-holder--before-EJBD96zX:after {
  border-bottom: 6px solid;
  border-bottom-color: var(
    --themed-color-common-tooltip-force-transparent,
    #0000
  );
  border-top: 6px solid;
  border-top-color: var(--themed-color-common-tooltip-force-transparent, #0000);
}
html.theme-dark
  .common-tooltip-EJBD96zX.theme-white
  .common-tooltip__ear-holder--after-EJBD96zX:after,
html.theme-dark
  .common-tooltip-EJBD96zX.theme-white
  .common-tooltip__ear-holder--before-EJBD96zX:after {
  border-bottom-color: var(
    --themed-color-common-tooltip-force-transparent,
    #0000
  );
  border-top-color: var(--themed-color-common-tooltip-force-transparent, #0000);
}
.common-tooltip-EJBD96zX.theme-white
  .common-tooltip__ear-holder-EJBD96zX:before {
  border: 0 solid;
  border-color: var(--themed-color-border, #e0e3eb);
  content: "";
  display: block;
  height: 0;
  position: absolute;
  width: 0;
  z-index: 1000;
}
html.theme-dark
  .common-tooltip-EJBD96zX.theme-white
  .common-tooltip__ear-holder-EJBD96zX:before {
  border-color: var(--themed-color-border, #363a45);
}
.common-tooltip-EJBD96zX.theme-white
  .common-tooltip__ear-holder--above-EJBD96zX:before,
.common-tooltip-EJBD96zX.theme-white
  .common-tooltip__ear-holder--below-EJBD96zX:before {
  border-left: 7px solid;
  border-left-color: var(
    --themed-color-common-tooltip-force-transparent,
    #0000
  );
  border-right: 7px solid;
  border-right-color: var(
    --themed-color-common-tooltip-force-transparent,
    #0000
  );
  left: 50%;
  margin-left: -7px;
}
html.theme-dark
  .common-tooltip-EJBD96zX.theme-white
  .common-tooltip__ear-holder--above-EJBD96zX:before,
html.theme-dark
  .common-tooltip-EJBD96zX.theme-white
  .common-tooltip__ear-holder--below-EJBD96zX:before {
  border-left-color: var(
    --themed-color-common-tooltip-force-transparent,
    #0000
  );
  border-right-color: var(
    --themed-color-common-tooltip-force-transparent,
    #0000
  );
}
.common-tooltip-EJBD96zX.theme-white
  .common-tooltip__ear-holder--below-EJBD96zX:before {
  border-bottom-width: 6px;
  top: -6px;
}
.common-tooltip-EJBD96zX.theme-white
  .common-tooltip__ear-holder--above-EJBD96zX:before {
  border-top-width: 6px;
  bottom: -6px;
}
.common-tooltip-EJBD96zX.theme-white
  .common-tooltip__ear-holder--after-EJBD96zX:before,
.common-tooltip-EJBD96zX.theme-white
  .common-tooltip__ear-holder--before-EJBD96zX:before {
  border-bottom: 7px solid;
  border-bottom-color: var(
    --themed-color-common-tooltip-force-transparent,
    #0000
  );
  border-top: 7px solid;
  border-top-color: var(--themed-color-common-tooltip-force-transparent, #0000);
  margin-top: -7px;
  top: 50%;
}
html.theme-dark
  .common-tooltip-EJBD96zX.theme-white
  .common-tooltip__ear-holder--after-EJBD96zX:before,
html.theme-dark
  .common-tooltip-EJBD96zX.theme-white
  .common-tooltip__ear-holder--before-EJBD96zX:before {
  border-bottom-color: var(
    --themed-color-common-tooltip-force-transparent,
    #0000
  );
  border-top-color: var(--themed-color-common-tooltip-force-transparent, #0000);
}
.common-tooltip-EJBD96zX.theme-white
  .common-tooltip__ear-holder--before-EJBD96zX:before {
  border-right-width: 6px;
  left: -6px;
}
.common-tooltip-EJBD96zX.theme-white
  .common-tooltip__ear-holder--after-EJBD96zX:before {
  border-left-width: 6px;
  right: -6px;
}
.common-tooltip-EJBD96zX.theme-round-shadow {
  box-shadow: 0 1px 3px 0 #2a2c394a;
  color: var(--themed-color-text-primary, #131722);
}
html.theme-dark .common-tooltip-EJBD96zX.theme-round-shadow {
  color: var(--themed-color-text-primary, #d1d4dc);
}
.common-tooltip-EJBD96zX.theme-round-shadow .common-tooltip__body-EJBD96zX {
  background-color: var(--themed-color-bg-primary, #fff);
}
html.theme-dark
  .common-tooltip-EJBD96zX.theme-round-shadow
  .common-tooltip__body-EJBD96zX {
  background-color: var(--themed-color-bg-primary, #1e222d);
}
.common-tooltip-EJBD96zX.theme-round-shadow
  .common-tooltip__ear-holder-EJBD96zX:after {
  border-color: var(--themed-color-bg-primary, #fff);
}
html.theme-dark
  .common-tooltip-EJBD96zX.theme-round-shadow
  .common-tooltip__ear-holder-EJBD96zX:after {
  border-color: var(--themed-color-bg-primary, #1e222d);
}
.common-tooltip-EJBD96zX.theme-round-shadow
  .common-tooltip__ear-holder--above-EJBD96zX:after,
.common-tooltip-EJBD96zX.theme-round-shadow
  .common-tooltip__ear-holder--below-EJBD96zX:after {
  border-left: 6px solid;
  border-left-color: var(
    --themed-color-common-tooltip-force-transparent,
    #0000
  );
  border-right: 6px solid;
  border-right-color: var(
    --themed-color-common-tooltip-force-transparent,
    #0000
  );
}
html.theme-dark
  .common-tooltip-EJBD96zX.theme-round-shadow
  .common-tooltip__ear-holder--above-EJBD96zX:after,
html.theme-dark
  .common-tooltip-EJBD96zX.theme-round-shadow
  .common-tooltip__ear-holder--below-EJBD96zX:after {
  border-left-color: var(
    --themed-color-common-tooltip-force-transparent,
    #0000
  );
  border-right-color: var(
    --themed-color-common-tooltip-force-transparent,
    #0000
  );
}
.common-tooltip-EJBD96zX.theme-round-shadow
  .common-tooltip__ear-holder--after-EJBD96zX:after,
.common-tooltip-EJBD96zX.theme-round-shadow
  .common-tooltip__ear-holder--before-EJBD96zX:after {
  border-bottom: 6px solid;
  border-bottom-color: var(
    --themed-color-common-tooltip-force-transparent,
    #0000
  );
  border-top: 6px solid;
  border-top-color: var(--themed-color-common-tooltip-force-transparent, #0000);
}
html.theme-dark
  .common-tooltip-EJBD96zX.theme-round-shadow
  .common-tooltip__ear-holder--after-EJBD96zX:after,
html.theme-dark
  .common-tooltip-EJBD96zX.theme-round-shadow
  .common-tooltip__ear-holder--before-EJBD96zX:after {
  border-bottom-color: var(
    --themed-color-common-tooltip-force-transparent,
    #0000
  );
  border-top-color: var(--themed-color-common-tooltip-force-transparent, #0000);
}
.common-tooltip-EJBD96zX.theme-chart .common-tooltip__body-EJBD96zX {
  background-color: var(--themed-color-bg-primary, #fff);
  border: 1px solid var(--themed-color-divider, #e0e3eb);
  border-radius: 16px;
  box-shadow: 0 2px 4px
    var(--themed-color-shadow-primary-neutral-extra-heavy, #0003);
  max-width: 342px;
}
html.theme-dark
  .common-tooltip-EJBD96zX.theme-chart
  .common-tooltip__body-EJBD96zX {
  background-color: var(--themed-color-bg-primary, #1e222d);
  border: 1px solid var(--themed-color-divider, #434651);
  box-shadow: 0 2px 4px
    var(--themed-color-shadow-primary-neutral-extra-heavy, #0006);
}
.common-tooltip-EJBD96zX.theme-chart
  .common-tooltip__ear-holder-EJBD96zX:after {
  content: none;
}
.container-B8mkOfAH {
  background-color: var(
    --tv-color-platform-background,
    var(--themed-color-chart-page-bg, #e0e3eb)
  );
  box-sizing: border-box;
  height: 100%;
  width: 100%;
}
html.theme-dark .container-B8mkOfAH {
  background-color: var(
    --tv-color-platform-background,
    var(--themed-color-chart-page-bg, #2a2e39)
  );
}
.container-B8mkOfAH .inner-B8mkOfAH {
  background-color: var(
    --tv-color-pane-background,
    var(--themed-color-pane-bg, #fff)
  );
  height: 100%;
  width: 100%;
}
html.theme-dark .container-B8mkOfAH .inner-B8mkOfAH {
  background-color: var(
    --tv-color-pane-background,
    var(--themed-color-pane-bg, #131722)
  );
}
.container-B8mkOfAH.border-left-B8mkOfAH {
  padding-left: 4px;
}
.container-B8mkOfAH.border-right-B8mkOfAH {
  padding-right: 4px;
}
.container-B8mkOfAH.border-top-B8mkOfAH {
  padding-top: 4px;
}
.container-B8mkOfAH.border-bottom-B8mkOfAH {
  padding-bottom: 4px;
}
.container-B8mkOfAH.top-right-radius-B8mkOfAH .inner-B8mkOfAH {
  border-top-right-radius: 0;
}
.container-B8mkOfAH.top-left-radius-B8mkOfAH .inner-B8mkOfAH {
  border-top-left-radius: 0;
}
.container-B8mkOfAH.bottom-right-radius-B8mkOfAH .inner-B8mkOfAH {
  border-bottom-right-radius: 0;
}
.container-B8mkOfAH.bottom-left-radius-B8mkOfAH .inner-B8mkOfAH {
  border-bottom-left-radius: 0;
}
.tv-spinner {
  animation: tv-spinner__container-rotate-aLqboHuu 0.9s linear infinite;
  border-bottom: 0 solid #9598a133;
  border-left: 0 solid #9598a133;
  border-left-color: var(--tv-spinner-color, #2962ff);
  border-radius: 50%;
  border-right: 0 solid #9598a133;
  border-top: 0 solid #9598a133;
  border-top-color: var(--tv-spinner-color, #2962ff);
  display: none;
  margin: 0 auto;
  position: absolute;
}
.tv-spinner--shown {
  display: block;
}
.tv-spinner--size_xxsmall {
  border-width: 2px;
  height: 10px;
  left: calc(50% - 7px);
  top: calc(50% - 7px);
  width: 10px;
}
.tv-spinner--size_xsmall {
  border-width: 2px;
  height: 14px;
  left: calc(50% - 9px);
  top: calc(50% - 9px);
  width: 14px;
}
.tv-spinner--size_small {
  border-width: 2px;
  height: 20px;
  left: calc(50% - 12px);
  top: calc(50% - 12px);
  width: 20px;
}
.tv-spinner--size_medium {
  border-width: 3px;
  height: 28px;
  left: calc(50% - 17px);
  top: calc(50% - 17px);
  width: 28px;
}
.tv-spinner--size_large {
  border-width: 4px;
  height: 56px;
  left: calc(50% - 32px);
  top: calc(50% - 32px);
  width: 56px;
}
@keyframes tv-spinner__container-rotate-aLqboHuu {
  to {
    transform: rotate(1turn);
  }
}
.screen-otjoFNF2 {
  bottom: 0;
  display: none;
  left: 0;
  opacity: 0.5;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 8;
}
.screen-otjoFNF2.fade-otjoFNF2 {
  animation: screenfade-otjoFNF2 0.3s ease backwards;
  display: block;
}
@keyframes screenfade-otjoFNF2 {
  0% {
    opacity: 0;
  }
}
.paneSeparator-uqBaC1Ki {
  margin: 0;
  padding: 0;
  position: relative;
}
.paneSeparator-uqBaC1Ki .handle-uqBaC1Ki {
  height: 9px;
  left: 0;
  position: absolute;
  top: -4px;
  width: 100%;
  z-index: 50;
}
.paneSeparator-uqBaC1Ki .handle-uqBaC1Ki.active-uqBaC1Ki,
.paneSeparator-uqBaC1Ki .handle-uqBaC1Ki.hovered-uqBaC1Ki {
  background: #b2b5be33;
  cursor: row-resize;
}
.chart-widget--themed-dark
  .paneSeparator-uqBaC1Ki
  .handle-uqBaC1Ki.active-uqBaC1Ki,
.chart-widget--themed-dark
  .paneSeparator-uqBaC1Ki
  .handle-uqBaC1Ki.hovered-uqBaC1Ki {
  background: #b2b5be1f;
}
.paneSeparator-uqBaC1Ki .handle-uqBaC1Ki.active-uqBaC1Ki:before {
  background: #b2b5be03;
  content: "";
  height: 100%;
  left: 0;
  opacity: 0;
  position: fixed;
  top: 0;
  width: 100%;
}
.price-axis-currency-label-wrapper-y5H41VPj {
  box-sizing: border-box;
  padding: 4px;
  pointer-events: none;
  position: absolute;
  -webkit-user-select: none;
  user-select: none;
  width: 100%;
  z-index: 3;
}
.price-axis-currency-label-wrapper-y5H41VPj.hidden-y5H41VPj {
  visibility: hidden;
}
.price-axis-currency-label-y5H41VPj {
  background: #fff;
  border: 1px solid #e0e3eb;
  border-radius: 4px;
  box-sizing: border-box;
  cursor: default;
  width: 100%;
}
.price-axis-currency-label-y5H41VPj .row-y5H41VPj {
  align-items: center;
  color: #131722;
  column-gap: 2px;
  display: flex;
  height: 24px;
  justify-content: space-between;
  line-height: 1em;
  padding: 0 3px;
}
.price-axis-currency-label-y5H41VPj
  .row-y5H41VPj:nth-child(1 of :not(.js-hidden)) {
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
}
.price-axis-currency-label-y5H41VPj
  .row-y5H41VPj:nth-last-child(-n + 1 of :not(.js-hidden)) {
  border-bottom-left-radius: 3px;
  border-bottom-right-radius: 3px;
}
@media (any-hover: hover) {
  .price-axis-currency-label-y5H41VPj .row-y5H41VPj:hover:not(.readonly) {
    background: #f0f3fa;
  }
}
.price-axis-currency-label-y5H41VPj .row-y5H41VPj.expanded-y5H41VPj {
  background: #e0e3eb;
}
.price-axis-currency-label-y5H41VPj
  .row-y5H41VPj.expanded-y5H41VPj
  .price-axis-currency-label-arrow-down-y5H41VPj {
  transform: scaleY(-1);
}
.price-axis-currency-label-y5H41VPj div {
  pointer-events: auto;
}
.price-axis-currency-label-text-y5H41VPj {
  white-space: nowrap;
}
.price-axis-currency-label-arrow-down-y5H41VPj {
  align-self: center;
  display: flex;
}
.chart-widget__bottom--themed-dark
  .price-axis-currency-label-wrapper-y5H41VPj
  .price-axis-currency-label-y5H41VPj {
  background: #131722;
  border: 1px solid #434651;
}
.chart-widget__bottom--themed-dark
  .price-axis-currency-label-wrapper-y5H41VPj
  .price-axis-currency-label-y5H41VPj
  .row-y5H41VPj {
  color: #d1d4dc;
}
@media (any-hover: hover) {
  .chart-widget__bottom--themed-dark
    .price-axis-currency-label-wrapper-y5H41VPj
    .price-axis-currency-label-y5H41VPj
    .row-y5H41VPj:hover:not(.readonly) {
    background: #2a2e39;
  }
}
.chart-widget__bottom--themed-dark
  .price-axis-currency-label-wrapper-y5H41VPj
  .price-axis-currency-label-y5H41VPj
  .row-y5H41VPj.expanded-y5H41VPj {
  background: #363a45;
}
.priceScaleModeButtons-PEm49B2T {
  --priceScaleModeButtons-color: #131722;
  --priceScaleModeButtons-background: #fff;
  --priceScaleModeButtons-background-hover: #f0f3fa;
  --priceScaleModeButtons-border-hover: #e0e3eb;
  --priceScaleModeButtons-background-active: #e0e3eb;
  --priceScaleModeButtons-border-active: #e0e3eb;
  --priceScaleModeButtons-background-activated: #2962ff;
  --priceScaleModeButtons-background-activated-hover: #1e53e5;
  --priceScaleModeButtons-border: #e0e3eb;
  --priceScaleModeButtons-text-activated: #fff;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  width: 100%;
}
.chart-widget__bottom--themed-dark .priceScaleModeButtons-PEm49B2T {
  --priceScaleModeButtons-color: #d1d4dc;
  --priceScaleModeButtons-background: #131722;
  --priceScaleModeButtons-background-hover: #2a2e39;
  --priceScaleModeButtons-border-hover: #434651;
  --priceScaleModeButtons-background-active: #363a45;
  --priceScaleModeButtons-border-active: #434651;
  --priceScaleModeButtons-border: #434651;
}
.priceScaleModeButtons__buttonWrapper-PEm49B2T {
  display: flex;
  flex: 1 1;
  padding: 4px;
}
.priceScaleModeButtons__buttonWrapper-PEm49B2T:first-child {
  justify-content: flex-end;
  padding-right: 2px;
}
.priceScaleModeButtons__buttonWrapper-PEm49B2T:last-child {
  justify-content: flex-start;
  padding-left: 2px;
}
.priceScaleModeButtons__button-PEm49B2T {
  font-family:
    -apple-system,
    BlinkMacSystemFont,
    Trebuchet MS,
    Roboto,
    Ubuntu,
    sans-serif;
  font-feature-settings:
    "tnum" on,
    "lnum" on;
  font-style: normal;
  --ui-lib-typography-font-size: 14px;
  font-size: var(--ui-lib-typography-font-size);
  font-weight: 400;
  --ui-lib-typography-line-height: 18px;
  background-color: var(--priceScaleModeButtons-background);
  border: 1px solid var(--priceScaleModeButtons-border);
  border-radius: 4px;
  color: var(--priceScaleModeButtons-color);
  display: flex;
  justify-content: center;
  line-height: var(--ui-lib-typography-line-height);
  padding: 1px 0;
  width: 20px;
}
@media (any-hover: hover) {
  .priceScaleModeButtons__button-PEm49B2T:hover {
    background-color: var(--priceScaleModeButtons-background-hover);
    border-color: var(--priceScaleModeButtons-border-hover);
  }
}
.priceScaleModeButtons__button-PEm49B2T:active {
  background-color: var(--priceScaleModeButtons-background-active);
  border-color: var(--priceScaleModeButtons-border-active);
}
.priceScaleModeButtons__button_activated-PEm49B2T {
  background-color: var(--priceScaleModeButtons-background-activated);
  border-color: var(--priceScaleModeButtons-background-activated);
  color: var(--priceScaleModeButtons-text-activated);
}
@media (any-hover: hover) {
  .priceScaleModeButtons__button_activated-PEm49B2T:hover {
    background-color: var(--priceScaleModeButtons-background-activated-hover);
    border-color: var(--priceScaleModeButtons-background-activated-hover);
  }
}
.price-axis {
  cursor: default;
  height: 100%;
  overflow: hidden;
  position: absolute;
}
.price-axis--cursor-grabbing {
  cursor: grabbing;
}
.price-axis--cursor-pointer {
  cursor: pointer;
}
.price-axis--cursor-ns-resize {
  cursor: ns-resize;
}
.price-axis__modeButtons {
  bottom: 0;
  margin: 0 1px;
  position: absolute;
  width: calc(100% - 2px);
  z-index: 3;
}
.price-axis__modeButtons_hidden {
  visibility: hidden;
}
.pane {
  cursor: crosshair;
  overflow: hidden;
}
.pane--cursor-pointer {
  cursor: pointer;
}
.pane--cursor-eraser {
  cursor: url(eraser.********************.cur), default;
}
.pane--cursor-dot {
  cursor: url(dot.3d617b6b01edba83a7f4.cur), default;
}
.pane--cursor-performance {
  cursor: url(performance.769cf9dda2ede7d12b74.svg), default;
}
.pane--cursor-default {
  cursor: default;
}
.pane--cursor-grabbing {
  cursor: grabbing;
}
.pane--cursor-zoom-in {
  cursor: zoom-in;
}
.pane--cursor-ew-resize {
  cursor: ew-resize;
}
.pane--cursor-ns-resize {
  cursor: ns-resize;
}
.pane--cursor-nwse-resize {
  cursor: nwse-resize;
}
.pane--cursor-nesw-resize {
  cursor: nesw-resize;
}
.pane--cursor-text {
  cursor: text;
}
.pane--cursor-none {
  cursor: none;
}
.time-axis {
  cursor: default;
}
.time-axis--cursor-grabbing {
  cursor: grabbing;
}
.time-axis--cursor-ew-resize {
  cursor: ew-resize;
}
.chart-widget {
  border-style: none;
  box-sizing: border-box;
  height: 256px;
  left: 0;
  margin: 0;
  overflow: hidden;
  padding: 0;
  position: absolute;
  top: 0;
  width: 512px;
}
.chart-markup-table {
  border: none;
  border-collapse: collapse;
  border-spacing: 0;
  box-sizing: border-box;
  line-height: 0px;
}
.chart-gui-wrapper {
  align-items: flex-start;
  direction: ltr;
  display: flex;
  height: 100%;
  max-height: 100%;
  max-width: 100%;
  overflow: hidden;
  position: relative;
  width: 100%;
}
.chart-page {
  background-color: var(
    --tv-color-platform-background,
    var(--themed-color-chart-page-bg, #e0e3eb)
  );
}
html.theme-dark .chart-page {
  background-color: var(
    --tv-color-platform-background,
    var(--themed-color-chart-page-bg, #2a2e39)
  );
}
.chart-page .chart-container {
  contain: strict;
  position: relative;
}
.chart-page .chart-container-border {
  background-color: var(--themed-color-pane-bg, #fff);
  border: none;
  height: 100%;
  overflow: hidden;
  position: relative;
  width: 100%;
  z-index: 0;
}
html.theme-dark .chart-page .chart-container-border {
  background-color: var(--themed-color-pane-bg, #131722);
}
.chart-page .chart-container.multiple.active:after {
  border: 2px solid;
  border-color: var(--themed-color-chart-active-outline, #2962ff);
  bottom: 0;
  box-sizing: border-box;
  content: "";
  display: block;
  left: 0;
  pointer-events: none;
  position: absolute;
  right: 0;
  top: 0;
}
html.theme-dark .chart-page .chart-container.multiple.active:after {
  border-color: var(--themed-color-chart-active-outline, #143eb3);
}
.chart-page .chart-container.inactive .back-to-present {
  display: none;
}
.chart-page .chart-container.no-header-toolbar .chart-container-border {
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}
.chart-page .chart-container.no-bottom-toolbar .chart-container-border {
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}
.layout-with-border-radius
  .chart-container.top-left-chart
  .chart-container-border,
.layout-with-border-radius
  .chart-container.top-left-chart.multiple.active:after {
  border-radius: 4px 0 0 0;
}
.layout-with-border-radius
  .chart-container.top-right-chart
  .chart-container-border,
.layout-with-border-radius
  .chart-container.top-right-chart.multiple.active:after {
  border-radius: 0 4px 0 0;
}
.layout-with-border-radius
  .chart-container.top-full-width-chart
  .chart-container-border,
.layout-with-border-radius
  .chart-container.top-full-width-chart.multiple.active:after {
  border-radius: 4px 4px 0 0;
}
.layout-with-border-radius
  .no-border-top-left-radius
  .chart-container
  .chart-container-border,
.layout-with-border-radius
  .no-border-top-left-radius
  .chart-container.multiple.active:after {
  border-top-left-radius: 0;
}
.layout-with-border-radius
  .no-border-top-right-radius
  .chart-container
  .chart-container-border,
.layout-with-border-radius
  .no-border-top-right-radius
  .chart-container.multiple.active:after {
  border-top-right-radius: 0;
}
.chartsSplitter-L0xapso5 {
  background: "transparent";
  position: absolute;
}
.chartsSplitter-L0xapso5.hovered-L0xapso5 {
  background: #2962ff26;
}
.chartsSplitter-L0xapso5.i-active-L0xapso5:before {
  background: #b2b5be03;
  content: "";
  height: 100%;
  left: 0;
  opacity: 0;
  position: fixed;
  top: 0;
  width: 100%;
}
.chart-controls-bar {
  background-color: var(
    --tv-color-pane-background,
    var(--themed-color-pane-bg, #fff)
  );
  border-top: 1px solid;
  border-color: var(
    --tv-color-platform-background,
    var(--themed-color-chart-page-bg, #e0e3eb)
  );
  border-radius: 0 0 4px 4px;
  box-sizing: border-box;
  contain: strict;
  height: 39px;
  overflow: hidden;
  position: absolute;
}
html.theme-dark .chart-controls-bar {
  background-color: var(
    --tv-color-pane-background,
    var(--themed-color-pane-bg, #131722)
  );
  border-color: var(
    --tv-color-platform-background,
    var(--themed-color-chart-page-bg, #2a2e39)
  );
}
.no-border-bottom-left-radius .chart-controls-bar {
  border-bottom-left-radius: 0;
}
.no-border-bottom-right-radius .chart-controls-bar {
  border-bottom-right-radius: 0;
}
@font-face {
  font-family: EuclidCircular;
  font-style: normal;
  font-weight: 400;
  src: url(EuclidCircular.be8f862db48c2976009f.woff2) format("woff2");
}
