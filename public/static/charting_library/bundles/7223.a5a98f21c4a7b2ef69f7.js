(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[7223],{4611:e=>{e.exports={calendar:"calendar-N6r5jhbE",popupStyle:"popupStyle-N6r5jhbE",header:"header-N6r5jhbE","flip-horizontal":"flip-horizontal-N6r5jhbE","sub-header":"sub-header-N6r5jhbE","view-month":"view-month-N6r5jhbE","view-year":"view-year-N6r5jhbE","view-decades":"view-decades-N6r5jhbE",weeks:"weeks-N6r5jhbE",week:"week-N6r5jhbE",day:"day-N6r5jhbE",hover:"hover-N6r5jhbE","accent-color":"accent-color-N6r5jhbE","another-month":"another-month-N6r5jhbE","current-day":"current-day-N6r5jhbE","slot-wrapper":"slot-wrapper-N6r5jhbE","hide-focus-ring":"hide-focus-ring-N6r5jhbE","decade-button":"decade-button-N6r5jhbE","visually-hidden":"visually-hidden-N6r5jhbE"}},30029:e=>{e.exports={container:"container-PNiXwSz6",icon:"icon-PNiXwSz6",tooltip:"tooltip-PNiXwSz6",date:"date-PNiXwSz6",time:"time-PNiXwSz6"}},83588:e=>{e.exports={pickerInput:"pickerInput-P2cJzZdH",icon:"icon-P2cJzZdH",disabled:"disabled-P2cJzZdH",picker:"picker-P2cJzZdH",fixed:"fixed-P2cJzZdH",absolute:"absolute-P2cJzZdH",nativePicker:"nativePicker-P2cJzZdH"}},57296:e=>{e.exports={tooltip:"tooltip-RU08GcsY"}},54153:e=>{e.exports={wrap:"wrap-NsE0FV0Z",input:"input-NsE0FV0Z"}},86432:e=>{e.exports={icon:"icon-Rubz29lH"}},27365:(e,t,n)=>{"use strict";n.d(t,{getChartTimezoneOffsetMs:()=>o,getTimezoneName:()=>a});var s=n(2740),r=n.n(s);function a(e){const t=e.model().timezone();if("exchange"!==t)return t;const n=e.model().mainSeries().symbolInfo();return null==n?void 0:n.timezone}function o(e,t){if(void 0===t)return 0;return r().get_timezone(t).offset_utc(e)}},51826:(e,t,n)=>{"use strict";n.d(t,{DialogsOpenerManager:()=>s,dialogsOpenerManager:()=>r});class s{constructor(){this._storage=new Map}setAsOpened(e,t){this._storage.set(e,t)}setAsClosed(e){this._storage.delete(e)}isOpened(e){return this._storage.has(e)}getDialogPayload(e){return this._storage.get(e)}}const r=new s},93301:(e,t,n)=>{"use strict";n.d(t,{Calendar:()=>$});var s=n(50959),r=n(97754),a=n(14543),o=n(9745),i=n(17140),l=n(4611);function u(e){const{prevAriaLabel:t,nextAriaLabel:n,currentAriaLabel:r,currentVisibleTitle:u,isNextDisabled:c,isPrevDisabled:d,isViewModeDisabled:h,prevRef:p,middleRef:f,onPrevClick:m,onNextClick:g,onCurrentClick:v,onPrevKeyDown:D,onMiddleKeyDown:y,onHeaderKeyDown:w}=e;return s.createElement("div",{className:l.header,onKeyDown:w},s.createElement(a.LightButton,{startSlot:s.createElement(o.Icon,{icon:i}),onClick:m,size:"small",variant:"ghost","aria-label":t,disabled:d,onKeyDown:D,reference:p}),s.createElement(a.LightButton,{size:"small",variant:"ghost","aria-label":r,onClick:v,disabled:h,onKeyDown:y,reference:f},u),s.createElement(a.LightButton,{startSlot:s.createElement(o.Icon,{icon:i}),onClick:g,size:"small",variant:"ghost","aria-label":n,disabled:c,className:l["flip-horizontal"]}))}var c=n(23935),d=n(82826),h=n(11542)
;const p=()=>[h.t(null,void 0,n(200)),h.t(null,void 0,n(81069)),h.t(null,void 0,n(93878)),h.t(null,void 0,n(28896)),h.t(null,void 0,n(25734)),h.t(null,void 0,n(61487)),h.t(null,void 0,n(6608)),h.t(null,void 0,n(11081)),h.t(null,void 0,n(32179)),h.t(null,void 0,n(37997)),h.t(null,void 0,n(4607)),h.t(null,void 0,n(90082))],f={get prevMonth(){return h.t(null,void 0,n(93984))},get nextMonth(){return h.t(null,void 0,n(92790))},get prevYear(){return h.t(null,void 0,n(75776))},get nextYear(){return h.t(null,void 0,n(69102))},get prevDecades(){return h.t(null,void 0,n(44701))},get nextDecades(){return h.t(null,void 0,n(17538))},get selectMonth(){return h.t(null,void 0,n(70235))},get selectYear(){return h.t(null,void 0,n(71961))},get selectDate(){return h.t(null,void 0,n(91245))}},m={get setMonth(){return h.t(null,void 0,n(99470))},get setYear(){return h.t(null,void 0,n(7861))},get setDecades(){return h.t(null,void 0,n(65728))}};var g;!function(e){e.Month="month",e.Year="year",e.Decades="decades"}(g||(g={}));const v=20;function D(e){return`${[h.t(null,void 0,n(61480)),h.t(null,void 0,n(19573)),h.t(null,void 0,n(82160)),h.t(null,void 0,n(94226)),h.t(null,void 0,n(79137)),h.t(null,void 0,n(3570)),h.t(null,void 0,n(30348))][e.getDay()]} ${e.getDate()} ${p()[e.getMonth()]} ${e.getFullYear()}`}function y(e,t){switch(e){case g.Month:{const e=new Date(t);return e.setMonth(e.getMonth()-1),`${f.prevMonth}, ${p()[e.getMonth()]} ${e.getFullYear()}`}case g.Year:return`${f.prevYear}, ${t.getFullYear()-1}`;case g.Decades:return`${f.prevDecades}, ${t.getFullYear()-v} - ${t.getFullYear()-1}`}}function w(e,t){switch(e){case g.Month:{const e=new Date(t);return e.setMonth(e.getMonth()+1),`${f.nextMonth}, ${p()[e.getMonth()]} ${e.getFullYear()}`}case g.Year:return`${f.nextYear}, ${t.getFullYear()+1}`;case g.Decades:return`${f.nextDecades}, ${t.getFullYear()+v} - ${t.getFullYear()+40-1}`}}function b(e,t){switch(e){case g.Month:return`${f.selectMonth}, ${t.getFullYear()}`;case g.Year:return`${f.selectYear}, ${t.getFullYear()} - ${t.getFullYear()+v-1}`;case g.Decades:return`${f.selectDate}, ${p()[t.getMonth()]} ${t.getFullYear()}`}}function F(e,t){switch(e){case g.Month:return`${p()[t.getMonth()]} ${t.getFullYear()}`;case g.Year:return`${t.getFullYear()}`;case g.Decades:return`${t.getFullYear()} - ${t.getFullYear()+v-1}`}}function _(e){return 11===e.getMonth()?new Date(e.getFullYear()+1,0,1):new Date(e.getFullYear(),e.getMonth()+1,1)}class C extends s.PureComponent{constructor(){super(...arguments),this._dateFormatter=new d.DateFormatter,this._getVariant=()=>{let e="ghost";return this._withinSelectedRange()&&(e=this.props.isDisabled?"secondary":"quiet-primary"),this._isOnHighlightedEdge()&&this.props.isDisabled&&(e="quiet-primary"),e},this._onClick=()=>{this.props.onClick&&!this.props.isDisabled&&this.props.onClick(new Date(this.props.day))},this._onPointerOver=()=>{this.props.onHover&&!this.props.isDisabled&&this.props.onHover(new Date(this.props.day))},this._onPointerOut=()=>{this.props.onHover&&!this.props.isDisabled&&this.props.onHover(null)}}
render(){const e=r(l.day,this.props.isDisabled&&l.disabled,!this.props.isDisabled&&(this.props.isSelected||this._isOnHighlightedEdge())&&l["accent-color"],this._withinSelectedRange()&&l["within-selected-range"],this._isCurrentDay()&&l["current-day"],!this.props.showFocusRing&&l["hide-focus-ring"],this.props.isAnotherMonth&&l["another-month"]),t=this.props.isSelected||this._isOnHighlightedEdge()||this._withinSelectedRange(),n=t||this._isCurrentDay();return s.createElement(a.LightButton,{onClick:this._onClick,onPointerOver:this._onPointerOver,onPointerOut:this._onPointerOut,onFocus:this.props.onFocus,size:"small",variant:this._getVariant(),isSelected:n,"data-day":this._dateFormatter.formatLocal(this.props.day),className:r(e,this.props.forceHover&&l.hover),disabled:this.props.isDisabled,reference:this.props.reference,tabIndex:this.props.tabIndex,"aria-label":D(this.props.day),"aria-selected":t,"aria-current":this._isCurrentDay()?"date":void 0},this.props.day.getDate())}_isOnHighlightedEdge(){const{day:e,highlightedFrom:t,highlightedTo:n}=this.props;return!(!t||!n)&&((0,c.isSameDay)(e,t)||(0,c.isSameDay)(e,n))}_withinSelectedRange(){const{day:e,highlightedFrom:t,highlightedTo:n}=this.props;return!(!t||!n)&&this._isBetweenByDay(t,e,n)}_isCurrentDay(){var e;return(0,c.isSameDay)(null!==(e=this.props.todayDate)&&void 0!==e?e:new Date,this.props.day)}_isBetweenByDay(e,t,n){const s=(0,c.resetToDayStart)(e),r=(0,c.resetToDayStart)(t),a=(0,c.resetToDayStart)(n);return s<r&&r<a}}function E(e){const{days:t,showFocusRing:n,dateInTabOrder:r,isDisabledDate:a,highlightedFrom:o,highlightedTo:i,setCurrentlyFocused:u,setItemRef:d,selectedDate:h,viewDate:p,onClickDay:f,onDayHover:m,onDayFocus:g,todayDate:v,forceHoverTo:D,forceHoverFrom:y}=e;return s.createElement("div",{className:l.week},t.map((e=>s.createElement(C,{key:e.toDateString(),day:e,isDisabled:a(e),isSelected:(0,c.isSameDay)(e,h),onClick:f,onHover:m,highlightedFrom:o,highlightedTo:i,forceHover:y&&D&&e>=y&&e<=D,reference:d(e),tabIndex:(0,c.isSameDay)(e,r)?0:-1,onFocus:()=>function(e){null==g||g(e),(0,c.isSameDay)(e,r)&&u(e)}(e),showFocusRing:n,todayDate:v,isAnotherMonth:!(0,c.isSameMonth)(e,p)}))))}var k=n(78869),S=n(68335);function M({isDisabledDate:e,findDate:t,getFirstDate:n,getLastDate:r,setNext:a,setPrev:o,dateToFocus:i,verticalOffset:l,dateLevel:u}){const[d,h]=(0,k.useRefsMap)(),p=(0,s.useCallback)((t=>t?e(t)?null:t:null),[e]),f=(0,s.useCallback)(((n,s)=>{if(!n)return null;const r=(0,c.getCloneDateWithOffset)({dateFrom:n,offset:s,isDisabledDate:e,level:u});return p(t(r))}),[t,p]),m=(0,s.useCallback)((()=>{const e=n();return p(e)||f(e,1)}),[p,f]),g=(0,s.useCallback)((()=>{const e=r();return p(e)||f(e,-1)}),[p,f]),v=(0,s.useCallback)(((t,n)=>{if(!t)return;const s=(0,c.getCloneDateWithOffset)({dateFrom:t,offset:n,isDisabledDate:e,level:u});s&&(n>0?a(s):o(s))}),[e,a,o]),{currentlyFocused:D,setCurrentlyFocused:y,focusItem:w,bindings:b}=function({refsMap:e,verticalOffset:t,getNextKeyToFocus:n,getFirstKey:r,getLastKey:a,onGridEnd:o}){const[i,l]=(0,s.useState)(null),u=(0,
s.useCallback)((t=>{if(!t)return;const n=e.current.get(t);n&&(n.focus(),l(t))}),[e]),c=(0,s.useCallback)((e=>{const t=n(i,e);t?u(t):o(i,e)}),[i,n]),d=(0,s.useCallback)((e=>{const n=(0,S.hashFromEvent)(e);if(40===n&&(e.preventDefault(),c(t)),38===n&&(e.preventDefault(),c(-1*t)),39===n&&(e.preventDefault(),c(1)),37===n&&(e.preventDefault(),c(-1)),36===n){e.preventDefault();const t=r();u(t)}if(35===n){e.preventDefault();const t=a();u(t)}}),[i,n]);return{currentlyFocused:i,setCurrentlyFocused:l,focusItem:u,bindings:{onKeyDown:d}}}({refsMap:d,verticalOffset:l,getNextKeyToFocus:f,getFirstKey:m,getLastKey:g,onGridEnd:v});return(0,s.useEffect)((()=>w(p(t(i)))),[i]),{itemsRefs:d,setItemRef:h,ensureNotDisabledDate:p,currentlyFocused:D,setCurrentlyFocused:y,focusItem:w,bindings:b}}function N(e){const{selectedDate:t,viewDate:r,dateToFocus:a,weeks:o,onClickDay:i,setPrevMonth:u,setNextMonth:d,maxDate:p,minDate:f,disableWeekends:m,showFocusRing:g,highlightedFrom:v,highlightedTo:D,isDisabled:y,focusableDateRef:w,onDayHover:b,onDayFocus:F,todayDate:_,forceHoverFrom:C,forceHoverTo:k}=e,S=(0,s.useCallback)((e=>{if(!e)return null;let t=null;return o.find((({days:n})=>(t=n.find((t=>(0,c.isSameDay)(t,e))),t))),t}),[o]),N=(0,s.useCallback)((()=>o[0].days[0]),[o]),x=(0,s.useCallback)((()=>{const e=o[o.length-1].days;return e[e.length-1]}),[o]),I=(0,s.useCallback)((e=>y||(0,c.isDayDisabled)({day:e,minDate:f,maxDate:p,disableWeekends:m})),[f,p,m,y]),{itemsRefs:R,setItemRef:T,currentlyFocused:P,setCurrentlyFocused:Y,bindings:H}=M({isDisabledDate:I,findDate:S,getFirstDate:N,getLastDate:x,setPrev:u,setNext:d,dateToFocus:a,verticalOffset:7,dateLevel:"day"}),z=(0,c.getDateInTabOrder)({selectedDate:t,dateToFocus:a,currentlyFocused:P,firstEnabledDate:(0,c.getFirstEnabledDay)(o,I),getFirstDate:N,getLastDate:x,isDisabledDate:I});return(0,s.useEffect)((()=>{w.current=z&&R.current.get(z)||null}),[z,w]),s.createElement(s.Fragment,null,s.createElement("div",{className:l["sub-header"],"aria-hidden":!0},[h.t(null,{context:"day_of_week"},n(30961)),h.t(null,{context:"day_of_week"},n(9135)),h.t(null,{context:"day_of_week"},n(92578)),h.t(null,{context:"day_of_week"},n(8765)),h.t(null,{context:"day_of_week"},n(97349)),h.t(null,{context:"day_of_week"},n(94748)),h.t(null,{context:"day_of_week"},n(75005))].map((e=>s.createElement("span",{key:e},e)))),s.createElement("div",{className:l["view-month"],tabIndex:-1,...H},s.createElement("div",{className:l.weeks},o.map((e=>s.createElement(E,{key:e.week,setCurrentlyFocused:Y,setItemRef:T,dateInTabOrder:z,days:e.days,onDayFocus:F,onDayHover:b,isDisabledDate:I,selectedDate:t,viewDate:r,onClickDay:i,highlightedFrom:v,highlightedTo:D,showFocusRing:g,todayDate:_,forceHoverFrom:C,forceHoverTo:k}))))))}function x(e){const{months:t,selectedDate:r,maxDate:o,minDate:i,showFocusRing:u,dateToFocus:d,isDisabled:p,focusableDateRef:f,onSelect:m,setPrevYear:g,setNextYear:v}=e,D=(0,s.useCallback)((e=>p||!(0,c.isInRange)(e,i,o,"month")),[i,o,p]),y=(0,s.useCallback)((e=>{if(!e)return null;let n=null;return t.find((({date:t})=>(n=(0,
c.isSameMonth)(e,t)?t:null,n))),n}),[t]),w=(0,s.useCallback)((()=>t[0].date),[t]),b=(0,s.useCallback)((()=>t[t.length-1].date),[t]),{itemsRefs:F,setItemRef:_,currentlyFocused:C,setCurrentlyFocused:E,bindings:k}=M({isDisabledDate:D,findDate:y,getFirstDate:w,getLastDate:b,setPrev:g,setNext:v,verticalOffset:3,dateToFocus:d,dateLevel:"month"}),S=(0,c.getDateInTabOrder)({selectedDate:r,dateToFocus:d,currentlyFocused:C,firstEnabledDate:(0,c.getFirstEnabledMonth)(t,D),getFirstDate:w,getLastDate:b,isDisabledDate:D});return(0,s.useEffect)((()=>{f.current=S&&F.current.get(S)||null}),[S,f]),s.createElement(s.Fragment,null,s.createElement("div",{className:l["sub-header"],"aria-hidden":!0},s.createElement("span",null,h.t(null,void 0,n(43154)))),s.createElement("div",{className:l["view-year"],...k},t.map((({title:e,ariaLabel:t,date:n})=>{const o=(0,c.isSameMonth)(n,r),i=D(n),d=o?"quiet-primary":"ghost";return s.createElement(a.LightButton,{key:n.toDateString(),size:"small",variant:d,disabled:i,isSelected:o,className:!u&&l["hide-focus-ring"],onClick:()=>m(n),reference:_(n),tabIndex:(0,c.isSameMonth)(n,S)?0:-1,onFocus:(0,c.isSameMonth)(n,S)?()=>E(n):void 0,"aria-label":`${t} ${n.getFullYear()}`,"aria-selected":o},e)}))))}function I(e){const{years:t,selectedDate:o,dateToFocus:i,maxDate:u,minDate:d,showFocusRing:p,isDisabled:f,focusableDateRef:m,onSelect:g,setPrevDecades:v,setNextDecades:D}=e,y=(0,s.useCallback)((e=>f||!(0,c.isInRange)(e,d,u,"year")),[d,u,f]),w=(0,s.useCallback)((e=>e&&t.find((t=>(0,c.isSameYear)(e,t)))||null),[t]),b=(0,s.useCallback)((()=>t[0]),[t]),F=(0,s.useCallback)((()=>t[t.length-1]),[t]),{itemsRefs:_,setItemRef:C,currentlyFocused:E,setCurrentlyFocused:k,bindings:S}=M({isDisabledDate:y,findDate:w,getFirstDate:b,getLastDate:F,setPrev:v,setNext:D,dateToFocus:i,verticalOffset:4,dateLevel:"year"}),N=(0,c.getDateInTabOrder)({selectedDate:o,dateToFocus:i,currentlyFocused:E,firstEnabledDate:(0,c.getFirstEnabledYear)(t,y),getFirstDate:b,getLastDate:F,isDisabledDate:y});return(0,s.useEffect)((()=>{m.current=N&&_.current.get(N)||null}),[N,m]),s.createElement(s.Fragment,null,s.createElement("div",{className:l["sub-header"],"aria-hidden":!0},s.createElement("span",null,h.t(null,void 0,n(66181)))),s.createElement("div",{className:l["view-decades"],...S},t.map((e=>{const t=e.getFullYear(),n=(0,c.isSameYear)(e,o),i=y(e),u=n?"quiet-primary":"ghost";return s.createElement(a.LightButton,{key:e.toDateString(),size:"small",variant:u,disabled:i,isSelected:n,className:r(l["decade-button"],!p&&l["hide-focus-ring"]),onClick:()=>g(e),reference:C(e),tabIndex:(0,c.isSameYear)(e,N)?0:-1,onFocus:(0,c.isSameYear)(e,N)?()=>k(e):void 0,"aria-selected":n},t)}))))}function R({selectedDate:e,autoFocus:t,showFocusRing:r,minDate:a,maxDate:o,onSelect:i,onMonthSwitch:l,onYearSwitch:u,onDecadesSwitch:d,onViewTypeChange:f,fullSixWeeks:m}){const[D,y]=(0,s.useState)(e),[w,b]=(0,s.useState)(g.Month),[F,C]=(0,s.useState)(t?e:null),E=(0,s.useMemo)((()=>(0,c.getDecadesStart)(D)),[D]),k=(0,s.useCallback)((e=>{
const t=new Date(D),n=_(D),s=new Date(n.getFullYear(),n.getMonth()+e,0).getDate(),r=D.getDate()>s?s:D.getDate();t.setMonth(t.getMonth()+e,r),y(t),l&&l(t)}),[D,l]),S=(0,s.useCallback)((e=>{const t=new Date(D);t.setFullYear(t.getFullYear()+e),y(t),u&&u(t)}),[D,u]),M=(0,s.useCallback)((e=>{const t=new Date(E);t.setFullYear(t.getFullYear()+e),y(t),d&&d(t)}),[E,D,d]),N=(0,s.useCallback)(((e,t)=>{switch(t&&C(t),w){case g.Month:return k(1*e);case g.Year:return S(1*e);case g.Decades:return M(e*v)}}),[w,k,S,M]),x=(0,s.useCallback)((e=>N(-1,e)),[N]),I=(0,s.useCallback)((e=>N(1,e)),[N]),R=(0,s.useCallback)((()=>{const e=Object.values(g);let t=e.indexOf(w)+1;t>=e.length&&(t=0),b(e[t]),f&&f(e[t])}),[w]),T=(0,s.useCallback)((e=>{y(new Date(e)),i&&i(new Date(e))}),[i]),P=(0,s.useCallback)((e=>{const t=new Date(D);t.setMonth(e.getMonth()),(0,c.isSameMonth)(t,e)||t.setMonth(e.getMonth(),1),y(t);const n=new Date(F||D);n.setMonth(e.getMonth()),(0,c.isSameMonth)(n,e)||n.setMonth(e.getMonth(),1),C(n),b(g.Month)}),[D,i,R]),Y=(0,s.useCallback)((e=>{const t=new Date(D);t.setFullYear(e.getFullYear()),y(t);const n=new Date(F||D);n.setFullYear(e.getFullYear()),C(n),b(g.Year)}),[D,i,R]),H=(0,s.useMemo)((()=>(0,c.getWeeks)(D,m)),[D,m]),z=(0,s.useMemo)((()=>function(e){return[h.t(null,void 0,n(62310)),h.t(null,void 0,n(2507)),h.t(null,void 0,n(92767)),h.t(null,void 0,n(27072)),h.t(null,void 0,n(25734)),h.t(null,void 0,n(429)),h.t(null,void 0,n(53786)),h.t(null,void 0,n(46450)),h.t(null,void 0,n(6816)),h.t(null,void 0,n(12179)),h.t(null,void 0,n(26899)),h.t(null,void 0,n(32084))].map(((t,n)=>{const s=(0,c.resetToMonthStart)(e);return s.setMonth(n),{title:t,ariaLabel:p()[n],date:s}}))}(D)),[D]),$=(0,s.useMemo)((()=>function(e){const t=[];for(let n=0;n<v;n++){const s=new Date(e);s.setFullYear(e.getFullYear()+n),t.push(s)}return t}(E)),[E]),O=(0,s.useMemo)((()=>{switch(w){case g.Month:{const e=H[H.length-1].days,t=new Date(e[e.length-1]);return t.setDate(t.getDate()+1),!(0,c.isInRange)(t,a,o)}case g.Year:{const e=new Date(z[z.length-1].date);return e.setMonth(e.getMonth()+1),!(0,c.isInRange)(e,a,o,"month")}case g.Decades:{const e=new Date($[$.length-1]);return e.setFullYear(e.getFullYear()+1),!(0,c.isInRange)(e,a,o,"year")}}}),[w,a,o,H,z,$]),L=(0,s.useMemo)((()=>{switch(w){case g.Month:{const e=new Date(H[0].days[0]);return e.setDate(e.getDate()-1),!(0,c.isInRange)(e,a,o)}case g.Year:{const e=new Date(z[0].date);return e.setMonth(e.getMonth()-1),!(0,c.isInRange)(e,a,o,"month")}case g.Decades:{const e=new Date($[0]);return e.setFullYear(e.getFullYear()-1),!(0,c.isInRange)(e,a,o,"year")}}}),[w,a,o,H,z,$]);return(0,s.useEffect)((()=>{!F&&r&&t&&C(e),r||C(null)}),[t,e,F,r]),{viewDate:D,viewType:w,dateToFocus:F,weeks:H,months:z,years:$,isNextDisabled:O,isPrevDisabled:L,setPrev:x,setNext:I,switchViewType:R,onClickDay:T,onClickMonth:P,onClickYear:Y}}class T{constructor(e,t=[]){this._messagesQueue=[],this._alternate=!1,this._renderedMessage=null,this._idCounter=0,this._containers=[...t],this._type=e}setContainers(e){this._containers=[...e]}addMessage(e,t=0){
const n=this._generateId(),s={id:n,message:e,destroyTimeout:this._calculateDestroyTimeout(e,t)};return this._messagesQueue.push(s),this._renderedMessage||this._renderMessage(),{...s,type:this._type,destroy:this._getDestroyMessageCallback(n)}}destroyAll(){var e;this._messagesQueue=[],clearTimeout(null===(e=this._renderedMessage)||void 0===e?void 0:e.destroyTimer),this._containers.forEach((e=>{e.innerText=""})),this._renderedMessage=null,this._alternate=!1}_generateId(){return`live-region-${this._type}-${this._idCounter++}`}_calculateDestroyTimeout(e,t=0){const n=50*e.trim().length+200,s=250*e.trim().length;return Math.min(Math.max(n,t),s)}_findById(e){var t;return(null===(t=this._renderedMessage)||void 0===t?void 0:t.id)===e?this._renderedMessage:this._messagesQueue.find((t=>t.id===e))}_getDestroyMessageCallback(e){return async t=>{const n=this._findById(e);return!!n&&(n.renderedTo?!!t&&(this._removeRenderedMessage(),this._renderMessage(),!0):(this._removeFromQueue(n),!0))}}_removeRenderedMessage(){var e;const t=null===(e=this._renderedMessage)||void 0===e?void 0:e.renderedTo;t&&(t.innerText=""),this._renderedMessage=null}_removeFromQueue(e){this._messagesQueue=this._messagesQueue.filter((t=>t!==e))}async _renderMessage(){if(!this._containers.length||!this._messagesQueue.length)return;const e=this._alternate?this._containers[0]:this._containers[1],t=this._messagesQueue.shift();let n;e.innerText=t.message;const s=new Promise(((e,s)=>{n=setTimeout((()=>{var n;if((null===(n=this._renderedMessage)||void 0===n?void 0:n.id)===t.id)return this._removeRenderedMessage(),void this._renderMessage().then(e);s("Currently rendered message is not the one that you 're trying to destroy")}),t.destroyTimeout)}));this._renderedMessage={...t,renderedTo:e,destroyTimer:n,destroyPromise:s},this._alternate=!this._alternate}}class P extends T{}const Y=new class{constructor(){this.isInited=!1,this._politeQueue=new P("polite"),this._assertiveQueue=new P("assertive")}renderTo(e,t=!1){return t&&this.destroy(),this._init(e)}destroy(){this._politeQueue.destroyAll(),this._assertiveQueue.destroyAll(),this.isInited=!1}sayPolitely(e,t){return this.isInited?this._politeQueue.addMessage(e,t):null}interrupt(e,t){return this.isInited?this._assertiveQueue.addMessage(e,t):null}_init(e){if(!e)return!1;if(this.isInited)return!1;return!!this._setContainers(e)&&(this.isInited=!0,!0)}_setContainers(e){const t=document.getElementById(e);if(!t)return!1;const n=t.querySelectorAll('[aria-live="polite"]'),s=t.querySelectorAll('[aria-live="assertive"]');return!(!n||2!==n.length||!s||2!==s.length)&&(this._politeQueue.setContainers([n[0],n[1]]),this._assertiveQueue.setContainers([s[0],s[1]]),!0)}};var H;function z({viewType:e,decadesStartYear:t,viewDate:n,setPrev:r,setNext:a,switchViewType:o}){const[i,l]=(0,s.useState)(null),u=(0,s.useCallback)((e=>{i&&i.destroy(),l(Y.sayPolitely(e))}),[i,l]),c=(0,s.useCallback)((()=>{u(function(e,t){switch(e){case g.Month:{const e=new Date(t);return e.setMonth(e.getMonth()-1),m.setMonth.format({month:p()[e.getMonth()]})}case g.Year:
return m.setYear.format({year:""+(t.getFullYear()-1)});case g.Decades:return m.setDecades.format({year_start:""+(t.getFullYear()-v),year_end:""+(t.getFullYear()-1)})}}(e,e===g.Decades?t:n)),r(null)}),[r,u]),d=(0,s.useCallback)((()=>{u(function(e,t){switch(e){case g.Month:{const e=_(t);return m.setMonth.format({month:p()[e.getMonth()]})}case g.Year:return m.setYear.format({year:`${t.getFullYear()+1}`});case g.Decades:return m.setDecades.format({year_start:`${t.getFullYear()+v}`,year_end:""+(t.getFullYear()+40-1)})}}(e,e===g.Decades?t:n)),a(null)}),[a,u]),h=(0,s.useCallback)((s=>{u(function(e,t){switch(e){case g.Month:return m.setYear.format({year:`${t.getFullYear()}`});case g.Year:return m.setDecades.format({year_start:`${t.getFullYear()}`,year_end:""+(t.getFullYear()+v-1)});case g.Decades:return m.setMonth.format({month:p()[t.getMonth()]})}}(e,e===g.Year?t:n)),o(s)}),[o,u]);return{onPrevClick:c,onNextClick:d,onSwitchViewType:h}}function $(e){const{selectedDate:t,maxDate:n,minDate:a,className:o,disableWeekends:i,highlightedFrom:c,highlightedTo:d,todayDate:h,popupStyle:p=!0,showFocusRing:f=!1,autoFocus:m=!1,isDisabled:v=!1,withFocusTrap:D=!1,endSlot:_,onSelect:C,onMonthSwitch:E,onYearSwitch:k,onDecadesSwitch:M,onViewTypeChange:T,focusTriggerElement:P,fullSixWeeks:Y,onDayHover:H,onDayFocus:$,forceHoverTo:O,forceHoverFrom:L}=e,[K,V]=(0,s.useState)(f),B=(0,s.useCallback)((()=>V(!0)),[V]);(0,s.useEffect)((()=>{f&&V(!0)}),[f]);const A=(0,s.useCallback)((e=>{P&&38===(0,S.hashFromEvent)(e)&&(e.preventDefault(),e.stopPropagation(),V(!1),P())}),[P]),{viewDate:j,viewType:Q,dateToFocus:W,weeks:Z,months:J,years:q,isNextDisabled:X,isPrevDisabled:U,setPrev:G,setNext:ee,switchViewType:te,onClickDay:ne,onClickMonth:se,onClickYear:re}=R({selectedDate:t,minDate:a,maxDate:n,autoFocus:m,showFocusRing:K,onMonthSwitch:E,onYearSwitch:k,onDecadesSwitch:M,onViewTypeChange:T,onSelect:C,fullSixWeeks:Y}),{focusableDateRef:ae,prevRef:oe,middleRef:ie,onPrevKeyDown:le,onMiddleKeyDown:ue,forwardFocusToStart:ce}=function({withFocusTrap:e,isPrevDisabled:t}){const n=(0,s.useRef)(null),r=(0,s.useCallback)((t=>{var s;e&&S.Modifiers.Shift+9===(0,S.hashFromEvent)(t)&&(t.preventDefault(),null===(s=n.current)||void 0===s||s.focus())}),[e]),a=(0,s.useCallback)((e=>{t||r(e)}),[t,r]),o=(0,s.useCallback)((e=>{t&&r(e)}),[t,r]),i=(0,s.useRef)(null),l=(0,s.useRef)(null),u=(0,s.useCallback)((e=>{var n,s;e.preventDefault(),e.stopPropagation(),t?null===(s=l.current)||void 0===s||s.focus():null===(n=i.current)||void 0===n||n.focus()}),[t]);return{focusableDateRef:n,prevRef:i,middleRef:l,onPrevKeyDown:a,onMiddleKeyDown:o,forwardFocusToStart:u}}({withFocusTrap:D,isPrevDisabled:U}),de=(0,s.useMemo)((()=>q[0]),[q]),{onPrevClick:he,onNextClick:pe,onSwitchViewType:fe}=z({viewType:Q,decadesStartYear:de,viewDate:j,setPrev:G,setNext:ee,switchViewType:te});return s.createElement("div",{className:r(l.calendar,p&&l.popupStyle,o),tabIndex:-1,onKeyDown:B},s.createElement(u,{prevAriaLabel:y(Q,Q===g.Decades?de:j),nextAriaLabel:w(Q,Q===g.Decades?de:j),
currentAriaLabel:b(Q,Q===g.Year?de:j),currentVisibleTitle:F(Q,Q===g.Decades?de:j),onPrevClick:he,onNextClick:pe,onPrevKeyDown:le,onMiddleKeyDown:ue,onHeaderKeyDown:A,onCurrentClick:fe,isNextDisabled:v||X,isPrevDisabled:v||U,isViewModeDisabled:v,prevRef:oe,middleRef:ie}),Q===g.Month&&s.createElement(N,{weeks:Z,selectedDate:t,viewDate:j,dateToFocus:W,maxDate:n,minDate:a,onClickDay:ne,disableWeekends:i,highlightedFrom:c,highlightedTo:d,setPrevMonth:G,setNextMonth:ee,showFocusRing:K,isDisabled:v,focusableDateRef:ae,todayDate:h,onDayHover:H,onDayFocus:$,forceHoverFrom:L,forceHoverTo:O}),Q===g.Year&&s.createElement(x,{months:J,selectedDate:t,dateToFocus:W,maxDate:n,minDate:a,onSelect:se,setPrevYear:G,setNextYear:ee,showFocusRing:K,isDisabled:v,focusableDateRef:ae}),Q===g.Decades&&s.createElement(I,{years:q,selectedDate:t,dateToFocus:W,maxDate:n,minDate:a,onSelect:re,setPrevDecades:G,setNextDecades:ee,showFocusRing:K,isDisabled:v,focusableDateRef:ae}),s.createElement("div",{className:l["slot-wrapper"]},_),D&&s.createElement("div",{tabIndex:0,onFocus:ce}))}!function(e){e.Assertive="assertive",e.Polite="polite"}(H||(H={}))},85528:(e,t,n)=>{"use strict";n.d(t,{DatePicker:()=>E});var s=n(11542),r=n(50959),a=n(97754),o=n(5325),i=n(32563),l=n(82826),u=n(93301),c=n(50151),d=n(9745),h=n(86623),p=n(1140),f=n(78274),m=n(52778),g=n(42842),v=n(68335),D=n(83588);class y extends r.PureComponent{constructor(e){super(e),this._input=null,this._inputContainer=null,this._handleFocus=e=>{var t,n;this.props.showOnFocus&&this.props.onShowPicker(),null===(n=(t=this.props).onFocus)||void 0===n||n.call(t,e)},this._handleInputRef=e=>{this._input=e,this.props.inputReference&&this.props.inputReference(this._input)},this._handleContainerRef=e=>{this._inputContainer=e},this._onShowPicker=e=>{if(e&&this._inputContainer){const t=e.getBoundingClientRect(),n=this._inputContainer.getBoundingClientRect();t.width&&t.width>window.innerWidth-n.left?(e.style.right="0",e.style.left="auto"):(e.style.right="auto",e.style.left=`${n.left}px`);const s=window.innerHeight-n.bottom,r=n.top;if(s>=t.height)return void(e.style.top=`${n.bottom}px`);e.style.top="auto",e.style.bottom=r<t.height?"0":`${s+n.height}px`}},this._onChange=()=>{const e=(0,c.ensureNotNull)(this._input).value;this.setState({value:e}),this.props.onType(e)},this._onKeyDown=e=>{this.props.hideOnKeyDown&&this.props.onHidePicker(),this.props.onKeyDown&&this.props.onKeyDown(e)},this._onKeyPress=e=>{if(e.charCode){const t=String.fromCharCode(e.charCode);this.props.inputRegex.test(t)||e.preventDefault()}},this._onKeyUp=e=>{if(8!==e.keyCode){const e=(0,c.ensureNotNull)(this._input).value,t=this.props.fixValue(e);t!==e&&this.setState({value:t})}},this._handleDropdownKeydown=e=>{var t;27===(0,v.hashFromEvent)(e)&&(null===(t=this._input)||void 0===t||t.focus(),this.props.onHidePicker())},this.state={value:e.value,valueFromProps:e.value}}render(){const{position:e="fixed",className:t,size:n,disabled:s,readonly:o,errors:i,icon:l,InputComponent:u=h.FormInput}=this.props;return r.createElement("div",{
className:D.pickerInput,ref:this._handleContainerRef},r.createElement(u,{value:this.state.value,onBlur:this.props.onBlur,onKeyDown:this._onKeyDown,onKeyPress:this._onKeyPress,onKeyUp:this._onKeyUp,onChange:this._onChange,onFocus:this._handleFocus,onClick:this.props.onShowPicker,reference:this._handleInputRef,className:t,size:n,disabled:s,errors:i,messagesPosition:p.MessagesPosition.Attached,hasErrors:this.props.showErrorMessages&&i&&i.length>0,name:this.props.name,readonly:o,endSlot:i&&i.length?void 0:r.createElement(f.EndSlot,null,r.createElement(d.Icon,{icon:l,className:a(D.icon,s&&D.disabled),onClick:s||o?void 0:this.props.onShowPicker})),"data-name":this.props.name}),this.props.showPicker&&!o?r.createElement(g.Portal,{top:"0",left:"0",right:"0",bottom:"0",pointerEvents:"none"},r.createElement(m.OutsideEvent,{mouseDown:!0,handler:this.props.onHidePicker},(t=>r.createElement("span",{ref:t,style:{pointerEvents:"auto"}},r.createElement("div",{className:a(D.picker,D[e]),key:"0",ref:this._onShowPicker,onKeyDown:this._handleDropdownKeydown},this.props.children))))):null)}static getDerivedStateFromProps(e,t){return e.value!==t.valueFromProps?{value:e.value,valueFromProps:e.value}:null}}y.defaultProps={showOnFocus:!0};class w extends r.PureComponent{constructor(e){super(e),this._input=null,this._nativeInputRef=r.createRef(),this._handleInputRef=e=>{this._input=e,this.props.inputReference&&this.props.inputReference(this._input)},this._onFocus=()=>{this.setState({isFocused:!0})},this._onBlur=()=>{this._nativeInputRef.current&&(this._nativeInputRef.current.defaultValue=this.props.value),this.setState({isFocused:!1})},this._onChange=e=>{const{value:t}=e.target;t&&(this.setState({value:t}),this.props.onChange(t))},this.state={value:e.value,isFocused:!1}}componentDidMount(){this._nativeInputRef.current&&(this._nativeInputRef.current.defaultValue=this.props.value)}render(){const{className:e,containerClassName:t,disabled:n,errors:s,InputComponent:o=h.FormInput}=this.props,i=!this.props.readonly&&!n,l=this.props.showErrorMessages&&s&&s.length>0;return r.createElement("div",{className:a(D.pickerInput,t)},r.createElement(o,{value:this.props.value,readonly:!0,noReadonlyStyles:!0,endSlot:s&&s.length?void 0:r.createElement(f.EndSlot,null,r.createElement(d.Icon,{icon:this.props.icon,className:a(D.icon,n&&D.disabled)})),className:e,inputClassName:D.textInput,size:this.props.size,disabled:n,hasErrors:l,errors:s,alwaysShowAttachedErrors:!0,messagesPosition:p.MessagesPosition.Attached,name:i?void 0:this.props.name,reference:this._handleInputRef,highlight:this.state.isFocused,intent:!l&&this.state.isFocused?"primary":void 0}),i&&r.createElement("input",{ref:this._nativeInputRef,type:this.props.type,className:D.nativePicker,onChange:this._onChange,onInput:this._onChange,min:this.props.min,max:this.props.max,name:this.props.name,onFocus:this._onFocus,onBlur:this._onBlur}))}}var b=n(23935),F=n(67029),_=n(53017),C=n(1401);class E extends r.PureComponent{constructor(e){super(e),this._pickerInputContainerRef=r.createRef(),
this._pickerInpuRef=r.createRef(),this._dateFormatter=new l.DateFormatter,this._onPickerInputKeyDown=e=>{const t=(0,v.hashFromEvent)(e);if([v.Modifiers.Shift+9,9].includes(t))this._hideCalendar();else{if(40===t||38===t)return e.preventDefault(),this._showCalendar(),void this.setState({autofocusCalendar:!0});13===t&&this.props.onEnter&&this.props.onEnter(e),this._hideCalendar()}},this._returnFocusToInput=e=>{var t;this.setState({autofocusCalendar:!1}),null===(t=this._pickerInpuRef.current)||void 0===t||t.focus(),e&&this._hideCalendar()},this._fixValue=e=>(e=(e=e.substring(0,10)).replace(/-+/g,"-"),(/^\d{4}$/.test(e)||/^\d{4}-\d{2}$/.test(e))&&(e+="-"),e),this._isValid=e=>{const t=this._dateFormatter.parse(e);if(t.res&&/^[0-9]{4}(-[0-9]{2}){2}/.test(t.value)){const e=new Date(t.value);return!(0,b.isInvalidDateObj)(e)&&(!!(this.props.noRangeValidation||i.mobiletouch&&o.isIOS)||(0,b.isInRange)(e,this.props.minDate,this.props.maxDate))}return!1},this._onBlur=e=>{var t;if(!this.props.revertInvalidData||(null===(t=this._pickerInputContainerRef.current)||void 0===t?void 0:t.contains(e.relatedTarget)))return;const{value:n}=e.target;if(!this._isValid(n)){const t=new Date(this.state.date);this.setState({pickerInputKey:e.timeStamp,date:t,isInvalid:!1}),this.props.onPick(t)}},this._onType=e=>{const t=this._isValid(e)?new Date(e.concat("T00:00")):null;t?this.setState({date:t,isInvalid:!1}):this.setState({isInvalid:!0}),this.props.onPick(t)},this._onSelect=e=>{this.setState({date:e,showCalendar:!1,isInvalid:!1}),this._returnFocusToInput(!0),this.props.onPick(e)},this._showCalendar=()=>{this.setState({showCalendar:!0})},this._hideCalendar=()=>{this.setState({showCalendar:!1,autofocusCalendar:!1})},this._getErrors=()=>{const e=this.props.errors?[...this.props.errors]:[];return this.state.isInvalid&&e.push(s.t(null,void 0,n(46420))),e},this.state={pickerInputKey:0,date:e.initial,showCalendar:!1,isInvalid:!this._isValid(this._dateFormatter.formatLocal(e.initial)),autofocusCalendar:!1,initial:e.initial}}render(){const e=this.props.endSlotComponent,t=(0,_.mergeRefs)([this._pickerInpuRef,this.props.inputReference]);return i.mobiletouch?r.createElement(w,{value:this._dateFormatter.formatLocal(this.state.date),type:"date",onChange:this._onType,icon:C,disabled:this.props.disabled,size:this.props.size,min:this.props.minDate&&this._dateFormatter.formatLocal(this.props.minDate),max:this.props.maxDate&&this._dateFormatter.formatLocal(this.props.maxDate),errors:this._getErrors(),showErrorMessages:this.props.showErrorMessages,name:this.props.name,readonly:this.props.readonly,className:a(this._getFontSizeClassName(this.props.size),this.props.className),containerClassName:this.props.containerClassName,inputReference:this.props.inputReference,InputComponent:this.props.InputComponent}):r.createElement("div",{className:this.props.containerClassName,ref:this._pickerInputContainerRef},r.createElement(y,{key:this.state.pickerInputKey,value:this._dateFormatter.formatLocal(this.state.date),inputRegex:/[0-9.]/,fixValue:this._fixValue,onType:this._onType,
onBlur:this._onBlur,onShowPicker:this._showCalendar,onHidePicker:this._hideCalendar,showPicker:this.state.showCalendar&&this.props.withCalendar,showOnFocus:this.props.showOnFocus,icon:C,disabled:this.props.disabled,size:this.props.size,errors:this._getErrors(),showErrorMessages:this.props.showErrorMessages,name:this.props.name,readonly:this.props.readonly,position:this.props.position,className:a(this._getFontSizeClassName(this.props.size),this.props.className),inputReference:t,InputComponent:this.props.InputComponent,onKeyDown:this._onPickerInputKeyDown,onFocus:this.props.onFocus},r.createElement(u.Calendar,{selectedDate:this.state.date,maxDate:this.props.maxDate,minDate:this.props.minDate,onSelect:this._onSelect,endSlot:e&&r.createElement(e,{onSelectDate:this._onSelect}),autoFocus:this.state.autofocusCalendar,showFocusRing:this.state.autofocusCalendar,focusTriggerElement:this._returnFocusToInput,withFocusTrap:!0})))}static getDerivedStateFromProps(e,t){return t.initial!==e.initial?{...t,date:e.initial,initial:e.initial}:null}_getFontSizeClassName(e){return e?"large"===e?F.InputClasses.FontSizeLarge:F.InputClasses.FontSizeMedium:void 0}}E.defaultProps={position:"fixed",withCalendar:!0}},76056:(e,t,n)=>{"use strict";n.d(t,{DateInput:()=>f});var s=n(50959),r=n(11542),a=n(9745),o=n(78274),i=n(86623),l=n(97754),u=n.n(l),c=n(57296);function d(e){const{className:t,text:n}=e;return s.createElement("span",{className:u()(c.tooltip,t)},n)}var h=n(98475);const p=n(30029);function f(e){const{hasErrors:t,onClick:l,errors:u,className:c,theme:f=p,...m}=e;return s.createElement("div",{className:f.container,onClick:l},s.createElement(i.FormInput,{...m,className:f.date,hasErrors:t,errors:[],endSlot:!t&&s.createElement(o.EndSlot,{icon:!0,interactive:!1},s.createElement(a.Icon,{icon:h,className:f.icon}))}),t&&s.createElement(d,{text:r.t(null,void 0,n(27254)),className:f.tooltip}))}},23935:(e,t,n)=>{"use strict";function s(e){return("0"+e).slice(-2)}function r(e){const t=new Date(e);return t.setMilliseconds(0),t.setSeconds(0),t.setMinutes(0),t.setHours(0),t}function a(e){const t=new Date(e);return t.setMilliseconds(999),t.setSeconds(59),t.setMinutes(59),t.setHours(23),t}function o(e,t=!1){const n=r(e),s=t?function(e){if(e>6)throw new Error("Invalid day is provided");return 0===e?6:e-1}(n.getDay()):n.getDay();return n.setDate(n.getDate()-s),n}function i(e){const t=r(e);return t.setDate(1),t}function l(e){const t=i(e);return t.setMonth(0),t}function u(e,t){return!!t&&Number(r(e))===Number(r(t))}function c(e,t){return!!t&&Number(i(e))===Number(i(t))}function d(e,t){return!!t&&Number(l(e))===Number(l(t))}function h(e){const t=new Date(e.getFullYear(),0,1),n=(Number(e)-Number(t))/864e5;return Math.ceil((n+t.getDay()+1)/7)}function p(e){const t=new Date(e);return t.setDate(t.getDate()+7),t}var f;n.d(t,{addLocalTime:()=>w,getCloneDateWithOffset:()=>S,getDateInTabOrder:()=>M,getDecadesStart:()=>b,getFirstEnabledDay:()=>C,getFirstEnabledMonth:()=>E,getFirstEnabledYear:()=>k,getWeeks:()=>F,isDayDisabled:()=>_,isInRange:()=>g,
isInvalidDateObj:()=>v,isSameDay:()=>u,isSameMonth:()=>c,isSameYear:()=>d,resetToDayEnd:()=>a,resetToDayStart:()=>r,resetToMonthStart:()=>i,subtractLocalTime:()=>y,twoDigitsFormat:()=>s}),function(e){e.Day="day",e.Month="month",e.Year="year"}(f||(f={}));const m={day:r,month:i,year:l};function g(e,t,n,s="day"){const r=m[s],a=!t||Number(r(t))-Number(r(e))<=0;return(!n||Number(r(n))-Number(r(e))>=0)&&a}function v(e){return Number.isNaN(Number(e))}function D(e){return new Date(e).getTimezoneOffset()/60}function y(e){const t=new Date(e);return t.setHours(t.getHours()+D(t)),t}function w(e){const t=new Date(e);return t.setHours(t.getHours()-D(t)),t}function b(e){const t=e.getFullYear()%10*-1,n=new Date(e);return n.setFullYear(e.getFullYear()+t),n}function F(e,t){const n=[];let s=o(i(e),!0);for(let r=0;r<6;r++){const r=[];for(let n=0;n<7;n++){const a=new Date(s);a.setDate(a.getDate()+n),(c(a,e)||t)&&r.push(a)}r.length&&n.push({week:h(s),days:r}),s=new Date(p(s))}return n}function _({day:e,minDate:t,maxDate:n,disableWeekends:s=!1}){if(!g(e,t,n))return!0;const r=[6,0].includes(e.getDay());return!!s&&r}function C(e,t){return function n(s=0,r=0){if(!e[s]||!e[s].days[r])return;const a=e[s].days,o=a[r];return t(o)?r+1<a.length?n(s,r+1):n(s+1,0):o}()}function E(e,t){return function n(s=0){if(!e[s])return;const r=e[s].date;return t(r)?n(s+1):r}()}function k(e,t){return function n(s=0){if(!e[s])return;const r=e[s];return t(r)?n(s+1):r}()}function S({dateFrom:e,offset:t,level:n="day",maxIterations:s=6,isDisabledDate:r=(()=>!1)}){return function e(a,o=0){const i=new Date(a);switch(n){case"day":i.setDate(i.getDate()+t);break;case"month":i.setMonth(i.getMonth()+t);break;case"year":i.setFullYear(i.getFullYear()+t)}const l=r(i);return o>s||!l?l?null:i:e(i,o+1)}(e)}function M({selectedDate:e,dateToFocus:t,currentlyFocused:n,firstEnabledDate:s,getFirstDate:r,getLastDate:a,isDisabledDate:o}){const i=g(e,r(),a(),"day")&&!o(e)?e:null,l=t&&g(t,r(),a(),"day")&&!o(t)?t:null;return(n&&g(n,r(),a(),"day")&&!o(n)?n:null)||i||l||s}},70412:(e,t,n)=>{"use strict";n.d(t,{hoverMouseEventFilter:()=>a,useAccurateHover:()=>o,useHover:()=>r});var s=n(50959);function r(){const[e,t]=(0,s.useState)(!1);return[e,{onMouseOver:function(e){a(e)&&t(!0)},onMouseOut:function(e){a(e)&&t(!1)}}]}function a(e){return!e.currentTarget.contains(e.relatedTarget)}function o(e){const[t,n]=(0,s.useState)(!1);return(0,s.useEffect)((()=>{const t=t=>{if(null===e.current)return;const s=e.current.contains(t.target);n(s)};return document.addEventListener("mouseover",t),()=>document.removeEventListener("mouseover",t)}),[]),t}},36565:(e,t,n)=>{"use strict";n.d(t,{TimeInput:()=>Y});var s=n(49483),r=n(50959),a=n(97754),o=n.n(a),i=n(50151),l=n(47201),u=n(78274),c=n(31261),d=n(9745),h=n(86432),p=n(95096);function f(e){return r.createElement(d.Icon,{className:h.icon,icon:p})}var m=n(29202),g=n(54153);var v=n(36383),D=n(9859);const y={0:{pattern:/\d/},9:{pattern:/\d/,optional:!0},"#":{pattern:/\d/,recursive:!0},A:{pattern:/[a-zA-Z0-9]/},S:{pattern:/[a-zA-Z]/}};function w(e,t,n){const s=[],r=n
;let a=0,o=0;const i=e.length,l=r.length;let u=-1,c=0;const d=[],h=i-1,p=[];let f;for(;a<i&&o<l;){const n=e.charAt(a),i=r.charAt(o),l=y[n];l?(i.match(l.pattern)?(s.push(i),l.recursive&&(-1===u?u=a:a===h&&a!==u&&(a=u-1),h===u&&(a-=1)),a+=1):i===f?(c--,f=void 0):l.optional?(a+=1,o-=1):l.fallback?(s.push(l.fallback),a+=1,o-=1):p.push({p:o,v:i,e:l.pattern}),o+=1):(t||s.push(n),i===n?(d.push(o),o+=1):(f=n,d.push(o+c),c++),a+=1)}const m=e.charAt(h);i!==l+1||y[m]||s.push(m);const g=s.join(""),v=function(e,t){const n=0,s={};for(let e=0;e<t.length;e++)s[t[e]+n]=1;return s}(0,d);return[g,v,p]}function b(e,t,n){const s=function(e){let t=!0;for(let n=0;n<e.length;n++){const s=y[e.charAt(n)];if(s&&s.recursive){t=!1;break}}return t?e.length:void 0}(e),[a,o]=w(e,!1,t),[l,u]=(0,r.useState)(a),[c,d]=(0,r.useState)(0),[h,p]=(0,r.useState)(!1),f=(0,r.useRef)(o),m=(0,r.useRef)(l);return(0,r.useEffect)((()=>{const[n,s]=w(e,!1,t);u(n),g(s)}),[t,e]),(0,r.useLayoutEffect)((()=>{const e=(0,i.ensureNotNull)(n.current);h&&(e.setSelectionRange(c,c),p(!1)),d(F(e))}),[h]),[t,m,{onChange:function(){const t=(0,i.ensureNotNull)(n.current),s=t.value,[r,a]=w(e,!1,s);u(r),m.current=r;const o=g(a),h=function(e,t,n,s,r,a){if(e!==t){const o=t.length,i=e.length;let l=0,u=0,c=0,d=0,h=0;for(h=s;h<o&&r[h];h++)u++;for(h=s-1;h>=0&&r[h];h--)l++;for(h=s-1;h>=0;h--)r[h]&&c++;for(h=n-1;h>=0;h--)a[h]&&d++;if(s>i)s=10*o;else if(n>=s&&n!==i){if(a[s]){const e=s;s-=d-c,r[s-=l]&&(s=e)}}else s>n&&(s+=c-d,s+=u)}return s}(l,r,c,F(t),a,o);d(h),p(!0)},onSelect:function(){const e=(0,i.ensureNotNull)(n.current);d(F(e))},maxLength:s}];function g(e){const t=f.current;return f.current=e,t}}function F(e){return e.selectionStart||0}function _(e){const{value:t,mask:n,onChange:s,...a}=e,o=(0,r.useRef)(null),[i,l,u]=b(n,t,o);return(0,r.useLayoutEffect)((()=>{void 0!==e.reference&&(e.reference.current=o.current)}),[e.reference]),r.createElement(c.InputControl,{...a,maxLength:u.maxLength,value:i,autoComplete:"off",reference:function(e){o.current=e},onChange:function(){u.onChange(),s(l.current)},onSelect:u.onSelect})}var C=n(68335),E=n(20520),k=n(16396),S=n(29981);const M=(()=>{const e=[];for(let t=0;t<24;++t)for(let n=0;n<60;n+=15){const[s,r]=[T(t.toString()),T(n.toString())],a=`${s}:${r}`,o=I(a)?a:R(a);e.push(o)}return e})();function N(e){let t=!1;const n=(0,r.useRef)(null),s=(0,r.useRef)(null),a=(0,r.useRef)(null),l=(0,r.useRef)(null),[c,d]=(0,m.useFocus)(),[h,p]=(0,r.useState)(e.value),g=x(h),y=I(g)?g:R(g),[w,b]=(0,r.useState)(y),F=c||L().some((e=>null!==e&&e.contains(document.activeElement)));(0,r.useLayoutEffect)((()=>p(e.value)),[e.value]),(0,r.useLayoutEffect)((()=>b(y)),[h,F]),(0,r.useEffect)((()=>K(w===y?"auto":"smooth")),[w]);const N=(0,S.lowerbound)(M,y,((e,t)=>e<t));let T=M;M[N]!==y&&(T=[...M],T.splice(N,0,y));const P=(0,v.useOutsideEvent)({mouseDown:!0,touchStart:!0,handler:function(e){null!==s.current&&F&&e.target instanceof Node&&null!==a.current&&!a.current.contains(e.target)&&s.current.blur()}});return r.createElement("div",{className:o()(e.className),
onKeyDown:function(e){if(e.defaultPrevented)return;const t=(0,C.hashFromEvent)(e.nativeEvent);if(38===t){e.preventDefault();const t=(T.indexOf(w)+T.length-1)%T.length;b(T[t])}if(40===t){e.preventDefault();const t=(T.indexOf(w)+T.length+1)%T.length;b(T[t])}},onFocus:function(e){$(e)||d.onFocus(e)},onBlur:function(e){$(e)||d.onBlur(e)},ref:P},r.createElement(_,{disabled:e.disabled,name:e.name,endSlot:r.createElement(u.EndSlot,{icon:!0},r.createElement(f,null)),reference:s,containerReference:n,mask:"09:00",value:h,onFocus:function(t){var n;setTimeout(V,0),null===(n=e.onFocus)||void 0===n||n.call(e,t)},onBlur:function(e){$(e)||Y(h)},onChange:function(t){p(t),e.onInput&&e.onInput(t)},onKeyDown:function(e){if(e.defaultPrevented)return;const t=(0,C.hashFromEvent)(e.nativeEvent);13===t&&(e.preventDefault(),Y(w),(0,i.ensureNotNull)(s.current).blur());27===t&&(e.preventDefault(),(0,i.ensureNotNull)(s.current).blur())}}),r.createElement(E.PopupMenu,{onOpen:function(){K()},onClose:function(){},position:function(){const e=(0,i.ensureNotNull)(n.current).getBoundingClientRect(),t=window.innerHeight-e.bottom,s=e.top;let r=231,a=e.bottom;if(r>s&&r>t){const n=(0,D.clamp)(r,0,s),o=(0,D.clamp)(r,0,t);r=Math.max(n,o),a=n>o?e.top-n:e.bottom}else r>t&&(a=e.top-r);return{x:e.left,y:a,overrideWidth:e.width,overrideHeight:r}},closeOnClickOutside:!1,isOpened:F,tabIndex:-1,reference:a},T.map((e=>r.createElement(k.PopupMenuItem,{key:e,label:e,isActive:e===y,isHovered:e===w,reference:e===w?H:void 0,onClick:z,onClickArg:e})))));function Y(n){const s=x(n),r=I(s)?s:R(s);p(r),t||(t=!0,e.onChange(r))}function H(e){l.current=e}function z(e){Y((0,i.ensureDefined)(e)),(0,i.ensureNotNull)(a.current).blur()}function $(e){return c&&(null!==O(document.activeElement)||null!==O(e.relatedTarget))}function O(e){return e instanceof Node&&L().find((t=>null!==t&&t.contains(e)))||null}function L(){return[a.current,s.current]}function K(e="auto"){if(null!==l.current){const t=(0,i.ensureNotNull)(a.current).getBoundingClientRect(),n=l.current.getBoundingClientRect();(t.top>n.top||t.bottom<n.bottom)&&l.current.scrollIntoView({behavior:e})}}function V(){const e=s.current;if(null!==e){const t=e.value||"";e.setSelectionRange(0,t.length)}}}function x(e){const[t="",n=""]=e.split(":"),[s,r]=[T(t),P(n)];return`${s}:${r}`}function I(e){return/^(0?[0-9]|1[0-9]|2[0-3]):[0-5][0-9]$/g.test(e)}function R(e){const[t,n]=e.split(":"),[s,r]=[(0,D.clamp)(parseInt(t),0,23),(0,D.clamp)(parseInt(n),0,59)],[a,o]=[T(s.toString()),P(r.toString())];return`${a}:${o}`}function T(e){return e.slice(0,2).padStart(2,"0")}function P(e){return e.slice(0,2).padEnd(2,"0")}const Y=s.CheckMobile.any()?function(e){const{onChange:t,onFocus:n,value:s,className:a,...d}=e,h=(0,r.useRef)(null),[p,v]=(0,m.useFocus)(),D=(0,l.createSafeMulticastEventHandler)(v.onBlur,(function(){h.current&&s&&(h.current.defaultValue=s)}));return(0,r.useLayoutEffect)((()=>{h.current&&s&&(h.current.defaultValue=s)}),[]),(0,r.useLayoutEffect)((()=>{h.current&&s&&(h.current.value=s)}),[s]),r.createElement("div",{className:o()(g.wrap,a)
},r.createElement(c.InputControl,{...d,type:"text",endSlot:r.createElement(u.EndSlot,{icon:!0},r.createElement(f,null)),value:s,highlight:p,intent:p?"primary":void 0,onFocus:function(e){(0,i.ensureNotNull)(h.current).focus(),n&&n(e)},onChange:function(){}}),r.createElement("input",{...v,disabled:e.disabled,className:g.input,type:"time",onBlur:D,onChange:function(e){const{value:n}=e.currentTarget;t&&n&&t(n)},ref:h}))}:N},17140:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="m16.47 7.47 1.06 1.06L12.06 14l5.47 5.47-1.06 1.06L9.94 14l6.53-6.53Z"/></svg>'},98475:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M10 4h1v2h6V4h1v2h2.5A2.5 2.5 0 0 1 23 8.5v11a2.5 2.5 0 0 1-2.5 2.5h-13A2.5 2.5 0 0 1 5 19.5v-11A2.5 2.5 0 0 1 7.5 6H10V4zm8 3H7.5C6.67 7 6 7.67 6 8.5v11c0 .83.67 1.5 1.5 1.5h13c.83 0 1.5-.67 1.5-1.5v-11c0-.83-.67-1.5-1.5-1.5H18zm-3 2h-2v2h2V9zm-7 4h2v2H8v-2zm12-4h-2v2h2V9zm-7 4h2v2h-2v-2zm-3 4H8v2h2v-2zm3 0h2v2h-2v-2zm7-4h-2v2h2v-2z"/></svg>'},1401:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" width="16" height="16"><path d="M4 0c-.6 0-1 .4-1 1v1H1c-.6 0-1 .4-1 1v12c0 .6.4 1 1 1h14c.6 0 1-.4 1-1V3c0-.6-.4-1-1-1h-2V1c0-.6-.4-1-1-1h-1c-.6 0-1 .4-1 1v1H6V1c0-.6-.4-1-1-1H4zM2 5h12v9H2V5zm5 2v2h2V7H7zm3 0v2h2V7h-2zm-6 3v2h2v-2H4zm3 0v2h2v-2H7zm3 0v2h2v-2h-2z"/></svg>'},95096:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 17 17" width="17" height="17"><path fill="currentColor" d="M1 8.5a7.5 7.5 0 1 1 15 0 7.5 7.5 0 0 1-15 0zM8.5 0a8.5 8.5 0 1 0 0 17 8.5 8.5 0 0 0 0-17zM9 9V3H8v5H5v1h4z"/></svg>'}}]);