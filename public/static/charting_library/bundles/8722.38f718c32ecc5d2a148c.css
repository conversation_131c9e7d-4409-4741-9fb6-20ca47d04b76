.autocomplete-uszkUMOz {
  flex: 1 1 auto;
  position: relative;
}
.autocomplete-uszkUMOz .caret-uszkUMOz {
  border-radius: 4px;
  display: flex;
  height: calc(100% - 2px);
  justify-content: center;
  margin: 1px 0;
  width: 21px;
}
@media (any-hover: hover) {
  .autocomplete-uszkUMOz .caret-uszkUMOz:hover {
    background-color: var(--themed-color-caret-hover, #f0f3fa);
  }
  html.theme-dark .autocomplete-uszkUMOz .caret-uszkUMOz:hover {
    background-color: var(--themed-color-caret-hover, #363a45);
  }
  .autocomplete-uszkUMOz .caret-uszkUMOz:hover .icon-uszkUMOz {
    color: var(--themed-color-icon-hover, #131722);
  }
  html.theme-dark .autocomplete-uszkUMOz .caret-uszkUMOz:hover .icon-uszkUMOz {
    color: var(--themed-color-icon-hover, #d1d4dc);
  }
}
.autocomplete-uszkUMOz .caret-uszkUMOz .icon-uszkUMOz {
  color: var(--themed-color-default-gray, #6a6d78);
}
html.theme-dark .autocomplete-uszkUMOz .caret-uszkUMOz .icon-uszkUMOz {
  color: var(--themed-color-default-gray, #868993);
}
.suggestions-uszkUMOz {
  background-color: var(--themed-color-input-bg, #fff);
  border-radius: 3px;
  box-shadow: 0 1px 2px 1px rgba(0, 0, 0, 0.275);
  font-size: 13px;
  max-height: 0;
  outline: 0;
  overflow-x: hidden;
  overflow-y: auto;
  padding: 10px 0;
  position: absolute;
  transform: translateZ(0);
  visibility: hidden;
  width: 100%;
  z-index: 150;
}
html.theme-dark .suggestions-uszkUMOz {
  background-color: var(--themed-color-input-bg, #1e222d);
}
.suggestions-uszkUMOz::-webkit-scrollbar {
  height: 5px;
  width: 5px;
}
.suggestions-uszkUMOz::-webkit-scrollbar-thumb {
  background-clip: content-box;
  background-color: var(
    --tv-color-scrollbar-thumb-background,
    var(--themed-color-scroll-bg, #9598a1)
  );
  border: 1px solid #0000;
  border-radius: 3px;
}
html.theme-dark .suggestions-uszkUMOz::-webkit-scrollbar-thumb {
  background-color: var(
    --tv-color-scrollbar-thumb-background,
    var(--themed-color-scroll-bg, #363a45)
  );
}
.suggestions-uszkUMOz::-webkit-scrollbar-track {
  background-color: initial;
  border-radius: 3px;
}
.suggestions-uszkUMOz::-webkit-scrollbar-corner {
  display: none;
}
.suggestions-uszkUMOz .noResults-uszkUMOz,
.suggestions-uszkUMOz .suggestion-uszkUMOz {
  overflow: hidden;
  padding: 10px 15px;
  position: relative;
  text-align: left;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.suggestions-uszkUMOz .suggestion-uszkUMOz {
  cursor: pointer;
}
.suggestions-uszkUMOz .suggestion-uszkUMOz.selected-uszkUMOz {
  background-color: var(--themed-color-item-selected-blue, #e3effd);
  color: var(--themed-color-suggestion-text, #4a4a4a);
}
html.theme-dark .suggestions-uszkUMOz .suggestion-uszkUMOz.selected-uszkUMOz {
  background-color: var(--themed-color-item-selected-blue, #142e61);
  color: var(--themed-color-suggestion-text, #868993);
}
@media (any-hover: hover) {
  .suggestions-uszkUMOz .suggestion-uszkUMOz:hover {
    background-color: var(--themed-color-button-hover-bg, #e0e3eb);
    color: var(--themed-color-suggestion-text, #4a4a4a);
  }
  html.theme-dark .suggestions-uszkUMOz .suggestion-uszkUMOz:hover {
    background-color: var(--themed-color-button-hover-bg, #1e222d);
    color: var(--themed-color-suggestion-text, #868993);
  }
}
.suggestions-uszkUMOz .suggestion-uszkUMOz:active {
  background-color: var(--themed-color-disabled-border-and-color, #e0e3eb);
  color: var(--themed-color-suggestion-text, #4a4a4a);
}
html.theme-dark .suggestions-uszkUMOz .suggestion-uszkUMOz:active {
  background-color: var(--themed-color-disabled-border-and-color, #2a2e39);
  color: var(--themed-color-suggestion-text, #868993);
}
.suggestions-uszkUMOz .noResults-uszkUMOz {
  color: var(--themed-color-empty-container-message, #6a6d78);
}
html.theme-dark .suggestions-uszkUMOz .noResults-uszkUMOz {
  color: var(--themed-color-empty-container-message, #868993);
}
.opened-uszkUMOz.suggestions-uszkUMOz {
  max-height: 45vh;
  visibility: visible;
}
.opened-uszkUMOz.suggestions-uszkUMOz::-webkit-scrollbar {
  height: 5px;
  width: 5px;
}
.opened-uszkUMOz.suggestions-uszkUMOz::-webkit-scrollbar-thumb {
  background-clip: content-box;
  background-color: var(
    --tv-color-scrollbar-thumb-background,
    var(--themed-color-scroll-bg, #9598a1)
  );
  border: 1px solid #0000;
  border-radius: 3px;
}
html.theme-dark .opened-uszkUMOz.suggestions-uszkUMOz::-webkit-scrollbar-thumb {
  background-color: var(
    --tv-color-scrollbar-thumb-background,
    var(--themed-color-scroll-bg, #363a45)
  );
}
.opened-uszkUMOz.suggestions-uszkUMOz::-webkit-scrollbar-track {
  background-color: initial;
  border-radius: 3px;
}
.opened-uszkUMOz.suggestions-uszkUMOz::-webkit-scrollbar-corner {
  display: none;
}
