(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[6456,9790],{36136:e=>{e.exports={"nav-button":"nav-button-znwuaSC1",link:"link-znwuaSC1",background:"background-znwuaSC1",icon:"icon-znwuaSC1","flip-icon":"flip-icon-znwuaSC1","size-large":"size-large-znwuaSC1","preserve-paddings":"preserve-paddings-znwuaSC1","size-medium":"size-medium-znwuaSC1","size-small":"size-small-znwuaSC1","size-xsmall":"size-xsmall-znwuaSC1","size-xxsmall":"size-xxsmall-znwuaSC1","visually-hidden":"visually-hidden-znwuaSC1"}},68976:e=>{e.exports={button:"button-D4RPB3ZC",content:"content-D4RPB3ZC",iconOnly:"iconOnly-D4RPB3ZC",link:"link-D4RPB3ZC",brand:"brand-D4RPB3ZC",primary:"primary-D4RPB3ZC",secondary:"secondary-D4RPB3ZC",gray:"gray-D4RPB3ZC",green:"green-D4RPB3ZC",red:"red-D4RPB3ZC",black:"black-D4RPB3ZC","black-friday":"black-friday-D4RPB3ZC","cyber-monday":"cyber-monday-D4RPB3ZC",xsmall:"xsmall-D4RPB3ZC",startIconWrap:"startIconWrap-D4RPB3ZC",endIconWrap:"endIconWrap-D4RPB3ZC",withStartIcon:"withStartIcon-D4RPB3ZC",withEndIcon:"withEndIcon-D4RPB3ZC",small:"small-D4RPB3ZC",medium:"medium-D4RPB3ZC",large:"large-D4RPB3ZC",xlarge:"xlarge-D4RPB3ZC",animated:"animated-D4RPB3ZC",stretch:"stretch-D4RPB3ZC",grouped:"grouped-D4RPB3ZC",adjustPosition:"adjustPosition-D4RPB3ZC",firstRow:"firstRow-D4RPB3ZC",firstCol:"firstCol-D4RPB3ZC","no-corner-top-left":"no-corner-top-left-D4RPB3ZC","no-corner-top-right":"no-corner-top-right-D4RPB3ZC","no-corner-bottom-right":"no-corner-bottom-right-D4RPB3ZC","no-corner-bottom-left":"no-corner-bottom-left-D4RPB3ZC",textWrap:"textWrap-D4RPB3ZC",multilineContent:"multilineContent-D4RPB3ZC",secondaryText:"secondaryText-D4RPB3ZC",primaryText:"primaryText-D4RPB3ZC"}},53330:e=>{e.exports={"tablet-normal-breakpoint":"screen and (max-width: 768px)","small-height-breakpoint":"screen and (max-height: 360px)","tablet-small-breakpoint":"screen and (max-width: 430px)"}},8473:e=>{e.exports={dialog:"dialog-b8SxMnzX",wrapper:"wrapper-b8SxMnzX",separator:"separator-b8SxMnzX",bounded:"bounded-b8SxMnzX"}},80822:e=>{e.exports={"small-height-breakpoint":"screen and (max-height: 360px)",container:"container-BZKENkhT",unsetAlign:"unsetAlign-BZKENkhT",title:"title-BZKENkhT",subtitle:"subtitle-BZKENkhT",textWrap:"textWrap-BZKENkhT",ellipsis:"ellipsis-BZKENkhT",close:"close-BZKENkhT",icon:"icon-BZKENkhT"}},59086:e=>{e.exports={container:"container-qm7Rg5MB",mobile:"mobile-qm7Rg5MB",inputContainer:"inputContainer-qm7Rg5MB",withCancel:"withCancel-qm7Rg5MB",input:"input-qm7Rg5MB",icon:"icon-qm7Rg5MB",cancel:"cancel-qm7Rg5MB"}},87886:e=>{e.exports={wrapper:"wrapper-nGEmjtaX",container:"container-nGEmjtaX",tab:"tab-nGEmjtaX",active:"active-nGEmjtaX",title:"title-nGEmjtaX",icon:"icon-nGEmjtaX",withoutIcon:"withoutIcon-nGEmjtaX",titleText:"titleText-nGEmjtaX",nested:"nested-nGEmjtaX",isTablet:"isTablet-nGEmjtaX",isMobile:"isMobile-nGEmjtaX",showLastDivider:"showLastDivider-nGEmjtaX",medium:"medium-nGEmjtaX",large:"large-nGEmjtaX",withoutArrow:"withoutArrow-nGEmjtaX",accessible:"accessible-nGEmjtaX"}},71921:e=>{e.exports={
title:"title-z9fs4j4t",small:"small-z9fs4j4t",normal:"normal-z9fs4j4t",large:"large-z9fs4j4t"}},72910:e=>{e.exports={container:"container-XOHpda28",mobile:"mobile-XOHpda28"}},74136:e=>{e.exports={title:"title-cIIj4HrJ",disabled:"disabled-cIIj4HrJ",icon:"icon-cIIj4HrJ",locked:"locked-cIIj4HrJ",open:"open-cIIj4HrJ",actionIcon:"actionIcon-cIIj4HrJ",selected:"selected-cIIj4HrJ",codeIcon:"codeIcon-cIIj4HrJ",solutionIcon:"solutionIcon-cIIj4HrJ"}},70307:e=>{e.exports={"tablet-small-breakpoint":"screen and (max-width: 430px)",container:"container-WeNdU0sq",mobile:"mobile-WeNdU0sq",selected:"selected-WeNdU0sq",disabled:"disabled-WeNdU0sq",favorite:"favorite-WeNdU0sq",highlighted:"highlighted-WeNdU0sq","highlight-animation":"highlight-animation-WeNdU0sq",badge:"badge-WeNdU0sq",main:"main-WeNdU0sq",paddingLeft:"paddingLeft-WeNdU0sq",author:"author-WeNdU0sq",likes:"likes-WeNdU0sq",actions:"actions-WeNdU0sq",isActive:"isActive-WeNdU0sq",mobileText:"mobileText-WeNdU0sq"}},80046:e=>{e.exports={container:"container-hrZZtP0J"}},77335:e=>{e.exports={container:"container-jiYDR9Eu",centerElement:"centerElement-jiYDR9Eu",contentWrap:"contentWrap-jiYDR9Eu",noticeShowed:"noticeShowed-jiYDR9Eu",icon:"icon-jiYDR9Eu",textWrap:"textWrap-jiYDR9Eu"}},28598:e=>{e.exports={"tablet-small-breakpoint":"screen and (max-width: 430px)",dialog:"dialog-I087YV6b",dialogLibrary:"dialogLibrary-I087YV6b",contentContainer:"contentContainer-I087YV6b",listContainer:"listContainer-I087YV6b",scroll:"scroll-I087YV6b",sidebarContainer:"sidebarContainer-I087YV6b",noContentBlock:"noContentBlock-I087YV6b",tabWithHint:"tabWithHint-I087YV6b",solution:"solution-I087YV6b",mobileSidebarItem:"mobileSidebarItem-I087YV6b"}},64856:e=>{e.exports={container:"container-QcG0kDOU",image:"image-QcG0kDOU",title:"title-QcG0kDOU",description:"description-QcG0kDOU",button:"button-QcG0kDOU"}},98992:e=>{e.exports={favorite:"favorite-_FRQhM5Y",hovered:"hovered-_FRQhM5Y",disabled:"disabled-_FRQhM5Y",active:"active-_FRQhM5Y",checked:"checked-_FRQhM5Y"}},32248:e=>{e.exports={highlighted:"highlighted-cwp8YRo6"}},47625:e=>{e.exports={separator:"separator-Pf4rIzEt"}},94720:(e,t,n)=>{"use strict";var i,r,o;function a(e="default"){switch(e){case"default":return"primary";case"stroke":return"secondary"}}function s(e="primary"){switch(e){case"primary":return"brand";case"success":return"green";case"default":return"gray";case"danger":return"red"}}function l(e="m"){switch(e){case"s":return"xsmall";case"m":return"small";case"l":return"large"}}n.d(t,{Button:()=>E}),function(e){e.Primary="primary",e.Success="success",e.Default="default",e.Danger="danger"}(i||(i={})),function(e){e.Small="s",e.Medium="m",e.Large="l"}(r||(r={})),function(e){e.Default="default",e.Stroke="stroke"}(o||(o={}));var c=n(50959),d=n(97754),u=n(95604),h=n(9745),m=n(68976),p=n.n(m);const g="apply-overflow-tooltip apply-overflow-tooltip--check-children-recursively apply-overflow-tooltip--allow-text apply-common-tooltip";function f(e){
const{color:t="brand",size:n="medium",variant:i="primary",stretch:r=!1,icon:o,startIcon:a,endIcon:s,iconOnly:l=!1,className:c,isGrouped:h,cellState:m,disablePositionAdjustment:f=!1,primaryText:v,secondaryText:b,isAnchor:y=!1}=e,w=function(e){let t="";return 0!==e&&(1&e&&(t=d(t,p()["no-corner-top-left"])),2&e&&(t=d(t,p()["no-corner-top-right"])),4&e&&(t=d(t,p()["no-corner-bottom-right"])),8&e&&(t=d(t,p()["no-corner-bottom-left"]))),t}((0,u.getGroupCellRemoveRoundBorders)(m));return d(c,p().button,p()[n],p()[t],p()[i],r&&p().stretch,(o||a)&&p().withStartIcon,s&&p().withEndIcon,l&&p().iconOnly,w,h&&p().grouped,h&&!f&&p().adjustPosition,h&&m.isTop&&p().firstRow,h&&m.isLeft&&p().firstCol,v&&b&&p().multilineContent,y&&p().link,g)}function v(e){const{startIcon:t,icon:n,iconOnly:i,children:r,endIcon:o,primaryText:a,secondaryText:s}=e,l=null!=t?t:n,u=!(t||n||o||i)&&!r&&a&&s;return c.createElement(c.Fragment,null,l&&c.createElement(h.Icon,{icon:l,className:p().startIconWrap}),r&&c.createElement("span",{className:p().content},r),o&&!i&&c.createElement(h.Icon,{icon:o,className:p().endIconWrap}),u&&function(e){return e.primaryText&&e.secondaryText&&c.createElement("div",{className:d(p().textWrap,g)},c.createElement("span",{className:p().primaryText}," ",e.primaryText," "),"string"==typeof e.secondaryText?c.createElement("span",{className:p().secondaryText}," ",e.secondaryText," "):c.createElement("span",{className:p().secondaryText},c.createElement("span",null,e.secondaryText.firstLine),c.createElement("span",null,e.secondaryText.secondLine)))}(e))}var b=n(34094),y=n(86332),w=n(90186);function C(e){const{className:t,color:n,variant:i,size:r,stretch:o,animated:a,icon:s,iconOnly:l,startIcon:c,endIcon:d,primaryText:u,secondaryText:h,...m}=e;return{...m,...(0,w.filterDataProps)(e),...(0,w.filterAriaProps)(e)}}function x(e){const{reference:t,tooltipText:n,...i}=e,{isGrouped:r,cellState:o,disablePositionAdjustment:a}=(0,c.useContext)(y.ControlGroupContext),s=f({...i,isGrouped:r,cellState:o,disablePositionAdjustment:a});return c.createElement("button",{...C(i),className:s,ref:t,"data-overflow-tooltip-text":null!=n?n:e.primaryText?[e.primaryText,e.secondaryText].join(" "):(0,b.getTextForTooltip)(e.children)},c.createElement(v,{...i}))}n(78572);function _(e){const{intent:t,size:n,appearance:i,useFullWidth:r,icon:o,...c}=e;return{...c,color:s(t),size:l(n),variant:a(i),stretch:r,startIcon:o}}function E(e){return c.createElement(x,{..._(e)})}},57340:(e,t,n)=>{"use strict";n.d(t,{CloseButton:()=>u});var i=n(50959),r=n(64388),o=n(17105),a=n(15130),s=n(38822),l=n(63346),c=n(34983);function d(e="large"){switch(e){case"large":return o;case"medium":default:return a;case"small":return s;case"xsmall":return l;case"xxsmall":return c}}const u=i.forwardRef(((e,t)=>i.createElement(r.NavButton,{...e,ref:t,icon:d(e.size)})))},64388:(e,t,n)=>{"use strict";n.d(t,{NavButton:()=>c});var i=n(50959),r=n(97754),o=n(9745),a=(n(78572),n(36136));function s(e){const{size:t="large",preservePaddings:n,isLink:i,flipIconOnRtl:o,className:s}=e
;return r(a["nav-button"],a[`size-${t}`],n&&a["preserve-paddings"],o&&a["flip-icon"],i&&a.link,s)}function l(e){const{children:t,icon:n}=e;return i.createElement(i.Fragment,null,i.createElement("span",{className:a.background}),i.createElement(o.Icon,{icon:n,className:a.icon,"aria-hidden":!0}),t&&i.createElement("span",{className:a["visually-hidden"]},t))}const c=(0,i.forwardRef)(((e,t)=>{const{icon:n,type:r="button",preservePaddings:o,flipIconOnRtl:a,size:c,"aria-label":d,...u}=e;return i.createElement("button",{...u,className:s({...e,children:d}),ref:t,type:r},i.createElement(l,{icon:n},d))}));c.displayName="NavButton";var d=n(21593),u=n(53017);(0,i.forwardRef)(((e,t)=>{const{icon:n,renderComponent:r,"aria-label":o,...a}=e,c=null!=r?r:d.CustomComponentDefaultLink;return i.createElement(c,{...a,className:s({...e,children:o,isLink:!0}),reference:(0,u.isomorphicRef)(t)},i.createElement(l,{icon:n},o))})).displayName="NavAnchorButton"},78572:(e,t,n)=>{"use strict";var i,r,o,a;!function(e){e.Primary="primary",e.QuietPrimary="quiet-primary",e.Secondary="secondary",e.Ghost="ghost"}(i||(i={})),function(e){e.XXSmall="xxsmall",e.XSmall="xsmall",e.Small="small",e.Medium="medium",e.Large="large",e.XLarge="xlarge",e.XXLarge="xxlarge"}(r||(r={})),function(e){e.Brand="brand",e.Gray="gray",e.LightGray="light-gray",e.Green="green",e.Red="red",e.Black="black",e.Gradient="gradient",e.BlackFriday="black-friday",e.CyberMonday="cyber-monday"}(o||(o={})),function(e){e.Semibold18px="semibold18px",e.Semibold16px="semibold16px",e.Semibold14px="semibold14px",e.Medium16px="medium16px",e.Regular16px="regular16px",e.Regular14px="regular14px"}(a||(a={}))},86332:(e,t,n)=>{"use strict";n.d(t,{ControlGroupContext:()=>i});const i=n(50959).createContext({isGrouped:!1,cellState:{isTop:!0,isRight:!0,isBottom:!0,isLeft:!0}})},95604:(e,t,n)=>{"use strict";function i(e){let t=0;return e.isTop&&e.isLeft||(t+=1),e.isTop&&e.isRight||(t+=2),e.isBottom&&e.isLeft||(t+=8),e.isBottom&&e.isRight||(t+=4),t}n.d(t,{getGroupCellRemoveRoundBorders:()=>i})},38952:(e,t,n)=>{"use strict";function i(e){const{reference:t,...n}=e;return{...n,ref:t}}n.d(t,{renameRef:()=>i})},21593:(e,t,n)=>{"use strict";n.d(t,{CustomComponentDefaultLink:()=>o});var i=n(50959),r=n(38952);function o(e){return i.createElement("a",{...(0,r.renameRef)(e)})}i.PureComponent},34094:(e,t,n)=>{"use strict";n.d(t,{getTextForTooltip:()=>a});var i=n(50959);const r=e=>(0,i.isValidElement)(e)&&Boolean(e.props.children),o=e=>null==e||"boolean"==typeof e||"{}"===JSON.stringify(e)?"":e.toString()+" ",a=e=>Array.isArray(e)||(0,i.isValidElement)(e)?i.Children.toArray(e).reduce(((e,t)=>{let n="";return n=(0,i.isValidElement)(t)&&r(t)?a(t.props.children):(0,i.isValidElement)(t)&&!r(t)?"":o(t),e.concat(n)}),"").trim():o(e)},29196:(e,t,n)=>{"use strict";n.d(t,{useHintShowAnimation:()=>r});var i=n(50959);function r(e){const[t,n]=(0,i.useState)(!1);return(0,i.useLayoutEffect)((()=>{const t=setTimeout((()=>n(!0)),50),i=setTimeout((()=>n(!1)),null!=e?e:2500);return()=>{clearTimeout(t),clearTimeout(i)}}),[]),t}},
24437:(e,t,n)=>{"use strict";n.d(t,{DialogBreakpoints:()=>r});var i=n(53330);const r={SmallHeight:i["small-height-breakpoint"],TabletSmall:i["tablet-small-breakpoint"],TabletNormal:i["tablet-normal-breakpoint"]}},79418:(e,t,n)=>{"use strict";n.d(t,{AdaptivePopupDialog:()=>D});var i=n(50959),r=n(50151),o=n(97754),a=n.n(o),s=n(68335),l=n(63273),c=n(35749),d=n(82206),u=n(1109),h=n(24437),m=n(90692),p=n(95711);var g=n(52092),f=n(76422),v=n(11542),b=n(57340);const y=i.createContext({setHideClose:()=>{}});var w=n(80822);function C(e){const{title:t,titleTextWrap:r=!1,subtitle:o,showCloseIcon:s=!0,onClose:l,onCloseButtonKeyDown:c,renderBefore:d,renderAfter:u,draggable:h,className:m,unsetAlign:p,closeAriaLabel:g=v.t(null,void 0,n(47742)),closeButtonReference:f}=e,[C,x]=(0,i.useState)(!1);return i.createElement(y.Provider,{value:{setHideClose:x}},i.createElement("div",{className:a()(w.container,m,(o||p)&&w.unsetAlign)},d,i.createElement("div",{"data-dragg-area":h,className:w.title},i.createElement("div",{className:a()(r?w.textWrap:w.ellipsis)},t),o&&i.createElement("div",{className:a()(w.ellipsis,w.subtitle)},o)),u,s&&!C&&i.createElement(b.CloseButton,{className:w.close,"data-name":"close","aria-label":g,onClick:l,onKeyDown:c,ref:f,size:"medium",preservePaddings:!0})))}var x=n(53017),_=n(90186),E=n(56570),S=n(8473);const R={vertical:20},N={vertical:0};class D extends i.PureComponent{constructor(){super(...arguments),this._controller=null,this._reference=null,this._orientationMediaQuery=null,this._embedResizerOverridesEnabled=E.enabled("embed_resizer_overrides"),this._renderChildren=(e,t)=>(this._controller=e,this.props.render({requestResize:this._requestResize,centerAndFit:this._centerAndFit,isSmallWidth:t})),this._handleReference=e=>this._reference=e,this._handleCloseBtnClick=()=>{this.props.onKeyboardClose&&this.props.onKeyboardClose(),this._handleClose()},this._handleClose=()=>{this.props.onClose()},this._handleOpen=()=>{void 0!==this.props.onOpen&&this.props.isOpened&&this.props.onOpen(this.props.fullScreen||window.matchMedia(h.DialogBreakpoints.TabletSmall).matches)},this._handleKeyDown=e=>{if(!e.defaultPrevented){if(this.props.onKeyDown&&this.props.onKeyDown(e),27===(0,s.hashFromEvent)(e)){if(e.defaultPrevented)return;if(this.props.forceCloseOnEsc&&this.props.forceCloseOnEsc())return this.props.onKeyboardClose&&this.props.onKeyboardClose(),void this._handleClose();const{activeElement:n}=document;if(null!==n){if(e.preventDefault(),"true"===(t=n).getAttribute("data-haspopup")&&"true"!==t.getAttribute("data-expanded"))return void this._handleClose();const i=this._reference;if(null!==i&&(0,c.isTextEditingField)(n))return void i.focus();if(null==i?void 0:i.contains(n))return this.props.onKeyboardClose&&this.props.onKeyboardClose(),void this._handleClose()}}var t,n;(function(e){if("function"==typeof e)return e();return Boolean(e)})(this.props.disableTabNavigationContainment)||(n=e,[9,s.Modifiers.Shift+9].includes((0,s.hashFromEvent)(n))&&n.stopPropagation())}},this._requestResize=()=>{
null!==this._controller&&this._controller.recalculateBounds()},this._centerAndFit=()=>{null!==this._controller&&this._controller.centerAndFit()},this._calculatePositionWithOffsets=(e,t)=>{const n=(0,r.ensureDefined)(this.props.fullScreenViewOffsets).value();return{top:n.top,left:(0,l.isRtl)()?-n.right:n.left,width:t.clientWidth-n.left-n.right,height:t.clientHeight-n.top-n.bottom}}}componentDidMount(){this.props.ignoreClosePopupsAndDialog||f.subscribe(g.CLOSE_POPUPS_AND_DIALOGS_COMMAND,this._handleClose,null),this._handleOpen(),void 0!==this.props.onOpen&&(this._orientationMediaQuery=window.matchMedia("(orientation: portrait)"),this._orientationMediaQuery.addEventListener("change",this._handleOpen)),this.props.fullScreenViewOffsets&&this.props.fullScreen&&this.props.fullScreenViewOffsets.subscribe(this._requestResize)}componentWillUnmount(){this.props.ignoreClosePopupsAndDialog||f.unsubscribe(g.CLOSE_POPUPS_AND_DIALOGS_COMMAND,this._handleClose,null),null!==this._orientationMediaQuery&&this._orientationMediaQuery.removeEventListener("change",this._handleOpen),this.props.fullScreenViewOffsets&&this.props.fullScreen&&this.props.fullScreenViewOffsets.unsubscribe(this._requestResize)}focus(){(0,r.ensureNotNull)(this._reference).focus()}getElement(){return this._reference}contains(e){var t,n;return null!==(n=null===(t=this._reference)||void 0===t?void 0:t.contains(e))&&void 0!==n&&n}render(){const{className:e,wrapperClassName:t,headerClassName:n,isOpened:r,title:o,titleTextWrap:s,dataName:l,onClickOutside:c,additionalElementPos:g,additionalHeaderElement:f,backdrop:v,shouldForceFocus:b=!0,shouldReturnFocus:y,onForceFocus:w,showSeparator:E,subtitle:D,draggable:I=!0,fullScreen:k=!1,showCloseIcon:P=!0,rounded:B=!0,isAnimationEnabled:T,growPoint:F,dialogTooltip:M,unsetHeaderAlign:A,onDragStart:L,dataDialogName:O,closeAriaLabel:z,containerAriaLabel:W,reference:Z,containerTabIndex:j,closeButtonReference:H,onCloseButtonKeyDown:q,shadowed:G,fullScreenViewOffsets:K,fixedBody:U,onClick:V}=this.props,X="after"!==g?f:void 0,Y="after"===g?f:void 0,J="string"==typeof o?o:O||"",Q=(0,_.filterDataProps)(this.props),$=(0,x.mergeRefs)([this._handleReference,Z]);return i.createElement(m.MatchMedia,{rule:h.DialogBreakpoints.SmallHeight},(g=>i.createElement(m.MatchMedia,{rule:h.DialogBreakpoints.TabletSmall},(h=>i.createElement(d.PopupDialog,{rounded:!(h||k)&&B,className:a()(S.dialog,k&&K&&S.bounded,e),isOpened:r,reference:$,onKeyDown:this._handleKeyDown,onClickOutside:c,onClickBackdrop:c,fullscreen:h||k,guard:g?N:R,boundByScreen:h||k,shouldForceFocus:b,onForceFocus:w,shouldReturnFocus:y,backdrop:v,draggable:I,isAnimationEnabled:T,growPoint:F,name:this.props.dataName,dialogTooltip:M,onDragStart:L,containerAriaLabel:W,containerTabIndex:j,calculateDialogPosition:k&&K?this._calculatePositionWithOffsets:void 0,shadowed:G,fixedBody:U,onClick:V,...Q},i.createElement("div",{className:a()(S.wrapper,t),"data-name":l,"data-dialog-name":J},void 0!==o&&i.createElement(C,{draggable:I&&!(h||k),onClose:this._handleCloseBtnClick,renderAfter:Y,renderBefore:X,
subtitle:D,title:o,titleTextWrap:s,showCloseIcon:P,className:n,unsetAlign:A,closeAriaLabel:z,closeButtonReference:H,onCloseButtonKeyDown:q}),E&&i.createElement(u.Separator,{className:S.separator}),i.createElement(p.PopupContext.Consumer,null,(e=>this._renderChildren(e,h||k)))))))))}}},69654:(e,t,n)=>{"use strict";n.d(t,{DialogSearch:()=>u});var i=n(50959),r=n(97754),o=n.n(r),a=n(11542),s=n(9745),l=n(69859),c=n(54313),d=n(59086);function u(e){const{children:t,isMobile:r,renderInput:u,onCancel:m,containerClassName:p,inputContainerClassName:g,iconClassName:f,cancelTitle:v=a.t(null,void 0,n(4543)),...b}=e;return i.createElement("div",{className:o()(d.container,r&&d.mobile,p)},i.createElement("div",{className:o()(d.inputContainer,r&&d.mobile,g,m&&d.withCancel)},u||i.createElement(h,{isMobile:r,...b})),t,i.createElement(s.Icon,{className:o()(d.icon,r&&d.mobile,f),icon:r?c:l}),m&&(!r||""!==b.value)&&i.createElement("div",{className:o()(d.cancel,r&&d.mobile),onClick:m},v))}function h(e){const{className:t,reference:n,isMobile:r,value:a,onChange:s,onFocus:l,onBlur:c,onKeyDown:u,onSelect:h,placeholder:m,activeDescendant:p,...g}=e;return i.createElement("input",{...g,ref:n,type:"text",className:o()(t,d.input,r&&d.mobile),autoComplete:"off","data-role":"search",placeholder:m,value:a,onChange:s,onFocus:l,onBlur:c,onSelect:h,onKeyDown:u,"aria-activedescendant":p})}},22265:(e,t,n)=>{"use strict";n.d(t,{DialogSidebarItem:()=>h,DialogSidebarWrapper:()=>u});var i,r=n(50959),o=n(97754),a=n.n(o),s=n(9745),l=n(65631),c=n(68648),d=n(87886);function u(e){return r.createElement("div",{className:d.wrapper,...e})}function h(e){const{mode:t,title:n,icon:i,isActive:o,onClick:u,tag:h="div",reference:m,className:p,mobileFontSize:g="medium",showLastDivider:f,useBoldIconsForMobile:v,hideArrow:b,...y}=e,{isMobile:w,isTablet:C}=(0,l.getSidebarMode)(t),x=function(){if(w&&v)return null==i?void 0:i.bold;return o?null==i?void 0:i.bold:null==i?void 0:i.default}();return r.createElement(h,{...y,ref:m,title:C?n:"",className:a()(d.tab,C&&d.isTablet,w&&d.isMobile,o&&d.active,b&&d.withoutArrow,p,C&&"apply-common-tooltip"),onClick:u},i&&r.createElement(s.Icon,{className:d.icon,icon:x}),!C&&r.createElement("span",{className:a()(d.title,!i&&d.withoutIcon,"medium"===g?d.medium:d.large,f&&d.showLastDivider)},r.createElement("span",{className:a()(d.titleText,"apply-overflow-tooltip")},n),w&&!b&&r.createElement(s.Icon,{className:d.nested,icon:c})))}!function(e){e.Medium="medium",e.Large="large"}(i||(i={}))},65631:(e,t,n)=>{"use strict";var i,r;function o(e){return{isMobile:"mobile"===e,isTablet:"tablet"===e}}n.d(t,{getSidebarMode:()=>o}),function(e){e.Bold="bold",e.Default="default"}(i||(i={})),function(e){e.Tablet="tablet",e.Mobile="mobile"}(r||(r={}))},75585:(e,t,n)=>{"use strict";n.r(t),n.d(t,{IndicatorsLibraryContainer:()=>pe});var i=n(50959),r=n(11542),o=n(928);const a=r.t(null,void 0,n(69644));var s,l,c,d,u,h,m;!function(e){e.Title="Title",e.Item="Item",e.Loader="Loader"}(s||(s={})),function(e){e.User="Script$USER",e.Public="Script$PUB",
e.InviteOnly="Script$INVITE",e.Favorite="Script$FAVORITE",e.BuiltIn="tv-basicstudies",e.CandlestickPatterns="candlestick-patterns",e.Standard="Script$STD",e.VolumeProfile="tv-volumebyprice",e.Strategies="strategies",e.EditorsPicks="editorsPicks",e.Trending="trending",e.AutoJava="auto-java",e.AutoStandard="auto-standard",e.Auto="auto",e.OldChartPatterns="tv-chartpatterns",e.ChartPatterns="tv-chart_patterns"}(l||(l={})),function(e){e.Favorites="favorites",e.BuiltIns="built-ins",e.PublicLibrary="public-library",e.UserScripts="my-scripts",e.InviteOnlyScripts="invite-only-scripts",e.Addons="addons",e.Financials="financials"}(c||(c={})),function(e){e.Indicators="indicators",e.Strategies="strategies",e.Patterns="patterns",e.Profiles="profiles"}(d||(d={})),function(e){e.Top="top",e.EditorsPicks="editorsPicks",e.Trending="trending"}(u||(u={})),function(e){e.Top="top",e.Trending="trending"}(h||(h={})),function(e){e.Favorites="favorites",e.IncomeStatement="income statements",e.BalanceSheet="balance sheet",e.CashFlow="cash flow",e.Ratios="ratios",e.Statistics="statistics"}(m||(m={}));var p=n(68159),g=n(56570),f=n(630);function v(e,t){const n=e.title.toLowerCase(),i=t.title.toLowerCase();return n<i?-1:n>i?1:0}const b={earning:new RegExp("EPS"),earnings:new RegExp("EPS"),"trailing twelve months":new RegExp("TTM")};function y(e){var t;const{id:i,description:o,shortDescription:s,description_localized:l,is_hidden_study:c,version:d,extra:u,tags:h}=e,m=g.enabled("graying_disabled_tools_enabled")&&(null===(t=window.ChartApiInstance)||void 0===t?void 0:t.studiesAccessController.isToolGrayed(o));return{id:i,title:l||r.t(o,{context:"study"},n(83477)),shortDescription:s,shortTitle:s,isStrategy:p.StudyMetaInfo.isScriptStrategy(e),isHidden:c,isNew:null==u?void 0:u.isNew,isUpdated:null==u?void 0:u.isUpdated,isBeta:null==u?void 0:u.isBeta,isPro:null==u?void 0:u.isPro,proBadgeTitle:a,isFundamental:!1,isOverlay:e.is_price_study,studyData:{id:i,version:d,descriptor:{type:"java",studyId:e.id},packageName:w(i,u)},isGrayed:m,tags:h}}function w(e,t){return(null==t?void 0:t.isChartPattern)?"tv-chart_patterns":(null==t?void 0:t.isAuto)?"auto-java":p.StudyMetaInfo.getPackageName(e)}var C=n(97754),x=n.n(C),_=n(63932),E=n(79418),S=n(49483),R=n(69654),N=n(22265),D=n(80046);function I(e){const{reference:t,className:n,...r}=e;return i.createElement("div",{ref:t,className:x()(D.container,n),...r,"data-role":"dialog-content"})}var k=n(74136);function P(e){const{children:t,className:n,disabled:r}=e;return i.createElement("span",{className:x()(k.title,r&&k.disabled,n)},t)}const B=i.createContext(null);var T=n(24637),F=n(36189),M=n(68335),A=n(70307);function L(e){const t=(0,i.useContext)(B),{style:o,isMobile:a,item:s,query:l,regExpRules:c,isBeta:d,isNew:u,isUpdated:h,isSelected:m,isHighlighted:p,reference:g,onClick:f,renderActions:v,isPro:b,proBadgeTitle:y,onItemActionsClick:w,favoriteClickHandler:C,hideEP:_}=e,{isFavorite:E,isLocked:S,public:R,editorsPick:N}=s,D=void 0!==E,I=O(f,s),k=(0,i.useCallback)((e=>{e.stopPropagation(),null==w||w()}),[w]),M=(0,
i.useCallback)((e=>{if(C)return null==w||w(),void C(e);if(null==t?void 0:t.toggleFavorite){O((e=>{null==w||w(),t.toggleFavorite(e)}),s)(e)}}),[C,w,null==t?void 0:t.toggleFavorite]),L=x()(A.container,a&&A.mobile,s.isGrayed&&A.disabled,m&&A.selected,p&&A.highlighted);return i.createElement("div",{ref:g,className:L,onClick:I,style:o,"data-role":"list-item","data-disabled":s.isGrayed,"data-title":s.title,"data-id":s.id},i.createElement("div",{className:x()(A.main,!D&&A.paddingLeft)},D&&i.createElement(F.FavoriteButton,{className:x()(A.favorite,E&&A.isActive),isFilled:E,onClick:M}),i.createElement(P,{disabled:s.isGrayed,className:x()(a&&A.mobileText)},i.createElement(T.HighlightedText,{queryString:l,rules:c,text:s.title})),!1,d&&i.createElement(BadgeStatus,{type:"beta",className:A.badge}),u&&i.createElement(BadgeStatus,{type:"new",className:A.badge}),h&&i.createElement(BadgeStatus,{type:"updated",className:A.badge}),Boolean(N&&!_)&&i.createElement(BadgeStatus,{type:"ep",className:A.badge,tooltip:r.t(null,void 0,n(10640))}),!1),R&&i.createElement("a",{href:R.authorLink,className:A.author,target:"_blank",onClick:k},R.authorName),!a&&R&&i.createElement("span",{className:A.likes},R.likesCount),!1)}function O(e,t){return n=>{const i=0===(0,M.modifiersFromEvent)(n)&&0===n.button;!n.defaultPrevented&&e&&i&&(n.preventDefault(),e(t))}}var z,W=n(71921);function Z(e){const{title:t,type:n,className:r}=e;return i.createElement("h3",{className:x()(W.title,"Small"===n&&W.small,"Normal"===n&&W.normal,"Large"===n&&W.large,r)},t)}!function(e){e.Small="Small",e.Normal="Normal",e.Large="Large"}(z||(z={}));var j=n(72910);function H(e){const{style:t,children:n,isMobile:r}=e;return i.createElement("div",{style:t,className:x()(j.container,r&&j.mobile)},n)}var q=n(9745),G=n(94720),K=n(64856);function U(e){const{className:t,icon:n,title:r,description:o,buttonText:a,buttonAction:s}=e;return i.createElement("div",{className:x()(K.container,t)},n&&i.createElement(q.Icon,{icon:n,className:K.image}),r&&i.createElement("h3",{className:K.title},r),o&&i.createElement("p",{className:K.description},o),a&&s&&i.createElement(G.Button,{onClick:s,className:K.button},a))}function V(e){const[t,n]=(0,i.useState)(null);function r(e){return e.findIndex((e=>(null==t?void 0:t.id)===e.id))}return[t,n,function(){n(function(){var n;const i=r(e),o=i===e.length-1;return null===t||-1===i?null!==(n=e[0])&&void 0!==n?n:null:o?e[i]:e[i+1]}())},function(){n(function(){var n;const i=r(e);return null===t||0===i||-1===i?null!==(n=e[0])&&void 0!==n?n:null:e[i-1]}())}]}var X=n(19785),Y=n(23390),J=n(28598);function Q(e){const{reference:t,data:o,isOpened:a,onClose:s,applyStudy:l,shouldReturnFocus:c}=e,[d,u]=(0,i.useState)(""),h=(0,i.useMemo)((()=>(0,X.createRegExpList)(d,b)),[d]),m=(0,i.useMemo)((()=>d?(0,X.rankedSearch)({data:o,rules:h,queryString:d,primaryKey:"shortDescription",secondaryKey:"title",optionalPrimaryKey:"shortTitle",tertiaryKey:"tags"}):o),[d,h,o]),p=(0,
i.useMemo)((()=>m.slice().sort($)),[m]),{highlightedItem:g,selectedItem:f,selectedNodeReference:v,scrollContainerRef:y,searchInputRef:w,onClickStudy:C,handleKeyDown:D}=function(e,t,n,r){let o=0;const[a,s]=(0,i.useState)(null),l=(0,i.useRef)(null),c=(0,i.useRef)(null),[d,u,h,m]=V(t),p=(0,i.useRef)(null);return(0,i.useEffect)((()=>{e?g(0):u(null)}),[e]),(0,i.useEffect)((()=>{void 0!==r&&(g(0),u(null))}),[r]),(0,i.useEffect)((()=>(a&&(o=setTimeout((()=>{s(null)}),1500)),()=>{clearInterval(o)})),[a]),{highlightedItem:a,scrollContainerRef:l,selectedNodeReference:c,selectedItem:d,searchInputRef:p,onClickStudy:function(e){n&&(n(e),u(e),s(e))},handleKeyDown:function(e){const[t,i]=function(e,t){if(null===e.current||null===t.current)return[0,0];const n=e.current.getBoundingClientRect(),i=t.current.getBoundingClientRect(),{height:r}=n,o=n.top-i.top,a=n.bottom-i.bottom+r<0?0:r,s=o-r>0?0:r,{scrollTop:l}=t.current;return[l-s,l+a]}(c,l);if(40===(0,M.hashFromEvent)(e)&&(e.preventDefault(),h(),g(i)),38===(0,M.hashFromEvent)(e)&&(e.preventDefault(),m(),g(t)),13===(0,M.hashFromEvent)(e)&&d){if(!n)return;n(d),s(d)}}};function g(e){null!==l.current&&l.current.scrollTo&&l.current.scrollTo(0,e)}}(a,p,l),k=""===d&&!p.length;return(0,i.useEffect)((()=>{var e;a||u(""),S.CheckMobile.any()||null===(e=w.current)||void 0===e||e.focus()}),[a]),i.createElement(E.AdaptivePopupDialog,{className:x()(J.dialogLibrary),isOpened:a,onClose:s,onClickOutside:s,title:r.t(null,void 0,n(84549)),dataName:"indicators-dialog",onKeyDown:D,shouldReturnFocus:c,ref:t,render:()=>i.createElement(i.Fragment,null,i.createElement(R.DialogSearch,{reference:w,placeholder:r.t(null,void 0,n(8573)),onChange:P,onFocus:B}),i.createElement(N.DialogSidebarWrapper,null,i.createElement(I,{reference:y,className:J.scroll},k?i.createElement(_.Spinner,null):p.length?i.createElement(i.Fragment,null,i.createElement(H,null,i.createElement(Z,{title:r.t(null,void 0,n(7378))})),p.map((e=>i.createElement(L,{key:e.id,item:e,onClick:()=>C(e),query:d,regExpRules:h,reference:(null==f?void 0:f.id)===e.id?v:void 0,isSelected:(null==f?void 0:f.id)===e.id,isHighlighted:(null==g?void 0:g.id)===e.id,favoriteClickHandler:t=>{t.stopPropagation(),(0,Y.toggleFavorite)(e.title)}})))):i.createElement(U,{className:J.noContentBlock,description:r.t(null,void 0,n(70269))}))))});function P(e){u(e.target.value)}function B(){var e;d.length>0&&(null===(e=w.current)||void 0===e||e.select())}}function $(e,t){return e.isFavorite===t.isFavorite?0:e.isFavorite?-1:1}var ee=n(76422),te=n(16638),ne=n(64147),ie=n(24437),re=n(32227),oe=n(12481),ae=n(29196),se=n(77335),le=n(99084);function ce(e){const{text:t}=e,n=(0,ae.useHintShowAnimation)(2500);return i.createElement("div",{className:se.container},i.createElement("div",{className:se.centerElement},i.createElement("div",{className:C(se.contentWrap,n&&se.noticeShowed)},i.createElement(q.Icon,{icon:le,className:se.icon}),i.createElement("div",{className:se.textWrap},t))))}class de{constructor(e){this._showed=!1,this._wrap=document.createElement("div"),this._container=e,
this._debouncedHide=(0,oe.default)((()=>this.hide()),3e3)}show(e){this._wrap&&!this._showed&&(this._showed=!0,this._container.appendChild(this._wrap),re.render(i.createElement(ce,{text:r.t(null,{replace:{studyTitle:e}},n(33673))}),this._wrap),this._debouncedHide())}hide(){this._wrap&&(this._showed=!1,re.unmountComponentAtNode(this._wrap),this._wrap.remove())}destroy(){this.hide(),delete this._wrap}}var ue=n(72708);function he(e,t){return e[t]||[]}var me=n(28124);class pe extends class{constructor(e){this._searchInputRef=i.createRef(),this._dialog=i.createRef(),this._rootInstance=null,this._visibility=new ne.WatchedValue(!1),this._container=document.createElement("div"),this._isForceRender=!1,this._parentSources=[],this._isDestroyed=!1,this._deepFundamentalsHistoryNotificationHasBeenShown=!1,this._hintRenderer=null,this._showDeepFundamentalsHistoryNotification=()=>{},this._chartWidgetCollection=e}isDestroyed(){return this._isDestroyed}visible(){return this._visibility.readonly()}resetAllStudies(){}updateFavorites(){}open(e,t,n,i,r){this._parentSources=e,this._updateSymbol(),this._setProps({isOpened:!0,shouldReturnFocus:null==r?void 0:r.shouldReturnFocus}),this._visibility.setValue(!0),ee.emit("indicators_dialog")}show(e){this.open([],void 0,void 0,void 0,e)}hide(){var e;this._parentSources=[],this._setProps({isOpened:!1}),this._visibility.setValue(!1),null===(e=this._hintRenderer)||void 0===e||e.destroy(),this._hintRenderer=null}destroy(){var e,t;this._isDestroyed=!0,null===(e=this._hintRenderer)||void 0===e||e.destroy(),null===(t=this._rootInstance)||void 0===t||t.unmount(),this._rootInstance=null}_shouldPreventRender(){return this._isDestroyed||!this._isForceRender&&!this._getProps().value().isOpened}_getRenderData(){return{props:this._getProps().value(),container:this._getContainer()}}_applyStudy(e,t){var n;e.isGrayed?ee.emit("onGrayedObjectClicked",{type:"study",name:e.shortDescription}):(S.CheckMobile.any()||null===(n=this._searchInputRef.current)||void 0===n||n.select(),async function(e,t,n,i,r,a){const s=e.activeChartWidget.value();if(!s)return null;const{studyData:l}=t;if(!l)return Promise.resolve(null);const c=l.descriptor;if("java"===c.type){const e=(0,f.tryFindStudyLineToolNameByStudyId)(c.studyId);if(null!==e)return await(0,f.initLineTool)(e),o.tool.setValue(e),null}return s.insertStudy(l.descriptor,n,{stubTitle:t.shortDescription,isFundamental:t.isFundamental,isOverlay:t.isOverlay},void 0,a)}(this._chartWidgetCollection,e,this._parentSources,0,this._symbol,(()=>this._showHint(e.title))).then((t=>{var n,i,r;null===t&&(null===(n=this._hintRenderer)||void 0===n||n.hide()),null!==t&&((0,ue.hasConfirmInputs)(t.metaInfo().inputs)||(0,ue.isSymbolicStudy)(t.metaInfo()))&&(null===(i=this._hintRenderer)||void 0===i||i.show(e.title));window.is_authenticated;S.CheckMobile.any()||(null===document.activeElement||document.activeElement===document.body||null!==this._dialog.current&&this._dialog.current.contains(document.activeElement))&&(null===(r=this._searchInputRef.current)||void 0===r||r.focus())})))}_setProps(e){
const t=this._getProps().value(),{isOpened:n}=t;this._isForceRender=n&&"isOpened"in e&&!e.isOpened;const i={...t,...e};this._getProps().setValue(i)}_requestBuiltInJavaStudies(){return(0,te.studyMetaInfoRepository)().findAllJavaStudies()}_focus(){var e;this._getProps().value().isOpened&&(null===(e=this._dialog.current)||void 0===e||e.focus())}_getContainer(){return this._container}_getDialog(){return this._dialog}_getSymbol(){return this._symbol}_updateSymbol(){this._symbol=void 0}_showHint(e){var t,n,i;if(window.matchMedia(ie.DialogBreakpoints.TabletSmall).matches){if(null===(t=this._hintRenderer)||void 0===t||t.hide(),!this._hintRenderer){const e=null===(n=this._dialog.current)||void 0===n?void 0:n.getElement();e&&(this._hintRenderer=new de(e))}null===(i=this._hintRenderer)||void 0===i||i.show(e)}}}{constructor(e,t){super(e),this._options={onWidget:!1},this._indicatorData=[],t&&(this._options=t),this._props=new ne.WatchedValue({data:[],applyStudy:this._applyStudy.bind(this),isOpened:!1,reference:this._getDialog(),onClose:this.hide.bind(this)}),this._getProps().subscribe(this._render.bind(this)),this._init()}_getProps(){return this._props}async _init(){const e=function(e){const t={};return e.forEach((e=>{const{studyData:n}=e;if(!n)return;const{packageName:i}=n;i in t?t[i].push(e):t[i]=[e]})),t}(function(e,t=!0){return e.filter((e=>{const n=!!t||!function(e){return e.isStrategy}(e);return!e.isHidden&&n}))}((await this._requestBuiltInJavaStudies()).map(y)));this._indicatorData=await async function(e,t){let n={...t};return[...he(n,"tv-basicstudies"),...he(n,"Script$STD"),...he(n,"tv-volumebyprice")].filter((e=>!e.isStrategy)).sort(v)}(this._options.onWidget,e),this._setFavorites(),this._setProps({data:this._indicatorData}),Y.favoriteAdded.subscribe(null,(()=>this._refreshFavorites())),Y.favoriteRemoved.subscribe(null,(()=>this._refreshFavorites()))}_setFavorites(){g.enabled("items_favoriting")&&this._indicatorData.forEach((e=>{e.isFavorite=(0,Y.isFavorite)(e.title)}))}_refreshFavorites(){this._setFavorites(),this._setProps({data:this._indicatorData})}_render(){if(this._shouldPreventRender())return;const{props:e,container:t}=this._getRenderData(),n=i.createElement(Q,{...e});this._rootInstance?this._rootInstance.render(n):this._rootInstance=(0,me.createReactRoot)(n,t)}}},36189:(e,t,n)=>{"use strict";n.d(t,{FavoriteButton:()=>u});var i=n(11542),r=n(50959),o=n(97754),a=n(9745),s=n(39146),l=n(48010),c=n(98992);const d={add:i.t(null,void 0,n(69207)),remove:i.t(null,void 0,n(85106))};function u(e){const{className:t,isFilled:n,isActive:i,onClick:u,...h}=e;return r.createElement(a.Icon,{...h,className:o(c.favorite,"apply-common-tooltip",n&&c.checked,i&&c.active,t),icon:n?s:l,onClick:u,title:n?d.remove:d.add})}},19785:(e,t,n)=>{"use strict";n.d(t,{createRegExpList:()=>a,getHighlightedChars:()=>s,rankedSearch:()=>o});var i,r=n(37265);function o(e){const{data:t,rules:n,queryString:i,isPreventedFromFiltering:o,primaryKey:a,secondaryKey:s=a,optionalPrimaryKey:l,tertiaryKey:c}=e;return t.map((e=>{
const t=l&&e[l]?e[l]:e[a],o=e[s],d=c&&e[c];let u,h=0;return n.forEach((e=>{var n,a,s,l,c;const{re:m,fullMatch:p}=e;if(m.lastIndex=0,(0,r.isString)(t)&&t&&t.toLowerCase()===i.toLowerCase())return h=4,void(u=null===(n=t.match(p))||void 0===n?void 0:n.index);if((0,r.isString)(t)&&p.test(t))return h=3,void(u=null===(a=t.match(p))||void 0===a?void 0:a.index);if((0,r.isString)(o)&&p.test(o))return h=2,void(u=null===(s=o.match(p))||void 0===s?void 0:s.index);if((0,r.isString)(o)&&m.test(o))return h=2,void(u=null===(l=o.match(m))||void 0===l?void 0:l.index);if(Array.isArray(d))for(const e of d)if(p.test(e))return h=1,void(u=null===(c=e.match(p))||void 0===c?void 0:c.index)})),{matchPriority:h,matchIndex:u,item:e}})).filter((e=>o||e.matchPriority)).sort(((e,t)=>{if(e.matchPriority<t.matchPriority)return 1;if(e.matchPriority>t.matchPriority)return-1;if(e.matchPriority===t.matchPriority){if(void 0===e.matchIndex||void 0===t.matchIndex)return 0;if(e.matchIndex>t.matchIndex)return 1;if(e.matchIndex<t.matchIndex)return-1}return 0})).map((({item:e})=>e))}function a(e,t){const n=[],i=e.toLowerCase(),r=e.split("").map(((e,t)=>`(${0!==t?`[/\\s-]${l(e)}`:l(e)})`)).join("(.*?)")+"(.*)";return n.push({fullMatch:new RegExp(`(${l(e)})`,"i"),re:new RegExp(`^${r}`,"i"),reserveRe:new RegExp(r,"i"),fuzzyHighlight:!0}),t&&t.hasOwnProperty(i)&&n.push({fullMatch:t[i],re:t[i],fuzzyHighlight:!1}),n}function s(e,t,n){const i=[];return e&&n?(n.forEach((e=>{const{fullMatch:n,re:r,reserveRe:o}=e;n.lastIndex=0,r.lastIndex=0;const a=n.exec(t),s=a||r.exec(t)||o&&o.exec(t);if(e.fuzzyHighlight=!a,s)if(e.fuzzyHighlight){let e=s.index;for(let t=1;t<s.length;t++){const n=s[t],r=s[t].length;if(t%2){const t=n.startsWith(" ")||n.startsWith("/")||n.startsWith("-");i[t?e+1:e]=!0}e+=r}}else for(let e=0;e<s[0].length;e++)i[s.index+e]=!0})),i):i}function l(e){return e.replace(/[!-/[-^{-}?]/g,"\\$&")}!function(e){e[e.Low=0]="Low",e[e.MediumLow=1]="MediumLow",e[e.Medium=2]="Medium",e[e.High=3]="High",e[e.Highest=4]="Highest"}(i||(i={}))},24637:(e,t,n)=>{"use strict";n.d(t,{HighlightedText:()=>s});var i=n(50959),r=n(97754),o=n(19785),a=n(32248);function s(e){const{queryString:t,rules:n,text:s,className:l}=e,c=(0,i.useMemo)((()=>(0,o.getHighlightedChars)(t,s,n)),[t,n,s]);return i.createElement(i.Fragment,null,c.length?s.split("").map(((e,t)=>i.createElement(i.Fragment,{key:t},c[t]?i.createElement("span",{className:r(a.highlighted,l)},e):i.createElement("span",null,e)))):s)}},90692:(e,t,n)=>{"use strict";n.d(t,{MatchMedia:()=>r});var i=n(50959);class r extends i.PureComponent{constructor(e){super(e),this._handleChange=()=>{this.forceUpdate()},this.state={query:window.matchMedia(this.props.rule)}}componentDidMount(){this._subscribe(this.state.query)}componentDidUpdate(e,t){this.state.query!==t.query&&(this._unsubscribe(t.query),this._subscribe(this.state.query))}componentWillUnmount(){this._unsubscribe(this.state.query)}render(){return this.props.children(this.state.query.matches)}static getDerivedStateFromProps(e,t){return e.rule!==t.query.media?{
query:window.matchMedia(e.rule)}:null}_subscribe(e){e.addEventListener("change",this._handleChange)}_unsubscribe(e){e.removeEventListener("change",this._handleChange)}}},1109:(e,t,n)=>{"use strict";n.d(t,{Separator:()=>a});var i=n(50959),r=n(97754),o=n(47625);function a(e){return i.createElement("div",{className:r(o.separator,e.className)})}},63932:(e,t,n)=>{"use strict";n.d(t,{Spinner:()=>a});var i=n(50959),r=n(97754),o=n(58096);n(7727);function a(e){const t=r(e.className,"tv-spinner","tv-spinner--shown",`tv-spinner--size_${o.spinnerSizeMap[e.size||o.DEFAULT_SIZE]}`);return i.createElement("div",{className:t,style:e.style,role:"progressbar"})}},4237:(e,t,n)=>{"use strict";var i=n(32227);t.createRoot=i.createRoot,i.hydrateRoot},23390:(e,t,n)=>{"use strict";n.r(t),n.d(t,{favoriteAdded:()=>o,favoriteRemoved:()=>a,favoritesSynced:()=>s,isFavorite:()=>d,saveFavorites:()=>m,toggleFavorite:()=>c});var i=n(52033),r=n(56840);const o=new i.Delegate,a=new i.Delegate,s=new i.Delegate;let l=[];function c(e){return-1===u(e)?(function(e){!d(e)&&(l.push(e),m(),o.fire(e))}(e),!0):(function(e){const t=u(e);-1!==t&&(l.splice(t,1),m(),a.fire(e))}(e),!1)}function d(e){return-1!==u(e)}function u(e){return l.indexOf(e)}function h(){var e,t;l=[];const n=Boolean(void 0===(0,r.getValue)("chart.favoriteLibraryIndicators")),i=(0,r.getJSON)("chart.favoriteLibraryIndicators",[]);if(l.push(...i),0===l.length&&n&&"undefined"!=typeof window){const n=JSON.parse(null!==(t=null===(e=window.urlParams)||void 0===e?void 0:e.favorites)&&void 0!==t?t:"{}").indicators;n&&Array.isArray(n)&&l.push(...n)}s.fire()}function m(){const e=l.slice();(0,r.setJSON)("chart.favoriteLibraryIndicators",e)}h(),r.onSync.subscribe(null,h)},99084:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M9 17A8 8 0 1 0 9 1a8 8 0 0 0 0 16Zm3.87-12.15c.36.2.49.66.28 1.02l-4 7a.75.75 0 0 1-1.18.16l-3-3a.75.75 0 1 1 1.06-1.06l2.3 2.3 3.52-6.14a.75.75 0 0 1 1.02-.28Z"/></svg>'},17105:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path stroke="currentColor" stroke-width="1.2" d="m1.5 1.5 21 21m0-21-21 21"/></svg>'},15130:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path stroke="currentColor" stroke-width="1.2" d="m1.5 1.5 15 15m0-15-15 15"/></svg>'},38822:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 14 14" width="14" height="14"><path stroke="currentColor" stroke-width="1.2" d="m1.5 1.5 11 11m0-11-11 11"/></svg>'},63346:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" width="12" height="12"><path stroke="currentColor" stroke-width="1.2" d="m1.5 1.5 9 9m0-9-9 9"/></svg>'},34983:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 10 10" width="10" height="10"><path stroke="currentColor" stroke-width="1.2" d="m1.5 1.5 7 7m0-7-7 7"/></svg>'},68648:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path stroke="currentcolor" stroke-width="1.3" d="M12 9l5 5-5 5"/></svg>'},69859:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none"><path stroke="currentColor" d="M12.4 12.5a7 7 0 1 0-4.9 2 7 7 0 0 0 4.9-2zm0 0l5.101 5"/></svg>'},39146:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18" fill="none"><path fill="currentColor" d="M9 1l2.35 4.76 5.26.77-3.8 3.7.9 5.24L9 13l-4.7 2.47.9-5.23-3.8-3.71 5.25-.77L9 1z"/></svg>'},48010:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18" fill="none"><path stroke="currentColor" d="M9 2.13l1.903 3.855.116.236.26.038 4.255.618-3.079 3.001-.188.184.044.259.727 4.237-3.805-2L9 12.434l-.233.122-3.805 2.001.727-4.237.044-.26-.188-.183-3.079-3.001 4.255-.618.26-.038.116-.236L9 2.13z"/></svg>'},54313:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="M18.5 12.5a6 6 0 1 1-12 0 6 6 0 0 1 12 0Zm-1.25 5.8a7.5 7.5 0 1 1 1.06-1.06l4.22 4.23.53.53L22 23.06l-.53-.53-4.22-4.22Z"/></svg>'},55698:(e,t,n)=>{"use strict";n.d(t,{nanoid:()=>i});let i=(e=21)=>crypto.getRandomValues(new Uint8Array(e)).reduce(((e,t)=>e+=(t&=63)<36?t.toString(36):t<62?(t-26).toString(36).toUpperCase():t>62?"-":"_"),"")}}]);