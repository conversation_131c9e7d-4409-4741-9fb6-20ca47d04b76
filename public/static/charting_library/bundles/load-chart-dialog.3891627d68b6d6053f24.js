(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[5009],{40352:e=>{e.exports={button:"button-PYEOTd6i",disabled:"disabled-PYEOTd6i",hidden:"hidden-PYEOTd6i",icon:"icon-PYEOTd6i",dropped:"dropped-PYEOTd6i"}},36136:e=>{e.exports={"nav-button":"nav-button-znwuaSC1",link:"link-znwuaSC1",background:"background-znwuaSC1",icon:"icon-znwuaSC1","flip-icon":"flip-icon-znwuaSC1","size-large":"size-large-znwuaSC1","preserve-paddings":"preserve-paddings-znwuaSC1","size-medium":"size-medium-znwuaSC1","size-small":"size-small-znwuaSC1","size-xsmall":"size-xsmall-znwuaSC1","size-xxsmall":"size-xxsmall-znwuaSC1","visually-hidden":"visually-hidden-znwuaSC1"}},3196:e=>{e.exports={"tv-circle-logo":"tv-circle-logo-PsAlMQQF","tv-circle-logo--xxxsmall":"tv-circle-logo--xxxsmall-PsAlMQQF","tv-circle-logo--xxsmall":"tv-circle-logo--xxsmall-PsAlMQQF","tv-circle-logo--xsmall":"tv-circle-logo--xsmall-PsAlMQQF","tv-circle-logo--small":"tv-circle-logo--small-PsAlMQQF","tv-circle-logo--medium":"tv-circle-logo--medium-PsAlMQQF","tv-circle-logo--large":"tv-circle-logo--large-PsAlMQQF","tv-circle-logo--xlarge":"tv-circle-logo--xlarge-PsAlMQQF","tv-circle-logo--xxlarge":"tv-circle-logo--xxlarge-PsAlMQQF","tv-circle-logo--xxxlarge":"tv-circle-logo--xxxlarge-PsAlMQQF","tv-circle-logo--visually-hidden":"tv-circle-logo--visually-hidden-PsAlMQQF"}},74581:e=>{e.exports={container:"container-WDZ0PRNh","container-xxsmall":"container-xxsmall-WDZ0PRNh","container-xsmall":"container-xsmall-WDZ0PRNh","container-small":"container-small-WDZ0PRNh","container-medium":"container-medium-WDZ0PRNh","container-large":"container-large-WDZ0PRNh","intent-default":"intent-default-WDZ0PRNh",focused:"focused-WDZ0PRNh",readonly:"readonly-WDZ0PRNh",disabled:"disabled-WDZ0PRNh","with-highlight":"with-highlight-WDZ0PRNh",grouped:"grouped-WDZ0PRNh","adjust-position":"adjust-position-WDZ0PRNh","first-row":"first-row-WDZ0PRNh","first-col":"first-col-WDZ0PRNh",stretch:"stretch-WDZ0PRNh","font-size-medium":"font-size-medium-WDZ0PRNh","font-size-large":"font-size-large-WDZ0PRNh","no-corner-top-left":"no-corner-top-left-WDZ0PRNh","no-corner-top-right":"no-corner-top-right-WDZ0PRNh","no-corner-bottom-right":"no-corner-bottom-right-WDZ0PRNh","no-corner-bottom-left":"no-corner-bottom-left-WDZ0PRNh","size-xxsmall":"size-xxsmall-WDZ0PRNh","size-xsmall":"size-xsmall-WDZ0PRNh","size-small":"size-small-WDZ0PRNh","size-medium":"size-medium-WDZ0PRNh","size-large":"size-large-WDZ0PRNh","intent-success":"intent-success-WDZ0PRNh","intent-warning":"intent-warning-WDZ0PRNh","intent-danger":"intent-danger-WDZ0PRNh","intent-primary":"intent-primary-WDZ0PRNh","border-none":"border-none-WDZ0PRNh","border-thin":"border-thin-WDZ0PRNh","border-thick":"border-thick-WDZ0PRNh",highlight:"highlight-WDZ0PRNh",shown:"shown-WDZ0PRNh"}},81329:e=>{e.exports={"inner-slot":"inner-slot-W53jtLjw",interactive:"interactive-W53jtLjw",icon:"icon-W53jtLjw","inner-middle-slot":"inner-middle-slot-W53jtLjw","before-slot":"before-slot-W53jtLjw","after-slot":"after-slot-W53jtLjw"}},53330:e=>{e.exports={
"tablet-normal-breakpoint":"screen and (max-width: 768px)","small-height-breakpoint":"screen and (max-height: 360px)","tablet-small-breakpoint":"screen and (max-width: 430px)"}},8473:e=>{e.exports={dialog:"dialog-b8SxMnzX",wrapper:"wrapper-b8SxMnzX",separator:"separator-b8SxMnzX",bounded:"bounded-b8SxMnzX"}},67850:e=>{e.exports={container:"container-AhaeiE0y",list:"list-AhaeiE0y",overlayScrollWrap:"overlayScrollWrap-AhaeiE0y",scroll:"scroll-AhaeiE0y"}},7176:e=>{e.exports={container:"container-huGG8x61",title:"title-huGG8x61"}},4914:e=>{e.exports={itemRow:"itemRow-BadjY5sX",active:"active-BadjY5sX",selected:"selected-BadjY5sX",mobile:"mobile-BadjY5sX",itemInfo:"itemInfo-BadjY5sX",title:"title-BadjY5sX",details:"details-BadjY5sX",itemInfoWithPadding:"itemInfoWithPadding-BadjY5sX",favoriteButton:"favoriteButton-BadjY5sX",favorite:"favorite-BadjY5sX",removeButton:"removeButton-BadjY5sX"}},80822:e=>{e.exports={"small-height-breakpoint":"screen and (max-height: 360px)",container:"container-BZKENkhT",unsetAlign:"unsetAlign-BZKENkhT",title:"title-BZKENkhT",subtitle:"subtitle-BZKENkhT",textWrap:"textWrap-BZKENkhT",ellipsis:"ellipsis-BZKENkhT",close:"close-BZKENkhT",icon:"icon-BZKENkhT"}},59086:e=>{e.exports={container:"container-qm7Rg5MB",mobile:"mobile-qm7Rg5MB",inputContainer:"inputContainer-qm7Rg5MB",withCancel:"withCancel-qm7Rg5MB",input:"input-qm7Rg5MB",icon:"icon-qm7Rg5MB",cancel:"cancel-qm7Rg5MB"}},91504:e=>{e.exports={scrollWrap:"scrollWrap-FaOvTD2r"}},60591:e=>{e.exports={wrap:"wrap-vSb6C0Bj","wrap--horizontal":"wrap--horizontal-vSb6C0Bj",bar:"bar-vSb6C0Bj",barInner:"barInner-vSb6C0Bj","barInner--horizontal":"barInner--horizontal-vSb6C0Bj","bar--horizontal":"bar--horizontal-vSb6C0Bj"}},23453:e=>{e.exports={container:"container-RZoAcQrm",labelWrap:"labelWrap-RZoAcQrm",icon:"icon-RZoAcQrm",text:"text-RZoAcQrm"}},32486:e=>{e.exports={sortButton:"sortButton-mMR_mxxG",icon:"icon-mMR_mxxG",buttonContainer:"buttonContainer-mMR_mxxG",skeleton:"skeleton-mMR_mxxG"}},44620:e=>{e.exports={button:"button-tFul0OhX","button-children":"button-children-tFul0OhX",hiddenArrow:"hiddenArrow-tFul0OhX",invisibleFocusHandler:"invisibleFocusHandler-tFul0OhX"}},98992:e=>{e.exports={favorite:"favorite-_FRQhM5Y",hovered:"hovered-_FRQhM5Y",disabled:"disabled-_FRQhM5Y",active:"active-_FRQhM5Y",checked:"checked-_FRQhM5Y"}},32248:e=>{e.exports={highlighted:"highlighted-cwp8YRo6"}},47625:e=>{e.exports={separator:"separator-Pf4rIzEt"}},2908:e=>{e.exports={"tablet-small-breakpoint":"screen and (max-width: 430px)",item:"item-jFqVJoPk",hovered:"hovered-jFqVJoPk",isDisabled:"isDisabled-jFqVJoPk",isActive:"isActive-jFqVJoPk",shortcut:"shortcut-jFqVJoPk",toolbox:"toolbox-jFqVJoPk",withIcon:"withIcon-jFqVJoPk","round-icon":"round-icon-jFqVJoPk",icon:"icon-jFqVJoPk",labelRow:"labelRow-jFqVJoPk",label:"label-jFqVJoPk",showOnHover:"showOnHover-jFqVJoPk","disclosure-item-circle-logo":"disclosure-item-circle-logo-jFqVJoPk",showOnFocus:"showOnFocus-jFqVJoPk"}},11772:e=>{e.exports={button:"button-iLKiGOdQ",hovered:"hovered-iLKiGOdQ",disabled:"disabled-iLKiGOdQ",
active:"active-iLKiGOdQ",hidden:"hidden-iLKiGOdQ"}},57340:(e,t,n)=>{"use strict";n.d(t,{CloseButton:()=>d});var o=n(50959),r=n(64388),i=n(17105),l=n(15130),s=n(38822),a=n(63346),c=n(34983);function u(e="large"){switch(e){case"large":return i;case"medium":default:return l;case"small":return s;case"xsmall":return a;case"xxsmall":return c}}const d=o.forwardRef(((e,t)=>o.createElement(r.NavButton,{...e,ref:t,icon:u(e.size)})))},64388:(e,t,n)=>{"use strict";n.d(t,{NavButton:()=>c});var o=n(50959),r=n(97754),i=n(9745),l=(n(78572),n(36136));function s(e){const{size:t="large",preservePaddings:n,isLink:o,flipIconOnRtl:i,className:s}=e;return r(l["nav-button"],l[`size-${t}`],n&&l["preserve-paddings"],i&&l["flip-icon"],o&&l.link,s)}function a(e){const{children:t,icon:n}=e;return o.createElement(o.Fragment,null,o.createElement("span",{className:l.background}),o.createElement(i.Icon,{icon:n,className:l.icon,"aria-hidden":!0}),t&&o.createElement("span",{className:l["visually-hidden"]},t))}const c=(0,o.forwardRef)(((e,t)=>{const{icon:n,type:r="button",preservePaddings:i,flipIconOnRtl:l,size:c,"aria-label":u,...d}=e;return o.createElement("button",{...d,className:s({...e,children:u}),ref:t,type:r},o.createElement(a,{icon:n},u))}));c.displayName="NavButton";var u=n(21593),d=n(53017);(0,o.forwardRef)(((e,t)=>{const{icon:n,renderComponent:r,"aria-label":i,...l}=e,c=null!=r?r:u.CustomComponentDefaultLink;return o.createElement(c,{...l,className:s({...e,children:i,isLink:!0}),reference:(0,d.isomorphicRef)(t)},o.createElement(a,{icon:n},i))})).displayName="NavAnchorButton"},78572:(e,t,n)=>{"use strict";var o,r,i,l;!function(e){e.Primary="primary",e.QuietPrimary="quiet-primary",e.Secondary="secondary",e.Ghost="ghost"}(o||(o={})),function(e){e.XXSmall="xxsmall",e.XSmall="xsmall",e.Small="small",e.Medium="medium",e.Large="large",e.XLarge="xlarge",e.XXLarge="xxlarge"}(r||(r={})),function(e){e.Brand="brand",e.Gray="gray",e.LightGray="light-gray",e.Green="green",e.Red="red",e.Black="black",e.Gradient="gradient",e.BlackFriday="black-friday",e.CyberMonday="cyber-monday"}(i||(i={})),function(e){e.Semibold18px="semibold18px",e.Semibold16px="semibold16px",e.Semibold14px="semibold14px",e.Medium16px="medium16px",e.Regular16px="regular16px",e.Regular14px="regular14px"}(l||(l={}))},53885:(e,t,n)=>{"use strict";n.d(t,{getStyleClasses:()=>l,isCircleLogoWithUrlProps:()=>s});var o=n(97754),r=n(3196),i=n.n(r);function l(e,t){return o(i()["tv-circle-logo"],i()[`tv-circle-logo--${e}`],t)}function s(e){return"logoUrl"in e&&null!==e.logoUrl&&void 0!==e.logoUrl&&0!==e.logoUrl.length}},86332:(e,t,n)=>{"use strict";n.d(t,{ControlGroupContext:()=>o});const o=n(50959).createContext({isGrouped:!1,cellState:{isTop:!0,isRight:!0,isBottom:!0,isLeft:!0}})},95604:(e,t,n)=>{"use strict";function o(e){let t=0;return e.isTop&&e.isLeft||(t+=1),e.isTop&&e.isRight||(t+=2),e.isBottom&&e.isLeft||(t+=8),e.isBottom&&e.isRight||(t+=4),t}n.d(t,{getGroupCellRemoveRoundBorders:()=>o})},67029:(e,t,n)=>{"use strict";n.d(t,{ControlSkeleton:()=>b,InputClasses:()=>p})
;var o=n(50959),r=n(97754),i=n(50151),l=n(38528),s=n(90186),a=n(86332),c=n(95604);var u=n(74581),d=n.n(u);function m(e){let t="";return 0!==e&&(1&e&&(t=r(t,d()["no-corner-top-left"])),2&e&&(t=r(t,d()["no-corner-top-right"])),4&e&&(t=r(t,d()["no-corner-bottom-right"])),8&e&&(t=r(t,d()["no-corner-bottom-left"]))),t}function h(e,t,n,o){const{removeRoundBorder:i,className:l,intent:s="default",borderStyle:a="thin",size:u,highlight:h,disabled:f,readonly:p,stretch:v,noReadonlyStyles:g,isFocused:b}=e,x=m(null!=i?i:(0,c.getGroupCellRemoveRoundBorders)(n));return r(d().container,d()[`container-${u}`],d()[`intent-${s}`],d()[`border-${a}`],u&&d()[`size-${u}`],x,h&&d()["with-highlight"],f&&d().disabled,p&&!g&&d().readonly,b&&d().focused,v&&d().stretch,t&&d().grouped,!o&&d()["adjust-position"],n.isTop&&d()["first-row"],n.isLeft&&d()["first-col"],l)}function f(e,t,n){const{highlight:o,highlightRemoveRoundBorder:i}=e;if(!o)return d().highlight;const l=m(null!=i?i:(0,c.getGroupCellRemoveRoundBorders)(t));return r(d().highlight,d().shown,d()[`size-${n}`],l)}const p={FontSizeMedium:(0,i.ensureDefined)(d()["font-size-medium"]),FontSizeLarge:(0,i.ensureDefined)(d()["font-size-large"])},v={passive:!1};function g(e,t){const{style:n,id:r,role:i,onFocus:c,onBlur:u,onMouseOver:d,onMouseOut:m,onMouseDown:p,onMouseUp:g,onKeyDown:b,onClick:x,tabIndex:C,startSlot:w,middleSlot:E,endSlot:y,onWheel:N,onWheelNoPassive:S=null,size:R}=e,{isGrouped:P,cellState:k,disablePositionAdjustment:D=!1}=(0,o.useContext)(a.ControlGroupContext),M=function(e,t=null,n){const r=(0,o.useRef)(null),i=(0,o.useRef)(null),l=(0,o.useCallback)((()=>{if(null===r.current||null===i.current)return;const[e,t,n]=i.current;null!==t&&r.current.addEventListener(e,t,n)}),[]),s=(0,o.useCallback)((()=>{if(null===r.current||null===i.current)return;const[e,t,n]=i.current;null!==t&&r.current.removeEventListener(e,t,n)}),[]),a=(0,o.useCallback)((e=>{s(),r.current=e,l()}),[]);return(0,o.useEffect)((()=>(i.current=[e,t,n],l(),s)),[e,t,n]),a}("wheel",S,v);return o.createElement("span",{style:n,id:r,role:i,className:h(e,P,k,D),tabIndex:C,ref:(0,l.useMergedRefs)([t,M]),onFocus:c,onBlur:u,onMouseOver:d,onMouseOut:m,onMouseDown:p,onMouseUp:g,onKeyDown:b,onClick:x,onWheel:N,...(0,s.filterDataProps)(e),...(0,s.filterAriaProps)(e)},w,E,y,o.createElement("span",{className:f(e,k,R)}))}g.displayName="ControlSkeleton";const b=o.forwardRef(g)},78274:(e,t,n)=>{"use strict";n.d(t,{AfterSlot:()=>u,EndSlot:()=>c,MiddleSlot:()=>a,StartSlot:()=>s});var o=n(50959),r=n(97754),i=n(81329),l=n.n(i);function s(e){const{className:t,interactive:n=!0,icon:i=!1,children:s}=e;return o.createElement("span",{className:r(l()["inner-slot"],n&&l().interactive,i&&l().icon,t)},s)}function a(e){const{className:t,children:n}=e;return o.createElement("span",{className:r(l()["inner-slot"],l()["inner-middle-slot"],t)},n)}function c(e){const{className:t,interactive:n=!0,icon:i=!1,children:s}=e;return o.createElement("span",{className:r(l()["inner-slot"],n&&l().interactive,i&&l().icon,t)},s)}function u(e){
const{className:t,children:n}=e;return o.createElement("span",{className:r(l()["after-slot"],t)},n)}},38952:(e,t,n)=>{"use strict";function o(e){const{reference:t,...n}=e;return{...n,ref:t}}n.d(t,{renameRef:()=>o})},21593:(e,t,n)=>{"use strict";n.d(t,{CustomComponentDefaultLink:()=>i});var o=n(50959),r=n(38952);function i(e){return o.createElement("a",{...(0,r.renameRef)(e)})}o.PureComponent},66686:(e,t,n)=>{"use strict";n.d(t,{useComposedKeyboardActionHandlers:()=>s,useKeyboardActionHandler:()=>l,useKeyboardClose:()=>u,useKeyboardEventHandler:()=>a,useKeyboardOpen:()=>d,useKeyboardToggle:()=>c});var o=n(50959),r=n(3343);const i=()=>!0;function l(e,t,n=i,r){return(0,o.useCallback)((o=>{if(r){if("horizontal"===r&&(40===o||38===o))return;if("vertical"===r&&(37===o||39===o))return}const i=e.map((e=>"function"==typeof e?e():e));return!(!n(o)||!i.includes(o))&&(t(o),!0)}),[...e,t,n])}function s(...e){return(0,o.useCallback)((t=>{for(const n of e)if(n(t))return!0;return!1}),[...e])}function a(e,t=!0){const n=s(...e);return(0,o.useCallback)((e=>{n((0,r.hashFromEvent)(e))&&t&&e.preventDefault()}),[n])}function c(e,t=!0){return l([13,32],e,(function(e){if(13===e)return t;return!0}))}function u(e,t){return l([9,(0,o.useCallback)((()=>r.Modifiers.Shift+9),[]),27],t,(0,o.useCallback)((()=>e),[e]))}function d(e,t){return l([40,38],t,(0,o.useCallback)((()=>!e),[e]))}},36104:(e,t,n)=>{"use strict";n.d(t,{useControlDisclosure:()=>r});var o=n(7953);function r(e){const{intent:t,highlight:n,...r}=e,{isFocused:i,...l}=(0,o.useDisclosure)(r);return{...l,isFocused:i,highlight:null!=n?n:i,intent:null!=t?t:i?"primary":"default"}}},7953:(e,t,n)=>{"use strict";n.d(t,{useDisclosure:()=>c});var o=n(50959),r=n(50151),i=n(54717),l=n(29202),s=n(47201),a=n(22064);function c(e){const{id:t,listboxId:n,disabled:c,buttonTabIndex:u=0,onFocus:d,onBlur:m,onClick:h}=e,[f,p]=(0,o.useState)(!1),[v,g]=(0,l.useFocus)(),b=v||f,x=(null!=n?n:void 0!==t)?(0,a.createDomId)(t,"listbox"):void 0,C=(0,o.useRef)(null),w=(0,o.useCallback)((e=>{var t;return null===(t=C.current)||void 0===t?void 0:t.focus(e)}),[C]),E=(0,o.useRef)(null),y=(0,o.useCallback)((()=>(0,r.ensureNotNull)(E.current).focus()),[E]),N=(0,o.useCallback)((()=>p(!0)),[p]),S=(0,o.useCallback)(((e=!1,t=!1)=>{p(!1);const{activeElement:n}=document;n&&(0,i.isTextEditingField)(n)||t||w({preventScroll:e})}),[p,w]),R=(0,o.useCallback)((()=>{f?S():N()}),[f,S,N]),P=c?[]:[d,g.onFocus],k=c?[]:[m,g.onBlur],D=c?[]:[h,R],M=(0,s.createSafeMulticastEventHandler)(...P),F=(0,s.createSafeMulticastEventHandler)(...k),B=(0,s.createSafeMulticastEventHandler)(...D);return{listboxId:x,isOpened:f,isFocused:b,buttonTabIndex:c?-1:u,listboxTabIndex:-1,open:N,close:S,toggle:R,onOpen:y,buttonFocusBindings:{onFocus:M,onBlur:F},onButtonClick:B,buttonRef:C,listboxRef:E,buttonAria:{"aria-controls":f?x:void 0,"aria-expanded":f,"aria-disabled":c}}}},29202:(e,t,n)=>{"use strict";n.d(t,{useFocus:()=>r});var o=n(50959);function r(e,t){const[n,r]=(0,o.useState)(!1);(0,o.useEffect)((()=>{t&&n&&r(!1)}),[t,n]);const i={onFocus:(0,
o.useCallback)((function(t){void 0!==e&&e.current!==t.target||r(!0)}),[e]),onBlur:(0,o.useCallback)((function(t){void 0!==e&&e.current!==t.target||r(!1)}),[e])};return[n,i]}},39416:(e,t,n)=>{"use strict";n.d(t,{useFunctionalRefObject:()=>i});var o=n(50959),r=n(43010);function i(e){const t=(0,o.useMemo)((()=>function(e){const t=n=>{e(n),t.current=n};return t.current=null,t}((e=>{s.current(e)}))),[]),n=(0,o.useRef)(null),i=t=>{if(null===t)return l(n.current,t),void(n.current=null);n.current!==e&&(n.current=e,l(n.current,t))},s=(0,o.useRef)(i);return s.current=i,(0,r.useIsomorphicLayoutEffect)((()=>{if(null!==t.current)return s.current(t.current),()=>s.current(null)}),[e]),t}function l(e,t){null!==e&&("function"==typeof e?e(t):e.current=t)}},38528:(e,t,n)=>{"use strict";n.d(t,{useMergedRefs:()=>i});var o=n(50959),r=n(53017);function i(e){return(0,o.useCallback)((0,r.mergeRefs)(e),e)}},67842:(e,t,n)=>{"use strict";n.d(t,{useResizeObserver:()=>l});var o=n(50959),r=n(43010),i=n(39416);function l(e,t=[]){const{callback:n,ref:l=null}=function(e){return"function"==typeof e?{callback:e}:e}(e),s=(0,o.useRef)(null),a=(0,o.useRef)(n);a.current=n;const c=(0,i.useFunctionalRefObject)(l),u=(0,o.useCallback)((e=>{c(e),null!==s.current&&(s.current.disconnect(),null!==e&&s.current.observe(e))}),[c,s]);return(0,r.useIsomorphicLayoutEffect)((()=>(s.current=new ResizeObserver(((e,t)=>{a.current(e,t)})),c.current&&u(c.current),()=>{var e;null===(e=s.current)||void 0===e||e.disconnect()})),[c,...t]),u}},22064:(e,t,n)=>{"use strict";n.d(t,{createDomId:()=>a,joinDomIds:()=>c});const o=/\s/g;function r(e){return"string"==typeof e}function i(e){switch(typeof e){case"string":return e;case"number":case"bigint":return e.toString(10);case"boolean":case"symbol":return e.toString();default:return null}}function l(e){return e.trim().length>0}function s(e){return e.replace(o,"-")}function a(...e){const t=e.map(i).filter(r).filter(l).map(s);return(t.length>0&&t[0].startsWith("id_")?t:["id",...t]).join("_")}function c(...e){return e.map(i).filter(r).filter(l).join(" ")}},47201:(e,t,n)=>{"use strict";function o(...e){return t=>{for(const n of e)void 0!==n&&n(t)}}n.d(t,{createSafeMulticastEventHandler:()=>o})},24437:(e,t,n)=>{"use strict";n.d(t,{DialogBreakpoints:()=>r});var o=n(53330);const r={SmallHeight:o["small-height-breakpoint"],TabletSmall:o["tablet-small-breakpoint"],TabletNormal:o["tablet-normal-breakpoint"]}},79418:(e,t,n)=>{"use strict";n.d(t,{AdaptivePopupDialog:()=>k});var o=n(50959),r=n(50151),i=n(97754),l=n.n(i),s=n(68335),a=n(63273),c=n(35749),u=n(82206),d=n(1109),m=n(24437),h=n(90692),f=n(95711);var p=n(52092),v=n(76422),g=n(11542),b=n(57340);const x=o.createContext({setHideClose:()=>{}});var C=n(80822);function w(e){const{title:t,titleTextWrap:r=!1,subtitle:i,showCloseIcon:s=!0,onClose:a,onCloseButtonKeyDown:c,renderBefore:u,renderAfter:d,draggable:m,className:h,unsetAlign:f,closeAriaLabel:p=g.t(null,void 0,n(47742)),closeButtonReference:v}=e,[w,E]=(0,o.useState)(!1);return o.createElement(x.Provider,{value:{setHideClose:E}
},o.createElement("div",{className:l()(C.container,h,(i||f)&&C.unsetAlign)},u,o.createElement("div",{"data-dragg-area":m,className:C.title},o.createElement("div",{className:l()(r?C.textWrap:C.ellipsis)},t),i&&o.createElement("div",{className:l()(C.ellipsis,C.subtitle)},i)),d,s&&!w&&o.createElement(b.CloseButton,{className:C.close,"data-name":"close","aria-label":p,onClick:a,onKeyDown:c,ref:v,size:"medium",preservePaddings:!0})))}var E=n(53017),y=n(90186),N=n(56570),S=n(8473);const R={vertical:20},P={vertical:0};class k extends o.PureComponent{constructor(){super(...arguments),this._controller=null,this._reference=null,this._orientationMediaQuery=null,this._embedResizerOverridesEnabled=N.enabled("embed_resizer_overrides"),this._renderChildren=(e,t)=>(this._controller=e,this.props.render({requestResize:this._requestResize,centerAndFit:this._centerAndFit,isSmallWidth:t})),this._handleReference=e=>this._reference=e,this._handleCloseBtnClick=()=>{this.props.onKeyboardClose&&this.props.onKeyboardClose(),this._handleClose()},this._handleClose=()=>{this.props.onClose()},this._handleOpen=()=>{void 0!==this.props.onOpen&&this.props.isOpened&&this.props.onOpen(this.props.fullScreen||window.matchMedia(m.DialogBreakpoints.TabletSmall).matches)},this._handleKeyDown=e=>{if(!e.defaultPrevented){if(this.props.onKeyDown&&this.props.onKeyDown(e),27===(0,s.hashFromEvent)(e)){if(e.defaultPrevented)return;if(this.props.forceCloseOnEsc&&this.props.forceCloseOnEsc())return this.props.onKeyboardClose&&this.props.onKeyboardClose(),void this._handleClose();const{activeElement:n}=document;if(null!==n){if(e.preventDefault(),"true"===(t=n).getAttribute("data-haspopup")&&"true"!==t.getAttribute("data-expanded"))return void this._handleClose();const o=this._reference;if(null!==o&&(0,c.isTextEditingField)(n))return void o.focus();if(null==o?void 0:o.contains(n))return this.props.onKeyboardClose&&this.props.onKeyboardClose(),void this._handleClose()}}var t,n;(function(e){if("function"==typeof e)return e();return Boolean(e)})(this.props.disableTabNavigationContainment)||(n=e,[9,s.Modifiers.Shift+9].includes((0,s.hashFromEvent)(n))&&n.stopPropagation())}},this._requestResize=()=>{null!==this._controller&&this._controller.recalculateBounds()},this._centerAndFit=()=>{null!==this._controller&&this._controller.centerAndFit()},this._calculatePositionWithOffsets=(e,t)=>{const n=(0,r.ensureDefined)(this.props.fullScreenViewOffsets).value();return{top:n.top,left:(0,a.isRtl)()?-n.right:n.left,width:t.clientWidth-n.left-n.right,height:t.clientHeight-n.top-n.bottom}}}componentDidMount(){this.props.ignoreClosePopupsAndDialog||v.subscribe(p.CLOSE_POPUPS_AND_DIALOGS_COMMAND,this._handleClose,null),this._handleOpen(),void 0!==this.props.onOpen&&(this._orientationMediaQuery=window.matchMedia("(orientation: portrait)"),this._orientationMediaQuery.addEventListener("change",this._handleOpen)),this.props.fullScreenViewOffsets&&this.props.fullScreen&&this.props.fullScreenViewOffsets.subscribe(this._requestResize)}componentWillUnmount(){
this.props.ignoreClosePopupsAndDialog||v.unsubscribe(p.CLOSE_POPUPS_AND_DIALOGS_COMMAND,this._handleClose,null),null!==this._orientationMediaQuery&&this._orientationMediaQuery.removeEventListener("change",this._handleOpen),this.props.fullScreenViewOffsets&&this.props.fullScreen&&this.props.fullScreenViewOffsets.unsubscribe(this._requestResize)}focus(){(0,r.ensureNotNull)(this._reference).focus()}getElement(){return this._reference}contains(e){var t,n;return null!==(n=null===(t=this._reference)||void 0===t?void 0:t.contains(e))&&void 0!==n&&n}render(){const{className:e,wrapperClassName:t,headerClassName:n,isOpened:r,title:i,titleTextWrap:s,dataName:a,onClickOutside:c,additionalElementPos:p,additionalHeaderElement:v,backdrop:g,shouldForceFocus:b=!0,shouldReturnFocus:x,onForceFocus:C,showSeparator:N,subtitle:k,draggable:D=!0,fullScreen:M=!1,showCloseIcon:F=!0,rounded:B=!0,isAnimationEnabled:L,growPoint:z,dialogTooltip:O,unsetHeaderAlign:A,onDragStart:I,dataDialogName:W,closeAriaLabel:T,containerAriaLabel:_,reference:H,containerTabIndex:K,closeButtonReference:j,onCloseButtonKeyDown:Z,shadowed:Q,fullScreenViewOffsets:V,fixedBody:q,onClick:X}=this.props,G="after"!==p?v:void 0,Y="after"===p?v:void 0,J="string"==typeof i?i:W||"",$=(0,y.filterDataProps)(this.props),U=(0,E.mergeRefs)([this._handleReference,H]);return o.createElement(h.MatchMedia,{rule:m.DialogBreakpoints.SmallHeight},(p=>o.createElement(h.MatchMedia,{rule:m.DialogBreakpoints.TabletSmall},(m=>o.createElement(u.PopupDialog,{rounded:!(m||M)&&B,className:l()(S.dialog,M&&V&&S.bounded,e),isOpened:r,reference:U,onKeyDown:this._handleKeyDown,onClickOutside:c,onClickBackdrop:c,fullscreen:m||M,guard:p?P:R,boundByScreen:m||M,shouldForceFocus:b,onForceFocus:C,shouldReturnFocus:x,backdrop:g,draggable:D,isAnimationEnabled:L,growPoint:z,name:this.props.dataName,dialogTooltip:O,onDragStart:I,containerAriaLabel:_,containerTabIndex:K,calculateDialogPosition:M&&V?this._calculatePositionWithOffsets:void 0,shadowed:Q,fixedBody:q,onClick:X,...$},o.createElement("div",{className:l()(S.wrapper,t),"data-name":a,"data-dialog-name":J},void 0!==i&&o.createElement(w,{draggable:D&&!(m||M),onClose:this._handleCloseBtnClick,renderAfter:Y,renderBefore:G,subtitle:k,title:i,titleTextWrap:s,showCloseIcon:F,className:n,unsetAlign:A,closeAriaLabel:T,closeButtonReference:j,onCloseButtonKeyDown:Z}),N&&o.createElement(d.Separator,{className:S.separator}),o.createElement(f.PopupContext.Consumer,null,(e=>this._renderChildren(e,m||M)))))))))}}},64530:(e,t,n)=>{"use strict";n.d(t,{DialogContentItem:()=>m});var o=n(50959),r=n(97754),i=n.n(r),l=n(49483),s=n(36189),a=n(96040);function c(e){const{url:t,...n}=e;return t?o.createElement("a",{...n,href:t}):o.createElement("div",{...n})}var u=n(60925),d=n(4914);function m(e){const{title:t,subtitle:n,removeBtnLabel:r,onClick:m,onClickFavorite:f,onClickRemove:p,isActive:v,isSelected:g,isFavorite:b,isMobile:x=!1,showFavorite:C=!0,className:w,...E}=e;return o.createElement(c,{...E,className:i()(d.itemRow,v&&!g&&d.active,x&&d.mobile,g&&d.selected,w),
onClick:h.bind(null,m),"data-role":"list-item","data-active":v},C&&f&&o.createElement(s.FavoriteButton,{className:i()(d.favoriteButton,b&&d.favorite,l.CheckMobile.any()&&d.mobile),isActive:v&&!g,isFilled:b,onClick:h.bind(null,f),"data-name":"list-item-favorite-button","data-favorite":b}),o.createElement("div",{className:i()(d.itemInfo,!C&&d.itemInfoWithPadding)},o.createElement("div",{className:i()(d.title,v&&!g&&d.active,x&&d.mobile),"data-name":"list-item-title"},t),o.createElement("div",{className:i()(d.details,v&&!g&&d.active,x&&d.mobile)},n)),o.createElement(a.RemoveButton,{className:d.removeButton,isActive:v&&!g,onClick:h.bind(null,p),"data-name":"list-item-remove-button",title:r,icon:u}))}function h(e,t){t.defaultPrevented||(t.preventDefault(),e(t))}},69654:(e,t,n)=>{"use strict";n.d(t,{DialogSearch:()=>d});var o=n(50959),r=n(97754),i=n.n(r),l=n(11542),s=n(9745),a=n(69859),c=n(54313),u=n(59086);function d(e){const{children:t,isMobile:r,renderInput:d,onCancel:h,containerClassName:f,inputContainerClassName:p,iconClassName:v,cancelTitle:g=l.t(null,void 0,n(4543)),...b}=e;return o.createElement("div",{className:i()(u.container,r&&u.mobile,f)},o.createElement("div",{className:i()(u.inputContainer,r&&u.mobile,p,h&&u.withCancel)},d||o.createElement(m,{isMobile:r,...b})),t,o.createElement(s.Icon,{className:i()(u.icon,r&&u.mobile,v),icon:r?c:a}),h&&(!r||""!==b.value)&&o.createElement("div",{className:i()(u.cancel,r&&u.mobile),onClick:h},g))}function m(e){const{className:t,reference:n,isMobile:r,value:l,onChange:s,onFocus:a,onBlur:c,onKeyDown:d,onSelect:m,placeholder:h,activeDescendant:f,...p}=e;return o.createElement("input",{...p,ref:n,type:"text",className:i()(t,u.input,r&&u.mobile),autoComplete:"off","data-role":"search",placeholder:h,value:l,onChange:s,onFocus:a,onBlur:c,onSelect:m,onKeyDown:d,"aria-activedescendant":f})}},3085:(e,t,n)=>{"use strict";n.d(t,{OverlayScrollContainer:()=>v});var o=n(50959),r=n(97754),i=n.n(r),l=n(63273),s=n(50151),a=n(9859);const c=n(60591);var u;!function(e){e[e.Vertical=0]="Vertical",e[e.Horizontal=1]="Horizontal",e[e.HorizontalRtl=2]="HorizontalRtl"}(u||(u={}));const d={0:{isHorizontal:!1,isNegative:!1,sizePropName:"height",minSizePropName:"minHeight",startPointPropName:"top",currentMousePointPropName:"clientY",progressBarTransform:"translateY"},1:{isHorizontal:!0,isNegative:!1,sizePropName:"width",minSizePropName:"minWidth",startPointPropName:"left",currentMousePointPropName:"clientX",progressBarTransform:"translateX"},2:{isHorizontal:!0,isNegative:!0,sizePropName:"width",minSizePropName:"minWidth",startPointPropName:"right",currentMousePointPropName:"clientX",progressBarTransform:"translateX"}},m=40;function h(e){const{size:t,scrollSize:n,clientSize:r,scrollProgress:l,onScrollProgressChange:u,scrollMode:h,theme:f=c,onDragStart:p,onDragEnd:v,minBarSize:g=m}=e,b=(0,o.useRef)(null),x=(0,o.useRef)(null),[C,w]=(0,o.useState)(!1),E=(0,o.useRef)(0),{isHorizontal:y,isNegative:N,sizePropName:S,minSizePropName:R,startPointPropName:P,currentMousePointPropName:k,progressBarTransform:D}=d[h]
;(0,o.useEffect)((()=>{const e=(0,s.ensureNotNull)(b.current).ownerDocument;return C?(p&&p(),e&&(e.addEventListener("mousemove",W),e.addEventListener("mouseup",T))):v&&v(),()=>{e&&(e.removeEventListener("mousemove",W),e.removeEventListener("mouseup",T))}}),[C]);const M=t/n||0,F=r*M||0,B=Math.max(F,g),L=(t-B)/(t-F),z=n-t,O=N?-z:0,A=N?0:z,I=H((0,a.clamp)(l,O,A))||0;return o.createElement("div",{ref:b,className:i()(f.wrap,y&&f["wrap--horizontal"]),style:{[S]:t},onMouseDown:function(e){if(e.isDefaultPrevented())return;e.preventDefault();const t=_(e.nativeEvent,(0,s.ensureNotNull)(b.current)),n=Math.sign(t),o=(0,s.ensureNotNull)(x.current).getBoundingClientRect();E.current=n*o[S]/2;let r=Math.abs(t)-Math.abs(E.current);const i=H(z);r<0?(r=0,E.current=t):r>i&&(r=i,E.current=t-n*i);u(K(n*r)),w(!0)}},o.createElement("div",{ref:x,className:i()(f.bar,y&&f["bar--horizontal"]),style:{[R]:g,[S]:B,transform:`${D}(${I}px)`},onMouseDown:function(e){e.preventDefault(),E.current=_(e.nativeEvent,(0,s.ensureNotNull)(x.current)),w(!0)}},o.createElement("div",{className:i()(f.barInner,y&&f["barInner--horizontal"])})));function W(e){const t=_(e,(0,s.ensureNotNull)(b.current))-E.current;u(K(t))}function T(){w(!1)}function _(e,t){const n=t.getBoundingClientRect()[P];return e[k]-n}function H(e){return e*M*L}function K(e){return e/M/L}}var f=n(70412),p=n(91504);function v(e){const{reference:t,className:n,containerHeight:i=0,containerWidth:s=0,contentHeight:a=0,contentWidth:c=0,scrollPosTop:u=0,scrollPosLeft:d=0,onVerticalChange:m,onHorizontalChange:v,visible:g}=e,[b,x]=(0,f.useHover)(),[C,w]=(0,o.useState)(!1),E=i<a,y=s<c,N=E&&y?8:0;return o.createElement("div",{...x,ref:t,className:r(n,p.scrollWrap),style:{visibility:g||b||C?"visible":"hidden"}},E&&o.createElement(h,{size:i-N,scrollSize:a-N,clientSize:i-N,scrollProgress:u,onScrollProgressChange:function(e){m&&m(e)},onDragStart:S,onDragEnd:R,scrollMode:0}),y&&o.createElement(h,{size:s-N,scrollSize:c-N,clientSize:s-N,scrollProgress:d,onScrollProgressChange:function(e){v&&v(e)},onDragStart:S,onDragEnd:R,scrollMode:(0,l.isRtl)()?2:1}));function S(){w(!0)}function R(){w(!1)}}},50238:(e,t,n)=>{"use strict";n.d(t,{useRovingTabindexElement:()=>i});var o=n(50959),r=n(39416);function i(e,t=[]){const[n,i]=(0,o.useState)(!1),l=(0,r.useFunctionalRefObject)(e);return(0,o.useLayoutEffect)((()=>{const e=l.current;if(null===e)return;const t=e=>{switch(e.type){case"roving-tabindex:main-element":i(!0);break;case"roving-tabindex:secondary-element":i(!1)}};return e.addEventListener("roving-tabindex:main-element",t),e.addEventListener("roving-tabindex:secondary-element",t),()=>{e.removeEventListener("roving-tabindex:main-element",t),e.removeEventListener("roving-tabindex:secondary-element",t)}}),t),[l,n?0:-1]}},59695:(e,t,n)=>{"use strict";n.d(t,{CircleLogo:()=>s,hiddenCircleLogoClass:()=>l});var o=n(50959),r=n(53885),i=n(3196);const l=n.n(i)()["tv-circle-logo--visually-hidden"];function s(e){var t,n;const i=(0,
r.getStyleClasses)(e.size,e.className),l=null!==(n=null!==(t=e.alt)&&void 0!==t?t:e.title)&&void 0!==n?n:"";return(0,r.isCircleLogoWithUrlProps)(e)?o.createElement("img",{className:i,crossOrigin:"",src:e.logoUrl,alt:l,title:e.title,loading:e.loading,"aria-label":e["aria-label"],"aria-hidden":e["aria-hidden"]}):o.createElement("span",{className:i,title:e.title,"aria-label":e["aria-label"],"aria-hidden":e["aria-hidden"]},e.placeholderLetter)}},59054:(e,t,n)=>{"use strict";n.d(t,{ControlDisclosureView:()=>v});var o=n(50959),r=n(97754),i=n.n(r),l=n(38528),s=n(67029),a=n(78274),c=n(4523),u=n(9745),d=n(2948),m=n(40352);function h(e){const{isDropped:t}=e;return o.createElement(u.Icon,{className:i()(m.icon,t&&m.dropped),icon:d})}function f(e){const{className:t,disabled:n,isDropped:r}=e;return o.createElement("span",{className:i()(m.button,n&&m.disabled,t)},o.createElement(h,{isDropped:r}))}var p=n(44620);const v=o.forwardRef(((e,t)=>{const{listboxId:n,className:r,listboxClassName:u,listboxTabIndex:d,hideArrowButton:m,matchButtonAndListboxWidths:h,popupPosition:v,disabled:g,isOpened:b,scrollWrapReference:x,repositionOnScroll:C,closeOnHeaderOverlap:w,listboxReference:E,size:y="small",onClose:N,onOpen:S,onListboxFocus:R,onListboxBlur:P,onListboxKeyDown:k,buttonChildren:D,children:M,caretClassName:F,buttonContainerClassName:B,listboxAria:L,...z}=e,O=(0,o.useRef)(null),A=!m&&o.createElement(a.EndSlot,null,o.createElement(f,{isDropped:b,disabled:g,className:F}));return o.createElement(c.PopupMenuDisclosureView,{buttonRef:O,listboxId:n,listboxClassName:u,listboxTabIndex:d,isOpened:b,onClose:N,onOpen:S,listboxReference:E,scrollWrapReference:x,onListboxFocus:R,onListboxBlur:P,onListboxKeyDown:k,listboxAria:L,matchButtonAndListboxWidths:h,popupPosition:v,button:o.createElement(s.ControlSkeleton,{...z,"data-role":"listbox",disabled:g,className:i()(p.button,r),size:y,ref:(0,l.useMergedRefs)([O,t]),middleSlot:o.createElement(a.MiddleSlot,null,o.createElement("span",{className:i()(p["button-children"],m&&p.hiddenArrow,B)},D)),endSlot:A}),popupChildren:M,repositionOnScroll:C,closeOnHeaderOverlap:w})}));v.displayName="ControlDisclosureView"},95276:(e,t,n)=>{"use strict";n.d(t,{ControlDisclosure:()=>u});var o=n(50959),r=n(38528),i=n(26597),l=n(59054),s=n(36104),a=n(68335),c=n(44620);const u=o.forwardRef(((e,t)=>{const{id:n,tabIndex:u,disabled:d,highlight:m,intent:h,children:f,onClick:p,onFocus:v,onBlur:g,listboxAria:b,onListboxKeyDown:x,...C}=e,w=(0,o.useRef)({"aria-labelledby":n}),{listboxId:E,isOpened:y,isFocused:N,buttonTabIndex:S,listboxTabIndex:R,highlight:P,intent:k,onOpen:D,close:M,toggle:F,buttonFocusBindings:B,onButtonClick:L,buttonRef:z,listboxRef:O,buttonAria:A}=(0,s.useControlDisclosure)({id:n,disabled:d,buttonTabIndex:u,intent:h,highlight:m,onFocus:v,onBlur:g,onClick:p}),I=(0,i.useKeyboardToggle)(F),W=(0,i.useKeyboardClose)(y,M),T=(0,i.useKeyboardEventHandler)([I,W]);return o.createElement(l.ControlDisclosureView,{...C,...B,...A,id:n,role:"button",tabIndex:S,disabled:d,isOpened:y,isFocused:N,ref:(0,r.useMergedRefs)([z,t]),
highlight:P,intent:k,onClose:M,onOpen:D,onClick:L,onKeyDown:T,listboxId:E,listboxTabIndex:R,listboxReference:O,listboxAria:null!=b?b:w.current,onListboxKeyDown:function(e){if(27===(0,a.hashFromEvent)(e))return e.preventDefault(),void M();null==x||x(e)}},f,o.createElement("span",{className:c.invisibleFocusHandler,tabIndex:0,"aria-hidden":!0,onFocus:()=>M()}))}));u.displayName="ControlDisclosure"},4523:(e,t,n)=>{"use strict";n.d(t,{PopupMenuDisclosureView:()=>u});var o=n(50959),r=n(20520),i=n(50151);const l={x:0,y:0};function s(e,t,n){return(0,o.useCallback)((()=>function(e,t,{x:n=l.x,y:o=l.y}=l){const r=(0,i.ensureNotNull)(e).getBoundingClientRect(),s={x:r.left+n,y:r.top+r.height+o,indentFromWindow:{top:4,bottom:4,left:4,right:4}};return t&&(s.overrideWidth=r.width),s}(e.current,t,n)),[e,t])}var a=n(86240);const c=parseInt(a["size-header-height"]);function u(e){const{button:t,popupChildren:n,buttonRef:i,listboxId:l,listboxClassName:a,listboxTabIndex:u,matchButtonAndListboxWidths:d,isOpened:m,scrollWrapReference:h,listboxReference:f,onClose:p,onOpen:v,onListboxFocus:g,onListboxBlur:b,onListboxKeyDown:x,listboxAria:C,repositionOnScroll:w=!0,closeOnHeaderOverlap:E=!1,popupPositionCorrection:y={x:0,y:0},popupPosition:N}=e,S=s(i,d,y),R=E?c:0;return o.createElement(o.Fragment,null,t,o.createElement(r.PopupMenu,{...C,id:l,className:a,tabIndex:u,isOpened:m,position:N||S,repositionOnScroll:w,onClose:p,onOpen:v,doNotCloseOn:i.current,reference:f,scrollWrapReference:h,onFocus:g,onBlur:b,onKeyDown:x,closeOnScrollOutsideOffset:R},n))}},36189:(e,t,n)=>{"use strict";n.d(t,{FavoriteButton:()=>d});var o=n(11542),r=n(50959),i=n(97754),l=n(9745),s=n(39146),a=n(48010),c=n(98992);const u={add:o.t(null,void 0,n(69207)),remove:o.t(null,void 0,n(85106))};function d(e){const{className:t,isFilled:n,isActive:o,onClick:d,...m}=e;return r.createElement(l.Icon,{...m,className:i(c.favorite,"apply-common-tooltip",n&&c.checked,o&&c.active,t),icon:n?s:a,onClick:d,title:n?u.remove:u.add})}},19785:(e,t,n)=>{"use strict";n.d(t,{createRegExpList:()=>l,getHighlightedChars:()=>s,rankedSearch:()=>i});var o,r=n(37265);function i(e){const{data:t,rules:n,queryString:o,isPreventedFromFiltering:i,primaryKey:l,secondaryKey:s=l,optionalPrimaryKey:a,tertiaryKey:c}=e;return t.map((e=>{const t=a&&e[a]?e[a]:e[l],i=e[s],u=c&&e[c];let d,m=0;return n.forEach((e=>{var n,l,s,a,c;const{re:h,fullMatch:f}=e;if(h.lastIndex=0,(0,r.isString)(t)&&t&&t.toLowerCase()===o.toLowerCase())return m=4,void(d=null===(n=t.match(f))||void 0===n?void 0:n.index);if((0,r.isString)(t)&&f.test(t))return m=3,void(d=null===(l=t.match(f))||void 0===l?void 0:l.index);if((0,r.isString)(i)&&f.test(i))return m=2,void(d=null===(s=i.match(f))||void 0===s?void 0:s.index);if((0,r.isString)(i)&&h.test(i))return m=2,void(d=null===(a=i.match(h))||void 0===a?void 0:a.index);if(Array.isArray(u))for(const e of u)if(f.test(e))return m=1,void(d=null===(c=e.match(f))||void 0===c?void 0:c.index)})),{matchPriority:m,matchIndex:d,item:e}})).filter((e=>i||e.matchPriority)).sort(((e,t)=>{
if(e.matchPriority<t.matchPriority)return 1;if(e.matchPriority>t.matchPriority)return-1;if(e.matchPriority===t.matchPriority){if(void 0===e.matchIndex||void 0===t.matchIndex)return 0;if(e.matchIndex>t.matchIndex)return 1;if(e.matchIndex<t.matchIndex)return-1}return 0})).map((({item:e})=>e))}function l(e,t){const n=[],o=e.toLowerCase(),r=e.split("").map(((e,t)=>`(${0!==t?`[/\\s-]${a(e)}`:a(e)})`)).join("(.*?)")+"(.*)";return n.push({fullMatch:new RegExp(`(${a(e)})`,"i"),re:new RegExp(`^${r}`,"i"),reserveRe:new RegExp(r,"i"),fuzzyHighlight:!0}),t&&t.hasOwnProperty(o)&&n.push({fullMatch:t[o],re:t[o],fuzzyHighlight:!1}),n}function s(e,t,n){const o=[];return e&&n?(n.forEach((e=>{const{fullMatch:n,re:r,reserveRe:i}=e;n.lastIndex=0,r.lastIndex=0;const l=n.exec(t),s=l||r.exec(t)||i&&i.exec(t);if(e.fuzzyHighlight=!l,s)if(e.fuzzyHighlight){let e=s.index;for(let t=1;t<s.length;t++){const n=s[t],r=s[t].length;if(t%2){const t=n.startsWith(" ")||n.startsWith("/")||n.startsWith("-");o[t?e+1:e]=!0}e+=r}}else for(let e=0;e<s[0].length;e++)o[s.index+e]=!0})),o):o}function a(e){return e.replace(/[!-/[-^{-}?]/g,"\\$&")}!function(e){e[e.Low=0]="Low",e[e.MediumLow=1]="MediumLow",e[e.Medium=2]="Medium",e[e.High=3]="High",e[e.Highest=4]="Highest"}(o||(o={}))},24637:(e,t,n)=>{"use strict";n.d(t,{HighlightedText:()=>s});var o=n(50959),r=n(97754),i=n(19785),l=n(32248);function s(e){const{queryString:t,rules:n,text:s,className:a}=e,c=(0,o.useMemo)((()=>(0,i.getHighlightedChars)(t,s,n)),[t,n,s]);return o.createElement(o.Fragment,null,c.length?s.split("").map(((e,t)=>o.createElement(o.Fragment,{key:t},c[t]?o.createElement("span",{className:r(l.highlighted,a)},e):o.createElement("span",null,e)))):s)}},26597:(e,t,n)=>{"use strict";n.d(t,{useKeyboardActionHandler:()=>o.useKeyboardActionHandler,useKeyboardClose:()=>o.useKeyboardClose,useKeyboardEventHandler:()=>o.useKeyboardEventHandler,useKeyboardOpen:()=>o.useKeyboardOpen,useKeyboardToggle:()=>o.useKeyboardToggle});var o=n(66686)},898:(e,t,n)=>{"use strict";n.d(t,{useDimensions:()=>i});var o=n(50959),r=n(67842);function i(e){const[t,n]=(0,o.useState)(null),i=(0,o.useCallback)((([e])=>{const o=e.target.getBoundingClientRect();o.width===(null==t?void 0:t.width)&&o.height===t.height||n(o)}),[t]);return[(0,r.useResizeObserver)({callback:i,ref:e}),t]}},70412:(e,t,n)=>{"use strict";n.d(t,{hoverMouseEventFilter:()=>i,useAccurateHover:()=>l,useHover:()=>r});var o=n(50959);function r(){const[e,t]=(0,o.useState)(!1);return[e,{onMouseOver:function(e){i(e)&&t(!0)},onMouseOut:function(e){i(e)&&t(!1)}}]}function i(e){return!e.currentTarget.contains(e.relatedTarget)}function l(e){const[t,n]=(0,o.useState)(!1);return(0,o.useEffect)((()=>{const t=t=>{if(null===e.current)return;const o=e.current.contains(t.target);n(o)};return document.addEventListener("mouseover",t),()=>document.removeEventListener("mouseover",t)}),[]),t}},33127:(e,t,n)=>{"use strict";n.d(t,{useOverlayScroll:()=>a});var o=n(50959),r=n(50151),i=n(70412),l=n(49483);const s={onMouseOver:()=>{},onMouseOut:()=>{}};function a(e,t=l.CheckMobile.any()){
const n=(0,o.useRef)(null),a=e||(0,o.useRef)(null),[c,u]=(0,i.useHover)(),[d,m]=(0,o.useState)({reference:n,containerHeight:0,containerWidth:0,contentHeight:0,contentWidth:0,scrollPosTop:0,scrollPosLeft:0,onVerticalChange:function(e){m((t=>({...t,scrollPosTop:e}))),(0,r.ensureNotNull)(a.current).scrollTop=e},onHorizontalChange:function(e){m((t=>({...t,scrollPosLeft:e}))),(0,r.ensureNotNull)(a.current).scrollLeft=e},visible:c}),h=(0,o.useCallback)((()=>{if(!a.current)return;const{clientHeight:e,scrollHeight:t,scrollTop:o,clientWidth:r,scrollWidth:i,scrollLeft:l}=a.current,s=n.current?n.current.offsetTop:0;m((n=>({...n,containerHeight:e-s,contentHeight:t-s,scrollPosTop:o,containerWidth:r,contentWidth:i,scrollPosLeft:l})))}),[]);function f(){m((e=>({...e,scrollPosTop:(0,r.ensureNotNull)(a.current).scrollTop,scrollPosLeft:(0,r.ensureNotNull)(a.current).scrollLeft})))}return(0,o.useEffect)((()=>{c&&h(),m((e=>({...e,visible:c})))}),[c]),(0,o.useEffect)((()=>{const e=a.current;return e&&e.addEventListener("scroll",f),()=>{e&&e.removeEventListener("scroll",f)}}),[a]),[d,t?s:u,a,h]}},77975:(e,t,n)=>{"use strict";n.d(t,{useWatchedValueReadonly:()=>r});var o=n(50959);const r=(e,t=!1)=>{const n="watchedValue"in e?e.watchedValue:void 0,r="defaultValue"in e?e.defaultValue:e.watchedValue.value(),[i,l]=(0,o.useState)(n?n.value():r);return(t?o.useLayoutEffect:o.useEffect)((()=>{if(n){l(n.value());const e=e=>l(e);return n.subscribe(e),()=>n.unsubscribe(e)}return()=>{}}),[n]),i}},1109:(e,t,n)=>{"use strict";n.d(t,{Separator:()=>l});var o=n(50959),r=n(97754),i=n(47625);function l(e){return o.createElement("div",{className:r(i.separator,e.className)})}},16396:(e,t,n)=>{"use strict";n.d(t,{DEFAULT_POPUP_MENU_ITEM_THEME:()=>u,PopupMenuItem:()=>m});var o=n(50959),r=n(97754),i=n(51768),l=n(59064),s=n(59695),a=n(76460),c=n(2908);const u=c;function d(e){e.stopPropagation()}function m(e){const{id:t,role:n,className:u,title:m,labelRowClassName:h,labelClassName:f,toolboxClassName:p,shortcut:v,forceShowShortcuts:g,icon:b,iconClassname:x,isActive:C,isDisabled:w,isHovered:E,appearAsDisabled:y,label:N,link:S,showToolboxOnHover:R,showToolboxOnFocus:P,target:k,rel:D,toolbox:M,reference:F,onMouseOut:B,onMouseOver:L,onKeyDown:z,suppressToolboxClick:O=!0,theme:A=c,tabIndex:I,tagName:W,renderComponent:T,roundedIcon:_,iconAriaProps:H,circleLogo:K,dontClosePopup:j,onClick:Z,onClickArg:Q,trackEventObject:V,trackMouseWheelClick:q,trackRightClick:X,...G}=e,Y=(0,o.useRef)(null),J=(0,o.useMemo)((()=>function(e){function t(t){const{reference:n,...r}=t,i=null!=e?e:r.href?"a":"div",l="a"===i?r:function(e){const{download:t,href:n,hrefLang:o,media:r,ping:i,rel:l,target:s,type:a,referrerPolicy:c,...u}=e;return u}(r);return o.createElement(i,{...l,ref:n})}return t.displayName=`DefaultComponent(${e})`,t}(W)),[W]),$=null!=T?T:J;return o.createElement($,{...G,id:t,role:n,className:r(u,A.item,b&&A.withIcon,{[A.isActive]:C,[A.isDisabled]:w||y,[A.hovered]:E}),title:m,href:S,target:k,rel:D,reference:function(e){Y.current=e,"function"==typeof F&&F(e)
;"object"==typeof F&&(F.current=e)},onClick:function(e){if(w)return;V&&(0,i.trackEvent)(V.category,V.event,V.label);Z&&Z(Q,e);j||(e.currentTarget.dispatchEvent(new CustomEvent("popup-menu-close-event",{bubbles:!0,detail:{clickType:(0,a.isKeyboardClick)(e)?"keyboard":"mouse"}})),(0,l.globalCloseMenu)())},onContextMenu:function(e){V&&X&&(0,i.trackEvent)(V.category,V.event,`${V.label}_rightClick`)},onMouseUp:function(e){if(1===e.button&&S&&V){let e=V.label;q&&(e+="_mouseWheelClick"),(0,i.trackEvent)(V.category,V.event,e)}},onMouseOver:L,onMouseOut:B,onKeyDown:z,tabIndex:I},K&&o.createElement(s.CircleLogo,{...H,className:c["disclosure-item-circle-logo"],size:"xxxsmall",logoUrl:K.logoUrl,placeholderLetter:"placeholderLetter"in K?K.placeholderLetter:void 0}),b&&o.createElement("span",{"aria-label":H&&H["aria-label"],"aria-hidden":H&&Boolean(H["aria-hidden"]),className:r(A.icon,_&&c["round-icon"],x),dangerouslySetInnerHTML:{__html:b}}),o.createElement("span",{className:r(A.labelRow,h)},o.createElement("span",{className:r(A.label,f)},N)),(void 0!==v||g)&&o.createElement("span",{className:A.shortcut},(U=v)&&U.split("+").join(" + ")),void 0!==M&&o.createElement("span",{onClick:O?d:void 0,className:r(p,A.toolbox,{[A.showOnHover]:R,[A.showOnFocus]:P})},M));var U}},96040:(e,t,n)=>{"use strict";n.d(t,{RemoveButton:()=>c});var o=n(11542),r=n(50959),i=n(97754),l=n(9745),s=n(33765),a=n(11772);function c(e){const{className:t,isActive:c,onClick:u,onMouseDown:d,title:m,hidden:h,"data-name":f="remove-button",icon:p,...v}=e;return r.createElement(l.Icon,{...v,"data-name":f,className:i(a.button,"apply-common-tooltip",c&&a.active,h&&a.hidden,t),icon:p||s,onClick:u,onMouseDown:d,title:m||o.t(null,void 0,n(67410))})}},4237:(e,t,n)=>{"use strict";var o=n(32227);t.createRoot=o.createRoot,o.hydrateRoot},19347:e=>{e.exports={accessible:"accessible-NQERJsv9",active:"active-NQERJsv9"}},92821:e=>{e.exports={highlight:"highlight-6tu1aYjZ",active:"active-6tu1aYjZ"}},39166:e=>{e.exports={dialog:"dialog-T4Q8BJPb",contentList:"contentList-T4Q8BJPb",contentHeader:"contentHeader-T4Q8BJPb"}},20243:(e,t,n)=>{"use strict";n.d(t,{focusFirstMenuItem:()=>c,handleAccessibleMenuFocus:()=>s,handleAccessibleMenuKeyDown:()=>a,queryMenuElements:()=>m});var o=n(19291),r=n(57177),i=n(68335);const l=[37,39,38,40];function s(e,t){var n;if(!e.target)return;const o=null===(n=e.relatedTarget)||void 0===n?void 0:n.getAttribute("aria-activedescendant");if(e.relatedTarget!==t.current){const e=o&&document.getElementById(o);if(!e||e!==t.current)return}c(e.target)}function a(e){var t;if(e.defaultPrevented)return;const n=(0,i.hashFromEvent)(e);if(!l.includes(n))return;const s=document.activeElement;if(!(document.activeElement instanceof HTMLElement))return;const a=m(e.currentTarget).sort(o.navigationOrderComparator);if(0===a.length)return;const c=document.activeElement.closest('[data-role="menuitem"]')||(null===(t=document.activeElement.parentElement)||void 0===t?void 0:t.querySelector('[data-role="menuitem"]'));if(!(c instanceof HTMLElement))return;const f=a.indexOf(c)
;if(-1===f)return;const p=h(c),v=p.indexOf(document.activeElement),g=-1!==v,b=e=>{s&&(0,r.becomeSecondaryElement)(s),(0,r.becomeMainElement)(e),e.focus()};switch((0,o.mapKeyCodeToDirection)(n)){case"inlinePrev":if(!p.length)return;e.preventDefault(),b(0===v?a[f]:g?u(p,v,-1):p[p.length-1]);break;case"inlineNext":if(!p.length)return;e.preventDefault(),v===p.length-1?b(a[f]):b(g?u(p,v,1):p[0]);break;case"blockPrev":{e.preventDefault();const t=u(a,f,-1);if(g){const e=d(t,v);b(e||t);break}b(t);break}case"blockNext":{e.preventDefault();const t=u(a,f,1);if(g){const e=d(t,v);b(e||t);break}b(t)}}}function c(e){const[t]=m(e);t&&((0,r.becomeMainElement)(t),t.focus())}function u(e,t,n){return e[(t+e.length+n)%e.length]}function d(e,t){const n=h(e);return n.length?n[(t+n.length)%n.length]:null}function m(e){return Array.from(e.querySelectorAll('[data-role="menuitem"]:not([disabled]):not([aria-disabled])')).filter((0,o.createScopedVisibleElementFilter)(e))}function h(e){return Array.from(e.querySelectorAll("[tabindex]:not([disabled]):not([aria-disabled])")).filter((0,o.createScopedVisibleElementFilter)(e))}},57177:(e,t,n)=>{"use strict";var o;function r(e){e.dispatchEvent(new CustomEvent("roving-tabindex:main-element"))}function i(e){e.dispatchEvent(new CustomEvent("roving-tabindex:secondary-element"))}n.d(t,{becomeMainElement:()=>r,becomeSecondaryElement:()=>i}),function(e){e.MainElement="roving-tabindex:main-element",e.SecondaryElement="roving-tabindex:secondary-element"}(o||(o={}))},10838:(e,t,n)=>{"use strict";n.d(t,{AccessibleMenuItem:()=>u});var o=n(50959),r=n(97754),i=n.n(r),l=n(3343),s=n(50238),a=n(16396),c=n(19347);function u(e){const{className:t,...n}=e,[r,u]=(0,s.useRovingTabindexElement)(null);return o.createElement(a.PopupMenuItem,{...n,className:i()(c.accessible,e.isActive&&c.active,t),reference:r,tabIndex:u,onKeyDown:function(e){if(e.target!==e.currentTarget)return;const t=(0,l.hashFromEvent)(e);13!==t&&32!==t||(e.preventDefault(),r.current instanceof HTMLElement&&r.current.click())},"data-role":"menuitem","aria-disabled":e.isDisabled||void 0})}},27830:(e,t,n)=>{"use strict";n.d(t,{showFavoriteLayouts:()=>o});const o=!1},75693:(e,t,n)=>{"use strict";n.r(t),n.d(t,{LoadChartDialogRenderer:()=>ae});var o,r,i=n(50959),l=n(97754),s=n.n(l),a=n(50151),c=n(11542),u=n(56840),d=n(49483),m=n(77975),h=n(79418),f=n(9745),p=n(95276),v=n(20243),g=n(44563),b=n(645),x=n(32486);function C(e){const{sortDirection:t,children:n,...o}=e,r=(0,i.useRef)(null);return i.createElement("div",{...o,className:l(x.sortButton,"apply-common-tooltip","common-tooltip-vertical")},i.createElement(p.ControlDisclosure,{hideArrowButton:!0,ref:r,buttonChildren:i.createElement(f.Icon,{className:x.icon,icon:0===t?g:b}),buttonContainerClassName:x.buttonContainer,className:x.skeleton,onListboxFocus:function(e){e.target instanceof HTMLElement&&(0,v.handleAccessibleMenuFocus)(e,r)},onListboxKeyDown:v.handleAccessibleMenuKeyDown},n))}!function(e){e.Modified="modified",e.Title="title",e.Expiration="expiration"}(o||(o={})),function(e){e[e.Asc=0]="Asc",
e[e.Desc=1]="Desc"}(r||(r={}));var w=n(10838),E=n(23453);function y(e){const{label:t,listSortField:n,itemSortField:o,listSortDirection:r,itemSortDirection:s,onClick:a,className:c,...u}=e,d=o===n&&s===r;return i.createElement(w.AccessibleMenuItem,{...u,isActive:d,className:l(E.container,c),label:i.createElement("div",{className:E.labelWrap},i.createElement(f.Icon,{className:E.icon,icon:0===s?g:b}),i.createElement("span",{className:E.text},t)),onClick:function(){a(o,s)},"data-active":d.toString(),"data-sort-field":o,"data-sort-direction":0===s?"asc":"desc"})}var N=n(69654),S=n(7176);function R(e){const{children:t,className:n}=e;return i.createElement("div",{className:s()(S.container,n)},t)}function P(e){const{title:t}=e;return i.createElement("div",{className:S.title},t)}var k=n(50335);var D=n(84952),M=n(63273),F=n(898),B=n(33127);var L=n(3085),z=n(67850);function O(e){const{className:t,onScroll:n,onTouchStart:o,reference:r,children:l,scrollbar:a,...c}=e,[u,m]=(0,F.useDimensions)(),[h,f,p,v]=(0,B.useOverlayScroll)();return(0,i.useEffect)((()=>{const e=()=>{};return d.isFF?(document.addEventListener("wheel",(()=>e)),()=>{document.removeEventListener("wheel",e)}):e}),[]),i.createElement("div",{..."overlay"===a&&f,className:s()(z.container,t),onTouchStart:o,onScrollCapture:n,ref:u},"overlay"===a&&i.createElement(L.OverlayScrollContainer,{...h,className:z.overlayScrollWrap}),i.createElement(D.FixedSizeList,{ref:r,className:s()("native"===a?z.scroll:z.list),outerRef:"overlay"===a?p:void 0,onItemsRendered:v,layout:"vertical",width:"100%",height:(null==m?void 0:m.height)||0,children:l,direction:(0,M.isRtl)()?"rtl":"ltr",...c}))}var A=n(84015);var I=n(56570),W=n(64530),T=n(10074),_=n(50655),H=n(3615);var K=n(82826),j=n(15344),Z=n(24637),Q=n(19785),V=n(92821);const q=new K.DateFormatter("dd-MM-yyyy"),X=new j.TimeFormatter(j.hourMinuteFormat),G=I.enabled("items_favoriting");function Y(e){const{chart:t,chartWidgetCollection:o,trackEvent:r,favorites:l,onClose:a,searchString:u,onRemoveCanceled:d,isSelected:m}=e,[h,f]=(0,i.useState)((()=>t.active())),[p,v]=(0,i.useState)(!1),g=t.url?function(e){const t=e.chartId?`/chart/${e.chartId}/`:"/chart/",n=new URL(t,location.href);return e.symbol&&n.searchParams.append("symbol",e.symbol),e.interval&&n.searchParams.append("interval",e.interval),e.style&&n.searchParams.append("style",e.style),(0,A.urlWithMobileAppParams)(n.href)}({chartId:t.url}):void 0,b=(0,i.useContext)(_.SlotContext),x=(0,i.useMemo)((()=>new Date(1e3*t.modified)),[t]),C=(0,i.useMemo)((()=>(0,Q.createRegExpList)(u)),[u]),w=s()(V.highlight,h&&V.active);return(0,i.useEffect)((()=>(o&&o.metaInfo.id.subscribe(y),()=>{o&&o.metaInfo.id.unsubscribe(y)})),[]),i.createElement(W.DialogContentItem,{url:g,title:i.createElement(Z.HighlightedText,{className:w,queryString:u,rules:C,text:t.name}),subtitle:i.createElement(i.Fragment,null,i.createElement(Z.HighlightedText,{className:w,queryString:u,rules:C,text:t.description})," ","(",q.format(x).replace(/-/g,".")," ",X.formatLocal(x),")"),onClick:function(e){
if(I.enabled("symphony_embed"))return;0;t.openAction(),!1},onClickFavorite:function(){0;t.favoriteAction()},showFavorite:G,onClickRemove:async function(){if(p)return;v(!0);const e=await async function(e){return c.t(null,{replace:{name:e.name}},n(30448))}(t);v(!1),function(e,t,n,o){(0,H.showConfirm)({text:e,onConfirm:({dialogClose:e})=>{t(),e()},onClose:()=>{n()}},o)}(e,E,d,b)},isFavorite:Boolean(l[t.id]),isActive:h,isSelected:m,"data-name":"load-chart-dialog-item"});function E(){t.deleteAction()}function y(e){f(t.id===e)}}var J=n(59064),$=n(68335);var U=n(27830),ee=n(39166);const te={sortField:"modified",sortDirection:1},ne=function(e){const{paddingTop:t=0,paddingBottom:n=0}=e;return(0,i.forwardRef)((({style:e,...o},r)=>{const{height:l=0}=e;return i.createElement("div",{ref:r,style:{...e,height:`${((0,k.isNumber)(l)?l:parseFloat(l))+t+n}px`},...o})}))}({paddingBottom:6});function oe(e){let t;try{t=(0,T.getTranslatedResolution)(e)}catch(n){t=e}return t}const re=I.enabled("items_favoriting");function ie(e){const{onClose:t,chartWidgetCollection:o,serviceState:r}=e,[l,f]=(0,i.useState)(""),[p,v]=(0,i.useState)(l),g=(0,i.useRef)(null),[b,x]=(0,i.useState)((()=>u.getJSON("loadChartDialog.viewState",te))),w=(0,i.useRef)(null),E=(0,i.useRef)(null),{chartList:S,favorites:k}=(0,m.useWatchedValueReadonly)({watchedValue:r}),D=(0,i.useMemo)((()=>S.map((e=>({...e,description:`${e.symbol}, ${oe(e.interval)}`})))),[S]);(0,i.useEffect)((()=>{d.CheckMobile.any()||(0,a.ensureNotNull)(w.current).focus()}),[]);const M=(0,i.useRef)();(0,i.useEffect)((()=>(M.current=setTimeout((()=>{f(p)}),300),()=>{clearTimeout(M.current)})),[p]);const F=(0,i.useCallback)((()=>!0),[]),B=(0,i.useMemo)((()=>{return(0,Q.rankedSearch)({data:D.sort((e=b.sortDirection,(t,n)=>{if(!U.showFavoriteLayouts){if(k[t.id]&&!k[n.id])return-1;if(!k[t.id]&&k[n.id])return 1}const o=0===e?1:-1;return"modified"===b.sortField?o*(t.modified-n.modified):o*t.name.localeCompare(n.name)})),rules:(0,Q.createRegExpList)(l),queryString:l,primaryKey:"name",secondaryKey:"description"});var e}),[l,b,D,!U.showFavoriteLayouts&&k]),{selectedItemIndex:L,setSelectedItemIndex:z,handleKeyboardSelection:A}=function(e,t,n){const[o,r]=(0,i.useState)(-1);return(0,i.useEffect)((()=>{var e;-1!==o&&(null===(e=n.current)||void 0===e||e.scrollToItem(o))}),[o]),{selectedItemIndex:o,setSelectedItemIndex:r,handleKeyboardSelection:function(n){switch((0,$.hashFromEvent)(n)){case 40:if(o===e-1)return;r(o+1);break;case 38:if(0===o)return;if(-1===o)return void r(o+1);r(o-1);break;case 13:t(n)}}}}(B.length,(function(e){const t=B[L];if(-1===L||!t)return;0;t.openAction(),!1}),E);return i.createElement(h.AdaptivePopupDialog,{ref:g,onClose:t,onClickOutside:t,onKeyDown:A,isOpened:!0,className:ee.dialog,title:c.t(null,void 0,n(79825)),dataName:"load-layout-dialog",render:function(){return i.createElement(i.Fragment,null,i.createElement(N.DialogSearch,{reference:w,onChange:W,placeholder:c.t(null,void 0,n(8573))}),i.createElement(R,{className:s()(!re&&ee.contentHeader)},i.createElement(P,{title:c.t(null,void 0,n(11478))
}),i.createElement(C,{sortDirection:b.sortDirection,title:c.t(null,void 0,n(5191)),"data-name":"load-chart-dialog-sort-button"},i.createElement(y,{label:c.t(null,void 0,n(21329)),listSortField:b.sortField,itemSortField:"title",listSortDirection:b.sortDirection,itemSortDirection:0,onClick:H,"data-name":"load-chart-dialog-sort-menu-item"}),i.createElement(y,{label:c.t(null,void 0,n(11324)),listSortField:b.sortField,itemSortField:"title",listSortDirection:b.sortDirection,itemSortDirection:1,onClick:H,"data-name":"load-chart-dialog-sort-menu-item"}),i.createElement(y,{label:c.t(null,void 0,n(55108)),listSortField:b.sortField,itemSortField:"modified",listSortDirection:b.sortDirection,itemSortDirection:0,onClick:H,"data-name":"load-chart-dialog-sort-menu-item"}),i.createElement(y,{label:c.t(null,void 0,n(75272)),listSortField:b.sortField,itemSortField:"modified",listSortDirection:b.sortDirection,itemSortDirection:1,onClick:H,"data-name":"load-chart-dialog-sort-menu-item"}))),i.createElement(O,{scrollbar:"native",reference:E,itemCount:B.length,itemSize:52,className:ee.contentList,onScroll:I,innerElementType:ne,itemKey:e=>(k[B[e].id]?"f_":"")+B[e].id,children:({style:e,index:n})=>i.createElement("div",{style:e},i.createElement(Y,{chart:B[n],onClose:t,chartWidgetCollection:o,trackEvent:T,onRemoveCanceled:_,favorites:k,searchString:l,isSelected:n===L}))}))},forceCloseOnEsc:F});function I(){J.globalCloseDelegate.fire()}function W(e){const t=e.currentTarget.value;v(t),z(-1)}function T(e){0}function _(){(0,a.ensureNotNull)(g.current).focus()}function H(e,t){const n={sortField:e,sortDirection:t};x(n),u.setValue("loadChartDialog.viewState",JSON.stringify(n),{forceFlush:!0}),T()}}var le=n(29280),se=n(28124);class ae extends le.DialogRenderer{constructor(e){super(),this._options=e}show(){this.visible().value()||(this._rootInstance=(0,se.createReactRoot)(i.createElement(ie,{...this._options,onClose:()=>this.hide()}),this._container),this._setVisibility(!0))}hide(){var e;null===(e=this._rootInstance)||void 0===e||e.unmount(),this._setVisibility(!1)}}},2948:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M3.92 7.83 9 12.29l5.08-4.46-1-1.13L9 10.29l-4.09-3.6-.99 1.14Z"/></svg>'},60925:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M12 4h3v1h-1.04l-.88 9.64a1.5 1.5 0 0 1-1.5 1.36H6.42a1.5 1.5 0 0 1-1.5-1.36L4.05 5H3V4h3v-.5C6 2.67 6.67 2 7.5 2h3c.83 0 1.5.67 1.5 1.5V4ZM7.5 3a.5.5 0 0 0-.5.5V4h4v-.5a.5.5 0 0 0-.5-.5h-3ZM5.05 5l.87 9.55a.5.5 0 0 0 .5.45h5.17a.5.5 0 0 0 .5-.45L12.94 5h-7.9Z"/></svg>'},17105:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path stroke="currentColor" stroke-width="1.2" d="m1.5 1.5 21 21m0-21-21 21"/></svg>'},15130:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path stroke="currentColor" stroke-width="1.2" d="m1.5 1.5 15 15m0-15-15 15"/></svg>'},
38822:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 14 14" width="14" height="14"><path stroke="currentColor" stroke-width="1.2" d="m1.5 1.5 11 11m0-11-11 11"/></svg>'},63346:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" width="12" height="12"><path stroke="currentColor" stroke-width="1.2" d="m1.5 1.5 9 9m0-9-9 9"/></svg>'},34983:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 10 10" width="10" height="10"><path stroke="currentColor" stroke-width="1.2" d="m1.5 1.5 7 7m0-7-7 7"/></svg>'},33765:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18"><path fill="currentColor" d="M9.707 9l4.647-4.646-.707-.708L9 8.293 4.354 3.646l-.708.708L8.293 9l-4.647 4.646.708.708L9 9.707l4.646 4.647.708-.707L9.707 9z"/></svg>'},645:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" d="M19.5 18.5h-3M21.5 13.5h-5M23.5 8.5h-7M8.5 7v13.5M4.5 16.5l4 4 4-4"/></svg>'},44563:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" d="M19.5 18.5h-3M21.5 13.5h-5M23.5 8.5h-7M8.5 20.5V7M12.5 11l-4-4-4 4"/></svg>'},69859:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none"><path stroke="currentColor" d="M12.4 12.5a7 7 0 1 0-4.9 2 7 7 0 0 0 4.9-2zm0 0l5.101 5"/></svg>'},39146:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18" fill="none"><path fill="currentColor" d="M9 1l2.35 4.76 5.26.77-3.8 3.7.9 5.24L9 13l-4.7 2.47.9-5.23-3.8-3.71 5.25-.77L9 1z"/></svg>'},48010:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18" fill="none"><path stroke="currentColor" d="M9 2.13l1.903 3.855.116.236.26.038 4.255.618-3.079 3.001-.188.184.044.259.727 4.237-3.805-2L9 12.434l-.233.122-3.805 2.001.727-4.237.044-.26-.188-.183-3.079-3.001 4.255-.618.26-.038.116-.236L9 2.13z"/></svg>'},54313:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="M18.5 12.5a6 6 0 1 1-12 0 6 6 0 0 1 12 0Zm-1.25 5.8a7.5 7.5 0 1 1 1.06-1.06l4.22 4.23.53.53L22 23.06l-.53-.53-4.22-4.22Z"/></svg>'},86240:e=>{"use strict";e.exports=JSON.parse('{"size-header-height":"64px","media-phone-vertical":"screen and (max-width: 479px)","media-mf-phone-landscape":"screen and (min-width: 568px)"}')}}]);