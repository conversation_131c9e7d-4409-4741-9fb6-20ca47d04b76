.scrollable-P78dPRF5 {
  flex: 1 1 auto;
  min-height: 145px;
  overflow-x: hidden;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  min-height: 116px;
}
@media screen and (max-height: 290px) {
  .scrollable-P78dPRF5 {
    min-height: auto;
  }
}
@supports (-moz-appearance: none) {
  .scrollable-P78dPRF5 {
    scrollbar-color: var(--themed-color-scroll-bg, #9598a1) #0000;
    scrollbar-width: thin;
  }
  html.theme-dark .scrollable-P78dPRF5 {
    scrollbar-color: var(--themed-color-scroll-bg, #363a45) #0000;
  }
}
.scrollable-P78dPRF5::-webkit-scrollbar {
  height: 5px;
  width: 5px;
}
.scrollable-P78dPRF5::-webkit-scrollbar-thumb {
  background-clip: content-box;
  background-color: var(
    --tv-color-scrollbar-thumb-background,
    var(--themed-color-scroll-bg, #9598a1)
  );
  border: 1px solid #0000;
  border-radius: 3px;
}
html.theme-dark .scrollable-P78dPRF5::-webkit-scrollbar-thumb {
  background-color: var(
    --tv-color-scrollbar-thumb-background,
    var(--themed-color-scroll-bg, #363a45)
  );
}
.scrollable-P78dPRF5::-webkit-scrollbar-track {
  background-color: initial;
  border-radius: 3px;
}
.scrollable-P78dPRF5::-webkit-scrollbar-corner {
  display: none;
}
.content-P78dPRF5 {
  border-top: 1px solid;
  border-color: var(--themed-color-top-border-content, #e0e3eb);
  box-sizing: border-box;
  flex: 1 1 auto;
  padding: 20px;
}
html.theme-dark .content-P78dPRF5 {
  border-color: var(--themed-color-top-border-content, #434651);
}
.content-P78dPRF5 .row-P78dPRF5 {
  color: var(--themed-color-add-dialog-text, #131722);
  display: flex;
  margin-top: 12px;
}
html.theme-dark .content-P78dPRF5 .row-P78dPRF5 {
  color: var(--themed-color-add-dialog-text, #d1d4dc);
}
.content-P78dPRF5 .row-P78dPRF5:first-child {
  margin-top: 0;
}
.content-P78dPRF5 .row-P78dPRF5 .title-P78dPRF5 {
  display: flex;
  flex: none;
  line-height: 21px;
  margin-right: 28px;
  margin-top: 8px;
  width: 80px;
  word-break: break-word;
}
.content-P78dPRF5 .row-P78dPRF5 .control-P78dPRF5 {
  line-height: 21px;
  width: 180px;
}
.inputWrap-P78dPRF5 {
  max-width: 180px;
}
.intervalsDesktopDialog-P78dPRF5 {
  width: 400px;
}
