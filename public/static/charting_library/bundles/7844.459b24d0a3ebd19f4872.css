.title-uNZ8yW1y {
  align-items: center;
  color: var(--themed-color-title, #131722);
  cursor: default;
  display: flex;
  flex-shrink: 0;
  font-size: 14px;
  font-weight: 600;
  justify-content: space-between;
  line-height: 24px;
  overflow: hidden;
  padding: 5px 7px 5px 16px;
  text-overflow: ellipsis;
  white-space: nowrap;
}
html.theme-dark .title-uNZ8yW1y {
  color: var(--themed-color-title, #d1d4dc);
}
.title-uNZ8yW1y.withoutIcon-uNZ8yW1y {
  padding: 12px 16px;
}
.buttons-uNZ8yW1y {
  align-items: center;
  border-bottom: 1px solid var(--themed-color-header-border, #e0e3eb);
  display: flex;
  flex-direction: row;
  flex-shrink: 0;
  padding: 0 8px;
}
html.theme-dark .buttons-uNZ8yW1y {
  border-bottom: 1px solid var(--themed-color-header-border, #363a45);
}
.button-uNZ8yW1y {
  flex: none;
  height: 38px;
  justify-content: center;
  padding: 0;
  transition: none;
  width: 38px;
}
.button-uNZ8yW1y.disabled-uNZ8yW1y {
  opacity: 0.3;
}
.button-uNZ8yW1y.disabled-uNZ8yW1y,
.button-uNZ8yW1y.disabled-uNZ8yW1y:active,
.button-uNZ8yW1y.disabled-uNZ8yW1y:before {
  background-color: var(--themed-color-header-hovered-button-bg-disabled, #fff);
}
@media (any-hover: hover) {
  .button-uNZ8yW1y.disabled-uNZ8yW1y:hover {
    background-color: var(
      --themed-color-header-hovered-button-bg-disabled,
      #fff
    );
  }
}
html.theme-dark .button-uNZ8yW1y.disabled-uNZ8yW1y,
html.theme-dark .button-uNZ8yW1y.disabled-uNZ8yW1y:active,
html.theme-dark .button-uNZ8yW1y.disabled-uNZ8yW1y:before {
  background-color: var(
    --themed-color-header-hovered-button-bg-disabled,
    #131722
  );
}
@media (any-hover: hover) {
  html.theme-dark .button-uNZ8yW1y.disabled-uNZ8yW1y:hover {
    background-color: var(
      --themed-color-header-hovered-button-bg-disabled,
      #131722
    );
  }
}
.spacing-uNZ8yW1y {
  flex-grow: 1;
}
.toolbar-uNZ8yW1y {
  display: contents;
}
.wrap-IEe5qpW4 {
  background-color: var(--themed-color-pane-bg, #fff);
  position: relative;
}
html.theme-dark .wrap-IEe5qpW4 {
  background-color: var(--themed-color-pane-bg, #131722);
}
@media (any-hover: hover) {
  .wrap-IEe5qpW4:hover {
    background-color: var(--themed-color-hovered-background, #f0f3fa);
  }
  html.theme-dark .wrap-IEe5qpW4:hover {
    background-color: var(--themed-color-hovered-background, #2a2e39);
  }
}
.wrap-IEe5qpW4.selected-IEe5qpW4 {
  background-color: var(--themed-color-list-item-bg-selected, #bbd9fb);
}
@media (any-hover: hover) {
  .wrap-IEe5qpW4.selected-IEe5qpW4:hover {
    background-color: var(--themed-color-list-item-bg-selected, #bbd9fb);
  }
}
html.theme-dark .wrap-IEe5qpW4.selected-IEe5qpW4 {
  background-color: var(--themed-color-list-item-bg-selected, #142e61);
}
@media (any-hover: hover) {
  html.theme-dark .wrap-IEe5qpW4.selected-IEe5qpW4:hover {
    background-color: var(--themed-color-list-item-bg-selected, #142e61);
  }
}
.wrap-IEe5qpW4.childOfSelected-IEe5qpW4 {
  background-color: var(--themed-color-child-of-selected-background, #e3effd);
}
@media (any-hover: hover) {
  .wrap-IEe5qpW4.childOfSelected-IEe5qpW4:hover {
    background-color: var(--themed-color-child-of-selected-background, #e3effd);
  }
}
html.theme-dark .wrap-IEe5qpW4.childOfSelected-IEe5qpW4 {
  background-color: var(--themed-color-child-of-selected-background, #132042);
}
@media (any-hover: hover) {
  html.theme-dark .wrap-IEe5qpW4.childOfSelected-IEe5qpW4:hover {
    background-color: var(--themed-color-child-of-selected-background, #132042);
  }
  .wrap-IEe5qpW4.disabled-IEe5qpW4:hover {
    background-color: var(--themed-color-tree-bg, #fff);
  }
  html.theme-dark .wrap-IEe5qpW4.disabled-IEe5qpW4:hover {
    background-color: var(--themed-color-tree-bg, #131722);
  }
}
.wrap-IEe5qpW4 .expandHandle-IEe5qpW4 {
  align-items: center;
  display: flex;
  height: 100%;
  left: 4px;
  position: absolute;
}
.wrap-IEe5qpW4 .expandHandle-IEe5qpW4.expanded-IEe5qpW4 {
  transform: rotate(90deg);
}
.dropTargetInside-e_nPSSdZ {
  pointer-events: none;
}
.dropTargetInside-e_nPSSdZ,
html.theme-dark .dropTargetInside-e_nPSSdZ {
  box-shadow: inset 0 0 0 2px var(--themed-color-drop-target-border, #2962ff);
}
.dropTarget-e_nPSSdZ {
  height: 2px;
  pointer-events: none;
  position: absolute;
  width: 100%;
  z-index: 6;
}
.dropTarget-e_nPSSdZ,
html.theme-dark .dropTarget-e_nPSSdZ {
  background-color: var(--themed-color-drop-target-border, #2962ff);
}
.dropTarget-e_nPSSdZ.before-e_nPSSdZ {
  top: -1px;
}
.dropTarget-e_nPSSdZ.after-e_nPSSdZ {
  bottom: -1px;
}
.dropTarget-e_nPSSdZ:before {
  background: var(--themed-color-drop-layer, #fff);
  border: 2px solid var(--themed-color-drop-target-border, #2962ff);
  border-radius: 5px;
  bottom: -4px;
  content: "";
  height: 6px;
  left: 0;
  position: absolute;
  width: 6px;
}
html.theme-dark .dropTarget-e_nPSSdZ:before {
  background: var(--themed-color-drop-layer, #2a2e39);
  border: 2px solid var(--themed-color-drop-target-border, #2962ff);
}
.sticky-U0YaDVkl {
  position: sticky;
  top: -1px;
  z-index: 3;
}
.hideSticky-U0YaDVkl {
  opacity: 0;
}
.separator-MgF6KBas {
  background-color: var(--themed-color-separator, #e0e3eb);
  flex: none;
  height: 1px;
  margin: 6px 0;
}
html.theme-dark .separator-MgF6KBas {
  background-color: var(--themed-color-separator, #434651);
}
.sticky-MgF6KBas {
  position: absolute;
  width: 100%;
}
.tree-MgF6KBas {
  cursor: default;
  height: 100%;
  overflow: hidden;
  position: relative;
  z-index: 0;
}
.tree-MgF6KBas .overlayScrollWrap-MgF6KBas {
  bottom: 0;
  right: 0;
  top: 0;
  z-index: 1;
}
@supports (-moz-appearance: none) {
  .listContainer-MgF6KBas {
    scrollbar-width: none;
  }
}
.listContainer-MgF6KBas.sb-scrollbar-wrap {
  display: none;
}
.listContainer-MgF6KBas::-webkit-scrollbar {
  display: none;
  height: 0;
  width: 0;
}
.listContainer-MgF6KBas::-webkit-scrollbar-thumb,
.listContainer-MgF6KBas::-webkit-scrollbar-track {
  display: none;
}
.listContainer-MgF6KBas::-webkit-scrollbar-corner {
  display: none;
}
.wrap-ukH4sVzT {
  display: flex;
  flex-direction: column;
  height: 100%;
  touch-action: manipulation;
}
.wrap-ukH4sVzT .space-ukH4sVzT {
  display: flex;
  flex: 1 1 auto;
  flex-direction: column;
  height: 0;
}
.wrap-ukH4sVzT .tree-ukH4sVzT {
  overflow-y: auto;
}
.button-w6lVe_oI {
  align-items: center;
  background-color: var(--tv-list-item-button-background-color);
  border-radius: 4px;
  color: var(--tv-color-popup-element-toolbox-text, #787b86);
  display: inline-flex;
  font-size: 0;
  height: 22px;
  justify-content: center;
  min-width: 22px;
  width: 22px;
}
.button-w6lVe_oI.hovered-w6lVe_oI,
.button-w6lVe_oI:active {
  background-color: var(
    --tv-color-popup-element-toolbox-background-hover,
    var(
      --tv-list-item-button-background-hover-color,
      var(--themed-color-popup-element-toolbox-background-hover, #e0e3eb)
    )
  );
  color: var(
    --tv-color-popup-element-toolbox-text-hover,
    var(--themed-color-popup-element-toolbox-text-hover, #131722)
  );
}
@media (any-hover: hover) {
  .button-w6lVe_oI:hover {
    background-color: var(
      --tv-color-popup-element-toolbox-background-hover,
      var(
        --tv-list-item-button-background-hover-color,
        var(--themed-color-popup-element-toolbox-background-hover, #e0e3eb)
      )
    );
    color: var(
      --tv-color-popup-element-toolbox-text-hover,
      var(--themed-color-popup-element-toolbox-text-hover, #131722)
    );
  }
}
html.theme-dark .button-w6lVe_oI.hovered-w6lVe_oI,
html.theme-dark .button-w6lVe_oI:active {
  background-color: var(
    --tv-color-popup-element-toolbox-background-hover,
    var(
      --tv-list-item-button-background-hover-color,
      var(--themed-color-popup-element-toolbox-background-hover, #363a45)
    )
  );
}
@media (any-hover: hover) {
  html.theme-dark .button-w6lVe_oI:hover {
    background-color: var(
      --tv-color-popup-element-toolbox-background-hover,
      var(
        --tv-list-item-button-background-hover-color,
        var(--themed-color-popup-element-toolbox-background-hover, #363a45)
      )
    );
  }
}
html.theme-dark .button-w6lVe_oI.hovered-w6lVe_oI,
html.theme-dark .button-w6lVe_oI:active {
  color: var(
    --tv-color-popup-element-toolbox-text-hover,
    var(--themed-color-popup-element-toolbox-text-hover, #d1d4dc)
  );
}
@media (any-hover: hover) {
  html.theme-dark .button-w6lVe_oI:hover {
    color: var(
      --tv-color-popup-element-toolbox-text-hover,
      var(--themed-color-popup-element-toolbox-text-hover, #d1d4dc)
    );
  }
}
.button-w6lVe_oI.disabled-w6lVe_oI,
.button-w6lVe_oI.disabled-w6lVe_oI:active {
  background-color: var(
    --tv-list-item-button-disabled-background-color,
    var(--themed-color-force-transparent, #0000)
  );
}
@media (any-hover: hover) {
  .button-w6lVe_oI.disabled-w6lVe_oI:hover {
    background-color: var(
      --tv-list-item-button-disabled-background-color,
      var(--themed-color-force-transparent, #0000)
    );
  }
}
html.theme-dark .button-w6lVe_oI.disabled-w6lVe_oI,
html.theme-dark .button-w6lVe_oI.disabled-w6lVe_oI:active {
  background-color: var(
    --tv-list-item-button-disabled-background-color,
    var(--themed-color-force-transparent, #0000)
  );
}
@media (any-hover: hover) {
  html.theme-dark .button-w6lVe_oI.disabled-w6lVe_oI:hover {
    background-color: var(
      --tv-list-item-button-disabled-background-color,
      var(--themed-color-force-transparent, #0000)
    );
  }
}
.wrap-C8ln3wvp {
  align-items: center;
  background-color: inherit;
  display: flex;
  flex: none;
  padding: 5px 8px 5px 22px;
}
.wrap-C8ln3wvp.dialog-C8ln3wvp {
  background-color: var(--themed-color-dialog-background, #fff);
}
html.theme-dark .wrap-C8ln3wvp.dialog-C8ln3wvp {
  background-color: var(--themed-color-dialog-background, #1e222d);
}
html.theme-dark .wrap-C8ln3wvp.mobile-C8ln3wvp {
  background-color: #000;
}
.wrap-C8ln3wvp.offset-C8ln3wvp {
  padding-left: 48px;
}
.wrap-C8ln3wvp .title-C8ln3wvp {
  color: var(--themed-color-title, #131722);
  margin-left: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
html.theme-dark .wrap-C8ln3wvp .title-C8ln3wvp {
  color: var(--themed-color-title, #d1d4dc);
}
.wrap-C8ln3wvp .title-C8ln3wvp.main-C8ln3wvp {
  font-weight: 600;
}
.wrap-C8ln3wvp .title-C8ln3wvp.disabled-C8ln3wvp {
  color: var(--themed-color-disabled-title, #b2b5be);
}
html.theme-dark .wrap-C8ln3wvp .title-C8ln3wvp.disabled-C8ln3wvp {
  color: var(--themed-color-disabled-title, #50535e);
}
.wrap-C8ln3wvp .icon-C8ln3wvp {
  color: var(--themed-color-title, #131722);
  flex-shrink: 0;
  height: 28px;
  line-height: 28px;
  width: 28px;
}
html.theme-dark .wrap-C8ln3wvp .icon-C8ln3wvp {
  color: var(--themed-color-title, #d1d4dc);
}
.wrap-C8ln3wvp .pathIcon-C8ln3wvp {
  align-items: center;
  display: flex;
  height: 24px;
  justify-content: center;
  padding: 2px;
  text-align: center;
  width: 24px;
}
.wrap-C8ln3wvp .syncIconWrap-C8ln3wvp {
  background-color: inherit;
  height: 28px;
  position: relative;
  width: 0;
}
.wrap-C8ln3wvp .syncIcon-C8ln3wvp {
  align-items: flex-end;
  background-color: inherit;
  border-radius: 4px;
  bottom: 0;
  color: var(--themed-color-default-gray, #6a6d78);
  display: flex;
  height: 14px;
  justify-content: flex-end;
  overflow: hidden;
  position: absolute;
  right: 0;
  width: 14px;
}
html.theme-dark .wrap-C8ln3wvp .syncIcon-C8ln3wvp {
  color: var(--themed-color-default-gray, #868993);
}
.wrap-C8ln3wvp .rightButtons-C8ln3wvp {
  display: flex;
  flex: none;
  height: 22px;
  margin-left: auto;
  position: relative;
}
.wrap-C8ln3wvp.hover-C8ln3wvp {
  background-color: var(--themed-color-hovered-background, #f0f3fa);
}
html.theme-dark .wrap-C8ln3wvp.hover-C8ln3wvp {
  background-color: var(--themed-color-hovered-background, #2a2e39);
}
.wrap-C8ln3wvp.disabled-C8ln3wvp {
  --tv-list-item-button-background-hover-color: var(
    --themed-color-hovered-background,
    #f0f3fa
  );
}
html.theme-dark .wrap-C8ln3wvp.disabled-C8ln3wvp {
  --tv-list-item-button-background-hover-color: var(
    --themed-color-hovered-background,
    #2a2e39
  );
}
.wrap-C8ln3wvp.disabled-C8ln3wvp .button-C8ln3wvp,
.wrap-C8ln3wvp.disabled-C8ln3wvp .expandHandle-C8ln3wvp,
.wrap-C8ln3wvp.disabled-C8ln3wvp .icon-C8ln3wvp,
.wrap-C8ln3wvp.disabled-C8ln3wvp .syncIcon-C8ln3wvp,
.wrap-C8ln3wvp.disabled-C8ln3wvp .title-C8ln3wvp {
  color: var(--themed-color-disabled-title, #b2b5be);
}
html.theme-dark .wrap-C8ln3wvp.disabled-C8ln3wvp .button-C8ln3wvp,
html.theme-dark .wrap-C8ln3wvp.disabled-C8ln3wvp .expandHandle-C8ln3wvp,
html.theme-dark .wrap-C8ln3wvp.disabled-C8ln3wvp .icon-C8ln3wvp,
html.theme-dark .wrap-C8ln3wvp.disabled-C8ln3wvp .syncIcon-C8ln3wvp,
html.theme-dark .wrap-C8ln3wvp.disabled-C8ln3wvp .title-C8ln3wvp {
  color: var(--themed-color-disabled-title, #50535e);
}
.wrap-C8ln3wvp.selected-C8ln3wvp .rightButtons-C8ln3wvp .button-C8ln3wvp {
  --tv-list-item-button-background-hover-color: var(
    --themed-color-selected-hovered-button-background,
    #90bff9
  );
}
html.theme-dark
  .wrap-C8ln3wvp.selected-C8ln3wvp
  .rightButtons-C8ln3wvp
  .button-C8ln3wvp {
  --tv-list-item-button-background-hover-color: var(
    --themed-color-selected-hovered-button-background,
    #143a87
  );
}
.wrap-C8ln3wvp.childOfSelected-C8ln3wvp
  .rightButtons-C8ln3wvp
  .button-C8ln3wvp {
  --tv-list-item-button-background-hover-color: var(
    --themed-color-child-of-selected-hovered-button-background,
    #bbd9fb
  );
}
html.theme-dark
  .wrap-C8ln3wvp.childOfSelected-C8ln3wvp
  .rightButtons-C8ln3wvp
  .button-C8ln3wvp {
  --tv-list-item-button-background-hover-color: var(
    --themed-color-child-of-selected-hovered-button-background,
    #142e61
  );
}
.wrap-C8ln3wvp .renameInput-C8ln3wvp {
  background-color: var(--themed-color-rename-input-background, #fff);
  height: 28px;
  margin-left: 3px;
  width: 100%;
}
html.theme-dark .wrap-C8ln3wvp .renameInput-C8ln3wvp {
  background-color: var(--themed-color-rename-input-background, #2a2e39);
}
.wrap-C8ln3wvp .renameInput-C8ln3wvp[draggable="true"] {
  -webkit-user-select: text;
  user-select: text;
}
.button-C8ln3wvp {
  --tv-list-item-button-background-hover-color: var(
    --themed-color-hovered-button-background,
    #e0e3eb
  );
  display: none;
  margin-left: 8px;
  visibility: hidden;
}
html.theme-dark .button-C8ln3wvp {
  --tv-list-item-button-background-hover-color: var(
    --themed-color-hovered-button-background,
    #363a45
  );
}
.button-C8ln3wvp.warn-C8ln3wvp,
.button-C8ln3wvp.warn-C8ln3wvp:active,
html.theme-dark .button-C8ln3wvp.warn-C8ln3wvp {
  color: var(--themed-color-warn-text, #fb8c00);
}
@media (any-hover: hover) {
  .button-C8ln3wvp.warn-C8ln3wvp:hover {
    color: var(--themed-color-warn-text, #fb8c00);
  }
}
html.theme-dark .button-C8ln3wvp.warn-C8ln3wvp:active {
  color: var(--themed-color-warn-text, #fb8c00);
}
@media (any-hover: hover) {
  html.theme-dark .button-C8ln3wvp.warn-C8ln3wvp:hover {
    color: var(--themed-color-warn-text, #fb8c00);
  }
}
.button-C8ln3wvp.visible-C8ln3wvp {
  display: inline-flex;
  visibility: visible;
}
.button-C8ln3wvp.visible-C8ln3wvp ~ .button-C8ln3wvp {
  display: inline-flex;
}
.dialog-VUnQLSMH {
  height: 400px;
}
.buttons-VUnQLSMH {
  align-items: center;
  display: flex;
  flex-direction: row;
  flex-shrink: 0;
}
.button-VUnQLSMH {
  flex: none;
  height: 34px;
  justify-content: center;
  padding: 0;
  transition: none;
  width: 34px;
  --tv-toolbar-explicit-hover-border-radius: 8px;
  --tv-toolbar-explicit-hover-margin: 0;
}
.button-VUnQLSMH.disabled-VUnQLSMH {
  opacity: 0.3;
}
.button-VUnQLSMH.disabled-VUnQLSMH,
.button-VUnQLSMH.disabled-VUnQLSMH:active,
.button-VUnQLSMH.disabled-VUnQLSMH:before {
  background-color: var(--themed-color-header-hovered-button-bg-disabled, #fff);
}
@media (any-hover: hover) {
  .button-VUnQLSMH.disabled-VUnQLSMH:hover {
    background-color: var(
      --themed-color-header-hovered-button-bg-disabled,
      #fff
    );
  }
}
html.theme-dark .button-VUnQLSMH.disabled-VUnQLSMH,
html.theme-dark .button-VUnQLSMH.disabled-VUnQLSMH:active,
html.theme-dark .button-VUnQLSMH.disabled-VUnQLSMH:before {
  background-color: var(
    --themed-color-header-hovered-button-bg-disabled,
    #131722
  );
}
@media (any-hover: hover) {
  html.theme-dark .button-VUnQLSMH.disabled-VUnQLSMH:hover {
    background-color: var(
      --themed-color-header-hovered-button-bg-disabled,
      #131722
    );
  }
}
