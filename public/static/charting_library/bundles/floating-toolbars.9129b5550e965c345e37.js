(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[2306],{66783:t=>{"use strict";var e=Object.prototype.hasOwnProperty;function o(t,e){return t===e?0!==t||0!==e||1/t==1/e:t!=t&&e!=e}t.exports=function(t,i){if(o(t,i))return!0;if("object"!=typeof t||null===t||"object"!=typeof i||null===i)return!1;var n=Object.keys(t),r=Object.keys(i);if(n.length!==r.length)return!1;for(var s=0;s<n.length;s++)if(!e.call(i,n[s])||!o(t[n[s]],i[n[s]]))return!1;return!0}},68976:t=>{t.exports={button:"button-D4RPB3ZC",content:"content-D4RPB3ZC",iconOnly:"iconOnly-D4RPB3ZC",link:"link-D4RPB3ZC",brand:"brand-D4RPB3ZC",primary:"primary-D4RPB3ZC",secondary:"secondary-D4RPB3ZC",gray:"gray-D4RPB3ZC",green:"green-D4RPB3ZC",red:"red-D4RPB3ZC",black:"black-D4RPB3ZC","black-friday":"black-friday-D4RPB3ZC","cyber-monday":"cyber-monday-D4RPB3ZC",xsmall:"xsmall-D4RPB3ZC",startIconWrap:"startIconWrap-D4RPB3ZC",endIconWrap:"endIconWrap-D4RPB3ZC",withStartIcon:"withStartIcon-D4RPB3ZC",withEndIcon:"withEndIcon-D4RPB3ZC",small:"small-D4RPB3ZC",medium:"medium-D4RPB3ZC",large:"large-D4RPB3ZC",xlarge:"xlarge-D4RPB3ZC",animated:"animated-D4RPB3ZC",stretch:"stretch-D4RPB3ZC",grouped:"grouped-D4RPB3ZC",adjustPosition:"adjustPosition-D4RPB3ZC",firstRow:"firstRow-D4RPB3ZC",firstCol:"firstCol-D4RPB3ZC","no-corner-top-left":"no-corner-top-left-D4RPB3ZC","no-corner-top-right":"no-corner-top-right-D4RPB3ZC","no-corner-bottom-right":"no-corner-bottom-right-D4RPB3ZC","no-corner-bottom-left":"no-corner-bottom-left-D4RPB3ZC",textWrap:"textWrap-D4RPB3ZC",multilineContent:"multilineContent-D4RPB3ZC",secondaryText:"secondaryText-D4RPB3ZC",primaryText:"primaryText-D4RPB3ZC"}},51179:t=>{t.exports={}},42329:t=>{t.exports={}},29399:t=>{t.exports={}},62133:t=>{t.exports={wrap:"wrap-Nn3SCuEL",icon:"icon-Nn3SCuEL",colorBg:"colorBg-Nn3SCuEL",color:"color-Nn3SCuEL",multicolor:"multicolor-Nn3SCuEL",white:"white-Nn3SCuEL"}},31527:t=>{t.exports={button:"button-BuUjli6L"}},66431:t=>{t.exports={item:"item-KdWj36gM",withIcon:"withIcon-KdWj36gM",icon:"icon-KdWj36gM",labelRow:"labelRow-KdWj36gM",multiWidth:"multiWidth-KdWj36gM",buttonWrap:"buttonWrap-KdWj36gM",buttonLabel:"buttonLabel-KdWj36gM"}},94996:t=>{t.exports={container:"container-mdcOkvbj",sectionTitle:"sectionTitle-mdcOkvbj",separator:"separator-mdcOkvbj",customButton:"customButton-mdcOkvbj",accessible:"accessible-mdcOkvbj"}},11988:t=>{t.exports={container:"container-iiEYaqPD",form:"form-iiEYaqPD",swatch:"swatch-iiEYaqPD",inputWrap:"inputWrap-iiEYaqPD",inputHash:"inputHash-iiEYaqPD",input:"input-iiEYaqPD",buttonWrap:"buttonWrap-iiEYaqPD",hueSaturationWrap:"hueSaturationWrap-iiEYaqPD",saturation:"saturation-iiEYaqPD",hue:"hue-iiEYaqPD"}},30338:t=>{t.exports={hue:"hue-r4uo5Wn6",pointer:"pointer-r4uo5Wn6",accessible:"accessible-r4uo5Wn6",pointerContainer:"pointerContainer-r4uo5Wn6"}},3252:t=>{t.exports={opacity:"opacity-EnWts7Xu",opacitySlider:"opacitySlider-EnWts7Xu",opacitySliderGradient:"opacitySliderGradient-EnWts7Xu",pointer:"pointer-EnWts7Xu",dragged:"dragged-EnWts7Xu",
opacityPointerWrap:"opacityPointerWrap-EnWts7Xu",opacityInputWrap:"opacityInputWrap-EnWts7Xu",opacityInput:"opacityInput-EnWts7Xu",opacityInputPercent:"opacityInputPercent-EnWts7Xu",accessible:"accessible-EnWts7Xu"}},52650:t=>{t.exports={saturation:"saturation-NFNfqP2w",pointer:"pointer-NFNfqP2w",accessible:"accessible-NFNfqP2w"}},6294:t=>{t.exports={swatches:"swatches-sfn7Lezv",swatch:"swatch-sfn7Lezv",hover:"hover-sfn7Lezv",empty:"empty-sfn7Lezv",white:"white-sfn7Lezv",selected:"selected-sfn7Lezv",contextItem:"contextItem-sfn7Lezv",row:"row-sfn7Lezv"}},62794:t=>{t.exports={icon:"icon-WB2y0EnP",dropped:"dropped-WB2y0EnP"}},94720:(t,e,o)=>{"use strict";var i,n,r;function s(t="default"){switch(t){case"default":return"primary";case"stroke":return"secondary"}}function a(t="primary"){switch(t){case"primary":return"brand";case"success":return"green";case"default":return"gray";case"danger":return"red"}}function l(t="m"){switch(t){case"s":return"xsmall";case"m":return"small";case"l":return"large"}}o.d(e,{Button:()=>T}),function(t){t.Primary="primary",t.Success="success",t.Default="default",t.Danger="danger"}(i||(i={})),function(t){t.Small="s",t.Medium="m",t.Large="l"}(n||(n={})),function(t){t.Default="default",t.Stroke="stroke"}(r||(r={}));var c=o(50959),h=o(97754),d=o(95604),p=o(9745),u=o(68976),m=o.n(u);const _="apply-overflow-tooltip apply-overflow-tooltip--check-children-recursively apply-overflow-tooltip--allow-text apply-common-tooltip";function g(t){const{color:e="brand",size:o="medium",variant:i="primary",stretch:n=!1,icon:r,startIcon:s,endIcon:a,iconOnly:l=!1,className:c,isGrouped:p,cellState:u,disablePositionAdjustment:g=!1,primaryText:v,secondaryText:y,isAnchor:f=!1}=t,b=function(t){let e="";return 0!==t&&(1&t&&(e=h(e,m()["no-corner-top-left"])),2&t&&(e=h(e,m()["no-corner-top-right"])),4&t&&(e=h(e,m()["no-corner-bottom-right"])),8&t&&(e=h(e,m()["no-corner-bottom-left"]))),e}((0,d.getGroupCellRemoveRoundBorders)(u));return h(c,m().button,m()[o],m()[e],m()[i],n&&m().stretch,(r||s)&&m().withStartIcon,a&&m().withEndIcon,l&&m().iconOnly,b,p&&m().grouped,p&&!g&&m().adjustPosition,p&&u.isTop&&m().firstRow,p&&u.isLeft&&m().firstCol,v&&y&&m().multilineContent,f&&m().link,_)}function v(t){const{startIcon:e,icon:o,iconOnly:i,children:n,endIcon:r,primaryText:s,secondaryText:a}=t,l=null!=e?e:o,d=!(e||o||r||i)&&!n&&s&&a;return c.createElement(c.Fragment,null,l&&c.createElement(p.Icon,{icon:l,className:m().startIconWrap}),n&&c.createElement("span",{className:m().content},n),r&&!i&&c.createElement(p.Icon,{icon:r,className:m().endIconWrap}),d&&function(t){return t.primaryText&&t.secondaryText&&c.createElement("div",{className:h(m().textWrap,_)},c.createElement("span",{className:m().primaryText}," ",t.primaryText," "),"string"==typeof t.secondaryText?c.createElement("span",{className:m().secondaryText}," ",t.secondaryText," "):c.createElement("span",{className:m().secondaryText},c.createElement("span",null,t.secondaryText.firstLine),c.createElement("span",null,t.secondaryText.secondLine)))}(t))}var y=o(34094),f=o(86332),b=o(90186)
;function w(t){const{className:e,color:o,variant:i,size:n,stretch:r,animated:s,icon:a,iconOnly:l,startIcon:c,endIcon:h,primaryText:d,secondaryText:p,...u}=t;return{...u,...(0,b.filterDataProps)(t),...(0,b.filterAriaProps)(t)}}function C(t){const{reference:e,tooltipText:o,...i}=t,{isGrouped:n,cellState:r,disablePositionAdjustment:s}=(0,c.useContext)(f.ControlGroupContext),a=g({...i,isGrouped:n,cellState:r,disablePositionAdjustment:s});return c.createElement("button",{...w(i),className:a,ref:e,"data-overflow-tooltip-text":null!=o?o:t.primaryText?[t.primaryText,t.secondaryText].join(" "):(0,y.getTextForTooltip)(t.children)},c.createElement(v,{...i}))}o(78572);function x(t){const{intent:e,size:o,appearance:i,useFullWidth:n,icon:r,...c}=t;return{...c,color:a(e),size:l(o),variant:s(i),stretch:n,startIcon:r}}function T(t){return c.createElement(C,{...x(t)})}},78572:(t,e,o)=>{"use strict";var i,n,r,s;!function(t){t.Primary="primary",t.QuietPrimary="quiet-primary",t.Secondary="secondary",t.Ghost="ghost"}(i||(i={})),function(t){t.XXSmall="xxsmall",t.XSmall="xsmall",t.Small="small",t.Medium="medium",t.Large="large",t.XLarge="xlarge",t.XXLarge="xxlarge"}(n||(n={})),function(t){t.Brand="brand",t.Gray="gray",t.LightGray="light-gray",t.Green="green",t.Red="red",t.Black="black",t.Gradient="gradient",t.BlackFriday="black-friday",t.CyberMonday="cyber-monday"}(r||(r={})),function(t){t.Semibold18px="semibold18px",t.Semibold16px="semibold16px",t.Semibold14px="semibold14px",t.Medium16px="medium16px",t.Regular16px="regular16px",t.Regular14px="regular14px"}(s||(s={}))},86332:(t,e,o)=>{"use strict";o.d(e,{ControlGroupContext:()=>i});const i=o(50959).createContext({isGrouped:!1,cellState:{isTop:!0,isRight:!0,isBottom:!0,isLeft:!0}})},95604:(t,e,o)=>{"use strict";function i(t){let e=0;return t.isTop&&t.isLeft||(e+=1),t.isTop&&t.isRight||(e+=2),t.isBottom&&t.isLeft||(e+=8),t.isBottom&&t.isRight||(e+=4),e}o.d(e,{getGroupCellRemoveRoundBorders:()=>i})},56073:(t,e,o)=>{"use strict";function i(t,e=!1){const o=getComputedStyle(t),i=[o.height];return"border-box"!==o.boxSizing&&i.push(o.paddingTop,o.paddingBottom,o.borderTopWidth,o.borderBottomWidth),e&&i.push(o.marginTop,o.marginBottom),i.reduce(((t,e)=>t+(parseFloat(e)||0)),0)}function n(t,e=!1){const o=getComputedStyle(t),i=[o.width];return"border-box"!==o.boxSizing&&i.push(o.paddingLeft,o.paddingRight,o.borderLeftWidth,o.borderRightWidth),e&&i.push(o.marginLeft,o.marginRight),i.reduce(((t,e)=>t+(parseFloat(e)||0)),0)}o.d(e,{outerHeight:()=>i,outerWidth:()=>n})},34094:(t,e,o)=>{"use strict";o.d(e,{getTextForTooltip:()=>s});var i=o(50959);const n=t=>(0,i.isValidElement)(t)&&Boolean(t.props.children),r=t=>null==t||"boolean"==typeof t||"{}"===JSON.stringify(t)?"":t.toString()+" ",s=t=>Array.isArray(t)||(0,i.isValidElement)(t)?i.Children.toArray(t).reduce(((t,e)=>{let o="";return o=(0,i.isValidElement)(e)&&n(e)?s(e.props.children):(0,i.isValidElement)(e)&&!n(e)?"":r(e),t.concat(o)}),"").trim():r(t)},52778:(t,e,o)=>{"use strict";o.d(e,{OutsideEvent:()=>n});var i=o(36383);function n(t){
const{children:e,...o}=t;return e((0,i.useOutsideEvent)(o))}},70114:(t,e,o)=>{"use strict";o.d(e,{ColorPickerButton:()=>v});var i=o(50959),n=o(97754),r=o.n(n),s=o(50151),a=o(9745),l=o(24377),c=o(19063),h=o(56512),d=o(35789),p=o(6914),u=o(20626),m=o(43982),_=o(78135),g=o(62133);function v(t){const{property:e,icon:o,propertyApplier:n,title:v,undoText:y,isToolbarFixed:f,className:b}=t,w=(0,m.useProperty)(e),C=(0,i.useRef)(null),x=w?(0,l.parseRgba)(w)[3]:void 0,T=""===w,S=String(D()).toLowerCase()===p.white,[E,P,B]=(0,h.useCustomColors)();return i.createElement(u.ToolWidgetMenu,{className:b,verticalDropDirection:f?_.VerticalDropDirection.FromBottomToTop:void 0,horizontalDropDirection:f?_.HorizontalDropDirection.FromLeftToRight:void 0,horizontalAttachEdge:f?_.HorizontalAttachEdge.Left:void 0,verticalAttachEdge:f?_.VerticalAttachEdge.Top:void 0,content:i.createElement("div",{className:g.wrap},i.createElement(a.Icon,{className:g.icon,icon:o}),i.createElement("div",{className:g.colorBg},i.createElement("div",{className:r()(g.color,T&&g.multicolor,S&&g.white),style:T?void 0:{backgroundColor:w}}))),arrow:!1,title:v,ref:C,"data-name":t["data-name"],menuDataName:`${t["data-name"]}-menu`},i.createElement(d.ColorPicker,{color:D(),opacity:x,onColorChange:function(t,e){const o=w?(0,c.alphaToTransparency)((0,l.parseRgba)(w)[3]):0;L((0,c.generateColor)(String(t),o,true)),e||(0,s.ensureNotNull)(C.current).close()},onOpacityChange:function(t){L((0,c.generateColor)(w,(0,c.alphaToTransparency)(t),!0))},selectOpacity:void 0!==x,selectCustom:!0,customColors:E,onAddColor:function(t){P(t),(0,s.ensureNotNull)(C.current).close()},onRemoveCustomColor:B}));function D(){return w?(0,l.rgbToHexString)((0,l.parseRgb)(w)):null}function L(t){n.setProperty(e,t,y)}}},61259:(t,e,o)=>{"use strict";o.d(e,{LineWidthButton:()=>b});var i=o(50959),n=o(97754),r=o(50151),s=o(9745),a=o(20626),l=o(43982),c=o(16396),h=o(40173),d=o(78135),p=o(22978),u=o(14631),m=o(6096),_=o(6483),g=o(66611),v=o(66431);const y=(0,h.mergeThemes)(c.DEFAULT_POPUP_MENU_ITEM_THEME,v),f=[{value:1,icon:p},{value:2,icon:u},{value:3,icon:m},{value:4,icon:_}];function b(t){const{multipleProperty:e,title:o,undoText:h,propertyApplier:p,isToolbarFixed:u,className:m,isSmallScreen:_}=t,b=(0,l.useProperty)((0,r.ensureDefined)(e)),w="mixed"===b||!b,C=function(t){const e=f.find((e=>e.value===t));if(!e)return g;return e.icon}(b);return i.createElement(a.ToolWidgetMenu,{className:m,arrow:!1,title:o,"data-name":t["data-name"],menuDataName:`${t["data-name"]}-menu`,verticalDropDirection:u?d.VerticalDropDirection.FromBottomToTop:void 0,horizontalDropDirection:u?d.HorizontalDropDirection.FromRightToLeft:void 0,horizontalAttachEdge:u?d.HorizontalAttachEdge.Right:void 0,verticalAttachEdge:u?d.VerticalAttachEdge.Top:void 0,content:i.createElement("div",null,w?i.createElement("div",{className:v.multiWidth},i.createElement(s.Icon,{icon:g})):i.createElement("div",{className:v.buttonWrap},!_&&i.createElement(s.Icon,{icon:C}),i.createElement("div",{className:n(!_&&v.buttonLabel)},`${b}px`)))
},f.map((({value:t,icon:e})=>i.createElement(c.PopupMenuItem,{key:t,theme:y,label:`${t}px`,icon:e,isActive:t===b,onClick:x,onClickArg:t}))));function x(t){t&&e&&(p.beginUndoMacro(h),e.setValue(t,void 0,{applyValue:(t,e)=>{p.setProperty(t,e,h)}}),p.endUndoMacro())}}},43982:(t,e,o)=>{"use strict";o.d(e,{useProperty:()=>n});var i=o(50959);const n=t=>{const[e,o]=(0,i.useState)(t.value());return(0,i.useEffect)((()=>{const e=t=>{o(t.value())};e(t);const i={};return t.subscribe(i,e),()=>t.unsubscribe(i,e)}),[t]),e}},35789:(t,e,o)=>{"use strict";o.d(e,{ColorPicker:()=>j});var i=o(50959),n=o(97754),r=o.n(n),s=o(11542),a=o(59369),l=o(43688),c=o(93532),h=o(45582),d=Math.ceil,p=Math.max;const u=function(t,e,o){e=(o?(0,c.default)(t,e,o):void 0===e)?1:p((0,h.default)(e),0);var i=null==t?0:t.length;if(!i||e<1)return[];for(var n=0,r=0,s=Array(d(i/e));n<i;)s[r++]=(0,l.default)(t,n,n+=e);return s};var m=o(24377),_=o(50151),g=o(49483),v=o(20520),y=o(16396),f=o(6914),b=o(50238),w=o(93544),C=o(6294);function x(t){const{color:e,selected:r,onSelect:a,onSwatchRemove:l}=t,[c,h]=(0,i.useState)(!1),[d,p]=(0,b.useRovingTabindexElement)(null),u=Boolean(l)&&!g.CheckMobile.any();return i.createElement(i.Fragment,null,i.createElement("button",{ref:d,style:e?{color:e}:void 0,className:n(C.swatch,c&&C.hover,r&&C.selected,!e&&C.empty,String(e).toLowerCase()===f.white&&C.white),onClick:function(){a(e)},onContextMenu:u?m:void 0,tabIndex:p,"data-role":"swatch"}),u&&i.createElement(v.PopupMenu,{isOpened:c,onClose:m,position:function(){const t=(0,_.ensureNotNull)(d.current).getBoundingClientRect();return{x:t.left,y:t.top+t.height+4}},onClickOutside:m},i.createElement(y.PopupMenuItem,{className:C.contextItem,label:s.t(null,void 0,o(89984)),icon:w,onClick:function(){m(),(0,_.ensureDefined)(l)()},dontClosePopup:!0})));function m(){h(!c)}}function T(t){const{colors:e,color:o,children:n,onSelect:r,onRemoveCustomColor:s}=t;if(!e)return null;const a=o?(0,m.parseRgb)(String(o)):void 0,l=u(e,10);return i.createElement("div",{className:C.swatches},l.map(((t,e)=>i.createElement("div",{className:C.row,"data-role":"row",key:e},t.map(((t,o)=>i.createElement(x,{key:String(t)+o,color:t,selected:a&&(0,m.areEqualRgb)(a,(0,m.parseRgb)(String(t))),onSelect:c,onSwatchRemove:s?()=>function(t,e){const o=10*t+e;null==s||s(o)}(e,o):void 0})))))),n);function c(t){r&&r(t)}}var S=o(54368),E=o(94720);function P(t){const e=`Invalid RGB color: ${t}`;if(null===t)throw new Error(e);const o=t.match(/^#?([a-f0-9]{2})([a-f0-9]{2})([a-f0-9]{2})$/i);if(null===o)throw new Error(e);const[,i,n,r]=o;if(!i||!n||!r)throw new Error(e);const s=parseInt(i,16)/255,a=parseInt(n,16)/255,l=parseInt(r,16)/255,c=Math.max(s,a,l),h=Math.min(s,a,l);let d;const p=c,u=c-h,m=0===c?0:u/c;if(c===h)d=0;else{switch(c){case s:d=(a-l)/u+(a<l?6:0);break;case a:d=(l-s)/u+2;break;case l:d=(s-a)/u+4;break;default:d=0}d/=6}return{h:d,s:m,v:p}}var B=o(43370),D=o(68335),L=o(9859),W=o(52650);const k=[37,39,38,40],N=.01;class M extends i.PureComponent{constructor(){super(...arguments),this._container=null,this._refContainer=t=>{
this._container=t},this._handlePosition=t=>{const{hsv:{h:e},onChange:o}=this.props;if(!o)return;const i=(0,_.ensureNotNull)(this._container).getBoundingClientRect(),n=t.clientX-i.left,r=t.clientY-i.top;o({h:e,s:(0,L.clamp)(n/i.width,0,1),v:(0,L.clamp)(1-r/i.height,0,1)})},this._handleKeyDown=t=>{const{hsv:{h:e,s:o,v:i},onChange:n}=this.props,r=(0,D.hashFromEvent)(t);if(!n||!k.includes(r))return;if(37===r||39===r){return void n({h:e,s:(0,L.clamp)(37===r?o-N:o+N,0,1),v:i})}n({h:e,s:o,v:(0,L.clamp)(40===r?i-N:i+N,0,1)})},this._mouseDown=t=>{window.addEventListener("mouseup",this._mouseUp),window.addEventListener("mousemove",this._mouseMove)},this._mouseUp=t=>{window.removeEventListener("mousemove",this._mouseMove),window.removeEventListener("mouseup",this._mouseUp),this._handlePosition(t)},this._mouseMove=(0,B.default)(this._handlePosition,100),this._handleTouch=t=>{this._handlePosition(t.nativeEvent.touches[0])}}render(){const{className:t,hsv:{h:e,s:o,v:n}}=this.props,s=`hsl(${360*e}, 100%, 50%)`;return i.createElement("div",{tabIndex:0,className:r()(W.accessible,t),onKeyDown:this._handleKeyDown},i.createElement("div",{className:W.saturation,style:{backgroundColor:s},ref:this._refContainer,onMouseDown:this._mouseDown,onTouchStart:this._handleTouch,onTouchMove:this._handleTouch},i.createElement("div",{className:W.pointer,style:{left:100*o+"%",top:100*(1-n)+"%"}})))}}var I=o(30338);class R extends i.PureComponent{constructor(){super(...arguments),this._container=null,this._refContainer=t=>{this._container=t},this._handlePosition=t=>{const{hsv:{s:e,v:o},onChange:i}=this.props;if(!i)return;const n=(0,_.ensureNotNull)(this._container).getBoundingClientRect(),r=t.clientY-n.top;i({h:(0,L.clamp)(r/n.height,0,1),s:e,v:o})},this._handleKeyDown=t=>{const{hsv:{h:e,s:o,v:i},onChange:n}=this.props,r=(0,D.hashFromEvent)(t);if(!n||38!==r&&40!==r)return;n({h:(0,L.clamp)(38===r?e-.01:e+.01,0,1),s:o,v:i})},this._mouseDown=t=>{window.addEventListener("mouseup",this._mouseUp),window.addEventListener("mousemove",this._mouseMove)},this._mouseUp=t=>{window.removeEventListener("mousemove",this._mouseMove),window.removeEventListener("mouseup",this._mouseUp),this._handlePosition(t)},this._mouseMove=(0,B.default)(this._handlePosition,100),this._handleTouch=t=>{this._handlePosition(t.nativeEvent.touches[0])}}render(){const{className:t,hsv:{h:e}}=this.props;return i.createElement("div",{className:r()(I.hue,I.accessible,t),tabIndex:0,onKeyDown:this._handleKeyDown},i.createElement("div",{className:I.pointerContainer,ref:this._refContainer,onMouseDown:this._mouseDown,onTouchStart:this._handleTouch,onTouchMove:this._handleTouch},i.createElement("div",{className:I.pointer,style:{top:100*e+"%"}})))}}var A=o(11988);const F="#000000",V=s.t(null,{context:"Color Picker"},o(55517));class O extends i.PureComponent{constructor(t){super(t),this._inputRef=i.createRef(),this._handleHSV=t=>{const e=function(t){const{h:e,s:o,v:i}=t;let n,r,s;const a=Math.floor(6*e),l=6*e-a,c=i*(1-o),h=i*(1-l*o),d=i*(1-(1-l)*o);switch(a%6){case 0:n=i,r=d,s=c;break;case 1:n=h,r=i,s=c
;break;case 2:n=c,r=i,s=d;break;case 3:n=c,r=h,s=i;break;case 4:n=d,r=c,s=i;break;case 5:n=i,r=c,s=h;break;default:n=0,r=0,s=0}return"#"+[255*n,255*r,255*s].map((t=>("0"+Math.round(t).toString(16)).replace(/.+?([a-f0-9]{2})$/i,"$1"))).join("")}(t)||F;this.setState({color:e,inputColor:z(e),hsv:t}),this.props.onSelect(e)},this._handleInput=t=>{const e=z(t.currentTarget.value);try{const t=P(e),o=`#${e}`;this.setState({color:o,inputColor:e,hsv:t}),this.props.onSelect(o)}catch(t){this.setState({inputColor:e})}},this._handleAddColor=()=>this.props.onAdd(this.state.color);const e=t.color||F;this.state={color:e,inputColor:z(e),hsv:P(e)}}componentDidMount(){var t;g.CheckMobile.any()||null===(t=this._inputRef.current)||void 0===t||t.focus()}render(){const{color:t,hsv:e,inputColor:o}=this.state;return i.createElement("div",{className:A.container},i.createElement("div",{className:A.form},i.createElement("div",{className:A.swatch,style:{backgroundColor:t}}),i.createElement("div",{className:A.inputWrap},i.createElement("span",{className:A.inputHash},"#"),i.createElement("input",{ref:this._inputRef,type:"text",className:A.input,value:o,onChange:this._handleInput})),i.createElement("div",{className:A.buttonWrap},i.createElement(E.Button,{size:"s",onClick:this._handleAddColor},V))),i.createElement("div",{className:A.hueSaturationWrap},i.createElement(M,{className:A.saturation,hsv:e,onChange:this._handleHSV}),i.createElement(R,{className:A.hue,hsv:e,onChange:this._handleHSV})))}}function z(t){return t.replace(/^#/,"")}var U=o(94996);const H=s.t(null,{context:"Color Picker"},o(29619)),Z=s.t(null,{context:"Color Picker"},o(80936));function j(t){const{color:e,opacity:o,selectCustom:n,selectOpacity:s,customColors:l,onRemoveCustomColor:c,onToggleCustom:h,onOpacityChange:d,menu:p}=t,[u,m]=(0,i.useState)(!1),_="number"==typeof o?o:1,[g,v]=(0,a.useRowsNavigation)();return(0,i.useLayoutEffect)((()=>{p&&p.update()}),[s,p]),u?i.createElement(O,{color:e,onSelect:y,onAdd:function(e){m(!1),null==h||h(!1);const{onAddColor:o}=t;o&&o(e)}}):i.createElement("div",{className:U.container},i.createElement("div",{ref:g,onKeyDown:v},i.createElement(T,{colors:f.basic,color:e,onSelect:y}),i.createElement(T,{colors:f.extended,color:e,onSelect:y}),i.createElement("div",{className:U.separator}),i.createElement(T,{colors:l,color:e,onSelect:y,onRemoveCustomColor:c},n&&i.createElement(i.Fragment,null,(null==l?void 0:l.length)?i.createElement("button",{title:H,onClick:b,className:r()(U.customButton,U.accessible,"apply-common-tooltip"),tabIndex:-1}):i.createElement("div",{"data-role":"row"},i.createElement("button",{title:H,onClick:b,className:r()(U.customButton,U.accessible,"apply-common-tooltip"),tabIndex:-1}))))),s&&i.createElement(i.Fragment,null,i.createElement("div",{className:U.sectionTitle},Z),i.createElement(S.Opacity,{color:e,opacity:_,onChange:function(t){d&&d(t)}})));function y(e){const{onColorChange:o}=t;o&&o(e,u)}function b(t){m(!0),null==h||h(!0)}}},54368:(t,e,o)=>{"use strict";o.d(e,{Opacity:()=>c})
;var i=o(50959),n=o(97754),r=o(50151),s=o(9859),a=o(68335),l=o(3252);class c extends i.PureComponent{constructor(t){super(t),this._container=null,this._pointer=null,this._raf=null,this._refContainer=t=>{this._container=t},this._refPointer=t=>{this._pointer=t},this._handlePosition=t=>{null===this._raf&&(this._raf=requestAnimationFrame((()=>{const e=(0,r.ensureNotNull)(this._container),o=(0,r.ensureNotNull)(this._pointer),i=e.getBoundingClientRect(),n=o.offsetWidth,a=t.clientX-n/2-i.left,l=(0,s.clamp)(a/(i.width-n),0,1);this.setState({inputOpacity:Math.round(100*l).toString()}),this.props.onChange(l),this._raf=null})))},this._onSliderClick=t=>{this._handlePosition(t.nativeEvent),this._dragSubscribe()},this._mouseUp=t=>{this.setState({isPointerDragged:!1}),this._dragUnsubscribe(),this._handlePosition(t)},this._mouseMove=t=>{this.setState({isPointerDragged:!0}),this._handlePosition(t)},this._onTouchStart=t=>{this._handlePosition(t.nativeEvent.touches[0])},this._handleTouch=t=>{this.setState({isPointerDragged:!0}),this._handlePosition(t.nativeEvent.touches[0])},this._handleTouchEnd=()=>{this.setState({isPointerDragged:!1})},this._handleInput=t=>{const e=t.currentTarget.value,o=Number(e)/100;this.setState({inputOpacity:e}),Number.isNaN(o)||o>1||this.props.onChange(o)},this._handleKeyDown=t=>{const e=(0,a.hashFromEvent)(t);if(37!==e&&39!==e)return;t.preventDefault();const o=Number(this.state.inputOpacity);37===e&&0!==o&&this._changeOpacity(o-1),39===e&&100!==o&&this._changeOpacity(o+1)},this.state={inputOpacity:Math.round(100*t.opacity).toString(),isPointerDragged:!1}}componentWillUnmount(){null!==this._raf&&(cancelAnimationFrame(this._raf),this._raf=null),this._dragUnsubscribe()}render(){const{color:t,opacity:e,hideInput:o,disabled:r}=this.props,{inputOpacity:s,isPointerDragged:a}=this.state,c={color:t||void 0};return i.createElement("div",{className:l.opacity},i.createElement("div",{className:n(l.opacitySlider,l.accessible),style:c,tabIndex:r?-1:0,ref:this._refContainer,onMouseDown:this._onSliderClick,onTouchStart:this._onTouchStart,onTouchMove:this._handleTouch,onTouchEnd:this._handleTouchEnd,onKeyDown:this._handleKeyDown,"aria-disabled":r},i.createElement("div",{className:l.opacitySliderGradient,style:{backgroundImage:`linear-gradient(90deg, transparent, ${t})`}}),i.createElement("div",{className:l.opacityPointerWrap},i.createElement("div",{className:n(l.pointer,a&&l.dragged),style:{left:100*e+"%"},ref:this._refPointer}))),!o&&i.createElement("div",{className:l.opacityInputWrap},i.createElement("input",{type:"text",className:l.opacityInput,value:s,onChange:this._handleInput}),i.createElement("span",{className:l.opacityInputPercent},"%")))}_dragSubscribe(){const t=(0,r.ensureNotNull)(this._container).ownerDocument;t&&(t.addEventListener("mouseup",this._mouseUp),t.addEventListener("mousemove",this._mouseMove))}_dragUnsubscribe(){const t=(0,r.ensureNotNull)(this._container).ownerDocument;t&&(t.removeEventListener("mousemove",this._mouseMove),t.removeEventListener("mouseup",this._mouseUp))}_changeOpacity(t){this.setState({
inputOpacity:t.toString()}),this.props.onChange(t/100)}}},6914:(t,e,o)=>{"use strict";o.d(e,{basic:()=>a,extended:()=>c,white:()=>n});var i=o(19625);const n=i.colorsPalette["color-white"],r=["ripe-red","tan-orange","banana-yellow","iguana-green","minty-green","sky-blue","tv-blue","deep-blue","grapes-purple","berry-pink"],s=[200,300,400,500,600,700,800,900].map((t=>`color-cold-gray-${t}`));s.unshift("color-white"),s.push("color-black"),r.forEach((t=>{s.push(`color-${t}-500`)}));const a=s.map((t=>i.colorsPalette[t])),l=[];[100,200,300,400,700,900].forEach((t=>{r.forEach((e=>{l.push(`color-${e}-${t}`)}))}));const c=l.map((t=>i.colorsPalette[t]))},56512:(t,e,o)=>{"use strict";o.d(e,{useCustomColors:()=>c});var i=o(50959),n=o(56840),r=o(76422);function s(t,e){(0,i.useEffect)((()=>(r.subscribe(t,e,null),()=>{r.unsubscribe(t,e,null)})),[t,e])}var a,l=o(24377);function c(){const[t,e]=(0,i.useState)((0,n.getJSON)("pickerCustomColors",[]));s("add_new_custom_color",(o=>e(h(o,t)))),s("remove_custom_color",(o=>e(d(o,t))));const o=(0,i.useCallback)((e=>{const o=e?(0,l.parseRgb)(e):null;t.some((t=>null!==t&&null!==o&&(0,l.areEqualRgb)((0,l.parseRgb)(t),o)))||(r.emit("add_new_custom_color",e),(0,n.setJSON)("pickerCustomColors",h(e,t)))}),[t]),a=(0,i.useCallback)((e=>{(e>=0||e<t.length)&&(r.emit("remove_custom_color",e),(0,n.setJSON)("pickerCustomColors",d(e,t)))}),[t]);return[t,o,a]}function h(t,e){const o=e.slice();return o.push(t),o.length>29&&o.shift(),o}function d(t,e){return e.filter(((e,o)=>t!==o))}!function(t){t.SettingsKey="pickerCustomColors",t.GlobalAddEventName="add_new_custom_color",t.GlobalRemoveEventName="remove_custom_color",t[t.MaxColors=29]="MaxColors"}(a||(a={}))},84877:(t,e,o)=>{"use strict";o.d(e,{MatchMediaMap:()=>s});var i=o(50959),n=o(66783),r=o.n(n);class s extends i.Component{constructor(t){super(t),this._handleMediaChange=()=>{const t=l(this.state.queries,((t,e)=>e.matches));let e=!1;for(const o in t)if(t.hasOwnProperty(o)&&this.state.matches[o]!==t[o]){e=!0;break}e&&this.setState({matches:t})};const{rules:e}=this.props;this.state=a(e)}shouldComponentUpdate(t,e){return!r()(t,this.props)||(!r()(e.rules,this.state.rules)||!r()(e.matches,this.state.matches))}componentDidMount(){this._migrate(null,this.state.queries)}componentDidUpdate(t,e){r()(t.rules,this.props.rules)||this._migrate(e.queries,this.state.queries)}componentWillUnmount(){this._migrate(this.state.queries,null)}render(){return this.props.children(this.state.matches)}static getDerivedStateFromProps(t,e){if(r()(t.rules,e.rules))return null;const{rules:o}=t;return a(o)}_migrate(t,e){null!==t&&l(t,((t,e)=>{e.removeEventListener("change",this._handleMediaChange)})),null!==e&&l(e,((t,e)=>{e.addEventListener("change",this._handleMediaChange)}))}}function a(t){const e=l(t,((t,e)=>window.matchMedia(e)));return{queries:e,matches:l(e,((t,e)=>e.matches)),rules:{...t}}}function l(t,e){const o={};for(const i in t)t.hasOwnProperty(i)&&(o[i]=e(i,t[i]));return o}},86431:(t,e,o)=>{"use strict";o.d(e,{makeOverlapable:()=>r});var i=o(50959),n=o(42842)
;function r(t,e){return class extends i.PureComponent{render(){const{isOpened:o,root:r}=this.props;if(!o)return null;const s=i.createElement(t,{...this.props,zIndex:150});return"parent"===r?s:i.createElement(n.Portal,{shouldTrapFocus:e},s)}}}},10381:(t,e,o)=>{"use strict";o.d(e,{ToolWidgetCaret:()=>l});var i=o(50959),n=o(97754),r=o(9745),s=o(62794),a=o(578);function l(t){const{dropped:e,className:o}=t;return i.createElement(r.Icon,{className:n(o,s.icon,{[s.dropped]:e}),icon:a})}},40173:(t,e,o)=>{"use strict";function i(t,e,o={}){return Object.assign({},t,function(t,e,o={}){const i=Object.assign({},e);for(const n of Object.keys(e)){const r=o[n]||n;r in t&&(i[n]=[t[r],e[n]].join(" "))}return i}(t,e,o))}o.d(e,{mergeThemes:()=>i})},38177:t=>{t.exports={button:"button-GwQQdU8S",hover:"hover-GwQQdU8S",clicked:"clicked-GwQQdU8S",isInteractive:"isInteractive-GwQQdU8S",accessible:"accessible-GwQQdU8S",isGrouped:"isGrouped-GwQQdU8S",isActive:"isActive-GwQQdU8S",isOpened:"isOpened-GwQQdU8S",isDisabled:"isDisabled-GwQQdU8S",text:"text-GwQQdU8S",icon:"icon-GwQQdU8S",endIcon:"endIcon-GwQQdU8S"}},52495:t=>{t.exports={button:"button-xNqEcuN2"}},94878:t=>{t.exports={button:"button-merBkM5y",hover:"hover-merBkM5y",clicked:"clicked-merBkM5y",accessible:"accessible-merBkM5y",arrow:"arrow-merBkM5y",arrowWrap:"arrowWrap-merBkM5y",isOpened:"isOpened-merBkM5y"}},34426:t=>{t.exports={dots:"dots-meVFo3Y9"}},33335:t=>{t.exports={mobile:"screen and (max-width: 567px)"}},78135:(t,e,o)=>{"use strict";o.d(e,{HorizontalAttachEdge:()=>n,HorizontalDropDirection:()=>s,VerticalAttachEdge:()=>i,VerticalDropDirection:()=>r,getPopupPositioner:()=>c});var i,n,r,s,a=o(50151);!function(t){t[t.Top=0]="Top",t[t.Bottom=1]="Bottom",t[t.AutoStrict=2]="AutoStrict"}(i||(i={})),function(t){t[t.Left=0]="Left",t[t.Right=1]="Right"}(n||(n={})),function(t){t[t.FromTopToBottom=0]="FromTopToBottom",t[t.FromBottomToTop=1]="FromBottomToTop"}(r||(r={})),function(t){t[t.FromLeftToRight=0]="FromLeftToRight",t[t.FromRightToLeft=1]="FromRightToLeft"}(s||(s={}));const l={verticalAttachEdge:i.Bottom,horizontalAttachEdge:n.Left,verticalDropDirection:r.FromTopToBottom,horizontalDropDirection:s.FromLeftToRight,verticalMargin:0,horizontalMargin:0,matchButtonAndListboxWidths:!1};function c(t,e){return o=>{var c,h;const{contentWidth:d,contentHeight:p,availableHeight:u}=o,m=(0,a.ensureNotNull)(t).getBoundingClientRect(),{horizontalAttachEdge:_=l.horizontalAttachEdge,horizontalDropDirection:g=l.horizontalDropDirection,horizontalMargin:v=l.horizontalMargin,verticalMargin:y=l.verticalMargin,matchButtonAndListboxWidths:f=l.matchButtonAndListboxWidths}=e;let b=null!==(c=e.verticalAttachEdge)&&void 0!==c?c:l.verticalAttachEdge,w=null!==(h=e.verticalDropDirection)&&void 0!==h?h:l.verticalDropDirection;b===i.AutoStrict&&(u<m.y+m.height+y+p?(b=i.Top,w=r.FromBottomToTop):(b=i.Bottom,w=r.FromTopToBottom));const C=b===i.Top?-1*y:y,x=_===n.Right?m.right:m.left,T=b===i.Top?m.top:m.bottom,S={x:x-(g===s.FromRightToLeft?d:0)+v,y:T-(w===r.FromBottomToTop?p:0)+C};return f&&(S.overrideWidth=m.width),S}}},
81348:(t,e,o)=>{"use strict";o.d(e,{DEFAULT_TOOL_WIDGET_BUTTON_THEME:()=>a,ToolWidgetButton:()=>l});var i=o(50959),n=o(97754),r=o(9745),s=o(38177);const a=s,l=i.forwardRef(((t,e)=>{const{tag:o="div",icon:a,endIcon:l,isActive:c,isOpened:h,isDisabled:d,isGrouped:p,isHovered:u,isClicked:m,onClick:_,text:g,textBeforeIcon:v,title:y,theme:f=s,className:b,forceInteractive:w,inactive:C,"data-name":x,"data-tooltip":T,...S}=t,E=n(b,f.button,(y||T)&&"apply-common-tooltip",{[f.isActive]:c,[f.isOpened]:h,[f.isInteractive]:(w||Boolean(_))&&!d&&!C,[f.isDisabled]:Boolean(d||C),[f.isGrouped]:p,[f.hover]:u,[f.clicked]:m}),P=a&&("string"==typeof a?i.createElement(r.Icon,{className:f.icon,icon:a}):i.cloneElement(a,{className:n(f.icon,a.props.className)}));return"button"===o?i.createElement("button",{...S,ref:e,type:"button",className:n(E,f.accessible),disabled:d&&!C,onClick:_,title:y,"data-name":x,"data-tooltip":T},v&&g&&i.createElement("div",{className:n("js-button-text",f.text)},g),P,!v&&g&&i.createElement("div",{className:n("js-button-text",f.text)},g)):i.createElement("div",{...S,ref:e,"data-role":"button",className:E,onClick:d?void 0:_,title:y,"data-name":x,"data-tooltip":T},v&&g&&i.createElement("div",{className:n("js-button-text",f.text)},g),P,!v&&g&&i.createElement("div",{className:n("js-button-text",f.text)},g),l&&i.createElement(r.Icon,{icon:l,className:s.endIcon}))}))},56388:(t,e,o)=>{"use strict";o.d(e,{ToolWidgetIconButton:()=>a});var i=o(50959),n=o(97754),r=o(81348),s=o(52495);const a=i.forwardRef((function(t,e){const{className:o,id:a,...l}=t;return i.createElement(r.ToolWidgetButton,{id:a,"data-name":a,...l,ref:e,className:n(o,s.button)})}))},20626:(t,e,o)=>{"use strict";o.d(e,{ToolWidgetMenu:()=>v});var i=o(50959),n=o(97754),r=o(3343),s=o(20520),a=o(10381),l=o(90186),c=o(37558),h=o(41590),d=o(78135),p=o(90692),u=o(56570),m=o(76460),_=o(94878);var g;!function(t){t[t.Vertical=2]="Vertical",t[t.Horizontal=0]="Horizontal"}(g||(g={}));class v extends i.PureComponent{constructor(t){super(t),this._wrapperRef=null,this._controller=i.createRef(),this._onPopupCloseOnClick=t=>{"keyboard"===t.detail.clickType&&this.focus()},this._handleMenuFocus=t=>{var e,o;t.relatedTarget===this._wrapperRef&&this.setState((t=>({...t,isOpenedByButton:!0}))),null===(o=(e=this.props).onMenuFocus)||void 0===o||o.call(e,t)},this._handleWrapperRef=t=>{this._wrapperRef=t,this.props.reference&&this.props.reference(t)},this._handleOpen=()=>{var t,e,o;"div"!==this.props.tag&&(this.setState((t=>({...t,isOpenedByButton:!1}))),null===(e=null===(t=this.props.menuReference)||void 0===t?void 0:t.current)||void 0===e||e.addEventListener("popup-menu-close-event",this._onPopupCloseOnClick),null===(o=this._controller.current)||void 0===o||o.focus())},this._handleClick=t=>{(u.enabled("skip_event_target_check")||t.target instanceof Node)&&t.currentTarget.contains(t.target)&&(this._handleToggleDropdown(void 0,(0,m.isKeyboardClick)(t)),this.props.onClick&&this.props.onClick(t,!this.state.isOpened))},this._handleToggleDropdown=(t,e=!1)=>{
const{onClose:o,onOpen:i}=this.props,{isOpened:n}=this.state,r="boolean"==typeof t?t:!n;this.setState({isOpened:r,shouldReturnFocus:!!r&&e}),r&&i&&i(),!r&&o&&o()},this._handleClose=()=>{this.close()},this._handleKeyDown=t=>{var e;const{orientation:o="horizontal"}=this.props;if(t.defaultPrevented)return;if(!(t.target instanceof Node))return;const i=(0,r.hashFromEvent)(t);if(t.currentTarget.contains(t.target))switch(i){case 40:if("div"===this.props.tag||"horizontal"!==o)return;if(this.state.isOpened)return;t.preventDefault(),this._handleToggleDropdown(!0,!0);break;case 27:if(!this.state.isOpened||!this.props.closeOnEsc)return;t.preventDefault(),t.stopPropagation(),this._handleToggleDropdown(!1)}else{if("div"===this.props.tag)return;switch(i){case 27:{t.preventDefault();const{shouldReturnFocus:o,isOpenedByButton:i}=this.state;this._handleToggleDropdown(!1),o&&i&&(null===(e=this._wrapperRef)||void 0===e||e.focus());break}}}},this.state={isOpened:!1,shouldReturnFocus:!1,isOpenedByButton:!1}}render(){const{tag:t="div",id:e,arrow:o,content:r,isDisabled:s,isDrawer:c,isShowTooltip:h,title:d,className:u,hotKey:m,theme:_,drawerBreakpoint:g,tabIndex:v,isClicked:f}=this.props,{isOpened:b}=this.state,w=n(u,_.button,{"apply-common-tooltip":h||!s,[_.isDisabled]:s,[_.isOpened]:b,[_.clicked]:f}),C=y(r)?r({isOpened:b}):r;return"button"===t?i.createElement("button",{type:"button",id:e,className:n(w,_.accessible),disabled:s,onClick:this._handleClick,title:d,"data-tooltip-hotkey":m,ref:this._handleWrapperRef,onKeyDown:this._handleKeyDown,tabIndex:v,...(0,l.filterDataProps)(this.props),...(0,l.filterAriaProps)(this.props)},C,o&&i.createElement("div",{className:_.arrow},i.createElement("div",{className:_.arrowWrap},i.createElement(a.ToolWidgetCaret,{dropped:b}))),this.state.isOpened&&(g?i.createElement(p.MatchMedia,{rule:g},(t=>this._renderContent(t))):this._renderContent(c))):i.createElement("div",{id:e,className:w,onClick:s?void 0:this._handleClick,title:d,"data-tooltip-hotkey":m,ref:this._handleWrapperRef,"data-role":"button",tabIndex:v,onKeyDown:this._handleKeyDown,...(0,l.filterDataProps)(this.props)},C,o&&i.createElement("div",{className:_.arrow},i.createElement("div",{className:_.arrowWrap},i.createElement(a.ToolWidgetCaret,{dropped:b}))),this.state.isOpened&&this._renderContent(c))}close(){var t,e;null===(e=null===(t=this.props.menuReference)||void 0===t?void 0:t.current)||void 0===e||e.removeEventListener("popup-menu-close-event",this._onPopupCloseOnClick),this._handleToggleDropdown(!1)}focus(){var t;null===(t=this._wrapperRef)||void 0===t||t.focus()}update(){null!==this._controller.current&&this._controller.current.update()}_renderContent(t){const{menuDataName:e,minWidth:o,menuClassName:n,maxHeight:r,drawerPosition:a="Bottom",children:l}=this.props,{isOpened:p}=this.state,u={horizontalMargin:this.props.horizontalMargin||0,verticalMargin:this.props.verticalMargin||2,verticalAttachEdge:this.props.verticalAttachEdge,horizontalAttachEdge:this.props.horizontalAttachEdge,verticalDropDirection:this.props.verticalDropDirection,
horizontalDropDirection:this.props.horizontalDropDirection,matchButtonAndListboxWidths:this.props.matchButtonAndListboxWidths},m=Boolean(p&&t&&a),_=y(l)?l({isDrawer:m}):l;return m?i.createElement(c.DrawerManager,null,i.createElement(h.Drawer,{reference:this.props.drawerReference,onClose:this._handleClose,position:a,"data-name":e},_)):i.createElement(s.PopupMenu,{reference:this.props.menuReference,controller:this._controller,closeOnClickOutside:this.props.closeOnClickOutside,doNotCloseOn:this,isOpened:p,minWidth:o,onClose:this._handleClose,position:(0,d.getPopupPositioner)(this._wrapperRef,u),className:n,maxHeight:r,"data-name":e,tabIndex:"div"!==this.props.tag?-1:void 0,onOpen:this._handleOpen,onKeyDown:this.props.onMenuKeyDown,onFocus:this._handleMenuFocus},_)}}function y(t){return"function"==typeof t}v.defaultProps={arrow:!0,closeOnClickOutside:!0,theme:_}},17815:(t,e,o)=>{"use strict";o.r(e),o.d(e,{FavoriteDrawingToolbar:()=>P});var i=o(50959),n=o(32227),r=o(11542),s=o(10540),a=o(26744),l=(o(50151),o(49481)),c=o(97702),h=o(40443),d=o(928),p=o(2627),u=o(56840),m=o(11417),_=o(64147),g=o(38780),v=o(630),y=o(9745),f=o(20626),b=o(10838);const w={Mobile:o(33335).mobile};var C=o(77975),x=o(97754),T=o(44996),S=o(34426);function E(t){const{title:e,actions:o,className:n,onActionClick:r}=t,s=(0,C.useWatchedValueReadonly)({watchedValue:d.tool}),a=o.map((t=>function(t,e,o){return i.createElement(b.AccessibleMenuItem,{key:t,onClick:()=>o(t),label:p.lineToolsInfo[t].localizedName,icon:p.lineToolsInfo[t].icon,isActive:t===e})}(t,s,r)));return i.createElement(f.ToolWidgetMenu,{className:x(n,S.dots),arrow:!1,title:e,drawerBreakpoint:w.Mobile,content:i.createElement(y.Icon,{icon:T}),"data-name":"more",menuDataName:"more-menu",closeOnEsc:!0},a)}o(51179);class P extends s.FloatingToolbar{constructor(t){super({allowSortable:!0,dragOnlyInsideToolbar:!0,defaultPosition:t,positionSettingsKey:"chart.favoriteDrawingsPosition",positionStorageType:"device"}),this._linetoolsWidgets={},this._canBeShownValue=new _.WatchedValue(!1),this._reactContainer=null,this._onActionClick=async t=>{await(0,v.initLineTool)(t),h.ContextMenuManager.hideAll(),d.tool.value()!==t&&d.tool.setValue(t)},this._createLineToolRenderer=()=>{let t=!0;const e=window.innerHeight>window.innerWidth,o=window.innerWidth>window.innerHeight,i=window.innerHeight===window.innerWidth;return n=>{if(t){const r=this._createLinetoolWidget(n);this.addWidget(r),e&&this.isVertical()||o&&!this.isVertical()||i?(t=this._isWidgetCanBeOnScreen(),t?this._linetoolsWidgets[n]=r:(this.removeWidget(r),this._dropdownItems.push(n))):this._linetoolsWidgets[n]=r}else this._dropdownItems.push(n)}},this._renderToolbarContent=()=>{const t=this._createLineToolRenderer();this._renderWidgetPlug(),this._linetoolsWidgets={},this._dropdownItems=[],this.removeWidgets(),a.LinetoolsFavoritesStore.favorites().filter((t=>p.lineToolsInfo[t]&&!0)).forEach(t),this._dropdownItems.length&&this._renderReactContent(),!this._dropdownItems.length&&this._reactContainer&&n.unmountComponentAtNode(this._reactContainer)},
this._attachHandlers(),this._loadVisibilityState(),this._hideAction=this._createHideToolbarAction(),this._reactContainer=this.getReactWidgetContainer(),this._dropdownItems=[]}show(){this._canBeShownValue.value()&&(super.show(this._renderToolbarContent),window.addEventListener("resize",this._renderToolbarContent))}hide(){window.removeEventListener("resize",this._renderToolbarContent),super.hide()}showAndSaveSettingsValue(){this._canBeShownValue.value()&&(m.TVLocalStorage.setItem("ChartFavoriteDrawingToolbarWidget.visible","true"),this.show())}hideAndSaveSettingsValue(){m.TVLocalStorage.setItem("ChartFavoriteDrawingToolbarWidget.visible","false"),this.hide()}canBeShown(){return this._canBeShownValue.readonly()}_onFavoriteAdded(t){this.isVisible()&&this._renderToolbarContent(),a.LinetoolsFavoritesStore.favorites().filter(B).length>0&&(this._canBeShownValue.setValue(!0),this.showAndSaveSettingsValue())}_onFavoriteRemoved(t){delete this._linetoolsWidgets[t],this.isVisible()&&this._renderToolbarContent(),0===a.LinetoolsFavoritesStore.favorites().filter(B).length&&(this._canBeShownValue.setValue(!1),this.hide())}_onFavoriteMoved(){this._renderToolbarContent()}_onSelectedLinetoolChanged(t){Object.keys(this._linetoolsWidgets).forEach((e=>{this._linetoolsWidgets[e].classList.toggle("i-active",t===e)})),this._dropdownItems.includes(t)&&this._renderReactContent()}_createLinetoolWidget(t){const e=`<span class="tv-favorited-drawings-toolbar__widget apply-common-tooltip ${t===d.tool.value()?"i-active":""}" title="${p.lineToolsInfo[t].localizedName}" data-name="FavoriteToolbar${t}">${p.lineToolsInfo[t].icon}</span>`,o=(0,l.parseHtmlElement)(e);return o.addEventListener("click",(()=>this._onActionClick(t))),o}_isWidgetCanBeOnScreen(){const t=this._getCorrectedWidgetRect();return this.isVertical()?t.height<window.innerHeight:t.width<window.innerWidth}_renderWidgetPlug(){n.render(i.createElement("div",{className:"tv-favorited-drawings-toolbar__widget"}),this._reactContainer)}_renderReactContent(){n.render(i.createElement(E,{title:r.t(null,void 0,o(37117)),actions:this._dropdownItems,onActionClick:this._onActionClick,className:"tv-favorited-drawings-toolbar__widget"}),this._reactContainer)}_attachHandlers(){a.LinetoolsFavoritesStore.favoriteAdded.subscribe(this,this._onFavoriteAdded),a.LinetoolsFavoritesStore.favoriteRemoved.subscribe(this,this._onFavoriteRemoved),a.LinetoolsFavoritesStore.favoriteMoved.subscribe(this,this._onFavoriteMoved),a.LinetoolsFavoritesStore.favoritesSynced.subscribe(null,(()=>{this._loadVisibilityState(),this._renderToolbarContent()})),this.onWidgetsReordered().subscribe(this,((t,e)=>{const o=a.LinetoolsFavoritesStore.favorite(t);if(o){const t=this._linetoolsWidgets[o];t.classList.remove("clicked"),setTimeout((()=>{t.style.pointerEvents=""}),50)}if(t!==e){if(a.LinetoolsFavoritesStore.favoriteMoved.unsubscribe(this,this._onFavoriteMoved),!a.LinetoolsFavoritesStore.moveFavorite(o,e))throw new Error("Something went wrong");a.LinetoolsFavoritesStore.favoriteMoved.subscribe(this,this._onFavoriteMoved)}})),
this.onSortableStart().subscribe(this,(t=>{const e=a.LinetoolsFavoritesStore.favorite(t);if(!e)return;const o=this._linetoolsWidgets[e];o.classList.add("clicked"),(0,g.hide)(),o.style.pointerEvents="none"})),this.onContextMenu((t=>{t.preventDefault(),h.ContextMenuManager.showMenu([this._hideAction],t)})),d.tool.subscribe(this._onSelectedLinetoolChanged.bind(this))}_createHideToolbarAction(){return new c.Action({actionId:"Chart.FavoriteDrawingToolsToolbar.Hide",options:{label:r.t(null,void 0,o(22688)),onExecute:()=>{this.hideAndSaveSettingsValue()}}})}_loadVisibilityState(){const t=a.LinetoolsFavoritesStore.favorites().filter(B).length>0;this._canBeShownValue.setValue(t);const e=a.LinetoolsFavoritesStore.favoritesCount()>0;let o;const i=u.getValue("ChartFavoriteDrawingToolbarWidget.visible");void 0!==i?(u.remove("ChartFavoriteDrawingToolbarWidget.visible",{forceFlush:!0}),o="false"!==i,m.TVLocalStorage.setItem("ChartFavoriteDrawingToolbarWidget.visible",i)):o="false"!==m.TVLocalStorage.getItem("ChartFavoriteDrawingToolbarWidget.visible"),o&&e?this.show():this.hide()}}function B(t){return!0}},10540:(t,e,o)=>{"use strict";o.d(e,{FLOATING_TOOLBAR_REACT_WIDGETS_CLASS:()=>x,FloatingToolbar:()=>S});var i=o(59064),n=o(32563),r=o(78871),s=o(56840),a=o(52033),l=o(64147),c=o(38881);class h extends c.ChunkLoader{_startLoading(){return Promise.all([o.e(1553),o.e(2377)]).then(o.bind(o,13367)).then((t=>t.HammerJS))}}var d=o(11417),p=o(50151),u=o(56073);o(29399);class m{constructor(t){var e,o;this._helper=null,this._handleDragStart=t=>{var e;if(null!==this._helper)return;const o=this._source;o.classList.add("ui-draggable-dragging");const[i,n]=[(0,u.outerWidth)(o),(0,u.outerHeight)(o)];this._helper={startTop:parseFloat(o.style.top)||0,startLeft:parseFloat(o.style.left)||0,nextTop:null,nextLeft:null,raf:null,size:[i,n],containment:this._containment instanceof HTMLElement?[parseInt(getComputedStyle(this._containment).borderLeftWidth)+parseInt(getComputedStyle(this._containment).paddingLeft),parseInt(getComputedStyle(this._containment).borderTopWidth)+parseInt(getComputedStyle(this._containment).paddingTop),this._containment.offsetWidth-parseInt(getComputedStyle(this._containment).borderRightWidth)-parseInt(getComputedStyle(this._containment).paddingRight)-parseInt(getComputedStyle(o).marginLeft)-parseInt(getComputedStyle(o).marginRight)-i,this._containment.offsetHeight-parseInt(getComputedStyle(this._containment).borderBottomWidth)-parseInt(getComputedStyle(this._containment).paddingBottom)-parseInt(getComputedStyle(o).marginTop)-parseInt(getComputedStyle(o).marginBottom)-n]:"window"===this._containment?[window.scrollX,window.scrollY,window.scrollX+document.documentElement.offsetWidth-i,window.scrollY+document.documentElement.offsetHeight-n]:null},null===(e=this._start)||void 0===e||e.call(this)},this._handleDragMove=t=>{var e;if(null===this._helper)return;const{current:o,initial:i}=t.detail,n=this._source,r=this._helper.nextTop,s=this._helper.nextLeft,a="y"===this._axis||!1===this._axis||0!==o.movementY;if(a){
const t=this._helper.startTop;isFinite(t)&&(this._helper.nextTop=o.clientY-i.clientY+t)}const l="x"===this._axis||!1===this._axis||0!==o.movementY;if(l){const t=this._helper.startLeft;isFinite(t)&&(this._helper.nextLeft=o.clientX-i.clientX+t)}if(null!==this._helper.containment){const[t,e,o,i]=this._helper.containment;a&&this._helper.nextTop&&(this._helper.nextTop=Math.min(this._helper.nextTop,i),this._helper.nextTop=Math.max(this._helper.nextTop,e)),l&&this._helper.nextLeft&&(this._helper.nextLeft=Math.min(this._helper.nextLeft,o),this._helper.nextLeft=Math.max(this._helper.nextLeft,t))}null!==this._helper.raf||r===this._helper.nextTop&&s===this._helper.nextLeft||(this._helper.raf=requestAnimationFrame((()=>{null!==this._helper&&(null!==this._helper.nextTop&&(n.style.top=this._helper.nextTop+"px",this._helper.nextTop=null),null!==this._helper.nextLeft&&(n.style.left=this._helper.nextLeft+"px",this._helper.nextLeft=null),this._helper.raf=null)}))),null===(e=this._drag)||void 0===e||e.call(this)},this._handleDragStop=t=>{var e;if(null===this._helper)return;this._source.classList.remove("ui-draggable-dragging"),this._helper=null,null===(e=this._stop)||void 0===e||e.call(this)};const i=this._source=t.source;i.classList.add("ui-draggable");const n=this._handle=null!==(e=t.handle?i.querySelector(t.handle):null)&&void 0!==e?e:i;n.classList.add("ui-draggable-handle"),this._start=t.start,this._stop=t.stop,this._drag=t.drag,this._backend=new _({handle:n,onDragStart:this._handleDragStart,onDragMove:this._handleDragMove,onDragStop:this._handleDragStop}),this._axis=null!==(o=t.axis)&&void 0!==o&&o,this._containment=t.containment}destroy(){const t=this._source;t.classList.remove("ui-draggable"),t.classList.remove("ui-draggable-dragging");this._handle.classList.remove("ui-draggable-handle"),this._backend.destroy(),null!==this._helper&&(this._helper.raf&&cancelAnimationFrame(this._helper.raf),this._helper=null)}}class _{constructor(t){var e;this._pointerStarted=!1,this._initial=null,this._handlePointerDown=t=>{if(null!==this._initial||0!==t.button)return;if(!(t.target instanceof Element&&this._handle.contains(t.target)))return;if(this._initial=t,!this._distance&&(this._pointerStart(),!this._pointerStarted))return;t.preventDefault();const e=this._getEventTarget();e.addEventListener("pointermove",this._handlePointerMove),e.addEventListener("pointerup",this._handlePointerUp),e.addEventListener("pointercancel",this._handlePointerUp),e.addEventListener("lostpointercapture",this._handleLostPointerCapture)},this._handleLostPointerCapture=t=>{this._getEventTarget()===t.target&&this._handlePointerUp(t)},this._handlePointerMove=t=>{if(null!==this._initial&&this._initial.pointerId===t.pointerId)if(this._pointerStarted)this._pointerDrag(t);else if(this._pointerDistanceMet(t)){if(this._pointerStart(),this._pointerStarted)return void this._pointerDrag(t);this._handlePointerUp(t)}},this._handlePointerUp=t=>{if(null===this._initial||this._initial.pointerId!==t.pointerId)return;t.preventDefault();const e=this._getEventTarget()
;e.removeEventListener("pointermove",this._handlePointerMove),e.removeEventListener("pointerup",this._handlePointerUp),e.removeEventListener("pointercancel",this._handlePointerUp),e.removeEventListener("lostpointercapture",this._handlePointerUp),this._pointerStarted&&(this._pointerStarted=!1,e.releasePointerCapture(this._initial.pointerId),this._dispatchEvent(this._createEvent("pointer-drag-stop",t))),this._initial=null};const o=this._handle=t.handle;this._onDragStart=t.onDragStart,this._onDragMove=t.onDragMove,this._onDragStop=t.onDragStop,this._distance=null!==(e=t.distance)&&void 0!==e?e:0,this._rootElement=t.rootElement,o.style.touchAction="none",o.addEventListener("pointerdown",this._handlePointerDown)}destroy(){const t=this._handle;t.style.touchAction="",t.removeEventListener("pointerdown",this._handlePointerDown),t.removeEventListener("pointermove",this._handlePointerMove),t.removeEventListener("pointerup",this._handlePointerUp),t.removeEventListener("pointercancel",this._handlePointerUp),t.removeEventListener("lostpointercapture",this._handlePointerUp),null!==this._initial&&(t.releasePointerCapture(this._initial.pointerId),this._initial=null),this._pointerStarted=!1}_pointerStart(){if(!this._initial)return;const t=this._getEventTarget();this._dispatchEvent(this._createEvent("pointer-drag-start",this._initial))?(this._pointerStarted=!0,t.setPointerCapture(this._initial.pointerId)):this._initial=null}_pointerDrag(t){t.preventDefault(),this._dispatchEvent(this._createEvent("pointer-drag-move",t))}_pointerDistanceMet(t){return!this._initial||!this._distance||Math.max(Math.abs(this._initial.clientX-t.clientX),Math.abs(this._initial.clientY-t.clientY))>=this._distance}_getEventTarget(){var t;return null!==(t=this._rootElement)&&void 0!==t?t:this._handle}_dispatchEvent(t){switch(t.type){case"pointer-drag-start":this._onDragStart(t);break;case"pointer-drag-move":this._onDragMove(t);break;case"pointer-drag-stop":this._onDragStop(t)}return!t.defaultPrevented}_createEvent(t,e){return(0,p.assert)(null!==this._initial),new CustomEvent(t,{bubbles:!0,cancelable:!0,detail:{backend:this,initial:this._initial,current:e}})}}var g=o(9859),v=o(63273),y=o(14314);class f{constructor(t){this._items=[],this._backends=[],this._helper=null,this._handleDragStart=t=>{var e,o;const i=this._getAllItems()[t];if(!(i instanceof HTMLElement)||null!==this._helper)return;i.style.zIndex="10";const n=this._options.source,r=i.getBoundingClientRect(),s=n.getBoundingClientRect();this._helper={movedIndexes:new Set,draggableIndex:t,leftBorder:s.left+b(n,"padding-left")+b(n,"border-left-width")-r.left-1,rightBorder:s.right-b(n,"padding-right")-b(n,"border-right-width")-r.right+1,topBorder:s.top+b(n,"padding-top")+b(n,"border-top-width")-r.top-1,bottomBorder:s.bottom-b(n,"padding-bottom")-b(n,"border-bottom-width")-r.bottom+1},null===(o=(e=this._options).start)||void 0===o||o.call(e,t)},this._handleDragMove=t=>{if(null===this._helper)return
;const{topBorder:e,bottomBorder:o,leftBorder:i,rightBorder:n,draggableIndex:r}=this._helper,s=this._getAllItems()[r],{current:a,initial:l}=t.detail,{axis:c}=this._options,h=a.clientX-l.clientX,d=a.clientY-l.clientY;if("y"===c){const t=(0,g.clamp)(d,e,o);s.style.transform=`translateY(${t}px)`}if("x"===c){const t=(0,g.clamp)(h,i,n);s.style.transform=`translateX(${t}px)`}this._updateIdleItemsStateAndPosition()},this._handleDragStop=()=>{var t,e;if(null===this._helper)return;this._getAllItems()[this._helper.draggableIndex].style.zIndex="";const o=this._applyNewItemsOrder();this._cleanup(),this._initBackends(),null!==o&&(null===(e=(t=this._options).stop)||void 0===e||e.call(t,o))},this._options=t,this._initBackends()}destroy(){this._cleanup();for(const t of this._backends)t.destroy()}updateOptions(t){(0,y.default)(this._options,t)}updateList(){this._cleanup(),this._initBackends()}_initBackends(){for(const t of this._backends)t.destroy();const t=[];this._getAllItems().forEach(((e,o)=>{t.push(new _({handle:e,onDragStart:()=>this._handleDragStart(o),onDragMove:this._handleDragMove,onDragStop:this._handleDragStop,distance:5,rootElement:window.document.documentElement}))})),this._backends=t}_cleanup(){this._getAllItems().forEach((t=>{t.style.transform=""})),this._helper=null,this._items=[]}_getAllItems(){var t;return(null===(t=this._items)||void 0===t?void 0:t.length)||(this._items=Array.from(this._options.source.children)),this._items}_updateIdleItemsStateAndPosition(){if(null===this._helper)return;const{axis:t}=this._options,{draggableIndex:e}=this._helper,o=this._getAllItems(),i=o[e].getBoundingClientRect(),n=i.top+i.height/2,r=i.left+i.width/2,s="x"===t?r:n,a=new Set;o.forEach(((o,n)=>{if(e===n)return;const r=o.getBoundingClientRect(),l=r.left+r.width/2,c=r.top+r.height/2,h="x"===t?l:c,d=e>n,p=(0,v.isRtl)();let u;if(u=d?p&&s>=h||!p&&s<=h:p&&s<=h||!p&&s>=h,u){a.add(n);const e=d?p?-1:1:p?1:-1;switch(t){case"x":o.style.transform=`translateX(${e*i.width}px)`;break;case"y":o.style.transform=`translateY(${e*i.height}px)`}}else o.style.transform="",a.delete(n)})),this._helper.movedIndexes=a}_applyNewItemsOrder(){if(null===this._helper)return null;const{draggableIndex:t,movedIndexes:e}=this._helper,o=this._getAllItems(),i=o[t];let n=null;const r=[];o.forEach(((o,i)=>{if(i===t)return;const n=t>i;if(!e.has(i))return void(r[i]=o);r[n?i+1:i-1]=o}));for(let t=0;t<o.length;t++){void 0===r[t]&&(n=t,r[t]=i)}return e.size&&r.forEach((t=>{this._options.source.appendChild(t)})),n}}function b(t,e){return parseInt(function(t,e){return getComputedStyle(t,null).getPropertyValue(e)}(t,e))}var w=o(49481),C=o(25388);o(42329);const x="floating-toolbar-react-widgets",T=`<div class="tv-floating-toolbar i-closed i-hidden"><div class="tv-floating-toolbar__widget-wrapper"><div class="tv-floating-toolbar__drag js-drag">${C}</div><div class="tv-floating-toolbar__content js-content"></div><div class="${x}"></div></div></div>`;class S{constructor(t){this._widget=document.createElement("div"),this._isVertical=!1,this._hiddingTimeoutId=null,
this._visibility=new l.WatchedValue(!1),this._windowResizeListener=this._onWindowResize.bind(this),this._reorderedDelegate=new a.Delegate,this._startSortableDelegate=new a.Delegate,this._responsiveResizeFunction=null,this._showTimeStamp=null,this._draggable=null,this._sortable=null,this._preventClickUntilAnimation=t=>{null!==this._showTimeStamp&&performance.now()-this._showTimeStamp<this.hideDuration()&&t.stopPropagation()},S._toolbars.push(this),this._options=t,this._widget=(0,w.parseHtmlElement)(T),this._content=this._widget.getElementsByClassName("js-content").item(0),this._reactWidgetsContainer=this._widget.getElementsByClassName(x).item(0),this._setZIndex(S._startZIndex+S._toolbars.length-1),this._options.addClass&&(this._widget.className+=` ${this._options.addClass}`),this._options["data-name"]&&(this._widget.dataset.name=this._options["data-name"]),this._options.layout&&"auto"!==this._options.layout&&(this._isVertical="vertical"===this._options.layout,this._updateLayoutType()),this._widget.addEventListener("click",this._preventClickUntilAnimation,!0)}destroy(){this.hide(!0),S._toolbars.splice(S._toolbars.indexOf(this),1),this._widget.removeEventListener("click",this._preventClickUntilAnimation,!0),document.body.contains(this._widget)&&document.body.removeChild(this._widget),null!==this._draggable&&this._draggable.destroy(),null!==this._sortable&&this._sortable.destroy(),this._widget.innerHTML="",this._responsiveResizeFunction=null}setResponsiveResizeFunc(t){this._responsiveResizeFunction=t}isVisible(){return this._visibility.value()}visibility(){return this._visibility.readonly()}isVertical(){return this._isVertical}show(t){this.isVisible()||(document.body.contains(this._widget)||(this._init(),document.body.appendChild(this._widget)),this._setHiddingTimeout(null),window.addEventListener("resize",this._windowResizeListener),this.raise(),this._visibility.setValue(!0),this._showTimeStamp=performance.now(),this._widget.classList.contains("i-hidden")?(this._widget.classList.remove("i-hidden"),setTimeout((()=>{this.isVisible()&&(null==t||t(),this._widget.classList.remove("i-closed"))}))):(null==t||t(),this._widget.classList.remove("i-closed")),this._onWindowResize())}hide(t=!1){if(!this.isVisible())return;const e=this._widget.classList.contains("i-closed");if(this._widget.classList.add("i-closed"),this._visibility.setValue(!1),t||e)this._setHiddingTimeout(null),this._widget.classList.add("i-hidden");else{const t=setTimeout((()=>{this._setHiddingTimeout(null),this._widget.classList.add("i-hidden")}),this.hideDuration());this._setHiddingTimeout(t)}window.removeEventListener("resize",this._windowResizeListener)}raise(){S._toolbars.length+S._startZIndex!==this._zIndex()&&(S._toolbars.splice(S._toolbars.indexOf(this),1),S._toolbars.push(this),S._updateAllZIndexes())}hideDuration(){return.75*r.dur}addWidget(t,e={}){var o;const i=this.widgetsCount();if(void 0===e.index&&(e.index=i),e.index<0||e.index>i)throw new Error(`Index must be in [0, ${i}]`);const n=document.createElement("div")
;n.className="tv-floating-toolbar__widget js-widget",n.appendChild(t);const r=e.index===i?null:this._content.childNodes.item(e.index);this._content.insertBefore(n,r),this._onWindowResize(),null===(o=this._sortable)||void 0===o||o.updateList()}getReactWidgetContainer(){return this._reactWidgetsContainer}onWidgetsReordered(){return this._reorderedDelegate}onSortableStart(){return this._startSortableDelegate}removeWidget(t){var e;const o=this._findWrapperForWidget(t);o&&(this._content.removeChild(o),this._onWindowResize(),null===(e=this._sortable)||void 0===e||e.updateList())}widgetsCount(){return this._content.childNodes.length}showWidget(t){const e=this._findWrapperForWidget(t);e&&e.classList.remove("i-hidden")}hideWidget(t){const e=this._findWrapperForWidget(t);e&&e.classList.add("i-hidden")}removeWidgets(){for(;this._content.firstChild;)this._content.removeChild(this._content.firstChild);this._onWindowResize()}onContextMenu(t){if(n.mobiletouch){(new h).load().then((e=>{const o=new e(this._widget);o.get("press").set({time:500}),o.on("press",(e=>{this._preventWidgetTouchEndEvent(),t(e.srcEvent)}))}))}else this._widget.addEventListener("contextmenu",t)}checkPosition(){const t=this._getCorrectedWidgetRect(),e={left:t.left,top:t.top};this._correctPosition(e),t.left===e.left&&t.top===e.top||(this._widget.style.left=e.left+"px",this._widget.style.top=e.top+"px")}_determineCurrentLayoutVertical(t){const e=this._isVertical?t.height:t.width;return window.innerWidth<e&&window.innerWidth<window.innerHeight}_getWidget(){return this._widget}_findWrapperForWidget(t){const e=this._content.getElementsByClassName("js-widget");for(let o=0;o<e.length;++o){const i=e.item(o);if(i.contains(t))return i}return null}_onVerticalChanged(t,e){}_correctPosition(t){const e=this._getCorrectedWidgetRect(),o=this._getSavedPosition(),i=window.innerWidth-e.right,n=window.innerHeight-e.bottom;i<0?t.left=Math.max(0,window.innerWidth-e.width):o&&o.left>t.left&&(t.left=Math.min(t.left+i,o.left)),n<0?t.top=Math.max(0,window.innerHeight-e.height):o&&o.top>t.top&&(t.top=Math.min(t.top+n,o.top))}_getCorrectedWidgetRect(){const t=this._widget.getBoundingClientRect();if(this._widget.classList.contains("i-closed")){const e=1/.925-1,o=t.width*e,i=t.height*e;return{bottom:t.bottom+i/2,height:t.height+i,left:t.left-o/2,right:t.right+o/2,top:t.top-i/2,width:t.width+o}}return t}_getSavedPosition(){var t;let e;if("device"===this._options.positionStorageType){const t=d.TVLocalStorage.getItem(this._options.positionSettingsKey);e=null!==t?JSON.parse(t):null}else e=null!==(t=(0,s.getJSON)(this._options.positionSettingsKey))&&void 0!==t?t:null;return null!==e&&"top"in e&&"left"in e?e:null}_setHiddingTimeout(t){null!==this._hiddingTimeoutId&&clearTimeout(this._hiddingTimeoutId),this._hiddingTimeoutId=t}_preventWidgetTouchEndEvent(){const t=e=>{e.preventDefault(),this._widget.removeEventListener("touchend",t)};this._widget.addEventListener("touchend",t)}_updateLayoutType(){this._widget.classList.toggle("i-vertical",this._isVertical)}_updateAxisOption(){
this._sortable&&this._sortable.updateOptions({axis:this._isVertical?"y":"x"})}_onWindowResize(){if("auto"===(this._options.layout||"auto")){const t=this._isVertical,e=this._getCorrectedWidgetRect();this._isVertical=this._determineCurrentLayoutVertical(e),this._updateLayoutType(),t!==this._isVertical&&(this._onVerticalChanged(this._isVertical,t),this._updateAxisOption())}this.checkPosition(),this._resizeResponsive()}_resizeResponsive(){if(null===this._responsiveResizeFunction)return;let t=this._options.layout||"auto";"auto"===t&&(t=this._isVertical?"vertical":"horizontal");const e="vertical"===t?this._widget.clientHeight:this._widget.clientWidth,o=("vertical"===t?window.innerHeight:window.innerWidth)-e;this._responsiveResizeFunction(e,o,t)}_setZIndex(t){this._widget.style.zIndex=String(t)}_zIndex(){return Number(this._widget.style.zIndex)}_loadPosition(){var t;const e=null!==(t=this._getSavedPosition())&&void 0!==t?t:this._options.defaultPosition;this._widget.style.left=Math.round(e.left)+"px",this._widget.style.top=Math.round(e.top)+"px",this._onWindowResize()}_savePosition(){const t=this._widget.getBoundingClientRect();if("device"===this._options.positionStorageType)try{d.TVLocalStorage.setItem(this._options.positionSettingsKey,JSON.stringify({left:t.left,top:t.top}))}catch(t){}else(0,s.setJSON)(this._options.positionSettingsKey,{left:t.left,top:t.top})}_init(){this._loadPosition(),this._draggable=new m({source:this._widget,containment:"window",handle:".js-drag",start:i.globalCloseMenu,stop:this._savePosition.bind(this)}),this._initSortable(),this._widget.addEventListener("pointerdown",this.raise.bind(this))}_initSortable(){if(!this._options.allowSortable)return;let t=-1;this._sortable=new f({source:this._content,axis:this._isVertical?"y":"x",start:e=>{(0,i.globalCloseMenu)(),t=e,this._startSortableDelegate.fire(t)},stop:e=>{this._reorderedDelegate.fire(t,e)}})}static _updateAllZIndexes(){S._toolbars.forEach(((t,e)=>{t._setZIndex(S._startZIndex+e)}))}}S._startZIndex=20,S._toolbars=[]},85497:(t,e,o)=>{"use strict";o.d(e,{LineToolPropertiesWidgetBase:()=>bt});var i=o(50959),n=o(32227),r=o(11542),s=o(45126),a=o(56570),l=o(64147),c=o(37265),h=o(928),d=o(46112),p=o(73305),u=o(50151),m=o(12988);class _ extends m.Property{constructor(t,e,o){super(),this._listenersMappers=[],this._isProcess=!1,this._baseProperty=t,this._propertyApplier=e,this._undoText=o}destroy(){this._baseProperty.destroy(),super.destroy()}value(){return this._baseProperty.value()}setValue(t){this._isProcess=!0,this._baseProperty.setValue(t,void 0,{applyValue:(t,e)=>this._propertyApplier.setProperty(t,e,this._undoText)}),this._isProcess=!1,this._listenersMappers.forEach((t=>{t.method.call(t.obj,this,"")}))}subscribe(t,e){const o=o=>{this._isProcess||e.call(t,this,"")},i={obj:t,method:e,callback:o};this._listenersMappers.push(i),this._baseProperty.subscribe(t,o)}unsubscribe(t,e){var o;const i=(0,u.ensureDefined)(null===(o=this._listenersMappers.find((o=>o.obj===t&&o.method===e)))||void 0===o?void 0:o.callback);this._baseProperty.unsubscribe(t,i)}
unsubscribeAll(t){this._baseProperty.unsubscribeAll(t)}}var g=o(95166),v=o(97754),y=o.n(v),f=o(84877),b=o(10540),w=o(24437);const C=b.FLOATING_TOOLBAR_REACT_WIDGETS_CLASS+"__button";function x(t){const{templateButton:e,propertyButtons:o,commonButtons:n,isDrawingFinished:r,isToolbarFixed:s,buttonClassName:a,activeChartWidget:l}=t,c=l.hasModel()&&l.model().selection().dataSources();return c&&c.length?i.createElement(f.MatchMediaMap,{rules:{isSmallWidth:w.DialogBreakpoints.TabletSmall,isSmallHeight:"screen and (max-height: 430px)"}},(({isSmallWidth:t,isSmallHeight:e})=>i.createElement(i.Fragment,null,h(),r&&i.createElement(i.Fragment,null,Boolean(o.length)&&o.map(((o,n)=>i.createElement(o.component,{...o.props,key:`${o.props.title}_${n}`,className:y()(C,a),isSmallScreen:t||e,isToolbarFixed:s}))),Boolean(n.length)&&n.map(((o,n)=>{const r=t||e;return r?o.showForSmallScreen?i.createElement(o.component,{...o.props,isSmallScreen:r,key:`${o.props.title}_${n}`,className:y()(C,a)}):null:i.createElement(o.component,{...o.props,key:`${o.props.title}_${n}`,className:y()(C,a)})})))))):h();function h(){return null===e?null:i.createElement(e.component,{...e.props,isToolbarFixed:s,isDrawingFinished:r,className:y()(C,a)})}}var T=o(51768),S=o(56388),E=o(34369);function P(t){const{title:e,activeChartWidget:o,className:n}=t;return i.createElement(S.ToolWidgetIconButton,{className:n,icon:E,title:e,onClick:async function(){(0,T.trackEvent)("GUI","Context action on drawings","Settings");const t=o.model().selection().lineDataSources(),e=t.length;1===e?await o.showChartPropertiesForSource(t[0],void 0,{onWidget:o.onWidget()}):e>1&&await o.showChartPropertiesForSources({sources:t})},"data-name":"settings"})}var B=o(43982),D=o(29835),L=o(31527);function W(t){const{className:e,...o}=t;return i.createElement(D.ToolButton,{className:v(e,L.button),tooltipPosition:"horizontal",...o})}var k=o(65186),N=o(91244);function M(t){const{activeChartWidget:e,className:n}=t,s=e.model().selection().lineDataSources();if(0===s.length)return null;const a=s[0].properties().frozen,l=(0,B.useProperty)(a),c=l?{tooltip:r.t(null,void 0,o(99894)),icon:k}:{tooltip:r.t(null,void 0,o(5837)),icon:N};return i.createElement(W,{className:n,isActive:Boolean(l),onClick:function(){(0,T.trackEvent)("GUI","Context action on drawings","Lock"),e.toggleLockSelectedObject()},"data-name":Boolean(l)?"unlock":"lock",...c})}var I=o(93544);function R(t){const{title:e,activeChartWidget:o,className:n}=t;return i.createElement(S.ToolWidgetIconButton,{className:n,icon:I,title:e,"data-name":"remove",onClick:function(){(0,T.trackEvent)("GUI","Context action on drawings","Remove"),o.removeSelectedSources()}})}var A=o(9745),F=o(5325),V=o(32563),O=o(20626),z=o(75535),U=o(40443),H=o(51330),Z=o(68335),j=o(4741),G=o(97702);function $(t,e){const i=[(0,H.createVisualOrderAction)(t,e),(0,H.createChangeIntervalsVisibilitiesAction)(t,e)],n=function(t,e){const i=[],n=Z.isMacKeyboard?" +":"",s=e.filter((t=>t.cloneable()));s.length>0&&i.push(new G.Action({actionId:"Chart.LineTool.Clone",options:{name:"clone",
icon:o(36296),shortcutHint:Z.humanReadableModifiers(j.Modifiers.Mod)+n+" Drag",label:r.t(null,void 0,o(12537)),onExecute:()=>{t.model().cloneLineTools(s,!1),(0,T.trackEvent)("GUI","Context action on drawings","Clone")}}}));const a=e.filter((t=>t.copiable()));if(a.length>0){const e={name:"copy",label:r.t(null,void 0,o(49680)),shortcutHint:Z.humanReadableModifiers(j.Modifiers.Mod)+n+" C",onExecute:()=>{t.chartWidgetCollection().clipboard.uiRequestCopy(a)}};i.push(new G.Action({actionId:"Chart.Clipboard.CopyLineTools",options:e,id:"Copy"}))}return i}(t,e);if(n.length&&i.push(new G.Separator,...n),e.some((t=>t.isSynchronizable()))){const o=(0,H.createSyncDrawingActions)(t,e);o.length&&i.push(new G.Separator,...o)}return i.push(new G.Separator,(0,H.createActionToggleVisibilityDataSources)(t,e)),i}var K=o(44996);function X(t){const{title:e,activeChartWidget:o,isSmallScreen:n,className:r}=t,s=o.model(),a=s.selection().lineDataSources(),[l,c]=(0,i.useState)([]),h=(0,i.useRef)(null),d=(0,i.useMemo)((()=>new H.ActionsProvider(o)),[o]),p=(0,i.useCallback)((()=>d.contextMenuActionsForSources(a,(0,u.ensureNotNull)(s.paneForSource(a[0])))),[d,a]),m=(0,i.useCallback)((()=>{if(n)return;const t=$(o,a);c(q(t))}),[n,o,a]),_=(0,i.useCallback)((t=>{n&&p().then((e=>{const o=q(e);window.matchMedia(w.DialogBreakpoints.TabletSmall).matches||!F.isAnyMobile?U.ContextMenuManager.showMenu(o,t,{mode:F.isAnyMobile?"drawer":"menu","data-name":"more-menu"},{menuName:"LineToolFloatingToolbarMoreMenu"}):c(o)}))}),[n,p]);return(0,i.useEffect)((()=>{var t;l.length&&(null===(t=h.current)||void 0===t||t.update())}),[l]),i.createElement(O.ToolWidgetMenu,{className:r,ref:h,arrow:!1,onOpen:m,onClick:_,title:e,content:i.createElement(A.Icon,{icon:K}),"data-name":"more",menuDataName:"more-menu",closeOnEsc:!0},i.createElement(z.ActionsTable,{parentIsOpened:!0,items:l}))}function q(t){if(V.touch&&!window.matchMedia("(pointer:fine)").matches){const e=t.filter((t=>"Copy"!==t.id));if(e.length===t.length)return e;const o=[];return e.forEach((t=>{("separator"!==t.type||o.length>0&&"separator"!==o[o.length-1].type)&&o.push(t)})),o}return t}var Q=o(85904),Y=o(78135),J=o(501),tt=o(23851),et=o(57740),ot=o(80427);function it(t){const{property:e,propertyApplier:n,title:s,undoText:a,isToolbarFixed:l,className:c}=t,h=(0,B.useProperty)(e),d=(0,i.useMemo)((()=>[new G.Action({actionId:"Chart.LineTool.Toolbar.ChangeLineStyleToSolid",options:{icon:J,label:r.t(null,void 0,o(3554)),active:Q.LineStyle.Solid===h,onExecute:()=>n.setProperty(e,Q.LineStyle.Solid,a)}}),new G.Action({actionId:"Chart.LineTool.Toolbar.ChangeLineStyleToDashed",options:{icon:tt,label:r.t(null,void 0,o(88123)),active:Q.LineStyle.Dashed===h,onExecute:()=>n.setProperty(e,Q.LineStyle.Dashed,a)}}),new G.Action({actionId:"Chart.LineTool.Toolbar.ChangeLineStyleToDotted",options:{icon:et,label:r.t(null,void 0,o(27390)),active:Q.LineStyle.Dotted===h,onExecute:()=>n.setProperty(e,Q.LineStyle.Dotted,a)}})]),[n,e,h]);return i.createElement(O.ToolWidgetMenu,{className:c,arrow:!1,content:i.createElement(A.Icon,{icon:nt(h)
}),title:s,"data-name":t["data-name"],menuDataName:`${t["data-name"]}-menu`,verticalDropDirection:l?Y.VerticalDropDirection.FromBottomToTop:void 0,horizontalDropDirection:l?Y.HorizontalDropDirection.FromRightToLeft:void 0,horizontalAttachEdge:l?Y.HorizontalAttachEdge.Right:void 0,verticalAttachEdge:l?Y.VerticalAttachEdge.Top:void 0},i.createElement(z.ActionsTable,{items:d}))}function nt(t){switch(t){case Q.LineStyle.Solid:return J;case Q.LineStyle.Dashed:return tt;case Q.LineStyle.Dotted:return et;case"mixed":return ot;default:return""}}const rt=[10,11,12,14,16,20,24,28,32,40];function st(t){const{property:e,propertyApplier:o,title:n,undoText:r,isToolbarFixed:s,className:a}=t,l=(0,B.useProperty)(e),c=rt.map((t=>new G.Action({actionId:"Chart.LineTool.Toolbar.ChangeFontSizeProperty",options:{label:t.toString(),onExecute:()=>o.setProperty(e,t,r),active:t===l}})));return i.createElement(O.ToolWidgetMenu,{arrow:!1,content:l,className:a,title:n,verticalDropDirection:s?Y.VerticalDropDirection.FromBottomToTop:void 0,horizontalDropDirection:s?Y.HorizontalDropDirection.FromRightToLeft:void 0,horizontalAttachEdge:s?Y.HorizontalAttachEdge.Right:void 0,verticalAttachEdge:s?Y.VerticalAttachEdge.Top:void 0,"data-name":t["data-name"],menuDataName:`${t["data-name"]}-menu`},i.createElement(z.ActionsTable,{items:c}))}var at=o(72894),lt=o(630),ct=o(15764),ht=o(66666),dt=o(35923);o(10892);const pt=new s.TranslatedString("change line tool(s) font size",r.t(null,void 0,o(37453))),ut=new s.TranslatedString("change line tool(s) line style",r.t(null,void 0,o(13423))),mt=(new s.TranslatedString("apply drawing template",r.t(null,void 0,o(89720))),new s.TranslatedString("line tool(s) line style",r.t(null,{context:"line tool property name"},o(64974)))),_t=r.t(null,void 0,o(32514)),gt=r.t(null,void 0,o(67410)),vt=r.t(null,void 0,o(37117)),yt=r.t(null,void 0,o(92516)),ft=r.t(null,void 0,o(2573));class bt{constructor(t){this._isDrawingFinished=new l.WatchedValue(!0),this._currentTool=null,this._updateVisibilityTimeout=null,this._lineWidthsProperty=null,this._lineColorsProperty=null,this._currentProperties=null,this._floatingContainer=null,this._floatingToolbarRendered=!1,this._toolbarVisible=!1,this._propertiesVisible=!1,this._templatesButton=null,this._propertyButtons=[],this._commonButtons=[],this._handleSourceEdit=t=>{h.isDirectionalMovementActive.value()||(t?this._floatingToolbar.hide(!0):this._floatingToolbarRendered&&this._floatingToolbar.show())},this._chartWidgetCollection=t,this._floatingToolbar=new b.FloatingToolbar({defaultPosition:{top:at.HEADER_TOOLBAR_HEIGHT_EXPANDED+15,left:window.innerWidth/2},positionSettingsKey:"properties_toolbar.position",positionStorageType:"device",layout:"horizontal","data-name":"drawing-toolbar"}),this._floatingContainer=this._floatingToolbar.getReactWidgetContainer(),this._isToolMovingNowSpawn=h.isToolMovingNow.spawn(),this._isToolEditingNowSpawn=h.isToolEditingNow.spawn(),this._toolSpawn=h.tool.spawn(),this._iconToolSpawn=h.iconTool.spawn(),this._emojiToolSpawn=h.emojiTool.spawn(),
this._selectedSourcesSpawn=this._chartWidgetCollection.selectedSources.spawn(),this._isToolMovingNowSpawn.subscribe(this._handleSourceEdit),this._isToolEditingNowSpawn.subscribe(this._handleSourceEdit),this._toolSpawn.subscribe((t=>this._onToolChanged(t)),{callWithLast:!0}),this._iconToolSpawn.subscribe((()=>this._onToolChanged(h.tool.value()))),this._emojiToolSpawn.subscribe((()=>this._onToolChanged(h.tool.value()))),this._selectedSourcesSpawn.subscribe((()=>this.refresh())),this._chartWidgetCollection.onAboutToBeDestroyed.subscribe(this,this.destroy,!0)}destroy(){this._isToolMovingNowSpawn.destroy(),this._isToolEditingNowSpawn.destroy(),this._toolSpawn.destroy(),this._iconToolSpawn.destroy(),this._emojiToolSpawn.destroy(),this._selectedSourcesSpawn.destroy()}refresh(){this._onSourceChanged(this.selectedSources())}activeChartWidget(){return this._chartWidgetCollection.activeChartWidget.value()}selectedSources(){return this._chartWidgetCollection.selectedSources.value().filter(lt.isLineTool)}hide(){this._updateVisibilityTimeout&&clearTimeout(this._updateVisibilityTimeout),this._updateVisibilityTimeout=setTimeout((()=>{(0,lt.unsetNewToolProperties)(),this._floatingToolbar.hide(!0),this._isToolbarRendered()&&this._unmountFloatingToolbar(),this._clearProperties(),this._clearCommonButtons()}),0),delete this._propertyApplier}templatesList(){return this._templatesList}_onToolChanged(t,e){this._currentTool=t;const o=this.selectedSources();this._isDrawingToolExcludingCustomUrlEventTool(t)?(this._isDrawingFinished.setValue(!1),this._updateVisibility()):o&&o.length?(o.length>1&&this._isDrawingFinished.setValue(!0),this.refresh()):this.hide()}_onSourceChanged(t){if(!(null==t?void 0:t.length))return this._propertiesVisible=!1,this._toolbarVisible=!1,void this.hide();if(this._createCommonButtons(),t.every((e=>e.toolname===t[0].toolname))?this._showTemplatesOf({sources:t}):this._templatesButton&&this._clearTemplatesButton(),1===t.length){const[e]=t;e.isAvailableInFloatingWidget()&&this.activeChartWidget().model().model().dataSourceForId(e.id())?(!e.userEditEnabled()||!(0,ct.isLineDrawnWithPressedButton)(e.toolname)&&this.activeChartWidget().model().lineBeingCreated()||this._isDrawingFinished.setValue(!0),this.showPropertiesOf(e.toolname,e.properties(),!0),this._toolbarVisible=!0):this.hide()}else this._clearProperties(),this._createWidthsButton(void 0,!0),this._createLineStyleButton(),this._createColorsButton(void 0,!0),this._createBackgroundsButton(void 0,!0),this._createTextColorsButton(void 0,!0),this._propertiesVisible=!0;this._updateVisibility()}_propertyApplierImpl(){return this._propertyApplier||(this._propertyApplier=new dt.PropertyApplierWithoutSavingChart((()=>this.activeChartWidget().model()))),this._propertyApplier}_clearProperties(){this._clearPropertyButtons(),this._lineWidthsProperty&&(this._lineWidthsProperty.destroy(),this._lineWidthsProperty=null),this._lineColorsProperty&&(this._lineColorsProperty.destroy(),this._lineColorsProperty=null),this._currentProperties&&(this._currentProperties=null)}_show(){
this._updateVisibilityTimeout&&clearTimeout(this._updateVisibilityTimeout),this._updateVisibilityTimeout=setTimeout((()=>{this._renderFloatingToolbar(),this._floatingToolbar.show(),this._floatingToolbar.checkPosition()}),0)}_addPropertyButton(t){this._propertyButtons.push(t),this._renderFloatingToolbar()}_addCommonButton(t){this._commonButtons.push(t),this._renderFloatingToolbar()}_addTemplatesButton(t){this._templatesButton=t}_renderFloatingToolbar(){null!==this._floatingContainer&&this.activeChartWidget()&&this.activeChartWidget().hasModel()&&(n.render(i.createElement(x,{templateButton:this._templatesButton,propertyButtons:this._propertyButtons,commonButtons:this._commonButtons,isDrawingFinished:this._isDrawingFinished.value(),activeChartWidget:this.activeChartWidget()}),this._floatingContainer),this._floatingToolbarRendered=!0)}_unmountFloatingToolbar(){null!==this._floatingContainer&&(n.unmountComponentAtNode(this._floatingContainer),this._floatingToolbarRendered=!1)}_clearTemplatesButton(){this._templatesButton=null}_clearPropertyButtons(){this._propertyButtons=[]}_clearCommonButtons(){this._commonButtons=[]}_isToolbarRendered(){return this._floatingToolbarRendered}_createSettingsButton(){const t={component:P,props:{title:_t,activeChartWidget:this.activeChartWidget()}};this._addCommonButton(t)}_createLockButton(){const t={component:M,props:{title:"Lock",activeChartWidget:this.activeChartWidget()}};this._addCommonButton(t)}_createRemoveButton(){const t={component:R,props:{title:gt,activeChartWidget:this.activeChartWidget()},showForSmallScreen:!0};this._addCommonButton(t)}_createDotsButton(){this._addCommonButton({component:X,props:{title:vt,activeChartWidget:this.activeChartWidget()},showForSmallScreen:!0})}_createAlertButton(){}_createSourceActions(){this._createLockButton()}_createLineStyleButton(t){const e=this.selectedSources().filter(lt.isLineTool);if(!e.length)return!1;let o,i=this._propertyApplierImpl();if(1===e.length){if(o=e[0].properties().linesStyles||t,!o)return!1}else{const t=e.map((t=>t.properties().linestyle||t.properties().lineStyle||t.properties().linesStyles)).filter(c.notUndefined);if(!t.length)return!1;o=new d.CollectiblePropertyUndoWrapper(new p.LineToolCollectedProperty(t),mt,this._propertyApplierImpl()),i={...i,setProperty:(t,e)=>t.setValue(e)}}return this._addPropertyButton({component:it,props:{property:o,title:yt,propertyApplier:i,"data-name":"style",undoText:ut}}),!0}_createFontSizeButton(t){const e=this.selectedSources();if(0===e.length)return!1;const o=e[0];if(!(0,ht.isDataSource)(o))return!1;const i={component:st,props:{property:o.properties().fontsize||t,title:ft,propertyApplier:this._propertyApplierImpl(),"data-name":"font-size",undoText:pt}};return this._addPropertyButton(i),!0}_createCommonButtons(){this._commonButtons.length&&this._clearCommonButtons(),a.enabled("property_pages")&&this._createSettingsButton(),this._createSourceActions(),this._createRemoveButton(),this._createDotsButton()}_prepareProperties(t){const e=this.selectedSources().filter((e=>e.properties()[t]))
;if(!(e.filter((e=>e.properties()[t].visible())).length<1))return e.map((e=>e.properties()[t])).filter(c.notNull)}_createProperty(t,e,o,i){if(e){const t=this._prepareProperties(o);if(!t)return;return this._isWidthProperty(t[0])?new _(new p.MultipleLineWidthsProperty(t),this._propertyApplierImpl(),i):new g.CollectibleColorPropertyUndoWrapper(new p.MultipleLineColorsProperty(t),this._propertyApplierImpl(),i)}if(t&&t.visible())return this._isWidthProperty(t)?new p.MultipleLineWidthsProperty([t]):new g.CollectibleColorPropertyDirectWrapper(new p.MultipleLineColorsProperty([t]))}_shouldShowBackgroundProperty(t,e){return!e||!e.fillBackground||!!e.fillBackground.value()}_isDrawingToolExcludingCustomUrlEventTool(t){return Boolean(null==t?void 0:t.toLowerCase().includes("linetool"))&&"LineToolTweet"!==t&&"LineToolIdea"!==t&&"LineToolImage"!==t}_updateVisibility(){this._isDrawingFinished.value()&&(this._toolbarVisible||this._propertiesVisible)?this._show():this.hide()}_showTemplatesOf(t){}_isWidthProperty(t){return t instanceof p.LineToolWidthsProperty}}},36074:(t,e,o)=>{"use strict";var i=o(45126).TranslatedString,n=o(85497).LineToolPropertiesWidgetBase;const r=o(70114).ColorPickerButton,s=o(61259).LineWidthButton;var a=o(5880),l=o(21065),c=o(89103),h=new i("change line tool(s) color",o.i18next(null,void 0,o(78655))),d=new i("change line tool(s) background color",o.i18next(null,void 0,o(50522))),p=new i("change line tool(s) text color",o.i18next(null,void 0,o(96142))),u=new i("change line tool(s) line width",o.i18next(null,void 0,o(81303))),m=o.i18next(null,void 0,o(47370)),_=o.i18next(null,void 0,o(11989)),g=o.i18next(null,void 0,o(77753)),v=o.i18next(null,void 0,o(81956)),y=o.i18next(null,void 0,o(69715)),f=o.i18next(null,void 0,o(14097)),b=o.i18next(null,void 0,o(92516)),w=o.i18next(null,void 0,o(92409)),C=o.i18next(null,void 0,o(53002)),x=o.i18next(null,void 0,o(75056)),T=o.i18next(null,void 0,o(28736)),S=o.i18next(null,void 0,o(65086)),E=o.i18next(null,void 0,o(46193)),P=o.i18next(null,void 0,o(7977)),B=o.i18next(null,void 0,o(8111));class D extends n{constructor(t){super(t),this._templatesButton=null}_createWidthsButton(t,e){if(this._lineWidthsProperty&&(this._lineWidthsProperty.destroy(),this._lineWidthsProperty=null),this._lineWidthsProperty=this._createProperty(t,e,"linesWidths",u),!this._lineWidthsProperty)return!0;var o=C;e&&(1!==this.selectedSources().filter((t=>t.properties().linesWidths)).length&&(o=x));return this._addPropertyButton({component:s,props:{title:o,multipleProperty:this._lineWidthsProperty,propertyApplier:this._propertyApplierImpl(),"data-name":"line-tool-width",undoText:u}}),!0}_createColorsButton(t,e){return this._lineColorsProperty&&(this._lineColorsProperty.destroy(),this._lineColorsProperty=null),this._lineColorsProperty=this._createProperty(t,e,"linesColors",h),!this._lineColorsProperty||(this._addPropertyButton({component:r,props:{icon:a,title:_,property:this._lineColorsProperty,propertyApplier:this._propertyApplierImpl(),"data-name":"line-tool-color",undoText:h}}),!0)}
_createBackgroundsButton(t,e){return this._backgroundsProperty&&(this._backgroundsProperty.destroy(),this._backgroundsProperty=null),this._backgroundsProperty=this._createProperty(t,e,"backgroundsColors",d),!this._backgroundsProperty||(this._addPropertyButton({component:r,props:{icon:l,title:f,property:this._backgroundsProperty,propertyApplier:this._propertyApplierImpl(),"data-name":"background-color",undoText:d}}),!0)}_createTextColorsButton(t,e){return this._textColorsProperty&&(this._textColorsProperty.destroy(),this._textColorsProperty=null),this._textColorsProperty=this._createProperty(t,e,"textsColors",p),!this._textColorsProperty||(this._addPropertyButton({component:r,props:{icon:c,title:v,property:this._textColorsProperty,propertyApplier:this._propertyApplierImpl(),"data-name":"text-color",undoText:p}}),!0)}_getPossibleProperty(t){for(var e=[],o=this._defaultToolProperties(),i=0;i<o.length;i++){var n=o[i];n.name in t&&e.push(n)}return e}showPropertiesOf(t,e,o){this._toolExceptionCases||(this._toolExceptionCases=this._createToolExceptionCases());var i=this._toolExceptionCases[t]||this._getPossibleProperty(e);if(this._clearProperties(),this._propertiesVisible=!1,i.length){for(var n={},s=0;s<i.length;s++){for(var a=i[s],l=e,c=a.name.split("."),h=0;h<c.length;++h)l=l&&l[c[h]];var d=a.showIf;if("function"!=typeof d||d(l,e)){var p=a.factory;if(p&&p.call(this,l,o))continue;if(!l)continue;if(this._propertiesVisible=!0,"combobox"!==a.inputType){const t={component:r,props:{icon:a.iconSvgCode,title:a.title,"data-name":a.dataName,property:l,propertyApplier:this._propertyApplierImpl(),undoText:a.undoText}};this._addPropertyButton(t);continue}n[a.name]=l}}this._currentProperties=n}}_defaultToolProperties(){return[{name:"linesColors",inputType:"colorPicker",iconSvgCode:a,title:m,factory:D.prototype._createColorsButton,dataName:"line-tool-color"},{name:"backgroundsColors",inputType:"colorPicker",iconSvgCode:l,title:y,factory:D.prototype._createBackgroundsButton,dataName:"background-color",showIf:this._shouldShowBackgroundProperty},{name:"textsColors",title:g,inputType:"colorPicker",iconSvgCode:c,factory:D.prototype._createTextColorsButton,dataName:"text-color"},{name:"linesWidths",inputType:"combobox",factory:D.prototype._createWidthsButton},{name:"linesStyles",title:b,inputType:"combobox",factory:D.prototype._createLineStyleButton}]}_regressionToolExceptionCases(){return[{name:"linesWidths",inputType:"combobox",factory:D.prototype._createWidthsButton}]}_pathExceptionCases(){return[{name:"linesColors",inputType:"colorPicker",iconSvgCode:a,title:m,factory:D.prototype._createColorsButton,dataName:"line-tool-color"},{name:"linesWidths",inputType:"combobox",factory:D.prototype._createWidthsButton},{name:"lineStyle",title:b,inputType:"combobox",factory:D.prototype._createLineStyleButton}]}_riskPropertiesExceptionCases(){return[{name:"textcolor",title:g,inputType:"colorPicker",iconSvgCode:c,dataName:"text-color",undoText:p},{name:"profitBackground",title:T,inputType:"colorPicker",iconSvgCode:l,dataName:"background-color",
undoText:d},{name:"stopBackground",title:S,inputType:"colorPicker",iconSvgCode:l,dataName:"background-color",undoText:d}]}_rangeExceptionCases(){return[{name:"linecolor",inputType:"colorPicker",iconSvgCode:a,title:m,dataName:"line-tool-color",undoText:h},{name:"backgroundColor",inputType:"colorPicker",iconSvgCode:l,title:y,dataName:"background-color",showIf:this._shouldShowBackgroundProperty,undoText:d},{name:"linesWidths",inputType:"combobox",factory:D.prototype._createWidthsButton}]}_brushPropertiesExceptionCase(){return[{name:"linesColors",inputType:"colorPicker",iconSvgCode:a,title:m,factory:D.prototype._createColorsButton,dataName:"line-tool-color"},{name:"backgroundsColors",inputType:"colorPicker",iconSvgCode:l,title:y,dataName:"background-color",factory:D.prototype._createBackgroundsButton},{name:"linesWidths",inputType:"combobox",factory:D.prototype._createWidthsButton}]}_bezierPropertiesExceptionCases(){return[{name:"linesColors",inputType:"colorPicker",iconSvgCode:a,title:m,factory:D.prototype._createColorsButton,dataName:"line-tool-color"},{name:"backgroundsColors",inputType:"colorPicker",iconSvgCode:l,dataName:"background-color",title:y,factory:D.prototype._createBackgroundsButton,showIf:this._shouldShowBackgroundProperty},{name:"linesWidths",inputType:"combobox",factory:D.prototype._createWidthsButton},{name:"linesStyles",title:b,inputType:"combobox",factory:D.prototype._createLineStyleButton}]}_textPropertiesExceptionCases(){return[{name:"color",title:g,inputType:"colorPicker",iconSvgCode:c,dataName:"text-color",undoText:p},{name:"backgroundColor",title:y,inputType:"colorPicker",iconSvgCode:l,dataName:"background-color",showIf:this._shouldShowBackgroundProperty,undoText:d},{name:"fontsize",title:w,inputType:"combobox",factory:D.prototype._createFontSizeButton}]}_notePropertiesExceptionCases(){return[{name:"markerColor",title:E,inputType:"colorPicker",iconSvgCode:a,dataName:"line-tool-color",undoText:h},{name:"textColor",title:g,inputType:"colorPicker",iconSvgCode:c,dataName:"text-color",undoText:p},{name:"fontSize",title:w,inputType:"combobox",factory:D.prototype._createFontSizeButton}]}_createToolExceptionCases(){return{LineToolBrush:D.prototype._brushPropertiesExceptionCase(),LineToolBezierQuadro:D.prototype._bezierPropertiesExceptionCases(),LineToolBezierCubic:D.prototype._bezierPropertiesExceptionCases(),LineToolText:D.prototype._textPropertiesExceptionCases(),LineToolTextAbsolute:D.prototype._textPropertiesExceptionCases(),LineToolBalloon:D.prototype._textPropertiesExceptionCases(),LineToolComment:D.prototype._textPropertiesExceptionCases(),LineToolCallout:D.prototype._textPropertiesExceptionCases(),LineToolPriceLabel:D.prototype._textPropertiesExceptionCases(),LineToolDateRange:D.prototype._rangeExceptionCases(),LineToolPriceRange:D.prototype._rangeExceptionCases(),LineToolDateAndPriceRange:D.prototype._rangeExceptionCases(),LineToolNote:D.prototype._notePropertiesExceptionCases(),LineToolNoteAbsolute:D.prototype._notePropertiesExceptionCases(),
LineToolRiskRewardLong:D.prototype._riskPropertiesExceptionCases(),LineToolRiskRewardShort:D.prototype._riskPropertiesExceptionCases(),LineToolPath:D.prototype._pathExceptionCases(),LineToolRegressionTrend:D.prototype._regressionToolExceptionCases(),LineToolBarsPattern:[{name:"color",title:m,inputType:"colorPicker",iconSvgCode:l,dataName:"background-color",undoText:h}],LineToolProjection:[{name:"color1",title:P,inputType:"colorPicker",iconSvgCode:l,dataName:"background-color",undoText:d},{name:"color2",title:B,inputType:"colorPicker",iconSvgCode:l,dataName:"background-color",undoText:d},{name:"linesWidths",inputType:"combobox",factory:D.prototype._createWidthsButton}],LineToolSignpost:[{name:"linesColors",inputType:"colorPicker",iconSvgCode:l,dataName:"background-color",title:m,factory:D.prototype._createBackgroundsButton,showIf:function(t,e){return e&&e.showImage.value()}},{name:"fontSize",title:w,inputType:"combobox",factory:D.prototype._createFontSizeButton}]}}}t.exports=D},95166:(t,e,o)=>{"use strict";o.d(e,{CollectibleColorPropertyDirectWrapper:()=>a,CollectibleColorPropertyUndoWrapper:()=>s});var i=o(50151),n=o(12988);class r extends n.Property{constructor(t){super(),this._listenersMappers=[],this._isProcess=!1,this._baseProperty=t}destroy(){this._baseProperty.destroy(),super.destroy()}value(){const t=this._baseProperty.value();return"mixed"===t?"":t}visible(){return this._baseProperty.visible()}setValue(t){this._isProcess=!0,this._baseProperty.setValue(""===t?"mixed":t,void 0,{applyValue:this._applyValue.bind(this)}),this._isProcess=!1,this._listenersMappers.forEach((t=>{t.method.call(t.obj,this,"")}))}subscribe(t,e){const o=o=>{this._isProcess||e.call(t,this,"")},i={obj:t,method:e,callback:o};this._listenersMappers.push(i),this._baseProperty.subscribe(t,o)}unsubscribe(t,e){var o;const n=(0,i.ensureDefined)(null===(o=this._listenersMappers.find((o=>o.obj===t&&o.method===e)))||void 0===o?void 0:o.callback);this._baseProperty.unsubscribe(t,n)}unsubscribeAll(t){this._baseProperty.unsubscribeAll(t)}}class s extends r{constructor(t,e,o){super(t),this._propertyApplier=e,this._undoText=o}_applyValue(t,e){this._propertyApplier.setProperty(t,e,this._undoText)}}class a extends r{_applyValue(t,e){t.setValue(e)}}},46112:(t,e,o)=>{"use strict";o.d(e,{CollectiblePropertyUndoWrapper:()=>l});var i=o(50151),n=o(11542),r=o(45126),s=o(12988);const a=new r.TranslatedString("change {propertyName} property",n.t(null,void 0,o(25167)));class l extends s.Property{constructor(t,e,o){super(),this._isProcess=!1,this._listenersMappers=[],this._valueApplier={applyValue:(t,e)=>{this._propertyApplier.setProperty(t,e,a)}},this._baseProperty=t,this._propertyApplier=o,this._propertyName=e}destroy(){this._baseProperty.destroy(),super.destroy()}value(){return this._baseProperty.value()}setValue(t,e){this._propertyApplier.beginUndoMacro(a.format({propertyName:this._propertyName})),this._isProcess=!0,this._baseProperty.setValue(t,void 0,this._valueApplier),this._isProcess=!1,this._propertyApplier.endUndoMacro(),this._listenersMappers.forEach((t=>{
t.method.call(t.obj,this,"")}))}subscribe(t,e){const o=()=>{this._isProcess||e.call(t,this,"")};this._listenersMappers.push({obj:t,method:e,callback:o}),this._baseProperty.subscribe(t,o)}unsubscribe(t,e){var o;const n=(0,i.ensureDefined)(null===(o=this._listenersMappers.find((o=>o.obj===t&&o.method===e)))||void 0===o?void 0:o.callback);this._baseProperty.unsubscribe(t,n)}unsubscribeAll(t){this._baseProperty.unsubscribeAll(t)}}},35923:(t,e,o)=>{"use strict";o.d(e,{PropertyApplierWithoutSavingChart:()=>n});var i=o(85719);class n{constructor(t){this._undoModelSupplier=t}setProperty(t,e,o){this._undoModelSupplier().setProperty(t,e,o,i.lineToolsDoNotAffectChartInvalidation)}beginUndoMacro(t){return this._undoModelSupplier().beginUndoMacro(t)}endUndoMacro(){this._undoModelSupplier().endUndoMacro()}setWatchedValue(t,e,o){this._undoModelSupplier().undoHistory().setWatchedValue(t,e,o,i.lineToolsDoNotAffectChartInvalidation)}}},5880:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" width="16" height="16"><path fill="currentColor" d="M10.62.72a2.47 2.47 0 0 1 3.5 0l1.16 1.16c.96.97.96 2.54 0 3.5l-.58.58-8.9 8.9-1 1-.14.14H0v-4.65l.14-.15 1-1 8.9-8.9.58-.58Zm2.8.7a1.48 1.48 0 0 0-2.1 0l-.23.23 3.26 3.26.23-.23c.58-.58.58-1.52 0-2.1l-1.16-1.16Zm.23 4.2-3.26-3.27-8.2 8.2 3.25 3.27 8.2-8.2Zm-8.9 8.9-3.27-3.26-.5.5V15h3.27l.5-.5Z"/></svg>'},578:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 8" width="16" height="8"><path fill="currentColor" d="M0 1.475l7.396 6.04.596.485.593-.49L16 1.39 14.807 0 7.393 6.122 8.58 6.12 1.186.08z"/></svg>'},44996:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path fill="currentColor" fillRule="evenodd" clipRule="evenodd" d="M7.5 13a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3zM5 14.5a2.5 2.5 0 1 1 5 0 2.5 2.5 0 0 1-5 0zm9.5-1.5a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3zM12 14.5a2.5 2.5 0 1 1 5 0 2.5 2.5 0 0 1-5 0zm9.5-1.5a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3zM19 14.5a2.5 2.5 0 1 1 5 0 2.5 2.5 0 0 1-5 0z"/></svg>'},23851:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path fill="currentColor" d="M4 13h5v1H4v-1zM12 13h5v1h-5v-1zM20 13h5v1h-5v-1z"/></svg>'},57740:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="currentColor"><circle cx="9" cy="14" r="1"/><circle cx="4" cy="14" r="1"/><circle cx="14" cy="14" r="1"/><circle cx="19" cy="14" r="1"/><circle cx="24" cy="14" r="1"/></svg>'},80427:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M5.5 7a.5.5 0 0 0 0 1h17a.5.5 0 0 0 0-1h-17Zm0 6a.5.5 0 0 0 0 1h3a.5.5 0 0 0 0-1h-3Zm7 0a.5.5 0 0 0 0 1h3a.5.5 0 0 0 0-1h-3Zm6.5.5c0-.28.22-.5.5-.5h3a.5.5 0 0 1 0 1h-3a.5.5 0 0 1-.5-.5ZM7 20a1 1 0 1 1-2 0 1 1 0 0 1 2 0Zm4 0a1 1 0 1 1-2 0 1 1 0 0 1 2 0Zm3 1a1 1 0 1 0 0-2 1 1 0 0 0 0 2Zm5-1a1 1 0 1 1-2 0 1 1 0 0 1 2 0Zm3 1a1 1 0 1 0 0-2 1 1 0 0 0 0 2Z"/></svg>'},501:t=>{
t.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path stroke="currentColor" d="M4 13.5h20"/></svg>'},36296:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M8 9.5H6.5a1 1 0 0 0-1 1v11a1 1 0 0 0 1 1h11a1 1 0 0 0 1-1V20m-8-1.5h11a1 1 0 0 0 1-1v-11a1 1 0 0 0-1-1h-11a1 1 0 0 0-1 1v11a1 1 0 0 0 1 1z"/></svg>'},21065:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" width="20" height="20" fill="none"><path stroke="currentColor" d="M13.5 6.5l-3-3-7 7 7.59 7.59a2 2 0 0 0 2.82 0l4.18-4.18a2 2 0 0 0 0-2.82L13.5 6.5zm0 0v-4a2 2 0 0 0-2-2v0a2 2 0 0 0-2 2v6"/><path fill="currentColor" d="M0 16.5C0 15 2.5 12 2.5 12S5 15 5 16.5 4 19 2.5 19 0 18 0 16.5z"/><circle fill="currentColor" cx="9.5" cy="9.5" r="1.5"/></svg>'},25388:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 8 12" width="8" height="12" fill="currentColor"><rect width="2" height="2" rx="1"/><rect width="2" height="2" rx="1" y="5"/><rect width="2" height="2" rx="1" y="10"/><rect width="2" height="2" rx="1" x="6"/><rect width="2" height="2" rx="1" x="6" y="5"/><rect width="2" height="2" rx="1" x="6" y="10"/></svg>'},22978:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 1" width="18" height="1"><rect width="18" height="1" fill="currentColor" rx=".5"/></svg>'},14631:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 2" width="18" height="2"><rect width="18" height="2" fill="currentColor" rx="1"/></svg>'},6096:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 3" width="18" height="3"><rect width="18" height="3" fill="currentColor" rx="1.5"/></svg>'},6483:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 4" width="18" height="4"><rect width="18" height="4" fill="currentColor" rx="2"/></svg>'},66611:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="currentColor"><rect width="18" height="2" rx="1" x="5" y="14"/><rect width="18" height="1" rx=".5" x="5" y="20"/><rect width="18" height="3" rx="1.5" x="5" y="7"/></svg>'},89103:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 13 15" width="13" height="15" fill="none"><path stroke="currentColor" d="M4 14.5h2.5m2.5 0H6.5m0 0V.5m0 0h-5a1 1 0 0 0-1 1V4m6-3.5h5a1 1 0 0 1 1 1V4"/></svg>'},55698:(t,e,o)=>{"use strict";o.d(e,{nanoid:()=>i});let i=(t=21)=>crypto.getRandomValues(new Uint8Array(t)).reduce(((t,e)=>t+=(e&=63)<36?e.toString(36):e<62?(e-26).toString(36).toUpperCase():e>62?"-":"_"),"")}}]);