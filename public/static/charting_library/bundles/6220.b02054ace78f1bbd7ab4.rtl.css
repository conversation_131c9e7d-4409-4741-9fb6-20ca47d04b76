.title-QPktCwTY {
  color: var(--themed-color-default-gray, #6a6d78);
  cursor: default;
  font-size: 11px;
  letter-spacing: 0.4px;
  line-height: 16px;
  padding: 10px 20px 8px;
  text-transform: uppercase;
}
html.theme-dark .title-QPktCwTY {
  color: var(--themed-color-default-gray, #868993);
}
.container-QPktCwTY {
  padding: 12px 20px;
  --ui-lib-round-tabs-hor-padding: 0;
}
.mobile-QPktCwTY {
  --ui-lib-round-tabs-hor-padding: 20px;
}
.empty-QPktCwTY {
  align-items: center;
  color: var(--themed-color-empty-state-text, #131722);
  cursor: default;
  display: flex;
  flex: 1 1;
  flex-direction: column;
  font-size: 16px;
  justify-content: center;
  line-height: 24px;
}
html.theme-dark .empty-QPktCwTY {
  color: var(--themed-color-empty-state-text, #d1d4dc);
}
.empty-QPktCwTY .image-QPktCwTY {
  height: 72px;
  margin-bottom: 8px;
  width: 72px;
}
.spinner-QPktCwTY {
  margin-top: -12px;
}
.contentList-QPktCwTY {
  min-width: 100%;
}
@media screen and (min-width: 480px) {
  .contentList-QPktCwTY {
    width: 380px;
  }
}
.item-QPktCwTY {
  flex-shrink: 0;
}
