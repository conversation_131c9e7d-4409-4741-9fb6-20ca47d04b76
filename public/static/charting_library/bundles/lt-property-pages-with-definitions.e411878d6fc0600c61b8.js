"use strict";(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[8537],{88924:(e,t,i)=>{i.d(t,{getLinesStylesPropertiesDefinitions:()=>y});var n=i(11542),o=i(45126),r=i(53749),l=i(72491),s=i(49406);const a=new o.TranslatedString("change {title} price label visibility",n.t(null,void 0,i(98822))),c=new o.TranslatedString("change {title} extension",n.t(null,void 0,i(10390))),d=new o.TranslatedString("change {title} time label visibility",n.t(null,void 0,i(66960))),p=n.t(null,void 0,i(97273)),u=n.t(null,void 0,i(56822)),h=n.t(null,void 0,i(41410));function y(e,t,i){const n=(0,s.removeSpaces)(i.originalText()),o=[],y=(0,r.createLineStyleDefinition)(e,{lineColor:t.linecolor,lineWidth:t.linewidth,lineStyle:t.linestyle},i,"Line");if(o.push(y),"showPrice"in t){const r=(0,l.createCheckablePropertyDefinition)({checked:(0,l.convertToDefinitionProperty)(e,t.showPrice,a.format({title:i}))},{id:`${n}ShowPrice`,title:p});o.push(r)}if("extendLine"in t){const r=(0,l.createCheckablePropertyDefinition)({checked:(0,l.convertToDefinitionProperty)(e,t.extendLine,c.format({title:i}))},{id:`${n}ExtendLine`,title:h});o.push(r)}if("showTime"in t){const r=(0,l.createCheckablePropertyDefinition)({checked:(0,l.convertToDefinitionProperty)(e,t.showTime,d.format({title:i}))},{id:`${n}ShowTime`,title:u});o.push(r)}return{definitions:o}}},72707:(e,t,i)=>{i.r(t),i.d(t,{getSelectionStylePropertiesDefinitions:()=>f});var n=i(11542),o=i(73305),r=i(46112),l=i(45126),s=i(72491);const a=new l.TranslatedString("lines width",n.t(null,void 0,i(41594))),c=new l.TranslatedString("lines style",n.t(null,void 0,i(96400))),d=new l.TranslatedString("lines color",n.t(null,void 0,i(24621))),p=new l.TranslatedString("backgrounds color",n.t(null,void 0,i(61218))),u=new l.TranslatedString("backgrounds filled",n.t(null,void 0,i(48433))),h=new l.TranslatedString("text color",n.t(null,void 0,i(44461))),y=new l.TranslatedString("show price",n.t(null,void 0,i(59012)));function f(e,t){const l=[];if("linesWidths"in e||"linestyle"in e||"linesColors"in e){const p=(0,s.createLinePropertyDefinition)({width:e.linesWidths?new r.CollectiblePropertyUndoWrapper(new o.LineToolCollectedProperty(e.linesWidths),a,t):void 0,style:e.linestyle?new r.CollectiblePropertyUndoWrapper(new o.LineToolCollectedProperty(e.linestyle),c,t):void 0,color:e.linesColors?new r.CollectiblePropertyUndoWrapper(new o.LineToolCollectedProperty(e.linesColors),d,t):void 0},{id:"LineStyles",title:n.t(null,void 0,i(3554))});l.push(p)}if("showPrice"in e||"showPriceLabels"in e||"axisLabelVisible"in e){const{showPrice:a=[],showPriceLabels:c=[],axisLabelVisible:d=[]}=e,p=(0,s.createCheckablePropertyDefinition)({checked:new r.CollectiblePropertyUndoWrapper(new o.LineToolCollectedProperty([...a,...c,...d]),y,t)},{id:"ShowPrice",title:n.t(null,void 0,i(97273))});l.push(p)}if("backgroundsColors"in e){const a=(0,s.createColorPropertyDefinition)({checked:e.fillBackground?new r.CollectiblePropertyUndoWrapper(new o.LineToolCollectedProperty(e.fillBackground),u,t):void 0,
color:new r.CollectiblePropertyUndoWrapper(new o.LineToolCollectedProperty(e.backgroundsColors),p,t)},{id:"BackgroundColors",title:n.t(null,void 0,i(79468))});l.push(a)}if("textsColors"in e){const a=(0,s.createLinePropertyDefinition)({color:new r.CollectiblePropertyUndoWrapper(new o.LineToolCollectedProperty(e.textsColors),h,t)},{id:"TextColors",title:n.t(null,void 0,i(70320))});l.push(a)}return{definitions:l}}},34412:(e,t,i)=>{i.d(t,{getTrendLineToolsStylePropertiesDefinitions:()=>W});var n=i(11542),o=i(45126),r=i(53749),l=i(72491),s=i(64147),a=i(39429),c=i(49406);const d=new o.TranslatedString("change {title} middle point visibility",n.t(null,void 0,i(27470))),p=new o.TranslatedString("change {title} price labels visibility",n.t(null,void 0,i(343))),u=new o.TranslatedString("change {title} price range visibility",n.t(null,void 0,i(63962))),h=new o.TranslatedString("change {title} percent change visibility",n.t(null,void 0,i(87246))),y=new o.TranslatedString("change {title} change in pips visibility",n.t(null,void 0,i(81340))),f=new o.TranslatedString("change {title} bars range visibility",n.t(null,void 0,i(98845))),v=new o.TranslatedString("change {title} date/time range visibility",n.t(null,void 0,i(37563))),g=new o.TranslatedString("change {title} distance visibility",n.t(null,void 0,i(45153))),T=new o.TranslatedString("change {title} angle visibility",n.t(null,void 0,i(59288))),D=new o.TranslatedString("change {title} always show stats",n.t(null,void 0,i(80390))),w=new o.TranslatedString("change {title} stats position",n.t(null,void 0,i(86722))),_=[{value:a.StatsPosition.Left,title:n.t(null,void 0,i(11626))},{value:a.StatsPosition.Center,title:n.t(null,void 0,i(24197))},{value:a.StatsPosition.Right,title:n.t(null,void 0,i(50421))},{value:a.StatsPosition.Auto,title:n.t(null,void 0,i(21469))}],P=n.t(null,void 0,i(12516)),S=n.t(null,void 0,i(42747)),b=n.t(null,void 0,i(16053)),m=n.t(null,void 0,i(62362)),x=n.t(null,void 0,i(73525)),L=n.t(null,void 0,i(4518)),C=n.t(null,void 0,i(89073)),k=n.t(null,void 0,i(62374)),A=n.t(null,void 0,i(3369)),V=n.t(null,void 0,i(390)),$=n.t(null,void 0,i(93857)),M=n.t(null,void 0,i(26754));function W(e,t,i,n){const o=(0,c.removeSpaces)(i.originalText()),a=[],W=t,B=(0,r.createLineStyleDefinition)(e,{...W,lineColor:t.linecolor,lineWidth:t.linewidth,lineStyle:t.linestyle},i,"Line");a.push(B);const z=(0,l.createCheckablePropertyDefinition)({checked:(0,l.convertToDefinitionProperty)(e,t.showMiddlePoint,d.format({title:i}))},{id:`${o}MiddlePoint`,title:n&&n.middlePoint||P});a.push(z);const N=(0,l.createCheckablePropertyDefinition)({checked:(0,l.convertToDefinitionProperty)(e,t.showPriceLabels,p.format({title:i}))},{id:`${o}ShowPriceLabels`,title:n&&n.showPriceLabelsTitle||S});a.push(N);const R=[],G=(0,l.createCheckablePropertyDefinition)({checked:(0,l.convertToDefinitionProperty)(e,t.showPriceRange,u.format({title:i}))},{id:`${o}PriceRange`,title:n&&n.priceRange||m});R.push(G);const E=(0,l.createCheckablePropertyDefinition)({checked:(0,
l.convertToDefinitionProperty)(e,t.showPercentPriceRange,h.format({title:i}))},{id:`${o}PercentChange`,title:n&&n.percentChange||x});R.push(E);const U=(0,l.createCheckablePropertyDefinition)({checked:(0,l.convertToDefinitionProperty)(e,t.showPipsPriceRange,y.format({title:i}))},{id:`${o}PipsChange`,title:n&&n.pipsChange||L});R.push(U);const O=(0,l.createCheckablePropertyDefinition)({checked:(0,l.convertToDefinitionProperty)(e,t.showBarsRange,f.format({title:i}))},{id:`${o}BarsRange`,title:n&&n.barRange||C});if(R.push(O),"showDateTimeRange"in t){const r=(0,l.createCheckablePropertyDefinition)({checked:(0,l.convertToDefinitionProperty)(e,t.showDateTimeRange,v.format({title:i}))},{id:`${o}DateTimeRange`,title:n&&n.dateTimeRange||k});R.push(r)}if("showDistance"in t){const r=(0,l.createCheckablePropertyDefinition)({checked:(0,l.convertToDefinitionProperty)(e,t.showDistance,g.format({title:i}))},{id:`${o}Distance`,title:n&&n.distance||A});R.push(r)}if("showAngle"in t){const r=(0,l.createCheckablePropertyDefinition)({checked:(0,l.convertToDefinitionProperty)(e,t.showAngle,T.format({title:i}))},{id:`${o}Angle`,title:n&&n.angle||V});R.push(r)}const I=(0,l.createCheckablePropertyDefinition)({checked:(0,l.convertToDefinitionProperty)(e,t.alwaysShowStats,D.format({title:i}))},{id:`${o}ShowStats`,title:n&&n.showStats||$});R.push(I);const F=(0,l.createOptionsPropertyDefinition)({option:(0,l.convertToDefinitionProperty)(e,t.statsPosition,w.format({title:i}))},{id:`${o}StatsPosition`,title:n&&n.statsPosition||b,options:new s.WatchedValue(_)});return R.push(F),a.push((0,l.createPropertyDefinitionsGeneralGroup)(R,`${o}StatsGroup`,M)),{definitions:a}}},53749:(e,t,i)=>{i.d(t,{createLineStyleDefinition:()=>T});var n=i(11542),o=i(45126),r=i(72491),l=i(49406);const s=new o.TranslatedString("change {toolName} line visibility",n.t(null,void 0,i(24550))),a=new o.TranslatedString("change {toolName} line width",n.t(null,void 0,i(19541))),c=new o.TranslatedString("change {toolName} line style",n.t(null,void 0,i(66429))),d=new o.TranslatedString("change {toolName} line color",n.t(null,void 0,i(24059))),p=new o.TranslatedString("change {toolName} line extending left",n.t(null,void 0,i(18773))),u=new o.TranslatedString("change {toolName} line left end",n.t(null,void 0,i(21474))),h=new o.TranslatedString("change {toolName} line extending right",n.t(null,void 0,i(43823))),y=new o.TranslatedString("change {toolName} line right end",n.t(null,void 0,i(54827))),f=n.t(null,void 0,i(3554)),v=n.t(null,void 0,i(61856)),g=n.t(null,void 0,i(87430));function T(e,t,i,n,o){const T={},D={id:`${(0,l.removeSpaces)(i.originalText())}${n}`,title:o&&o.line||f};return void 0!==t.showLine&&(T.checked=(0,r.convertToDefinitionProperty)(e,t.showLine,s.format({toolName:i}))),void 0!==t.lineWidth&&(T.width=(0,r.convertToDefinitionProperty)(e,t.lineWidth,a.format({toolName:i}))),void 0!==t.lineStyle&&(T.style=(0,r.convertToDefinitionProperty)(e,t.lineStyle,c.format({toolName:i}))),void 0!==t.lineColor&&(T.color=(0,r.getColorDefinitionProperty)(e,t.lineColor,null,d.format({
toolName:i}))),void 0!==t.extendLeft&&(T.extendLeft=(0,r.convertToDefinitionProperty)(e,t.extendLeft,p.format({toolName:i})),D.extendLeftTitle=o&&o.extendLeftTitle||v),void 0!==t.leftEnd&&(T.leftEnd=(0,r.convertToDefinitionProperty)(e,t.leftEnd,u.format({toolName:i}))),void 0!==t.extendRight&&(T.extendRight=(0,r.convertToDefinitionProperty)(e,t.extendRight,h.format({toolName:i})),D.extendRightTitle=o&&o.extendRightTitle||g),void 0!==t.rightEnd&&(T.rightEnd=(0,r.convertToDefinitionProperty)(e,t.rightEnd,y.format({toolName:i}))),(0,r.createLinePropertyDefinition)(T,D)}},91335:(e,t,i)=>{i.d(t,{createTextStyleDefinition:()=>m});var n=i(11542),o=i(45126),r=i(72491),l=i(49406);const s=new o.TranslatedString("change {toolName} text visibility",n.t(null,void 0,i(56634))),a=new o.TranslatedString("change {toolName} text color",n.t(null,void 0,i(64500))),c=new o.TranslatedString("change {toolName} text font size",n.t(null,void 0,i(21781))),d=new o.TranslatedString("change {toolName} text font bold",n.t(null,void 0,i(24701))),p=new o.TranslatedString("change {toolName} text font italic",n.t(null,void 0,i(42694))),u=new o.TranslatedString("change {toolName} text",n.t(null,void 0,i(66668))),h=new o.TranslatedString("change {toolName} labels alignment vertical",n.t(null,void 0,i(31689))),y=new o.TranslatedString("change {toolName} labels alignment horizontal",n.t(null,void 0,i(88277))),f=new o.TranslatedString("change {toolName} labels direction",n.t(null,void 0,i(61160))),v=new o.TranslatedString("change {toolName} text background visibility",n.t(null,void 0,i(31133))),g=new o.TranslatedString("change {toolName} text background color",n.t(null,void 0,i(22231))),T=new o.TranslatedString("change {toolName} text border visibility",n.t(null,void 0,i(58704))),D=new o.TranslatedString("change {toolName} text border width",n.t(null,void 0,i(35423))),w=new o.TranslatedString("change {toolName} text border color",n.t(null,void 0,i(36666))),_=new o.TranslatedString("change {toolName} text wrap",n.t(null,void 0,i(39587))),P=n.t(null,void 0,i(79468)),S=n.t(null,void 0,i(38408)),b=n.t(null,void 0,i(7560));function m(e,t,i,n){const o={},m={id:`${(0,l.removeSpaces)(i.originalText())}Text`,title:n.customTitles&&n.customTitles.text||""};if(void 0!==t.showText&&(o.checked=(0,r.convertToDefinitionProperty)(e,t.showText,s.format({toolName:i}))),void 0!==t.textColor&&(o.color=(0,r.getColorDefinitionProperty)(e,t.textColor,t.transparency||null,a.format({toolName:i}))),void 0!==t.fontSize&&(o.size=(0,r.convertToDefinitionProperty)(e,t.fontSize,c.format({toolName:i}))),void 0!==t.bold&&(o.bold=(0,r.convertToDefinitionProperty)(e,t.bold,d.format({toolName:i}))),void 0!==t.italic&&(o.italic=(0,r.convertToDefinitionProperty)(e,t.italic,p.format({toolName:i}))),void 0!==t.text&&(o.text=(0,r.convertToDefinitionProperty)(e,t.text,u.format({toolName:i})),m.isEditable=Boolean(n.isEditable),m.isMultiLine=Boolean(n.isMultiLine)),void 0!==t.vertLabelsAlign&&(o.alignmentVertical=(0,r.convertToDefinitionProperty)(e,t.vertLabelsAlign,h.format({toolName:i})),
m.alignmentVerticalItems=n.alignmentVerticalItems),void 0!==t.horzLabelsAlign&&(o.alignmentHorizontal=(0,r.convertToDefinitionProperty)(e,t.horzLabelsAlign,y.format({toolName:i})),m.alignmentHorizontalItems=n.alignmentHorizontalItems),void 0!==t.textOrientation&&(o.orientation=(0,r.convertToDefinitionProperty)(e,t.textOrientation,f.format({toolName:i}))),void 0!==t.backgroundVisible&&(o.backgroundVisible=(0,r.convertToDefinitionProperty)(e,t.backgroundVisible,v.format({toolName:i}))),void 0!==t.backgroundColor){let n=null;void 0!==t.backgroundTransparency&&(n=t.backgroundTransparency),o.backgroundColor=(0,r.getColorDefinitionProperty)(e,t.backgroundColor,n,g.format({toolName:i}))}return void 0===t.backgroundVisible&&void 0===t.backgroundColor||(m.backgroundTitle=n.customTitles&&n.customTitles.backgroundTitle||P),void 0!==t.borderVisible&&(o.borderVisible=(0,r.convertToDefinitionProperty)(e,t.borderVisible,T.format({toolName:i}))),void 0!==t.borderWidth&&(o.borderWidth=(0,r.convertToDefinitionProperty)(e,t.borderWidth,D.format({toolName:i}))),void 0!==t.borderColor&&(o.borderColor=(0,r.getColorDefinitionProperty)(e,t.borderColor,null,w.format({toolName:i}))),void 0===t.borderVisible&&void 0===t.borderColor&&void 0===t.borderWidth||(m.borderTitle=n.customTitles&&n.customTitles.borderTitle||S),void 0!==t.wrap&&(o.wrap=(0,r.convertToDefinitionProperty)(e,t.wrap,_.format({toolName:i})),m.wrapTitle=n.customTitles&&n.customTitles.wrapTitle||b),(0,r.createTextPropertyDefinition)(o,m)}},67675:(e,t,i)=>{i.r(t),i.d(t,{ArrowMarkDefinitionsViewModel:()=>p});var n=i(11542),o=i(45126),r=i(91335),l=i(18009),s=i(72491);const a=new o.TranslatedString("change arrow color",n.t(null,void 0,i(77931))),c=n.t(null,void 0,i(70320)),d=n.t(null,void 0,i(11858));class p extends l.LineDataSourceDefinitionsViewModel{_textPropertyDefinitions(){const e=this._source.properties().childs();return{definitions:[(0,r.createTextStyleDefinition)(this._propertyApplier,{text:e.text,showText:e.showLabel,textColor:e.color,fontSize:e.fontsize,bold:e.bold,italic:e.italic},new o.TranslatedString(this._source.name(),this._source.translatedType()),{isEditable:!0,isMultiLine:!0,customTitles:{text:c}})]}}_stylePropertyDefinitions(){const e=this._source.properties().childs();return{definitions:[(0,s.createColorPropertyDefinition)({color:(0,s.getColorDefinitionProperty)(this._propertyApplier,e.arrowColor,null,a)},{id:"ArrowColor",title:d})]}}}},86658:(e,t,i)=>{i.r(t),i.d(t,{ArrowMarkerDefinitionsViewModel:()=>u});var n=i(11542),o=i(45126),r=i(72491),l=i(18009),s=i(49406),a=i(91335);const c=new o.TranslatedString("change {title} color",n.t(null,void 0,i(49442))),d=n.t(null,void 0,i(47370)),p=n.t(null,void 0,i(70320));class u extends l.LineDataSourceDefinitionsViewModel{_stylePropertyDefinitions(){const e=this._source.properties().childs(),t=this._source.name(),i=new o.TranslatedString(t,this._source.translatedType());return{definitions:[(0,r.createColorPropertyDefinition)({color:(0,r.getColorDefinitionProperty)(this._propertyApplier,e.backgroundColor,null,c.format({
title:i}))},{id:(0,s.removeSpaces)(`${t}Color`),title:d})]}}_textPropertyDefinitions(){const e=this._source.properties().childs();return{definitions:[(0,a.createTextStyleDefinition)(this._propertyApplier,{text:e.text,showText:e.showLabel,textColor:e.textColor,fontSize:e.fontsize,bold:e.bold,italic:e.italic},new o.TranslatedString(this._source.name(),this._source.translatedType()),{isEditable:!0,isMultiLine:!0,customTitles:{text:p}})]}}}},18046:(e,t,i)=>{i.r(t),i.d(t,{BalloonDefinitionsViewModel:()=>a});var n=i(11542),o=i(45126),r=i(91335),l=i(18009);const s=n.t(null,void 0,i(70320));class a extends l.LineDataSourceDefinitionsViewModel{_textPropertyDefinitions(){const e=this._source.properties().childs();return{definitions:[(0,r.createTextStyleDefinition)(this._propertyApplier,{textColor:e.color,fontSize:e.fontsize,text:e.text,backgroundColor:e.backgroundColor,backgroundTransparency:e.transparency,borderColor:e.borderColor},new o.TranslatedString(this._source.name(),this._source.translatedType()),{isEditable:!0,isMultiLine:!0,customTitles:{text:s}})]}}}},1847:(e,t,i)=>{i.r(t),i.d(t,{BarsPatternDefinitionsViewModel:()=>w});var n=i(11542),o=i(45126),r=i(18009),l=i(72491),s=i(64147),a=i(67467),c=i(52305),d=i(49406);const p=new o.TranslatedString("change {title} color",n.t(null,void 0,i(49442))),u=new o.TranslatedString("change {title} mode",n.t(null,void 0,i(57462))),h=new o.TranslatedString("change {title} mirrored",n.t(null,void 0,i(85198))),y=new o.TranslatedString("change {title} flipped",n.t(null,void 0,i(10643))),f=n.t(null,void 0,i(47370)),v=n.t(null,void 0,i(3214)),g=n.t(null,void 0,i(28941)),T=n.t(null,void 0,i(63271)),D=[{value:a.LineToolBarsPatternMode.Bars,title:n.t(null,void 0,i(49275))},{value:a.LineToolBarsPatternMode.OpenClose,title:n.t(null,void 0,i(98136))},{value:a.LineToolBarsPatternMode.Line,title:n.t(null,void 0,i(30216))},{value:a.LineToolBarsPatternMode.LineOpen,title:n.t(null,void 0,i(80332))},{value:a.LineToolBarsPatternMode.LineHigh,title:n.t(null,void 0,i(18387))},{value:a.LineToolBarsPatternMode.LineLow,title:n.t(null,void 0,i(53880))},{value:a.LineToolBarsPatternMode.LineHL2,title:n.t(null,void 0,i(32982))}];class w extends r.LineDataSourceDefinitionsViewModel{_stylePropertyDefinitions(){const e=this._source.properties().childs(),t=this._source.name(),i=new o.TranslatedString(t,this._source.translatedType()),n=(0,d.removeSpaces)(t);return{definitions:[(0,l.createColorPropertyDefinition)({color:(0,l.getColorDefinitionProperty)(this._propertyApplier,e.color,null,p.format({title:i}))},{id:`${n}Color`,title:f}),(0,l.createOptionsPropertyDefinition)({option:(0,l.convertToDefinitionProperty)(this._propertyApplier,e.mode,u.format({title:i}),[c.convertToInt])},{id:`${n}Mode`,title:v,options:new s.WatchedValue(D)}),(0,l.createCheckablePropertyDefinition)({checked:(0,l.convertToDefinitionProperty)(this._propertyApplier,e.mirrored,h.format({title:i}))},{id:`${n}Mirrored`,title:g}),(0,l.createCheckablePropertyDefinition)({checked:(0,
l.convertToDefinitionProperty)(this._propertyApplier,e.flipped,y.format({title:i}))},{id:`${n}Flipped`,title:T})]}}}},32364:(e,t,i)=>{i.r(t),i.d(t,{BrushDefinitionsViewModel:()=>u});var n=i(11542),o=i(45126),r=i(53749),l=i(18009),s=i(72491),a=i(49406);const c=new o.TranslatedString("change {title} background visibility",n.t(null,void 0,i(30839))),d=new o.TranslatedString("change {title} background color",n.t(null,void 0,i(49765))),p=n.t(null,void 0,i(79468));class u extends l.LineDataSourceDefinitionsViewModel{_stylePropertyDefinitions(){const e=this._source.properties().childs(),t=this._source.name(),i=new o.TranslatedString(t,this._source.translatedType());return{definitions:[(0,r.createLineStyleDefinition)(this._propertyApplier,{lineColor:e.linecolor,lineWidth:e.linewidth,leftEnd:e.leftEnd,rightEnd:e.rightEnd},i,"Line"),(0,s.createColorPropertyDefinition)({checked:(0,s.convertToDefinitionProperty)(this._propertyApplier,e.fillBackground,c.format({title:i})),color:(0,s.getColorDefinitionProperty)(this._propertyApplier,e.backgroundColor,e.transparency,d.format({title:i}))},{id:(0,a.removeSpaces)(`${t}BackgroundColor`),title:p})]}}}},29908:(e,t,i)=>{i.r(t),i.d(t,{CalloutDefinitionsViewModel:()=>l});var n=i(91335),o=i(18009),r=i(45126);class l extends o.LineDataSourceDefinitionsViewModel{_textPropertyDefinitions(){const e=this._source.properties().childs();return{definitions:[(0,n.createTextStyleDefinition)(this._propertyApplier,{textColor:e.color,fontSize:e.fontsize,bold:e.bold,italic:e.italic,text:e.text,backgroundColor:e.backgroundColor,backgroundTransparency:e.transparency,borderColor:e.bordercolor,borderWidth:e.linewidth,wrap:e.wordWrap},new r.TranslatedString(this._source.name(),this._source.translatedType()),{isEditable:!0,isMultiLine:!0})]}}}},9805:(e,t,i)=>{i.r(t),i.d(t,{CrossLineDefinitionsViewModel:()=>c});var n=i(11542),o=i(45126),r=i(18009),l=i(88924),s=i(91335);const a=n.t(null,void 0,i(70320));class c extends r.LineDataSourceDefinitionsViewModel{_stylePropertyDefinitions(){const e=this._source.properties().childs();return(0,l.getLinesStylesPropertiesDefinitions)(this._propertyApplier,e,new o.TranslatedString(this._source.name(),this._source.translatedType()))}_textPropertyDefinitions(){const e=this._source.properties().childs();if("showLabel"in e){return{definitions:[(0,s.createTextStyleDefinition)(this._propertyApplier,{...e,showText:e.showLabel,textColor:e.textcolor,fontSize:e.fontsize},new o.TranslatedString(this._source.name(),this._source.translatedType()),{isEditable:!0,isMultiLine:!0,customTitles:{text:a}})]}}return null}}},19298:(e,t,i)=>{i.r(t),i.d(t,{CyclicAndSineLinesPatternDefinitionsViewModel:()=>a});var n=i(11542),o=i(45126),r=i(53749),l=i(18009);const s=n.t(null,void 0,i(56982));class a extends l.LineDataSourceDefinitionsViewModel{_stylePropertyDefinitions(){const e=this._source.properties().childs();return{definitions:[(0,r.createLineStyleDefinition)(this._propertyApplier,{lineColor:e.linecolor,lineWidth:e.linewidth,lineStyle:e.linestyle
},new o.TranslatedString(this._source.name(),this._source.translatedType()),"Line",{line:s})]}}}},70491:(e,t,i)=>{i.r(t),i.d(t,{ElliottPatternDefinitionsViewModel:()=>f});var n=i(11542),o=i(45126),r=i(53749),l=i(18009),s=i(72491),a=i(64147),c=i(49406);const d=new o.TranslatedString("change {title} color",n.t(null,void 0,i(49442))),p=new o.TranslatedString("change {title} degree",n.t(null,void 0,i(86650))),u=n.t(null,void 0,i(47370)),h=n.t(null,void 0,i(32998)),y=n.t(null,void 0,i(23403));class f extends l.LineDataSourceDefinitionsViewModel{_stylePropertyDefinitions(){const e=this._source.properties().childs(),t=this._source.name(),i=new o.TranslatedString(t,this._source.translatedType());return{definitions:[(0,s.createColorPropertyDefinition)({color:(0,s.getColorDefinitionProperty)(this._propertyApplier,e.color,null,d.format({title:i}))},{id:(0,c.removeSpaces)(`${t}BackgroundColor`),title:u}),(0,r.createLineStyleDefinition)(this._propertyApplier,{showLine:e.showWave,lineWidth:e.linewidth},i,"Line",{line:h}),(0,s.createOptionsPropertyDefinition)({option:(0,s.convertToDefinitionProperty)(this._propertyApplier,e.degree,p.format({title:i}))},{id:`${t}Degree`,title:y,options:new a.WatchedValue(this._source.availableDegreesValues())})]}}}},99458:(e,t,i)=>{i.r(t),i.d(t,{EllipseCircleDefinitionsViewModel:()=>a});var n=i(11542),o=i(45126),r=i(91335),l=i(90490);const s=n.t(null,void 0,i(70320));class a extends l.GeneralFiguresDefinitionsViewModelBase{_textPropertyDefinitions(){const e=this._source.properties().childs();return{definitions:[(0,r.createTextStyleDefinition)(this._propertyApplier,{textColor:e.textColor,text:e.text,bold:e.bold,italic:e.italic,fontSize:e.fontSize,showText:e.showLabel},new o.TranslatedString(this._source.name(),this._source.translatedType()),{isEditable:!0,isMultiLine:!0,customTitles:{text:s}})]}}}},41618:(e,t,i)=>{i.r(t),i.d(t,{FibCirclesDefinitionsViewModel:()=>m});var n=i(50151),o=i(11542),r=i(45126),l=i(53749),s=i(72491),a=i(18009),c=i(49406),d=i(95166);const p=new r.TranslatedString("change {title} level {index} line visibility",o.t(null,void 0,i(51403))),u=new r.TranslatedString("change {title} levels visibility",o.t(null,void 0,i(54517))),h=new r.TranslatedString("change {title} level {index} line color",o.t(null,void 0,i(664))),y=new r.TranslatedString("change {title} level {index} line width",o.t(null,void 0,i(97870))),f=new r.TranslatedString("change {title} level {index} line coeff",o.t(null,void 0,i(27154))),v=new r.TranslatedString("change {title} all lines color",o.t(null,void 0,i(59577))),g=new r.TranslatedString("change {title} background visibility",o.t(null,void 0,i(30839))),T=new r.TranslatedString("change {title} background transparency",o.t(null,void 0,i(13783))),D=new r.TranslatedString("change {title} coeffs as percents visibility",o.t(null,void 0,i(31753))),w=o.t(null,void 0,i(51574)),_=o.t(null,void 0,i(28683)),P=o.t(null,void 0,i(79468)),S=o.t(null,void 0,i(79650)),b=o.t(null,void 0,i(53912));class m extends a.LineDataSourceDefinitionsViewModel{_stylePropertyDefinitions(){
const e=[],t=this._source.properties().childs(),i=this._source.name(),o=(0,c.removeSpaces)(i),a=new r.TranslatedString(i,this._source.translatedType()),m=t.trendline.childs(),x=(0,l.createLineStyleDefinition)(this._propertyApplier,{showLine:m.visible,lineColor:m.color,lineStyle:m.linestyle,lineWidth:m.linewidth},a,"TrendLine",{line:w});e.push(x);const L=this._source.levelsCount();for(let i=1;i<=L;i++){const n=t[`level${i}`].childs(),r=(0,s.createLeveledLinePropertyDefinition)({checked:(0,s.convertToDefinitionProperty)(this._propertyApplier,n.visible,p.format({title:a,index:i})),color:(0,s.getColorDefinitionProperty)(this._propertyApplier,n.color,null,h.format({title:a,index:i})),width:(0,s.convertToDefinitionProperty)(this._propertyApplier,n.linewidth,y.format({title:a,index:i})),level:(0,s.convertToDefinitionProperty)(this._propertyApplier,n.coeff,f.format({title:a,index:i}))},{id:`${o}LineLevel${i}`});e.push(r)}const C=(0,s.createColorPropertyDefinition)({color:(0,s.getColorDefinitionProperty)(this._propertyApplier,new d.CollectibleColorPropertyUndoWrapper((0,n.ensureNotNull)(this._source.lineColorsProperty()),this._propertyApplier,null),null,v.format({title:a}),!0)},{id:`${o}AllLineColor`,title:_});e.push(C);const k=(0,s.createTransparencyPropertyDefinition)({checked:(0,s.convertToDefinitionProperty)(this._propertyApplier,t.fillBackground,g.format({title:a})),transparency:(0,s.convertToDefinitionProperty)(this._propertyApplier,t.transparency,T.format({title:a}))},{id:`${o}Background`,title:P});e.push(k);const A=(0,s.createCheckablePropertyDefinition)({checked:(0,s.convertToDefinitionProperty)(this._propertyApplier,t.showCoeffs,u.format({title:a}))},{id:`${o}Levels`,title:S});e.push(A);const V=(0,s.createCheckablePropertyDefinition)({checked:(0,s.convertToDefinitionProperty)(this._propertyApplier,t.coeffsAsPercents,D.format({title:a}))},{id:`${o}Percentage`,title:b});return e.push(V),{definitions:e}}}},75450:(e,t,i)=>{i.r(t),i.d(t,{FibDrawingsWith24LevelsDefinitionsViewModel:()=>H});var n=i(50151),o=i(11542),r=i(45126),l=i(53749),s=i(72491),a=i(18009),c=i(23720),d=i(64147),p=i(49406),u=i(95166)
;const h=new r.TranslatedString("change {title} level {index} line visibility",o.t(null,void 0,i(51403))),y=new r.TranslatedString("change {title} level {index} line color",o.t(null,void 0,i(664))),f=new r.TranslatedString("change {title} level {index} line coeff",o.t(null,void 0,i(27154))),v=new r.TranslatedString("change {title} all lines color",o.t(null,void 0,i(59577))),g=new r.TranslatedString("change {title} background visibility",o.t(null,void 0,i(30839))),T=new r.TranslatedString("change {title} background transparency",o.t(null,void 0,i(13783))),D=new r.TranslatedString("change {title} reverse",o.t(null,void 0,i(50762))),w=new r.TranslatedString("change {title} prices visibility",o.t(null,void 0,i(4714))),_=new r.TranslatedString("change {title} labels alignment",o.t(null,void 0,i(58312))),P=new r.TranslatedString("change {title} text alignment",o.t(null,void 0,i(55134))),S=new r.TranslatedString("change {title} text visibility",o.t(null,void 0,i(30353))),b=new r.TranslatedString("change {title} labels font size",o.t(null,void 0,i(19658))),m=new r.TranslatedString("change {title} style",o.t(null,void 0,i(98463))),x=new r.TranslatedString("change {title} fib levels based on log scale",o.t(null,void 0,i(85509))),L=o.t(null,void 0,i(51574)),C=o.t(null,void 0,i(36937)),k=o.t(null,void 0,i(68461)),A=o.t(null,void 0,i(25112)),V=o.t(null,void 0,i(79192)),$=o.t(null,void 0,i(25188)),M=o.t(null,void 0,i(64489)),W=o.t(null,void 0,i(29416)),B=o.t(null,void 0,i(79650)),z=o.t(null,void 0,i(5119)),N=o.t(null,void 0,i(70320)),R=o.t(null,void 0,i(2573)),G=o.t(null,void 0,i(28683)),E=o.t(null,void 0,i(79468)),U=o.t(null,void 0,i(66086)),O=[{id:"values",value:!1,title:o.t(null,void 0,i(60092))},{id:"percents",value:!0,title:o.t(null,void 0,i(33120))}],I=[{id:"bottom",value:"bottom",title:o.t(null,void 0,i(97118))},{id:"middle",value:"middle",title:o.t(null,void 0,i(68833))},{id:"top",value:"top",title:o.t(null,void 0,i(27567))}],F=[10,11,12,14,16,20,24].map((e=>({title:String(e),value:e})));class H extends a.LineDataSourceDefinitionsViewModel{constructor(e,t){super(e,t),this._disabledBasedOnLog=null;if("fibLevelsBasedOnLogScale"in this._source.properties().childs()){const e=this._source.priceScale();null!==e&&(this._disabledBasedOnLog=new d.WatchedValue(Boolean(!e.mode().log)),this._createPropertyRages(),e.modeChanged().subscribe(this,((e,t)=>{null!==this._disabledBasedOnLog&&this._disabledBasedOnLog.setValue(Boolean(!t.log))})))}}destroy(){super.destroy();const e=this._source.priceScale();null!==e&&e.modeChanged().unsubscribeAll(this)}_stylePropertyDefinitions(){const e=[],t=this._source.properties(),i=t.childs(),o=this._source.name(),a=(0,p.removeSpaces)(o),H=new r.TranslatedString(o,this._source.translatedType());if("trendline"in i){const t=i.trendline.childs(),n=(0,l.createLineStyleDefinition)(this._propertyApplier,{showLine:t.visible,lineColor:t.color,lineStyle:t.linestyle,lineWidth:t.linewidth},H,"TrendLine",{line:L});e.push(n)}const j=i.levelsStyle.childs(),Y={lineStyle:j.linestyle,lineWidth:j.linewidth},X={line:C}
;"extendLines"in i&&(Y.extendRight=i.extendLines,X.extendRightTitle=V),"extendLinesLeft"in i&&(Y.extendLeft=i.extendLinesLeft,X.extendLeftTitle=$),"extendRight"in i&&(Y.extendRight=i.extendRight,X.extendRightTitle=k),"extendLeft"in i&&(Y.extendLeft=i.extendLeft,X.extendLeftTitle=A);const q=(0,l.createLineStyleDefinition)(this._propertyApplier,Y,H,"LevelsStyleLine",X);e.push(q);const J=[],K=this._source.levelsCount();for(let e=1;e<=K;e++){const t=i[`level${e}`].childs(),n=(0,s.createLeveledLinePropertyDefinition)({checked:(0,s.convertToDefinitionProperty)(this._propertyApplier,t.visible,h.format({title:H,index:e})),color:(0,s.getColorDefinitionProperty)(this._propertyApplier,t.color,null,y.format({title:H,index:e})),level:(0,s.convertToDefinitionProperty)(this._propertyApplier,t.coeff,f.format({title:H,index:e}))},{id:`${a}LineLevel${e}`});J.push(n)}const Q=(0,s.createPropertyDefinitionsLeveledLinesGroup)(J,`${a}LeveledLinesGroup`);e.push((0,s.createPropertyDefinitionsGeneralGroup)([Q],`${a}Group`));const Z=(0,s.createColorPropertyDefinition)({color:(0,s.getColorDefinitionProperty)(this._propertyApplier,new u.CollectibleColorPropertyUndoWrapper((0,n.ensureNotNull)(this._source.lineColorsProperty()),this._propertyApplier,null),null,v.format({title:H}),!0)},{id:`${a}AllLineColor`,title:G});e.push(Z);const ee=(0,s.createTransparencyPropertyDefinition)({checked:(0,s.convertToDefinitionProperty)(this._propertyApplier,i.fillBackground,g.format({title:H})),transparency:(0,s.convertToDefinitionProperty)(this._propertyApplier,i.transparency,T.format({title:H}))},{id:`${a}Background`,title:E});e.push(ee);const te=i;if("reverse"in te){const t=(0,s.createCheckablePropertyDefinition)({checked:(0,s.convertToDefinitionProperty)(this._propertyApplier,te.reverse,D.format({title:H}))},{id:`${a}Reverse`,title:M});e.push(t)}const ie=(0,s.createCheckablePropertyDefinition)({checked:(0,s.convertToDefinitionProperty)(this._propertyApplier,i.showPrices,w.format({title:H}))},{id:`${a}Prices`,title:W});e.push(ie);const ne=(0,s.createOptionsPropertyDefinition)({checked:(0,s.convertToDefinitionProperty)(this._propertyApplier,i.showCoeffs,m.format({title:H})),option:(0,s.convertToDefinitionProperty)(this._propertyApplier,i.coeffsAsPercents,m.format({title:H}))},{id:`${a}PitchStyle`,title:B,options:new d.WatchedValue(O)});e.push(ne);const oe=(0,s.createTwoOptionsPropertyDefinition)({option1:(0,s.convertToDefinitionProperty)(this._propertyApplier,i.horzLabelsAlign,_.format({title:H})),option2:(0,s.convertToDefinitionProperty)(this._propertyApplier,i.vertLabelsAlign,_.format({title:H}))},{id:`${a}Alignment`,title:z,optionsItems1:new d.WatchedValue(c.availableAlignmentHorizontalItems),optionsItems2:new d.WatchedValue(I)});e.push(oe);const re=t.child("showText"),le=t.child("horzTextAlign"),se=t.child("vertTextAlign");if(re&&le&&se){const t=(0,s.createTwoOptionsPropertyDefinition)({checked:(0,s.convertToDefinitionProperty)(this._propertyApplier,re,S.format({title:H})),option1:(0,s.convertToDefinitionProperty)(this._propertyApplier,le,P.format({title:H
})),option2:(0,s.convertToDefinitionProperty)(this._propertyApplier,se,P.format({title:H}))},{id:`${a}Text`,title:N,optionsItems1:new d.WatchedValue(c.availableAlignmentHorizontalItems),optionsItems2:new d.WatchedValue(I)});e.push(t)}const ae=(0,s.createOptionsPropertyDefinition)({option:(0,s.convertToDefinitionProperty)(this._propertyApplier,i.labelFontSize,b.format({title:H}))},{id:`${a}FontSize`,title:R,options:new d.WatchedValue(F)});if(e.push(ae),"fibLevelsBasedOnLogScale"in i&&null!==this._disabledBasedOnLog){const t=(0,s.createCheckablePropertyDefinition)({disabled:(0,s.convertFromWVToDefinitionProperty)(this._propertyApplier,this._disabledBasedOnLog,x.format({title:H})),checked:(0,s.convertToDefinitionProperty)(this._propertyApplier,i.fibLevelsBasedOnLogScale,x.format({title:H}))},{id:`${a}BasedOnLog`,title:U});e.push(t)}return{definitions:e}}}},20875:(e,t,i)=>{i.r(t),i.d(t,{FibSpeedResistanceArcsDefinitionsViewModel:()=>m});var n=i(50151),o=i(11542),r=i(45126),l=i(53749),s=i(72491),a=i(18009),c=i(49406),d=i(95166);const p=new r.TranslatedString("change {title} level {index} line visibility",o.t(null,void 0,i(51403))),u=new r.TranslatedString("change {title} levels visibility",o.t(null,void 0,i(54517))),h=new r.TranslatedString("change {title} level {index} line color",o.t(null,void 0,i(664))),y=new r.TranslatedString("change {title} level {index} line width",o.t(null,void 0,i(97870))),f=new r.TranslatedString("change {title} level {index} line coeff",o.t(null,void 0,i(27154))),v=new r.TranslatedString("change {title} all lines color",o.t(null,void 0,i(59577))),g=new r.TranslatedString("change {title} background visibility",o.t(null,void 0,i(30839))),T=new r.TranslatedString("change {title} background transparency",o.t(null,void 0,i(13783))),D=new r.TranslatedString("change {title} full circles visibility",o.t(null,void 0,i(30484))),w=o.t(null,void 0,i(51574)),_=o.t(null,void 0,i(28683)),P=o.t(null,void 0,i(79468)),S=o.t(null,void 0,i(79650)),b=o.t(null,void 0,i(95279));class m extends a.LineDataSourceDefinitionsViewModel{_stylePropertyDefinitions(){const e=[],t=this._source.properties().childs(),i=this._source.name(),o=(0,c.removeSpaces)(i),a=new r.TranslatedString(i,this._source.translatedType()),m=t.trendline.childs(),x=(0,l.createLineStyleDefinition)(this._propertyApplier,{showLine:m.visible,lineColor:m.color,lineStyle:m.linestyle,lineWidth:m.linewidth},a,"TrendLine",{line:w});e.push(x);const L=this._source.levelsCount();for(let i=1;i<=L;i++){const n=t[`level${i}`].childs(),r=(0,s.createLeveledLinePropertyDefinition)({checked:(0,s.convertToDefinitionProperty)(this._propertyApplier,n.visible,p.format({title:a,index:i})),color:(0,s.getColorDefinitionProperty)(this._propertyApplier,n.color,null,h.format({title:a,index:i})),width:(0,s.convertToDefinitionProperty)(this._propertyApplier,n.linewidth,y.format({title:a,index:i})),level:(0,s.convertToDefinitionProperty)(this._propertyApplier,n.coeff,f.format({title:a,index:i}))},{id:`${o}LineLevel${i}`});e.push(r)}const C=(0,s.createColorPropertyDefinition)({
color:(0,s.getColorDefinitionProperty)(this._propertyApplier,new d.CollectibleColorPropertyUndoWrapper((0,n.ensureNotNull)(this._source.lineColorsProperty()),this._propertyApplier,null),null,v.format({title:a}),!0)},{id:`${o}AllLineColor`,title:_});e.push(C);const k=(0,s.createTransparencyPropertyDefinition)({checked:(0,s.convertToDefinitionProperty)(this._propertyApplier,t.fillBackground,g.format({title:a})),transparency:(0,s.convertToDefinitionProperty)(this._propertyApplier,t.transparency,T.format({title:a}))},{id:`${o}Background`,title:P});e.push(k);const A=(0,s.createCheckablePropertyDefinition)({checked:(0,s.convertToDefinitionProperty)(this._propertyApplier,t.showCoeffs,u.format({title:a}))},{id:`${o}Levels`,title:S});e.push(A);const V=(0,s.createCheckablePropertyDefinition)({checked:(0,s.convertToDefinitionProperty)(this._propertyApplier,t.fullCircles,D.format({title:a}))},{id:`${o}FullCircles`,title:b});return e.push(V),{definitions:e}}}},72605:(e,t,i)=>{i.r(t),i.d(t,{FibSpeedResistanceFanDefinitionsViewModel:()=>B});var n=i(50151),o=i(11542),r=i(45126),l=i(72491),s=i(18009),a=i(49406),c=i(95166);const d=new r.TranslatedString("change {title} level {index} line visibility",o.t(null,void 0,i(51403))),p=new r.TranslatedString("change {title} level {index} line color",o.t(null,void 0,i(664))),u=new r.TranslatedString("change {title} level {index} line coeff",o.t(null,void 0,i(27154))),h=new r.TranslatedString("change {title} all lines color",o.t(null,void 0,i(59577))),y=new r.TranslatedString("change {title} background visibility",o.t(null,void 0,i(30839))),f=new r.TranslatedString("change {title} background transparency",o.t(null,void 0,i(13783))),v=new r.TranslatedString("change {title} left labels visibility",o.t(null,void 0,i(63021))),g=new r.TranslatedString("change {title} right labels visibility",o.t(null,void 0,i(8390))),T=new r.TranslatedString("change {title} top labels visibility",o.t(null,void 0,i(81301))),D=new r.TranslatedString("change {title} bottom labels visibility",o.t(null,void 0,i(62130))),w=new r.TranslatedString("change {title} reverse",o.t(null,void 0,i(50762))),_=new r.TranslatedString("change {title} grid visibility",o.t(null,void 0,i(20664))),P=new r.TranslatedString("change {title} grid line color",o.t(null,void 0,i(36467))),S=new r.TranslatedString("change {title} grid line width",o.t(null,void 0,i(30127))),b=new r.TranslatedString("change {title} grid line style",o.t(null,void 0,i(54244))),m=o.t(null,void 0,i(28683)),x=o.t(null,void 0,i(79468)),L=o.t(null,void 0,i(58557)),C=o.t(null,void 0,i(58476)),k=o.t(null,void 0,i(65e3)),A=o.t(null,void 0,i(28971)),V=o.t(null,void 0,i(74939)),$=o.t(null,void 0,i(17129)),M=o.t(null,void 0,i(81356)),W=o.t(null,void 0,i(64489));class B extends s.LineDataSourceDefinitionsViewModel{_stylePropertyDefinitions(){const e=[],t=this._source.properties().childs(),i=this._source.name(),o=(0,a.removeSpaces)(i),s=new r.TranslatedString(i,this._source.translatedType()),B=[],z=this._source.hLevelsCount();for(let e=1;e<=z;e++){
const i=t[`hlevel${e}`].childs(),n=(0,l.createLeveledLinePropertyDefinition)({checked:(0,l.convertToDefinitionProperty)(this._propertyApplier,i.visible,d.format({title:s,index:e})),color:(0,l.getColorDefinitionProperty)(this._propertyApplier,i.color,null,p.format({title:s,index:e})),level:(0,l.convertToDefinitionProperty)(this._propertyApplier,i.coeff,u.format({title:s,index:e}))},{id:`${o}HLineLevel${e}`});B.push(n)}const N=(0,l.createPropertyDefinitionsLeveledLinesGroup)(B,`${o}HLeveledLinesGroup`),R=(0,l.createCheckablePropertyDefinition)({checked:(0,l.convertToDefinitionProperty)(this._propertyApplier,t.showLeftLabels,v.format({title:s}))},{id:`${o}LeftLabels`,title:k}),G=(0,l.createCheckablePropertyDefinition)({checked:(0,l.convertToDefinitionProperty)(this._propertyApplier,t.showRightLabels,g.format({title:s}))},{id:`${o}RightLabels`,title:A}),E=(0,l.createPropertyDefinitionsGeneralGroup)([N,R,G],`${o}HLevelGroup`,L);e.push(E);const U=[],O=this._source.vLevelsCount();for(let e=1;e<=O;e++){const i=t[`vlevel${e}`].childs(),n=(0,l.createLeveledLinePropertyDefinition)({checked:(0,l.convertToDefinitionProperty)(this._propertyApplier,i.visible,d.format({title:s,index:e})),color:(0,l.getColorDefinitionProperty)(this._propertyApplier,i.color,null,p.format({title:s,index:e})),level:(0,l.convertToDefinitionProperty)(this._propertyApplier,i.coeff,u.format({title:s,index:e}))},{id:`${o}VLineLevel${e}`});U.push(n)}const I=(0,l.createPropertyDefinitionsLeveledLinesGroup)(U,`${o}VLeveledLinesGroup`),F=(0,l.createCheckablePropertyDefinition)({checked:(0,l.convertToDefinitionProperty)(this._propertyApplier,t.showTopLabels,T.format({title:s}))},{id:`${o}TopLabels`,title:V}),H=(0,l.createCheckablePropertyDefinition)({checked:(0,l.convertToDefinitionProperty)(this._propertyApplier,t.showBottomLabels,D.format({title:s}))},{id:`${o}BottomLabels`,title:$}),j=(0,l.createPropertyDefinitionsGeneralGroup)([I,F,H],`${o}VLevelGroup`,C);e.push(j);const Y=(0,l.createColorPropertyDefinition)({color:(0,l.getColorDefinitionProperty)(this._propertyApplier,new c.CollectibleColorPropertyUndoWrapper((0,n.ensureNotNull)(this._source.lineColorsProperty()),this._propertyApplier,null),null,h.format({title:s}),!0)},{id:`${o}AllLineColor`,title:m});e.push(Y);const X=(0,l.createTransparencyPropertyDefinition)({checked:(0,l.convertToDefinitionProperty)(this._propertyApplier,t.fillBackground,y.format({title:s})),transparency:(0,l.convertToDefinitionProperty)(this._propertyApplier,t.transparency,f.format({title:s}))},{id:`${o}Background`,title:x});e.push(X);const q=t.grid.childs(),J=(0,l.createLinePropertyDefinition)({checked:(0,l.convertToDefinitionProperty)(this._propertyApplier,q.visible,_.format({title:s})),color:(0,l.getColorDefinitionProperty)(this._propertyApplier,q.color,null,P.format({title:s})),width:(0,l.convertToDefinitionProperty)(this._propertyApplier,q.linewidth,S.format({title:s})),style:(0,l.convertToDefinitionProperty)(this._propertyApplier,q.linestyle,b.format({title:s}))},{id:`${o}GridLine`,title:M});e.push(J);const K=(0,
l.createCheckablePropertyDefinition)({checked:(0,l.convertToDefinitionProperty)(this._propertyApplier,t.reverse,w.format({title:s}))},{id:`${o}Reverse`,title:W});return e.push(K),{definitions:e}}}},24277:(e,t,i)=>{i.r(t),i.d(t,{FibSpiralDefinitionsViewModel:()=>y});var n=i(11542),o=i(45126),r=i(72491),l=i(18009),s=i(49406);const a=new o.TranslatedString("change {title} line color",n.t(null,void 0,i(7455))),c=new o.TranslatedString("change {title} line width",n.t(null,void 0,i(46040))),d=new o.TranslatedString("change {title} line style",n.t(null,void 0,i(30843))),p=new o.TranslatedString("change {title} counterclockwise",n.t(null,void 0,i(60003))),u=n.t(null,void 0,i(3554)),h=n.t(null,void 0,i(33004));class y extends l.LineDataSourceDefinitionsViewModel{_stylePropertyDefinitions(){const e=this._source.properties().childs(),t=this._source.name(),i=(0,s.removeSpaces)(t),n=new o.TranslatedString(t,this._source.translatedType());return{definitions:[(0,r.createLinePropertyDefinition)({color:(0,r.getColorDefinitionProperty)(this._propertyApplier,e.linecolor,null,a.format({title:n})),width:(0,r.convertToDefinitionProperty)(this._propertyApplier,e.linewidth,c.format({title:n})),style:(0,r.convertToDefinitionProperty)(this._propertyApplier,e.linestyle,d.format({title:n}))},{id:`${i}Line`,title:u}),(0,r.createCheckablePropertyDefinition)({checked:(0,r.convertToDefinitionProperty)(this._propertyApplier,e.counterclockwise,p.format({title:n}))},{id:`${i}Counterclockwise`,title:h})]}}}},23720:(e,t,i)=>{i.r(t),i.d(t,{FibTimezoneDefinitionsViewModel:()=>x,availableAlignmentHorizontalItems:()=>m,availableAlignmentVerticalItems:()=>b});var n=i(50151),o=i(11542),r=i(45126),l=i(72491),s=i(18009),a=i(64147),c=i(49406),d=i(95166);const p=new r.TranslatedString("change {title} level {index} line visibility",o.t(null,void 0,i(51403))),u=new r.TranslatedString("change {title} level {index} line color",o.t(null,void 0,i(664))),h=new r.TranslatedString("change {title} level {index} line width",o.t(null,void 0,i(97870))),y=new r.TranslatedString("change {title} level {index} line style",o.t(null,void 0,i(64707))),f=new r.TranslatedString("change {title} level {index} line coeff",o.t(null,void 0,i(27154))),v=new r.TranslatedString("change {title} all lines color",o.t(null,void 0,i(59577))),g=new r.TranslatedString("change {title} background visibility",o.t(null,void 0,i(30839))),T=new r.TranslatedString("change {title} background transparency",o.t(null,void 0,i(13783))),D=new r.TranslatedString("change {title} labels visibility",o.t(null,void 0,i(93340))),w=new r.TranslatedString("change {title} labels alignment",o.t(null,void 0,i(58312))),_=o.t(null,void 0,i(28683)),P=o.t(null,void 0,i(79468)),S=o.t(null,void 0,i(5119)),b=[{id:"top",value:"top",title:o.t(null,void 0,i(97118))},{id:"middle",value:"middle",title:o.t(null,void 0,i(68833))},{id:"bottom",value:"bottom",title:o.t(null,void 0,i(27567))}],m=[{id:"left",value:"left",title:o.t(null,void 0,i(11626))},{id:"center",value:"center",title:o.t(null,void 0,i(24197))},{id:"right",value:"right",
title:o.t(null,void 0,i(50421))}];class x extends s.LineDataSourceDefinitionsViewModel{_stylePropertyDefinitions(){const e=[],t=this._source.properties().childs(),i=this._source.name(),o=(0,c.removeSpaces)(i),s=new r.TranslatedString(i,this._source.translatedType()),x=this._source.levelsCount();for(let i=1;i<=x;i++){const n=t[`level${i}`].childs(),r=(0,l.createLeveledLinePropertyDefinition)({checked:(0,l.convertToDefinitionProperty)(this._propertyApplier,n.visible,p.format({title:s,index:i})),color:(0,l.getColorDefinitionProperty)(this._propertyApplier,n.color,null,u.format({title:s,index:i})),width:(0,l.convertToDefinitionProperty)(this._propertyApplier,n.linewidth,h.format({title:s,index:i})),style:(0,l.convertToDefinitionProperty)(this._propertyApplier,n.linestyle,y.format({title:s,index:i})),level:(0,l.convertToDefinitionProperty)(this._propertyApplier,n.coeff,f.format({title:s,index:i}))},{id:`${o}LineLevel${i}`});e.push(r)}const L=(0,l.createColorPropertyDefinition)({color:(0,l.getColorDefinitionProperty)(this._propertyApplier,new d.CollectibleColorPropertyUndoWrapper((0,n.ensureNotNull)(this._source.lineColorsProperty()),this._propertyApplier,null),null,v.format({title:s}),!0)},{id:`${o}AllLineColor`,title:_});e.push(L);const C=(0,l.createTransparencyPropertyDefinition)({checked:(0,l.convertToDefinitionProperty)(this._propertyApplier,t.fillBackground,g.format({title:s})),transparency:(0,l.convertToDefinitionProperty)(this._propertyApplier,t.transparency,T.format({title:s}))},{id:`${o}Background`,title:P});e.push(C);const k=(0,l.createTwoOptionsPropertyDefinition)({checked:(0,l.convertToDefinitionProperty)(this._propertyApplier,t.showLabels,D.format({title:s})),option1:(0,l.convertToDefinitionProperty)(this._propertyApplier,t.horzLabelsAlign,w.format({title:s})),option2:(0,l.convertToDefinitionProperty)(this._propertyApplier,t.vertLabelsAlign,w.format({title:s}))},{id:`${o}Labels`,title:S,optionsItems1:new a.WatchedValue(m),optionsItems2:new a.WatchedValue(b)});return e.push(k),{definitions:e}}}},49267:(e,t,i)=>{i.r(t),i.d(t,{FibWedgeDefinitionsViewModel:()=>S});var n=i(50151),o=i(11542),r=i(45126),l=i(53749),s=i(72491),a=i(18009),c=i(49406),d=i(95166);const p=new r.TranslatedString("change {title} level {index} line visibility",o.t(null,void 0,i(51403))),u=new r.TranslatedString("change {title} levels visibility",o.t(null,void 0,i(54517))),h=new r.TranslatedString("change {title} level {index} line color",o.t(null,void 0,i(664))),y=new r.TranslatedString("change {title} level {index} line width",o.t(null,void 0,i(97870))),f=new r.TranslatedString("change {title} level {index} line coeff",o.t(null,void 0,i(27154))),v=new r.TranslatedString("change {title} all lines color",o.t(null,void 0,i(59577))),g=new r.TranslatedString("change {title} background visibility",o.t(null,void 0,i(30839))),T=new r.TranslatedString("change {title} background transparency",o.t(null,void 0,i(13783))),D=o.t(null,void 0,i(51574)),w=o.t(null,void 0,i(28683)),_=o.t(null,void 0,i(79468)),P=o.t(null,void 0,i(79650))
;class S extends a.LineDataSourceDefinitionsViewModel{_stylePropertyDefinitions(){const e=[],t=this._source.properties().childs(),i=this._source.name(),o=(0,c.removeSpaces)(i),a=new r.TranslatedString(i,this._source.translatedType()),S=t.trendline.childs(),b=(0,l.createLineStyleDefinition)(this._propertyApplier,{showLine:S.visible,lineColor:S.color,lineWidth:S.linewidth},a,"TrendLine",{line:D});e.push(b);const m=this._source.levelsCount();for(let i=1;i<=m;i++){const n=t[`level${i}`].childs(),r=(0,s.createLeveledLinePropertyDefinition)({checked:(0,s.convertToDefinitionProperty)(this._propertyApplier,n.visible,p.format({title:a,index:i})),color:(0,s.getColorDefinitionProperty)(this._propertyApplier,n.color,null,h.format({title:a,index:i})),width:(0,s.convertToDefinitionProperty)(this._propertyApplier,n.linewidth,y.format({title:a,index:i})),level:(0,s.convertToDefinitionProperty)(this._propertyApplier,n.coeff,f.format({title:a,index:i}))},{id:`${o}LineLevel${i}`});e.push(r)}const x=(0,s.createColorPropertyDefinition)({color:(0,s.getColorDefinitionProperty)(this._propertyApplier,new d.CollectibleColorPropertyUndoWrapper((0,n.ensureNotNull)(this._source.lineColorsProperty()),this._propertyApplier,null),null,v.format({title:a}),!0)},{id:`${o}AllLineColor`,title:w});e.push(x);const L=(0,s.createTransparencyPropertyDefinition)({checked:(0,s.convertToDefinitionProperty)(this._propertyApplier,t.fillBackground,g.format({title:a})),transparency:(0,s.convertToDefinitionProperty)(this._propertyApplier,t.transparency,T.format({title:a}))},{id:`${o}Background`,title:_});e.push(L);const C=(0,s.createCheckablePropertyDefinition)({checked:(0,s.convertToDefinitionProperty)(this._propertyApplier,t.showCoeffs,u.format({title:a}))},{id:`${o}Levels`,title:P});return e.push(C),{definitions:e}}}},11138:(e,t,i)=>{i.r(t),i.d(t,{FlagMarkDefinitionsViewModel:()=>c});var n=i(11542),o=i(45126),r=i(18009),l=i(72491);const s=new o.TranslatedString("change flag color",n.t(null,void 0,i(77883))),a=n.t(null,void 0,i(33885));class c extends r.LineDataSourceDefinitionsViewModel{_stylePropertyDefinitions(){const e=this._source.properties().childs();return{definitions:[(0,l.createColorPropertyDefinition)({color:(0,l.getColorDefinitionProperty)(this._propertyApplier,e.flagColor,null,s)},{id:"FlagColor",title:a})]}}}},53894:(e,t,i)=>{i.r(t),i.d(t,{GannComplexAndFixedDefinitionsViewModel:()=>N,isGannComplexLineTool:()=>z});var n=i(50151),o=i(11542),r=i(45126),l=i(91335),s=i(72491),a=i(18009),c=i(99083),d=i(64147),p=i(52305),u=i(49406),h=i(95166)
;const y=new r.TranslatedString("change {title} level {index} line visibility",o.t(null,void 0,i(51403))),f=new r.TranslatedString("change {title} level {index} line color",o.t(null,void 0,i(664))),v=new r.TranslatedString("change {title} level {index} line width",o.t(null,void 0,i(97870))),g=new r.TranslatedString("change {title} all lines color",o.t(null,void 0,i(59577))),T=new r.TranslatedString("change {title} background visibility",o.t(null,void 0,i(30839))),D=new r.TranslatedString("change {title} background transparency",o.t(null,void 0,i(13783))),w=new r.TranslatedString("change {title} reverse",o.t(null,void 0,i(50762))),_=new r.TranslatedString("change {title} fan {index} line visibility",o.t(null,void 0,i(28833))),P=new r.TranslatedString("change {title} fan {index} line color",o.t(null,void 0,i(62500))),S=new r.TranslatedString("change {title} fan {index} line width",o.t(null,void 0,i(6298))),b=new r.TranslatedString("change {title} arcs {index} line visibility",o.t(null,void 0,i(4313))),m=new r.TranslatedString("change {title} arcs {index} line color",o.t(null,void 0,i(95582))),x=new r.TranslatedString("change {title} arcs {index} line width",o.t(null,void 0,i(8745))),L=new r.TranslatedString("change top margin",o.t(null,void 0,i(74883))),C=o.t(null,void 0,i(64489)),k=o.t(null,void 0,i(28683)),A=o.t(null,void 0,i(79468)),V=o.t(null,void 0,i(78393)),$=o.t(null,void 0,i(27177)),M=o.t(null,void 0,i(79650)),W=o.t(null,void 0,i(84885)),B=o.t(null,void 0,i(59129));function z(e){return e instanceof c.LineToolGannComplex}class N extends a.LineDataSourceDefinitionsViewModel{_stylePropertyDefinitions(){const e=[],t=this._source.properties().childs(),i=this._source.name(),o=(0,u.removeSpaces)(i),a=new r.TranslatedString(i,this._source.translatedType()),c=[],N=t.levels.childCount();for(let e=0;e<N;e++){const i=t.levels.childs()[e].childs(),n=(0,s.createLeveledLinePropertyDefinition)({checked:(0,s.convertToDefinitionProperty)(this._propertyApplier,i.visible,y.format({title:a,index:e})),color:(0,s.getColorDefinitionProperty)(this._propertyApplier,i.color,null,f.format({title:a,index:e})),width:(0,s.convertToDefinitionProperty)(this._propertyApplier,i.width,v.format({title:a,index:e}))},{id:`${o}LineLevel${e}`,title:`${e}`});c.push(n)}const R=(0,s.createPropertyDefinitionsLeveledLinesGroup)(c,`${o}LeveledLinesGroup`);e.push((0,s.createPropertyDefinitionsGeneralGroup)([R],`${o}LevelGroup`,M));const G=[],E=t.fanlines.childCount();for(let e=0;e<E;e++){const i=t.fanlines.childs()[e].childs(),n=(0,s.createLeveledLinePropertyDefinition)({checked:(0,s.convertToDefinitionProperty)(this._propertyApplier,i.visible,_.format({title:a,index:e})),color:(0,s.getColorDefinitionProperty)(this._propertyApplier,i.color,null,P.format({title:a,index:e})),width:(0,s.convertToDefinitionProperty)(this._propertyApplier,i.width,S.format({title:a,index:e}))},{id:`${o}FanLineLevel${e}`,title:`${i.x.value()}x${i.y.value()}`});G.push(n)}const U=(0,s.createPropertyDefinitionsLeveledLinesGroup)(G,`${o}FanLeveledLinesGroup`);e.push((0,
s.createPropertyDefinitionsGeneralGroup)([U],`${o}FanLinesGroup`,W));const O=[],I=t.arcs.childCount();for(let e=0;e<I;e++){const i=t.arcs.childs()[e].childs(),n=(0,s.createLeveledLinePropertyDefinition)({checked:(0,s.convertToDefinitionProperty)(this._propertyApplier,i.visible,b.format({title:a,index:e})),color:(0,s.getColorDefinitionProperty)(this._propertyApplier,i.color,null,m.format({title:a,index:e})),width:(0,s.convertToDefinitionProperty)(this._propertyApplier,i.width,x.format({title:a,index:e}))},{id:`${o}ArcsLineLevel${e}`,title:`${i.x.value()}x${i.y.value()}`});O.push(n)}const F=(0,s.createPropertyDefinitionsLeveledLinesGroup)(O,`${o}ArcsLeveledLinesGroup`);e.push((0,s.createPropertyDefinitionsGeneralGroup)([F],`${o}ArcsLinesGroup`,B));const H=(0,s.createColorPropertyDefinition)({color:(0,s.getColorDefinitionProperty)(this._propertyApplier,new h.CollectibleColorPropertyUndoWrapper((0,n.ensureNotNull)(this._source.lineColorsProperty()),this._propertyApplier,g.format({title:a})),null,null)},{id:`${o}AllLineColor`,title:k});e.push(H);const j=t.arcsBackground.childs(),Y=(0,s.createTransparencyPropertyDefinition)({checked:(0,s.convertToDefinitionProperty)(this._propertyApplier,j.fillBackground,T.format({title:a})),transparency:(0,s.convertToDefinitionProperty)(this._propertyApplier,j.transparency,D.format({title:a}))},{id:`${o}Background`,title:A});e.push(Y);const X=(0,s.createCheckablePropertyDefinition)({checked:(0,s.convertToDefinitionProperty)(this._propertyApplier,t.reverse,w.format({title:a}))},{id:`${o}Reverse`,title:C});if(e.push(X),z(this._source)){const t=this._source,i=t.properties().childs(),n=(0,s.createNumberPropertyDefinition)({value:(0,s.convertToDefinitionProperty)(this._propertyApplier,i.scaleRatio,L,[(0,p.limitedPrecision)(7),e=>null!==e?parseFloat(t.getScaleRatioFormatter().format(e,{ignoreLocaleNumberFormat:!0})):null])},{id:"scaleRatio",title:V,min:new d.WatchedValue(1e-7),max:new d.WatchedValue(1e8),step:new d.WatchedValue(t.getScaleRatioStep())});e.push(n);const o=i.labelsStyle.childs(),r=(0,l.createTextStyleDefinition)(this._propertyApplier,{showText:i.showLabels,fontSize:o.fontSize,bold:o.bold,italic:o.italic},a,{customTitles:{text:$}});e.push(r)}return{definitions:e}}}},72443:(e,t,i)=>{i.r(t),i.d(t,{GannFanDefinitionsViewModel:()=>_});var n=i(50151),o=i(11542),r=i(45126),l=i(72491),s=i(18009),a=i(49406),c=i(95166)
;const d=new r.TranslatedString("change {title} level {index} line visibility",o.t(null,void 0,i(51403))),p=new r.TranslatedString("change {title} level {index} line color",o.t(null,void 0,i(664))),u=new r.TranslatedString("change {title} level {index} line width",o.t(null,void 0,i(97870))),h=new r.TranslatedString("change {title} level {index} line style",o.t(null,void 0,i(64707))),y=new r.TranslatedString("change {title} all lines color",o.t(null,void 0,i(59577))),f=new r.TranslatedString("change {title} background visibility",o.t(null,void 0,i(30839))),v=new r.TranslatedString("change {title} background transparency",o.t(null,void 0,i(13783))),g=new r.TranslatedString("change {title} labels visibility",o.t(null,void 0,i(93340))),T=o.t(null,void 0,i(28683)),D=o.t(null,void 0,i(79468)),w=o.t(null,void 0,i(5119));class _ extends s.LineDataSourceDefinitionsViewModel{_stylePropertyDefinitions(){const e=[],t=this._source.properties().childs(),i=this._source.name(),o=(0,a.removeSpaces)(i),s=new r.TranslatedString(i,this._source.translatedType()),_=this._source.levelsCount();for(let i=1;i<=_;i++){const n=t[`level${i}`].childs(),r=(0,l.createLeveledLinePropertyDefinition)({checked:(0,l.convertToDefinitionProperty)(this._propertyApplier,n.visible,d.format({title:s,index:i})),color:(0,l.getColorDefinitionProperty)(this._propertyApplier,n.color,null,p.format({title:s,index:i})),width:(0,l.convertToDefinitionProperty)(this._propertyApplier,n.linewidth,u.format({title:s,index:i})),style:(0,l.convertToDefinitionProperty)(this._propertyApplier,n.linestyle,h.format({title:s,index:i}))},{id:`${o}LineLevel${i}`,title:`${n.coeff1.value()}/${n.coeff2.value()}`});e.push(r)}const P=(0,l.createColorPropertyDefinition)({color:(0,l.getColorDefinitionProperty)(this._propertyApplier,new c.CollectibleColorPropertyUndoWrapper((0,n.ensureNotNull)(this._source.lineColorsProperty()),this._propertyApplier,null),null,y.format({title:s}))},{id:`${o}AllLineColor`,title:T});e.push(P);const S=(0,l.createTransparencyPropertyDefinition)({checked:(0,l.convertToDefinitionProperty)(this._propertyApplier,t.fillBackground,f.format({title:s})),transparency:(0,l.convertToDefinitionProperty)(this._propertyApplier,t.transparency,v.format({title:s}))},{id:`${o}Background`,title:D});e.push(S);const b=(0,l.createCheckablePropertyDefinition)({checked:(0,l.convertToDefinitionProperty)(this._propertyApplier,t.showLabels,g.format({title:s}))},{id:`${o}Labels`,title:w});return e.push(b),{definitions:e}}}},27340:(e,t,i)=>{i.r(t),i.d(t,{GannSquareDefinitionsViewModel:()=>M});var n=i(50151),o=i(11542),r=i(45126),l=i(72491),s=i(18009),a=i(49406),c=i(95166)
;const d=new r.TranslatedString("change {title} level {index} line visibility",o.t(null,void 0,i(51403))),p=new r.TranslatedString("change {title} level {index} line color",o.t(null,void 0,i(664))),u=new r.TranslatedString("change {title} level {index} line coeff",o.t(null,void 0,i(27154))),h=new r.TranslatedString("change {title} all lines color",o.t(null,void 0,i(59577))),y=new r.TranslatedString("change {title} background visibility",o.t(null,void 0,i(30839))),f=new r.TranslatedString("change {title} background transparency",o.t(null,void 0,i(13783))),v=new r.TranslatedString("change {title} reverse",o.t(null,void 0,i(50762))),g=new r.TranslatedString("change {title} left labels visibility",o.t(null,void 0,i(63021))),T=new r.TranslatedString("change {title} right labels visibility",o.t(null,void 0,i(8390))),D=new r.TranslatedString("change {title} top labels visibility",o.t(null,void 0,i(81301))),w=new r.TranslatedString("change {title} bottom labels visibility",o.t(null,void 0,i(62130))),_=new r.TranslatedString("change {title} fans visibility",o.t(null,void 0,i(15972))),P=new r.TranslatedString("change {title} fans line color",o.t(null,void 0,i(1716))),S=o.t(null,void 0,i(28683)),b=o.t(null,void 0,i(79468)),m=o.t(null,void 0,i(58557)),x=o.t(null,void 0,i(58476)),L=o.t(null,void 0,i(65e3)),C=o.t(null,void 0,i(28971)),k=o.t(null,void 0,i(74939)),A=o.t(null,void 0,i(17129)),V=o.t(null,void 0,i(36335)),$=o.t(null,void 0,i(64489));class M extends s.LineDataSourceDefinitionsViewModel{_stylePropertyDefinitions(){const e=[],t=this._source.properties().childs(),i=this._source.name(),o=(0,a.removeSpaces)(i),s=new r.TranslatedString(i,this._source.translatedType()),M=[],W=this._source.hLevelsCount();for(let e=1;e<=W;e++){const i=t[`hlevel${e}`].childs(),n=(0,l.createLeveledLinePropertyDefinition)({checked:(0,l.convertToDefinitionProperty)(this._propertyApplier,i.visible,d.format({title:s,index:e})),color:(0,l.getColorDefinitionProperty)(this._propertyApplier,i.color,null,p.format({title:s,index:e})),level:(0,l.convertToDefinitionProperty)(this._propertyApplier,i.coeff,u.format({title:s,index:e}))},{id:`${o}HLineLevel${e}`});M.push(n)}const B=(0,l.createPropertyDefinitionsLeveledLinesGroup)(M,`${o}HLeveledLinesGroup`),z=(0,l.createCheckablePropertyDefinition)({checked:(0,l.convertToDefinitionProperty)(this._propertyApplier,t.showLeftLabels,g.format({title:s}))},{id:`${o}LeftLabels`,title:L}),N=(0,l.createCheckablePropertyDefinition)({checked:(0,l.convertToDefinitionProperty)(this._propertyApplier,t.showRightLabels,T.format({title:s}))},{id:`${o}RightLabels`,title:C}),R=(0,l.createTransparencyPropertyDefinition)({checked:(0,l.convertToDefinitionProperty)(this._propertyApplier,t.fillHorzBackground,y.format({title:s})),transparency:(0,l.convertToDefinitionProperty)(this._propertyApplier,t.horzTransparency,f.format({title:s}))},{id:`${o}BackgroundH`,title:b}),G=(0,l.createPropertyDefinitionsGeneralGroup)([B,z,N,R],`${o}HLevelGroup`,m);e.push(G);const E=[],U=this._source.vLevelsCount();for(let e=1;e<=U;e++){
const i=t[`vlevel${e}`].childs(),n=(0,l.createLeveledLinePropertyDefinition)({checked:(0,l.convertToDefinitionProperty)(this._propertyApplier,i.visible,d.format({title:s,index:e})),color:(0,l.getColorDefinitionProperty)(this._propertyApplier,i.color,null,p.format({title:s,index:e})),level:(0,l.convertToDefinitionProperty)(this._propertyApplier,i.coeff,u.format({title:s,index:e}))},{id:`${o}VLineLevel${e}`});E.push(n)}const O=(0,l.createPropertyDefinitionsLeveledLinesGroup)(E,`${o}VLeveledLinesGroup`),I=(0,l.createCheckablePropertyDefinition)({checked:(0,l.convertToDefinitionProperty)(this._propertyApplier,t.showTopLabels,D.format({title:s}))},{id:`${o}TopLabels`,title:k}),F=(0,l.createCheckablePropertyDefinition)({checked:(0,l.convertToDefinitionProperty)(this._propertyApplier,t.showBottomLabels,w.format({title:s}))},{id:`${o}BottomLabels`,title:A}),H=(0,l.createTransparencyPropertyDefinition)({checked:(0,l.convertToDefinitionProperty)(this._propertyApplier,t.fillVertBackground,y.format({title:s})),transparency:(0,l.convertToDefinitionProperty)(this._propertyApplier,t.vertTransparency,f.format({title:s}))},{id:`${o}BackgroundV`,title:b}),j=(0,l.createPropertyDefinitionsGeneralGroup)([O,I,F,H],`${o}VLevelGroup`,x);e.push(j);const Y=(0,l.createColorPropertyDefinition)({color:(0,l.getColorDefinitionProperty)(this._propertyApplier,new c.CollectibleColorPropertyUndoWrapper((0,n.ensureNotNull)(this._source.lineColorsProperty()),this._propertyApplier,null),null,h.format({title:s}),!0)},{id:`${o}AllLineColor`,title:S});e.push(Y);const X=t.fans.childs(),q=(0,l.createColorPropertyDefinition)({checked:(0,l.convertToDefinitionProperty)(this._propertyApplier,X.visible,_.format({title:s})),color:(0,l.getColorDefinitionProperty)(this._propertyApplier,X.color,null,P.format({title:s}))},{id:`${o}FansLines`,title:V});e.push(q);const J=(0,l.createCheckablePropertyDefinition)({checked:(0,l.convertToDefinitionProperty)(this._propertyApplier,t.reverse,v.format({title:s}))},{id:`${o}Reverse`,title:$});return e.push(J),{definitions:e}}}},86622:(e,t,i)=>{i.r(t),i.d(t,{GeneralBezierDefinitionsViewModel:()=>u});var n=i(11542),o=i(45126),r=i(53749),l=i(18009),s=i(72491),a=i(49406);const c=new o.TranslatedString("change {title} background visibility",n.t(null,void 0,i(30839))),d=new o.TranslatedString("change {title} background color",n.t(null,void 0,i(49765))),p=n.t(null,void 0,i(79468));class u extends l.LineDataSourceDefinitionsViewModel{_stylePropertyDefinitions(){const e=this._source.properties().childs(),t=this._source.name(),i=new o.TranslatedString(t,this._source.translatedType());return{definitions:[(0,r.createLineStyleDefinition)(this._undoModel,{lineColor:e.linecolor,lineWidth:e.linewidth,lineStyle:e.linestyle,extendLeft:e.extendLeft,extendRight:e.extendRight,leftEnd:e.leftEnd,rightEnd:e.rightEnd},i,"Line"),(0,s.createColorPropertyDefinition)({checked:(0,s.convertToDefinitionProperty)(this._undoModel,e.fillBackground,c.format({title:i})),color:(0,s.getColorDefinitionProperty)(this._undoModel,e.backgroundColor,e.transparency,d.format({
title:i}))},{id:(0,a.removeSpaces)(`${t}BackgroundColor`),title:p})]}}}},14336:(e,t,i)=>{i.r(t),i.d(t,{GeneralDatePriceRangeDefinitionsViewModel:()=>m});var n=i(11542),o=i(45126),r=i(53749),l=i(91335),s=i(18009),a=i(72491),c=i(49406);const d=new o.TranslatedString("change {title} background visibility",n.t(null,void 0,i(30839))),p=new o.TranslatedString("change {title} background color",n.t(null,void 0,i(49765))),u=new o.TranslatedString("change {title} extend top",n.t(null,void 0,i(20867))),h=new o.TranslatedString("change {title} extend bottom",n.t(null,void 0,i(59665))),y=new o.TranslatedString("change {title} extend left",n.t(null,void 0,i(35139))),f=n.t(null,void 0,i(3554)),v=n.t(null,void 0,i(38408)),g=n.t(null,void 0,i(79468)),T=n.t(null,void 0,i(71397)),D=n.t(null,void 0,i(76410)),w=n.t(null,void 0,i(25112)),_=n.t(null,void 0,i(68461)),P=n.t(null,void 0,i(74872)),S=n.t(null,void 0,i(73863)),b=n.t(null,void 0,i(70320));class m extends s.LineDataSourceDefinitionsViewModel{_stylePropertyDefinitions(){const e=[],t=this._source.properties().childs(),i=this._source.name(),n=(0,c.removeSpaces)(i),s=new o.TranslatedString(i,this._source.translatedType()),b=(0,r.createLineStyleDefinition)(this._propertyApplier,{lineColor:t.linecolor,lineWidth:t.linewidth},s,"Line",{line:f});if(e.push(b),t.hasOwnProperty("borderWidth")){const i=(0,r.createLineStyleDefinition)(this._propertyApplier,{showLine:t.drawBorder,lineColor:t.borderColor,lineWidth:t.borderWidth},s,"Border",{line:v});e.push(i)}const m=(0,a.createColorPropertyDefinition)({checked:(0,a.convertToDefinitionProperty)(this._propertyApplier,t.fillBackground,d.format({title:s})),color:(0,a.getColorDefinitionProperty)(this._propertyApplier,t.backgroundColor,t.backgroundTransparency,p.format({title:s}))},{id:`${n}BackgroundColor`,title:g});if(e.push(m),function(e){return e.hasOwnProperty("extendTop")}(t)){const i=(0,a.createCheckablePropertyDefinition)({checked:(0,a.convertToDefinitionProperty)(this._propertyApplier,t.extendTop,u.format({title:s}))},{id:`${n}ExtendTop`,title:T}),o=(0,a.createCheckablePropertyDefinition)({checked:(0,a.convertToDefinitionProperty)(this._propertyApplier,t.extendBottom,h.format({title:s}))},{id:`${n}ExtendBottom`,title:D});e.push(i,o)}if(function(e){return e.hasOwnProperty("extendLeft")}(t)){const i=(0,a.createCheckablePropertyDefinition)({checked:(0,a.convertToDefinitionProperty)(this._propertyApplier,t.extendLeft,y.format({title:s}))},{id:`${n}extendLeft`,title:w}),o=(0,a.createCheckablePropertyDefinition)({checked:(0,a.convertToDefinitionProperty)(this._propertyApplier,t.extendRight,h.format({title:s}))},{id:`${n}ExtendBottom`,title:_});e.push(i,o)}const x=(0,l.createTextStyleDefinition)(this._propertyApplier,{textColor:t.textcolor,backgroundColor:t.labelBackgroundColor,backgroundTransparency:t.backgroundTransparency,fontSize:t.fontsize,backgroundVisible:t.fillLabelBackground},s,{isEditable:!0,isMultiLine:!0,customTitles:{text:P,backgroundTitle:S}});return e.push(x),{definitions:e}}_textPropertyDefinitions(){
const e=this._source.properties().childs().customText.childs();return{definitions:[(0,l.createTextStyleDefinition)(this._propertyApplier,{...e,showText:e.visible,textColor:e.color,fontSize:e.fontsize},new o.TranslatedString(this._source.name(),this._source.translatedType()),{isEditable:!0,isMultiLine:!0,customTitles:{text:b}})]}}}},90490:(e,t,i)=>{i.r(t),i.d(t,{GeneralFiguresDefinitionsViewModel:()=>y,GeneralFiguresDefinitionsViewModelBase:()=>h});var n=i(11542),o=i(45126),r=i(53749),l=i(18009),s=i(72491),a=i(49406);const c=new o.TranslatedString("change {title} background visibility",n.t(null,void 0,i(30839))),d=new o.TranslatedString("change {title} background color",n.t(null,void 0,i(49765))),p=n.t(null,void 0,i(38408)),u=n.t(null,void 0,i(79468));class h extends l.LineDataSourceDefinitionsViewModel{_stylePropertyDefinitions(){const e=this._source.properties().childs(),t=this._source.name(),i=new o.TranslatedString(t,this._source.translatedType()),n=(0,r.createLineStyleDefinition)(this._propertyApplier,{lineColor:e.color,lineWidth:e.linewidth},i,"Line",{line:p}),l="transparency"in e?e.transparency:null;return{definitions:[n,(0,s.createColorPropertyDefinition)({checked:(0,s.convertToDefinitionProperty)(this._propertyApplier,e.fillBackground,c.format({title:i})),color:(0,s.getColorDefinitionProperty)(this._propertyApplier,e.backgroundColor,l,d.format({title:i}))},{id:(0,a.removeSpaces)(`${t}BackgroundColor`),title:u})]}}}class y extends h{}},41585:(e,t,i)=>{i.r(t),i.d(t,{GeneralTrendFiguresDefinitionsViewModel:()=>v});var n=i(11542),o=i(45126),r=i(53749),l=i(91335),s=i(18009),a=i(72491),c=i(49406);const d=new o.TranslatedString("change {title} background visibility",n.t(null,void 0,i(30839))),p=new o.TranslatedString("change {title} background color",n.t(null,void 0,i(49765))),u=[{value:"bottom",title:n.t(null,void 0,i(97118))},{value:"middle",title:n.t(null,void 0,i(91612))},{value:"top",title:n.t(null,void 0,i(27567))}],h=n.t(null,void 0,i(70320)),y=n.t(null,void 0,i(29416)),f=n.t(null,void 0,i(79468));class v extends s.LineDataSourceDefinitionsViewModel{_stylePropertyDefinitions(){const e=this._source.properties().childs(),t=this._source.name(),i=new o.TranslatedString(t,this._source.translatedType());return{definitions:[(0,r.createLineStyleDefinition)(this._propertyApplier,{...e,lineColor:e.linecolor,lineWidth:e.linewidth,lineStyle:e.linestyle},i,"Line"),(0,l.createTextStyleDefinition)(this._propertyApplier,{showText:e.showPrices,textColor:e.textcolor,fontSize:e.fontsize,bold:e.bold,italic:e.italic},i,{customTitles:{text:y}}),(0,a.createColorPropertyDefinition)({checked:(0,a.convertToDefinitionProperty)(this._propertyApplier,e.fillBackground,d.format({title:i})),color:(0,a.getColorDefinitionProperty)(this._propertyApplier,e.backgroundColor,e.transparency,p.format({title:i}))},{id:(0,c.removeSpaces)(`${t}Background`),title:f})]}}_textPropertyDefinitions(){const e=this._source.properties().childs();return{definitions:[(0,l.createTextStyleDefinition)(this._propertyApplier,{textColor:e.labelTextColor,
text:e.labelText,bold:e.labelBold,italic:e.labelItalic,fontSize:e.labelFontSize,horzLabelsAlign:e.labelHorzAlign,vertLabelsAlign:e.labelVertAlign,showText:e.labelVisible},new o.TranslatedString(this._source.name(),this._source.translatedType()),{isEditable:!0,isMultiLine:!0,alignmentVerticalItems:u,customTitles:{text:h}})]}}}},9786:(e,t,i)=>{i.r(t),i.d(t,{GhostFeedDefinitionsViewModel:()=>m});var n=i(11542),o=i(45126),r=i(18009),l=i(72491),s=i(64147),a=i(49406);const c=new o.TranslatedString("change {title} candle up color",n.t(null,void 0,i(21631))),d=new o.TranslatedString("change {title} candle down color",n.t(null,void 0,i(80022))),p=new o.TranslatedString("change {title} candle border visibility",n.t(null,void 0,i(88530))),u=new o.TranslatedString("change {title} candle border up color",n.t(null,void 0,i(97345))),h=new o.TranslatedString("change {title} candle border down color",n.t(null,void 0,i(81139))),y=new o.TranslatedString("change {title} candle wick visibility",n.t(null,void 0,i(85382))),f=new o.TranslatedString("change {title} candle wick color",n.t(null,void 0,i(33589))),v=new o.TranslatedString("change {title} transparency",n.t(null,void 0,i(51085))),g=new o.TranslatedString("change {title} average HL value",n.t(null,void 0,i(39393))),T=new o.TranslatedString("change {title} variance value",n.t(null,void 0,i(23171))),D=n.t(null,void 0,i(45054)),w=n.t(null,void 0,i(333)),_=n.t(null,void 0,i(32163)),P=n.t(null,void 0,i(19788)),S=n.t(null,void 0,i(52648)),b=n.t(null,void 0,i(29566));class m extends r.LineDataSourceDefinitionsViewModel{_stylePropertyDefinitions(){const e=this._source.properties().childs(),t=this._source.name(),i=(0,a.removeSpaces)(t),n=new o.TranslatedString(t,this._source.translatedType()),r=e.candleStyle.childs();return{definitions:[(0,l.createTwoColorsPropertyDefinition)({color1:(0,l.getColorDefinitionProperty)(this._propertyApplier,r.upColor,null,c.format({title:n})),color2:(0,l.getColorDefinitionProperty)(this._propertyApplier,r.downColor,null,d.format({title:n}))},{id:`${i}Candle2Colors`,title:D}),(0,l.createTwoColorsPropertyDefinition)({checked:(0,l.convertToDefinitionProperty)(this._propertyApplier,r.drawBorder,p.format({title:n})),color1:(0,l.getColorDefinitionProperty)(this._propertyApplier,r.borderUpColor,null,u.format({title:n})),color2:(0,l.getColorDefinitionProperty)(this._propertyApplier,r.borderDownColor,null,h.format({title:n}))},{id:`${i}CandleBorder2Colors`,title:w}),(0,l.createColorPropertyDefinition)({checked:(0,l.convertToDefinitionProperty)(this._propertyApplier,r.drawWick,y.format({title:n})),color:(0,l.getColorDefinitionProperty)(this._propertyApplier,r.wickColor,null,f.format({title:n}))},{id:`${i}CandleWickColor`,title:_}),(0,l.createTransparencyPropertyDefinition)({transparency:(0,l.convertToDefinitionProperty)(this._propertyApplier,e.transparency,v.format({title:n}))},{id:`${i}Transparency`,title:P})]}}_inputsPropertyDefinitions(){const e=this._source.properties().childs(),t=this._source.name(),i=(0,
a.removeSpaces)(t),n=new o.TranslatedString(t,this._source.translatedType());return{definitions:[(0,l.createNumberPropertyDefinition)({value:(0,l.convertToDefinitionProperty)(this._propertyApplier,e.averageHL,g.format({title:n}))},{id:`${i}AvgHL`,title:S,type:0,min:new s.WatchedValue(1),max:new s.WatchedValue(5e4),step:new s.WatchedValue(1)}),(0,l.createNumberPropertyDefinition)({value:(0,l.convertToDefinitionProperty)(this._propertyApplier,e.variance,T.format({title:n}))},{id:`${i}Variance`,title:b,type:0,min:new s.WatchedValue(1),max:new s.WatchedValue(100),step:new s.WatchedValue(1)})]}}}},14542:(e,t,i)=>{i.r(t),i.d(t,{HighlighterDefinitionsViewModel:()=>l});var n=i(53749),o=i(18009),r=i(45126);class l extends o.LineDataSourceDefinitionsViewModel{_stylePropertyDefinitions(){const e=this._source.properties().childs();return{definitions:[(0,n.createLineStyleDefinition)(this._propertyApplier,{lineColor:e.linecolor},new r.TranslatedString(this._source.name(),this._source.translatedType()),"Line")]}}}},84190:(e,t,i)=>{i.r(t),i.d(t,{HorizontalLineDefinitionsViewModel:()=>h});var n=i(11542),o=i(45126),r=i(72491),l=i(73174),s=i(49406),a=i(18009),c=i(88924),d=i(91335);const p=n.t(null,void 0,i(70320)),u=n.t(null,{context:"linetool point"},i(37814));class h extends a.LineDataSourceDefinitionsViewModel{_stylePropertyDefinitions(){const e=this._source.properties().childs();return(0,c.getLinesStylesPropertiesDefinitions)(this._propertyApplier,e,new o.TranslatedString(this._source.name(),this._source.translatedType()))}_coordinatesPropertyDefinitions(){const e=this._source.pointsProperty().childs().points[0].childs(),t=this._getYCoordinateStepWV(),i=(0,l.getCoordinateYMetaInfo)(this._propertyApplier,e,t);return{definitions:[(0,r.createCoordinatesPropertyDefinition)({y:i.property},{id:(0,s.removeSpaces)(`${this._source.name()}Point`),title:u,...i.info})]}}_textPropertyDefinitions(){const e=this._source.properties().childs();return{definitions:[(0,d.createTextStyleDefinition)(this._propertyApplier,{...e,showText:e.showLabel,textColor:e.textcolor,fontSize:e.fontsize,textOrientation:e.textOrientation},new o.TranslatedString(this._source.name(),this._source.translatedType()),{isEditable:!0,isMultiLine:!0,customTitles:{text:p}})]}}}},22707:(e,t,i)=>{i.r(t),i.d(t,{HorizontalRayDefinitionsViewModel:()=>c});var n=i(11542),o=i(45126),r=i(18009),l=i(88924),s=i(91335);const a=n.t(null,void 0,i(70320));class c extends r.LineDataSourceDefinitionsViewModel{_stylePropertyDefinitions(){const e=this._source.properties().childs();return(0,l.getLinesStylesPropertiesDefinitions)(this._propertyApplier,e,new o.TranslatedString(this._source.name(),this._source.translatedType()))}_textPropertyDefinitions(){const e=this._source.properties().childs();return{definitions:[(0,s.createTextStyleDefinition)(this._propertyApplier,{...e,showText:e.showLabel,textColor:e.textcolor,fontSize:e.fontsize,textOrientation:e.textOrientation},new o.TranslatedString(this._source.name(),this._source.translatedType()),{isEditable:!0,isMultiLine:!0,customTitles:{text:a}})]}}}
},19532:(e,t,i)=>{i.r(t),i.d(t,{IconsDefinitionsViewModel:()=>d});var n=i(11542),o=i(45126),r=i(18009),l=i(72491),s=i(49406);const a=new o.TranslatedString("change {title} color",n.t(null,void 0,i(49442))),c=n.t(null,void 0,i(47370));class d extends r.LineDataSourceDefinitionsViewModel{constructor(e,t){super(e,t)}_stylePropertyDefinitions(){const e=this._source.properties().childs(),t=this._source.name(),i=new o.TranslatedString(t,this._source.translatedType());return{definitions:[(0,l.createColorPropertyDefinition)({color:(0,l.getColorDefinitionProperty)(this._propertyApplier,e.color,null,a.format({title:i}))},{id:(0,s.removeSpaces)(`${t}Color`),title:c})]}}}},65504:(e,t,i)=>{i.r(t),i.d(t,{ImageDefinitionsViewModel:()=>f});var n=i(11542),o=i(45126),r=i(72491),l=i(18009),s=i(18181),a=i(12988);const c=new o.TranslatedString("change image",n.t(null,void 0,i(83842))),d=new o.TranslatedString("change image transparency",n.t(null,void 0,i(65309))),p=n.t(null,void 0,i(68065)),u=n.t(null,void 0,i(19788)),h=n.t(null,void 0,i(81169));function y(e){return null!==(0,s.buildAbsoluteUserImageUrl)(e)?"":h}class f extends l.LineDataSourceDefinitionsViewModel{destroy(){this._source.properties().childs().url.unsubscribeAll(this),super.destroy()}_stylePropertyDefinitions(){const e=this._source.properties().childs();if(void 0===this._customErrorProperty){const t=new a.Property(y(e.url.value()));this._customErrorProperty=t,e.url.subscribe(this,(e=>{t.setValue(y(e.value()))}))}return{definitions:[(0,r.createImagePropertyDefinition)({url:(0,r.convertToDefinitionProperty)(this._propertyApplier,this._source.absoluteUserImageUrl(),c),transparency:(0,r.convertToDefinitionProperty)(this._propertyApplier,e.transparency,d)},{id:"image",title:p}),(0,r.createTransparencyPropertyDefinition)({transparency:(0,r.convertToDefinitionProperty)(this._propertyApplier,e.transparency,d)},{id:"imageTransparency",title:u})]}}}},14531:(e,t,i)=>{i.r(t),i.d(t,{NoteDefinitionsViewModel:()=>p});var n=i(11542),o=i(45126),r=i(91335),l=i(18009),s=i(72491),a=i(49406);const c=new o.TranslatedString("change {title} background color",n.t(null,void 0,i(49765))),d=n.t(null,void 0,i(74872));class p extends l.LineDataSourceDefinitionsViewModel{_stylePropertyDefinitions(){const e=this._source.properties().childs(),t=this._source.name(),i=new o.TranslatedString(t,this._source.translatedType());return{definitions:[(0,s.createColorPropertyDefinition)({color:(0,s.getColorDefinitionProperty)(this._propertyApplier,e.markerColor,null,c.format({title:i}))},{id:(0,a.removeSpaces)(`${t}LabelColor`),title:d})]}}_textPropertyDefinitions(){const e=this._source.properties().childs();return{definitions:[(0,r.createTextStyleDefinition)(this._propertyApplier,{textColor:e.textColor,fontSize:e.fontSize,bold:e.bold,italic:e.italic,text:e.text,backgroundVisible:e.drawBackground,backgroundColor:e.backgroundColor,backgroundTransparency:e.backgroundTransparency,borderVisible:e.drawBorder,borderColor:e.borderColor},new o.TranslatedString(this._source.name(),this._source.translatedType()),{
isEditable:!0,isMultiLine:!0})]}}}},73653:(e,t,i)=>{i.r(t),i.d(t,{ParallelChannelDefinitionsViewModel:()=>_});var n=i(11542),o=i(45126),r=i(72491),l=i(18009),s=i(53749),a=i(91335),c=i(49406);const d=new o.TranslatedString("change {title} extending left",n.t(null,void 0,i(58052))),p=new o.TranslatedString("change {title} extending right",n.t(null,void 0,i(74867))),u=new o.TranslatedString("change {title} background visibility",n.t(null,void 0,i(30839))),h=new o.TranslatedString("change {title} background color",n.t(null,void 0,i(49765))),y=[{value:"bottom",title:n.t(null,void 0,i(97118))},{value:"middle",title:n.t(null,void 0,i(91612))},{value:"top",title:n.t(null,void 0,i(27567))}],f=n.t(null,void 0,i(70320)),v=n.t(null,void 0,i(79468)),g=n.t(null,void 0,i(61856)),T=n.t(null,void 0,i(87430)),D=n.t(null,void 0,i(5124)),w=n.t(null,void 0,i(68833));class _ extends l.LineDataSourceDefinitionsViewModel{_stylePropertyDefinitions(){const e=this._source.properties().childs(),t=this._source.name(),i=(0,c.removeSpaces)(t),n=new o.TranslatedString(t,this._source.translatedType());return{definitions:[(0,s.createLineStyleDefinition)(this._propertyApplier,{lineColor:e.linecolor,lineStyle:e.linestyle,lineWidth:e.linewidth},n,"ChannelLine",{line:D}),(0,s.createLineStyleDefinition)(this._propertyApplier,{showLine:e.showMidline,lineColor:e.midlinecolor,lineStyle:e.midlinestyle,lineWidth:e.midlinewidth},n,"MiddleLine",{line:w}),(0,r.createCheckablePropertyDefinition)({checked:(0,r.convertToDefinitionProperty)(this._propertyApplier,e.extendLeft,d.format({title:n}))},{id:`${i}ExtendLeft`,title:g}),(0,r.createCheckablePropertyDefinition)({checked:(0,r.convertToDefinitionProperty)(this._propertyApplier,e.extendRight,p.format({title:n}))},{id:`${i}ExtendRight`,title:T}),(0,r.createColorPropertyDefinition)({checked:(0,r.convertToDefinitionProperty)(this._propertyApplier,e.fillBackground,u.format({title:n})),color:(0,r.getColorDefinitionProperty)(this._propertyApplier,e.backgroundColor,e.transparency,h.format({title:n}))},{id:`${i}Background`,title:v})]}}_textPropertyDefinitions(){const e=this._source.properties().childs();return{definitions:[(0,a.createTextStyleDefinition)(this._propertyApplier,{textColor:e.labelTextColor,text:e.labelText,bold:e.labelBold,italic:e.labelItalic,fontSize:e.labelFontSize,horzLabelsAlign:e.labelHorzAlign,vertLabelsAlign:e.labelVertAlign,showText:e.labelVisible},new o.TranslatedString(this._source.name(),this._source.translatedType()),{isEditable:!0,isMultiLine:!0,alignmentVerticalItems:y,customTitles:{text:f}})]}}}},39878:(e,t,i)=>{i.r(t),i.d(t,{PathDefinitionsViewModel:()=>a});var n=i(11542),o=i(45126),r=i(53749),l=i(18009);const s=n.t(null,void 0,i(3554));class a extends l.LineDataSourceDefinitionsViewModel{_stylePropertyDefinitions(){const e=this._source.properties().childs();return{definitions:[(0,r.createLineStyleDefinition)(this._propertyApplier,{lineColor:e.lineColor,lineWidth:e.lineWidth,lineStyle:e.lineStyle,leftEnd:e.leftEnd,rightEnd:e.rightEnd
},new o.TranslatedString(this._source.name(),this._source.translatedType()),"Line",{line:s})]}}}},35737:(e,t,i)=>{i.r(t),i.d(t,{PatternWithBackgroundDefinitionViewModel:()=>f});var n=i(11542),o=i(45126),r=i(53749),l=i(91335),s=i(18009),a=i(72491),c=i(49406);const d=new o.TranslatedString("change {title} background visibility",n.t(null,void 0,i(30839))),p=new o.TranslatedString("change {title} background color",n.t(null,void 0,i(49765))),u=n.t(null,void 0,i(74872)),h=n.t(null,void 0,i(38408)),y=n.t(null,void 0,i(79468));class f extends s.LineDataSourceDefinitionsViewModel{_stylePropertyDefinitions(){const e=this._source.properties().childs(),t=this._source.name(),i=new o.TranslatedString(t,this._source.translatedType());return{definitions:[(0,l.createTextStyleDefinition)(this._propertyApplier,{textColor:e.textcolor,fontSize:e.fontsize,bold:e.bold,italic:e.italic},i,{isEditable:!0,isMultiLine:!0,customTitles:{text:u}}),(0,r.createLineStyleDefinition)(this._propertyApplier,{lineColor:e.color,lineWidth:e.linewidth},i,"Line",{line:h}),(0,a.createColorPropertyDefinition)({checked:(0,a.convertToDefinitionProperty)(this._propertyApplier,e.fillBackground,d.format({title:i})),color:(0,a.getColorDefinitionProperty)(this._propertyApplier,e.backgroundColor,e.transparency,p.format({title:i}))},{id:(0,c.removeSpaces)(`${t}BackgroundColor`),title:y})]}}}},66085:(e,t,i)=>{i.r(t),i.d(t,{PatternWithoutBackgroundDefinitionsViewModel:()=>d});var n=i(11542),o=i(45126),r=i(53749),l=i(91335),s=i(18009);const a=n.t(null,void 0,i(74872)),c=n.t(null,void 0,i(38408));class d extends s.LineDataSourceDefinitionsViewModel{_stylePropertyDefinitions(){const e=this._source.properties().childs(),t=new o.TranslatedString(this._source.name(),this._source.translatedType());return{definitions:[(0,l.createTextStyleDefinition)(this._propertyApplier,{textColor:e.textcolor,fontSize:e.fontsize,bold:e.bold,italic:e.italic},t,{isEditable:!0,isMultiLine:!0,customTitles:{text:a}}),(0,r.createLineStyleDefinition)(this._propertyApplier,{lineColor:e.color,lineWidth:e.linewidth},t,"Line",{line:c})]}}}},56641:(e,t,i)=>{i.r(t),i.d(t,{PitchBaseDefinitionsViewModel:()=>b});var n=i(50151),o=i(11542),r=i(45126),l=i(53749),s=i(18009),a=i(72491),c=i(49406),d=i(95166)
;const p=new r.TranslatedString("change {title} extend lines",o.t(null,void 0,i(76295))),u=new r.TranslatedString("change {title} level {index} line visibility",o.t(null,void 0,i(51403))),h=new r.TranslatedString("change {title} level {index} line color",o.t(null,void 0,i(664))),y=new r.TranslatedString("change {title} level {index} line width",o.t(null,void 0,i(97870))),f=new r.TranslatedString("change {title} level {index} line style",o.t(null,void 0,i(64707))),v=new r.TranslatedString("change {title} level {index} line coeff",o.t(null,void 0,i(27154))),g=new r.TranslatedString("change {title} all lines color",o.t(null,void 0,i(59577))),T=new r.TranslatedString("change {title} background visibility",o.t(null,void 0,i(30839))),D=new r.TranslatedString("change {title} background transparency",o.t(null,void 0,i(13783))),w=o.t(null,{context:"study"},i(29291)),_=o.t(null,void 0,i(28683)),P=o.t(null,void 0,i(79468)),S=o.t(null,void 0,i(819));class b extends s.LineDataSourceDefinitionsViewModel{_stylePropertyDefinitions(){const e=[],t=this._source.properties(),i=t.childs(),o=this._source.name(),s=(0,c.removeSpaces)(o),b=new r.TranslatedString(o,this._source.translatedType());t.hasChild("extendLines")&&e.push((0,a.createCheckablePropertyDefinition)({checked:(0,a.convertToDefinitionProperty)(this._propertyApplier,i.extendLines,p.format({title:b}))},{id:`${s}ExtendLines`,title:S}));const m=i.median.childs(),x=(0,l.createLineStyleDefinition)(this._propertyApplier,{lineColor:m.color,lineStyle:m.linestyle,lineWidth:m.linewidth},b,"Median",{line:w});e.push(x);const L=this._source.levelsCount();for(let t=0;t<=L;t++){const n=i[`level${t}`].childs(),o=(0,a.createLeveledLinePropertyDefinition)({checked:(0,a.convertToDefinitionProperty)(this._propertyApplier,n.visible,u.format({title:b,index:t+1})),color:(0,a.getColorDefinitionProperty)(this._propertyApplier,n.color,null,h.format({title:b,index:t+1})),width:(0,a.convertToDefinitionProperty)(this._propertyApplier,n.linewidth,y.format({title:b,index:t+1})),style:(0,a.convertToDefinitionProperty)(this._propertyApplier,n.linestyle,f.format({title:b,index:t+1})),level:(0,a.convertToDefinitionProperty)(this._propertyApplier,n.coeff,v.format({title:b,index:t+1}))},{id:`${s}LineLevel${t+1}`});e.push(o)}const C=(0,a.createColorPropertyDefinition)({color:(0,a.getColorDefinitionProperty)(this._propertyApplier,new d.CollectibleColorPropertyUndoWrapper((0,n.ensureNotNull)(this._source.lineColorsProperty()),this._propertyApplier,null),null,g.format({title:b}),!0)},{id:`${s}AllLineColor`,title:_});e.push(C);const k=(0,a.createTransparencyPropertyDefinition)({checked:(0,a.convertToDefinitionProperty)(this._propertyApplier,i.fillBackground,T.format({title:b})),transparency:(0,a.convertToDefinitionProperty)(this._propertyApplier,i.transparency,D.format({title:b}))},{id:`${s}Background`,title:P});return e.push(k),{definitions:e}}}},10258:(e,t,i)=>{i.r(t),i.d(t,{PitchForkDefinitionsViewModel:()=>u});var n=i(11542),o=i(45126),r=i(72491),l=i(56641),s=i(96333),a=i(64147)
;const c=new o.TranslatedString("change {title} style",n.t(null,void 0,i(98463))),d=n.t(null,void 0,i(92516)),p=[{value:s.LineToolPitchforkStyle.Original,title:n.t(null,void 0,i(46005))},{value:s.LineToolPitchforkStyle.Schiff2,title:n.t(null,void 0,i(69904))},{value:s.LineToolPitchforkStyle.Schiff,title:n.t(null,void 0,i(70382))},{value:s.LineToolPitchforkStyle.Inside,title:n.t(null,void 0,i(91612))}];class u extends l.PitchBaseDefinitionsViewModel{_stylePropertyDefinitions(){const e=super._stylePropertyDefinitions(),t=this._source.properties().childs(),i=this._source.name(),n=new o.TranslatedString(i,this._source.translatedType()),l=(0,r.createOptionsPropertyDefinition)({option:(0,r.convertToDefinitionProperty)(this._propertyApplier,t.style,c.format({title:n}))},{id:`${i}PitchStyle`,title:d,options:new a.WatchedValue(p)});return e.definitions.push(l),e}}},16963:(e,t,i)=>{i.r(t),i.d(t,{PolylinesDefinitionsViewModel:()=>h});var n=i(11542),o=i(45126),r=i(53749),l=i(18009),s=i(72491),a=i(49406);const c=new o.TranslatedString("change {title} background visibility",n.t(null,void 0,i(30839))),d=new o.TranslatedString("change {title} background color",n.t(null,void 0,i(49765))),p=n.t(null,void 0,i(38408)),u=n.t(null,void 0,i(79468));class h extends l.LineDataSourceDefinitionsViewModel{_stylePropertyDefinitions(){const e=this._source.properties().childs(),t=this._source.name(),i=new o.TranslatedString(t,this._source.translatedType());return{definitions:[(0,r.createLineStyleDefinition)(this._propertyApplier,{lineColor:e.linecolor,lineWidth:e.linewidth},i,"Line",{line:p}),(0,s.createColorPropertyDefinition)({checked:(0,s.convertToDefinitionProperty)(this._propertyApplier,e.fillBackground,c.format({title:i})),color:(0,s.getColorDefinitionProperty)(this._propertyApplier,e.backgroundColor,e.transparency,d.format({title:i}))},{id:(0,a.removeSpaces)(`${t}BackgroundColor`),title:u})]}}}},26486:(e,t,i)=>{i.r(t),i.d(t,{PredictionDefinitionsViewModel:()=>k});var n=i(11542),o=i(45126),r=i(53749),l=i(18009),s=i(72491),a=i(49406)
;const c=new o.TranslatedString("change {title} source text color",n.t(null,void 0,i(45833))),d=new o.TranslatedString("change {title} source background color",n.t(null,void 0,i(63047))),p=new o.TranslatedString("change {title} source border color",n.t(null,void 0,i(93889))),u=new o.TranslatedString("change {title} target text color",n.t(null,void 0,i(32230))),h=new o.TranslatedString("change {title} target background color",n.t(null,void 0,i(25987))),y=new o.TranslatedString("change {title} target border color",n.t(null,void 0,i(24138))),f=new o.TranslatedString("change {title} success text color",n.t(null,void 0,i(71715))),v=new o.TranslatedString("change {title} success background color",n.t(null,void 0,i(80428))),g=new o.TranslatedString("change {title} failure text color",n.t(null,void 0,i(58406))),T=new o.TranslatedString("change {title} failure background color",n.t(null,void 0,i(91321))),D=n.t(null,void 0,i(88479)),w=n.t(null,void 0,i(14279)),_=n.t(null,void 0,i(64598)),P=n.t(null,void 0,i(59264)),S=n.t(null,void 0,i(95631)),b=n.t(null,void 0,i(79622)),m=n.t(null,void 0,i(23971)),x=n.t(null,void 0,i(90431)),L=n.t(null,void 0,i(71525)),C=n.t(null,void 0,i(28357));class k extends l.LineDataSourceDefinitionsViewModel{_stylePropertyDefinitions(){const e=this._source.properties().childs(),t=this._source.name(),i=(0,a.removeSpaces)(t),n=new o.TranslatedString(t,this._source.translatedType());return{definitions:[(0,r.createLineStyleDefinition)(this._propertyApplier,{lineColor:e.linecolor,lineWidth:e.linewidth},n,"Line"),(0,s.createColorPropertyDefinition)({color:(0,s.getColorDefinitionProperty)(this._propertyApplier,e.sourceTextColor,null,c.format({title:n}))},{id:`${i}SourceTextColor`,title:D}),(0,s.createColorPropertyDefinition)({color:(0,s.getColorDefinitionProperty)(this._propertyApplier,e.sourceBackColor,e.transparency,d.format({title:n}))},{id:`${i}SourceBackgroundColor`,title:w}),(0,s.createColorPropertyDefinition)({color:(0,s.getColorDefinitionProperty)(this._propertyApplier,e.sourceStrokeColor,null,p.format({title:n}))},{id:`${i}SourceBorderColor`,title:_}),(0,s.createColorPropertyDefinition)({color:(0,s.getColorDefinitionProperty)(this._propertyApplier,e.targetTextColor,null,u.format({title:n}))},{id:`${i}TargetTextColor`,title:P}),(0,s.createColorPropertyDefinition)({color:(0,s.getColorDefinitionProperty)(this._propertyApplier,e.targetBackColor,null,h.format({title:n}))},{id:`${i}TargetBackgroundColor`,title:S}),(0,s.createColorPropertyDefinition)({color:(0,s.getColorDefinitionProperty)(this._propertyApplier,e.targetStrokeColor,null,y.format({title:n}))},{id:`${i}TargetBorderColor`,title:b}),(0,s.createColorPropertyDefinition)({color:(0,s.getColorDefinitionProperty)(this._propertyApplier,e.successTextColor,null,f.format({title:n}))},{id:`${i}SuccessTextColor`,title:m}),(0,s.createColorPropertyDefinition)({color:(0,s.getColorDefinitionProperty)(this._propertyApplier,e.successBackground,null,v.format({title:n}))},{id:`${i}SuccessBackgroundColor`,title:x}),(0,s.createColorPropertyDefinition)({color:(0,
s.getColorDefinitionProperty)(this._propertyApplier,e.failureTextColor,null,g.format({title:n}))},{id:`${i}FailureTextColor`,title:L}),(0,s.createColorPropertyDefinition)({color:(0,s.getColorDefinitionProperty)(this._propertyApplier,e.failureBackground,null,T.format({title:n}))},{id:`${i}FailureBackgroundColor`,title:C})]}}}},89785:(e,t,i)=>{i.r(t),i.d(t,{PriceLabelDefinitionsViewModel:()=>a});var n=i(11542),o=i(45126),r=i(91335),l=i(18009);const s=n.t(null,void 0,i(70320));class a extends l.LineDataSourceDefinitionsViewModel{_stylePropertyDefinitions(){const e=this._source.properties().childs();return{definitions:[(0,r.createTextStyleDefinition)(this._propertyApplier,{textColor:e.color,fontSize:e.fontsize,backgroundColor:e.backgroundColor,backgroundTransparency:e.transparency,borderColor:e.borderColor},new o.TranslatedString(this._source.name(),this._source.translatedType()),{customTitles:{text:s}})]}}}},286:(e,t,i)=>{i.r(t),i.d(t,{PriceNoteDefinitionsViewModel:()=>f});var n=i(11542),o=i(45126),r=i(91335),l=i(18009),s=i(72491),a=i(49406);const c=new o.TranslatedString("change {title} line color",n.t(null,void 0,i(7455))),d=n.t(null,void 0,i(95170)),p=n.t(null,void 0,i(70320)),u=n.t(null,void 0,i(97575)),h=n.t(null,void 0,i(34974)),y=n.t(null,void 0,i(73863));class f extends l.LineDataSourceDefinitionsViewModel{_stylePropertyDefinitions(){const e=this._source.properties().childs(),t=this._source.name(),i=(0,a.removeSpaces)(t),n=new o.TranslatedString(t,this._source.translatedType()),l=(0,s.createColorPropertyDefinition)({color:(0,s.getColorDefinitionProperty)(this._propertyApplier,e.lineColor,null,c.format({title:n}))},{id:`${i}LineColor`,title:u});return{definitions:[(0,r.createTextStyleDefinition)(this._propertyApplier,{textColor:e.priceLabelTextColor,fontSize:e.priceLabelFontSize,bold:e.priceLabelBold,italic:e.priceLabelItalic,backgroundColor:e.priceLabelBackgroundColor,borderColor:e.priceLabelBorderColor},n,{isEditable:!1,isMultiLine:!1,customTitles:{text:d,borderTitle:h,backgroundTitle:y}}),l]}}_textPropertyDefinitions(){const e=this._source.properties().childs();return{definitions:[(0,r.createTextStyleDefinition)(this._propertyApplier,{...e,showText:e.showLabel,textColor:e.textColor,fontSize:e.fontSize},new o.TranslatedString(this._source.name(),this._source.translatedType()),{isEditable:!0,isMultiLine:!0,customTitles:{text:p}})]}}}},48306:(e,t,i)=>{i.r(t),i.d(t,{ProjectionDefinitionsViewModel:()=>h});var n=i(11542),o=i(45126),r=i(53749),l=i(18009),s=i(72491),a=i(49406);const c=new o.TranslatedString("change {title} background color 1",n.t(null,void 0,i(2788))),d=new o.TranslatedString("change {title} background color 2",n.t(null,void 0,i(47108))),p=n.t(null,void 0,i(38408)),u=n.t(null,void 0,i(79468));class h extends l.LineDataSourceDefinitionsViewModel{_stylePropertyDefinitions(){const e=this._source.properties().childs(),t=this._source.name(),i=new o.TranslatedString(t,this._source.translatedType());return{definitions:[(0,s.createTwoColorsPropertyDefinition)({color1:(0,
s.getColorDefinitionProperty)(this._propertyApplier,e.color1,e.transparency,c.format({title:i})),color2:(0,s.getColorDefinitionProperty)(this._propertyApplier,e.color2,e.transparency,d.format({title:i}))},{id:(0,a.removeSpaces)(`${t}Background2Color`),title:u}),(0,r.createLineStyleDefinition)(this._propertyApplier,{lineColor:e.trendline.childs().color,lineWidth:e.linewidth},i,"Line",{line:p})]}}}},15041:(e,t,i)=>{i.r(t),i.d(t,{RectangleDefinitionsViewModel:()=>g});var n=i(11542),o=i(45126),r=i(49406),l=i(72491),s=i(53749),a=i(90490),c=i(91335);const d=new o.TranslatedString("change {title} extending left",n.t(null,void 0,i(58052))),p=new o.TranslatedString("change {title} extending right",n.t(null,void 0,i(74867))),u=n.t(null,void 0,i(70320)),h=n.t(null,void 0,i(25112)),y=n.t(null,void 0,i(68461)),f=n.t(null,void 0,i(68286)),v=[{value:"bottom",title:n.t(null,void 0,i(97118))},{value:"middle",title:n.t(null,void 0,i(91612))},{value:"top",title:n.t(null,void 0,i(27567))}];class g extends a.GeneralFiguresDefinitionsViewModelBase{_stylePropertyDefinitions(){const e=this._source.properties().childs(),t=this._source.name(),i=new o.TranslatedString(t,this._source.translatedType()),n=super._stylePropertyDefinitions(),a=e.middleLine.childs(),c=(0,s.createLineStyleDefinition)(this._propertyApplier,{showLine:a.showLine,lineColor:a.lineColor,lineWidth:a.lineWidth,lineStyle:a.lineStyle},i,t,{line:f}),u=n.definitions.findIndex((e=>e.id===(0,r.removeSpaces)(`${t}BackgroundColor`)));u<0?n.definitions.push(c):n.definitions.splice(u,0,c);const v=(0,l.createCheckablePropertyDefinition)({checked:(0,l.convertToDefinitionProperty)(this._propertyApplier,e.extendRight,p.format({title:i}))},{id:`${t}ExtendRight`,title:y});n.definitions.push(v);const g=(0,l.createCheckablePropertyDefinition)({checked:(0,l.convertToDefinitionProperty)(this._propertyApplier,e.extendLeft,d.format({title:i}))},{id:`${t}ExtendLeft`,title:h});return n.definitions.push(g),n}_textPropertyDefinitions(){const e=this._source.properties().childs();return{definitions:[(0,c.createTextStyleDefinition)(this._propertyApplier,{textColor:e.textColor,text:e.text,bold:e.bold,italic:e.italic,fontSize:e.fontSize,horzLabelsAlign:e.horzLabelsAlign,vertLabelsAlign:e.vertLabelsAlign,showText:e.showLabel},new o.TranslatedString(this._source.name(),this._source.translatedType()),{isEditable:!0,isMultiLine:!0,alignmentVerticalItems:v,customTitles:{text:u}})]}}}},2243:(e,t,i)=>{i.r(t),i.d(t,{RiskRewardDefinitionsViewModel:()=>F});var n=i(11542),o=i(45126),r=i(53749),l=i(91335),s=i(18009),a=i(72491),c=i(76386),d=i(64147),p=i(49406)
;const u=new o.TranslatedString("change {title} stop color",n.t(null,void 0,i(26598))),h=new o.TranslatedString("change {title} target color",n.t(null,void 0,i(10504))),y=new o.TranslatedString("change {title} price labels visibility",n.t(null,void 0,i(343))),f=new o.TranslatedString("change {title} compact stats mode",n.t(null,void 0,i(49904))),v=new o.TranslatedString("change {title} always show stats",n.t(null,void 0,i(80390))),g=new o.TranslatedString("change {title} account size",n.t(null,void 0,i(99232))),T=new o.TranslatedString("change {title} lot size",n.t(null,void 0,i(54087))),D=new o.TranslatedString("change {title} risk",n.t(null,void 0,i(79875))),w=new o.TranslatedString("change {title} risk display mode",n.t(null,void 0,i(60308))),_=new o.TranslatedString("change {title} entry price",n.t(null,void 0,i(91534))),P=new o.TranslatedString("change {title} profit level",n.t(null,void 0,i(64330))),S=new o.TranslatedString("change {title} profit price",n.t(null,void 0,i(12073))),b=new o.TranslatedString("change {title} stop level",n.t(null,void 0,i(45438))),m=new o.TranslatedString("change {title} stop price",n.t(null,void 0,i(27503))),x=n.t(null,void 0,i(56982)),L=n.t(null,void 0,i(33310)),C=n.t(null,void 0,i(3560)),k=n.t(null,void 0,i(70320)),A=n.t(null,void 0,i(89735)),V=n.t(null,void 0,i(24821)),$=n.t(null,void 0,i(83840)),M=n.t(null,void 0,i(6002)),W=n.t(null,void 0,i(67852)),B=n.t(null,void 0,i(23814)),z=n.t(null,void 0,i(97668)),N=n.t(null,void 0,i(74045)),R=n.t(null,void 0,i(63886)),G=n.t(null,void 0,i(93857)),E=n.t(null,void 0,i(42747)),U=n.t(null,void 0,i(75106)),O=n.t(null,void 0,i(87145));function I(e){return[{value:c.RiskDisplayMode.Percentage,title:U},{value:c.RiskDisplayMode.Money,title:e||O}]}class F extends s.LineDataSourceDefinitionsViewModel{constructor(e,t){super(e,t);const i=this._source.properties().childs(),n=i.riskDisplayMode.value();this._riskMaxWV=new d.WatchedValue(this._getRiskMax(n)),this._riskStepWV=new d.WatchedValue(this._getRiskStep(n)),this._riskPrecisionWV=new d.WatchedValue(this._getRiskPrecision(n)),this._riskUnitWV=new d.WatchedValue(this._getRiskUnit()),this._riskUnitOptionsWV=new d.WatchedValue(this._getRiskUnitOptions()),this._lotSizeStepWV=new d.WatchedValue(this._getLotSizeStep()),this._createPropertyRages(),i.riskDisplayMode.subscribe(this,(e=>this._onRiskDisplayChanged(e))),i.accountSize.subscribe(this,(()=>this._onAccountSizeChanged())),i.lotSize.subscribe(this,(()=>this._onLotSizeChanged())),this._undoModel.model().mainSeries().dataEvents().symbolResolved().subscribe(this,this._onSymbolInfoChanged)}destroy(){super.destroy();const e=this._source.properties().childs();e.riskDisplayMode.unsubscribeAll(this),e.accountSize.unsubscribeAll(this),e.lotSize.unsubscribeAll(this),this._undoModel.model().mainSeries().dataEvents().symbolResolved().unsubscribeAll(this)}_stylePropertyDefinitions(){const e=this._source.properties().childs(),t=this._source.name(),i=(0,p.removeSpaces)(t),n=new o.TranslatedString(t,this._source.translatedType());return{definitions:[(0,
r.createLineStyleDefinition)(this._propertyApplier,{lineColor:e.linecolor,lineWidth:e.linewidth},n,"Line",{line:x}),(0,a.createColorPropertyDefinition)({color:(0,a.getColorDefinitionProperty)(this._propertyApplier,e.stopBackground,e.stopBackgroundTransparency,u.format({title:n}))},{id:`${i}StopColor`,title:L}),(0,a.createColorPropertyDefinition)({color:(0,a.getColorDefinitionProperty)(this._propertyApplier,e.profitBackground,e.profitBackgroundTransparency,h.format({title:n}))},{id:`${i}ProfitColor`,title:C}),(0,l.createTextStyleDefinition)(this._propertyApplier,{textColor:e.textcolor,fontSize:e.fontsize},n,{isEditable:!0,isMultiLine:!0,customTitles:{text:k}}),(0,a.createCheckablePropertyDefinition)({checked:(0,a.convertToDefinitionProperty)(this._propertyApplier,e.showPriceLabels,y.format({title:n}))},{id:`${i}ShowPriceLabels`,title:E}),(0,a.createCheckablePropertyDefinition)({checked:(0,a.convertToDefinitionProperty)(this._propertyApplier,e.compact,f.format({title:n}))},{id:`${i}CompactMode`,title:A}),(0,a.createCheckablePropertyDefinition)({checked:(0,a.convertToDefinitionProperty)(this._propertyApplier,e.alwaysShowStats,v.format({title:n}))},{id:`${i}AlwaysShowStats`,title:G})]}}_inputsPropertyDefinitions(){const e=this._source.properties().childs(),t=this._source.name(),i=(0,p.removeSpaces)(t),n=new o.TranslatedString(t,this._source.translatedType()),r=this._getYCoordinateStepWV(),l=(0,a.createNumberPropertyDefinition)({value:(0,a.convertToDefinitionProperty)(this._propertyApplier,e.accountSize,g.format({title:n}))},{id:`${i}AccountSize`,title:z,type:1,min:new d.WatchedValue(1e-9),max:new d.WatchedValue(1e9),step:new d.WatchedValue(1),unit:this._riskUnitWV}),s=(0,a.createNumberPropertyDefinition)({value:(0,a.convertToDefinitionProperty)(this._propertyApplier,e.lotSize,T.format({title:n}))},{id:`${i}LotSize`,title:N,type:1,min:new d.WatchedValue(1e-9),max:new d.WatchedValue(1e8),step:this._lotSizeStepWV}),c=(0,a.createNumberPropertyDefinition)({value:(0,a.convertToDefinitionProperty)(this._propertyApplier,e.risk,D.format({title:n}),[e=>parseFloat(e)]),unitOptionsValue:(0,a.convertToDefinitionProperty)(this._propertyApplier,e.riskDisplayMode,w.format({title:n}))},{id:`${i}Risk`,title:R,type:1,min:new d.WatchedValue(1e-9),max:this._riskMaxWV,precision:this._riskPrecisionWV,step:this._riskStepWV,unitOptions:this._riskUnitOptionsWV}),u=(0,a.createNumberPropertyDefinition)({value:(0,a.convertToDefinitionProperty)(this._propertyApplier,e.entryPrice,_.format({title:n}))},{id:`${i}EntryPrice`,title:M,type:1,step:r}),h=(0,a.createPropertyDefinitionsGeneralGroup)([l,s,c,u],`${i}AccountRisk`),y=(0,a.createNumberPropertyDefinition)({value:(0,a.convertToDefinitionProperty)(this._propertyApplier,e.profitLevel,P.format({title:n}))},{id:`${i}ProfitLevelTicks`,title:V,type:0,min:new d.WatchedValue(0),max:new d.WatchedValue(1e9),step:new d.WatchedValue(1)}),f=(0,a.createNumberPropertyDefinition)({value:(0,a.convertToDefinitionProperty)(this._propertyApplier,e.targetPrice,S.format({title:n
}),[e=>e,e=>this._source.prepareProfitPrice(e)])},{id:`${i}ProfitLevelPrice`,title:$,type:1,step:r}),v=(0,a.createPropertyDefinitionsGeneralGroup)([y,f],`${i}ProfitLevel`,W),x=(0,a.createNumberPropertyDefinition)({value:(0,a.convertToDefinitionProperty)(this._propertyApplier,e.stopLevel,b.format({title:n}))},{id:`${i}StopLevelTicks`,title:V,type:0,min:new d.WatchedValue(0),max:new d.WatchedValue(1e9),step:new d.WatchedValue(1)}),L=(0,a.createNumberPropertyDefinition)({value:(0,a.convertToDefinitionProperty)(this._propertyApplier,e.stopPrice,m.format({title:n}),[e=>e,e=>this._source.prepareStopPrice(e)])},{id:`${i}StopLevelPrice`,title:$,type:1,step:r});return{definitions:[h,v,(0,a.createPropertyDefinitionsGeneralGroup)([x,L],`${i}StopLevel`,B)]}}_onRiskDisplayChanged(e){const t=e.value();this._riskMaxWV.setValue(this._getRiskMax(t)),this._riskStepWV.setValue(this._getRiskStep(t)),this._riskPrecisionWV.setValue(this._getRiskPrecision(t))}_onAccountSizeChanged(){this._riskMaxWV.setValue(this._getRiskMax(this._source.properties().childs().riskDisplayMode.value()))}_onLotSizeChanged(){this._lotSizeStepWV.setValue(this._getLotSizeStep())}_onSymbolInfoChanged(){this._riskUnitWV.setValue(this._getRiskUnit()),this._riskUnitOptionsWV.setValue(this._getRiskUnitOptions())}_getRiskMax(e){return e===c.RiskDisplayMode.Percentage?100:this._source.properties().childs().accountSize.value()}_getRiskStep(e){return e===c.RiskDisplayMode.Percentage?.01:1}_getRiskPrecision(e){if(e===c.RiskDisplayMode.Percentage)return 2}_getLotSizeStep(){const e=this._source.properties().childs().lotSize.value();if(e%1==0)return 1;const t=e.toString(),i=t.split(".");if(2===i.length)return Number(`1e-${i[1].length}`);{const e=/\d+e-(\d+)/.exec(t);if(null!==e)return Number(`1e-${e[1]}`)}return this._lotSizeStepWV.value()}_getRiskUnit(){const e=this._undoModel.model().mainSeries().symbolInfo();return null!==e&&e.currency_code||""}_getRiskUnitOptions(){const e=this._undoModel.model().mainSeries().symbolInfo();return null!==e?I(e.currency_code):I()}}},91051:(e,t,i)=>{i.r(t),i.d(t,{SignpostDefinitionsViewModel:()=>g});var n=i(11542),o=i(45126),r=i(49406),l=i(64147),s=i(72491),a=i(91335),c=i(73174),d=i(18009);const p=new o.TranslatedString("change vertical position Y coordinate",n.t(null,void 0,i(69183))),u=new o.TranslatedString("change {title} emoji visibility",n.t(null,void 0,i(53274))),h=new o.TranslatedString("change {title} image background color",n.t(null,void 0,i(86993))),y=new o.TranslatedString("change {title} emoji",n.t(null,void 0,i(73247))),f=n.t(null,{context:"linetool point"},i(87476)),v=n.t(null,void 0,i(71310));class g extends d.LineDataSourceDefinitionsViewModel{_textPropertyDefinitions(){const e=this._source.properties().childs();return{definitions:[(0,a.createTextStyleDefinition)(this._propertyApplier,{text:e.text,fontSize:e.fontSize,bold:e.bold,italic:e.italic},new o.TranslatedString(this._source.name(),this._source.translatedType()),{isEditable:!0,isMultiLine:!0})]}}_coordinatesPropertyDefinitions(){
const e=this._source.pointsProperty().childs().points[0].childs(),t=this._source.name(),i=(0,c.getCoordinateXMetaInfo)(this._propertyApplier,e),n={property:(0,s.convertToDefinitionProperty)(this._propertyApplier,e.price,p),info:{typeY:1,stepY:new l.WatchedValue(1),minY:new l.WatchedValue(-100),maxY:new l.WatchedValue(100)}};return{definitions:[(0,s.createCoordinatesPropertyDefinition)({x:i.property,y:n.property},{id:(0,r.removeSpaces)(`${t}Coordinates${f}`),title:f,...i.info,...n.info})]}}_stylePropertyDefinitions(){const e=this._source.properties().childs(),t=this._source.name(),i=new o.TranslatedString(t,this._source.translatedType());return{definitions:[(0,s.createEmojiPropertyDefinition)({checked:(0,s.convertToDefinitionProperty)(this._propertyApplier,e.showImage,u.format({title:i})),backgroundColor:(0,s.getColorDefinitionProperty)(this._propertyApplier,e.plateColor,null,h.format({title:i})),emoji:(0,s.convertToDefinitionProperty)(this._propertyApplier,e.emoji,y.format({title:i}))},{id:(0,r.removeSpaces)(`${t}Emoji${v}`),title:v})]}}}},27975:(e,t,i)=>{i.r(t),i.d(t,{TextDefinitionsViewModel:()=>l});var n=i(91335),o=i(18009),r=i(45126);class l extends o.LineDataSourceDefinitionsViewModel{_textPropertyDefinitions(){const e=this._source.properties().childs();return{definitions:[(0,n.createTextStyleDefinition)(this._propertyApplier,{textColor:e.color,fontSize:e.fontsize,bold:e.bold,italic:e.italic,text:e.text,backgroundVisible:e.fillBackground,backgroundColor:e.backgroundColor,backgroundTransparency:e.backgroundTransparency,borderVisible:e.drawBorder,borderColor:e.borderColor,wrap:e.wordWrap},new r.TranslatedString(this._source.name(),this._source.translatedType()),{isEditable:!0,isMultiLine:!0})]}}}},84437:(e,t,i)=>{i.r(t),i.d(t,{TimeCyclesPatternDefinitionsViewModel:()=>h});var n=i(11542),o=i(45126),r=i(53749),l=i(18009),s=i(72491),a=i(49406);const c=new o.TranslatedString("change {title} background visibility",n.t(null,void 0,i(30839))),d=new o.TranslatedString("change {title} background color",n.t(null,void 0,i(49765))),p=n.t(null,void 0,i(3554)),u=n.t(null,void 0,i(79468));class h extends l.LineDataSourceDefinitionsViewModel{_stylePropertyDefinitions(){const e=this._source.properties().childs(),t=this._source.name(),i=new o.TranslatedString(t,this._source.translatedType());return{definitions:[(0,r.createLineStyleDefinition)(this._propertyApplier,{lineColor:e.linecolor,lineWidth:e.linewidth,lineStyle:e.linestyle},i,"Line",{line:p}),(0,s.createColorPropertyDefinition)({checked:(0,s.convertToDefinitionProperty)(this._propertyApplier,e.fillBackground,c.format({title:i})),color:(0,s.getColorDefinitionProperty)(this._propertyApplier,e.backgroundColor,e.transparency,d.format({title:i}))},{id:(0,a.removeSpaces)(`${t}BackgroundColor`),title:u})]}}}},30574:(e,t,i)=>{i.r(t),i.d(t,{TrendAngleDefinitionsViewModel:()=>y});var n=i(11542),o=i(45126),r=i(72491),l=i(18009),s=i(64147),a=i(73174),c=i(34412)
;const d=new o.TranslatedString("change angle",n.t(null,void 0,i(3894))),p=n.t(null,void 0,i(390)),u=n.t(null,void 0,i(70320)),h=n.t(null,{context:"linetool point"},i(6737));class y extends l.LineDataSourceDefinitionsViewModel{_coordinatesPropertyDefinitions(){const e=this._source.points(),t=[],i=this._source.pointsProperty().childs().points[0].childs(),n=this._getYCoordinateStepWV();t.push((0,a.getCoordinatesPropertiesDefinitions)(this._propertyApplier,i,e[0],n,h,this._source.name()));const o=(0,r.createNumberPropertyDefinition)({value:(0,r.convertToDefinitionProperty)(this._propertyApplier,this._source.properties().childs().angle,d)},{id:"TrendLineAngleCoordinate",title:p,min:new s.WatchedValue(-360),max:new s.WatchedValue(360),step:new s.WatchedValue(1)});return t.push(o),{definitions:t}}_stylePropertyDefinitions(){const e=this._source.properties().childs();return(0,c.getTrendLineToolsStylePropertiesDefinitions)(this._propertyApplier,e,new o.TranslatedString(this._source.name(),this._source.translatedType()),{text:u})}}},46662:(e,t,i)=>{i.r(t),i.d(t,{TrendBasedFibTimeDefinitionsViewModel:()=>L});var n=i(50151),o=i(11542),r=i(45126),l=i(53749),s=i(72491),a=i(18009),c=i(23720),d=i(64147),p=i(49406),u=i(95166);const h=new r.TranslatedString("change {title} level {index} line visibility",o.t(null,void 0,i(51403))),y=new r.TranslatedString("change {title} level {index} line color",o.t(null,void 0,i(664))),f=new r.TranslatedString("change {title} level {index} line width",o.t(null,void 0,i(97870))),v=new r.TranslatedString("change {title} level {index} line style",o.t(null,void 0,i(64707))),g=new r.TranslatedString("change {title} level {index} line coeff",o.t(null,void 0,i(27154))),T=new r.TranslatedString("change {title} all lines color",o.t(null,void 0,i(59577))),D=new r.TranslatedString("change {title} background visibility",o.t(null,void 0,i(30839))),w=new r.TranslatedString("change {title} background transparency",o.t(null,void 0,i(13783))),_=new r.TranslatedString("change {title} labels visibility",o.t(null,void 0,i(93340))),P=new r.TranslatedString("change {title} labels alignment",o.t(null,void 0,i(58312))),S=o.t(null,void 0,i(51574)),b=o.t(null,void 0,i(28683)),m=o.t(null,void 0,i(79468)),x=o.t(null,void 0,i(5119));class L extends a.LineDataSourceDefinitionsViewModel{_stylePropertyDefinitions(){const e=[],t=this._source.properties(),i=t.childs(),o=this._source.name(),a=(0,p.removeSpaces)(o),L=new r.TranslatedString(o,this._source.translatedType()),C=i.trendline.childs(),k=(0,l.createLineStyleDefinition)(this._propertyApplier,{showLine:C.visible,lineColor:C.color,lineStyle:C.linestyle,lineWidth:C.linewidth},L,"TrendLine",{line:S});e.push(k);const A=this._source.levelsCount();for(let i=1;i<=A;i++){const o=(0,n.ensureDefined)(t.child(`level${i}`)).childs(),r=(0,s.createLeveledLinePropertyDefinition)({checked:(0,s.convertToDefinitionProperty)(this._propertyApplier,o.visible,h.format({title:L,index:i})),color:(0,s.getColorDefinitionProperty)(this._propertyApplier,o.color,null,y.format({title:L,index:i})),width:(0,
s.convertToDefinitionProperty)(this._propertyApplier,o.linewidth,f.format({title:L,index:i})),style:(0,s.convertToDefinitionProperty)(this._propertyApplier,o.linestyle,v.format({title:L,index:i})),level:(0,s.convertToDefinitionProperty)(this._propertyApplier,o.coeff,g.format({title:L,index:i}))},{id:`${a}LineLevel${i}`});e.push(r)}const V=(0,s.createColorPropertyDefinition)({color:(0,s.getColorDefinitionProperty)(this._propertyApplier,new u.CollectibleColorPropertyUndoWrapper((0,n.ensureNotNull)(this._source.lineColorsProperty()),this._propertyApplier,null),null,T.format({title:L}),!0)},{id:`${a}AllLineColor`,title:b});e.push(V);const $=(0,s.createTransparencyPropertyDefinition)({checked:(0,s.convertToDefinitionProperty)(this._propertyApplier,i.fillBackground,D.format({title:L})),transparency:(0,s.convertToDefinitionProperty)(this._propertyApplier,i.transparency,w.format({title:L}))},{id:`${a}Background`,title:m});e.push($);const M=(0,s.createTwoOptionsPropertyDefinition)({checked:(0,s.convertToDefinitionProperty)(this._propertyApplier,i.showCoeffs,_.format({title:L})),option1:(0,s.convertToDefinitionProperty)(this._propertyApplier,i.horzLabelsAlign,P.format({title:L})),option2:(0,s.convertToDefinitionProperty)(this._propertyApplier,i.vertLabelsAlign,P.format({title:L}))},{id:`${a}Labels`,title:x,optionsItems1:new d.WatchedValue(c.availableAlignmentHorizontalItems),optionsItems2:new d.WatchedValue(c.availableAlignmentVerticalItems)});return e.push(M),{definitions:e}}}},26360:(e,t,i)=>{i.r(t),i.d(t,{TrendLineDefinitionsViewModel:()=>c});var n=i(11542),o=i(45126),r=i(18009),l=i(34412),s=i(91335);const a=n.t(null,void 0,i(70320));class c extends r.LineDataSourceDefinitionsViewModel{_stylePropertyDefinitions(){const e=this._source.properties().childs();return(0,l.getTrendLineToolsStylePropertiesDefinitions)(this._propertyApplier,e,new o.TranslatedString(this._source.name(),this._source.translatedType()))}_textPropertyDefinitions(){const e=this._source.properties().childs();return{definitions:[(0,s.createTextStyleDefinition)(this._propertyApplier,{...e,showText:e.showLabel,textColor:e.textcolor,fontSize:e.fontsize},new o.TranslatedString(this._source.name(),this._source.translatedType()),{isEditable:!0,isMultiLine:!0,customTitles:{text:a}})]}}}},34353:(e,t,i)=>{i.r(t),i.d(t,{VerticalLineDefinitionsViewModel:()=>h});var n=i(11542),o=i(45126),r=i(72491),l=i(73174),s=i(88924),a=i(49406),c=i(18009),d=i(91335);const p=n.t(null,void 0,i(70320)),u=n.t(null,{context:"linetool point"},i(26381));class h extends c.LineDataSourceDefinitionsViewModel{_stylePropertyDefinitions(){const e=this._source.properties().childs();return(0,s.getLinesStylesPropertiesDefinitions)(this._propertyApplier,e,new o.TranslatedString(this._source.name(),this._source.translatedType()))}_coordinatesPropertyDefinitions(){const e=this._source.pointsProperty().childs().points[0].childs(),t=(0,l.getCoordinateXMetaInfo)(this._propertyApplier,e);return{definitions:[(0,r.createCoordinatesPropertyDefinition)({x:t.property},{id:(0,
a.removeSpaces)(`${this._source.name()}Point1`),title:u,...t.info})]}}_textPropertyDefinitions(){const e=this._source.properties().childs();return{definitions:[(0,d.createTextStyleDefinition)(this._propertyApplier,{...e,showText:e.showLabel,textColor:e.textcolor,fontSize:e.fontsize,textOrientation:e.textOrientation},new o.TranslatedString(this._source.name(),this._source.translatedType()),{isEditable:!0,isMultiLine:!0,customTitles:{text:p}})]}}}},27873:(e,t,i)=>{i.r(t),i.d(t,{AnchoredVWAPDefinitionsViewModel:()=>O});var n=i(11542),o=i(45126),r=(i(21251),i(72491)),l=i(31507),s=i(49406),a=i(1183);const c=new o.TranslatedString("change {title} VWAP line color",n.t(null,void 0,i(83030))),d=new o.TranslatedString("change {title} VWAP line width",n.t(null,void 0,i(47479))),p=new o.TranslatedString("change {title} lower band #1 line visibility",n.t(null,void 0,i(87716))),u=new o.TranslatedString("change {title} lower band #1 line color",n.t(null,void 0,i(67450))),h=new o.TranslatedString("change {title} lower band #1 line width",n.t(null,void 0,i(43181))),y=new o.TranslatedString("change {title} upper band #1 line visibility",n.t(null,void 0,i(81363))),f=new o.TranslatedString("change {title} upper band #1 line color",n.t(null,void 0,i(27326))),v=new o.TranslatedString("change {title} upper band #1 line width",n.t(null,void 0,i(1353))),g=new o.TranslatedString("change {title} lower band #2 line visibility",n.t(null,void 0,i(94308))),T=new o.TranslatedString("change {title} lower band #2 line color",n.t(null,void 0,i(35092))),D=new o.TranslatedString("change {title} lower band #2 line width",n.t(null,void 0,i(8873))),w=new o.TranslatedString("change {title} upper band #2 line visibility",n.t(null,void 0,i(93075))),_=new o.TranslatedString("change {title} upper band #2 line color",n.t(null,void 0,i(42905))),P=new o.TranslatedString("change {title} upper band #2 line width",n.t(null,void 0,i(7943))),S=new o.TranslatedString("change {title} lower band #3 line visibility",n.t(null,void 0,i(32442))),b=new o.TranslatedString("change {title} lower band #3 line color",n.t(null,void 0,i(68180))),m=new o.TranslatedString("change {title} lower band #3 line width",n.t(null,void 0,i(3476))),x=new o.TranslatedString("change {title} upper band #3 line visibility",n.t(null,void 0,i(15698))),L=new o.TranslatedString("change {title} upper band #3 line color",n.t(null,void 0,i(51780))),C=new o.TranslatedString("change {title} upper band #3 line width",n.t(null,void 0,i(27414))),k=new o.TranslatedString("change {title} background visibility",n.t(null,void 0,i(30839))),A=new o.TranslatedString("change {title} background color",n.t(null,void 0,i(49765))),V=new o.TranslatedString("change {title} price visibility",n.t(null,void 0,i(727))),$=n.t(null,void 0,i(5682)),M=n.t(null,void 0,i(71571)),W=n.t(null,void 0,i(21315)),B=n.t(null,void 0,i(91889)),z=n.t(null,void 0,i(65448)),N=n.t(null,void 0,i(83301)),R=n.t(null,void 0,i(64848)),G=n.t(null,void 0,i(58375)),E=n.t(null,void 0,i(97273));function U(e,t,i,n,o,l,s,c,d){return(0,
r.createLinePropertyDefinition)({checked:(0,r.convertToDefinitionProperty)(e,new a.StudyPlotVisibleProperty(t.display),i.format({title:d})),color:(0,r.getColorDefinitionProperty)(e,t.color,t.transparency,n.format({title:d})),width:(0,r.convertToDefinitionProperty)(e,t.linewidth,o.format({title:d}))},{id:`${c}${s}`,title:l})}class O extends l.StudyLineDataSourceDefinitionsViewModel{constructor(e,t){super(e,t)}_stylePropertyDefinitions(){var e,t,i,n,l,a;const O=this._source.properties().childs(),I=this._source.name(),F=(0,s.removeSpaces)(I),H=new o.TranslatedString(I,this._source.translatedType()),j=O.styles.childs().VWAP.childs(),Y=[(0,r.createLinePropertyDefinition)({color:(0,r.getColorDefinitionProperty)(this._propertyApplier,j.color,j.transparency,c.format({title:H})),width:(0,r.convertToDefinitionProperty)(this._propertyApplier,j.linewidth,d.format({title:H}))},{id:`${F}VWAPLine`,title:$})],X=this._source.metaInfo();if((null===(e=X.styles)||void 0===e?void 0:e.UpperBand)&&(null===(t=X.styles)||void 0===t?void 0:t.LowerBand)){const e=O.styles.childs().LowerBand.childs(),t=U(this._propertyApplier,e,p,u,h,M,"LowerBandLine",F,H),i=O.styles.childs().UpperBand.childs(),n=U(this._propertyApplier,i,y,f,v,W,"UpperBandLine",F,H);Y.push(t,n)}if(null==X?void 0:X.area){const e=O.areaBackground.childs(),t=(0,r.createLinePropertyDefinition)({checked:(0,r.convertToDefinitionProperty)(this._propertyApplier,e.fillBackground,k.format({title:H})),color:(0,r.getColorDefinitionProperty)(this._propertyApplier,e.backgroundColor,e.transparency,A.format({title:H}))},{id:`${F}Background`,title:G});Y.push(t)}if((null===(i=X.styles)||void 0===i?void 0:i.UpperBand_2)&&(null===(n=X.styles)||void 0===n?void 0:n.LowerBand_2)&&(null===(l=X.styles)||void 0===l?void 0:l.UpperBand_3)&&(null===(a=X.styles)||void 0===a?void 0:a.LowerBand_3)){const e=O.styles.childs().LowerBand_2.childs(),t=U(this._propertyApplier,e,g,T,D,B,"LowerBand2Line",F,H),i=O.styles.childs().UpperBand_2.childs(),n=U(this._propertyApplier,i,w,_,P,z,"UpperBand2Line",F,H),o=O.styles.childs().LowerBand_3.childs(),r=U(this._propertyApplier,o,S,b,m,N,"LowerBand3Line",F,H),l=O.styles.childs().UpperBand_3.childs(),s=U(this._propertyApplier,l,x,L,C,R,"UpperBand3Line",F,H);Y.push(t,n,r,s)}const q=(0,r.createCheckablePropertyDefinition)({checked:(0,r.convertToDefinitionProperty)(this._propertyApplier,O.axisLabelVisible,V.format({title:H}))},{id:`${F}ShowPrice`,title:E});return Y.push(q),{definitions:Y}}_coordinatesPropertyDefinitions(){return null}}},95166:(e,t,i)=>{i.d(t,{CollectibleColorPropertyDirectWrapper:()=>s,CollectibleColorPropertyUndoWrapper:()=>l});var n=i(50151),o=i(12988);class r extends o.Property{constructor(e){super(),this._listenersMappers=[],this._isProcess=!1,this._baseProperty=e}destroy(){this._baseProperty.destroy(),super.destroy()}value(){const e=this._baseProperty.value();return"mixed"===e?"":e}visible(){return this._baseProperty.visible()}setValue(e){this._isProcess=!0,this._baseProperty.setValue(""===e?"mixed":e,void 0,{applyValue:this._applyValue.bind(this)}),
this._isProcess=!1,this._listenersMappers.forEach((e=>{e.method.call(e.obj,this,"")}))}subscribe(e,t){const i=i=>{this._isProcess||t.call(e,this,"")},n={obj:e,method:t,callback:i};this._listenersMappers.push(n),this._baseProperty.subscribe(e,i)}unsubscribe(e,t){var i;const o=(0,n.ensureDefined)(null===(i=this._listenersMappers.find((i=>i.obj===e&&i.method===t)))||void 0===i?void 0:i.callback);this._baseProperty.unsubscribe(e,o)}unsubscribeAll(e){this._baseProperty.unsubscribeAll(e)}}class l extends r{constructor(e,t,i){super(e),this._propertyApplier=t,this._undoText=i}_applyValue(e,t){this._propertyApplier.setProperty(e,t,this._undoText)}}class s extends r{_applyValue(e,t){e.setValue(t)}}}}]);