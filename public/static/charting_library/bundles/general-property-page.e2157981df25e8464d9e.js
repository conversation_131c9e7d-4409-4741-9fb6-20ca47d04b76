(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[3596],{61857:(e,t,i)=>{"use strict";i.r(t),i.d(t,{ChartPropertyDefinitionsViewModel:()=>hi});var n=i(50151),o=i(11542),r=i(56570),l=(i(91140),i(79320),i(64147)),a=i(72491),s=i(57717),c=i(60859),d=i(51613),h=i(34391),u=i(64482),p=i(30141),v=i(45126),g=i(68805),y=i(23486),P=i(99531),f=i(60074),b=i(88960);const w=new v.TranslatedString("change symbol description visibility",o.t(null,void 0,i(88167))),m=new v.TranslatedString("change symbol legend format",o.t(null,void 0,i(902))),S=new v.TranslatedString("change open market status visibility",o.t(null,void 0,i(96227))),D=new v.TranslatedString("change chart values visibility",o.t(null,void 0,i(79637))),T=new v.TranslatedString("change last day change visibility",o.t(null,void 0,i(66307))),_=new v.TranslatedString("change bar change visibility",o.t(null,void 0,i(27426))),C=new v.TranslatedString("change indicator arguments visibility",o.t(null,void 0,i(78310))),V=new v.TranslatedString("change indicator titles visibility",o.t(null,void 0,i(63050))),L=new v.TranslatedString("change indicator values visibility",o.t(null,void 0,i(49583))),k=new v.TranslatedString("change legend background visibility",o.t(null,void 0,i(14246))),W=new v.TranslatedString("change legend background transparency",o.t(null,void 0,i(91873))),x=new v.TranslatedString("change volume values visibility",o.t(null,void 0,i(96201))),M=new v.TranslatedString("change symbol field visibility",o.t(null,void 0,i(12050))),O=o.t(null,void 0,i(14876)),G=o.t(null,void 0,i(70198)),R=o.t(null,void 0,i(45639)),A=o.t(null,void 0,i(72423)),F=o.t(null,void 0,i(10842)),H=o.t(null,void 0,i(37644)),Z=o.t(null,void 0,i(29854)),B=o.t(null,void 0,i(14119)),N=o.t(null,void 0,i(60092)),E=o.t(null,void 0,i(79468)),j=o.t(null,void 0,i(25765)),z=o.t(null,void 0,i(84684)),U=r.enabled("symbol_info_price_source"),I=r.enabled("show_symbol_logos")&&r.enabled("show_symbol_logo_in_legend");var q=i(94164),J=i(52305),K=i(19475),Q=i(50788),X=i(92158);const Y=new v.TranslatedString("change symbol labels visibility",o.t(null,void 0,i(73357))),$=new v.TranslatedString("change symbol last value visibility",o.t(null,void 0,i(67453))),ee=new v.TranslatedString("change symbol last value mode",o.t(null,void 0,i(46066))),te=(new v.TranslatedString("change bid and ask labels visibility",o.t(null,void 0,i(69362))),new v.TranslatedString("change bid and ask lines visibility",o.t(null,void 0,i(52919))),new v.TranslatedString("change bid line color",o.t(null,void 0,i(17919))),new v.TranslatedString("change ask line color",o.t(null,void 0,i(98407))),new v.TranslatedString("change pre/post market price label visibility",o.t(null,void 0,i(30870))),new v.TranslatedString("change pre/post market price lines visibility",o.t(null,void 0,i(91978))),new v.TranslatedString("change pre market line color",o.t(null,void 0,i(96114))),new v.TranslatedString("change post market line color",o.t(null,void 0,i(28075))),
new v.TranslatedString("change high and low price labels visibility",o.t(null,void 0,i(24226)))),ie=new v.TranslatedString("change high and low price lines visibility",o.t(null,void 0,i(80692))),ne=new v.TranslatedString("change high and low price line color",o.t(null,void 0,i(61407))),oe=new v.TranslatedString("change high and low price line width",o.t(null,void 0,i(39581))),re=(new v.TranslatedString("change indicators and financials name labels visibility",o.t(null,void 0,i(12411))),new v.TranslatedString("change indicators name labels visibility",o.t(null,void 0,i(24893)))),le=(new v.TranslatedString("change indicators and financials value labels visibility",o.t(null,void 0,i(71161))),new v.TranslatedString("change indicators value labels visibility",o.t(null,void 0,i(64729)))),ae=new v.TranslatedString("change no overlapping labels",o.t(null,void 0,i(61557))),se=new v.TranslatedString("change countdown to bar close visibility",o.t(null,void 0,i(39383))),ce=new v.TranslatedString("change currency label visibility",o.t(null,void 0,i(64003))),de=new v.TranslatedString("change scale modes buttons visibility",o.t(null,void 0,i(69023))),he=new v.TranslatedString("change unit label visibility",o.t(null,void 0,i(51250))),ue=new v.TranslatedString("change currency and unit labels visibility",o.t(null,void 0,i(63119))),pe=new v.TranslatedString("change plus button visibility",o.t(null,void 0,i(96379))),ve=new v.TranslatedString("toggle lock scale",o.t(null,void 0,i(49695))),ge=new v.TranslatedString("change price to bar ratio",o.t(null,void 0,i(2509))),ye=new v.TranslatedString("change date format",o.t(null,void 0,i(43109))),Pe=new v.TranslatedString("change time hours format",o.t(null,void 0,i(39754))),fe=(new v.TranslatedString("change day of week on labels",o.t(null,void 0,i(30418))),new v.TranslatedString("change price line visibility",o.t(null,void 0,i(8662)))),be=new v.TranslatedString("change price line color",o.t(null,void 0,i(87861))),we=new v.TranslatedString("change price line width",o.t(null,void 0,i(29353))),me=new v.TranslatedString("change average close price label visibility",o.t(null,void 0,i(76852))),Se=new v.TranslatedString("change average close price line visibility",o.t(null,void 0,i(1022))),De=new v.TranslatedString("change average close price line color",o.t(null,void 0,i(47026))),Te=new v.TranslatedString("change average close price line width",o.t(null,void 0,i(43231))),_e=(new v.TranslatedString("change previous close price line visibility",o.t(null,void 0,i(58419))),new v.TranslatedString("change previous close price line color",o.t(null,void 0,i(69814))),new v.TranslatedString("change previous close price line width",o.t(null,void 0,i(13660))),new v.TranslatedString("change symbol previous close value visibility",o.t(null,void 0,i(4729))),o.t(null,void 0,i(99709))),Ce=o.t(null,void 0,i(51514)),Ve=o.t(null,void 0,i(3554)),Le=o.t(null,void 0,i(58589)),ke=o.t(null,void 0,i(62142)),We=o.t(null,void 0,i(95481)),xe=o.t(null,void 0,i(68650)),Me=(o.t(null,void 0,i(83811)),
o.t(null,void 0,i(78082))),Oe=(o.t(null,void 0,i(60904)),o.t(null,void 0,i(14180))),Ge=(o.t(null,void 0,i(76473)),o.t(null,void 0,i(83140))),Re=o.t(null,void 0,i(81849)),Ae=o.t(null,void 0,i(26204)),Fe=o.t(null,void 0,i(33564)),He=o.t(null,void 0,i(3015)),Ze=o.t(null,void 0,i(71566)),Be=o.t(null,void 0,i(30042)),Ne=o.t(null,void 0,i(14017)),Ee=o.t(null,void 0,i(35082)),je=o.t(null,void 0,i(84838)),ze=o.t(null,void 0,i(5591)),Ue=o.t(null,void 0,i(93965)),Ie=(o.t(null,void 0,i(42357)),r.enabled("show_average_close_price_line_and_label")),qe=[{value:q.PriceAxisLastValueMode.LastPriceAndPercentageValue,title:o.t(null,void 0,i(27632))},{value:q.PriceAxisLastValueMode.LastValueAccordingToScale,title:o.t(null,void 0,i(31218))}];var Je=i(39158),Ke=i(23351),Qe=i(59411);const Xe=new v.TranslatedString("change sessions breaks visibility",o.t(null,void 0,i(60067))),Ye=new v.TranslatedString("change sessions breaks color",o.t(null,void 0,i(33895))),$e=new v.TranslatedString("change sessions breaks width",o.t(null,void 0,i(28175))),et=new v.TranslatedString("change sessions breaks style",o.t(null,void 0,i(21641))),tt=o.t(null,void 0,i(66707))
;const it=new v.TranslatedString("change chart background color",o.t(null,void 0,i(42803))),nt=new v.TranslatedString("change chart background type",o.t(null,void 0,i(41382))),ot=new v.TranslatedString("change vert grid lines color",o.t(null,void 0,i(71805))),rt=new v.TranslatedString("change horz grid lines color",o.t(null,void 0,i(21133))),lt=new v.TranslatedString("change grid lines visibility",o.t(null,void 0,i(73844))),at=new v.TranslatedString("change scales text color",o.t(null,void 0,i(76131))),st=new v.TranslatedString("change scales font size",o.t(null,void 0,i(27792))),ct=new v.TranslatedString("change scales lines color",o.t(null,void 0,i(94997))),dt=new v.TranslatedString("change pane separators color",o.t(null,void 0,i(52203))),ht=new v.TranslatedString("change crosshair color",o.t(null,void 0,i(92885))),ut=new v.TranslatedString("change crosshair width",o.t(null,void 0,i(50544))),pt=new v.TranslatedString("change crosshair style",o.t(null,void 0,i(68418))),vt=new v.TranslatedString("change symbol watermark visibility",o.t(null,void 0,i(73227))),gt=new v.TranslatedString("change symbol watermark color",o.t(null,void 0,i(78995))),yt=new v.TranslatedString("change navigation buttons visibility",o.t(null,void 0,i(3311))),Pt=new v.TranslatedString("change pane buttons visibility",o.t(null,void 0,i(18378))),ft=new v.TranslatedString("change top margin",o.t(null,void 0,i(74883))),bt=new v.TranslatedString("change bottom margin",o.t(null,void 0,i(32094))),wt=new v.TranslatedString("change right margin",o.t(null,void 0,i(82946))),mt=new v.TranslatedString("change right margin percentage",o.t(null,void 0,i(79545))),St=o.t(null,void 0,i(79468)),Dt=o.t(null,void 0,i(83594)),Tt=o.t(null,void 0,i(8402)),_t=o.t(null,void 0,i(61900)),Ct=o.t(null,void 0,i(60798)),Vt=o.t(null,void 0,i(68662)),Lt=o.t(null,void 0,i(70320)),kt=o.t(null,void 0,i(56982)),Wt=o.t(null,void 0,i(82894)),xt=o.t(null,void 0,i(74622)),Mt=o.t(null,void 0,i(41571)),Ot=o.t(null,void 0,i(51019)),Gt=o.t(null,void 0,i(53263)),Rt=o.t(null,void 0,i(97118)),At=o.t(null,void 0,i(27567)),Ft=o.t(null,void 0,i(50421)),Ht=o.t(null,void 0,i(16207)),Zt=o.t(null,void 0,i(27377)),Bt=o.t(null,{context:"unit"},i(80587));async function Nt(e,t,n,s,c,d,h,u,p,v){const g=[],y=[],f=[],b=[],w=[],m=(0,a.createColorPropertyDefinition)({color:(0,a.getColorDefinitionProperty)(e,t.background,null,it),gradientColor1:(0,a.getColorDefinitionProperty)(e,t.backgroundGradientStartColor,null,it),gradientColor2:(0,a.getColorDefinitionProperty)(e,t.backgroundGradientEndColor,null,it),type:(0,a.convertToDefinitionProperty)(e,t.backgroundType,nt)},{id:"chartBackground",title:St,noAlpha:!0}),S=t.vertGridProperties.childs(),D=t.horzGridProperties.childs(),T=(0,a.createOptionalTwoColorsPropertyDefinition)({option:(0,a.convertToDefinitionProperty)(e,t.gridLinesMode,lt),color1:(0,a.getColorDefinitionProperty)(e,S.color,null,ot),color2:(0,a.getColorDefinitionProperty)(e,D.color,null,rt)},{id:"gridLines",title:Vt,options:new l.WatchedValue([{title:Dt,value:"both"},{title:Tt,value:"vert"},{
title:_t,value:"horz"},{title:Ct,value:"none"}]),color1Visible:v.vertLinesVisible,color2Visible:v.horzLinesVisible}),_=(0,P.createWVFromGetterAndSubscription)((()=>1!==e.model().panes().length),e.model().panesCollectionChanged()),C=(0,a.createLinePropertyDefinition)({visible:(0,a.convertFromReadonlyWVToDefinitionProperty)(_.ownership()),color:(0,a.getColorDefinitionProperty)(e,t.separatorColor,null,dt)},{id:"paneSeparators",title:Wt}),V=t.crossHairProperties.childs(),L=(0,a.createLinePropertyDefinition)({color:(0,a.getColorDefinitionProperty)(e,V.color,V.transparency,ht),width:(0,a.convertToDefinitionProperty)(e,V.width,ut),style:(0,a.convertToDefinitionProperty)(e,V.style,pt)},{id:"crossHair",title:xt});g.push(m,T);{const t=await async function(e){const t=(await e.model().sessions().promise()).properties().childs().graphics.childs().vertlines.childs().sessBreaks.childs(),i=(0,Je.combineProperty)((e=>!e),e.mainSeries().isDWMProperty().weakReference());return(0,Qe.createLinePropertyDefinition)({visible:(0,Ke.makeProxyDefinitionProperty)(i.ownership()),checked:(0,a.convertToDefinitionProperty)(e,t.visible,Xe),color:(0,a.getColorDefinitionProperty)(e,t.color,null,Ye),width:(0,a.convertToDefinitionProperty)(e,t.width,$e),style:(0,a.convertToDefinitionProperty)(e,t.style,et)},{id:"sessionBeaks",title:tt})}(e);g.push(t)}if(g.push(C,L),null!==n){const t=(0,a.createColorPropertyDefinition)({checked:(0,a.convertToDefinitionProperty)(e,n.visibility,vt),color:(0,a.getColorDefinitionProperty)(e,n.color,null,gt)},{id:"watermark",title:Mt});g.push(t)}const k=(0,a.createTextPropertyDefinition)({color:(0,a.getColorDefinitionProperty)(e,s.textColor,null,at),size:(0,a.convertToDefinitionProperty)(e,s.fontSize,st)},{id:"scalesText",title:Lt}),W=(0,a.createLinePropertyDefinition)({color:(0,a.getColorDefinitionProperty)(e,s.lineColor,null,ct)},{id:"scalesLine",title:kt});y.push(k,W);const x=(0,a.createOptionsPropertyDefinition)({option:(0,a.convertToDefinitionProperty)(e,d.property,yt)},{id:"navButtons",title:Ot,options:new l.WatchedValue(d.values)}),M=(0,a.createOptionsPropertyDefinition)({option:(0,a.convertToDefinitionProperty)(e,h.property,Pt)},{id:"paneButtons",title:Gt,options:new l.WatchedValue(h.values)});f.push(x,M);const O=(0,a.createNumberPropertyDefinition)({value:(0,a.convertToDefinitionProperty)(e,t.topMargin,ft,[J.floor])},{type:0,id:"paneTopMargin",title:Rt,min:new l.WatchedValue(0),max:new l.WatchedValue(25),step:new l.WatchedValue(1),unit:new l.WatchedValue("%")}),G=(0,a.createNumberPropertyDefinition)({value:(0,a.convertToDefinitionProperty)(e,t.bottomMargin,bt,[J.floor])},{type:0,id:"paneBottomMargin",title:At,min:new l.WatchedValue(0),max:new l.WatchedValue(25),step:new l.WatchedValue(1),unit:new l.WatchedValue("%")});if(b.push(O,G),r.enabled("chart_property_page_right_margin_editor")){const t={value:(0,a.convertFromWVToDefinitionProperty)(e,c.value,wt,[J.floor])},i={type:0,id:"paneRightMargin",title:Ft,min:c.min,max:c.max,step:new l.WatchedValue(1),unit:new l.WatchedValue(Bt)}
;if(r.enabled("show_percent_option_for_right_margin")){const n=(0,a.createNumberPropertyDefinition)({...t,checked:(0,a.convertFromWVToDefinitionProperty)(e,p,mt,[e=>!e,e=>!e])},{...i,title:Zt}),o=(0,a.createNumberPropertyDefinition)({checked:(0,a.convertFromWVToDefinitionProperty)(e,p,mt),value:(0,a.convertFromWVToDefinitionProperty)(e,u,mt,[J.floor])},{type:0,id:"paneRightMarginPercentage",title:Ht,min:new l.WatchedValue(0),max:new l.WatchedValue(99),step:new l.WatchedValue(1),unit:new l.WatchedValue("%")});w.push(n),w.push(o)}else{const e=(0,a.createNumberPropertyDefinition)(t,i);b.push(e)}}const R=[(0,a.createPropertyDefinitionsGeneralGroup)(g,"chartBasicStylesAppearanceGroup",o.t(null,void 0,i(15153))),(0,a.createPropertyDefinitionsGeneralGroup)(y,"scalesAppearanceGroup",o.t(null,void 0,i(93968))),(0,a.createPropertyDefinitionsGeneralGroup)(f,"buttonsAppearanceGroup",o.t(null,void 0,i(32744))),(0,a.createPropertyDefinitionsGeneralGroup)(b,"marginsAppearanceGroup",o.t(null,void 0,i(70937)))];return w.length>0&&R.push((0,a.createPropertyDefinitionsGeneralGroup)(w,"rightMarginsAppearanceGroup",o.t(null,void 0,i(62532)))),{definitions:R}}var Et=i(47462),jt=i(63829),zt=i(82826),Ut=i(11497),It=i(63950),qt=i(53078),Jt=i(82038),Kt=i(84806),Qt=i(87717),Xt=i(5666),Yt=i(7621),$t=i(8021),ei=i(60339),ti=i(75709),ii=i(20037),ni=i(84504);const oi={symbol:{bold:$t,default:qt},legend:{bold:ei,default:Jt},scales:{bold:ti,default:Kt},canvas:{bold:i(97660),default:Yt},events:{bold:ni,default:Xt},trading:{bold:ii,default:Qt}},ri=o.t(null,void 0,i(95481)),li=o.t(null,void 0,i(28715)),ai=o.t(null,void 0,i(90275)),si=o.t(null,void 0,i(23739)),ci=(o.t(null,void 0,i(69808)),o.t(null,void 0,i(76495)),o.t(null,void 0,i(8249)),o.t(null,void 0,i(94408)),o.t(null,void 0,i(24821)),!1);const di=[{id:"symbol-text-source-description",value:"description",title:o.t(null,void 0,i(78734))},{id:"symbol-text-source-ticker",value:"ticker",title:o.t(null,void 0,i(5791))},{id:"symbol-text-source-ticker-and-description",value:"ticker-and-description",title:o.t(null,void 0,i(16816))}];r.enabled("symbol_info_long_description")&&di.push({id:"symbol-text-source-long-description",value:"long-description",title:o.t(null,void 0,i(89315))});class hi{constructor(e,t,i){this._propertyPages=null,this._maxRightOffsetPropertyObject=null,this._defaultRightOffsetPercentageWatchedValue=null,this._useRightOffsetPercentageWatchedValue=null,this._profitLossOptions=null,this._isDestroyed=!1,this._availableDateFormatValues=null,this._undoModel=e,this._model=this._undoModel.model(),this._series=this._model.mainSeries(),this._chartWidgetProperties=t,this._options=i,this._seriesPropertyDefinitionViewModel=this._createSeriesViewModel();const n=this._chartWidgetProperties.childs().paneProperties.childs(),o=(0,P.createWVFromProperty)(n.gridLinesMode);this._gridColorsVisibilities={gridLinesMode:o,vertLinesVisible:(0,b.combine)((e=>"both"===e||"vert"===e),o.weakReference()),horzLinesVisible:(0,b.combine)((e=>"both"===e||"horz"===e),o.weakReference())},
this._legendPropertyPage=this._createLegendPropertyPage(),this._scalesPropertyPage=this._createScalesPropertyPage(),this._appearancePropertyPage=this._createAppearancePropertyPage(),this._tradingPropertyPage=this._createTradingPropertyPage(),this._eventsPropertyPage=this._createEventsPropertyPage(),this._series.onStyleChanged().subscribe(this,this._updateDefinitions),this._series.priceScaleChanged().subscribe(this,this._updateDefinitions)}destroy(){var e,t;null!==this._propertyPages&&this._propertyPages.filter(((e,t)=>0!==t)).forEach((e=>{(0,a.destroyDefinitions)(e.definitions.value())})),this._seriesPropertyDefinitionViewModel.destroy(),null===(e=this._pipValueTypeSubscription)||void 0===e||e.unsubscribe(),null===(t=this._availableDateFormatValues)||void 0===t||t.destroy(),this._series.onStyleChanged().unsubscribe(this,this._updateDefinitions),this._series.priceScaleChanged().unsubscribe(this,this._updateDefinitions);(0,n.ensureNotNull)(this._model.timeScale()).maxRightOffsetChanged().unsubscribeAll(this),this._gridColorsVisibilities.vertLinesVisible.destroy(),this._gridColorsVisibilities.horzLinesVisible.destroy(),this._gridColorsVisibilities.gridLinesMode.destroy(),this._isDestroyed=!0}async propertyPages(){if(null===this._propertyPages){const e=await this._seriesPropertyDefinitionViewModel.propertyPages();if(this._isDestroyed)throw new Error("ChartPropertyDefinitionsViewModel already destroyed");if(null===this._propertyPages){this._propertyPages=[...e],this._propertyPages.push(this._legendPropertyPage,this._scalesPropertyPage,await this._appearancePropertyPage),null!==this._tradingPropertyPage&&this._propertyPages.push(this._tradingPropertyPage);const t=await this._eventsPropertyPage;t&&this._propertyPages.push(t)}return this._propertyPages}return Promise.resolve(this._propertyPages)}_updatePlDisplayOptions(e){(0,n.ensureNotNull)(this._profitLossOptions).setValue([])}_updateDefinitions(){(0,a.destroyDefinitions)(this._scalesPropertyPage.definitions.value());const e=this._createScalesDefinitions();this._scalesPropertyPage.definitions.setValue(e.definitions)}_createSeriesViewModel(){const e={property:this._model.properties().childs().timezone,values:It.availableTimezones.map((e=>({value:e.id,title:e.title})))};return new u.SeriesPropertyDefinitionsViewModel(this._series,this._undoModel,"symbol",ri,oi.symbol,e)}_createLegendPropertyPage(){const e=this._chartWidgetProperties.childs().paneProperties.childs().legendProperties.childs(),t={property:this._series.properties().childs().statusViewStyle.childs().symbolTextSource,values:di},n=function(e,t,n,r,s){const c=[],d=[];if(I){const i=(0,a.createCheckablePropertyDefinition)({checked:(0,a.convertToDefinitionProperty)(e,t.showLogo,S)},{id:"showLogo",title:O});d.push(i)}const h=(0,a.createOptionsPropertyDefinition)({checked:(0,a.convertToDefinitionProperty)(e,t.showSeriesTitle,w),option:(0,a.convertToDefinitionProperty)(e,n.property,m)},{id:"symbolTextSource",title:G,options:new l.WatchedValue(n.values)});if(d.push(h),null!==r){const t=(0,
b.combineWithFilteredUpdate)(((t,i)=>"market"===t&&!(0,g.isEconomicSymbol)(e.mainSeries().symbolInfo())),((e,t)=>null!==e),e.mainSeries().marketStatusModel().status().weakReference(),e.mainSeries().symbolResolvingActive().weakReference()),i=(0,a.createCheckablePropertyDefinition)({checked:(0,a.convertToDefinitionProperty)(e,r,S),visible:(0,a.convertFromReadonlyWVToDefinitionProperty)(t.ownership())},{id:"showOpenMarketStatus",title:j});d.push(i)}const u=(0,a.createCheckablePropertyDefinition)({checked:(0,a.convertToDefinitionProperty)(e,t.showSeriesOHLC,D)},{id:"ohlcTitle",title:R});if(d.push(u),!y.alwaysShowLastPriceAndLastDayChange){const i=(0,a.createCheckablePropertyDefinition)({checked:(0,a.convertToDefinitionProperty)(e,t.showBarChange,_),visible:(0,a.makeProxyDefinitionProperty)((0,P.combineProperty)((e=>12!==e&&20!==e),e.mainSeries().properties().childs().style.weakReference()).ownership())},{id:"barChange",title:F});d.push(i)}if(d.push((0,a.createCheckablePropertyDefinition)({checked:(0,a.convertToDefinitionProperty)(e,t.showVolume,x),visible:(0,a.makeProxyDefinitionProperty)((0,P.combineProperty)((e=>20!==e),e.mainSeries().properties().childs().style.weakReference()).ownership())},{id:"barVolume",title:H})),y.lastDayChangeAvailable||y.alwaysShowLastPriceAndLastDayChange){const i=y.alwaysShowLastPriceAndLastDayChange?t.showBarChange:t.showLastDayChange,n=(0,a.createCheckablePropertyDefinition)({checked:(0,a.convertToDefinitionProperty)(e,i,T),visible:(0,a.makeProxyDefinitionProperty)((0,P.combineProperty)((e=>12!==e&&20!==e),e.mainSeries().properties().childs().style.weakReference()).ownership())},{id:"lastDayChange",title:A});d.push(n)}if(U){const i=(0,b.combineWithFilteredUpdate)((()=>e.model().symbolSources().some((e=>{var t;return void 0!==(null===(t=e.symbolInfo())||void 0===t?void 0:t.price_source_id)}))),(e=>!e),e.model().symbolSourceResolvingActive().weakReference(),(0,P.createWVFromGetterAndSubscription)((()=>e.model().symbolSources().length),e.model().symbolSourceCollectionChanged()).ownership());d.push((0,a.createCheckablePropertyDefinition)({disabled:(0,a.convertFromReadonlyWVToDefinitionProperty)(e.model().symbolSourceResolvingActive().weakReference()),checked:(0,a.convertToDefinitionProperty)(e,t.showPriceSource,M),visible:(0,a.convertFromReadonlyWVToDefinitionProperty)(i.ownership())},{id:"priceSource",title:z}))}c.push((0,a.createPropertyDefinitionsGeneralGroup)(d,"seriesLegendVisibilityGroup",o.t(null,void 0,i(95481))));const p=[],v=(0,a.createCheckablePropertyDefinition)({checked:(0,a.convertToDefinitionProperty)(e,t.showStudyArguments,C)},{id:"studyArguments",title:B}),q=(0,a.createCheckableSetPropertyDefinition)({checked:(0,a.convertToDefinitionProperty)(e,t.showStudyTitles,V)},{id:"studyTitles",title:Z},[v]),J=(0,a.createCheckablePropertyDefinition)({checked:(0,a.convertToDefinitionProperty)(e,t.showStudyValues,L)},{id:"studyValues",title:N}),K=(0,P.createWVFromGetterAndSubscription)((()=>e.model().priceDataSources().some((e=>!(0,
f.isActingAsSymbolSource)(e)&&e.showInObjectTree()))),e.model().dataSourceCollectionChanged());p.push(q,J),c.push((0,a.createPropertyDefinitionsGeneralGroup)(p,"studiesLegendVisibilityGroup",o.t(null,void 0,i(84549)),K));const Q=[],X=(0,a.createTransparencyPropertyDefinition)({checked:(0,a.convertToDefinitionProperty)(e,t.showBackground,k),transparency:(0,a.convertToDefinitionProperty)(e,t.backgroundTransparency,W)},{id:"legendBgTransparency",title:E});return Q.push(X),c.push((0,a.createPropertyDefinitionsGeneralGroup)(Q,"generalLegendGroup")),{definitions:c}}(this._undoModel,e,t,this._options.marketStatusWidgetEnabled?p.showMarketOpenStatusProperty:null);return(0,s.createPropertyPage)(n,"legend",li,oi.legend)}_createScalesPropertyPage(){const e=this._createScalesDefinitions();return(0,s.createPropertyPage)(e,"scales",ai,oi.scales)}_createScalesDefinitions(){const e=this._chartWidgetProperties.childs().scalesProperties.childs(),t={property:this._model.properties().childs().priceScaleSelectionStrategyName,values:(0,h.allPriceScaleSelectionStrategyInfo)().map((e=>({value:e.name,title:e.title})))};null===this._availableDateFormatValues&&(this._availableDateFormatValues=new l.WatchedValue(function(e=!1){const t=new Date(Date.UTC(1997,8,29));return Et.availableDateFormats.map((i=>({value:i,title:new zt.DateFormatter(i,e).format(t)})))}()).spawn());const n={property:Ut.timeHoursFormatProperty,values:[{value:"24-hours",title:o.t(null,void 0,i(5797))},{value:"12-hours",title:o.t(null,void 0,i(31882))}]},s=this._model.mainSeriesScaleRatioProperty();return function(e,t,i,n){const o=n.seriesPriceScale.properties().childs(),s=[],c=[];if(n.currencyConversionEnabled||n.unitConversionEnabled){const t=n.currencyConversionEnabled&&n.unitConversionEnabled?He:n.currencyConversionEnabled?Re:Fe,i=n.currencyConversionEnabled&&n.unitConversionEnabled?ue:n.currencyConversionEnabled?ce:he,o=(0,a.createOptionsPropertyDefinition)({option:(0,a.convertToDefinitionProperty)(e,(0,Q.currencyUnitVisibilityProperty)(),i)},{id:"scalesCurrencyUnit",title:t,options:new l.WatchedValue((0,Q.currencyUnitVisibilityOptions)())});c.push(o)}const d=(0,a.createOptionsPropertyDefinition)({option:(0,a.convertToDefinitionProperty)(e,(0,X.autoLogButtonsVisibilityProperty)(),de)},{id:"autoLogButtonsVisibility",title:Ae,options:new l.WatchedValue((0,X.autoLogButtonsVisibilityOptions)())});c.push(d);const h=(0,a.createNumberPropertyDefinition)({checked:(0,a.getLockPriceScaleDefinitionProperty)(e,o.lockScale,n.seriesPriceScale,ve),value:(0,a.getScaleRatioDefinitionProperty)(e,n.mainSeriesScaleRatioProperty,ge,[(0,J.limitedPrecision)(7),e=>e])},{id:"lockScale",title:Ne,min:new l.WatchedValue(n.mainSeriesScaleRatioProperty.getMinValue()),max:new l.WatchedValue(n.mainSeriesScaleRatioProperty.getMaxValue()),step:new l.WatchedValue(n.mainSeriesScaleRatioProperty.getStepChangeValue())}),u=(0,a.createOptionsPropertyDefinition)({option:(0,a.getPriceScaleSelectionStrategyDefinitionProperty)(e,n.scalesPlacementPropertyObj.property)},{id:"scalesPlacement",title:Be,
options:new l.WatchedValue(n.scalesPlacementPropertyObj.values)});c.push(h,u),s.push((0,a.createPropertyDefinitionsGeneralGroup)(c,"scalesPriceScaleGroup",Ee));const p=[],v=(0,a.createCheckablePropertyDefinition)({checked:(0,a.convertToDefinitionProperty)(e,o.alignLabels,ae)},{id:"noOverlappingLabels",title:Le});if(p.push(v),e.crossHairSource().isMenuEnabled()){const t=(0,a.createCheckablePropertyDefinition)({checked:(0,a.convertToDefinitionProperty)(e,K.addPlusButtonProperty,pe)},{id:"addPlusButton",title:Ze,solutionId:void 0});p.push(t)}if(n.countdownEnabled){const i=(0,a.createCheckablePropertyDefinition)({checked:(0,a.convertToDefinitionProperty)(e,t.showCountdown,se)},{id:"countdown",title:Ge});p.push(i)}if(n.seriesHasClosePrice){const n=(0,a.createCheckablePropertyDefinition)({checked:(0,a.convertToDefinitionProperty)(e,i.showSymbolLabels,Y)},{id:"symbolNameLabel",title:_e}),o=(0,a.createCheckablePropertyDefinition)({checked:(0,a.convertToDefinitionProperty)(e,i.showSeriesLastValue,$)},{id:"symbolValueLabel",title:Ce}),r=(0,a.createCheckablePropertyDefinition)({checked:(0,a.convertToDefinitionProperty)(e,t.showPriceLine,fe)},{id:"symbolLine",title:Ve}),s=(0,a.createLinePropertyDefinition)({color:(0,a.getColorDefinitionProperty)(e,t.priceLineColor,null,be),width:(0,a.convertToDefinitionProperty)(e,t.priceLineWidth,we)},{id:"SymbolLastValuePriceLine",title:""}),c=(0,a.createOptionsPropertyDefinition)({checked:(0,a.convertToDefinitionProperty)(e,i.showSeriesLastValue,$),option:(0,a.convertToDefinitionProperty)(e,i.seriesLastValueMode,ee)},{id:"symbolLastValueLabel",title:Ce,options:new l.WatchedValue(qe)});p.push((0,a.createPropertyDefinitionsCheckableListOptionsGroup)([n,o,r],[s,c],"symbolCheckableListGroup",We))}if(Ie){const i=t.highLowAvgPrice.childs(),n=(0,a.createCheckablePropertyDefinition)({checked:(0,a.convertToDefinitionProperty)(e,i.averageClosePriceLabelVisible,me)},{id:"averageClosePriceLabel",title:Ce}),o=(0,a.createCheckablePropertyDefinition)({checked:(0,a.convertToDefinitionProperty)(e,i.averageClosePriceLineVisible,Se)},{id:"averageCloseLine",title:Ve}),r=(0,a.createLinePropertyDefinition)({color:(0,a.getColorDefinitionProperty)(e,i.averagePriceLineColor,null,De),width:(0,a.convertToDefinitionProperty)(e,i.averagePriceLineWidth,Te)},{id:"averageClosePriceLine",title:""});p.push((0,a.createPropertyDefinitionsCheckableListOptionsGroup)([n,o],[r],"avgCloseCheckableListGroup",xe))}let g,y;g=(0,a.createCheckablePropertyDefinition)({visible:(0,a.convertFromReadonlyWVToDefinitionProperty)((0,P.createWVFromGetterAndSubscription)((()=>e.model().priceDataSources().some((e=>!(0,f.isActingAsSymbolSource)(e)&&e.showInObjectTree()))),e.model().dataSourceCollectionChanged()).ownership()),checked:(0,a.convertToDefinitionProperty)(e,i.showStudyPlotLabels,re)},{id:"studyNameLabel",title:_e}),y=(0,a.createCheckablePropertyDefinition)({visible:(0,a.convertFromReadonlyWVToDefinitionProperty)((0,P.createWVFromGetterAndSubscription)((()=>e.model().priceDataSources().some((e=>!(0,
f.isActingAsSymbolSource)(e)&&e.showInObjectTree()))),e.model().dataSourceCollectionChanged()).ownership()),checked:(0,a.convertToDefinitionProperty)(e,i.showStudyLastValue,le)},{id:"studyLastValueLabel",title:Ce}),p.push((0,a.createPropertyDefinitionsCheckableListOptionsGroup)([g,y],[],"studiesCheckableListGroup",Me));const b=t.highLowAvgPrice.childs(),w=(0,a.createCheckablePropertyDefinition)({checked:(0,a.convertToDefinitionProperty)(e,b.highLowPriceLabelsVisible,te)},{id:"highLowPriceLabels",title:Ce}),m=(0,a.createCheckablePropertyDefinition)({checked:(0,a.convertToDefinitionProperty)(e,b.highLowPriceLinesVisible,ie)},{id:"highLowPriceLine",title:Ve}),S=(0,a.createLinePropertyDefinition)({color:(0,a.getColorDefinitionProperty)(e,b.highLowPriceLinesColor,null,ne),width:(0,a.convertToDefinitionProperty)(e,b.highLowPriceLinesWidth,oe)},{id:"highLowLineColors",title:""});p.push((0,a.createPropertyDefinitionsCheckableListOptionsGroup)([w,m],[S],"hiLowCheckableListGroup",Oe)),s.push((0,a.createPropertyDefinitionsGeneralGroup)(p,"scalesLabelsLineGroup",ke));const D=[];if(r.enabled("scales_date_format")){const t=(0,a.createOptionsPropertyDefinition)({option:(0,a.convertToDefinitionProperty)(e,n.dateFormatPropertyObj.property,ye)},{id:"dateFormat",title:je,options:n.dateFormatPropertyObj.values});D.push(t)}if(r.enabled("scales_time_hours_format")){const t=(0,a.createOptionsPropertyDefinition)({option:(0,a.convertToDefinitionProperty)(e,n.timeHoursFormatPropertyObj.property,Pe)},{id:"timeHoursFormat",title:ze,options:new l.WatchedValue(n.timeHoursFormatPropertyObj.values)});D.push(t)}return D.length>0&&s.push((0,a.createPropertyDefinitionsGeneralGroup)(D,"scalesTimeScaleGroup",Ue)),{definitions:s}}(this._undoModel,this._series.properties().childs(),e,{disableSeriesPrevCloseValueProperty:this._series.isDWMProperty(),seriesHasClosePrice:this._series.hasClosePrice(),seriesPriceScale:this._series.priceScale(),mainSeriesScaleRatioProperty:s,scalesPlacementPropertyObj:t,dateFormatPropertyObj:{property:jt.dateFormatProperty,values:this._availableDateFormatValues},timeHoursFormatPropertyObj:n,currencyConversionEnabled:this._options.currencyConversionEnabled,unitConversionEnabled:this._options.unitConversionEnabled,countdownEnabled:this._options.countdownEnabled,withWeekdayProperty:void 0})}_createMaxOffsetPropertyObject(){const e=(0,n.ensureNotNull)(this._model.timeScale()),t=new l.WatchedValue(Math.floor(e.maxRightOffset()));e.maxRightOffsetChanged().subscribe(this,(e=>{t.setValue(Math.floor(e))})),this._maxRightOffsetPropertyObject={value:e.defaultRightOffset(),min:new l.WatchedValue(0),max:t}}_createDefaultRightOffsetPercentageWatchedValue(){const e=(0,n.ensureNotNull)(this._model.timeScale());this._defaultRightOffsetPercentageWatchedValue=e.defaultRightOffsetPercentage()}_createUseRightOffsetPercentageWatchedValue(){const e=(0,n.ensureNotNull)(this._model.timeScale());this._useRightOffsetPercentageWatchedValue=e.usePercentageRightOffset()}async _createAppearancePropertyPage(){
const e=this._chartWidgetProperties.childs(),t=e.paneProperties.childs(),i=e.scalesProperties.childs(),o=this._model.watermarkSource();let r=null;null!==o&&(r=o.properties().childs());const l={property:c.property(),values:c.availableValues()},a={property:d.property(),values:d.availableValues()};null===this._maxRightOffsetPropertyObject&&this._createMaxOffsetPropertyObject(),null===this._defaultRightOffsetPercentageWatchedValue&&this._createDefaultRightOffsetPercentageWatchedValue(),null===this._useRightOffsetPercentageWatchedValue&&this._createUseRightOffsetPercentageWatchedValue();const h=(0,n.ensureNotNull)(this._maxRightOffsetPropertyObject),u=(0,n.ensureNotNull)(this._defaultRightOffsetPercentageWatchedValue),p=(0,n.ensureNotNull)(this._useRightOffsetPercentageWatchedValue),v=await Nt(this._undoModel,t,r,i,h,l,a,u,p,this._gridColorsVisibilities);return(0,s.createPropertyPage)(v,"canvas",si,oi.canvas)}_createTradingPropertyPage(){return null}async _createEventsPropertyPage(){return null}}},84504:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="M9.75 3c.41 0 .75.34.75.75V5h7V3.75a.75.75 0 0 1 1.5 0V5a4 4 0 0 1 4 4v10a4 4 0 0 1-4 4H9a4 4 0 0 1-4-4V9a4 4 0 0 1 4-4V3.75c0-.41.34-.75.75-.75zM9 6.5h10A2.5 2.5 0 0 1 21.5 9v1.5h-15V9A2.5 2.5 0 0 1 9 6.5zM6.5 12v7A2.5 2.5 0 0 0 9 21.5h10a2.5 2.5 0 0 0 2.5-2.5v-7h-15z"/></svg>'},8021:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M12 7h-.75V4h-1.5v3H9a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h.75v3h1.5v-3H12a1 1 0 0 0 1-1V8a1 1 0 0 0-1-1ZM9.5 19.5v-11h2v11h-2Zm8-3v-5h2v5h-2Zm.24-6.5H17a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h.75v3h1.5v-3H20a1 1 0 0 0 1-1v-6a1 1 0 0 0-1-1h-.76V7h-1.5v3Z"/></svg>'},97660:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path stroke="currentColor" stroke-width="1.5" d="m7.5 16.5-1 1v4h4l1-1m-4-4 9-9m-9 9 4 4m0 0 9-9m-4-4 .59-.59a2 2 0 0 1 2.82 0L21.1 8.1a2 2 0 0 1 0 2.82l-.59.59m-4-4 2 2 2 2"/></svg>'},75709:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path stroke="currentColor" stroke-width="1.5" d="M10.5 20.5a2 2 0 1 1-2-2m2 2a2 2 0 0 0-2-2m2 2h14m-16-2v-14m16 16L21 17m3.5 3.5L21 24M8.5 4.5 12 8M8.5 4.5 5 8"/></svg>'},60339:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="M7 7.5h14a.5.5 0 0 1 0 1H7a.5.5 0 0 1 0-1ZM5 8c0-1.1.9-2 2-2h14a2 2 0 1 1 0 4H7a2 2 0 0 1-2-2Zm13 5H6v1.5h12V13ZM6 17h12v1.5H6V17Zm12 4H6v1.5h12V21Z"/></svg>'},20037:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="m17.53 15.08.45.33.44-.33 6.65-4.92a2.3 2.3 0 0 0 .42-3.3 2.35 2.35 0 0 0-3.23-.4l-4.28 3.18-4.29-3.18a2.35 2.35 0 0 0-3.22.4 2.3 2.3 0 0 0 .42 3.3l6.64 4.92Zm6.64-6.1-6.2 4.59-6.19-4.6a.83.83 0 0 1-.15-1.18.85.85 0 0 1 1.17-.15l4.73 3.51.45.33.44-.33 4.74-3.5a.85.85 0 0 1 1.16.14c.3.37.23.9-.15 1.19Zm-13.7 3.94-.45-.33-.44.33-6.65 4.92a2.3 2.3 0 0 0-.42 3.3 2.35 2.35 0 0 0 3.23.4l4.28-3.18 4.29 3.18c1 .75 2.44.57 3.22-.4a2.3 2.3 0 0 0-.42-3.3l-6.64-4.92Zm-6.64 6.1 6.2-4.59 6.19 4.6c.38.27.45.81.15 1.18a.85.85 0 0 1-1.17.15l-4.73-3.51-.45-.33-.44.33-4.74 3.5a.85.85 0 0 1-1.16-.14.83.83 0 0 1 .15-1.19Z"/></svg>'},5666:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M10 5h8V3h1v2h.5A3.5 3.5 0 0 1 23 8.5v11a3.5 3.5 0 0 1-3.5 3.5h-11A3.5 3.5 0 0 1 5 19.5v-11A3.5 3.5 0 0 1 8.5 5H9V3h1v2Zm12 5V8.5A2.5 2.5 0 0 0 19.5 6h-11A2.5 2.5 0 0 0 6 8.5V10h16ZM6 11v8.5A2.5 2.5 0 0 0 8.5 22h11a2.5 2.5 0 0 0 2.5-2.5V11H6Z"/></svg>'},53078:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="M11 4h-1v3H8.5a.5.5 0 0 0-.5.5v13a.5.5 0 0 0 .5.5H10v3h1v-3h1.5a.5.5 0 0 0 .5-.5v-13a.5.5 0 0 0-.5-.5H11V4ZM9 8v12h3V8H9Zm10-1h-1v3h-1.5a.5.5 0 0 0-.5.5v7a.5.5 0 0 0 .5.5H18v3h1v-3h1.5a.5.5 0 0 0 .5-.5v-7a.5.5 0 0 0-.5-.5H19V7Zm-2 10v-6h3v6h-3Z"/></svg>'},7621:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M16.73 6.56a2.5 2.5 0 0 1 3.54 0l1.17 1.17a2.5 2.5 0 0 1 0 3.54l-.59.58-9 9-1 1-.14.15H6v-4.7l.15-.15 1-1 9-9 .58-.59Zm2.83.7a1.5 1.5 0 0 0-2.12 0l-.23.24 3.29 3.3.23-.24a1.5 1.5 0 0 0 0-2.12l-1.17-1.17Zm.23 4.24L16.5 8.2l-8.3 8.3 3.3 3.3 8.3-8.3Zm-9 9L7.5 17.2l-.5.5V21h3.3l.5-.5Z"/></svg>'},84806:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M10.5 20.5a2 2 0 1 1-2-2m2 2a2 2 0 0 0-2-2m2 2h14m-16-2v-14m16 16L21 17m3.5 3.5L21 24M8.5 4.5L12 8M8.5 4.5L5 8"/></svg>'},82038:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="M7 7h14a1 1 0 1 1 0 2H7a1 1 0 0 1 0-2ZM5 8c0-1.1.9-2 2-2h14a2 2 0 1 1 0 4H7a2 2 0 0 1-2-2Zm13 5H6v1h12v-1Zm0 4H6v1h12v-1ZM6 21h12v1H6v-1Z"/></svg>'},87717:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M12.8441 8.61921C13.232 8.13425 13.9481 8.07567 14.4097 8.49112L18.1651 11.871L18.4996 12.172L18.8341 11.871L22.5896 8.49121C23.0512 8.07582 23.7672 8.13438 24.1551 8.61927C24.5188 9.07382 24.4567 9.73484 24.0147 10.1137L18.4996 14.8409L12.9845 10.1137C12.5425 9.73482 12.4804 9.07379 12.8441 8.61921ZM15.0787 7.74783C14.1896 6.94765 12.8104 7.06048 12.0632 7.99452C11.3628 8.87007 11.4824 10.1432 12.3338 10.8729L18.1742 15.879L18.4996 16.158L18.825 15.879L24.6655 10.8729C25.5168 10.1432 25.6364 8.87006 24.936 7.99454C24.1888 7.06061 22.8097 6.94781 21.9207 7.7479L18.4996 10.8267L15.0787 7.74783ZM15.1551 18.8798C14.7672 19.3647 14.0511 19.4233 13.5895 19.0078L9.83409 15.628L9.49962 15.3269L9.16514 15.6279L5.4096 19.0077C4.94802 19.4231 4.23205 19.3646 3.84411 18.8797C3.48044 18.4251 3.54256 17.7641 3.98455 17.3853L9.49961 12.6581L15.0147 17.3853C15.4567 17.7641 15.5188 18.4252 15.1551 18.8798ZM12.9205 19.7511C13.8096 20.5513 15.1888 20.4385 15.936 19.5044C16.6364 18.6289 16.5168 17.3557 15.6655 16.626L9.82501 11.6199L9.49961 11.341L9.17421 11.6199L3.33376 16.626C2.48244 17.3557 2.3628 18.6289 3.06327 19.5044C3.81047 20.4383 5.1895 20.5512 6.07854 19.7511L9.4996 16.6723L12.9205 19.7511Z"/></svg>'}}]);