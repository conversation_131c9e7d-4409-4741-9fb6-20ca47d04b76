(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[7078],{30744:e=>{e.exports={backButton:"backButton-yMMXpYEB"}},87886:e=>{e.exports={wrapper:"wrapper-nGEmjtaX",container:"container-nGEmjtaX",tab:"tab-nGEmjtaX",active:"active-nGEmjtaX",title:"title-nGEmjtaX",icon:"icon-nGEmjtaX",withoutIcon:"withoutIcon-nGEmjtaX",titleText:"titleText-nGEmjtaX",nested:"nested-nGEmjtaX",isTablet:"isTablet-nGEmjtaX",isMobile:"isMobile-nGEmjtaX",showLastDivider:"showLastDivider-nGEmjtaX",medium:"medium-nGEmjtaX",large:"large-nGEmjtaX",withoutArrow:"withoutArrow-nGEmjtaX",accessible:"accessible-nGEmjtaX"}},94574:e=>{e.exports={"tablet-normal-breakpoint":"screen and (max-width: 768px)","tablet-small-breakpoint":"screen and (max-width: 430px)",withSidebar:"withSidebar-F0WBLDV5",content:"content-F0WBLDV5",tabContent:"tabContent-F0WBLDV5",applyToAllButton:"applyToAllButton-F0WBLDV5"}},11559:e=>{e.exports={themesButtonText:"themesButtonText-w7kgghoW",themesButtonIcon:"themesButtonIcon-w7kgghoW",defaultsButtonText:"defaultsButtonText-w7kgghoW",defaultsButtonItem:"defaultsButtonItem-w7kgghoW",remove:"remove-w7kgghoW"}},71150:e=>{e.exports={separator:"separator-QjUlCDId",small:"small-QjUlCDId",normal:"normal-QjUlCDId",large:"large-QjUlCDId"}},22265:(e,t,n)=>{"use strict";n.d(t,{DialogSidebarItem:()=>u,DialogSidebarWrapper:()=>h});var i,l=n(50959),a=n(97754),o=n.n(a),s=n(9745),r=n(65631),c=n(68648),m=n(87886);function h(e){return l.createElement("div",{className:m.wrapper,...e})}function u(e){const{mode:t,title:n,icon:i,isActive:a,onClick:h,tag:u="div",reference:d,className:b,mobileFontSize:p="medium",showLastDivider:g,useBoldIconsForMobile:v,hideArrow:T,...y}=e,{isMobile:_,isTablet:C}=(0,r.getSidebarMode)(t),f=function(){if(_&&v)return null==i?void 0:i.bold;return a?null==i?void 0:i.bold:null==i?void 0:i.default}();return l.createElement(u,{...y,ref:d,title:C?n:"",className:o()(m.tab,C&&m.isTablet,_&&m.isMobile,a&&m.active,T&&m.withoutArrow,b,C&&"apply-common-tooltip"),onClick:h},i&&l.createElement(s.Icon,{className:m.icon,icon:f}),!C&&l.createElement("span",{className:o()(m.title,!i&&m.withoutIcon,"medium"===p?m.medium:m.large,g&&m.showLastDivider)},l.createElement("span",{className:o()(m.titleText,"apply-overflow-tooltip")},n),_&&!T&&l.createElement(s.Icon,{className:m.nested,icon:c})))}!function(e){e.Medium="medium",e.Large="large"}(i||(i={}))},65631:(e,t,n)=>{"use strict";var i,l;function a(e){return{isMobile:"mobile"===e,isTablet:"tablet"===e}}n.d(t,{getSidebarMode:()=>a}),function(e){e.Bold="bold",e.Default="default"}(i||(i={})),function(e){e.Tablet="tablet",e.Mobile="mobile"}(l||(l={}))},56080:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GeneralChartPropertiesDialogRenderer:()=>he});var i=n(11542),l=n(50959),a=n(94720),o=n(62709),s=n(50182),r=n(66849),c=n(71891),m=n(56840),h=n.n(m),u=n(68993),d=n(90692),b=n(9745),p=n(56570),g=n(10838),v=n(11684),T=n(85662),y=(n(21251),n(3615));const _=i.t(null,void 0,n(6113));var C=n(50655),f=n(34585),S=n(38068),k=n(51768),A=n(70412),E=n(32563),w=n(11559);function M(e){
const{themeName:t,chartWidgetCollection:n,onRemove:i,manager:a}=e,[o,s]=(0,A.useHover)(),r=l.useCallback((()=>function(e,t,n){(0,y.showConfirm)({text:_.format({name:e}),onConfirm:({dialogClose:n})=>{(0,T.removeTheme)(e),t&&t(e),n()}},n)}(t,i,a)),[t,i,a]),c=l.useCallback((()=>{(0,T.loadTheme)(n,{themeName:t,standardTheme:!1}).then((()=>{(0,k.trackEvent)("GUI","Switch to custom theme")}))}),[t,n]);return l.createElement("div",{...s},l.createElement(g.AccessibleMenuItem,{"data-series-theme-item-theme-name":t,className:w.defaultsButtonItem,isActive:!1,label:t,onClick:c,toolbox:l.createElement(S.MenuRemoveButton,{className:w.remove,hidden:!E.mobiletouch&&!o,onClick:r})}))}var P=n(95276),B=n(20243),I=n(44996);const x=i.t(null,void 0,n(93553)),D=i.t(null,void 0,n(96413)),N=(0,f.appendEllipsis)(i.t(null,void 0,n(76266))),L=i.t(null,void 0,n(77571)),R=p.enabled("chart_template_storage");class F extends l.PureComponent{constructor(e){super(e),this._manager=null,this._reference=l.createRef(),this._handleApplyDefaults=()=>{const{model:e,chartWidgetCollection:t}=this.props;e.restorePreferences();const n=(0,T.getCurrentTheme)().name;(0,T.loadTheme)(t,{themeName:n,standardTheme:!0,applyOverrides:!0,onlyActiveChart:!0})},this._handleSaveAs=()=>{if(R){const{model:e}=this.props;window.runOrSignIn((()=>async function(e,t,i){const[l,a]=await Promise.all([Promise.all([n.e(6302),n.e(7648)]).then(n.bind(n,57351)),(0,T.getThemeNames)()]);l.showThemeSaveDialog(e,t,a,i)}(e.model().theme(),this._syncThemeList,this._handleRenameClose)),{source:"Save theme in chart properties"})}},this._handleRemoveTheme=e=>{this.setState({themes:this.state.themes.filter((t=>t!==e))})},this._syncThemeList=()=>{R&&(0,T.getThemeNames)().then((e=>{this.setState({themes:e})}))},this._handleListboxFocus=e=>{e.target instanceof HTMLElement&&(0,B.handleAccessibleMenuFocus)(e,this._reference)},this._handleRenameClose=()=>{var e;null===(e=this._reference.current)||void 0===e||e.focus()},this.state={themes:[]},this._syncThemeList()}render(){return l.createElement(C.SlotContext.Consumer,null,(e=>(this._manager=e,l.createElement(d.MatchMedia,{rule:"screen and (max-width: 768px)"},(e=>l.createElement(P.ControlDisclosure,{id:"series-theme-manager",className:!e&&w.themesButtonText,hideArrowButton:e,"data-name":"theme-select",ref:this._reference,buttonChildren:this._getPlaceHolderItem(e),onListboxFocus:this._handleListboxFocus,onListboxKeyDown:B.handleAccessibleMenuKeyDown},this._getThemeItems(e)))))))}_getPlaceHolderItem(e){return e?l.createElement(b.Icon,{className:w.themesButtonIcon,icon:I}):x}_getThemeItems(e){const{isApplyToAllVisible:t,chartWidgetCollection:n,applyToAllCallback:i}=this.props,{themes:a}=this.state;return l.createElement(l.Fragment,null,e&&t&&l.createElement(g.AccessibleMenuItem,{className:w.defaultsButtonItem,isActive:!1,label:L,onClick:i}),l.createElement(g.AccessibleMenuItem,{"data-name":"series-theme-manager-apply-defaults",className:w.defaultsButtonItem,isActive:!1,label:D,onClick:this._handleApplyDefaults}),R&&l.createElement(g.AccessibleMenuItem,{
"data-name":"series-theme-manager-save-as",className:w.defaultsButtonItem,isActive:!1,label:N,onClick:this._handleSaveAs}),a.length>0&&l.createElement(l.Fragment,null,l.createElement(v.PopupMenuSeparator,{key:"separator"}),a.map((e=>l.createElement(M,{key:e,themeName:e,onRemove:this._handleRemoveTheme,chartWidgetCollection:n,manager:this._manager})))))}}var W=n(59064),z=n(71953),V=n(24437),j=n(97754),G=n.n(j),X=n(50238),H=n(6190),K=n(22265),O=n(65631),U=n(87886);const q=(0,l.forwardRef)(((e,t)=>{const[n,i]=(0,X.useRovingTabindexElement)(t),{className:a}=e;return l.createElement(K.DialogSidebarItem,{...e,className:G()(U.accessible,a),tag:"button",reference:n,tabIndex:i})}));function Q(e){const{mode:t,className:n,...i}=e,{isMobile:a,isTablet:o}=(0,O.getSidebarMode)(t),s=G()(U.container,o&&U.isTablet,a&&U.isMobile,n);return l.createElement(H.Toolbar,{...i,className:s,orientation:"vertical",blurOnEscKeydown:!1,blurOnClick:!1,"data-role":"dialog-sidebar"})}var J=n(86656);const Y={areaSymbolMinTick:"normal",areaSymbolTimezone:"normal",barSymbolMinTick:"normal",barSymbolTimezone:"normal",baselineSymbolMinTick:"normal",baselineSymbolTimezone:"normal",candleSymbolMinTick:"normal",candleSymbolTimezone:"normal",dateFormat:"normal",haSymbolMinTick:"normal",haSymbolTimezone:"normal",hiloSymbolMinTick:"normal",hiloSymbolTimezone:"normal",hollowCandleSymbolMinTick:"normal",hollowCandleSymbolTimezone:"normal",kagiAtrLength:"normal",kagiReversalAmount:"normal",kagiStyle:"normal",kagiSymbolMinTick:"normal",kagiSymbolTimezone:"normal",lineSymbolMinTick:"normal",lineSymbolTimezone:"normal",sessionId:"normal",lockScale:"normal",mainSeriesSymbolAreaPriceSource:"normal",mainSeriesSymbolBaseLevelPercentage:"normal",mainSeriesSymbolBaseLinePriceSource:"normal",mainSeriesSymbolLinePriceSource:"normal",mainSeriesSymbolStyleType:"normal",navButtons:"big",paneButtons:"big",scalesCurrencyUnit:"big",autoLogButtonsVisibility:"big",pbLb:"normal",pbSymbolMinTick:"normal",pbSymbolTimezone:"normal",pnfAtrLength:"normal",pnfBoxSize:"normal",pnfReversalAmount:"normal",pnfSources:"normal",pnfStyle:"normal",pnfSymbolMinTick:"normal",pnfSymbolTimezone:"normal",rangeSymbolMinTick:"normal",rangeSymbolTimezone:"normal",renkoAtrLength:"normal",renkoBoxSize:"normal",renkoStyle:"normal",renkoSymbolMinTick:"normal",renkoSymbolTimezone:"normal",scalesPlacement:"normal",symbolLastValueLabel:"big",symbolTextSource:"normal",tradingNotifications:"normal",tpoSymbolMinTick:"normal",tpoSymbolTimezone:"normal",volFootprintSymbolMinTick:"normal",volFootprintSymbolTimezone:"normal"};var Z=n(48199),$=n(63273),ee=n(30744);function te(e){return l.createElement(Z.BackButton,{className:ee.backButton,size:"medium","aria-label":i.t(null,{context:"input"},n(41256)),preservePaddings:!0,flipIconOnRtl:(0,$.isRtl)(),...e})}var ne=n(19291),ie=n(94574);const le="properties_dialog.last_page_id";class ae extends l.PureComponent{constructor(e){var t;super(e),this._renderChildren=({requestResize:e,isSmallWidth:t})=>(this._requestResize=e,l.createElement("div",{className:ie.content
},this._renderTabs(t),this._renderTabContent(t))),this._renderApplyToAllButton=()=>l.createElement(d.MatchMedia,{rule:V.DialogBreakpoints.TabletNormal},(e=>this._renderApplyToAll(e))),this._renderFooterLeft=()=>{const{model:e,chartWidgetCollection:t}=this.props,{isApplyToAllVisible:n}=this.state;return l.createElement(F,{model:e,isApplyToAllVisible:n,applyToAllCallback:this._handleApplyToAll,chartWidgetCollection:t})},this._createTabClickHandler=e=>()=>this._selectPage(e),this._selectPage=(e,t)=>{const{activePage:n}=this.state;e!==n&&(n&&n.definitions.unsubscribe(this._onChangeActivePageDefinitions),null!==e&&(t||h().setValue(le,e.id),e.definitions.subscribe(this._onChangeActivePageDefinitions)),this.setState({activePage:e,tableKey:Date.now()},(()=>{this._requestResize&&this._requestResize()})))},this._onChangeActivePageDefinitions=()=>{z.logger.logNormal("Definition collection was updated"),this.setState({tableKey:Date.now()},(()=>{this._requestResize&&this._requestResize()}))},this._onTabVisibilityChanged=()=>{const e=this.props.pages.filter((e=>e.visible.value()));this.setState({visiblePages:e});const t=this.state.activePage;null===t||e.includes(t)||this._selectPage(0===e.length?null:e[0],!0)},this._handleCancel=()=>{this.props.onCancel(),this.props.onClose()},this._handleSubmit=()=>{this.props.onSubmit(),this.props.onClose()},this._handleScroll=()=>{W.globalCloseDelegate.fire()},this._handleApplyToAll=()=>{const{chartWidgetCollection:e,model:t}=this.props,{isApplyToAllVisible:n}=this.state;n&&e.applyPreferencesToAllCharts(t)},this._syncApplyToAllVisibility=()=>{const{chartWidgetCollection:e}=this.props;this.setState({isApplyToAllVisible:(0,u.isMultipleLayout)(e.layout.value())})},this._handleBackClick=()=>{const{activePage:e}=this.state;e&&e.definitions.unsubscribe(this._onChangeActivePageDefinitions),this.setState({activePage:null})},this._handleForceFocus=e=>{(0,ne.updateTabIndexes)(),setTimeout((()=>{const[t]=(0,ne.queryTabbableElements)(e);t&&t.focus()}))};const{pages:n,activePageId:i}=e,a=n.filter((e=>e.visible.value()));let o=null!==(t=a.find((e=>e.id===i)))&&void 0!==t?t:null;if(!o){const e=h().getValue(le),t=a.find((t=>t.id===e));o=t||(a.length>0?a[0]:null)}this.state={activePage:o,visiblePages:a,isApplyToAllVisible:(0,u.isMultipleLayout)(e.chartWidgetCollection.layout.value()),tableKey:Date.now()}}componentDidMount(){const{chartWidgetCollection:e,pages:t}=this.props,{activePage:n}=this.state;e.layout.subscribe(this._syncApplyToAllVisibility),n&&n.definitions.subscribe(this._onChangeActivePageDefinitions),t.forEach((e=>e.visible.subscribe(this._onTabVisibilityChanged)))}componentWillUnmount(){const{chartWidgetCollection:e,pages:t}=this.props,{activePage:n}=this.state;n&&n.definitions.unsubscribe(this._onChangeActivePageDefinitions),e.layout.unsubscribe(this._syncApplyToAllVisibility),t.forEach((e=>e.visible.unsubscribe(this._onTabVisibilityChanged)))}render(){const{isOpened:e,onClose:t,shouldReturnFocus:a}=this.props,{activePage:o}=this.state;return l.createElement(d.MatchMedia,{
rule:V.DialogBreakpoints.TabletSmall},(r=>l.createElement(s.AdaptiveConfirmDialog,{className:ie.withSidebar,dataName:"series-properties-dialog",onClose:t,isOpened:e,title:null!==o&&r?o.title:i.t(null,void 0,n(71262)),footerLeftRenderer:this._renderFooterLeft,additionalButtons:this._renderApplyToAllButton(),additionalHeaderElement:null!==o&&r?l.createElement(te,{onClick:this._handleBackClick}):void 0,onSubmit:this._handleSubmit,onForceFocus:this._handleForceFocus,onCancel:this._handleCancel,render:this._renderChildren,submitOnEnterKey:!1,shouldReturnFocus:a})))}_renderTabContent(e){const{pages:t}=this.props,n=this._getCurrentPage(e);if(n){const e=t.find((e=>e.id===n.id)),i=e?e.definitions.value():[];return l.createElement(J.TouchScrollContainer,{className:ie.tabContent,onScroll:this._handleScroll},l.createElement(r.ControlCustomWidthContext.Provider,{value:Y},l.createElement(c.PropertyTable,{key:this.state.tableKey},i.map((e=>l.createElement(o.Section,{key:e.id,definition:e}))))))}return null}_renderTabs(e){const{activePage:t,visiblePages:n}=this.state;if(t&&e)return null;const i=this._getCurrentPage(e);return l.createElement(d.MatchMedia,{rule:V.DialogBreakpoints.TabletNormal},(e=>l.createElement(d.MatchMedia,{rule:V.DialogBreakpoints.TabletSmall},(t=>{const a=t?"mobile":e?"tablet":void 0;return l.createElement(Q,{mode:a,onScroll:this._handleScroll},n.map((e=>l.createElement(q,{key:e.id,mode:a,"data-name":e.id,title:e.title,icon:e.icon,onClick:this._createTabClickHandler(e),isActive:i?e.id===i.id:void 0}))))}))))}_renderApplyToAll(e){const{isApplyToAllVisible:t}=this.state;return!e&&t&&l.createElement("span",{className:ie.applyToAllButton},l.createElement(a.Button,{appearance:"stroke",onClick:this._handleApplyToAll},i.t(null,void 0,n(77571))))}_getCurrentPage(e){const{pages:t}=this.props,{activePage:n}=this.state;let i=null;return n?i=n:!e&&t.length&&(i=t[0]),i}}var oe=n(76422),se=n(29280),re=n(19466),ce=n(28124);const me=i.t(null,void 0,n(71262));class he extends se.DialogRenderer{constructor(e){super(),this._handleClose=()=>{var e;null===(e=this._rootInstance)||void 0===e||e.unmount(),this._setVisibility(!1),this._onClose&&this._onClose()},this._handleSubmit=()=>{},this._handleCancel=()=>{this._model.undoToCheckpoint(this._checkpoint)},this._propertyPages=e.propertyPages,this._model=e.model,this._activePageId=e.activePageId,this._onClose=e.onClose,this._chartWidgetCollection=e.chartWidgetCollection,this._checkpoint=this._ensureCheckpoint(e.undoCheckPoint)}hide(e){e?this._handleCancel():this._handleSubmit(),this._handleClose()}isVisible(){return this.visible().value()}show(e){this.visible().value()||(this._rootInstance=(0,ce.createReactRoot)(l.createElement(ae,{title:me,isOpened:!0,onSubmit:this._handleSubmit,onClose:this._handleClose,onCancel:this._handleCancel,pages:this._propertyPages,model:this._model,activePageId:this._activePageId,chartWidgetCollection:this._chartWidgetCollection,shouldReturnFocus:null==e?void 0:e.shouldReturnFocus}),this._container),this._setVisibility(!0),oe.emit("edit_object_dialog",{
objectType:"mainSeries",scriptTitle:this._model.mainSeries().title(re.TitleDisplayTarget.StatusLine)}))}_ensureCheckpoint(e){return void 0===e&&(e=this._model.createUndoCheckpoint()),e}}},11684:(e,t,n)=>{"use strict";n.d(t,{PopupMenuSeparator:()=>r});var i,l=n(50959),a=n(97754),o=n.n(a),s=n(71150);function r(e){const{size:t="normal",className:n,ariaHidden:i=!1}=e;return l.createElement("div",{className:o()(s.separator,"small"===t&&s.small,"normal"===t&&s.normal,"large"===t&&s.large,n),role:"separator","aria-hidden":i})}!function(e){e.Small="small",e.Large="large",e.Normal="normal"}(i||(i={}))},19347:e=>{e.exports={accessible:"accessible-NQERJsv9",active:"active-NQERJsv9"}},15965:e=>{e.exports={button:"button-Y1TCZogJ",active:"active-Y1TCZogJ"}},10838:(e,t,n)=>{"use strict";n.d(t,{AccessibleMenuItem:()=>m});var i=n(50959),l=n(97754),a=n.n(l),o=n(3343),s=n(50238),r=n(16396),c=n(19347);function m(e){const{className:t,...n}=e,[l,m]=(0,s.useRovingTabindexElement)(null);return i.createElement(r.PopupMenuItem,{...n,className:a()(c.accessible,e.isActive&&c.active,t),reference:l,tabIndex:m,onKeyDown:function(e){if(e.target!==e.currentTarget)return;const t=(0,o.hashFromEvent)(e);13!==t&&32!==t||(e.preventDefault(),l.current instanceof HTMLElement&&l.current.click())},"data-role":"menuitem","aria-disabled":e.isDisabled||void 0})}},38068:(e,t,n)=>{"use strict";n.d(t,{MenuRemoveButton:()=>m});var i=n(50959),l=n(97754),a=n.n(l),o=n(50238),s=n(96040),r=n(60925),c=n(15965);function m(e){const{tooltip:t,onClick:n,...l}=e,[m,h]=(0,o.useRovingTabindexElement)(null);return i.createElement("button",{ref:m,tabIndex:h,onClick:n,className:a()(c.button,l.isActive&&c.active),type:"button"},i.createElement(s.RemoveButton,{"aria-label":t,...l,"data-tooltip":t,icon:r}))}},68648:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path stroke="currentcolor" stroke-width="1.3" d="M12 9l5 5-5 5"/></svg>'}}]);