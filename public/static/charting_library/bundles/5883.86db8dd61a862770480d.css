.row-Sj9z7O1v {
  display: grid;
  grid-template-columns: 150px 100px;
  grid-gap: 12px;
  margin-bottom: 16px;
  padding-left: 20px;
  padding-right: 20px;
}
.mobileRow-Sj9z7O1v {
  justify-content: flex-start;
  padding-left: 20px;
}
.calendar-PM3TZruR {
  margin-left: 8px;
  margin-right: 8px;
  padding-bottom: 15px;
  padding-top: 1px;
}
.dialogWrapper-P_IVoUsZ {
  min-width: 302px;
}
.dialogWrapperSmall-P_IVoUsZ {
  max-width: 419px;
  width: 100%;
}
.tabs-P_IVoUsZ {
  --ui-lib-underline-tabs-hor-padding: 20px;
  padding: 0 var(--ui-lib-underline-tabs-hor-padding);
}
.content-P_IVoUsZ {
  overflow: auto;
  padding-top: 17px;
}
@supports (-moz-appearance: none) {
  .content-P_IVoUsZ {
    scrollbar-color: var(--themed-color-scroll-bg, #9598a1) #0000;
    scrollbar-width: thin;
  }
  html.theme-dark .content-P_IVoUsZ {
    scrollbar-color: var(--themed-color-scroll-bg, #363a45) #0000;
  }
}
.content-P_IVoUsZ::-webkit-scrollbar {
  height: 5px;
  width: 5px;
}
.content-P_IVoUsZ::-webkit-scrollbar-thumb {
  background-clip: content-box;
  background-color: var(
    --tv-color-scrollbar-thumb-background,
    var(--themed-color-scroll-bg, #9598a1)
  );
  border: 1px solid #0000;
  border-radius: 3px;
}
html.theme-dark .content-P_IVoUsZ::-webkit-scrollbar-thumb {
  background-color: var(
    --tv-color-scrollbar-thumb-background,
    var(--themed-color-scroll-bg, #363a45)
  );
}
.content-P_IVoUsZ::-webkit-scrollbar-track {
  background-color: initial;
  border-radius: 3px;
}
.content-P_IVoUsZ::-webkit-scrollbar-corner {
  display: none;
}
.contentMobile-P_IVoUsZ {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}
.bodyWrapper-P_IVoUsZ {
  flex: 0 1 201px;
}
