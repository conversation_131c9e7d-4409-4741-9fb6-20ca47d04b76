(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[7260],{3232:e=>{e.exports={item:"item-GJX1EXhk",interactive:"interactive-GJX1EXhk",hovered:"hovered-GJX1EXhk",disabled:"disabled-GJX1EXhk",active:"active-GJX1EXhk",shortcut:"shortcut-GJX1EXhk",normal:"normal-GJX1EXhk",big:"big-GJX1EXhk",iconCell:"iconCell-GJX1EXhk",icon:"icon-GJX1EXhk",checkmark:"checkmark-GJX1EXhk",content:"content-GJX1EXhk",label:"label-GJX1EXhk",checked:"checked-GJX1EXhk",toolbox:"toolbox-GJX1EXhk",showToolboxOnHover:"showToolboxOnHover-GJX1EXhk",arrowIcon:"arrowIcon-GJX1EXhk",subMenu:"subMenu-GJX1EXhk",invisibleHotkey:"invisibleHotkey-GJX1EXhk"}},36411:e=>{e.exports={item:"item-WJDah4zD",emptyIcons:"emptyIcons-WJDah4zD",loading:"loading-WJDah4zD",disabled:"disabled-WJDah4zD",interactive:"interactive-WJDah4zD",hovered:"hovered-WJDah4zD",normal:"normal-WJDah4zD",big:"big-WJDah4zD",icon:"icon-WJDah4zD",label:"label-WJDah4zD",title:"title-WJDah4zD",nested:"nested-WJDah4zD",shortcut:"shortcut-WJDah4zD",remove:"remove-WJDah4zD"}},47625:e=>{e.exports={separator:"separator-Pf4rIzEt"}},22933:e=>{e.exports={tabs:"tabs-NGf0gcnH",tab:"tab-NGf0gcnH",noBorder:"noBorder-NGf0gcnH",disabled:"disabled-NGf0gcnH",active:"active-NGf0gcnH",defaultCursor:"defaultCursor-NGf0gcnH",slider:"slider-NGf0gcnH",content:"content-NGf0gcnH"}},42142:(e,t,n)=>{"use strict";n.d(t,{FragmentMap:()=>i});var s=n(50959);function i(e){if(e.map){return s.Children.toArray(e.children).map(e.map)}return e.children}},99025:(e,t,n)=>{"use strict";n.d(t,{Hint:()=>r});var s=n(50959),i=n(97754),a=n.n(i),o=n(3232);function r(e){const{text:t="",className:n}=e;return s.createElement("span",{className:a()(o.shortcut,n)},t)}},23829:(e,t,n)=>{"use strict";n.d(t,{ContextMenuItem:()=>p});var s=n(50959),i=n(97754),a=n.n(i),o=n(9745),r=n(26996),l=n(54627),c=n(99025),h=n(39750),d=n(79978),u=n(60925),m=n(36411);function p(e){const{className:t,isTitle:n,isLoading:i,isHovered:p,active:g,checkable:b,disabled:v,checked:_,icon:C,iconChecked:f,hint:x,subItems:S,label:y,styledLabel:E,onClick:M,children:k,toolbox:R,jsxLabel:B,size:w="normal"}=e,T=(0,s.useContext)(l.EmptyIconsContext),W=!!S.length;return i?s.createElement("li",{className:a()(t,m.item,m.loading,m[w])},s.createElement(r.Loader,null)):s.createElement("li",{className:a()(t,m.item,m.interactive,n&&m.title,v&&m.disabled,p&&m.hovered,g&&m.active,T&&m.emptyIcons,m[w]),onClick:M},s.createElement(o.Icon,{className:a()(m.icon),icon:function(){if(b&&_)return f||C||h;return C}()}),s.createElement("span",{className:a()(m.label)},!B&&E?E.map((({text:e,...t},n)=>s.createElement("span",{key:n,style:t},e))):null!=B?B:y),!!R&&s.createElement(o.Icon,{onClick:function(){R&&R.action()},className:m.remove,icon:u}),!W&&x&&s.createElement(c.Hint,{className:m.shortcut,text:x}),W&&s.createElement(o.Icon,{className:m.nested,icon:d}),k)}},54627:(e,t,n)=>{"use strict";n.d(t,{EmptyIconsContext:()=>s});const s=n(50959).createContext(!1)},1109:(e,t,n)=>{"use strict";n.d(t,{Separator:()=>o});var s=n(50959),i=n(97754),a=n(47625);function o(e){
return s.createElement("div",{className:i(a.separator,e.className)})}},74483:e=>{e.exports={toolbar:"toolbar-BXXUwft2",dateRangeWrapper:"dateRangeWrapper-BXXUwft2",noranges:"noranges-BXXUwft2",dateRangeExpanded:"dateRangeExpanded-BXXUwft2",separator:"separator-BXXUwft2",seriesControlWrapper:"seriesControlWrapper-BXXUwft2",dateRangeCollapsed:"dateRangeCollapsed-BXXUwft2",item:"item-BXXUwft2",inline:"inline-BXXUwft2",dateRange:"dateRange-BXXUwft2",hidden:"hidden-BXXUwft2",collapsed:"collapsed-BXXUwft2"}},49014:e=>{e.exports={button:"button-Hfju7pW_"}},3080:e=>{e.exports={button:"button-uToIfRbZ"}},19615:e=>{e.exports={separator:"separator-yDfG9Ccu"}},88108:e=>{e.exports={headerMenuText:"headerMenuText-AcJrLng7"}},98638:e=>{e.exports={button:"button-x1dCOTP3",disabled:"disabled-x1dCOTP3",hover:"hover-x1dCOTP3",clicked:"clicked-x1dCOTP3",accessible:"accessible-x1dCOTP3"}},56905:e=>{e.exports={item:"item-SqYYy1zF"}},41745:e=>{e.exports={slider:"slider-3kCW6DWs",inner:"inner-3kCW6DWs"}},49564:e=>{e.exports={sliderRow:"sliderRow-k2h4OAz8"}},6190:(e,t,n)=>{"use strict";n.d(t,{Toolbar:()=>d});var s=n(50959),i=n(50151),a=n(47201),o=n(3343),r=n(19291),l=n(57177),c=n(39416),h=n(7047);const d=(0,s.forwardRef)((function(e,t){const{onKeyDown:n,orientation:d,blurOnEscKeydown:u=!0,blurOnClick:m=!0,...p}=e,g=(0,c.useFunctionalRefObject)(t);return(0,s.useLayoutEffect)((()=>{const e=(0,i.ensureNotNull)(g.current),t=()=>{const t=(0,r.queryTabbableElements)(e).sort(r.navigationOrderComparator);if(0===t.length){const[t]=(0,r.queryFocusableElements)(e).sort(r.navigationOrderComparator);if(void 0===t)return;(0,l.becomeMainElement)(t)}if(t.length>1){const[,...e]=t;for(const t of e)(0,l.becomeSecondaryElement)(t)}};return window.addEventListener("keyboard-navigation-activation",t),()=>window.removeEventListener("keyboard-navigation-activation",t)}),[]),s.createElement("div",{...h.MouseClickAutoBlurHandler.attributes(m),...p,role:"toolbar","aria-orientation":d,ref:g,onKeyDown:(0,a.createSafeMulticastEventHandler)((function(e){if(e.defaultPrevented)return;if(!(document.activeElement instanceof HTMLElement))return;const t=(0,o.hashFromEvent)(e);if(u&&27===t)return e.preventDefault(),void document.activeElement.blur();if("vertical"!==d&&37!==t&&39!==t)return;if("vertical"===d&&38!==t&&40!==t)return;const n=e.currentTarget,s=(0,r.queryFocusableElements)(n).sort(r.navigationOrderComparator);if(0===s.length)return;const i=s.indexOf(document.activeElement);if(-1===i)return;e.preventDefault();const a=()=>{const e=(i+s.length-1)%s.length;(0,l.becomeSecondaryElement)(s[i]),(0,l.becomeMainElement)(s[e]),s[e].focus()},c=()=>{const e=(i+s.length+1)%s.length;(0,l.becomeSecondaryElement)(s[i]),(0,l.becomeMainElement)(s[e]),s[e].focus()};switch((0,r.mapKeyCodeToDirection)(t)){case"inlinePrev":"vertical"!==d&&a();break;case"inlineNext":"vertical"!==d&&c();break;case"blockPrev":"vertical"===d&&a();break;case"blockNext":"vertical"===d&&c()}}),n)})}))},68426:(e,t,n)=>{"use strict";n.r(t),n.d(t,{BottomToolbarRenderer:()=>Ct})
;var s=n(50959),i=n(32227),a=n(11542),o=n(19036),r=n(97754),l=n.n(r),c=n(56570),h=n(88811),d=n(78135),u=n(59064),m=n(90692),p=n(10838),g=n(11684),b=n(50151),v=n(51768),_=n(77151),C=n(52033),f=n(85049);const x=e=>a.t(null,{plural:"{str} minutes",count:e,replace:{str:`${e}`}},n(5926)),S=e=>a.t(null,{plural:"{str} hours",count:e,replace:{str:`${e}`}},n(64963)),y=e=>a.t(null,{plural:"{str} months",count:e,replace:{str:`${e}`}},n(20062)),E={1:{resolution:"1",text:x(1)},3:{resolution:"3",text:x(3)},5:{resolution:"5",text:x(5)},15:{resolution:"15",text:x(15)},30:{resolution:"30",text:x(30)},45:{resolution:"45",text:x(45)},60:{resolution:"60",text:S(1)},120:{resolution:"120",text:S(2)},180:{resolution:"180",text:S(3)},240:{resolution:"240",text:S(4)},"1D":{resolution:"1D",text:(R=1,a.t(null,{plural:"{str} days",count:R,replace:{str:`${R}`}},n(62368)))},"1W":{resolution:"1W",text:(k=1,a.t(null,{plural:"{str} weeks",count:k,replace:{str:`${k}`}},n(49306)))},"1M":{resolution:"1M",text:y(1)},"3M":{resolution:"3M",text:y(3)},"6M":{resolution:"6M",text:y(6)},"12M":{resolution:"12M",text:(M=1,a.t(null,{plural:"{str} years",count:M,replace:{str:`${M}`}},n(91549)))}};var M,k,R;function B(e){const t=function(e){const t=e.value.value,s=f.Interval.parse(t);if(!s.isValid()){if("YTD"===t)return a.t(null,{context:"timeframe_title"},n(19273));if("ALL"===t)return a.t(null,{context:"timeframe_title"},n(58221));if("LASTSESSION"===t)return w(1)}if(s.isMinutes()){const e=s.multiplier();return e%60!=0?(o=e,a.t(null,{plural:"{str} minutes",count:o,replace:{str:`${o}`},context:"timeframe_title"},n(95484))):(i=e/60,a.t(null,{plural:"{str} hours",count:i,replace:{str:`${i}`},context:"timeframe_title"},n(72495)))}var i;var o;if(s.isDays())return w(s.multiplier());if(s.isWeeks())return(e=>a.t(null,{plural:"{str} weeks",count:e,replace:{str:`${e}`},context:"timeframe_title"},n(6088)))(s.multiplier());if(s.isMonths()){const e=s.multiplier();return e%12!=0?(r=e,a.t(null,{plural:"{str} months",count:r,replace:{str:`${r}`},context:"timeframe_title"},n(12752))):(e=>a.t(null,{plural:"{str} years",count:e,replace:{str:`${e}`},context:"timeframe_title"},n(96325)))(e/12)}var r;return e.description||e.text}(e),s=function(e){const t=e.targetResolution,s=f.Interval.parse(t);if(s.isMinutes()){const e=s.multiplier();return e%60!=0?(o=e,a.t(null,{plural:"{str} minutes intervals",count:o,replace:{str:`${o}`},context:"timeframe_title"},n(15489))):(i=e/60,a.t(null,{plural:"{str} hours intervals",count:i,replace:{str:`${i}`},context:"timeframe_title"},n(14887)))}var i;var o;if(s.isDays())return(e=>a.t(null,{plural:"{str} days intervals",count:e,replace:{str:`${e}`},context:"timeframe_title"},n(561)))(s.multiplier());if(s.isWeeks())return(e=>a.t(null,{plural:"{str} weeks intervals",count:e,replace:{str:`${e}`},context:"timeframe_title"},n(60316)))(s.multiplier());if(s.isMonths()){const e=s.multiplier();return e%12!=0?(r=e,a.t(null,{plural:"{str} months intervals",count:r,replace:{str:`${r}`},context:"timeframe_title"},n(48514))):(e=>a.t(null,{
plural:"{str} years intervals",count:e,replace:{str:`${e}`},context:"timeframe_title"},n(78971)))(e/12)}var r;return E[t].text}(e);return a.t(null,{replace:{timePeriod:t,timeInterval:s},context:"timeframe_title"},n(58426))}const w=e=>a.t(null,{plural:"{str} days",count:e,replace:{str:`${e}`},context:"timeframe_title"},n(63808));class T{constructor(e){this._state={ranges:[]},this._change=new C.Delegate,this._rangeChangedListenerBound=this._onRangeChanged.bind(this),this._updateAvailableRangesBound=this._updateAvailableRanges.bind(this,void 0);const{chartWidget:t}=this._context=e;t.withModel(null,(()=>{const e=t.model(),n=e.mainSeries();n.onStatusChanged().subscribe(this,this._updateAvailableRanges),c.enabled("update_timeframes_set_on_symbol_resolve")&&n.dataEvents().symbolResolved().subscribe(this,this._updateAvailableRangesBound),n.priceScale().properties().childs().lockScale.subscribe(this,this._updateAvailableRangesBound);const s=e.model().appliedTimeFrame();s.subscribe(this._rangeChangedListenerBound),this._rangeChangedListenerBound(s.value()),this._updateAvailableRanges(!0)}))}state(){return this._state}onChange(){return this._change}selectRange(e){this._setState({activeRange:e.value.value});const{chartWidgetCollection:t}=this._context,n={val:e.value,res:e.targetResolution};t.setTimeFrame(n)}destroy(){const{chartWidget:e}=this._context;e.withModel(null,(()=>{const t=e.model(),n=t.mainSeries();n.onStatusChanged().unsubscribe(this,this._updateAvailableRanges),c.enabled("update_timeframes_set_on_symbol_resolve")&&n.dataEvents().symbolResolved().unsubscribe(this,this._updateAvailableRangesBound),n.priceScale().properties().childs().lockScale.unsubscribe(this,this._updateAvailableRangesBound),t.model().appliedTimeFrame().unsubscribe(this._rangeChangedListenerBound)})),this._change.destroy()}_setState(e){this._state=Object.assign({},this._state,e),this._change.fire(this._state)}_onRangeChanged(e){let t;null!==e&&"period-back"===e.val.type&&(t=e.val.value),this._setState({activeRange:t})}_updateAvailableRanges(e){const{availableTimeFrames:t,chartWidget:n}=this._context;if(!n.hasModel())return;const s=n.model().mainSeries(),i=s.symbolInfo(),a=s.status();if(2===a||1===a||e&&!i)return;const o=t(i,a).map((e=>({...e,description:""===e.description?B(e):e.description})));this._setState({ranges:o})}}const W=(0,_.registryContextType)();function A(e){var t;return(t=class extends s.PureComponent{constructor(e,t){super(e,t),this._handleUpdate=e=>{this.setState(e)},this._handleSelectRange=e=>{var t,n;(0,v.trackEvent)("GUI","Chart Bottom Toolbar",`range ${e.value}`),null===(n=(t=this.props).onSelectRange)||void 0===n||n.call(t,e),this._binding.selectRange(e)},(0,_.validateRegistry)(t,{availableTimeFrames:o.any.isRequired,chartWidgetCollection:o.any.isRequired,chartWidget:o.any.isRequired}),D.has(t.chartWidget)||D.set(t.chartWidget,new T(t));const n=this._binding=(0,b.ensureDefined)(D.get(t.chartWidget));this.state=n.state()}componentDidMount(){this._binding.onChange().subscribe(this,this._handleUpdate)}componentWillUnmount(){
this._binding.onChange().unsubscribe(this,this._handleUpdate)}render(){return s.createElement(e,{goToDateButton:this.props.goToDateButton,className:this.props.className,ranges:this.state.ranges,activeRange:this.state.activeRange,onSelectRange:this._handleSelectRange})}}).contextType=W,t}const D=new WeakMap;var N=n(62400),z=n(23829),F=n(1109),P=n(34585),H=n(90752),L=n(49014);function I(e){const{ranges:t,activeRange:n,onSelectRange:i}=e;return s.createElement(s.Fragment,null,t.map((e=>s.createElement(z.ContextMenuItem,{key:e.value.value,label:e.description||e.text,active:n===e.value.value,checked:n===e.value.value,checkable:!0,disabled:!1,onClick:a.bind(null,e),doNotCloseOnClick:!1,subItems:[]}))));function a(e){e&&i&&i(e),(0,u.globalCloseMenu)()}}function X(e){const{onGoToDateClick:t}=e;return s.createElement(s.Fragment,null,s.createElement(F.Separator,{className:L.separator}),s.createElement(z.ContextMenuItem,{icon:H,label:(0,P.appendEllipsis)(a.t(null,void 0,n(42432))),onClick:t,active:!1,checked:!1,checkable:!1,disabled:!1,doNotCloseOnClick:!1,subItems:[]}))}const O={title:a.t(null,void 0,n(85444)),goToDate:(0,P.appendEllipsis)(a.t(null,void 0,n(42432)))},G=(0,_.registryContextType)();class j extends s.PureComponent{constructor(e,t){super(e,t),this._handleGoToDateClick=()=>{const{chartWidget:e}=this.context;(0,N.showGoToDateDialog)(e),(0,u.globalCloseMenu)()},this._handleRangeSelect=e=>{e&&this.props.onSelectRange&&this.props.onSelectRange(e),(0,u.globalCloseMenu)()},this._renderChildren=e=>{const{ranges:t,activeRange:n,goToDateButton:i}=this.props;return e?s.createElement(s.Fragment,null,s.createElement(I,{ranges:t,activeRange:n,onSelectRange:this._handleRangeSelect}),i&&s.createElement(X,{onGoToDateClick:this._handleGoToDateClick})):s.createElement(s.Fragment,null,t.map((e=>s.createElement(p.AccessibleMenuItem,{key:e.value.value,label:e.description||e.text,isActive:n===e.value.value,onClick:this._handleRangeSelect,onClickArg:e}))),i&&t.length>0&&s.createElement(g.PopupMenuSeparator,null),i&&s.createElement(p.AccessibleMenuItem,{label:O.goToDate,onClick:this._handleGoToDateClick}))},(0,_.validateRegistry)(t,{chartWidget:o.any.isRequired})}render(){return s.createElement(m.MatchMedia,{rule:"screen and (max-width: 430px)"},(e=>s.createElement(h.ToolbarMenuButton,{className:l()(L.button,this.props.className),content:O.title,arrow:!0,verticalAttachEdge:d.VerticalAttachEdge.Top,verticalDropDirection:d.VerticalDropDirection.FromBottomToTop,horizontalMargin:4,"data-name":"date-ranges-menu",isDrawer:e,onClick:this._trackClick},this._renderChildren(e))))}_trackClick(){0}}j.contextType=G;const U=A(j);var J=n(22933);const V=J;function q(e){return class extends s.PureComponent{constructor(){super(...arguments),this.activeTab={current:null}}componentDidUpdate(){(0,b.ensureNotNull)(this._slider).style.transition="transform 350ms",this._componentDidUpdate()}componentDidMount(){this._componentDidUpdate()}render(){const{className:t}=this.props,n=this._generateTabs();return s.createElement("div",{className:r(t,J.tabs),
"data-name":this.props["data-name"]},n,s.createElement(e,{reference:e=>{this._slider=e}}))}_generateTabs(){return this.activeTab.current=null,s.Children.map(this.props.children,(e=>{const t=e,n=Boolean(t.props.isActive),i={reference:e=>{n&&(this.activeTab.current=e),t.props.reference&&t.props.reference(e)}};return s.cloneElement(t,i)}))}_componentDidUpdate(){const e=(0,b.ensureNotNull)(this._slider).style;if(this.activeTab.current){const t=this.activeTab.current.offsetWidth,n=this.activeTab.current.offsetLeft;e.transform=`translateX(${n}px)`,e.width=`${t}px`,e.opacity="1"}else e.opacity="0"}}}q((function(e){return s.createElement("div",{className:J.slider,ref:e.reference})}));var $=n(40173),Z=n(20792),K=n(56905);(0,$.mergeThemes)(Z.DEFAULT_TOOLBAR_BUTTON_THEME,K);function Y(e){const{reference:t,text:n,tooltip:i,isActive:a,className:o,onClick:l,theme:c=K,...h}=e,d=r(o,c.item,{[c.isActive]:a});return s.createElement(Z.ToolbarButton,{...h,ref:t,text:n,isActive:a,tooltip:i,className:d,onClick:l})}var Q=n(41745);const ee=(0,$.mergeThemes)(V,Q);var te=n(49564);const ne=q((function(e){return s.createElement("div",{className:r(e.className,ee.slider),ref:e.reference},s.createElement("div",{className:ee.inner}))}));const se=A((function(e){const{className:t,ranges:n,activeRange:i,onSelectRange:a}=e;return s.createElement(ne,{className:r(te.sliderRow,t),"data-name":"date-ranges-tabs"},n.map((e=>s.createElement(Y,{key:e.value.value,value:e.value.value,"data-name":`date-range-tab-${e.value.value}`,isActive:i===e.value.value,onClick:a&&a.bind(null,e),text:e.text,tooltip:e.description||e.text}))))}));var ie=n(61814),ae=n(68335),oe=n(98945),re=n(92574),le=n(3080);const ce=(0,ie.hotKeySerialize)({keys:[(0,ae.humanReadableModifiers)(ae.Modifiers.Alt,!1),"G"],text:"{0} + {1}"}),he=(0,_.registryContextType)();class de extends s.PureComponent{constructor(e,t){super(e,t),this._handleClick=()=>{const{chartWidget:e}=this.context;(0,v.trackEvent)("GUI","Chart Bottom Toolbar","go to"),(0,N.showGoToDateDialog)(e)},(0,_.validateRegistry)(t,{chartWidget:o.any.isRequired})}render(){const{className:e}=this.props;return s.createElement(oe.ToolbarIconButton,{icon:re,onClick:this._handleClick,"data-tooltip-hotkey":ce,tooltip:a.t(null,void 0,n(42432)),"data-name":"go-to-date",className:r(le.button,e)})}}de.contextType=he;const ue=A(de);var me=n(76460),pe=n(82112),ge=n(15344),be=n(97702),ve=n(2740),_e=n(63146),Ce=n(11497),fe=n(40443),xe=n(98638);const Se=(0,$.mergeThemes)(Z.DEFAULT_TOOLBAR_BUTTON_THEME,{isDisabled:xe.disabled,button:xe.button});const ye=(0,_.registryContextType)();class Ee extends s.PureComponent{constructor(e,t){super(e,t),this._timeFormatter=new ge.TimeFormatter((0,_e.getHourMinuteSecondFormat)(Ce.timeHoursFormatProperty.value())),this._tickInterval=void 0,this._element=null,this._menuShown=!1,this._preventShowingMenu=!1,this._tickClock=()=>{const{chartApiInstance:e}=this.context;if(void 0!==this._timezone){const t=(0,ve.utc_to_cal)(this._timezone,e.serverTime());this.setState({time:this._timeFormatter.format(t)})}},
this._getActions=()=>{if(!this.props.withMenu)return[];const{chartWidget:e}=this.context;return function(e){e.updateActions();const t=e.actions();return t&&t.applyTimeZone instanceof be.Action?t.applyTimeZone.getSubItems():[]}(e)},this._handleRef=e=>{this._element=e},this._onMouseDown=()=>{this._preventShowingMenu=this._menuShown},this._showMenu=e=>{if(this._preventShowingMenu)return void fe.ContextMenuManager.hideAll();const t=(0,b.ensureNotNull)(this._element),n=this._getActions();if(0===n.length)return;const s=t.getBoundingClientRect();fe.ContextMenuManager.showMenu(n,{clientX:s.left,clientY:s.top,attachToYBy:"bottom"},{returnFocus:!0,takeFocus:!0,isKeyboardEvent:(0,me.isKeyboardClick)(e)},{menuName:"TimezoneMenuContextMenu"},(()=>{this._menuShown=!1})).then((()=>{this._menuShown=!0}))},(0,_.validateRegistry)(t,{chartWidget:o.any.isRequired,chartApiInstance:o.any.isRequired}),this.state={time:""}}componentDidMount(){const{chartWidget:e}=this.context;this._tickInterval=setInterval(this._tickClock,1e3),e.withModel(null,(()=>{const t=e.model();t.model().mainSeries().dataEvents().symbolResolved().subscribe(this,this.updateTimezonesButton),t.model().properties().childs().timezone.subscribe(this,this.updateTimezonesButton),Ce.timeHoursFormatProperty.subscribe(this,this._timeHoursFormatPropertyChanged)}))}componentWillUnmount(){const{chartWidget:e}=this.context;clearInterval(this._tickInterval),e.withModel(null,(()=>{const t=e.model();t.model().mainSeries().dataEvents().symbolResolved().unsubscribe(this,this.updateTimezonesButton),t.model().properties().childs().timezone.unsubscribe(this,this.updateTimezonesButton),Ce.timeHoursFormatProperty.unsubscribe(this,this._timeHoursFormatPropertyChanged)}))}render(){const{className:e,withMenu:t}=this.props,{time:i}=this.state,o=void 0!==this._timezone?(0,pe.parseTzOffset)(this._timezone.name()).string:null;return s.createElement(Z.ToolbarButton,{onMouseDown:this._onMouseDown,ref:this._handleRef,onClick:this._showMenu,isDisabled:!t,theme:Se,"data-name":"time-zone-menu",tooltip:t?a.t(null,void 0,n(77073)):void 0,className:e,text:i&&o&&`${i} (${o})`})}updateTimezonesButton(){const{chartWidget:e}=this.context;if(!e.hasModel())return;if(null===e.model().mainSeries().symbolInfo())return;const t=(0,b.ensureNotNull)(e.model().model().timezoneExceptExchange().value());this._timezone=(0,ve.get_timezone)(t),this._tickClock()}_timeHoursFormatPropertyChanged(){this._timeFormatter=new ge.TimeFormatter((0,_e.getHourMinuteSecondFormat)(Ce.timeHoursFormatProperty.value())),this.updateTimezonesButton()}}Ee.contextType=ye;var Me=n(19615);function ke(e){return s.createElement("span",{className:r(Me.separator,e.className)})}var Re=n(6190),Be=n(45126),we=n(49483);class Te{constructor(e,t,n){this._highlighted=!1,this._chartWidget=e,this._priceScaleGetter=t,this._owner=n,this._setHighlight=this._setHighlight.bind(this),this._removeHighlight=this._removeHighlight.bind(this)}destroy(){this._highlighted&&this._removeHighlight()}handlers(){const e=we.CheckMobile.any();return{
onMouseEnter:e?void 0:this._setHighlight,onMouseLeave:e?void 0:this._removeHighlight}}_setHighlight(){if(!this._chartWidget.hasModel())return;const e=this._chartWidget.model().model(),t=e.paneForSource(e.mainSeries()),n=this._priceScaleGetter();if(null===t||null===n)return;const s=this._chartWidget.paneByState(t);if(null!==s){const t=s.rightPriceAxisesContainer().findAxisWidgetForScale(n);let i=null;null!==t&&(i=t.axisInfo());const a=s.leftPriceAxisesContainer().findAxisWidgetForScale(n);null!==a&&(i=a.axisInfo());const o=s.highlightedPriceAxis();null!==i&&o.value().axis!==i&&(o.setValue({owner:this._owner,axis:i}),e.lightUpdate(),this._highlighted=!0)}}_removeHighlight(){if(!this._chartWidget.hasModel())return;const e=this._chartWidget.model().model(),t=e.paneForSource(e.mainSeries());if(null===t)return;const n=this._chartWidget.paneByState(t);if(null!==n){const t=n.highlightedPriceAxis(),s=t.value();null!==s.axis&&s.owner===this._owner&&(t.setValue({owner:this._owner,axis:null}),e.lightUpdate(),this._highlighted=!1)}}}const We=(0,_.registryContextType)(),Ae=new Be.TranslatedString("toggle log scale",a.t(null,void 0,n(49403)));const De=(0,_.registryContextType)(),Ne=new Be.TranslatedString("toggle auto scale",a.t(null,void 0,n(42240)));const ze=(0,_.registryContextType)(),Fe=new Be.TranslatedString("toggle percentage scale",a.t(null,void 0,n(98994)));const Pe=(0,_.registryContextType)();var He=n(42142),Le=n(14729),Ie=n(16829),Xe=n(95700),Oe=n(88108);const Ge=new Be.TranslatedString("change session",a.t(null,void 0,n(87041))),je={hint:a.t(null,void 0,n(94031)),headerMenuText:a.t(null,void 0,n(27665))},Ue=(0,_.registryContextType)();class Je extends s.PureComponent{constructor(e,t){super(e,t),(0,_.validateRegistry)(t,{chartWidget:o.any.isRequired,chartApiInstance:o.any.isRequired}),this.state={availableSessions:[]}}componentDidMount(){const{chartWidget:e}=this.context;e.withModel(null,(()=>{const t=e.model();t.model().mainSeries().dataEvents().symbolResolved().subscribe(this,this.updateSessionButton),t.model().mainSeries().properties().childs().sessionId.subscribe(this,this.updateSessionButton),this.updateSessionButton()}))}componentWillUnmount(){const{chartWidget:e}=this.context;e.withModel(null,(()=>{const t=e.model();t.model().mainSeries().dataEvents().symbolResolved().unsubscribe(this,this.updateSessionButton),t.model().mainSeries().properties().childs().sessionId.unsubscribe(this,this.updateSessionButton)}))}render(){const{className:e,withMenu:t}=this.props,{sessionName:n,sessionDescription:i}=this.state;return s.createElement(h.ToolbarMenuButton,{arrow:!1,isDisabled:!t,content:n,className:e,closeOnClickOutside:!0,tooltip:t?i:void 0,"data-name":"session-menu",verticalDropDirection:d.VerticalDropDirection.FromBottomToTop,verticalAttachEdge:d.VerticalAttachEdge.Top,onClick:this._trackClick},this._menuItems())}updateSessionButton(){var e,t;const{chartWidget:n}=this.context;if(!n.model())return;const s=n.model().mainSeries().symbolInfo();if(null===s)return
;const i=s.subsession_id,a=null!==(t=null===(e=s.subsessions)||void 0===e?void 0:e.filter((e=>!e.private)))&&void 0!==t?t:[],o=a.find((e=>e.id===i));this.setState({sessionId:i,sessionName:(0,Xe.translateSessionShortDescription)((null==o?void 0:o.description)||""),sessionDescription:(0,Xe.translateSessionDescription)((null==o?void 0:o.description)||""),availableSessions:a})}_menuItems(){if(!this.props.withMenu)return[];const{chartWidget:e}=this.context,{availableSessions:t}=this.state;if(!e.model())return[];const n=e.model().mainSeries(),i=[s.createElement(Ie.ToolWidgetMenuSummary,{key:"header_menu_text",className:Oe.headerMenuText},je.headerMenuText.toUpperCase())];for(const a of t){const t={category:"SetSession",event:a.id},o=()=>{e.model().setProperty(n.properties().childs().sessionId,a.id,Ge)};i.push(s.createElement(p.AccessibleMenuItem,{key:a.id,label:(0,Xe.translateSessionDescription)(a.description),isActive:this.state.sessionId===a.id,trackEventObject:t,onClick:o}))}return i}_trackClick(){0}}Je.contextType=Ue;var Ve,qe=n(21868),$e=n(72026),Ze=n(51267),Ke=n(74483);!function(e){e[e.MinSpace=0]="MinSpace"}(Ve||(Ve={}));const Ye={extLabel:a.t(null,void 0,n(8586)),extHint:a.t(null,void 0,n(92966)),percentageHint:a.t(null,void 0,n(81649)),logLabel:a.t(null,{context:"scale"},n(4161)),logHint:a.t(null,void 0,n(1e4)),autoLabel:a.t(null,{context:"scale"},n(22233)),autoHint:a.t(null,void 0,n(41888)),maximizeChartHint:a.t(null,void 0,n(61206)),restoreChartHint:a.t(null,void 0,n(31142)),adjLabel:a.t(null,{context:"adjustments"},n(94920)),adjHint:a.t(null,void 0,n(93020)),adjForDividendsOnlyHint:a.t(null,void 0,n(2031)),adjForSplitsOnlyHint:a.t(null,void 0,n(95739)),backAdjustLabel:a.t(null,{context:"adjustments"},n(16755)),backAdjustHint:a.t(null,void 0,n(68921)),settlementAsCloseLabel:a.t(null,{context:"adjustments"},n(82631)),settlementAsCloseHint:a.t(null,void 0,n(49545))};var Qe,et;!function(e){e.Separator="separator",e.TimeZones="timeZones",e.SessionId="session",e.Percentage="percentage",e.Logarithm="logarithm",e.Auto="auto",e.Fullscreen="fullscreen",e.Adj="adj",e.BackAdj="backAdj",e.SettlementAsClose="settlementAsClose",e.PreventPhoneLayout="preventPhoneLayout"}(Qe||(Qe={})),function(e){e.Expanded="expanded",e.Collapsed="collapsed",e.Hidden="hidden"}(et||(et={}));const tt=(nt=e=>s.createElement(Z.ToolbarButton,{text:Ye.logLabel,tooltip:Ye.logHint,className:e.className,isActive:e.isLogarithm,"aria-pressed":e.isLogarithm,onClick:ut(e.onClick,"log",e.isLogarithm),onMouseEnter:e.onMouseEnter,onMouseLeave:e.onMouseLeave,"data-name":"logarithm"}),(st=class extends s.PureComponent{constructor(e,t){super(e,t),this._priceScale=null,this._handleSelect=()=>{const e=this.context.chartWidget.model(),t=(0,b.ensureNotNull)(this.state.series),n=t.priceScale(),s=n.mode();t.priceScale().isLockScale()||e.setPriceScaleMode({log:!s.log},n,Ae)},(0,_.validateRegistry)(t,{chartWidget:o.any.isRequired}),this.state={isActive:!1,series:null},this._priceAxisHighlighter=new Te(this.context.chartWidget,(()=>this._priceScale),"logarithm")}
componentDidMount(){const e=this.context.chartWidget;e.withModel(null,(()=>{const t=e.model().mainSeries(),n=t.priceScale();this._handleMainSeriesPriceScaleChanged(n),t.priceScaleChanged().subscribe(this,this._handleMainSeriesPriceScaleChanged),this._handleModeChanged({},n.mode()),this.setState({isActive:t.priceScale().isLog(),series:t})}))}componentWillUnmount(){const e=this.context.chartWidget;e.withModel(null,(()=>{e.model().mainSeries().priceScaleChanged().unsubscribe(this,this._handleMainSeriesPriceScaleChanged)})),null!==this._priceScale&&(this._priceScale.modeChanged().unsubscribeAll(this),this._priceScale=null),this._priceAxisHighlighter.destroy()}render(){const{className:e}=this.props,{isActive:t,series:n}=this.state;return s.createElement(nt,{...this._priceAxisHighlighter.handlers(),className:e,isLogarithm:t,isDisabled:null===n,onClick:this._handleSelect})}_handleMainSeriesPriceScaleChanged(e){null!==this._priceScale&&this._priceScale.modeChanged().unsubscribe(this,this._handleModeChanged),this._priceScale=e,this._priceScale.modeChanged().subscribe(this,this._handleModeChanged),this._handleModeChanged({},e.mode())}_handleModeChanged(e,t){Boolean(t.log)!==this.state.isActive&&this.setState({isActive:Boolean(t.log)})}}).contextType=We,st);var nt,st;const it=function(e){var t;return(t=class extends s.PureComponent{constructor(e,t){super(e,t),this._priceScale=null,this._handleSelect=()=>{const e=this.context.chartWidget.model(),t=(0,b.ensureNotNull)(this.state.series).priceScale(),n=t.mode();e.setPriceScaleMode({autoScale:!n.autoScale},t,Ne)},(0,_.validateRegistry)(t,{chartWidget:o.any.isRequired}),this.state={isActive:!1,series:null},this._priceAxisHighlighter=new Te(this.context.chartWidget,(()=>this._priceScale),"auto")}componentDidMount(){const e=this.context.chartWidget;e.withModel(null,(()=>{const t=e.model().mainSeries(),n=t.priceScale();this._handleMainSeriesPriceScaleChanged(n),t.priceScaleChanged().subscribe(this,this._handleMainSeriesPriceScaleChanged),this._handleModeChanged({},n.mode()),this.setState({isActive:t.priceScale().isAutoScale(),series:t})}))}componentWillUnmount(){const e=this.context.chartWidget;e.withModel(null,(()=>{e.model().mainSeries().priceScaleChanged().unsubscribe(this,this._handleMainSeriesPriceScaleChanged)})),null!==this._priceScale&&(this._priceScale.modeChanged().unsubscribeAll(this),this._priceScale=null),this._priceAxisHighlighter.destroy()}render(){const{className:t}=this.props,{isActive:n,series:i}=this.state;return s.createElement(e,{...this._priceAxisHighlighter.handlers(),className:t,isAuto:n,isDisabled:null===i,onClick:this._handleSelect})}_handleMainSeriesPriceScaleChanged(e){null!==this._priceScale&&this._priceScale.modeChanged().unsubscribe(this,this._handleModeChanged),this._priceScale=e,this._priceScale.modeChanged().subscribe(this,this._handleModeChanged),this._handleModeChanged({},e.mode())}_handleModeChanged(e,t){Boolean(t.autoScale)!==this.state.isActive&&this.setState({isActive:Boolean(t.autoScale)})}}).contextType=De,t
}((e=>s.createElement(Z.ToolbarButton,{text:Ye.autoLabel,tooltip:Ye.autoHint,className:e.className,isActive:e.isAuto,"aria-pressed":e.isAuto,onClick:ut(e.onClick,"auto",e.isAuto),onMouseEnter:e.onMouseEnter,onMouseLeave:e.onMouseLeave,"data-name":"auto"}))),at=function(e){var t;return(t=class extends s.PureComponent{constructor(e,t){super(e,t),this._priceScale=null,this._handleSelect=()=>{const e=this.context.chartWidget.model(),t=(0,b.ensureNotNull)(this.state.series),n=t.priceScale(),s=n.mode();t.priceScale().isLockScale()||e.setPriceScaleMode({percentage:!s.percentage},n,Fe)},(0,_.validateRegistry)(t,{chartWidget:o.any.isRequired}),this.state={isActive:!1,series:null},this._priceAxisHighlighter=new Te(this.context.chartWidget,(()=>this._priceScale),"percentage")}componentDidMount(){const e=this.context.chartWidget;e.withModel(null,(()=>{const t=e.model().mainSeries(),n=t.priceScale();this._handleMainSeriesPriceScaleChanged(n),t.priceScaleChanged().subscribe(this,this._handleMainSeriesPriceScaleChanged),this._handleScaleChange({},n.mode()),this.setState({isActive:t.priceScale().isPercentage(),series:t})}))}componentWillUnmount(){const e=this.context.chartWidget;e.withModel(null,(()=>{e.model().mainSeries().priceScaleChanged().unsubscribe(this,this._handleMainSeriesPriceScaleChanged)})),null!==this._priceScale&&(this._priceScale.modeChanged().unsubscribeAll(this),this._priceScale=null),this._priceAxisHighlighter.destroy()}render(){const{className:t}=this.props,{isActive:n,series:i}=this.state;return s.createElement(e,{...this._priceAxisHighlighter.handlers(),className:t,isPercentage:n,isDisabled:null===i,onClick:this._handleSelect})}_handleMainSeriesPriceScaleChanged(e){null!==this._priceScale&&this._priceScale.modeChanged().unsubscribe(this,this._handleScaleChange),this._priceScale=e,this._priceScale.modeChanged().subscribe(this,this._handleScaleChange),this._handleScaleChange({},e.mode())}_handleScaleChange(e,t){Boolean(t.percentage)!==this.state.isActive&&this.setState({isActive:Boolean(t.percentage)})}}).contextType=ze,t}((e=>s.createElement(Z.ToolbarButton,{icon:qe,tooltip:Ye.percentageHint,className:e.className,isActive:e.isPercentage,"aria-pressed":e.isPercentage,isDisabled:e.isDisabled,onClick:ut(e.onClick,"percent",e.isPercentage),onMouseEnter:e.onMouseEnter,onMouseLeave:e.onMouseLeave,"data-name":"percentage"})));const ot=(0,ie.hotKeySerialize)({keys:[(0,ae.humanReadableModifiers)(ae.Modifiers.Alt,!1),"Enter"],text:"{0} + {1}"}),rt=function(e){var t;return(t=class extends s.PureComponent{constructor(e,t){super(e,t),this._handleClick=e=>{const{resizerDetacher:t,chartWidgetCollection:n}=this.context;e.shiftKey&&t.detachable.value()?t.detach():this.state.isFullscreen?t.exitFullscreen():t.requestFullscreen()},this._handleLayoutChange=e=>{this.setState({isFullscreen:e})},this._handlePhoneSize=()=>{0},(0,_.validateRegistry)(t,{chartWidgetCollection:o.any.isRequired,resizerDetacher:o.any.isRequired});const{resizerDetacher:n}=t;this.state={isFullscreen:n.fullscreen.value(),
isChangeLayoutButton:this._isChangeLayoutButton()}}componentDidMount(){const{resizerDetacher:e,chartWidgetCollection:t}=this.context,{mobileChangeLayoutEnabled:n}=this.props;e.fullscreen.subscribe(this._handleLayoutChange)}componentWillUnmount(){const{resizerDetacher:e,chartWidgetCollection:t}=this.context,{mobileChangeLayoutEnabled:n}=this.props;e.fullscreen.unsubscribe(this._handleLayoutChange)}render(){const{className:t}=this.props,{isFullscreen:n,isChangeLayoutButton:i}=this.state;return s.createElement(e,{className:t,isFullscreen:n,onClick:this._handleClick})}_isChangeLayoutButton(){return!1}}).contextType=Pe,t}((e=>s.createElement(Z.ToolbarButton,{icon:e.isFullscreen?Ze:$e,tooltip:e.isFullscreen?Ye.restoreChartHint:Ye.maximizeChartHint,className:e.className,isActive:e.isFullscreen,onClick:ut(e.onClick,"maximize chart",e.isFullscreen),"data-tooltip-hotkey":ot,"data-name":"fullscreen"}))),lt={fullscreen:!0,preventPhoneLayout:!0},ct={fullscreen:Number.MIN_SAFE_INTEGER,preventPhoneLayout:Number.MIN_SAFE_INTEGER,separator:-2,timeZones:-1,auto:0,logarithm:1,percentage:2,session:3,adj:4,backAdj:5,settlementAsClose:6},ht=(()=>{const e=new Map;return e.set(tt,"logarithm"),e.set(at,"percentage"),e.set(it,"auto"),e.set(Je,"session"),e.set(rt,"fullscreen"),e})();function dt(e){0}function ut(e,t,n){return t=>{e(t)}}const mt={dateRangeMode:"hidden",noRanges:!1,separator:!0,timeZones:!0,fullscreen:!0,preventPhoneLayout:!0,auto:!0,logarithm:!0,percentage:!0,session:!0,adj:!0,backAdj:!0,settlementAsClose:!0},pt=(0,_.registryContextType)();class gt extends s.PureComponent{constructor(e,t){var n,a;super(e,t),this._timezoneButtonRef=null,this._layout=Object.assign({},mt),this._raf=null,this._toolbar=null,this._rangeExpanded=null,this._rangeCollapsed=null,this._seriesComponents={},this._resizeObserver=null,this._injector=(n=()=>this._layout,a=(e,t)=>this._seriesComponents[t]=e,(e,t,i)=>{if(s.isValidElement(e)&&"string"!=typeof e.type){const{props:t}=e;if("string"==typeof t.className){const i={className:t.className},o=n(),l=(0,b.ensureDefined)(ht.get(e.type));return s.createElement("div",{key:null===e.key?void 0:e.key,className:r(Ke.inline,o[l]&&Ke.collapsed),ref:e=>a(e,l),onClick:()=>dt()},s.cloneElement(e,i))}}return e}),this._onSymbolSourceCollectionChanged=()=>{this._updateButtonsVisibilityImp(!0)},this._updateButtonsVisibility=()=>{this._updateButtonsVisibilityImp()},this._updateButtonsVisibilityImp=e=>{const{chartWidget:t}=this.context,n=t.model().model(),s=n.mainSeries(),i=s.symbolInfo(),a=!s.isDWMProperty().value();if(s.symbolResolvingActive().value())return void this._setStateWithResize({intervalAllowsSessionButton:a},e);const o=((null==i?void 0:i.subsessions)||[]).filter((e=>!e.private)).length>1;this._setStateWithResize({intervalAllowsSessionButton:a,symbolAllowsSessionButton:o},e)},this._resizeByRaf=()=>{null===this._raf&&(this._raf=requestAnimationFrame((()=>{this._resizeHandler(),this._raf=null})))},this._resizeHandler=()=>{const e=this._layout,t=(0,b.ensureNotNull)(this._toolbar),n=(0,
b.ensureNotNull)(this._rangeExpanded),s=(a=function(e){const t={};return Object.keys(e).forEach((n=>{const s=e[n];if(null!==s){const e=i.findDOMNode(s);null!==e&&(t[n]=e)}})),t}(this._seriesComponents),Object.keys(a).map((e=>({name:e,width:a[e].offsetWidth}))).sort(((e,t)=>ct[e.name]-ct[t.name])));var a;const o=t.offsetWidth,r=s.reduce(((e,t)=>e+t.width),0),l=n.offsetWidth,c=!Boolean(n.textContent),h=o-r-l<=0?"collapsed":"expanded";if(Object.assign(e,{dateRangeMode:h,noRanges:c}),"expanded"!==h){const t=o-(0,b.ensureNotNull)(this._rangeCollapsed).offsetWidth-0;let n=0,i=0;for(const a of s)n+=a.width,a.name in lt?(i+=a.width,Object.assign(e,{[a.name]:!1})):Object.assign(e,{[a.name]:t<=n});t<=i&&Object.assign(e,{dateRangeMode:"hidden"})}else Object.assign(e,{separator:!1,timeZones:!1,fullscreen:!1,preventPhoneLayout:!1,auto:!1,logarithm:!1,percentage:!1,session:!1,adj:!1,settlementAsClose:!1,backAdj:!1});this._applyResizing()},this._handleTimezoneButtonRef=e=>{this._timezoneButtonRef=e},this._handleMeasure=()=>{null!==this._toolbar&&this.resizeUI()},this._handleFullscreenableChange=e=>{this._setStateWithResize({isFullscreenable:e})},this._handlePreventPhoneLayoutButtonVisibility=()=>{0},this._handleToolbarRef=e=>this._toolbar=e,this._handleRangeCollapsedRef=e=>this._rangeCollapsed=e,this._handleRangeExpandedRef=e=>{this._updateResizeObserver(this._rangeExpanded,e),this._rangeExpanded=e},this._handleTimeZonesRef=e=>{this._updateResizeObserver(this._seriesComponents.timeZones,e),this._seriesComponents.timeZones=e},this._handleSessionsRef=e=>{this._updateResizeObserver(this._seriesComponents.session,e),this._seriesComponents.session=e},this._handleSeparatorRef=e=>{this._seriesComponents.separator=e},this._updateResizeObserver=(e,t)=>{this._resizeObserver&&e!==t&&(e&&this._resizeObserver.unobserve(e),t&&this._resizeObserver.observe(t))},(0,_.validateRegistry)(t,{onContentBoxChanged:o.any.isRequired,chartApiInstance:o.any.isRequired,chartWidget:o.any.isRequired,chartWidgetCollection:o.any.isRequired,resizerDetacher:o.any.isRequired});const{resizerDetacher:l}=this.context;this.state={isFullscreenable:l.fullscreenable.value(),isPreventPhoneLayoutButton:this._isPreventPhoneLayoutButton()},this._resizeObserver=new ResizeObserver(this._handleMeasure)}componentDidMount(){const{onContentBoxChanged:e,resizerDetacher:t,chartWidgetCollection:n,chartWidget:s}=this.context;e.subscribe(this,this._resizeByRaf),t.fullscreenable.subscribe(this._handleFullscreenableChange),s.withModel(null,(()=>{const e=s.model(),t=e.model();e.mainSeries().isDWMProperty().subscribe(this,this._updateButtonsVisibility),t.symbolSourceResolvingActive().subscribe(this._updateButtonsVisibility),t.symbolSourceCollectionChanged().subscribe(this,this._onSymbolSourceCollectionChanged),this._updateButtonsVisibility()})),this.updateTimezonesButton(),this.resizeUI()}componentWillUnmount(){var e;const{onContentBoxChanged:t,resizerDetacher:n,chartWidgetCollection:s,chartWidget:i}=this.context;t.unsubscribe(this,this._resizeByRaf),
n.fullscreenable.unsubscribe(this._handleFullscreenableChange),null===(e=this._resizeObserver)||void 0===e||e.disconnect(),i.withModel(null,(()=>{const e=i.model(),t=e.model();e.mainSeries().isDWMProperty().unsubscribe(this,this._updateButtonsVisibility),e.mainSeries().isBackAdjustmentForbiddenProperty().unsubscribe(this,this._updateButtonsVisibility),e.mainSeries().isSettlementAsCloseForbiddenProperty().unsubscribe(this,this._updateButtonsVisibility),t.symbolSourceCollectionChanged().unsubscribe(this,this._onSymbolSourceCollectionChanged),t.symbolSourceResolvingActive().unsubscribe(this._updateButtonsVisibility)})),null!==this._raf&&(cancelAnimationFrame(this._raf),this._raf=null)}render(){const e=this._layout,{timeFramesWidgetEnabled:t,timeWidgetEnabled:n,percentageScaleButtonEnabled:i,logScaleButtonEnabled:a,autoScaleButtonEnabled:o,fullscreenButtonEnabled:l}=this.props;return s.createElement(Re.Toolbar,{className:Ke.toolbar,onContextMenu:Le.preventDefault,ref:this._handleToolbarRef},t&&s.createElement(He.FragmentMap,null,s.createElement("div",{className:r(Ke.dateRangeWrapper,"collapsed"!==e.dateRangeMode&&Ke.collapsed),ref:this._handleRangeCollapsedRef},s.createElement("div",{className:r(Ke.dateRangeCollapsed)},s.createElement(U,{goToDateButton:this.props.goToDateEnabled,className:Ke.dateRange}))),s.createElement("div",{className:r(Ke.dateRangeWrapper,"expanded"!==e.dateRangeMode&&Ke.collapsed,e.noRanges&&Ke.noranges),ref:this._handleRangeExpandedRef},s.createElement("div",{className:r(Ke.dateRangeExpanded)},s.createElement(se,{onSelectRange:this._trackRangeButtonClick,className:Ke.dateRange}),this.props.goToDateEnabled&&s.createElement(ke,{className:r(Ke.separator)}),this.props.goToDateEnabled&&s.createElement(ue,null)))),s.createElement("div",{className:Ke.seriesControlWrapper},n&&s.createElement("div",{className:r(Ke.inline,e.timeZones&&Ke.collapsed),ref:this._handleTimeZonesRef},s.createElement("div",{className:Ke.inline,onClick:this._trackTimezonesButtonClick},s.createElement(Ee,{className:Ke.item,withMenu:this.props.timezoneMenuEnabled,ref:this._handleTimezoneButtonRef}))),this.props.sessionIdButtonEnabled&&this.state.symbolAllowsSessionButton&&this.state.intervalAllowsSessionButton&&s.createElement("div",{className:r(Ke.inline,e.session&&Ke.collapsed),ref:this._handleSessionsRef},s.createElement("div",{className:Ke.inline},s.createElement(Je,{className:Ke.item,withMenu:this.props.sessionIdButtonEnabled}))),s.createElement("div",{ref:this._handleSeparatorRef,className:r(Ke.inline,e.separator&&Ke.collapsed)},s.createElement(ke,null)),s.createElement(He.FragmentMap,{map:this._injector},!1,!1,!1,i&&!c.enabled("fundamental_widget")&&s.createElement(at,{className:Ke.item}),a&&s.createElement(tt,{className:Ke.item}),o&&s.createElement(it,{className:Ke.item}),l&&this.state.isFullscreenable&&s.createElement(rt,{className:Ke.item,mobileChangeLayoutEnabled:this.props.mobileChangeLayoutEnabled}),!1)))}updateTimezonesButton(){null!==this._timezoneButtonRef&&this._timezoneButtonRef.updateTimezonesButton()}resizeUI(){
this._resizeByRaf()}_trackRangeButtonClick(e){0}_trackTimezonesButtonClick(){dt()}_setStateWithResize(e,t){Object.assign(this._layout,mt),this._applyResizing(),t?(this.setState(e),this._resizeHandler()):this.setState(e,(()=>this._resizeByRaf()))}_applyResizing(){const{dateRangeMode:e,noRanges:t,...n}=this._layout;this._rangeExpanded&&(this._rangeExpanded.classList.toggle(Ke.collapsed,"expanded"!==e),this._rangeExpanded.classList.toggle(Ke.noranges,t)),this._rangeCollapsed&&this._rangeCollapsed.classList.toggle(Ke.collapsed,"collapsed"!==e);let s=!1,i=!1;Object.keys(n).forEach((e=>{const t=e;if("separator"!==t){const e=this._seriesComponents[t],a=!0===n[t];e&&("timeZones"===t||"session"===t?s=s||!a:i=i||!a,e.classList.toggle(Ke.collapsed,a))}}));const a=this._seriesComponents.separator;if(a){const e=!s||!i||!0===n.separator;a.classList.toggle(Ke.collapsed,e)}}_isPreventPhoneLayoutButton(){return!1}}gt.contextType=pt;const bt={onContentBoxChanged:o.any,computeContentBox:o.any,chartWidget:o.any,chartApiInstance:o.any,chartWidgetCollection:o.any,resizerDetacher:o.any,availableTimeFrames:o.any};class vt extends s.PureComponent{constructor(e){super(e),this._setActiveChart=e=>{this._defineRegistry(e),this.setState({chartWidget:e})};const t=this.props.chartWidgetCollection.activeChartWidget.value();this.state={chartWidget:t},this._defineRegistry(t)}componentDidMount(){this.props.chartWidgetCollection.activeChartWidget.subscribe(this._setActiveChart)}componentWillUnmount(){this.props.chartWidgetCollection.activeChartWidget.unsubscribe(this._setActiveChart)}render(){const{chartWidget:e}=this.state;if(!e)return null;const{options:t}=this.props,n={timeFramesWidgetEnabled:t.timeFramesWidgetEnabled,goToDateEnabled:t.timeFramesWidget.goToDateEnabled,timeWidgetEnabled:t.timeWidgetEnabled,timezoneMenuEnabled:t.timeWidget&&t.timeWidget.timezoneMenuEnabled,sessionIdButtonEnabled:t.sessionIdButtonEnabled,backAdjustmentButtonEnabled:t.backAdjustmentButtonEnabled,settlementAsCloseButtonEnabled:t.settlementAsCloseButtonEnabled,adjustForDividendsButtonEnabled:t.adjustForDividendsButtonEnabled,logScaleButtonEnabled:t.logScaleButtonEnabled,percentageScaleButtonEnabled:t.percentageScaleButtonEnabled,autoScaleButtonEnabled:t.autoScaleButtonEnabled,fullscreenButtonEnabled:t.fullscreenButtonEnabled,mobileChangeLayoutEnabled:t.mobileChangeLayoutEnabled};return s.createElement(_.RegistryProvider,{validation:bt,value:this._registry},s.createElement(gt,{key:e.id(),...n}))}_defineRegistry(e){const{onContentBoxChanged:t,computeContentBox:n,chartApiInstance:s,chartWidgetCollection:i,options:{timeFramesWidgetEnabled:a,timeFramesWidget:o}}=this.props,r=a?o.availableTimeFrames:void 0;this._registry={onContentBoxChanged:t,computeContentBox:n,chartWidget:e,availableTimeFrames:r,chartApiInstance:s,chartWidgetCollection:i,resizerDetacher:e.getResizerDetacher()}}}var _t;!function(e){e.InitializedAttribute="data-initialized"}(_t||(_t={}));class Ct{constructor(e,t,n,a,o,r,l){this._container=e;const c=s.createElement(vt,{onContentBoxChanged:t,computeContentBox:n,
chartWidgetCollection:a,chartApiInstance:o,chartWidgetOptions:r,options:l});i.render(c,e),e.setAttribute("data-initialized","true")}destroy(){i.unmountComponentAtNode(this._container),this._container.removeAttribute("data-initialized")}}},72026:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M15 8V3h-5V2h6v6h-1ZM3 10v5h5v1H2v-6h1Z"/></svg>'},51267:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M11 2v5h5v1h-6V2h1ZM7 16v-5H2v-1h6v6H7Z"/></svg>'},92574:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="M11 4h-1v2H7.5A2.5 2.5 0 0 0 5 8.5V13h1v-2h16v8.5c0 .83-.67 1.5-1.5 1.5H14v1h6.5a2.5 2.5 0 0 0 2.5-2.5v-11A2.5 2.5 0 0 0 20.5 6H18V4h-1v2h-6V4Zm6 4V7h-6v1h-1V7H7.5C6.67 7 6 7.67 6 8.5V10h16V8.5c0-.83-.67-1.5-1.5-1.5H18v1h-1Zm-5.15 10.15-3.5-3.5-.7.7L10.29 18H4v1h6.3l-2.65 2.65.7.7 3.5-3.5.36-.35-.36-.35Z"/></svg>'},79978:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18" fill="none"><path stroke="currentColor" d="M8 5l3.5 3.5L8 12"/></svg>'},21868:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14"><g fill="none" stroke="currentColor"><circle cx="3.5" cy="3.5" r="2"/><circle cx="10.5" cy="10.5" r="2"/><path stroke-linecap="square" d="M9.5 1.5l-5 11"/></g></svg>'},39750:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path stroke="currentColor" stroke-linecap="round" stroke-width="1.5" d="M7 15l5 5L23 9"/></svg>'},90752:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path stroke="currentColor" d="M5.5 13v-2.5m8.5 11h6.5a2 2 0 0 0 2-2v-9m-17 0v-2c0-1.1.9-2 2-2h13a2 2 0 0 1 2 2v2m-17 0h17"/><path fill="currentColor" d="M10 4h1v4h-1V4zM17 4h1v4h-1V4z"/><path stroke="currentColor" d="M4 18.5h7.5m0 0L8 22m3.5-3.5L8 15"/></svg>'}}]);