(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[6014],{20121:e=>{e.exports={dialog:"dialog-aRAWUDhF",rounded:"rounded-aRAWUDhF",shadowed:"shadowed-aRAWUDhF",fullscreen:"fullscreen-aRAWUDhF",darker:"darker-aRAWUDhF",backdrop:"backdrop-aRAWUDhF"}},52036:e=>{e.exports={"tablet-normal-breakpoint":"screen and (max-width: 768px)","tooltip-offset":"20px",dialog:"dialog-qyCw0PaN",dragging:"dragging-qyCw0PaN",mobile:"mobile-qyCw0PaN",fullscreen:"fullscreen-qyCw0PaN",dialogAnimatedAppearance:"dialogAnimatedAppearance-qyCw0PaN",dialogAnimation:"dialogAnimation-qyCw0PaN",dialogTooltip:"dialogTooltip-qyCw0PaN"}},43010:(e,t,i)=>{"use strict";i.d(t,{useIsomorphicLayoutEffect:()=>o});var n=i(50959);function o(e,t){("undefined"==typeof window?n.useEffect:n.useLayoutEffect)(e,t)}},27267:(e,t,i)=>{"use strict";function n(e,t,i,n,o){function s(o){if(e>o.timeStamp)return;const s=o.target;void 0!==i&&null!==t&&null!==s&&s.ownerDocument===n&&(t.contains(s)||i(o))}return o.click&&n.addEventListener("click",s,!1),o.mouseDown&&n.addEventListener("mousedown",s,!1),o.touchEnd&&n.addEventListener("touchend",s,!1),o.touchStart&&n.addEventListener("touchstart",s,!1),()=>{n.removeEventListener("click",s,!1),n.removeEventListener("mousedown",s,!1),n.removeEventListener("touchend",s,!1),n.removeEventListener("touchstart",s,!1)}}i.d(t,{addOutsideEventListener:()=>n})},36383:(e,t,i)=>{"use strict";i.d(t,{useOutsideEvent:()=>a});var n=i(50959),o=i(43010),s=i(27267);function a(e){const{click:t,mouseDown:i,touchEnd:a,touchStart:r,handler:l,reference:d}=e,u=(0,n.useRef)(null),c=(0,n.useRef)("undefined"==typeof window?0:new window.CustomEvent("timestamp").timeStamp);return(0,o.useIsomorphicLayoutEffect)((()=>{const e={click:t,mouseDown:i,touchEnd:a,touchStart:r},n=d?d.current:u.current;return(0,s.addOutsideEventListener)(c.current,n,l,document,e)}),[t,i,a,r,l]),d||u}},9745:(e,t,i)=>{"use strict";i.d(t,{Icon:()=>o});var n=i(50959);const o=n.forwardRef(((e,t)=>{const{icon:i="",title:o,ariaLabel:s,ariaLabelledby:a,ariaHidden:r,...l}=e,d=!!(o||s||a);return n.createElement("span",{...l,ref:t,role:"img","aria-label":s,"aria-labelledby":a,"aria-hidden":r||!d,title:o,dangerouslySetInnerHTML:{__html:i}})}))},99663:(e,t,i)=>{"use strict";i.d(t,{Slot:()=>o,SlotContext:()=>s});var n=i(50959);class o extends n.Component{shouldComponentUpdate(){return!1}render(){return n.createElement("div",{style:{position:"fixed",zIndex:150,left:0,top:0},ref:this.props.reference})}}const s=n.createContext(null)},90186:(e,t,i)=>{"use strict";function n(e){return s(e,a)}function o(e){return s(e,r)}function s(e,t){const i=Object.entries(e).filter(t),n={};for(const[e,t]of i)n[e]=t;return n}function a(e){const[t,i]=e;return 0===t.indexOf("data-")&&"string"==typeof i}function r(e){return 0===e[0].indexOf("aria-")}i.d(t,{filterAriaProps:()=>o,filterDataProps:()=>n,filterProps:()=>s,isAriaAttribute:()=>r,isDataAttribute:()=>a})},53017:(e,t,i)=>{"use strict";function n(e){return t=>{e.forEach((e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)}))}}function o(e){
return n([e])}i.d(t,{isomorphicRef:()=>o,mergeRefs:()=>n})},52778:(e,t,i)=>{"use strict";i.d(t,{OutsideEvent:()=>o});var n=i(36383);function o(e){const{children:t,...i}=e;return t((0,n.useOutsideEvent)(i))}},67961:(e,t,i)=>{"use strict";i.d(t,{OverlapManager:()=>s,getRootOverlapManager:()=>r});var n=i(50151);class o{constructor(){this._storage=[]}add(e){this._storage.push(e)}remove(e){this._storage=this._storage.filter((t=>e!==t))}has(e){return this._storage.includes(e)}getItems(){return this._storage}}class s{constructor(e=document){this._storage=new o,this._windows=new Map,this._index=0,this._document=e,this._container=e.createDocumentFragment()}setContainer(e){const t=this._container,i=null===e?this._document.createDocumentFragment():e;!function(e,t){Array.from(e.childNodes).forEach((e=>{e.nodeType===Node.ELEMENT_NODE&&t.appendChild(e)}))}(t,i),this._container=i}registerWindow(e){this._storage.has(e)||this._storage.add(e)}ensureWindow(e,t={position:"fixed",direction:"normal"}){const i=this._windows.get(e);if(void 0!==i)return i;this.registerWindow(e);const n=this._document.createElement("div");if(n.style.position=t.position,n.style.zIndex=this._index.toString(),n.dataset.id=e,void 0!==t.index){const e=this._container.childNodes.length;if(t.index>=e)this._container.appendChild(n);else if(t.index<=0)this._container.insertBefore(n,this._container.firstChild);else{const e=this._container.childNodes[t.index];this._container.insertBefore(n,e)}}else"reverse"===t.direction?this._container.insertBefore(n,this._container.firstChild):this._container.appendChild(n);return this._windows.set(e,n),++this._index,n}unregisterWindow(e){this._storage.remove(e);const t=this._windows.get(e);void 0!==t&&(null!==t.parentElement&&t.parentElement.removeChild(t),this._windows.delete(e))}getZindex(e){const t=this.ensureWindow(e);return parseInt(t.style.zIndex||"0")}moveToTop(e){if(this.getZindex(e)!==this._index){const t=this.ensureWindow(e);this._windows.forEach(((e,i)=>{e.hasAttribute("data-focus-trap")&&e.setAttribute("data-focus-trap",e===t?"true":"false")})),t.style.zIndex=(++this._index).toString()}}removeWindow(e){this.unregisterWindow(e)}}const a=new WeakMap;function r(e=document){const t=e.getElementById("overlap-manager-root");if(null!==t)return(0,n.ensureDefined)(a.get(t));{const t=new s(e),i=function(e){const t=e.createElement("div");return t.style.position="absolute",t.style.zIndex=150..toString(),t.style.top="0px",t.style.left="0px",t.id="overlap-manager-root",t}(e);return a.set(i,t),t.setContainer(i),e.body.appendChild(i),t}}var l;!function(e){e[e.BaseZindex=150]="BaseZindex"}(l||(l={}))},99054:(e,t,i)=>{"use strict";i.d(t,{setFixedBodyState:()=>d});const n=(()=>{let e;return()=>{var t;if(void 0===e){const i=document.createElement("div"),n=i.style;n.visibility="hidden",n.width="100px",n.msOverflowStyle="scrollbar",document.body.appendChild(i);const o=i.offsetWidth;i.style.overflow="scroll";const s=document.createElement("div");s.style.width="100%",i.appendChild(s);const a=s.offsetWidth
;null===(t=i.parentNode)||void 0===t||t.removeChild(i),e=o-a}return e}})();function o(e,t,i){null!==e&&e.style.setProperty(t,i)}function s(e,t){return getComputedStyle(e,null).getPropertyValue(t)}function a(e,t){return parseInt(s(e,t))}let r=0,l=!1;function d(e){const{body:t}=document,i=t.querySelector(".widgetbar-wrap");if(e&&1==++r){const e=s(t,"overflow"),r=a(t,"padding-right");"hidden"!==e.toLowerCase()&&t.scrollHeight>t.offsetHeight&&(o(i,"right",`${n()}px`),t.style.paddingRight=`${r+n()}px`,l=!0),t.classList.add("i-no-scroll")}else if(!e&&r>0&&0==--r&&(t.classList.remove("i-no-scroll"),l)){o(i,"right","0px");let e=0;0,t.scrollHeight<=t.clientHeight&&(e-=n()),t.style.paddingRight=(e<0?0:e)+"px",l=!1}}},28124:(e,t,i)=>{"use strict";i.d(t,{createReactRoot:()=>_});var n=i(50959),o=i(32227),s=i(4237);const a=(0,n.createContext)({isOnMobileAppPage:()=>!1,isRtl:!1,locale:{language:"en"}});var r=i(84015),l=i(63273),d=i(50151)
;const u=JSON.parse('{"ar_AE":{"language":"ar","language_name":"العربية","flag":"sa","geoip_code":"sa","countries_with_this_language":["ae","bh","dj","dz","eg","er","iq","jo","km","kw","lb","ly","ma","mr","om","qa","sa","sd","so","sy","td","tn","ye"],"priority":500,"dir":"rtl","iso":"ar","iso_639_3":"arb","show_on_widgets":true,"global_name":"Arabic"},"br":{"language":"pt","language_name":"Português","flag":"br","geoip_code":"br","priority":650,"iso":"pt","iso_639_3":"por","show_on_widgets":true,"global_name":"Portuguese"},"ca_ES":{"language":"ca_ES","language_name":"Català","flag":"es","geoip_code":"es","priority":745,"iso":"ca","iso_639_3":"cat","disabled":true,"show_on_widgets":true,"global_name":"Catalan"},"cs":{"language":"cs","language_name":"Czech","flag":"cz","geoip_code":"cz","priority":718,"iso":"cs","iso_639_3":"ces","show_on_widgets":true,"global_name":"Czech","is_in_european_union":true,"isBattle":true},"de_DE":{"language":"de","language_name":"Deutsch","flag":"de","geoip_code":"de","countries_with_this_language":["at","ch"],"priority":756,"iso":"de","iso_639_3":"deu","show_on_widgets":true,"global_name":"German","is_in_european_union":true},"en":{"language":"en","language_name":"English","flag":"us","geoip_code":"us","priority":1000,"iso":"en","iso_639_3":"eng","show_on_widgets":true,"global_name":"English","is_only_recommended_tw_autorepost":true},"es":{"language":"es","language_name":"Español","flag":"es","geoip_code":"es","countries_with_this_language":["mx","ar","ve","cl","co","pe","uy","py","cr","gt","c","bo","pa","pr"],"priority":744,"iso":"es","iso_639_3":"spa","show_on_widgets":true,"global_name":"Spanish","is_in_european_union":true},"fa_IR":{"language":"fa","language_name":"فارسى","flag":"ir","geoip_code":"ir","priority":480,"dir":"rtl","iso":"fa","iso_639_3":"fas","show_on_widgets":false,"global_name":"Iranian","disabled":true},"fr":{"language":"fr","language_name":"Français","flag":"fr","geoip_code":"fr","priority":750,"iso":"fr","iso_639_3":"fra","show_on_widgets":true,"global_name":"French","is_in_european_union":true},"he_IL":{"language":"he_IL","language_name":"עברית","flag":"il","geoip_code":"il","priority":490,"dir":"rtl","iso":"he","iso_639_3":"heb","show_on_widgets":true,"global_name":"Israeli"},"hu_HU":{"language":"hu_HU","language_name":"Magyar","flag":"hu","geoip_code":"hu","priority":724,"iso":"hu","iso_639_3":"hun","show_on_widgets":true,"global_name":"Hungarian","is_in_european_union":true,"disabled":true},"id":{"language":"id_ID","language_name":"Bahasa Indonesia","flag":"id","geoip_code":"id","priority":648,"iso":"id","iso_639_3":"ind","show_on_widgets":true,"global_name":"Indonesian"},"in":{"language":"en","language_name":"English ‎(India)‎","flag":"in","geoip_code":"in","priority":800,"iso":"en","iso_639_3":"eng","show_on_widgets":true,"global_name":"Indian"},"it":{"language":"it","language_name":"Italiano","flag":"it","geoip_code":"it","priority":737,"iso":"it","iso_639_3":"ita","show_on_widgets":true,"global_name":"Italian","is_in_european_union":true},"ja":{"language":"ja","language_name":"日本語","flag":"jp","geoip_code":"jp","priority":600,"iso":"ja","iso_639_3":"jpn","show_on_widgets":true,"global_name":"Japanese"},"kr":{"language":"ko","language_name":"한국어","flag":"kr","geoip_code":"kr","priority":550,"iso":"ko","iso_639_3":"kor","show_on_widgets":true,"global_name":"Korean"},"ms_MY":{"language":"ms_MY","language_name":"Bahasa Melayu","flag":"my","geoip_code":"my","priority":647,"iso":"ms","iso_639_3":"zlm","show_on_widgets":true,"global_name":"Malaysian"},"pl":{"language":"pl","language_name":"Polski","flag":"pl","geoip_code":"pl","priority":725,"iso":"pl","iso_639_3":"pol","show_on_widgets":true,"global_name":"Polish","is_in_european_union":true},"ru":{"language":"ru","language_name":"Русский","flag":"ru","geoip_code":"ru","countries_with_this_language":["am","by","kg","kz","md","tj","tm","uz"],"priority":700,"iso":"ru","iso_639_3":"rus","show_on_widgets":true,"global_name":"Russian","is_only_recommended_tw_autorepost":true},"sv_SE":{"language":"sv","language_name":"Svenska","flag":"se","geoip_code":"se","priority":723,"iso":"sv","iso_639_3":"swe","show_on_widgets":true,"global_name":"Swedish","is_in_european_union":true},"th_TH":{"language":"th","language_name":"ภาษาไทย","flag":"th","geoip_code":"th","priority":646,"iso":"th","iso_639_3":"tha","show_on_widgets":true,"global_name":"Thai"},"tr":{"language":"tr","language_name":"Türkçe","flag":"tr","geoip_code":"tr","priority":713,"iso":"tr","iso_639_3":"tur","show_on_widgets":true,"global_name":"Turkish","is_only_recommended_tw_autorepost":true},"vi_VN":{"language":"vi","language_name":"Tiếng Việt","flag":"vn","geoip_code":"vn","priority":645,"iso":"vi","iso_639_3":"vie","show_on_widgets":true,"global_name":"Vietnamese"},"zh_CN":{"language":"zh","language_name":"简体中文","flag":"cn","geoip_code":"cn","countries_with_this_language":["zh"],"priority":537,"iso":"zh-Hans","iso_639_3":"cmn","show_on_widgets":true,"global_name":"Chinese"},"zh_TW":{"language":"zh_TW","language_name":"繁體中文","flag":"tw","geoip_code":"tw","countries_with_this_language":["hk"],"priority":536,"iso":"zh-Hant","iso_639_3":"cmn","show_on_widgets":true,"global_name":"Taiwanese"},"el":{"language":"el","language_name":"Greek","flag":"gr","geoip_code":"gr","priority":625,"iso":"el","iso_639_3":"ell","global_name":"Greek","is_in_european_union":true,"isBattle":true},"nl_NL":{"language":"nl_NL","language_name":"Dutch","flag":"nl","geoip_code":"nl","priority":731,"iso":"nl","iso_639_3":"nld","global_name":"Dutch","is_in_european_union":true,"isBattle":true},"ro":{"language":"ro","language_name":"Romanian","flag":"ro","geoip_code":"ro","priority":707,"iso":"ro","iso_639_3":"ron","global_name":"Romanian","is_in_european_union":true,"isBattle":true}}'),c=function(){
const e=document.querySelectorAll("link[rel~=link-locale][data-locale]");if(0===e.length)return u;const t={};return e.forEach((e=>{const i=(0,d.ensureNotNull)(e.getAttribute("data-locale"));t[i]={...u[i],href:e.href}})),t}();const h={iOs:"old",android:"new",old:"old",new:"new",any:"any"};function g(e){const[t]=(0,n.useState)({isOnMobileAppPage:e=>(0,r.isOnMobileAppPage)(h[e]),isRtl:(0,l.isRtl)(),locale:(i=i||window.locale,c[i])});var i;return n.createElement(a.Provider,{value:t},e.children)}function _(e,t,i="legacy"){const a=n.createElement(g,null,e);if("modern"===i){const e=(0,s.createRoot)(t);return e.render(a),{render(t){e.render(n.createElement(g,null,t))},unmount(){e.unmount()}}}return o.render(a,t),{render(e){o.render(n.createElement(g,null,e),t)},unmount(){o.unmountComponentAtNode(t)}}}},95711:(e,t,i)=>{"use strict";i.d(t,{PopupContext:()=>n});const n=i(50959).createContext(null)},82206:(e,t,i)=>{"use strict";i.d(t,{PopupDialog:()=>z});var n=i(50959),o=i(97754),s=i(50151),a=i(99663),r=i(67961),l=i(90186),d=i(20121);class u extends n.PureComponent{constructor(){super(...arguments),this._manager=new r.OverlapManager,this._handleSlot=e=>{this._manager.setContainer(e)}}render(){const{rounded:e=!0,shadowed:t=!0,fullscreen:i=!1,darker:s=!1,className:r,backdrop:u,containerTabIndex:c=-1}=this.props,h=o(r,d.dialog,e&&d.rounded,t&&d.shadowed,i&&d.fullscreen,s&&d.darker),g=(0,l.filterDataProps)(this.props),_=this.props.style?{...this._createStyles(),...this.props.style}:this._createStyles();return n.createElement(n.Fragment,null,n.createElement(a.SlotContext.Provider,{value:this._manager},u&&n.createElement("div",{onClick:this.props.onClickBackdrop,className:d.backdrop}),n.createElement("div",{...g,className:h,style:_,ref:this.props.reference,onFocus:this.props.onFocus,onMouseDown:this.props.onMouseDown,onMouseUp:this.props.onMouseUp,onClick:this.props.onClick,onKeyDown:this.props.onKeyDown,tabIndex:c,"aria-label":this.props.containerAriaLabel},this.props.children)),n.createElement(a.Slot,{reference:this._handleSlot}))}_createStyles(){const{bottom:e,left:t,width:i,right:n,top:o,zIndex:s,height:a}=this.props;return{bottom:e,left:t,right:n,top:o,zIndex:s,maxWidth:i,height:a}}}var c,h=i(86431),g=i(52778),_=i(9859),p=i(19291);function m(e,t,i,n){return e+t>n&&(e=n-t),e<i&&(e=i),e}function f(e){return{x:(0,_.clamp)(e.x,20,document.documentElement.clientWidth-20),y:(0,_.clamp)(e.y,20,window.innerHeight-20)}}function v(e){return{x:e.clientX,y:e.clientY}}function y(e){return{x:e.touches[0].clientX,y:e.touches[0].clientY}}!function(e){e[e.MouseGuardZone=20]="MouseGuardZone"}(c||(c={}));class w{constructor(e,t,i={boundByScreen:!0}){this._drag=null,this._canBeTouchClick=!1,this._frame=null,this._onMouseDragStart=e=>{if(0!==e.button||this._isTargetNoDraggable(e))return;e.preventDefault(),document.addEventListener("mousemove",this._onMouseDragMove),document.addEventListener("mouseup",this._onMouseDragEnd);const t=f(v(e));this._dragStart(t)},this._onTouchDragStart=e=>{if(this._isTargetNoDraggable(e))return;this._canBeTouchClick=!0,
e.preventDefault(),this._header.addEventListener("touchmove",this._onTouchDragMove,{passive:!1});const t=f(y(e));this._dragStart(t)},this._onMouseDragEnd=e=>{e.target instanceof Node&&this._header.contains(e.target)&&e.preventDefault(),document.removeEventListener("mousemove",this._onMouseDragMove),document.removeEventListener("mouseup",this._onMouseDragEnd),this._onDragStop()},this._onTouchDragEnd=e=>{this._header.removeEventListener("touchmove",this._onTouchDragMove),this._onDragStop(),this._canBeTouchClick&&(this._canBeTouchClick=!1,function(e){if(e instanceof SVGElement){const t=document.createEvent("SVGEvents");t.initEvent("click",!0,!0),e.dispatchEvent(t)}e instanceof HTMLElement&&e.click()}(e.target))},this._onMouseDragMove=e=>{const t=f(v(e));this._dragMove(t)},this._onTouchDragMove=e=>{this._canBeTouchClick=!1,e.preventDefault();const t=f(y(e));this._dragMove(t)},this._onDragStop=()=>{this._drag=null,this._header.classList.remove("dragging"),this._options.onDragEnd&&this._options.onDragEnd()},this._dialog=e,this._header=t,this._options=i,this._header.addEventListener("mousedown",this._onMouseDragStart),this._header.addEventListener("touchstart",this._onTouchDragStart),this._header.addEventListener("touchend",this._onTouchDragEnd)}destroy(){null!==this._frame&&cancelAnimationFrame(this._frame),this._header.removeEventListener("mousedown",this._onMouseDragStart),document.removeEventListener("mouseup",this._onMouseDragEnd),this._header.removeEventListener("touchstart",this._onTouchDragStart),this._header.removeEventListener("touchend",this._onTouchDragEnd),document.removeEventListener("mouseleave",this._onMouseDragEnd)}updateOptions(e){this._options=e}_dragStart(e){const t=this._dialog.getBoundingClientRect();this._drag={startX:e.x,startY:e.y,finishX:e.x,finishY:e.y,dialogX:t.left,dialogY:t.top};const i=Math.round(t.left),n=Math.round(t.top);this._dialog.style.transform=`translate(${i}px, ${n}px)`,this._header.classList.add("dragging"),this._options.onDragStart&&this._options.onDragStart()}_dragMove(e){if(this._drag){if(this._drag.finishX=e.x,this._drag.finishY=e.y,null!==this._frame)return;this._frame=requestAnimationFrame((()=>{if(this._drag){const t=e.x-this._drag.startX,i=e.y-this._drag.startY;this._moveDialog(this._drag.dialogX+t,this._drag.dialogY+i)}this._frame=null}))}}_moveDialog(e,t){const i=this._dialog.getBoundingClientRect(),{boundByScreen:n}=this._options,o=m(e,i.width,n?0:-1/0,n?window.innerWidth:1/0),s=m(t,i.height,n?0:-1/0,n?window.innerHeight:1/0);this._dialog.style.transform=`translate(${Math.round(o)}px, ${Math.round(s)}px)`}_isTargetNoDraggable(e){return e.target instanceof Element&&null!==e.target.closest("[data-disable-drag]")}}const E={vertical:0};var b,x=i(42842),D=i(95711),S=i(99054),T=i(31955),C=i(92184);!function(e){e.Open="dialog-open",e.Close="dialog-close",e.FullscreenOn="dialog-fullscreen-on",e.FullscreenOff="dialog-fullscreen-off"}(b||(b={}));const M=(0,T.getLogger)("DialogEventDispatcher");class A{constructor(){this._openSessionId=null}dispatch(e){if("dialog-open"===e){
if(null!==this._openSessionId)return void M.logError("Multiple calls to open dialog");this._openSessionId=(0,C.randomHash)()}null!==this._openSessionId?(window.dispatchEvent(new CustomEvent(e,{bubbles:!0,detail:{dialogSessionId:this._openSessionId}})),"dialog-close"===e&&(this._openSessionId=null)):M.logError("Empty open dialog session id")}}var F=i(84015),P=(i(56570),i(52036));P["tooltip-offset"];const k=class{constructor(e,t){this._frame=null,this._isFullscreen=!1,this._handleResize=()=>{null===this._frame&&(this._frame=requestAnimationFrame((()=>{this.recalculateBounds(),this._frame=null})))},this._dialog=e,this._guard=t.guard||E,this._calculateDialogPosition=t.calculateDialogPosition,this._initialHeight=e.style.height,window.addEventListener("resize",this._handleResize)}updateOptions(e){this._guard=e.guard||E,this._calculateDialogPosition=e.calculateDialogPosition}setFullscreen(e){this._isFullscreen!==e&&(this._isFullscreen=e,this.recalculateBounds())}centerAndFit(){const{x:e,y:t}=this.getDialogsTopLeftCoordinates(),i=this._calcAvailableHeight(),n=this._calcDialogHeight();if(i===n)if(this._calculateDialogPosition){const{left:e,top:t}=this._calculateDialogPosition(this._dialog,document.documentElement,this._guard);this._dialog.style.transform=`translate(${Math.round(e)}px, ${Math.round(t)}px)`}else this._dialog.style.height=n+"px";this._dialog.style.top="0px",this._dialog.style.left="0px",this._dialog.style.transform=`translate(${e}px, ${t}px)`}getDialogsTopLeftCoordinates(){const{clientWidth:e,clientHeight:t}=this._getClientDimensions(),i=this._calcDialogHeight(),n=e/2-this._dialog.clientWidth/2,o=t/2-i/2+this._getTopOffset();return{x:Math.round(n),y:Math.round(o)}}recalculateBounds(){var e;const{clientWidth:t,clientHeight:i}=this._getClientDimensions(),{vertical:n}=this._guard,o=null===(e=this._calculateDialogPosition)||void 0===e?void 0:e.call(this,this._dialog,{clientWidth:t,clientHeight:i},{vertical:n});if(this._isFullscreen){if(this._dialog.style.top="0px",this._dialog.style.left="0px",this._dialog.style.width="100%",this._dialog.style.height="100%",this._dialog.style.transform="none",o){const{left:e,top:t,width:i,height:n}=o;this._dialog.style.transform=`translate(${Math.round(e)}px, ${Math.round(t)}px)`,i&&(this._dialog.style.width=`${i}px`,this._dialog.style.minWidth="unset"),n&&(this._dialog.style.height=`${n}px`,this._dialog.style.minHeight="unset")}}else if(o){const{left:e,top:t}=o;this._dialog.style.transform=`translate(${Math.round(e)}px, ${Math.round(t)}px)`}else{this._dialog.style.width="",this._dialog.style.height="";const e=this._dialog.getBoundingClientRect(),o=i-2*n,s=m(e.left,e.width,0,t),a=m(e.top,e.height,n,i);this._dialog.style.top="0px",this._dialog.style.left="0px",this._dialog.style.transform=`translate(${Math.round(s)}px, ${Math.round(a)}px)`,this._dialog.style.height=o<e.height?o+"px":this._initialHeight}}destroy(){window.removeEventListener("resize",this._handleResize),null!==this._frame&&(cancelAnimationFrame(this._frame),this._frame=null)}_getClientDimensions(){return{
clientHeight:document.documentElement.clientHeight,clientWidth:document.documentElement.clientWidth}}_getTopOffset(){return 0}_calcDialogHeight(){const e=this._calcAvailableHeight();return e<this._dialog.clientHeight?e:this._dialog.clientHeight}_calcAvailableHeight(){return this._getClientDimensions().clientHeight-2*this._guard.vertical}};class L extends n.PureComponent{constructor(e){super(e),this._dialog=null,this._cleanUpFunctions=[],this._prevActiveElement=null,this._eventDispatcher=new A,this._handleDialogRef=e=>{const{reference:t}=this.props;this._dialog=e,"function"==typeof t&&t(e)},this._handleFocus=()=>{this._moveToTop()},this._handleMouseDown=e=>{this._moveToTop()},this._handleTouchStart=e=>{this._moveToTop()},this.state={canFitTooltip:!1},this._prevActiveElement=document.activeElement}render(){return n.createElement(D.PopupContext.Provider,{value:this},n.createElement(g.OutsideEvent,{mouseDown:!0,touchStart:!0,handler:this.props.onClickOutside},(e=>n.createElement("div",{ref:e,"data-outside-boundary-for":this.props.name,onFocus:this._handleFocus,onMouseDown:this._handleMouseDown,onTouchStart:this._handleTouchStart,"data-dialog-name":this.props["data-dialog-name"]},n.createElement(u,{style:this._applyAnimationCSSVariables(),...this.props,reference:this._handleDialogRef,className:o(P.dialog,(0,F.isOnMobileAppPage)("any")&&!this.props.noMobileAppShadows&&P.mobile,this.props.fullscreen&&P.fullscreen,this.props.className)},!1,this.props.children)))))}componentDidMount(){const{draggable:e,boundByScreen:t,onDragStart:i}=this.props,n=(0,s.ensureNotNull)(this._dialog);if(this._eventDispatcher.dispatch("dialog-open"),e){const e=n.querySelector("[data-dragg-area]");if(e&&e instanceof HTMLElement){const o=new w(n,e,{boundByScreen:Boolean(t),onDragStart:i});this._cleanUpFunctions.push((()=>o.destroy())),this._drag=o}}this.props.autofocus&&!n.contains(document.activeElement)&&n.focus(),(this._isFullScreen()||this.props.fixedBody)&&(0,S.setFixedBodyState)(!0);const{guard:o,calculateDialogPosition:a}=this.props;if(this.props.resizeHandler)this._resize=this.props.resizeHandler;else{const e=new k(n,{guard:o,calculateDialogPosition:a});this._cleanUpFunctions.push((()=>e.destroy())),this._resize=e}if(this._isFullScreen()&&this._eventDispatcher.dispatch("dialog-fullscreen-on"),this.props.isAnimationEnabled&&this.props.growPoint&&this._applyAppearanceAnimation(this.props.growPoint),this.props.centeredOnMount&&this._resize.centerAndFit(),this._resize.setFullscreen(this._isFullScreen()),this.props.shouldForceFocus){if(this.props.onForceFocus)return void this.props.onForceFocus(n);n.focus()}if(!n.contains(document.activeElement)){const e=function(e){const t=e.querySelector("[autofocus]:not([disabled])");if(t)return t;if(e.tabIndex>=0)return e;const i=(0,p.getActiveElementSelectors)(),n=Array.from(e.querySelectorAll(i)).filter((0,p.createScopedVisibleElementFilter)(e));let o=Number.NEGATIVE_INFINITY,s=null;for(let e=0;e<n.length;e++){const t=n[e],i=t.getAttribute("tabindex");if(null!==i){const e=parseInt(i,10);!isNaN(e)&&e>o&&(o=e,
s=t)}}return s}(n);e instanceof HTMLElement&&e.focus()}}componentDidUpdate(e){const t=e.fullscreen;if(this._resize){const{guard:e,calculateDialogPosition:t}=this.props;this._resize.updateOptions({guard:e,calculateDialogPosition:t}),this._resize.setFullscreen(this._isFullScreen())}if(this._drag&&this._drag.updateOptions({boundByScreen:Boolean(this.props.boundByScreen),onDragStart:this.props.onDragStart}),e.fullscreen!==this.props.fullscreen){const e=this.props.fullscreen;e&&!t?this._eventDispatcher.dispatch("dialog-fullscreen-on"):!e&&t&&this._eventDispatcher.dispatch("dialog-fullscreen-off")}}componentWillUnmount(){var e;if(this.props.shouldReturnFocus&&this._prevActiveElement&&document.body.contains(this._prevActiveElement)&&(null===document.activeElement||document.activeElement===document.body||(null===(e=this._dialog)||void 0===e?void 0:e.contains(document.activeElement))))try{setTimeout((()=>{this._prevActiveElement.focus({preventScroll:!0})}))}catch(e){}for(const e of this._cleanUpFunctions)e();(this._isFullScreen()||this.props.fixedBody)&&(0,S.setFixedBodyState)(!1),this._isFullScreen()&&this._eventDispatcher.dispatch("dialog-fullscreen-off"),this._eventDispatcher.dispatch("dialog-close")}focus(){this._dialog&&this._dialog.focus()}centerAndFit(){this._resize&&this._resize.centerAndFit()}recalculateBounds(){this._resize&&this._resize.recalculateBounds()}_moveToTop(){null!==this.context&&this.context.moveToTop()}_applyAnimationCSSVariables(){return{"--animationTranslateStartX":null,"--animationTranslateStartY":null,"--animationTranslateEndX":null,"--animationTranslateEndY":null}}_applyAppearanceAnimation(e){if(this._resize&&this._dialog){const{x:t,y:i}=e,{x:n,y:o}=this._resize.getDialogsTopLeftCoordinates();this._dialog.style.setProperty("--animationTranslateStartX",`${t}px`),this._dialog.style.setProperty("--animationTranslateStartY",`${i}px`),this._dialog.style.setProperty("--animationTranslateEndX",`${n}px`),this._dialog.style.setProperty("--animationTranslateEndY",`${o}px`),this._dialog.classList.add(P.dialogAnimatedAppearance)}}_handleTooltipFit(){0}_isFullScreen(){return Boolean(this.props.fullscreen)}}L.contextType=x.PortalContext,L.defaultProps={boundByScreen:!0,draggable:!0,centeredOnMount:!0,shouldReturnFocus:!0};const z=(0,h.makeOverlapable)(L,!0)},86431:(e,t,i)=>{"use strict";i.d(t,{makeOverlapable:()=>s});var n=i(50959),o=i(42842);function s(e,t){return class extends n.PureComponent{render(){const{isOpened:i,root:s}=this.props;if(!i)return null;const a=n.createElement(e,{...this.props,zIndex:150});return"parent"===s?a:n.createElement(o.Portal,{shouldTrapFocus:t},a)}}}},42842:(e,t,i)=>{"use strict";i.d(t,{Portal:()=>l,PortalContext:()=>d});var n=i(50959),o=i(32227),s=i(55698),a=i(67961),r=i(99663);class l extends n.PureComponent{constructor(){super(...arguments),this._uuid=(0,s.nanoid)()}componentWillUnmount(){this._manager().removeWindow(this._uuid)}render(){const e=this._manager().ensureWindow(this._uuid,this.props.layerOptions);e.style.top=this.props.top||"",e.style.bottom=this.props.bottom||"",
e.style.left=this.props.left||"",e.style.right=this.props.right||"",e.style.pointerEvents=this.props.pointerEvents||"";const t=this.props.className;return t&&("string"==typeof t?e.classList.add(t):e.classList.add(...t)),this.props.shouldTrapFocus&&!e.hasAttribute("data-focus-trap")&&e.setAttribute("data-focus-trap","true"),this.props["aria-hidden"]&&e.setAttribute("aria-hidden","true"),o.createPortal(n.createElement(d.Provider,{value:this},this.props.children),e)}moveToTop(){this._manager().moveToTop(this._uuid)}_manager(){return null===this.context?(0,a.getRootOverlapManager)():this.context}}l.contextType=r.SlotContext;const d=n.createContext(null)}}]);