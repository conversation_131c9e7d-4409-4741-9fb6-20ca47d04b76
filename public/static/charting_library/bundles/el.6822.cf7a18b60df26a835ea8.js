(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[6822],{94920:e=>{e.exports={en:["ADJ"],el:["adj"]}},16755:e=>{e.exports={en:["B-ADJ"],el:["B-ADJ"]}},82631:e=>{e.exports={en:["SET"],el:["SET"]}},22233:e=>{e.exports={en:["auto"],el:["αυτοματο"]}},4161:e=>{e.exports={en:["log"],el:["log"]}},58221:e=>{e.exports={en:["All data"],el:["All data"]}},19273:e=>{e.exports={en:["Year to day"],el:["Year to day"]}},58426:e=>{e.exports={en:["{timePeriod} in {timeInterval}"],el:["{timePeriod} in {timeInterval}"]}},93020:e=>{e.exports={en:["Adjust data for dividends"],el:["Adjust data for dividends"]}},68921:e=>{e.exports={en:["Adjust for contract changes"],el:["Adjust for contract changes"]}},42432:e=>{e.exports={en:["Go to"],el:["Go to"]}},92966:e=>{e.exports={en:["Extended Hours is available only for intraday charts"],el:["Extended Hours is available only for intraday charts"]}},61206:e=>{e.exports={en:["Maximize chart"],el:["Maximize chart"]}},2031:e=>{e.exports={en:["Main symbol data is adjusted for dividends only"],el:["Main symbol data is adjusted for dividends only"]}},95739:e=>{e.exports={en:["Main symbol data is adjusted for splits only"],el:["Main symbol data is adjusted for splits only"]}},27665:e=>{e.exports={en:["Sessions"],el:["Sessions"]}},31142:e=>{e.exports={en:["Restore chart"],el:["Restore chart"]}},41888:e=>{e.exports={en:["Toggle Auto Scale"],el:["Αυτόματη κλίμακα"]}},1e4:e=>{e.exports={en:["Toggle Log Scale"],el:["Λογαριθμική κλίμακα"]}},81649:e=>{e.exports={en:["Toggle Percentage"],el:["Ποσοστιαία κλίμακα"]}},77073:e=>{e.exports={en:["Timezone"],el:["Timezone"]}},49545:e=>{e.exports={en:["Use settlement as close on daily interval"],el:["Use settlement as close on daily interval"]}},8586:e=>{e.exports={en:["ext"],el:["ext"]}},63808:e=>{e.exports={en:["{str} day","{str} days"],el:["{str} day","{str} days"]}},62368:e=>{e.exports={en:["{str} day","{str} days"],el:["{str} day","{str} days"]}},561:e=>{e.exports={en:["{str} day intervals","{str} days intervals"],el:["{str} day intervals","{str} days intervals"]}},72495:e=>{e.exports={en:["{str} hour","{str} hours"],el:["{str} hour","{str} hours"]}},64963:e=>{e.exports={en:["{str} hour","{str} hours"],el:["{str} hour","{str} hours"]}},14887:e=>{e.exports={en:["{str} hour intervals","{str} hours intervals"],el:["{str} hour intervals","{str} hours intervals"]}},12752:e=>{e.exports={en:["{str} month","{str} months"],el:["{str} month","{str} months"]}},20062:e=>{e.exports={en:["{str} month","{str} months"],el:["{str} month","{str} months"]}},48514:e=>{e.exports={en:["{str} month intervals","{str} months intervals"],el:["{str} month intervals","{str} months intervals"]}},95484:e=>{e.exports={en:["{str} minute","{str} minutes"],el:["{str} minute","{str} minutes"]}},5926:e=>{e.exports={en:["{str} minute","{str} minutes"],el:["{str} minute","{str} minutes"]}},15489:e=>{e.exports={en:["{str} minute intervals","{str} minutes intervals"],el:["{str} minute intervals","{str} minutes intervals"]}},6088:e=>{e.exports={en:["{str} week","{str} weeks"],
el:["{str} week","{str} weeks"]}},49306:e=>{e.exports={en:["{str} week","{str} weeks"],el:["{str} week","{str} weeks"]}},60316:e=>{e.exports={en:["{str} week intervals","{str} weeks intervals"],el:["{str} week intervals","{str} weeks intervals"]}},96325:e=>{e.exports={en:["{str} year","{str} years"],el:["{str} year","{str} years"]}},91549:e=>{e.exports={en:["{str} year","{str} years"],el:["{str} year","{str} years"]}},78971:e=>{e.exports={en:["{str} year intervals","{str} years intervals"],el:["{str} year intervals","{str} years intervals"]}}}]);