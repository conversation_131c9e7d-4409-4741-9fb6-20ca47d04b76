(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[2831],{4543:e=>{e.exports={en:["Cancel"],ru:["Отмена"]}},32910:e=>{e.exports={en:["Choose image"],ru:["Выбрать изображение"]}},47742:e=>{e.exports={en:["Close menu"],ru:["Закрыть меню"]}},98918:e=>{e.exports={en:["Drop image here!"],ru:["Переместите изображение сюда!"]}},49580:e=>{e.exports={en:["Image is required"],ru:["Необходимо изображение"]}},46280:e=>{e.exports={en:["JPG or PNG"],ru:["JPG или PNG"]}},19295:e=>{e.exports={en:["Ok"],ru:["Ок"]}},7654:e=>{e.exports={en:["Max size {value}"],ru:["Макс. размер — {value}"]}},18916:e=>{e.exports={en:["Something went wrong, try again"],ru:["Что-то пошло не так, повторите попытку"]}},73007:e=>{e.exports={en:["The image being pasted is way too large"],ru:["Изображение слишком большого размера"]}},19788:e=>{e.exports={en:["Transparency"],ru:["Прозрачность"]}},16992:e=>{e.exports={en:["Upload a valid image. The file you uploaded was either not an image or a corrupted image."],ru:["Загрузите корректное изображение. Файл, который вы пытаетесь загрузить не является изображением, или повреждён."]}}}]);