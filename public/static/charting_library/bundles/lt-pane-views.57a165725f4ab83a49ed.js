(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[1583],{77142:(e,t,i)=>{"use strict";i.r(t),i.d(t,{Pattern5pointsPaneView:()=>p});var n=i(51056),r=i(49857),s=i(95201),o=i(17330),a=i(1161),l=i(91046),d=i(43290),h=i(56468),c=i(74011),u=i(27916),_=i(15938);class p extends u.LineSourcePaneView{constructor(e,t){super(e,t),this._abRetracement=NaN,this._bcRetracement=NaN,this._cdRetracement=NaN,this._xdRetracement=NaN,this._bcRetracementTrend=new l.TrendLineRenderer,this._xdRetracementTrend=new l.TrendLineRenderer,this._xbTrend=new l.TrendLineRenderer,this._bdTrend=new l.TrendLineRenderer,this._polylineRenderer=new c.<PERSON>ygon<PERSON>er(new h.HitTestResult(h.HitTarget.MovePoint)),this._mainTriangleRenderer=new a.TriangleRenderer,this._triangleRendererPoints234=new a.TriangleRenderer,this._xbLabelRenderer=new o.TextRenderer,this._acLabelRenderer=new o.TextRenderer,this._bdLabelRenderer=new o.TextRenderer,this._xdLabelRenderer=new o.TextRenderer,this._textRendererALabel=new o.TextRenderer,this._textRendererBLabel=new o.TextRenderer,this._textRendererCLabel=new o.TextRenderer,this._textRendererDLabel=new o.TextRenderer,this._textRendererXLabel=new o.TextRenderer,this._renderer=null}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){if(super._updateImpl(e),this._updateBaseData(),this._renderer=null,this._points.length<2)return;const t=this._source.properties().childs(),i=new s.CompositeRenderer,o=(e,i)=>({points:[e],text:i,color:t.textcolor.value(),vertAlign:"middle",horzAlign:"center",font:_.CHART_FONT_FAMILY,offsetX:0,offsetY:0,bold:t.bold&&t.bold.value(),italic:t.italic&&t.italic.value(),fontsize:t.fontsize.value(),backgroundColor:t.color.value(),backgroundRoundRect:4}),a=(e,i)=>({points:[e,i],color:t.color.value(),linewidth:1,linestyle:n.LINESTYLE_DOTTED,extendleft:!1,extendright:!1,leftend:r.LineEnd.Normal,rightend:r.LineEnd.Normal}),[l,h,c,u,p]=this._points,g={points:[l,h,this._points.length<3?h:c],color:"rgba(0, 0, 0, 0)",linewidth:t.linewidth.value(),backcolor:t.backgroundColor.value(),fillBackground:t.fillBackground.value(),transparency:t.transparency.value()};if(this._mainTriangleRenderer.setData(g),i.append(this._mainTriangleRenderer),this._points.length>3){const e={points:[c,u,5===this._points.length?p:u],color:"rgba(0, 0, 0, 0)",linewidth:t.linewidth.value(),backcolor:t.backgroundColor.value(),fillBackground:t.fillBackground.value(),transparency:t.transparency.value()};this._triangleRendererPoints234.setData(e),i.append(this._triangleRendererPoints234)}const x={points:this._points,color:t.color.value(),linewidth:t.linewidth.value(),backcolor:t.backgroundColor.value(),fillBackground:!1,linestyle:n.LINESTYLE_SOLID,filled:!1};this._polylineRenderer.setData(x),i.append(this._polylineRenderer);const f=(0,d.getNumericFormatter)();if(this._points.length>=3){const e=o(l.add(c).scaled(.5),f.format(this._abRetracement));this._xbLabelRenderer.setData(e),i.append(this._xbLabelRenderer),this._xbTrend.setData(a(l,c)),i.append(this._xbTrend)}if(this._points.length>=4){
this._bcRetracementTrend.setData(a(h,u)),i.append(this._bcRetracementTrend);const e=o(h.add(u).scaled(.5),f.format(this._bcRetracement));this._acLabelRenderer.setData(e),i.append(this._acLabelRenderer)}if(this._points.length>=5){const e=o(c.add(p).scaled(.5),f.format(this._cdRetracement));this._bdLabelRenderer.setData(e),i.append(this._bdLabelRenderer),this._xdRetracementTrend.setData(a(l,p)),i.append(this._xdRetracementTrend);const t=o(l.add(p).scaled(.5),f.format(this._xdRetracement));this._xdLabelRenderer.setData(t),i.append(this._xdLabelRenderer),this._bdTrend.setData(a(c,p)),i.append(this._bdTrend)}const v=o(l,"X");h.y>l.y?(v.vertAlign="bottom",v.offsetY=5):(v.vertAlign="top",v.offsetY=5),this._textRendererXLabel.setData(v),i.append(this._textRendererXLabel);const T=o(h,"A");if(h.y<l.y?(T.vertAlign="bottom",T.offsetY=5):(T.vertAlign="top",T.offsetY=5),this._textRendererALabel.setData(T),i.append(this._textRendererALabel),this._points.length>2){const e=o(c,"B");c.y<h.y?(e.vertAlign="bottom",e.offsetY=5):(e.vertAlign="top",e.offsetY=5),this._textRendererBLabel.setData(e),i.append(this._textRendererBLabel)}if(this._points.length>3){const e=o(u,"C");u.y<c.y?(e.vertAlign="bottom",e.offsetY=5):(e.vertAlign="top",e.offsetY=5),this._textRendererCLabel.setData(e),i.append(this._textRendererCLabel)}if(this._points.length>4){const e=o(p,"D");p.y<u.y?(e.vertAlign="bottom",e.offsetY=5):(e.vertAlign="top",e.offsetY=5),this._textRendererDLabel.setData(e),i.append(this._textRendererDLabel)}this.addAnchors(i),this._renderer=i}_updateBaseData(){if(this._source.points().length>=3){const[e,t,i]=this._source.points();this._abRetracement=Math.round(1e3*Math.abs((i.price-t.price)/(t.price-e.price)))/1e3}if(this._source.points().length>=4){const[,e,t,i]=this._source.points();this._bcRetracement=Math.round(1e3*Math.abs((i.price-t.price)/(t.price-e.price)))/1e3}if(this._source.points().length>=5){const[e,t,i,n,r]=this._source.points();this._cdRetracement=Math.round(1e3*Math.abs((r.price-n.price)/(n.price-i.price)))/1e3,this._xdRetracement=Math.round(1e3*Math.abs((r.price-t.price)/(t.price-e.price)))/1e3}}}},76441:(e,t,i)=>{"use strict";i.r(t),i.d(t,{ABCDPaneView:()=>_});var n=i(51056),r=i(95201),s=i(43290),o=i(91046),a=i(17330),l=i(49857),d=i(74011),h=i(56468),c=i(27916),u=i(15938);class _ extends c.LineSourcePaneView{constructor(){super(...arguments),this._abRetracementTrend=new o.TrendLineRenderer,this._cdRetracementTrend=new o.TrendLineRenderer,this._polylineRenderer=new d.PolygonRenderer(new h.HitTestResult(h.HitTarget.MovePoint)),this._abLabelRenderer=new a.TextRenderer,this._cdLabelRenderer=new a.TextRenderer,this._textRendererALabel=new a.TextRenderer,this._textRendererBLabel=new a.TextRenderer,this._textRendererCLabel=new a.TextRenderer,this._textRendererDLabel=new a.TextRenderer,this._renderer=null}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){if(super._updateImpl(e),this._points.length<2)return void(this._renderer=null)
;const t=this._source.properties().childs(),i=new r.CompositeRenderer,o=(e,i)=>({points:[e],text:i,color:t.textcolor.value(),vertAlign:"middle",horzAlign:"center",font:u.CHART_FONT_FAMILY,offsetX:0,offsetY:0,bold:t.bold&&t.bold.value(),italic:t.italic&&t.italic.value(),fontsize:t.fontsize.value(),backgroundColor:t.color.value(),backgroundRoundRect:4}),a=(e,i)=>({points:[e,i],color:t.color.value(),linewidth:t.linewidth.value(),linestyle:n.LINESTYLE_DOTTED,extendleft:!1,extendright:!1,leftend:l.LineEnd.Normal,rightend:l.LineEnd.Normal}),[d,h,c,_]=this._points,p={points:this._points,color:t.color.value(),linewidth:t.linewidth.value(),linestyle:n.LINESTYLE_SOLID,fillBackground:!1,filled:!1,backcolor:"rgba(0, 0, 0, 0)"};this._polylineRenderer.setData(p),i.append(this._polylineRenderer);const g=o(d,"A");h.y>d.y?(g.vertAlign="bottom",g.offsetY=5):(g.vertAlign="top",g.offsetY=5),this._textRendererALabel.setData(g),i.append(this._textRendererALabel);const x=o(h,"B");if(h.y<d.y?(x.vertAlign="bottom",x.offsetY=5):(x.vertAlign="top",x.offsetY=5),this._textRendererBLabel.setData(x),i.append(this._textRendererBLabel),this._points.length>2){const e=o(c,"C");c.y<h.y?(e.vertAlign="bottom",e.offsetY=5):(e.vertAlign="top",e.offsetY=5),this._textRendererCLabel.setData(e),i.append(this._textRendererCLabel)}if(this._points.length>3){const e=o(_,"D");_.y<c.y?(e.vertAlign="bottom",e.offsetY=5):(e.vertAlign="top",e.offsetY=5),this._textRendererDLabel.setData(e),i.append(this._textRendererDLabel)}const f=(0,s.getNumericFormatter)();if(this._points.length>=3){this._abRetracementTrend.setData(a(d,c)),i.append(this._abRetracementTrend);const e=d.add(c).scaled(.5),[t,n,r]=this._source.points(),s=Math.round(1e3*Math.abs((r.price-n.price)/(n.price-t.price)))/1e3,l=o(e,f.format(s));this._abLabelRenderer.setData(l),i.append(this._abLabelRenderer)}if(this._points.length>=4){this._cdRetracementTrend.setData(a(h,_)),i.append(this._cdRetracementTrend);const e=h.add(_).scaled(.5),[,t,n,r]=this._source.points(),s=Math.round(1e3*Math.abs((r.price-n.price)/(n.price-t.price)))/1e3,l=o(e,f.format(s));this._cdLabelRenderer.setData(l),i.append(this._cdLabelRenderer)}this.addAnchors(i),this._renderer=i}}},15190:(e,t,i)=>{"use strict";i.r(t),i.d(t,{ArcPaneView:()=>p});var n=i(4652),r=i(86441),s=i(25422),o=i(95201),a=i(36036),l=i(27916),d=i(19063),h=i(56468),c=i(75919),u=i(61993);class _ extends c.MediaCoordinatesPaneRenderer{constructor(){super(...arguments),this._data=null}setData(e){this._data={...e,angleFrom:0,angleTo:Math.PI,clockwise:!1}}hitTest(e){if(null===this._data||this._data.points.length<3)return null;const t=(0,u.interactionTolerance)().curve,i=this._data.points[0],o=this._data.points[1];let a=this._data.points[2],l=(0,n.distanceToLine)(i,o,a).distance;if(l<1)return l=(0,n.distanceToLine)(i,o,e).distance,l<t?new h.HitTestResult(h.HitTarget.MovePoint):null;const d=o.subtract(i),c=d.length(),_=i.add(o).scaled(.5);let p=a.subtract(_).normalized();a=_.add(p.scaled(l));const g=d.x/c,x=d.y/c;let f=Math.acos(g);x<0&&(f=-f);let v=(0,
s.translationMatrix)(-i.x,-i.y);e=(0,s.transformPoint)(v,e),v=(0,s.rotationMatrix)(-f),e=(0,s.transformPoint)(v,e),p=(0,s.transformPoint)(v,p);const T=1-Math.sqrt(3)/2;if(v=(0,s.scalingMatrix)(1,c*T/l),e=(0,s.transformPoint)(v,e),p=(0,s.transformPoint)(v,p),e.y*p.y<0)return null;let w;w=e.y<0?new r.Point(.5*c,c*Math.sqrt(3)/2):new r.Point(.5*c,-c*Math.sqrt(3)/2);const R=e.subtract(w).length();return Math.abs(R-c)<=t?new h.HitTestResult(h.HitTarget.MovePoint):null}_drawImpl(e){if(null===this._data||this._data.points.length<2)return;const t=e.context,i=this._data.points[0],o=this._data.points[1];if(this._data.points.length<3)return t.strokeStyle=this._data.color,t.lineWidth=this._data.linewidth,t.beginPath(),t.moveTo(i.x,i.y),t.lineTo(o.x,o.y),void t.stroke();let a=this._data.points[2];const l=(0,n.distanceToLine)(i,o,a).distance;if(l<1)return t.strokeStyle=this._data.color,t.lineWidth=this._data.linewidth,t.beginPath(),t.moveTo(i.x,i.y),t.lineTo(o.x,o.y),void t.stroke();const h=o.subtract(i),c=i.add(o).scaled(.5),u=new r.Point(-h.y,h.x).normalized();a=c.add(u.scaled(l)),t.strokeStyle=this._data.color,t.lineWidth=this._data.linewidth;const _=h.length(),p=h.x/_,g=h.y/_;let x=Math.acos(p);g<0&&(x=-x);let f=this._data.points[2],v=(0,s.translationMatrix)(-c.x,-c.y);f=(0,s.transformPoint)(v,f),v=(0,s.rotationMatrix)(-x),f=(0,s.transformPoint)(v,f),v=(0,s.scalingMatrix)(1,_/(2*l)),f=(0,s.transformPoint)(v,f),f.y<0?this._data.clockwise=!0:this._data.clockwise=!1,t.save(),t.beginPath(),t.translate(i.x,i.y),t.rotate(x);const T=1-Math.sqrt(3)/2;t.scale(1,l/(_*T)),this._data.clockwise?t.arc(.5*_,_*Math.sqrt(3)/2,_,-2*Math.PI/3,-Math.PI/3,!1):t.arc(.5*_,-_*Math.sqrt(3)/2,_,Math.PI/3,2*Math.PI/3,!1),t.restore(),t.stroke(),this._data.fillBackground&&(t.fillStyle=(0,d.generateColor)(this._data.backcolor,this._data.transparency),t.fill())}}class p extends l.LineSourcePaneView{constructor(){super(...arguments),this._arcRenderer=new _,this._renderer=null}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){if(super._updateImpl(e),this._renderer=null,0===this._points.length)return;const t=this._source.properties().childs(),i={points:this._points,color:t.color.value(),linewidth:t.linewidth.value(),backcolor:t.backgroundColor.value(),fillBackground:t.fillBackground.value(),transparency:t.transparency.value()};this._arcRenderer.setData(i);const d=new o.CompositeRenderer;if(this._renderer=d,d.append(this._arcRenderer),1===i.points.length)return;const h=[],c=i.points[0],u=(0,a.anchor)({x:c.x,y:c.y,pointIndex:0});h.push(u);const _=i.points[1],p=(0,a.anchor)({x:_.x,y:_.y,pointIndex:1});if(2===i.points.length)return void this.addAnchors(d);h.push(p);let g=i.points[2];const x=(0,n.distanceToLine)(c,_,g).distance,f=_.subtract(c),v=c.add(_).scaled(.5),T=new r.Point(-f.y,f.x).normalized();g=v.add(T.scaled(x));const w=v.add(T.scaled(-x)),R=f.length(),m=f.x/R,y=f.y/R;let b=Math.acos(m);y<0&&(b=-b);let L=i.points[2],P=(0,s.translationMatrix)(-v.x,-v.y);L=(0,s.transformPoint)(P,L),P=(0,s.rotationMatrix)(-b),L=(0,
s.transformPoint)(P,L),P=(0,s.scalingMatrix)(1,R/(2*x)),L=(0,s.transformPoint)(P,L);const S=(0,a.anchor)({x:L.y>=0?g.x:w.x,y:L.y>=0?g.y:w.y,pointIndex:2,cursorType:(0,l.thirdPointCursorType)(c,_)});h.push(S),d.append(this.createLineAnchor({points:h},0))}}},54176:(e,t,i)=>{"use strict";i.r(t),i.d(t,{ArrowMarkPaneView:()=>_});var n,r=i(15938),s=i(27916),o=i(95201),a=i(11064),l=i(17330),d=i(56468),h=i(34026),c=i(9859);!function(e){e[e.ArrowHeadWidth=19.5]="ArrowHeadWidth",e[e.ArrowHeadHeight=12]="ArrowHeadHeight",e[e.ArrowTailWidth=10]="ArrowTailWidth",e[e.ArrowTailHeight=10]="ArrowTailHeight"}(n||(n={}));class u{constructor(){this._data=null}setData(e){this._data=e}draw(e,t){if(null!==this._data){switch(e.save(),e.fillStyle=this._data.color,this._data.direction){case"up":case"down":!function(e,t,i,n){const{horizontalPixelRatio:r,verticalPixelRatio:s}=n,o=Math.max(1,Math.floor(r))%2?.5:0,a="up"===i?1:-1,l=a*Math.round(12*s),d=(0,c.ceiledEven)(19.5*r)/2+o,h=a*Math.round(10*s),u=(0,c.ceiledEven)(10*r)/2+o,_=Math.round(t.x*r)+o,p=Math.round(t.y*s);e.beginPath(),e.moveTo(_,p),e.lineTo(_+d,p+l),e.lineTo(_+u,p+l),e.lineTo(_+u,p+l+h),e.lineTo(_-u,p+l+h),e.lineTo(_-u,p+l),e.lineTo(_-d,p+l),e.moveTo(_,p),e.fill()}(e,this._data.point,this._data.direction,t);break;case"left":case"right":!function(e,t,i,n){const{horizontalPixelRatio:r,verticalPixelRatio:s}=n,o=Math.max(1,Math.floor(r))%2?.5:0,a="left"===i?1:-1,l=a*Math.round(12*s)+o,d=(0,c.ceiledEven)(19.5*r)/2+o,h=a*Math.round(22*s)+o,u=(0,c.ceiledEven)(10*r)/2+o,_=Math.round(t.x*r)+o,p=Math.round(t.y*s)+o;e.beginPath(),e.moveTo(_,p),e.lineTo(_+l,p+d),e.lineTo(_+l,p+u),e.lineTo(_+h,p+u),e.lineTo(_+h,p-u),e.lineTo(_+l,p-u),e.lineTo(_+l,p-d),e.moveTo(_,p),e.fill()}(e,this._data.point,this._data.direction,t)}e.restore()}}hitTest(e){if(null===this._data)return null;let t,i,n,r;switch(this._data.direction){case"up":t=this._data.point.x-9.75,n=t+19.5,i=this._data.point.y,r=i+12+10;break;case"down":t=this._data.point.x-9.75,n=t+19.5,r=this._data.point.y,i=r-12-10;break;case"left":t=this._data.point.x,n=t+12+10,i=this._data.point.y-9.75,r=i+19.5;break;case"right":n=this._data.point.x,t=n-12-10,i=this._data.point.y-9.75,r=i+19.5}return e.x<t||e.x>n||e.y<i||e.y>r?null:new d.HitTestResult(d.HitTarget.MovePoint)}doesIntersectWithBox(e){return null!==this._data&&(0,h.pointInBox)(this._data.point,e)}}class _ extends s.LineSourcePaneView{constructor(){super(...arguments),this._arrowMarkRenderer=new u,this._textRenderer=new l.TextRenderer,this._renderer=null,this._anchorsOffset=null}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){if(super._updateImpl(e),this._renderer=null,1!==this._points.length)return;const t=this._getSource(),i=t.properties().childs(),n=this._getModel();this._arrowMarkRenderer.setData({point:this._points[0],direction:t.direction(),color:i.arrowColor.value()}),this._renderer=new o.CompositeRenderer,this._renderer.append(this._arrowMarkRenderer),""!==i.text.value()&&i.showLabel.value()&&(this._textRenderer.setData({points:this._points,
font:r.CHART_FONT_FAMILY,bold:i.bold.value(),italic:i.italic.value(),fontSize:i.fontsize.value(),text:i.text.value(),color:i.color.value(),...t.textAlignParams()}),this._renderer.append(this._textRenderer));const s=[this._anchorsOffset?this._points[0].add(this._anchorsOffset):this._points[0].clone()];this._renderer.append(new a.SelectionRenderer({points:s,bgColors:this._lineAnchorColors(s),visible:this.areAnchorsVisible(),barSpacing:n.timeScale().barSpacing(),hittestResult:d.HitTarget.MovePoint}))}}},34823:(e,t,i)=>{"use strict";i.r(t),i.d(t,{ArrowMarkerPaneView:()=>x});var n,r=i(27916),s=i(95201),o=i(17330),a=i(86441),l=i(75919),d=i(56468),h=i(61993);function c(e){if(e<92)return 18;let t=.25*e;return t=Math.min(t,106),t=Math.max(t,18),t=Math.min(t,.9*e),t}!function(e){e[e.LengthToLineWidthCoeff=.02]="LengthToLineWidthCoeff",e[e.MinLineWidth=2]="MinLineWidth",e[e.MaxLineWidth=5]="MaxLineWidth",e[e.ThresholdLineLength=92]="ThresholdLineLength",e[e.MinimalArrowLength=18]="MinimalArrowLength",e[e.MinimalLineLength=22]="MinimalLineLength",e[e.MaximumArrowLength=106]="MaximumArrowLength",e[e.LengthToArrowLengthCoeff=.25]="LengthToArrowLengthCoeff",e[e.MaximalPercentOfArrow=.9]="MaximalPercentOfArrow",e[e.ArrowWidthToHeight=1.22]="ArrowWidthToHeight",e[e.ArrowBackStep=.1]="ArrowBackStep",e[e.ArrowBackStepLengthLimit=35]="ArrowBackStepLengthLimit"}(n||(n={}));class u extends l.MediaCoordinatesPaneRenderer{constructor(e){super(),this._data=e}setData(e){this._data=e}hitTest(e){if(this._data.points.length<2)return null;let t=this._data.points[0],i=this._data.points[1].subtract(t);const n=i.length();i=this._data.points[1].subtract(this._data.points[0]);i.length()<22&&(t=this._data.points[1].addScaled(i.normalized(),-22),i=this._data.points[1].subtract(t));const r=e.subtract(t),s=i.dotProduct(r)/n;if(s<0||s>n)return null;const o=i.scaled(1/n),a=t.addScaled(o,s),l=e.subtract(a),c=(0,h.interactionTolerance)().line,u=this._hittestGeometry(n);for(let e=u.length-2;e>=0;e--){const t=u[e];if(s>=t.x){const i=u[e+1],n=i.x-t.x,r=i.y-t.y,o=(s-t.x)/n,a=t.y+r*o;return l.length()<=a+c?new d.HitTestResult(d.HitTarget.MovePoint):null}}return l.length()<3?new d.HitTestResult(d.HitTarget.MovePoint):null}_drawImpl(e){if(this._data.points.length<2)return;const t=e.context;t.fillStyle=this._data.color,t.strokeStyle=this._data.color,t.lineJoin="round",t.lineCap="round";let i=this._data.points[1].subtract(this._data.points[0]);const n=i.length();let r=this._data.points[0];n<22&&(r=this._data.points[1].addScaled(i.normalized(),-22),i=this._data.points[1].subtract(r));const s=new a.Point(i.y,-i.x).normalized(),o=this._arrowGeometry(i.length()),l=i.normalized();t.lineWidth=function(e){let t=Math.round(.02*e);return t=Math.min(t,5),t=Math.max(t,2),t}(i.length()),t.beginPath(),t.moveTo(r.x,r.y);for(let e=0;e<o.length;e++){const i=o[e],n=r.addScaled(l,i.x).addScaled(s,i.y);t.lineTo(n.x,n.y)}t.lineTo(this._data.points[1].x,this._data.points[1].y);for(let e=o.length-1;e>=0;e--){const i=o[e],n=r.addScaled(l,i.x).addScaled(s,-i.y);t.lineTo(n.x,n.y)}
t.lineTo(r.x,r.y),t.stroke(),t.fill()}_arrowGeometry(e){const t=c(e),i=[],n=e>=35?.1:0;return i.push(new a.Point(0,0)),i.push(new a.Point(e-t+t*n,1.22*t/4)),i.push(new a.Point(e-t,1.22*t/2)),i.push(new a.Point(e,0)),i}_hittestGeometry(e){const t=c(e),i=[];return i.push(new a.Point(0,0)),i.push(new a.Point(e-t,1.22*t/4)),i.push(new a.Point(e-t,1.22*t/2)),i.push(new a.Point(e,0)),i}}var _,p=i(36155),g=i(15938);!function(e){e[e.CircleRadius=9]="CircleRadius"}(_||(_={}));class x extends r.LineSourcePaneView{constructor(e,t){super(e,t),this._textRendererData={text:"",color:"",vertAlign:"middle",horzAlign:"center",font:"",offsetX:10,offsetY:10,points:[],forceTextAlign:!0},this._arrowRendererData={points:[],color:""},this._ellipseRendererData={color:"",linewidth:0,points:[],fillBackground:!0,backcolor:"",noHitTestOnBackground:!0},this._drawAsCircle=!1,this._textRenderer=new o.TextRenderer(this._textRendererData),this._arrowRenderer=new u(this._arrowRendererData),this._ellipseRenderer=new p.EllipseRendererSimple(this._ellipseRendererData)}renderer(e){this._invalidated&&this._updateImpl(e);const t=new s.CompositeRenderer;this._drawAsCircle?t.append(this._ellipseRenderer):t.append(this._arrowRenderer);const i=this._getSource().properties().childs();return this._textRendererData.points&&this._textRendererData.points.length>0&&i.showLabel.value()&&(this._textRenderer.setData({...this._textRendererData}),t.append(this._textRenderer)),this.addAnchors(t),t}_updateImpl(e){super._updateImpl(e);const t=this._getPoints(),i=this._getSource().properties().childs();if(this._arrowRendererData.color=i.backgroundColor.value(),this._arrowRendererData.points=t,this._textRendererData.text=i.text.value(),this._textRendererData.color=i.textColor.value(),this._textRendererData.font=g.CHART_FONT_FAMILY,this._textRendererData.bold=i.bold.value(),this._textRendererData.italic=i.italic.value(),this._textRendererData.fontsize=i.fontsize.value(),t.length>=2){const e=this._getSource().points(),n=e[0].index-e[1].index,r=e[0].price-e[1].price;if(this._drawAsCircle=0===n&&Math.abs(r)<1e-8,this._textRendererData.points=[t[0]],this._drawAsCircle){this._textRendererData.horzAlign="left",this._textRendererData.vertAlign="middle";const e=new a.Point(t[0].x-9,t[0].y-9),n=new a.Point(t[0].x+9,t[0].y+9);this._ellipseRendererData.points=[e,n],this._ellipseRendererData.backcolor=i.backgroundColor.value(),this._ellipseRendererData.color=i.backgroundColor.value()}else{const e=t[1].subtract(t[0]);Math.abs(e.x)>=Math.abs(e.y)?(t[1].x>t[0].x?this._textRendererData.horzAlign="right":this._textRendererData.horzAlign="left",this._textRendererData.vertAlign="middle"):(t[1].y>t[0].y?this._textRendererData.vertAlign="bottom":this._textRendererData.vertAlign="top",this._textRendererData.horzAlign="center")}}}}},82462:(e,t,i)=>{"use strict";i.r(t),i.d(t,{BalloonPaneView:()=>f});var n,r=i(68979),s=i(19063),o=i(15938),a=i(27916),l=i(86441),d=i(34026),h=i(63273),c=i(7114),u=i(75919),_=i(56468);!function(e){e[e.Radius=15]="Radius",
e[e.TailApexXOffsetFromTextStart=20]="TailApexXOffsetFromTextStart",e[e.TailHeight=9]="TailHeight"}(n||(n={}));class p extends u.MediaCoordinatesPaneRenderer{constructor(){super(...arguments),this._geometryCache={innerHeight:NaN,textHorizontalPadding:NaN,innerWidth:NaN,paddingLeft:NaN},this._geomertryCacheInvalidated=!0,this._data=null}setData(e){this._data=e,this._geomertryCacheInvalidated=!0}hitTest(e){if(null===this._data||0===this._data.points.length)return null;const t=this._data.points[0].x-(this._geometryCache.paddingLeft+20),i=this._data.points[0].y-(this._geometryCache.innerHeight+9),n=(0,l.box)(new l.Point(t,i),new l.Point(t+this._geometryCache.innerWidth,i+this._geometryCache.innerHeight));return(0,d.pointInBox)(e,n)?new _.HitTestResult(_.HitTarget.MovePoint,{areaName:_.AreaName.Text}):null}_drawImpl(e){if(null===this._data||0===this._data.points.length)return;const t=e.context;t.font=this._data.font;const i=this._measureInfo(t,this._data.label,this._data.fontSize),{paddingLeft:n,innerHeight:r,innerWidth:s,textHorizontalPadding:o}=i;t.textAlign=(0,h.isRtl)()?"right":"left";const a=this._data.points[0].x-(n+20),l=this._data.points[0].y-(r+9);t.translate(a,l),t.beginPath(),t.moveTo(24,r),t.lineTo(15,r),t.arcTo(-1e3,0,1e3,0,r/2),t.lineTo(s-15,0),t.arcTo(1e3,r,-1e3,r,r/2),t.lineTo(33,r),t.quadraticCurveTo(33,r+4,35,r+9),t.quadraticCurveTo(27,r+6,24,r),t.fillStyle=this._data.backgroundColor,t.fill(),t.strokeStyle=this._data.borderColor,t.lineWidth=2,t.stroke(),t.closePath(),t.textBaseline="middle",t.fillStyle=this._data.color,t.fillText(this._data.label,n+o,r/2)}_measureInfo(e,t,i){if(this._geomertryCacheInvalidated){const n=e.measureText(t),r=i,s=15,o=Math.round(r/1.3),a=n.width+2*s,l=r+2*o,d=(0,c.calcTextHorizontalShift)(e,n.width);this._geometryCache={paddingLeft:s,innerWidth:a,innerHeight:l,textHorizontalPadding:d},this._geomertryCacheInvalidated=!1}return this._geometryCache}}var g=i(95201),x=i(11064);class f extends a.LineSourcePaneView{constructor(e,t){super(e,t),this._balloonRenderer=new p,this._renderer=null}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){super._updateImpl(e);const t=this._source.properties().childs(),i={points:this._points,color:t.color.value(),borderColor:t.borderColor.value(),backgroundColor:(0,s.generateColor)(t.backgroundColor.value(),t.transparency.value()),font:(0,r.makeFont)(t.fontsize.value(),o.CHART_FONT_FAMILY),fontSize:t.fontsize.value(),label:t.text.value()};if(this._balloonRenderer.setData(i),1===i.points.length){const e=new g.CompositeRenderer;return e.append(this._balloonRenderer),e.append(new x.SelectionRenderer({points:i.points,bgColors:this._lineAnchorColors(i.points),visible:this.areAnchorsVisible(),barSpacing:this._model.timeScale().barSpacing(),hittestResult:_.HitTarget.MovePoint})),void(this._renderer=e)}this._renderer=this._balloonRenderer}}},52666:(e,t,i)=>{"use strict";i.r(t),i.d(t,{BarsPatternPaneView:()=>v})
;var n=i(86441),r=i(19625),s=i(51056),o=i(19063),a=i(56468),l=i(95201),d=i(49857),h=i(74010),c=i(62189),u=i(91046),_=i(95173),p=i(27916),g=i(67467);const x=r.colorsPalette["color-cold-gray-500"],f={[g.LineToolBarsPatternMode.Bars]:e=>[e[2],e[3]],[g.LineToolBarsPatternMode.Line]:e=>e[4],[g.LineToolBarsPatternMode.OpenClose]:e=>[e[1],e[4]],[g.LineToolBarsPatternMode.LineOpen]:e=>e[1],[g.LineToolBarsPatternMode.LineHigh]:e=>e[2],[g.LineToolBarsPatternMode.LineLow]:e=>e[3],[g.LineToolBarsPatternMode.LineHL2]:e=>(e[2]+e[3])/2};class v extends p.LineSourcePaneView{constructor(){super(...arguments),this._vertLineRenderer1=new _.VerticalLineRenderer,this._vertLineRenderer2=new _.VerticalLineRenderer,this._medianRenderer=new u.TrendLineRenderer,this._renderer=null}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){var t,i;if(super._updateImpl(e),this._renderer=null,this._points.length<2)return;const r=this._source.priceScale(),u=null!==(i=null===(t=this._source.ownerSource())||void 0===t?void 0:t.firstValue())&&void 0!==i?i:null;if(!r||r.isEmpty()||null===u)return;const _=this._source.points(),p=this._source.pattern(),v=p.length,T=new l.CompositeRenderer;if(v>0&&2===_.length){const e=this._source.properties().childs(),t=e.mode.value(),i=e.color.value(),l=Math.abs((this._points[0].x-this._points[1].x)/(v-1)),d=this._source.getScale(),x=e=>r.priceToCoordinate(e,u)*d,[{index:w},{index:R}]=_,m=w<R?this._points[0]:this._points[1],[y,b]=this._source.points(),L=w<R?y.index:b.index,P=m.x,S=m.y-x(this._source.firstPatternPrice());if(t===g.LineToolBarsPatternMode.Bars||t===g.LineToolBarsPatternMode.OpenClose){const e=f[t];for(let t=0;t<v;t++){const r=Math.round(P+t*l+.5),s=e(p[t]).map(((e,t)=>new n.Point(r+(2*t-1),Math.round(x(e))+S))),o=new c.RectangleRenderer;o.setData({points:s,color:i,backcolor:i,linewidth:1,fillBackground:!0,transparency:10,extendLeft:!1,extendRight:!1}),T.append(o)}T.append(this.createLineAnchor({points:this._points},0))}else{const e=f[t],n=p.map(((t,i)=>{const n=Math.round(P+i*l+.5);return{timePointIndex:L+i,center:n,left:NaN,right:NaN,y:x(e(t))+S}}));T.append(new h.PaneRendererLine({barSpacing:l,items:n,lineColor:(0,o.generateColor)(i,10),lineStyle:s.LINESTYLE_SOLID,lineWidth:2,hittest:new a.HitTestResult(a.HitTarget.MovePoint),simpleMode:!0,withMarkers:!1,skipHoles:!0})),T.append(this.createLineAnchor({points:this._points},1))}}else this._vertLineRenderer1.setData({x:this._points[0].x,color:x,linewidth:1,linestyle:s.LINESTYLE_SOLID}),T.append(this._vertLineRenderer1),this._vertLineRenderer2.setData({x:this._points[1].x,color:x,linewidth:1,linestyle:s.LINESTYLE_SOLID}),T.append(this._vertLineRenderer2),this._medianRenderer.setData({points:this._points,color:x,linewidth:1,linestyle:s.LINESTYLE_SOLID,extendleft:!1,extendright:!1,leftend:d.LineEnd.Normal,rightend:d.LineEnd.Normal}),T.append(this._medianRenderer);this._renderer=T}}},12487:(e,t,i)=>{"use strict";i.r(t),i.d(t,{BezierCubicPaneView:()=>v})
;var n=i(19063),r=i(27916),s=i(75919),o=i(56468),a=i(64034),l=i(66825),d=i(91046),h=i(49857),c=i(82839),u=i(61993),_=i(37743);class p extends s.MediaCoordinatesPaneRenderer{constructor(e){super(),this._data=e||null}setData(e){this._data=e}hitTest(e){const t=this._data;if(null===t)return null;if(4===t.points.length){const i=(0,u.interactionTolerance)().curve,[n,r,s,a]=t.points,d=a.subtract(n),h=s.subtract(d.scaled(.25)),_=s.add(d.scaled(.25)),p=r.subtract(s),g=a.subtract(p.scaled(.25)),x=a.add(p.scaled(.25));if((0,l.quadroBezierHitTest)(s,n,h,e,i)||(0,l.cubicBezierHitTest)(s,_,g,a,e,i)||(0,l.quadroBezierHitTest)(a,r,x,e,i))return new o.HitTestResult(o.HitTarget.MovePoint);let f=(0,c.hitTestExtendedPoints)(e,i,t.extendLeftPoints);return null===f&&(f=(0,c.hitTestExtendedPoints)(e,i,t.extendRightPoints)),f}return null}_drawImpl(e){if(null===this._data)return;const t=e.context;t.lineCap="round",t.strokeStyle=this._data.color,t.lineWidth=this._data.lineWidth,(0,_.setLineStyle)(t,this._data.lineStyle);const i=this._data.points[0],n=this._data.points[1];if(2===this._data.points.length)t.beginPath(),t.moveTo(i.x,i.y),t.lineTo(n.x,n.y),t.stroke(),this._data.leftEnd===h.LineEnd.Arrow&&(0,d.drawArrow)(n,i,t,t.lineWidth,a.dpr1PixelRatioInfo),this._data.rightEnd===h.LineEnd.Arrow&&(0,d.drawArrow)(i,n,t,t.lineWidth,a.dpr1PixelRatioInfo);else{const e=this._data.points[2],r=this._data.points[3],s=r.subtract(i),o=e.subtract(s.scaled(.25)),l=e.add(s.scaled(.25)),u=n.subtract(e),_=r.subtract(u.scaled(.25)),p=r.add(u.scaled(.25));this._data.fillBack&&this._data.points.length>2&&(t.fillStyle=this._data.backColor,t.beginPath(),t.moveTo(i.x,i.y),t.quadraticCurveTo(o.x,o.y,e.x,e.y),t.bezierCurveTo(l.x,l.y,_.x,_.y,r.x,r.y),t.quadraticCurveTo(p.x,p.y,n.x,n.y),t.fill()),t.beginPath(),(0,c.buildExtendedSegments)(t,this._data.extendLeftPoints),t.moveTo(i.x,i.y),t.quadraticCurveTo(o.x,o.y,e.x,e.y),t.bezierCurveTo(l.x,l.y,_.x,_.y,r.x,r.y),t.quadraticCurveTo(p.x,p.y,n.x,n.y),(0,c.buildExtendedSegments)(t,this._data.extendRightPoints),this._data.leftEnd===h.LineEnd.Arrow&&(0,d.drawArrow)(o,i,t,t.lineWidth,a.dpr1PixelRatioInfo),this._data.rightEnd===h.LineEnd.Arrow&&(0,d.drawArrow)(p,n,t,t.lineWidth,a.dpr1PixelRatioInfo),t.stroke()}}}var g=i(95201),x=i(38581),f=i(50151);class v extends r.LineSourcePaneView{constructor(){super(...arguments),this._bezierCubicRenderer=new p,this._renderer=null,this._extendedSegmentLeftCache=null,this._extendedSegmentRightCache=null}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){if(super._updateImpl(e),this._renderer=null,this._points.length<2)return;const t=this._source.properties().childs();let i=[],r=[];if(4===this._source.points().length){const n=(0,f.ensureNotNull)(this._source.pointToScreenPoint(this._source.points()[0])),s=(0,f.ensureNotNull)(this._source.pointToScreenPoint(this._source.points()[1])),o=(0,f.ensureNotNull)(this._source.pointToScreenPoint(this._source.points()[2])),a=(0,
f.ensureNotNull)(this._source.pointToScreenPoint(this._source.points()[3])),l=a.subtract(n),d=o.subtract(l.scaled(.25)),h=s.subtract(o),c=a.add(h.scaled(.25)),{mediaSize:{width:u,height:_}}=e;t.extendLeft.value()&&(i=this._extendSegmentLeft(o,n,d,u,_)),t.extendRight.value()&&(r=this._extendSegmentRight(a,s,c,u,_))}const s=this._points.slice(),o=this._source.controlPoints();null!==o&&(s.push((0,f.ensureNotNull)(this._source.pointToScreenPoint(o[0]))),s.push((0,f.ensureNotNull)(this._source.pointToScreenPoint(o[1]))));const a={points:s,color:t.linecolor.value(),lineWidth:t.linewidth.value(),lineStyle:t.linestyle.value(),leftEnd:t.leftEnd.value(),rightEnd:t.rightEnd.value(),fillBack:t.fillBackground.value(),backColor:(0,n.generateColor)(t.backgroundColor.value(),t.transparency.value()),extendLeftPoints:i,extendRightPoints:r};this._bezierCubicRenderer.setData(a);const l=new g.CompositeRenderer;l.append(this._bezierCubicRenderer),this.addAnchors(l),this._renderer=l}_extendSegmentLeft(e,t,i,n,r){return(0,x.cacheIsValid)(this._extendedSegmentLeftCache,e,t,i,n,r)||(this._extendedSegmentLeftCache={p1:e,p2:t,p3:i,width:n,height:r,segment:(0,l.extendQuadroBezier)(e,t,i,n,r)}),(0,f.ensureNotNull)(this._extendedSegmentLeftCache).segment}_extendSegmentRight(e,t,i,n,r){return(0,x.cacheIsValid)(this._extendedSegmentRightCache,e,t,i,n,r)||(this._extendedSegmentRightCache={p1:e,p2:t,p3:i,width:n,height:r,segment:(0,l.extendQuadroBezier)(e,t,i,n,r)}),(0,f.ensureNotNull)(this._extendedSegmentRightCache).segment}}},38581:(e,t,i)=>{"use strict";i.r(t),i.d(t,{BezierQuadroPaneView:()=>h,cacheIsValid:()=>d});var n=i(50151),r=i(19063),s=i(27916),o=i(95201),a=i(66825),l=i(82839);function d(e,t,i,n,r,s){return null!==e&&e.p1.x===t.x&&e.p1.y===t.y&&e.p2.x===i.x&&e.p2.y===i.y&&e.p3.x===n.x&&e.p3.y===n.y&&e.width===r&&e.height===s}class h extends s.LineSourcePaneView{constructor(){super(...arguments),this._bezierQuadroRenderer=new l.BezierQuadroRenderer,this._renderer=null,this._extendedSegmentLeftCache=null,this._extendedSegmentRightCache=null}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){if(super._updateImpl(e),this._renderer=null,this._points.length<2)return;const t=this._source.properties().childs();let i=[],s=[];if(3===this._source.points().length){const r=(0,n.ensureNotNull)(this._source.pointToScreenPoint(this._source.points()[0])),o=(0,n.ensureNotNull)(this._source.pointToScreenPoint(this._source.points()[1])),a=(0,n.ensureNotNull)(this._source.pointToScreenPoint(this._source.points()[2])),l=o.subtract(r),d=a.subtract(l.scaled(.25)),h=a.add(l.scaled(.25)),{mediaSize:{width:c,height:u}}=e;t.extendLeft.value()&&(i=this._extendSegmentLeft(a,r,d,c,u)),t.extendRight.value()&&(s=this._extendSegmentRight(a,o,h,c,u))}const a=this._points.slice(),l=this._source.controlPoint();null!==l&&a.push((0,n.ensureNotNull)(this._source.pointToScreenPoint(l)));const d={points:a,color:t.linecolor.value(),lineWidth:t.linewidth.value(),lineStyle:t.linestyle.value(),leftEnd:t.leftEnd.value(),rightEnd:t.rightEnd.value(),
fillBack:t.fillBackground.value(),backColor:(0,r.generateColor)(t.backgroundColor.value(),t.transparency.value()),extendLeftSegments:i,extendRightSegments:s};this._bezierQuadroRenderer.setData(d);const h=new o.CompositeRenderer;h.append(this._bezierQuadroRenderer),this.addAnchors(h),this._renderer=h}_extendSegmentLeft(e,t,i,r,s){return d(this._extendedSegmentLeftCache,e,t,i,r,s)||(this._extendedSegmentLeftCache={p1:e,p2:t,p3:i,width:r,height:s,segment:(0,a.extendQuadroBezier)(e,t,i,r,s)}),(0,n.ensureNotNull)(this._extendedSegmentLeftCache).segment}_extendSegmentRight(e,t,i,r,s){return d(this._extendedSegmentRightCache,e,t,i,r,s)||(this._extendedSegmentRightCache={p1:e,p2:t,p3:i,width:r,height:s,segment:(0,a.extendQuadroBezier)(e,t,i,r,s)}),(0,n.ensureNotNull)(this._extendedSegmentRightCache).segment}}},82839:(e,t,i)=>{"use strict";i.d(t,{BezierQuadroRenderer:()=>p,buildExtendedSegments:()=>_,hitTestExtendedPoints:()=>u});var n=i(4652),r=i(75919),s=i(49857),o=i(56468),a=i(64034),l=i(66825),d=i(91046),h=i(61993),c=i(37743);function u(e,t,i){for(const r of i)for(let i=1;i<r.length;i++){const s=r[i-1],a=r[i];if((0,n.distanceToSegment)(s,a,e).distance<t)return new o.HitTestResult(o.HitTarget.MovePoint)}return null}function _(e,t){for(let i=0;i<t.length;i++){const n=t[i],r=n[0];e.moveTo(r.x,r.y);for(let t=1;t<n.length;t++){const i=n[t];e.lineTo(i.x,i.y)}}}class p extends r.MediaCoordinatesPaneRenderer{constructor(e){super(),this._data=e||null}setData(e){this._data=e}hitTest(e){if(null!==this._data&&3===this._data.points.length){const t=(0,h.interactionTolerance)().curve,[i,n,r]=this._data.points,s=n.subtract(i),a=r.subtract(s.scaled(.25)),d=r.add(s.scaled(.25));if((0,l.quadroBezierHitTest)(r,i,a,e,t)||(0,l.quadroBezierHitTest)(r,n,d,e,t))return new o.HitTestResult(o.HitTarget.MovePoint);let c=u(e,t,this._data.extendLeftSegments);return null===c&&(c=u(e,t,this._data.extendRightSegments)),c}return null}_drawImpl(e){if(null===this._data)return;const[t,i,n]=this._data.points,r=e.context;if(r.lineCap="round",r.strokeStyle=this._data.color,r.lineWidth=this._data.lineWidth,(0,c.setLineStyle)(r,this._data.lineStyle),2===this._data.points.length)r.beginPath(),r.moveTo(t.x,t.y),r.lineTo(i.x,i.y),r.stroke();else{const e=i.subtract(t),o=n.subtract(e.scaled(.25)),l=n.add(e.scaled(.25));this._data.fillBack&&this._data.points.length>2&&(r.fillStyle=this._data.backColor,r.beginPath(),r.moveTo(t.x,t.y),r.quadraticCurveTo(o.x,o.y,n.x,n.y),r.quadraticCurveTo(l.x,l.y,i.x,i.y),r.fill()),r.beginPath(),_(r,this._data.extendLeftSegments),r.moveTo(t.x,t.y),r.quadraticCurveTo(o.x,o.y,n.x,n.y),r.quadraticCurveTo(l.x,l.y,i.x,i.y),_(r,this._data.extendRightSegments),this._data.leftEnd===s.LineEnd.Arrow&&(0,d.drawArrow)(o,t,r,r.lineWidth,a.dpr1PixelRatioInfo),this._data.rightEnd===s.LineEnd.Arrow&&(0,d.drawArrow)(l,i,r,r.lineWidth,a.dpr1PixelRatioInfo),r.stroke()}}}},70849:(e,t,i)=>{"use strict";i.d(t,{BrushBasePaneView:()=>d});var n=i(86441),r=i(74011),s=i(11064),o=i(95201),a=i(56468),l=i(27916);class d extends l.LineSourcePaneView{constructor(){
super(...arguments),this._polygonRenderer=new r.PolygonRenderer,this._renderer=new o.CompositeRenderer}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){super._updateImpl(e);const t=Math.max(1,this._source.smooth()),i=this._points;if(0===i.length)return void this._renderer.clear();const n=[i[0]];for(let e=1;e<i.length;e++){const r=i[e].subtract(i[e-1]),s=r.length(),o=Math.min(5,Math.floor(s/t)),a=r.normalized().scaled(s/o);for(let t=0;t<o-1;t++)n.push(i[e-1].add(a.scaled(t)));n.push(i[e])}this._points=this._smoothArray(n,t);const r=this._createPolygonRendererData();if(this._polygonRenderer.setData(r),this._renderer=new o.CompositeRenderer,this._renderer.append(this._polygonRenderer),this._source.finished()){const e=r.points.length;if(e>0){const t=1!==e?[r.points[0],r.points[e-1]]:[r.points[0]],i=new s.SelectionRenderer({points:t,bgColors:this._lineAnchorColors(t),visible:this.areAnchorsVisible(),hittestResult:a.HitTarget.Regular,barSpacing:this._getModel().timeScale().barSpacing()});this._renderer.append(i)}}}_smoothArray(e,t){if(1===e.length)return e;const i=new Array(e.length);for(let r=0;r<e.length;r++){let s=new n.Point(0,0);for(let i=0;i<t;i++){const t=Math.max(r-i,0),n=Math.min(r+i,e.length-1);s=s.add(e[t]),s=s.add(e[n])}i[r]=s.scaled(.5/t)}return i.push(e[e.length-1]),i}}},68716:(e,t,i)=>{"use strict";i.r(t),i.d(t,{BrushPaneView:()=>s});var n=i(51056),r=i(70849);class s extends r.BrushBasePaneView{_createPolygonRendererData(){const e=this._source.properties().childs(),t={points:this._points,color:e.linecolor.value(),linewidth:e.linewidth.value(),linestyle:n.LINESTYLE_SOLID,skipClosePath:!0,leftend:e.leftEnd.value(),rightend:e.rightEnd.value(),filled:!1,fillBackground:!1,backcolor:e.backgroundColor.value()};return e.fillBackground.value()&&this._model.lineBeingCreated()!==this._source&&(t.filled=!0,t.fillBackground=!0,t.transparency=e.transparency.value()),t}}},31463:(e,t,i)=>{"use strict";i.r(t),i.d(t,{CalloutPaneView:()=>R});var n=i(86441),r=i(50151),s=i(68979),o=i(95201),a=i(15938),l=i(32211),d=i(2844),h=i(56468),c=i(36036),u=i(17330),_=i(63273),p=i(19063),g=i(7114),x=i(75919),f=i(64034),v=i(36696);class T extends x.MediaCoordinatesPaneRenderer{constructor(e){super(),this._data=null,this._textInfoCache=null,this._hitTest=e||new h.HitTestResult(h.HitTarget.MovePoint,{areaName:h.AreaName.Text})}data(){return this._data}setData(e){null===this._data||this._data.textData.lines===e.textData.lines&&this._data.textData.font===e.textData.font&&this._data.textData.maxWidth===e.textData.maxWidth||(this._textInfoCache=null),this._data=e}setCursorType(e){this._hitTest.mergeData({cursorType:e})}hitTest(e){if(null===this._data||this._data.points.length<2)return null;const t=this._data.points[0],i=this._data.points[1];if(t.subtract(e).length()<3)return new h.HitTestResult(h.HitTarget.ChangePoint);const n=(0,r.ensureNotNull)(this._calcTextSize()),s=i.x-n.totalWidth/2,o=i.y-n.totalHeight/2;return e.x>=s&&e.x<=s+n.totalWidth&&e.y>=o&&e.y<=o+n.totalHeight?this._hitTest:null}getTextInfo(){
const e=(0,r.ensureNotNull)(this._data),t=(0,_.isRtl)()?"right":"left",i=e.points[1].clone(),{totalWidth:n,totalHeight:s}=(0,r.ensureNotNull)(this._calcTextSize()),o=i.x-n/2,a=i.y-s/2;return{font:e.textData.font,fontSize:e.textData.fontSize,lineHeight:Math.ceil(e.textData.fontSize),lineSpacing:0,textTop:a+10,textBottom:a+s-10,textLeft:o+10,textRight:o+n-10,textAlign:t}}positionToCoordinate(e,t){const i=(0,r.ensureNotNull)(this._data),n=this.getTextInfo(),{x:s,y:o,lineNumber:a}=(0,v.getSymbolCoordinatesInfo)({symbolPosition:t,textWidth:n.textRight-n.textLeft,textByLines:i.textData.linesIncludingHidden,lineHeight:i.textData.fontSize,font:i.textData.font,textAlign:n.textAlign});return{x:s+n.textLeft,y:o+n.textTop,lineNumber:a}}_drawImpl(e){if(null===this._data||this._data.points.length<2)return;const t=this._data.points[0].clone(),i=this._data.points[1].clone(),n=e.context;n.lineCap="round",n.strokeStyle=this._data.bordercolor,n.lineWidth=this._data.linewidth,n.textBaseline="bottom",n.font=this._data.textData.font;const{textWidth:s,textHeight:o,totalWidth:a,totalHeight:l}=(0,r.ensureNotNull)(this._calcTextSize()),d=i.x-a/2,h=i.y-l/2;let c=0;const u=s+4>16,x=o+4>16;n.textAlign=(0,_.isRtl)()?"right":"left";const f=(0,g.calcTextHorizontalShift)(n,s);t.x>d+a?c=20:t.x>d&&(c=10),t.y>h+l?c+=2:t.y>h&&(c+=1),n.translate(d,h),t.x-=d,t.y-=h,i.x-=d,i.y-=h,n.beginPath(),n.moveTo(8,0),10===c?u?(n.lineTo(i.x-8,0),n.lineTo(t.x,t.y),n.lineTo(i.x+8,0),n.lineTo(a-8,0)):(n.lineTo(t.x,t.y),n.lineTo(a-8,0)):n.lineTo(a-8,0),20===c?(n.lineTo(t.x,t.y),n.lineTo(a,8)):n.arcTo(a,0,a,8,8),21===c?x?(n.lineTo(a,i.y-8),n.lineTo(t.x,t.y),n.lineTo(a,i.y+8),n.lineTo(a,l-8)):(n.lineTo(t.x,t.y),n.lineTo(a,l-8)):n.lineTo(a,l-8),22===c?(n.lineTo(t.x,t.y),n.lineTo(a-8,l)):n.arcTo(a,l,a-8,l,8),12===c?u?(n.lineTo(i.x+8,l),n.lineTo(t.x,t.y),n.lineTo(i.x-8,l),n.lineTo(8,l)):(n.lineTo(t.x,t.y),n.lineTo(8,l)):n.lineTo(8,l),2===c?(n.lineTo(t.x,t.y),n.lineTo(0,l-8)):n.arcTo(0,l,0,l-8,8),1===c?x?(n.lineTo(0,i.y+8),n.lineTo(t.x,t.y),n.lineTo(0,i.y-8),n.lineTo(0,8)):(n.lineTo(t.x,t.y),n.lineTo(0,8)):n.lineTo(0,8),0===c?(n.lineTo(t.x,t.y),n.lineTo(8,0)):n.arcTo(0,0,8,0,8),n.stroke(),n.fillStyle=(0,p.generateColor)(this._data.backcolor,this._data.transparency),n.fill(),n.translate(-d,-h),this._drawSelectionIfNeeded(n),n.fillStyle=this._data.color;let v=h+8+2+this._data.textData.fontSize;const T=d+8+2+f;for(const e of this._data.textData.lines)n.fillText(e.text,T,v),v+=this._data.textData.fontSize}_calcTextSize(){if(null===this._data||this._data.points.length<2)return null;if(null===this._textInfoCache){const e=this._data.textData.fontSize*this._data.textData.lines.length,t=this._data.textData.maxWidth,i=2*2+2*8;this._textInfoCache={textWidth:t,textHeight:e,totalWidth:t+i,totalHeight:e+i}}return this._textInfoCache}_drawSelectionIfNeeded(e){const t=(0,r.ensureNotNull)(this._data),i=t.textData.fontSize;if(t.selectionHighlight){const n=this.positionToCoordinate(!1,t.selectionHighlight.start),r=this.positionToCoordinate(!1,t.selectionHighlight.end),s=this.getTextInfo();(0,
v.drawSelection)(e,f.dpr1PixelRatioInfo,{lines:t.textData.lines,selectionStart:n,selectionEnd:r,left:s.textLeft,right:s.textRight,color:t.selectionHighlight.color,font:t.textData.font,lineHeight:i})}}}let w=null;class R extends l.InplaceTextLineSourcePaneView{constructor(e,t,i,n,r){super(e,t,i,n,r),this._renderer=new o.CompositeRenderer,this._textWidthCache=new d.TextWidthCache,this._calloutRenderer=new T(new h.HitTestResult(h.HitTarget.MovePoint,(0,l.inplaceEditHandlers)(this._tryActivateEditMode.bind(this,null)))),this._source.setAdditionalCursorData((()=>({color:this._source.editableTextStyle().cursorColor,...this._calloutRenderer.getTextInfo()})),this._calloutRenderer.positionToCoordinate.bind(this._calloutRenderer,!0))}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){if(super._updateImpl(e),this._source.calculatePoint2(),this._renderer.clear(),!this._points[0])return void this.closeTextEditor();if(this._points.length<2)return void this.closeTextEditor();const t=this._source.properties().childs(),i=this._points[0],s=i.x+this._source.getBarOffset()*this._model.timeScale().barSpacing(),o=new n.Point(s,this._points[1].y),a=this._fontStyle(),l=t.wordWrap.value()?t.wordWrapWidth.value():void 0,d=(0,u.wordWrap)(this._textData(),a,this._textWidthCache,!1,l),h=d.filter((e=>!e.hidden));let _;_=void 0!==l?l:h.reduce(((e,t)=>Math.max(e,function(e,t){if(null===w){const e=document.createElement("canvas");e.width=0,e.height=0,w=(0,r.ensureNotNull)(e.getContext("2d"))}return w.font=t,w.measureText(e).width}(t.text,a))),0);const p={points:[i,o],color:this._textColor(),linewidth:t.linewidth.value(),backcolor:t.backgroundColor.value(),transparency:t.transparency.value(),textData:{originalText:this._textData(),linesIncludingHidden:d,lines:h,maxWidth:_,font:a,fontSize:t.fontsize.value()},bordercolor:t.bordercolor.value(),...this._inplaceTextHighlight()};if(this._calloutRenderer.setData(p),this._renderer.append(this._calloutRenderer),this._updateInplaceText(this._calloutRenderer.getTextInfo()),this._renderer.append(this.createLineAnchor({points:[i]},0)),void 0!==l){const e=p.points[1],t=(0,c.anchor)({x:e.x+l/2+8+2,y:e.y,pointIndex:1});this._renderer.append(this.createLineAnchor({points:[t]},1))}this._calloutRenderer.setCursorType(this._textCursorType())}_fontStyle(){const e=this._source.properties().childs(),t=(e.bold.value()?"bold ":"")+(e.italic.value()?"italic ":""),i=e.fontsize.value();return(0,s.makeFont)(i,a.CHART_FONT_FAMILY,t)}}},38401:(e,t,i)=>{"use strict";i.r(t),i.d(t,{CirclePaneView:()=>_});var n=i(15938),r=i(95201),s=i(56468),o=i(13075),a=i(36036),l=i(32211),d=i(62689),h=i(34026),c=i(61993);class u{constructor(e){this._data=null!=e?e:null}setData(e){this._data=e}draw(e,t){if(null===this._data)return;const{center:i,radius:n,lineWidth:r,color:s,backColor:o}=this._data;e.save();const{horizontalPixelRatio:a,verticalPixelRatio:l}=t,d=Math.max(1,Math.floor(a)),h=d%2/2,c=Math.round(i.x*a)+h,u=Math.round(i.y*l)+h,_=Math.round(c+n*a),p=Math.max(1,Math.floor(r*a)),g=_-c-p;g>0&&(e.fillStyle=o,
e.beginPath(),e.moveTo(c+g,u),e.arc(c,u,g,0,2*Math.PI,!1),e.fill());const x=Math.max(d/2,_-c-p/2);e.strokeStyle=s,e.lineWidth=p,e.beginPath(),e.moveTo(c+x,u),e.arc(c,u,x,0,2*Math.PI,!1),e.stroke(),e.restore()}hitTest(e){if(null===this._data)return null;const{center:t,radius:i,backgroundHitTarget:n}=this._data,r=(0,c.interactionTolerance)().curve;if(!(0,h.pointInCircle)(e,t,i+r))return null;const o=i>r&&(0,h.pointInCircle)(e,t,i-r)?null!=n?n:s.HitTarget.MovePointBackground:s.HitTarget.MovePoint;return new s.HitTestResult(o)}}class _ extends l.InplaceTextLineSourcePaneView{constructor(e,t,i,n,o){super(e,t,i,n,o),this._circleRenderer=new u,this._renderer=new r.CompositeRenderer,this._textRenderer=new d.LineToolTextRenderer(void 0,new s.HitTestResult(s.HitTarget.MovePoint,(0,l.inplaceEditHandlers)(this._tryActivateEditMode.bind(this,null)))),this._source.setAdditionalCursorData((()=>{var e;return{color:this._source.editableTextStyle().cursorColor,rotationPoint:null!==(e=this._textRenderer.rotation())&&void 0!==e?e:void 0,...this._textRenderer.getTextInfo()}}),this._textRenderer.positionToCoordinate.bind(this._textRenderer))}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){var t;if(super._updateImpl(e),this._renderer.clear(),this._points.length<2)return;const i=this._source.properties().childs(),[r,l]=this._points;this._circleRenderer.setData({center:r,radius:Math.sqrt((l.x-r.x)**2+(l.y-r.y)**2),color:i.color.value(),lineWidth:i.linewidth.value(),backColor:i.fillBackground.value()?i.backgroundColor.value():"transparent",backgroundHitTarget:this._model.selection().isSelected(this._source)?s.HitTarget.MovePoint:void 0}),this._renderer.append(this._circleRenderer);const d=this._placeHolderMode();let h=!1;if((null===(t=i.showLabel)||void 0===t?void 0:t.value())&&i.text.value()||d||this._isTextEditMode()){const{fontSize:t,bold:i,italic:s}=this._source.properties().childs(),a=r.subtract(l).length()*Math.sqrt(2);this._textRenderer.setData({points:[r],text:this._textData(),color:this._textColor(),fontSize:t.value(),font:n.CHART_FONT_FAMILY,bold:i.value(),italic:s.value(),wordWrapWidth:d?void 0:a,maxHeight:d?void 0:a,offsetX:0,offsetY:0,horzAlign:"center",vertAlign:"middle",forceTextAlign:!0,decorator:d?o.PlusTextRendererDecorator.instance():void 0,...this._inplaceTextHighlight()});let c=!1;if(d&&this._textRenderer.measure().width>a&&(this._textRenderer.updateData({text:""}),c=this._textRenderer.measure().width>a),!c){this._textRenderer.setCursorType(this._textCursorType());const{mediaSize:{width:t,height:i}}=e;this._textRenderer.isOutOfScreen(t,i)?this.closeTextEditor():this._updateInplaceText(this._textRenderer.getTextInfo()),this._renderer.append(this._textRenderer),h=!0}}const c=[(0,a.anchor)({...l,pointIndex:1})];h||c.unshift((0,a.anchor)({...r,pointIndex:0,hitTarget:s.HitTarget.MovePoint})),this._renderer.append(this.createLineAnchor({points:c},0))}}},39157:(e,t,i)=>{"use strict";i.r(t),i.d(t,{CommentPaneView:()=>g})
;var n,r=i(19063),s=i(15938),o=i(56468),a=i(32211),l=i(62689),d=i(95201),h=i(86441),c=i(34026),u=i(75919),_=i(37743);class p extends u.MediaCoordinatesPaneRenderer{constructor(){super(...arguments),this._data=null}setData(e){this._data=e}hitTest(e){if(null===this._data||0===this._data.points.length)return null;const{points:[t],innerWidth:i,innerHeight:n}=this._data,r=t.x,s=t.y-n,a=(0,h.box)(new h.Point(r,s),new h.Point(r+i,s+n));return(0,c.pointInBox)(e,a)?new o.HitTestResult(o.HitTarget.MovePoint,{areaName:o.AreaName.Text}):null}_drawImpl(e){if(null===this._data||0===this._data.points.length)return;const t=e.context,{points:[i],innerWidth:n,innerHeight:r,backgroundColor:s,borderColor:o,borderRadius:a}=this._data,l=i.x,d=i.y-r;t.translate(l,d),(0,_.drawRoundRect)(t,0,0,n,r,[a,a,a,2]),t.fillStyle=s,t.fill(),t.strokeStyle=o,t.lineWidth=2,t.stroke(),t.closePath()}}!function(e){e[e.CommentTextLeft=12]="CommentTextLeft"}(n||(n={}));class g extends a.InplaceTextLineSourcePaneView{constructor(e,t,i,n,r){super(e,t,i,n,r),this._commentRenderer=new p,this._labelRenderer=new l.LineToolTextRenderer(void 0,new o.HitTestResult(o.HitTarget.MovePoint,(0,a.inplaceEditHandlers)(this._tryActivateEditMode.bind(this,null)))),this._renderer=new d.CompositeRenderer,this._source.setAdditionalCursorData((()=>({color:this._source.editableTextStyle().cursorColor,...this._labelRenderer.getTextInfo()})),this._labelRenderer.positionToCoordinate.bind(this._labelRenderer))}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){super._updateImpl(e),this._renderer.clear();const t=this._source.properties().childs(),i={...this._inplaceTextHighlight(),text:this._textData(),fontSize:t.fontsize.value(),offsetX:0,offsetY:0,points:this._points,vertAlign:"bottom",horzAlign:"left",horzTextAlign:"left",font:s.CHART_FONT_FAMILY,color:this._textColor(),boxPaddingVert:Math.round(t.fontsize.value()/1.3),boxPaddingHorz:12};this._labelRenderer.setData(i),this._labelRenderer.setCursorType(this._textCursorType());const{height:n,width:o}=e.mediaSize;this._labelRenderer.isOutOfScreen(o,n)?this.closeTextEditor():this._updateInplaceText(this._labelRenderer.getTextInfo());const a=this._labelRenderer.measure(),l={points:this._points,borderColor:t.borderColor.value(),backgroundColor:(0,r.generateColor)(t.backgroundColor.value(),t.transparency.value()),innerWidth:a.width,innerHeight:a.height,borderRadius:Math.min(a.width,this._labelRenderer.lineHeight()+2*Math.round(t.fontsize.value()/1.3))/2};this._commentRenderer.setData(l),this._renderer.append(this._commentRenderer),1===l.points.length&&this._renderer.append(this.createLineAnchor({points:l.points},0)),this._renderer.append(this._labelRenderer)}}},17551:(e,t,i)=>{"use strict";i.r(t),i.d(t,{CrossLinePaneView:()=>l});var n=i(27916),r=i(50600),s=i(95173),o=i(95201),a=i(56468);class l extends n.LineSourcePaneView{constructor(e,t){super(e,t),this._renderer=null,this._horizLineRenderer=new r.HorizontalLineRenderer,this._vertLineRenderer=new s.VerticalLineRenderer,
this._horizLineRenderer.setHitTest(new a.HitTestResult(a.HitTarget.MovePoint))}update(){this._invalidated=!0}renderer(e){return this._invalidated&&(this._updateImpl(e),this._invalidated=!1),this._renderer}_updateImpl(e){super._updateImpl(e),this._renderer=null;const t=this._getPoints();if(0===t.length)return;const i={color:this._getSource().lineColor(),linestyle:this._getSource().lineStyle(),linewidth:this._getSource().lineWidth(),x:t[0].x,y:t[0].y};this._horizLineRenderer.setData(i),this._horizLineRenderer.setHitTest(new a.HitTestResult(a.HitTarget.MovePoint,{snappingPrice:this._source.points()[0].price})),this._vertLineRenderer.setData(i),this._vertLineRenderer.setHitTest(new a.HitTestResult(a.HitTarget.MovePoint,{snappingIndex:this._source.points()[0].index}));const n=new o.CompositeRenderer;n.append(this._horizLineRenderer),n.append(this._vertLineRenderer),this.addAnchors(n),this._renderer=n}}},6074:(e,t,i)=>{"use strict";i.r(t),i.d(t,{LineToolCyclicLinesPaneView:()=>c});var n=i(95201),r=i(49857),s=i(91046),o=i(27916),a=i(95173),l=i(36036),d=i(56468),h=i(51056);class c extends o.LineSourcePaneView{constructor(){super(...arguments),this._coordinates=[],this._trendRenderer=new s.TrendLineRenderer,this._renderer=new n.CompositeRenderer}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){if(super._updateImpl(e),this._renderer.clear(),this._source.points().length<2)return;const t=this._model.timeScale(),i=this._source.priceScale();if(!i||i.isEmpty()||t.isEmpty())return;const n=this._source.points()[0],s=this._source.points()[1],o=s?s.index-n.index:1;if(this._coordinates=[],0===o)return;const c=t.visibleBarsStrictRange();if(null===c)return;if(o>0){for(let e=n.index;e<=c.lastBar();e+=o)this._coordinates.push(t.indexToCoordinate(e))}else{for(let e=n.index;e>=c.firstBar();e+=o)this._coordinates.push(t.indexToCoordinate(e))}if(this._points.length<2)return;const u=this._source.properties().childs(),_={points:this._points,color:"#808080",linewidth:1,linestyle:h.LINESTYLE_DASHED,extendleft:!1,extendright:!1,leftend:r.LineEnd.Normal,rightend:r.LineEnd.Normal};this._trendRenderer.setData(_),this._renderer.append(this._trendRenderer);for(let e=0;e<this._coordinates.length;e++){const t={x:this._coordinates[e],color:u.linecolor.value(),linewidth:u.linewidth.value(),linestyle:u.linestyle.value()},i=new a.VerticalLineRenderer;i.setData(t),this._renderer.append(i)}if(2===this._source.points().length){const e=this._points;this._renderer.append(this.createLineAnchor({points:e},0))}else this._renderer.append(this.createLineAnchor({points:[(0,l.anchor)({x:this._points[0].x,y:i.height()/2,hitTarget:d.HitTarget.MovePoint})]},1))}}},8198:(e,t,i)=>{"use strict";i.r(t),i.d(t,{CypherPaneView:()=>r});var n=i(77142);class r extends n.Pattern5pointsPaneView{_updateBaseData(){if(this._source.points().length>=3){const[e,t,i]=this._source.points();this._abRetracement=Math.round(1e3*Math.abs((i.price-t.price)/(t.price-e.price)))/1e3}if(this._source.points().length>=4){const[e,t,,i]=this._source.points()
;this._bcRetracement=Math.round(1e3*Math.abs((i.price-e.price)/(t.price-e.price)))/1e3}if(this._source.points().length>=5){const[e,,t,i,n]=this._source.points();this._cdRetracement=Math.round(1e3*Math.abs((n.price-i.price)/(i.price-t.price)))/1e3,this._xdRetracement=Math.round(1e3*Math.abs((n.price-i.price)/(e.price-i.price)))/1e3}}}},27436:(e,t,i)=>{"use strict";i.d(t,{DateAndPriceRangeBasePaneView:()=>a});var n=i(86441),r=i(15938),s=i(17330),o=i(27916);class a extends o.LineSourcePaneView{constructor(){super(...arguments),this._customTextrenderer=new s.TextRenderer}_updateCustomTextRenderer(e,t){const i=this._source.properties().childs().customText.childs();if(i.visible.value()&&i.text.value().length>0){const[o,a]=this._points,l=Math.round((o.y+a.y)/2),d=new n.Point(o.x,l),h=new n.Point(a.x,l),c=d.x<h.x?d:h,u=c===d?h:d,_="middle",p="center",g=new n.Point((d.x+h.x)/2,(d.y+h.y)/2),x=Math.atan((u.y-c.y)/(u.x-c.x)),f={points:[g],text:i.text.value(),color:i.color.value(),vertAlign:_,horzAlign:p,font:r.CHART_FONT_FAMILY,offsetX:0,offsetY:0,bold:i.bold.value(),italic:i.italic.value(),fontsize:i.fontsize.value(),forceTextAlign:!0,angle:x};return this._customTextrenderer.setData(f),this._needLabelExclusionPath(this._customTextrenderer,"middle")?(0,s.getTextBoundaries)(this._customTextrenderer,t,e):null}return this._customTextrenderer.setData(null),null}}},93955:(e,t,i)=>{"use strict";i.r(t),i.d(t,{DateAndPriceRangePaneView:()=>m});var n=i(50151),r=i(86441),s=i(11542),o=i(63273),a=i(17330),l=i(62189),d=i(91046),h=i(95201),c=i(43290),u=i(51056),_=i(49857),p=i(92953),g=i(15938),x=i(27436);const f=new p.TimeSpanFormatter,v=(0,c.getPercentageFormatter)(),T=(0,c.getVolumeFormatter)(),w=s.t(null,void 0,i(41643)),R=s.t(null,{context:"study"},i(24261));class m extends x.DateAndPriceRangeBasePaneView{constructor(){super(...arguments),this._distanceLineRenderer=new d.TrendLineRenderer,this._distancePriceRenderer=new d.TrendLineRenderer,this._backgroundRenderer=new l.RectangleRenderer,this._borderRenderer=new l.RectangleRenderer,this._textRenderer=new a.TextRenderer,this._renderer=new h.CompositeRenderer}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){var t,i,s;if(super._updateImpl(e),this._renderer.clear(),this._points.length<2||this._source.points().length<2)return;const l=this._source.properties().childs();l.fillBackground&&l.fillBackground.value()&&(this._backgroundRenderer.setData({points:this._points,color:"white",linewidth:0,backcolor:l.backgroundColor.value(),fillBackground:!0,transparency:l.backgroundTransparency.value(),extendLeft:!1,extendRight:!1}),this._renderer.append(this._backgroundRenderer));const[d,h]=this._points;l.drawBorder.value()&&(this._borderRenderer.setData({points:this._points,color:l.borderColor.value(),linewidth:l.borderWidth.value(),fillBackground:!1,extendLeft:!1,extendRight:!1,backcolor:""}),this._renderer.append(this._borderRenderer))
;const p=l.drawBorder.value()?l.borderWidth.value()/2:0,{mediaSize:{width:x,height:m}}=e,y=this._updateCustomTextRenderer(m,x),b=Math.round((d.y+h.y)/2),L=new r.Point(d.x+Math.sign(h.x-d.x)*p,b),P=new r.Point(h.x+Math.sign(d.x-h.x)*p,b);this._distanceLineRenderer.setData({points:[L,P],color:l.linecolor.value(),linewidth:l.linewidth.value(),linestyle:u.LINESTYLE_SOLID,extendleft:!1,extendright:!1,leftend:_.LineEnd.Normal,rightend:Math.abs(d.x-h.x)>=25*l.linewidth.value()?_.LineEnd.Arrow:_.LineEnd.Normal,excludeBoundaries:y?[y]:void 0}),this._renderer.append(this._distanceLineRenderer);const S=Math.round((d.x+h.x)/2),M=new r.Point(S,d.y+Math.sign(h.y-d.y)*p),C=new r.Point(S,h.y+Math.sign(d.y-h.y)*p);this._distancePriceRenderer.setData({points:[M,C],color:l.linecolor.value(),linewidth:l.linewidth.value(),linestyle:u.LINESTYLE_SOLID,extendleft:!1,extendright:!1,leftend:_.LineEnd.Normal,rightend:Math.abs(M.y-C.y)>=25*l.linewidth.value()?_.LineEnd.Arrow:_.LineEnd.Normal,excludeBoundaries:y?[y]:void 0}),this._renderer.append(this._distancePriceRenderer);const I=this._source.points()[0].price,A=this._source.points()[1].price,D=A-I,k=100*D/Math.abs(I),N=this._source.points()[0].index,B=this._source.points()[1].index,H=B-N,z=(0,o.forceLTRStr)(H+""),E=this._model.timeScale().indexToUserTime(N),W=this._model.timeScale().indexToUserTime(B);let O="";if(E&&W){const e=(W.valueOf()-E.valueOf())/1e3;O=", "+(0,o.startWithLTR)(f.format(e))}const V=this._model.mainSeries().symbolInfo(),F=V&&(0,c.getPipFormatter)(V),Y=(0,n.ensureNotNull)(this._source.ownerSource()).formatter(),j=(null!==(i=null===(t=Y.formatChange)||void 0===t?void 0:t.call(Y,A,I))&&void 0!==i?i:Y.format(D))+" ("+v.format(Math.round(100*k)/100)+") "+(F?F.format(D):"");let U=(0,o.forceLTRStr)(j)+"\n"+w.format({count:z})+O;const Q=this._source.volume();let q;Number.isNaN(Q)||(U+=`\n${R} ${T.format(Q)}`),q=A>I?new r.Point(.5*(d.x+h.x),h.y-2*l.fontsize.value()):new r.Point(.5*(d.x+h.x),h.y+.7*l.fontsize.value());const X={x:0,y:10},Z=l.fontsize.value(),G={points:[q],text:U,color:l.textcolor.value(),font:g.CHART_FONT_FAMILY,offsetX:X.x,offsetY:X.y,padding:8,vertAlign:"middle",horzAlign:"center",fontsize:Z,backgroundRoundRect:4,boxPaddingHorz:.4*Z+Z/3,boxPaddingVert:.2*Z+Z/3};(null===(s=l.fillLabelBackground)||void 0===s?void 0:s.value())&&(G.boxShadow={shadowColor:l.shadow.value(),shadowBlur:4,shadowOffsetY:1},G.backgroundColor=l.labelBackgroundColor.value()),this._textRenderer.setData(G);const J=this._textRenderer.measure(),$=(0,a.calculateLabelPosition)(J,d,h,X,m);this._textRenderer.setPoints([$]),this._renderer.append(this._textRenderer),this._renderer.append(this._customTextrenderer),this.addAnchors(this._renderer)}_needLabelExclusionPath(e){return e.getLinesInfo().lines.length>0}}},33406:(e,t,i)=>{"use strict";i.r(t),i.d(t,{DateRangePaneView:()=>T});var n=i(86441),r=i(11542),s=i(63273),o=i(17330),a=i(62189),l=i(91046),d=i(95201),h=i(51056),c=i(49857),u=i(92953),_=i(43290),p=i(15938),g=i(27436);const x=(0,_.getVolumeFormatter)(),f=r.t(null,void 0,i(41643)),v=r.t(null,{
context:"study"},i(24261));class T extends g.DateAndPriceRangeBasePaneView{constructor(){super(...arguments),this._leftBorderRenderer=new l.TrendLineRenderer,this._rightBorderRenderer=new l.TrendLineRenderer,this._distancePriceRenderer=new l.TrendLineRenderer,this._backgroundRenderer=new a.RectangleRenderer,this._textRenderer=new o.TextRenderer,this._renderer=new d.CompositeRenderer}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){var t;if(super._updateImpl(e),this._renderer.clear(),this._points.length<2||this._source.points().length<2)return;const i=this._source.properties().childs(),r=i.extendTop.value(),a=i.extendBottom.value(),[l,d]=this._points,_=r?0:Math.min(l.y,d.y),g=a?this._height():Math.max(l.y,d.y);i.fillBackground.value()&&(this._backgroundRenderer.setData({points:[new n.Point(l.x,_),new n.Point(d.x,g)],color:"white",linewidth:0,backcolor:i.backgroundColor.value(),fillBackground:!0,transparency:i.backgroundTransparency.value(),extendLeft:!1,extendRight:!1}),this._renderer.append(this._backgroundRenderer));const T=(e,t,n)=>{e.setData({points:[t,n],color:i.linecolor.value(),linewidth:i.linewidth.value(),linestyle:h.LINESTYLE_SOLID,extendleft:!1,extendright:!1,leftend:c.LineEnd.Normal,rightend:c.LineEnd.Normal}),this._renderer.append(e)};T(this._leftBorderRenderer,new n.Point(l.x,_),new n.Point(l.x,g)),T(this._rightBorderRenderer,new n.Point(d.x,_),new n.Point(d.x,g));const w=Math.round((l.y+d.y)/2),R=new n.Point(l.x,w),m=new n.Point(d.x,w),{mediaSize:{width:y,height:b}}=e,L=this._updateCustomTextRenderer(b,y);this._distancePriceRenderer.setData({points:[R,m],color:i.linecolor.value(),linewidth:i.linewidth.value(),linestyle:h.LINESTYLE_SOLID,extendleft:!1,extendright:!1,leftend:c.LineEnd.Normal,rightend:Math.abs(R.x-m.x)>=15*i.linewidth.value()?c.LineEnd.Arrow:c.LineEnd.Normal,excludeBoundaries:L?[L]:void 0}),this._renderer.append(this._distancePriceRenderer);const P=this._source.points()[0].index,S=this._source.points()[1].index,M=S-P,C=this._model.timeScale().indexToUserTime(P),I=this._model.timeScale().indexToUserTime(S);let A="";if(C&&I){const e=(I.valueOf()-C.valueOf())/1e3;A=", "+(0,s.startWithLTR)((new u.TimeSpanFormatter).format(e))}const D=this._source.volume(),k=Number.isNaN(D)?"":`\n${v} ${x.format(D)}`,N=f.format({count:(0,s.forceLTRStr)(M.toString())})+A+k,B={x:0,y:10},H=i.fontsize.value(),z={text:N,color:i.textcolor.value(),font:p.CHART_FONT_FAMILY,offsetX:B.x,offsetY:B.y,padding:8,vertAlign:"middle",horzAlign:"center",fontsize:H,backgroundRoundRect:4,boxPaddingHorz:.4*H+H/3,boxPaddingVert:.2*H+H/3};(null===(t=i.fillLabelBackground)||void 0===t?void 0:t.value())&&(z.boxShadow={shadowColor:i.shadow.value(),shadowBlur:4,shadowOffsetY:1},z.backgroundColor=i.labelBackgroundColor.value()),this._textRenderer.setData(z);const E=this._textRenderer.measure(),W=(0,o.calculateLabelPosition)(E,l,d,B,b);this._textRenderer.setPoints([W]),this._renderer.append(this._textRenderer),this._renderer.append(this._customTextrenderer),this.addAnchors(this._renderer)}}},
41376:(e,t,i)=>{"use strict";i.r(t),i.d(t,{DisjointChannelPaneView:()=>p});var n=i(50151),r=i(86441),s=i(49483),o=i(62317),a=i(91046),l=i(17330),d=i(95201),h=i(72791),c=i(15938),u=i(27916),_=i(36036);class p extends u.LineSourcePaneView{constructor(){super(...arguments),this._trendLineRendererPoints12=new a.TrendLineRenderer,this._trendLineRendererPoints43=new a.TrendLineRenderer,this._disjointChannelRenderer=new o.DisjointChannelRenderer,this._p1LabelRenderer=new l.TextRenderer,this._p2LabelRenderer=new l.TextRenderer,this._p3LabelRenderer=new l.TextRenderer,this._p4LabelRenderer=new l.TextRenderer,this._labelTextRenderer=new l.TextRenderer,this._renderer=new d.CompositeRenderer}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){var t;if(super._updateImpl(e),this._renderer.clear(),this._source.points().length<2)return;const i=this._source.priceScale(),o=null===(t=this._source.ownerSource())||void 0===t?void 0:t.firstValue();if(!i||null==o)return;const[a,l,d]=this._source.points(),u=i.formatPrice(a.price,o),p=i.formatPrice(l.price,o);let g,x;if(d){g=i.formatPrice(d.price,o);const e=l.price-a.price;x=i.formatPrice(d.price+e,o)}if(this._points.length<2)return;const f=this._source.properties().childs(),[v,T]=this._points;let w,R;if(this._points.length>=3){w=(0,r.point)(T.x,this._points[2].y);const e=T.y-v.y;if(R=(0,r.point)(v.x,w.y+e),f.fillBackground.value()){const e=f.extendLeft.value(),t=f.extendRight.value();this._disjointChannelRenderer.setData({extendleft:e,extendright:t,points:[v,T,w,R],backcolor:f.backgroundColor.value(),transparency:f.transparency.value(),hittestOnBackground:s.CheckMobile.any()}),this._renderer.append(this._disjointChannelRenderer)}f.labelVisible.value()&&f.labelText.value()&&this._renderer.append(this._getLabelTextRenderer(v,T,R,w))}const m=(e,t)=>({points:[e,t],color:f.linecolor.value(),linewidth:f.linewidth.value(),linestyle:f.linestyle.value(),extendleft:f.extendLeft.value(),extendright:f.extendRight.value(),leftend:f.leftEnd.value(),rightend:f.rightEnd.value()}),y=(e,t,i,n,r,s)=>{f.showPrices.value()&&(e.setData({points:[i],text:r,color:f.textcolor.value(),horzAlign:i.x>n.x?"left":"right",vertAlign:"middle",font:c.CHART_FONT_FAMILY,offsetX:6,offsetY:0,boxPadding:0,bold:f.bold.value(),italic:f.italic.value(),fontsize:f.fontsize.value(),forceTextAlign:!0}),this._renderer.append(e),t.setData({points:[n],text:s,color:f.textcolor.value(),horzAlign:i.x<n.x?"left":"right",vertAlign:"middle",font:c.CHART_FONT_FAMILY,offsetX:6,offsetY:0,boxPadding:0,bold:f.bold.value(),italic:f.italic.value(),fontsize:f.fontsize.value(),forceTextAlign:!0}),this._renderer.append(t))};if(this._trendLineRendererPoints12.setData(m(v,T)),this._renderer.append(this._trendLineRendererPoints12),y(this._p1LabelRenderer,this._p2LabelRenderer,v,T,u,p),!w||!R)return void this.addAnchors(this._renderer);this._trendLineRendererPoints43.setData(m(R,w)),this._renderer.append(this._trendLineRendererPoints43),y(this._p3LabelRenderer,this._p4LabelRenderer,w,R,(0,n.ensureDefined)(g),(0,
n.ensureDefined)(x));const b=[(0,_.anchor)({...v}),(0,_.anchor)({...T}),(0,_.anchor)({...w,cursorType:h.PaneCursorType.VerticalResize,pointIndex:2,square:!0}),(0,_.anchor)({...R,pointIndex:3})];this._model.lineBeingCreated()===this._source&&b.pop(),this._renderer.append(this.createLineAnchor({points:b},0)),v&&T&&this._addAlertRenderer(this._renderer,[v,T])}_getLabelTextRenderer(e,t,i,n){const r=this._source.properties().childs();let s,o;const a=r.labelFontSize.value()/3;let l=0;switch(r.labelVertAlign.value()){case"bottom":e.y<i.y?(s=e,o=t):(s=i,o=n);break;case"top":e.y>i.y?(s=e,o=t):(s=i,o=n);break;case"middle":s=e.add(i).scaled(.5),o=t.add(n).scaled(.5),l=a}const d=s.x<o.x?s:o,h=d===s?o:s;let u;switch(r.labelHorzAlign.value()){case"left":u=d;break;case"right":u=h;break;default:u=d.add(h).scaled(.5)}return this._labelTextRenderer.setData({points:[u],color:r.labelTextColor.value(),fontSize:r.labelFontSize.value(),text:r.labelText.value(),font:c.CHART_FONT_FAMILY,bold:r.labelBold.value(),italic:r.labelItalic.value(),vertAlign:r.labelVertAlign.value(),horzAlign:r.labelHorzAlign.value(),offsetX:0,offsetY:0,boxPaddingVert:a,boxPaddingHorz:l,forceTextAlign:!0,angle:Math.atan((d.y-h.y)/(d.x-h.x))}),this._labelTextRenderer}}},98424:(e,t,i)=>{"use strict";i.r(t),i.d(t,{ElliottLabelsPaneView:()=>v});var n=i(27916),r=i(95201),s=i(9859),o=i(19063),a=i(51056),l=i(56468),d=i(86441),h=i(34026),c=i(7114),u=i(68979);class _{constructor(e,t){this._data=e,this._hitTestResult=t}hitTest(e){const t=this._center(),i=this._data.circleRadius,n={min:new d.Point(t.x-i,t.y-i),max:new d.Point(t.x+i,t.y+i)};return(0,h.pointInBox)(e,n)?this._hitTestResult:null}draw(e,t){e.save();const{horizontalPixelRatio:i,verticalPixelRatio:n}=t,r=Math.max(1,Math.floor(i))%2/2,s=this._center(),o=Math.round(s.x*i)+r,a=Math.round(s.y*n)+r;if(this._data.showCircle){const t=Math.round(o+this._data.circleRadius*i)-o-this._data.circleBorderWidth*i/2;e.strokeStyle=this._data.color,e.lineWidth=this._data.circleBorderWidth*i,e.beginPath(),e.moveTo(o+t,a),e.arc(o,a,t,0,2*Math.PI,!1),e.stroke()}e.font=(0,u.makeFont)(this._data.fontSize,this._data.font,this._data.bold?"bold":void 0),e.textBaseline="middle",e.textAlign="center",e.fillStyle=this._data.color,(0,c.drawScaled)(e,i,n,(()=>{e.fillText(this._data.letter,o/i,a/n+.05*this._data.fontSize)})),e.restore()}_center(){const e="bottom"===this._data.vertAlign?-1:1,t=this._data.point.y+e*this._data.yOffset+e*this._data.circleRadius,i=this._data.point.x;return new d.Point(i,t)}}var p=i(15938),g=i(36036),x=i(74011);const f={4:{font:24,circle:36,circleBorderWidth:1,bold:!0},3:{font:20,circle:28,circleBorderWidth:1,bold:!1},2:{font:18,circle:22,circleBorderWidth:1,bold:!1},1:{font:16,circle:22,circleBorderWidth:1,bold:!1},0:{font:11,circle:14,circleBorderWidth:1,bold:!0}};class v extends n.LineSourcePaneView{constructor(e,t){super(e,t),this._renderer=null,this._polylineRenderer=new x.PolygonRenderer}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){var t;super._updateImpl(e),
this._renderer=null;const i=this._source.properties().childs(),n=this._source.priceScale(),d=this._model.timeScale(),h=null===(t=this._source.ownerSource())||void 0===t?void 0:t.firstValue();if(!n||n.isEmpty()||d.isEmpty()||null==h)return;const c=new r.CompositeRenderer;if(i.showWave.value()){const e={points:this._points,color:(0,o.generateColor)(i.color.value(),0),linewidth:i.linewidth.value(),linestyle:a.LINESTYLE_SOLID,fillBackground:!1,filled:!1,backcolor:"rgba(0, 0, 0, 0)",linejoin:"round"};this._polylineRenderer.setData(e),c.append(this._polylineRenderer)}const u=this.areAnchorsVisible()?0:1;let x=1;if(this._points.length>2){const e=this._points[2],t=this._points[1];x=(0,s.sign)(e.y-t.y)}let v=0;this._model.lineBeingCreated()===this._source&&(v=1);const T=(0,o.resetTransparency)(i.color.value());for(let e=0;e<this._points.length-v;e++,x=-x){if(e<u)continue;const t=this._source.label(e);let i=t.label;const n="circle"===t.decoration;"brackets"===t.decoration&&(i="("+i+")");const r=f[t.group],s=new l.HitTestResult(l.HitTarget.ChangePoint,{pointIndex:e});c.append(new _({point:this._points[e],letter:i,color:T,font:p.CHART_FONT_FAMILY,fontSize:r.font,bold:r.bold,showCircle:n,circleRadius:r.circle/2,circleBorderWidth:r.circleBorderWidth,yOffset:10,vertAlign:1===x?"top":"bottom"},s))}const w=[];for(let e=0;e<this._points.length;e++)w.push((0,g.anchor)({...this._points[e],pointIndex:e}));this._model.lineBeingCreated()===this._source&&w.pop(),c.append(this.createLineAnchor({points:w},0)),this._renderer=c}}},75649:(e,t,i)=>{"use strict";i.r(t),i.d(t,{EllipsePaneView:()=>T});var n=i(4652),r=i(86441),s=i(5531),o=i(19063),a=i(15938),l=i(95201),d=i(36036),h=i(56468),c=i(13075),u=i(27916),_=i(32211),p=i(62689),g=i(25422),x=i(75919),f=i(61993);class v extends x.MediaCoordinatesPaneRenderer{constructor(){super(...arguments),this._data=null}setData(e){this._data={...e,angleFrom:0,angleTo:2*Math.PI,clockwise:!1}}hitTest(e){var t;if(null===this._data||this._data.points.length<3)return null;const i=this._data.points[0],s=this._data.points[1];let o=this._data.points[2];const a=(0,n.distanceToLine)(i,s,o).distance,l=s.subtract(i),d=i.add(s).scaled(.5),c=new r.Point(-l.y,l.x).normalized();o=d.add(c.scaled(a));const u=l.length(),_=l.x/u,p=l.y/u;let x=Math.acos(_);p<0&&(x=-x);let v=(0,g.translationMatrix)(-d.x,-d.y);e=(0,g.transformPoint)(v,e);let T=(0,g.transformPoint)(v,this._data.points[2]);v=(0,g.rotationMatrix)(-x),e=(0,g.transformPoint)(v,e),T=(0,g.transformPoint)(v,T),v=(0,g.scalingMatrix)(1,u/(2*a)),e=(0,g.transformPoint)(v,e),T=(0,g.transformPoint)(v,T);const w=e.length(),R=(0,f.interactionTolerance)().curve;return Math.abs(w-.5*u)<=R?new h.HitTestResult(h.HitTarget.MovePoint):!this._data.noHitTestOnBackground&&w<=.5*u?new h.HitTestResult(null!==(t=this._data.backgroundHitTarget)&&void 0!==t?t:h.HitTarget.MovePointBackground):null}_drawImpl(e){if(null===this._data||this._data.points.length<2)return;const t=this._data.points[0],i=this._data.points[1],s=e.context;if(this._data.points.length<3)return s.strokeStyle=this._data.color,
s.lineWidth=this._data.linewidth,s.beginPath(),s.moveTo(t.x,t.y),s.lineTo(i.x,i.y),void s.stroke();let o=this._data.points[2];const a=(0,n.distanceToLine)(t,i,o).distance;if(a<1)return s.strokeStyle=this._data.color,s.lineWidth=this._data.linewidth,s.beginPath(),s.moveTo(t.x,t.y),s.lineTo(i.x,i.y),void s.stroke();const l=i.subtract(t),d=t.add(i).scaled(.5),h=new r.Point(-l.y,l.x).normalized();o=d.add(h.scaled(a)),s.strokeStyle=this._data.color,s.lineWidth=this._data.linewidth;const c=l.length(),u=l.x/c,_=l.y/c;let p=Math.acos(u);_<0&&(p=-p);let x=this._data.points[2],f=(0,g.translationMatrix)(-d.x,-d.y);x=(0,g.transformPoint)(f,x),f=(0,g.rotationMatrix)(-p),x=(0,g.transformPoint)(f,x),f=(0,g.scalingMatrix)(1,c/(2*a)),x=(0,g.transformPoint)(f,x),x.y<0?this._data.clockwise=!0:this._data.clockwise=!1,s.save(),s.beginPath(),s.translate(d.x,d.y),s.rotate(p),s.scale(1,2*a/c),s.arc(0,0,.5*c,this._data.angleFrom,this._data.angleTo,this._data.clockwise),s.restore(),s.stroke(),s.fillStyle=this._data.backcolor,s.fill()}}class T extends _.InplaceTextLineSourcePaneView{constructor(e,t,i,n,r){super(e,t,i,n,r),this._ellipseRenderer=new v,this._renderer=null,this._textRenderer=new p.LineToolTextRenderer(void 0,new h.HitTestResult(h.HitTarget.MovePoint,(0,_.inplaceEditHandlers)(this._tryActivateEditMode.bind(this,null)))),this._source.setAdditionalCursorData((()=>{var e;return{color:this._source.editableTextStyle().cursorColor,rotationPoint:null!==(e=this._textRenderer.rotation())&&void 0!==e?e:void 0,...this._textRenderer.getTextInfo()}}),this._textRenderer.positionToCoordinate.bind(this._textRenderer))}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){if(super._updateImpl(e),this._renderer=null,this._points.length<2)return;const t=this._source.properties().childs(),i={points:this._points,color:t.color.value(),linewidth:t.linewidth.value(),backcolor:t.fillBackground.value()?(0,o.generateColor)(t.backgroundColor.value(),t.transparency.value()):"transparent",noHitTestOnBackground:!1,backgroundHitTarget:this._model.selection().isSelected(this._source)?h.HitTarget.MovePoint:void 0};this._ellipseRenderer.setData(i);const s=new l.CompositeRenderer;s.append(this._ellipseRenderer);const a=i.points[0],c=i.points[1];if(2===this._points.length)return this.addAnchors(s),void(this._renderer=s);let _=i.points[2];const p=(0,n.distanceToLine)(a,c,_).distance,g=c.subtract(a),x=a.add(c).scaled(.5),f=new r.Point(-g.y,g.x).normalized();_=x.add(f.scaled(p));const v=x.add(f.scaled(-p)),T=this._placeHolderMode();(t.showLabel.value()&&t.text.value()||T||this._isTextEditMode())&&this._updateTextRenderer([a,c,_,v],T,e)&&s.append(this._textRenderer);const w=(0,u.thirdPointCursorType)(a,c),R=[(0,d.anchor)({...a,pointIndex:0}),(0,d.anchor)({...c,pointIndex:1}),(0,d.anchor)({..._,pointIndex:2,cursorType:w}),(0,d.anchor)({...v,pointIndex:3,cursorType:w})];s.append(this.createLineAnchor({points:R},0)),this._renderer=s}_updateTextRenderer([e,t,i,n],o,l){if(t.subtract(e).length()<1e-5||n.subtract(i).length()<1e-5)return!1
;const d=(0,s.intersectLines)((0,r.lineThroughPoints)(e,t),(0,r.lineThroughPoints)(i,n));if(!d)return!1;const{fontSize:h,bold:u,italic:_}=this._source.properties().childs(),p=Math.sqrt(2),g=e.subtract(t).length()/p;if(this._textRenderer.setData({points:[d],text:this._textData(),color:this._textColor(),fontSize:h.value(),font:a.CHART_FONT_FAMILY,bold:u.value(),italic:_.value(),wordWrapWidth:o?void 0:g,maxHeight:o?void 0:n.subtract(i).length()/p,angle:Math.atan((e.y-t.y)/(e.x-t.x)),offsetX:0,offsetY:0,horzAlign:"center",vertAlign:"middle",forceTextAlign:!0,decorator:o?c.PlusTextRendererDecorator.instance():void 0,...this._inplaceTextHighlight()}),o&&this._textRenderer.measure().width>g&&(this._textRenderer.updateData({text:""}),this._textRenderer.measure().width>g))return!1;this._textRenderer.setCursorType(this._textCursorType());const{mediaSize:{width:x,height:f}}=l;return this._textRenderer.isOutOfScreen(x,f)?this.closeTextEditor():this._updateInplaceText(this._textRenderer.getTextInfo()),!0}}},98633:(e,t,i)=>{"use strict";i.r(t),i.d(t,{EmojiPaneView:()=>r});var n=i(68498);class r extends n.SvgIconPaneView{_iconColor(){return null}}},256:(e,t,i)=>{"use strict";i.r(t),i.d(t,{ExecutionPaneView:()=>h});var n,r=i(86441),s=i(27916),o=i(56468),a=i(75919);!function(e){e[e.ArrowWidth=4]="ArrowWidth"}(n||(n={}));class l extends a.MediaCoordinatesPaneRenderer{constructor(e){super(),this._data=null!=e?e:null}hitTest(e){if(null===this._data)return null;const t=Math.round(this._data.point.x),i=Math.round(this._data.point.y),n=this._data.arrowHeight;let r,s;if("buy"===this._data.direction?(r=i,s=i+n):(r=i-n,s=i),e.x>=t-2&&e.x<=t+2&&e.y>=r&&e.y<=s){const e=this._data.tooltip;return new o.HitTestResult(o.HitTarget.Custom,{tooltip:""!==e?{text:e,rect:{x:t,y:r,w:2,h:s-r}}:void 0})}return null}setData(e){this._data=e}_drawImpl(e){const t=e.context;if(null===this._data)return;const i=Math.round(this._data.point.x),n=Math.round(this._data.point.y);!function(e,t,i,n,r,s){e.save(),e.strokeStyle=n,e.fillStyle=n,e.translate(t-2,i),"buy"!==r&&(e.rotate(Math.PI),e.translate(-4,0)),e.beginPath(),e.moveTo(2,s),e.lineTo(2,0),e.moveTo(0,2),e.lineTo(2,0),e.lineTo(4,2),e.stroke(),e.restore()}(t,i,n,this._data.arrowColor,this._data.direction,this._data.arrowHeight);const{arrowHeight:r,arrowSpacing:s,fontHeight:o,direction:a,text:l,font:d,textColor:h}=this._data,c=function(e,t,i){if(0===t.length)return 0;e.save(),e.font=i;const n=e.measureText(t).width;return e.restore(),5+n}(t,l,d);if(0!==c){const e="buy"===a?n+r+s:n-r-s-o;!function(e,t,i,n,r,s,o,a){if(!s)return;e.save(),e.textAlign="center",e.textBaseline="middle",e.font=o,e.fillStyle=a;const l=t+n/2,d=i+r/2;e.fillText(s,l,d-1),e.restore()}(t,Math.round(i+.5-c/2),e,c,o,l,d,h)}}}var d=i(91920);class h extends s.LineSourcePaneView{constructor(){super(...arguments),this._executionRenderer=new l,this._renderer=null}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){var t;super._updateImpl(e),this._renderer=null;const i=this._source,n=i.points();if(0===n.length)return
;const s=i.adapter(),o=i.model().timeScale(),a=null===(t=i.model().paneForSource(i))||void 0===t?void 0:t.executionsPositionController();if(!a)return;const l=a.getXYCoordinate(s,o,n[0].index);!isFinite(l.y)||l.y<0||l.y>e.mediaSize.height||l.x<0||(this._executionRenderer.setData({point:new r.Point(l.x,l.y),arrowColor:s.getArrowColor(),arrowHeight:s.getArrowHeight(),direction:s.getDirection(),tooltip:s.getTooltip(),arrowSpacing:s.getArrowSpacing(),fontHeight:d.fontHeight(s.getFont()),text:s.getText(),textColor:s.getTextColor(),font:s.getFont()}),this._renderer=this._executionRenderer)}}},23033:(e,t,i)=>{"use strict";i.d(t,{FibHorizontalLevelsPaneViewBase:()=>S});var n=i(50151),r=i(86441),s=i(11542),o=i(69186),a=i(91046),l=i(17330),d=i(13075);class h{geometry(e){const t=(0,l.fontSize)(e);return{decoratorAndTextMargin:t/3,width:Math.round(t/8),ignoreRtl:!0}}draw(e,t,i,n){const{horizontalPixelRatio:r,verticalPixelRatio:s}=t,o=Math.max(1,Math.round(r*n.decoratorWidth/2)),a=Math.round((n.textTop+n.textBottom)/2*s),l=Math.round((n.decoratorLeft+n.decoratorWidth/2)*r);e.fillStyle=i.color,e.beginPath(),e.arc(l,a,o,0,2*Math.PI),e.fill()}static instance(){var e;return this._instance=null!==(e=this._instance)&&void 0!==e?e:new h,this._instance}}h._instance=null;var c,u=i(56468),_=i(95201),p=i(49857),g=i(72609),x=i(9859);function f(e,t,i,n){if(null!==e&&"middle"===t){const t=(0,l.getTextBoundaries)(e,i,n);if(t)return[t]}return[]}function v(e){return"left"===e?0:"center"===e?1:2}function T(e){const{labelAndTextHaveSameAlign:t,labelAndTextHaveSameVertAlign:i,labelRenderer:s,textRenderer:o,horzTextAlign:a,horzLabelsAlign:l,textMaxWidth:d}=e;if(t)return function(e){const{labelRenderer:t,textRenderer:i,horzTextAlign:n,textMaxWidth:s,extendLeft:o,extendRight:a}=e,l=w(t),d=w(i);switch(n){case"left":o?R(l,d):m(l,d);break;case"center":if(y(d,s))return{hideText:!0};!function(e,t,i,n){const s=i-n/2,o=null==e?void 0:e.rect.width,a=null==t?void 0:t.rect.width;e&&e.renderer.setPoint((0,r.point)(s+o/2,e.point.y));t&&t.renderer.setPoint((0,r.point)(s+o+a/2,t.point.y))}(l,d,l.rect.x+l.rect.width/2,l.rect.width+d.rect.width);break;case"right":a?m(l,d):R(l,d)}return{hideText:!1}}(e);if(null!==d){if(y(w(o),d))return{hideText:!0}}if(i){const e=s.rect(),t=o.rect(),i=(0,n.ensureNotNull)(o.point());if(v(a)<v(l)){const n=t.x+t.width+g.labelEdgeOffset-e.x;n>0&&o.setPoint((0,r.point)(i.x-n,i.y))}else{const n=e.x+e.width+g.labelEdgeOffset-t.x;n>0&&o.setPoint((0,r.point)(i.x+n,i.y))}}return{hideText:!1}}function w(e){return{renderer:e,rect:e.rect(),point:(0,n.ensureNotNull)(e.point()),horzAlign:(0,n.ensureNotNull)(e.data()).horzAlign}}function R(e,t){var i,n;t.renderer.setPoint((0,r.point)(e.rect.x+e.rect.width+(i=t.horzAlign,n=t.rect.width,"right"===i?n:0),t.point.y),0)}function m(e,t){var i,n;e.renderer.setPoint((0,r.point)(t.rect.x-(i=e.horzAlign,n=e.rect.width,"left"===i?n:0),e.point.y),0)}function y(e,t){return null!==t&&(e.rect.width>t&&(e.renderer.setData({...(0,n.ensureNotNull)(e.renderer.data()),text:""}),e.rect=e.renderer.rect(),e.rect.width>t))}
function b(e){const{leftAnchorX:t,rightAnchorX:i,extendLeft:n,extendRight:r,screenWidth:s,labelRenderer:o,horzLabelsAlign:a,labelAndTextHaveSameVertAlign:l}=e;let d;const h=l?function(e,t){return e?(0,x.clamp)(e.x+e.width,0,t)-(0,x.clamp)(e.x,0,t):0}(null==o?void 0:o.rect(),s):0;return d=n||r?n&&r?s-h:n?i-("left"===a?h:0):s-t-("right"===a?h:0):i-t-("center"===a?h:0),d-8}!function(e){e[e.LevelHitTestTolerance=4]="LevelHitTestTolerance",e[e.CenterTextMinHorzMargins=4]="CenterTextMinHorzMargins"}(c||(c={}));const L=s.t(null,void 0,i(6060));function P(e){if(!e)return;const t=(0,n.ensureNotNull)(e.data()),i=t.vertAlign;if("middle"!==i){const s=(0,n.ensureDefined)(t.points)[0],o=("top"===i?1:-1)*((0,l.fontSize)(t)+(0,l.lineSpacing)(t));e.setData({...t,vertAlign:"middle",points:[(0,r.point)(s.x,s.y+o)]})}}class S extends g.LineToolPaneViewFibWithLabels{constructor(){super(...arguments),this._renderer=new _.CompositeRenderer}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_addLevels(e){var t;const{mediaSize:i,levels:s,showLabel:l,showText:c,horzLabelsAlign:_,vertLabelsAlign:g,horzTextAlign:x,vertTextAlign:v,extendLeft:w,extendRight:R,isOnScreen:m,trendLineRenderer:y}=e;let{left:S,right:M}=e;S===M&&(w&&(S-=1),R&&(M+=1));const{width:C,height:I}=i,A=this._model.hoveredSource()===this._source?null===(t=this._model.lastHittestData())||void 0===t?void 0:t.activeItem:null,D=[],k=[],N=this._model.selection().isSelected(this._source),B=this._textCursorType();for(let e=0;e<s.length;e++){const t=s[e],i=new r.Point(S,t.y),y=new r.Point(M,t.y),H=Boolean(t.text),z=this._isTextEditMode()&&this._inplaceEditLevelIndex===t.index,E=!H&&!z,W=E?"center":x,O=E?"middle":v,V=_===W&&g===O,F={levelIndex:t.index,leftPoint:i,rightPoint:y,color:t.color,extendLeft:w,extendRight:R},Y=t.index===A;let j,U=null;const Q=c&&(H||Y&&N||z),q=Q&&!H&&!z;!Q||q&&(0,o.lastMouseOrTouchEventInfo)().isTouch||(j=q?d.PlusTextRendererDecorator.instance():V&&l?h.instance():void 0,U=(0,n.ensureDefined)(this._textRenderers[t.index]),this._updateRendererLabel({...F,...z?this._inplaceTextHighlight():{},horzAlign:W,vertAlign:O,color:z?this._textColor():F.color,decorator:j},U,z?this._textData():t.text||L),U.setCursorType(B));const X=j===h.instance(),Z=this._updateLabelForLevel({...F,price:t.price,horzAlign:_,vertAlign:g,boxPaddingRight:X?0:void 0});let G=!1;if(null!==U&&null!==Z){const e=g===O;G=T({labelRenderer:Z,textRenderer:U,labelAndTextHaveSameAlign:V,labelAndTextHaveSameVertAlign:e,horzTextAlign:W,horzLabelsAlign:_,textMaxWidth:q?b({leftAnchorX:S,rightAnchorX:M,extendLeft:w,extendRight:R,screenWidth:C,horzLabelsAlign:_,labelRenderer:Z,labelAndTextHaveSameVertAlign:e}):null,extendLeft:w,extendRight:R}).hideText}if(m){P(U),P(Z);if(!(!w&&!R&&(U&&"center"===W&&"middle"===O&&M-4<U.rect().x+U.rect().width||Z&&"center"===_&&"middle"===g&&S+4>Z.rect().x))){const e=[...f(Z,g,C,I),...f(G?null:U,O,C,I)],n={points:[i,y],color:t.color,linewidth:t.linewidth,linestyle:t.linestyle,extendleft:w,extendright:R,leftend:p.LineEnd.Normal,rightend:p.LineEnd.Normal,
excludeBoundaries:e,hitTestTolerance:4},r=new a.TrendLineRenderer;r.setData(n),r.setHitTest(new u.HitTestResult(u.HitTarget.MovePoint,{snappingPrice:t.price,activeItem:t.index},t.index)),D.push(r)}}null===Z||Z.isOutOfScreen(C,I)||k.push(Z),G||null===U||U.isOutOfScreen(C,I)||k.push(U)}for(const e of D)this._renderer.append(e);y&&this._renderer.append(y);for(const e of k)this._renderer.append(e);if(this._isTextEditMode()){const e=(0,n.ensureDefined)(this._textRenderers[this._inplaceEditLevelIndex]);e.isOutOfScreen(C,I)?this.closeTextEditor():this._updateInplaceText(e.getTextInfo())}}}},72609:(e,t,i)=>{"use strict";i.d(t,{LineToolPaneViewFibWithLabels:()=>u,labelEdgeOffset:()=>c});var n=i(50151),r=i(86441),s=i(17330),o=i(56468),a=i(43290),l=i(15938),d=i(32211),h=i(62689);const c=4;class u extends d.InplaceTextLineSourcePaneView{constructor(e,t,i,r,l){super(e,t,i,r,l),this._labelsRenderers={},this._numericFormatter=(0,a.getNumericFormatter)(),this._percentageFormatter=(0,a.getPercentageFormatter)(),this._textRenderers={},this._inplaceEditLevelIndex=1;for(let t=1;t<=e.levelsCount();t++)this._labelsRenderers[t]=new s.TextRenderer(void 0,new o.HitTestResult(o.HitTarget.MovePoint,{activeItem:t}));if(i&&r&&l){for(let t=1;t<=e.levelsCount();t++){const e=new o.HitTestResult(o.HitTarget.MovePoint,{...(0,d.inplaceEditHandlers)(this._tryActivateEditMode.bind(this,t)),activeItem:t});this._textRenderers[t]=new h.LineToolTextRenderer(void 0,e)}this._source.setAdditionalCursorData((()=>{const e=(0,n.ensureDefined)(this._textRenderers[this._inplaceEditLevelIndex]);return{color:this._source.editableTextStyle().cursorColor,...e.getTextInfo()}}),(e=>(0,n.ensureDefined)(this._textRenderers[this._inplaceEditLevelIndex]).positionToCoordinate(e)))}}_tryActivateEditMode(e,t){this._inplaceEditLevelIndex=e,super._tryActivateEditMode(e,t)}_activateEditMode(e){const t=(0,n.ensureDefined)(this._textRenderers[this._inplaceEditLevelIndex]);this._updateInplaceText(t.getTextInfo()),super._activateEditMode(e)}_updateLabelForLevel(e){var t,i,n,r,s;const o=this._labelsRenderers[e.levelIndex];if(void 0===o)return null;const a=this._source.priceScale();if(!a)return null;const l=null===(t=this._source.ownerSource())||void 0===t?void 0:t.firstValue();if(null==l)return null;const d=this._source.properties(),h=Boolean(null===(i=d.showCoeffs)||void 0===i?void 0:i.value()),c=Boolean(null===(n=d.showPrices)||void 0===n?void 0:n.value());if(!h&&!c)return null;const u=d["level"+e.levelIndex].coeff.value();let _="";if(h){_+=null!==(s=null===(r=d.coeffsAsPercents)||void 0===r?void 0:r.value())&&void 0!==s&&s?this._percentageFormatter.format(100*u,{signPositive:!1,tailSize:2}):this._numericFormatter.format(u)}return c&&(_+=" ("+a.formatPrice(e.price,l)+")"),this._updateRendererLabel(e,o,_),o}_updateRendererLabel(e,t,i){var n,s;const{leftPoint:o,rightPoint:a,color:d,horzAlign:h,vertAlign:u,extendLeft:_=!1,extendRight:p=!1,...g}=e,x=this._source.properties();if(!i&&void 0===g.decorator)return null
;const f=!(o.x>this._model.timeScale().width()&&!_||a.x<0&&!p),v=this._model.timeScale().width();let T,w,R=h;switch(R){case"left":w=o.y,_?T=f?0:a.x:(T=o.x,R="right");break;case"right":w=a.y,p?T=f?this._model.timeScale().width():o.x:(T=a.x,R="left");break;default:T=((_&&f?0:o.x)+(p&&f?v:a.x))/2,w=(o.y+a.y)/2;break}return t.setData({points:[new r.Point(T,w)],text:i,color:d,vertAlign:u,horzAlign:R,offsetX:c,offsetY:0,font:l.CHART_FONT_FAMILY,fontSize:null!==(s=null===(n=x.labelFontSize)||void 0===n?void 0:n.value())&&void 0!==s?s:12,...g}),t}}},93143:(e,t,i)=>{"use strict";i.r(t),i.d(t,{FibChannelPaneView:()=>u});var n=i(50151),r=i(19063),s=i(49857),o=i(95201),a=i(90241),l=i(91046),d=i(72609),h=i(17330);class c extends a.ParallelChannelRenderer{constructor(){super(...arguments),this._data=null}_drawLine(e,t,i,n){var r;const s=null===(r=this._data)||void 0===r?void 0:r.excludeBoundaries;if(void 0!==s){e.save(),e.beginPath(),e.rect(0,0,n.width,n.height);for(let t=0;t<s.length;t++){const{x:i,y:n}=s[t];0!==t?e.lineTo(i,n):e.moveTo(i,n)}e.closePath(),e.clip("evenodd")}super._drawLine(e,t,i,n),void 0!==s&&e.restore()}}class u extends d.LineToolPaneViewFibWithLabels{constructor(){super(...arguments),this._baseLineRenderer=new l.TrendLineRenderer,this._lastLevelTrendRenderer=new l.TrendLineRenderer,this._renderer=null,this._norm=null}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){var t,i,a;super._updateImpl(e);const l=this._source.priceScale();if(null===l)return;this._renderer=null;const d=null===(t=this._source.ownerSource())||void 0===t?void 0:t.firstValue();if(null==d)return;3===this._points.length&&3===this._source.points().length&&(this._norm=this._points[2].subtract(this._points[0]));const u=new o.CompositeRenderer;if(this._points.length<2)return this.addAnchors(u),void(this._renderer=u);const _=this._source.properties().childs(),p=this._points[0],g=this._points[1];if(this._points.length<3){const e={points:[p,g],color:_.level1.childs().color.value(),linewidth:_.levelsStyle.childs().linewidth.value(),linestyle:_.levelsStyle.childs().linestyle.value(),extendleft:_.extendLeft.value(),extendright:_.extendRight.value(),leftend:s.LineEnd.Normal,rightend:s.LineEnd.Normal};return this._baseLineRenderer.setData(e),u.append(this._baseLineRenderer),this.addAnchors(u),void(this._renderer=u)}const x=this._source.levelsCount(),f=(0,n.ensureNotNull)(this._norm),{mediaSize:{width:v,height:T}}=e;for(let e=1;e<x;e++){const t=(0,n.ensureDefined)(this._source.properties().child("level"+e)).childs();if(!t.visible.value())continue;let s=null;for(let t=e+1;t<=x;t++){const e=(0,n.ensureDefined)(this._source.properties().child("level"+t)).childs();if(e.visible.value()){s=e;break}}if(!s)break;const o=f.scaled(t.coeff.value()),a=p.add(o),w=g.add(o),R=f.scaled(s.coeff.value()),m=p.add(R),y=g.add(R),b=t.color.value(),L=_.extendLeft.value(),P=_.extendRight.value(),S=_.levelsStyle.childs().linestyle.value(),M=_.levelsStyle.childs().linewidth.value(),C=l.coordinateToPrice(a.y,d),I=this._updateLabelForLevel({
levelIndex:e,leftPoint:a,rightPoint:w,price:C,color:b,horzAlign:_.horzLabelsAlign.value(),vertAlign:_.vertLabelsAlign.value()});let A;null!==I&&(u.append(I),A=null!==(i=(0,h.getTextBoundaries)(I,v,T))&&void 0!==i?i:void 0);const D={line1:{color:b,lineStyle:S,lineWidth:M,points:[a,w]},line2:{color:b,lineStyle:S,lineWidth:M,points:[m,y]},extendLeft:L,extendRight:P,backColor:(0,r.generateColor)(b,_.transparency.value(),!0),skipTopLine:!0,fillBackground:_.fillBackground.value(),hittestOnBackground:!0,excludeBoundaries:A},k=new c;k.setData(D),u.append(k)}let w=null;for(let e=x;e>=1;e--){if((0,n.ensureDefined)(this._source.properties().child("level"+e)).childs().visible.value()){w=e;break}}if(null!==w){const e=(0,n.ensureDefined)(this._source.properties().child("level"+w)).childs();if(e.visible.value()){const t=f.scaled(e.coeff.value()),i=p.add(t),n=g.add(t),r=l.coordinateToPrice(i.y,d),o=this._updateLabelForLevel({levelIndex:w,leftPoint:i,rightPoint:n,price:r,color:e.color.value(),horzAlign:_.horzLabelsAlign.value(),vertAlign:_.vertLabelsAlign.value()});let c;null!==o&&(u.append(o),c=null!==(a=(0,h.getTextBoundaries)(o,v,T))&&void 0!==a?a:void 0);const x={points:[i,n],color:e.color.value(),linewidth:_.levelsStyle.childs().linewidth.value(),linestyle:_.levelsStyle.childs().linestyle.value(),extendleft:_.extendLeft.value(),extendright:_.extendRight.value(),leftend:s.LineEnd.Normal,rightend:s.LineEnd.Normal,excludeBoundaries:c?[c]:void 0};this._lastLevelTrendRenderer.setData(x),u.append(this._lastLevelTrendRenderer)}}this.addAnchors(u),this._renderer=u}}},83201:(e,t,i)=>{"use strict";i.r(t),i.d(t,{FibCirclesPaneView:()=>c});var n=i(50151),r=i(86441),s=i(72609),o=i(91046),a=i(56468),l=i(95201),d=i(36155),h=i(49857);class c extends s.LineToolPaneViewFibWithLabels{constructor(){super(...arguments),this._trendLineRenderer=new o.TrendLineRenderer,this._renderer=new l.CompositeRenderer}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){if(super._updateImpl(e),this._renderer.clear(),this._source.points().length<2||this._points.length<2)return;const t=this._source.priceScale();if(!t||t.isEmpty()||this._model.timeScale().isEmpty())return;const[i,s]=this._points,o=i.add(s).scaled(.5),l=Math.abs(s.x-i.x),c=Math.abs(s.y-i.y),u=[],_=this._source.properties(),p=this._source.levelsCount();for(let e=1;e<=p;e++){const t=(0,n.ensureDefined)(_.child(`level${e}`)).childs();if(!t.visible.value())continue;const i=t.coeff.value(),s=t.color.value(),a=[];a.push(new r.Point(o.x-.5*l*i,o.y-.5*c*i)),a.push(new r.Point(o.x+.5*l*i,o.y+.5*c*i));const d=new r.Point(o.x,o.y+.5*c*i);u.push({color:s,points:a,labelPoint:d,linewidth:t.linewidth.value(),linestyle:t.linestyle.value(),index:e})}const g=_.childs().fillBackground.value(),x=_.childs().transparency.value();for(let e=0;e<u.length;e++){const t=u[e];this._renderer.append(new d.EllipseRendererSimple({points:t.points,color:t.color,linewidth:t.linewidth,backcolor:t.color,wholePoints:e>0?u[e-1].points:void 0,fillBackground:g,transparency:x
},new a.HitTestResult(a.HitTarget.MovePoint,void 0,t.index)));const i=this._updateLabelForLevel({levelIndex:t.index,color:t.color,price:0,vertAlign:"middle",horzAlign:"left",leftPoint:t.labelPoint,rightPoint:t.labelPoint});null!==i&&this._renderer.append(i)}const f=_.childs().trendline.childs();f.visible.value()&&(this._trendLineRenderer.setData({points:[this._points[0],this._points[1]],color:f.color.value(),linewidth:f.linewidth.value(),linestyle:f.linestyle.value(),extendleft:!1,extendright:!1,leftend:h.LineEnd.Normal,rightend:h.LineEnd.Normal}),this._renderer.append(this._trendLineRenderer)),this.addAnchors(this._renderer)}}},70011:(e,t,i)=>{"use strict";i.d(t,{fibLevelCoordinate:()=>r,fibLevelPrice:()=>s});var n=i(50151);function r(e,t,i,r,s,o){if(o)return Math.round((0,n.ensureDefined)(e.coordinate)+(0,n.ensureDefined)(t.coordinate)*i);const a=e.price+t.price*i;return r.priceToCoordinate(a,s)}function s(e,t,i,r,s,o){if(!o)return e.price+t.price*i;const a=(0,n.ensureDefined)(e.coordinate)+(0,n.ensureDefined)(t.coordinate)*i;return r.coordinateToPrice(a,s)}},65343:(e,t,i)=>{"use strict";i.r(t),i.d(t,{FibRetracementPaneView:()=>d});var n=i(86441),r=i(62189),s=i(91046),o=i(49857),a=i(70011),l=i(23033);class d extends l.FibHorizontalLevelsPaneViewBase{constructor(){super(...arguments),this._trendLineRenderer=new s.TrendLineRenderer,this._levels=[]}_tryActivateEditMode(e,t){this._source.setInplaceEditLevelIndex(e),super._tryActivateEditMode(e,t)}_updateImpl(e){var t;if(super._updateImpl(e),this._renderer.clear(),this._source.points().length<2)return;const i=this._source.priceScale();if(!i||i.isEmpty()||this._model.timeScale().isEmpty())return;const s=null===(t=this._source.ownerSource())||void 0===t?void 0:t.firstValue();if(null==s)return;const[l,d]=this._source.points(),h=this._source.properties().childs(),c=h.reverse.value();if(this._points.length<2)return;const u=this._points[0],_=this._points[1],p=Math.min(u.x,_.x),g=Math.max(u.x,_.x),x=h.fillBackground.value(),f=h.transparency.value(),v=h.extendLinesLeft.value(),T=h.extendLines.value(),w=i.isLog()&&h.fibLevelsBasedOnLogScale.value(),{mediaSize:{width:R}}=e,m=!(p>R&&!v||g<0&&!T);this._levels=[];const y=c?l.price:d.price,b=c?d.price:l.price,L=b-y,P=i.priceToCoordinate(y,s),S={price:y,coordinate:P},M={price:L,coordinate:i.priceToCoordinate(b,s)-P},C=this._source.levelsCount();for(let e=1;e<=C;e++){const t=h["level"+e].childs();if(!t||!t.visible.value())continue;const n=t.coeff.value(),r=(0,a.fibLevelCoordinate)(S,M,n,i,s,w),o=(0,a.fibLevelPrice)(S,M,n,i,s,w);this._levels.push({color:t.color.value(),text:t.text.value(),y:r,price:o,linewidth:h.levelsStyle.childs().linewidth.value(),linestyle:h.levelsStyle.childs().linestyle.value(),index:e})}if(x&&m)for(let e=0;e<this._levels.length;e++)if(e>0&&x){const t=this._levels[e-1],i={points:[new n.Point(p,this._levels[e].y),new n.Point(g,t.y)],color:this._levels[e].color,linewidth:0,backcolor:this._levels[e].color,fillBackground:!0,transparency:f,extendLeft:v,extendRight:T},s=new r.RectangleRenderer(!0);s.setData(i),
this._renderer.append(s)}let I=p,A=g;I===A&&(v&&(I-=1),T&&(A+=1));const D=h.trendline.childs();let k=null;D.visible.value()&&m&&(k=this._trendLineRenderer,k.setData({points:[this._points[0],this._points[1]],color:D.color.value(),linewidth:D.linewidth.value(),linestyle:D.linestyle.value(),extendleft:!1,extendright:!1,leftend:o.LineEnd.Normal,rightend:o.LineEnd.Normal})),this._addLevels({mediaSize:e.mediaSize,levels:this._levels,left:I,right:A,showLabel:h.showCoeffs.value()||h.showPrices.value(),showText:h.showText.value(),horzLabelsAlign:h.horzLabelsAlign.value(),vertLabelsAlign:h.vertLabelsAlign.value(),horzTextAlign:h.horzTextAlign.value(),vertTextAlign:h.vertTextAlign.value(),extendLeft:v,extendRight:T,fontSize:h.labelFontSize.value(),isOnScreen:m,trendLineRenderer:k}),this.addAnchors(this._renderer),this._model.selection().isSelected(this._source)||this.closeTextEditor()}}},66152:(e,t,i)=>{"use strict";i.r(t),i.d(t,{FibSpeedResistanceArcsPaneView:()=>p});var n=i(86441),r=i(9859),s=i(56468),o=i(95201),a=i(49857),l=i(91046),d=i(19063),h=i(61993),c=i(75919);class u extends c.MediaCoordinatesPaneRenderer{constructor(e,t,i){super(),this._data=e,this._hittest=t||new s.HitTestResult(s.HitTarget.MovePoint),this._backHittest=i||new s.HitTestResult(s.HitTarget.MovePointBackground)}hitTest(e){const t=this._data;if(null===t)return null;if((0,r.sign)(e.y-t.center.y)!==t.dir&&!t.fullCircles)return null;const i=e.subtract(t.center).length(),n=(0,h.interactionTolerance)().curve;return Math.abs(i-t.radius)<n?this._hittest:t.hittestOnBackground&&Math.abs(i)<=t.radius+n?this._backHittest:null}_drawImpl(e){const t=this._data;if(null===t)return;const i=e.context;i.lineCap="round",i.strokeStyle=t.color,i.lineWidth=t.linewidth,i.translate(t.center.x,t.center.y),i.beginPath(),t.fullCircles?i.arc(0,0,t.radius,2*Math.PI,0,!1):t.dir>0?i.arc(0,0,t.radius,0,Math.PI,!1):i.arc(0,0,t.radius,Math.PI,0,!1),i.stroke(),t.fillBackground&&(t.radius2&&(t.fullCircles?i.arc(0,0,t.radius2,2*Math.PI,0,!0):t.dir>0?i.arc(0,0,t.radius2,Math.PI,0,!0):i.arc(0,0,t.radius2,0,Math.PI,!0)),i.fillStyle=(0,d.generateColor)(t.color,t.transparency,!0),i.fill())}}var _=i(72609);class p extends _.LineToolPaneViewFibWithLabels{constructor(){super(...arguments),this._trendLineRenderer=new l.TrendLineRenderer,this._renderer=null,this._levels=[]}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){var t;if(super._updateImpl(e),this._renderer=null,this._points.length<2)return;const i=this._source.priceScale();if(!i||i.isEmpty()||this._model.timeScale().isEmpty())return;if(null==(null===(t=this._source.ownerSource())||void 0===t?void 0:t.firstValue()))return;const l=this._points[0],d=this._points[1],h=l.subtract(d).length();this._levels=[];const c=this._source.properties().childs(),_=this._source.levelsCount();for(let e=1;e<=_;e++){const t="level"+e,i=this._source.properties().child(t).childs();if(!i.visible.value())continue;const s=i.coeff.value(),o=i.color.value(),a=Math.abs(d.subtract(l).length()*s),c=(0,
r.sign)(d.y-l.y),u=new n.Point(l.x,l.y+c*h*Math.abs(s));this._levels.push({color:o,radius:a,dir:c,labelPoint:u,linewidth:i.linewidth.value(),linestyle:i.linestyle.value(),index:e})}if(this._points.length<2)return;const p=new o.CompositeRenderer,g=c.fillBackground.value(),x=c.transparency.value();for(let e=0;e<this._levels.length;e++){const t=this._levels[e],i={center:l,color:t.color,linewidth:t.linewidth,radius:t.radius,dir:t.dir,transparency:x,fillBackground:g,hittestOnBackground:!0,fullCircles:c.fullCircles.value(),radius2:e>0?this._levels[e-1].radius:void 0},n=new s.HitTestResult(s.HitTarget.MovePoint,void 0,t.index);p.append(new u(i,n));const r=this._updateLabelForLevel({levelIndex:this._levels[e].index,leftPoint:this._levels[e].labelPoint,rightPoint:this._levels[e].labelPoint,price:0,color:this._levels[e].color,horzAlign:"left",vertAlign:"middle"});null!==r&&p.append(r)}const f=c.trendline.childs();if(f.visible.value()){const e={points:[this._points[0],this._points[1]],color:f.color.value(),linewidth:f.linewidth.value(),linestyle:f.linestyle.value(),extendleft:!1,extendright:!1,leftend:a.LineEnd.Normal,rightend:a.LineEnd.Normal};this._trendLineRenderer.setData(e),p.append(this._trendLineRenderer)}this.addAnchors(p),this._renderer=p}}},66832:(e,t,i)=>{"use strict";i.r(t),i.d(t,{FibSpeedResistanceFanPaneView:()=>p});var n=i(50151),r=i(86441),s=i(43290),o=i(56468),a=i(65395),l=i(95201),d=i(49857),h=i(17330),c=i(91046),u=i(27916),_=i(15938);class p extends u.LineSourcePaneView{constructor(e,t){super(e,t),this._leftTextRenderers=[],this._rightTextRenderers=[],this._topTextRenderers=[],this._bottomTextRenderers=[],this._renderer=null;for(let e=0;e<this._source.hLevelsCount();e++)this._leftTextRenderers.push(new h.TextRenderer),this._rightTextRenderers.push(new h.TextRenderer);for(let e=0;e<this._source.vLevelsCount();e++)this._topTextRenderers.push(new h.TextRenderer),this._bottomTextRenderers.push(new h.TextRenderer)}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){if(super._updateImpl(e),this._renderer=null,this._source.points().length<2)return;const t=this._source.priceScale(),i=(0,n.ensureNotNull)(this._source.ownerSource()).firstValue();if(null===i||!t||t.isEmpty()||this._model.timeScale().isEmpty())return;if(this._points.length<2)return;const h=this._source.points()[0],u=this._source.points()[1],p=this._source.properties().childs(),g=p.reverse.value(),x=[],f=g?u.price-h.price:h.price-u.price,v=g?h.price:u.price;for(let e=1;e<=this._source.hLevelsCount();e++){const n="hlevel"+e,r=this._source.properties().child(n).childs();if(!r.visible.value())continue;const s=r.coeff.value(),o=r.color.value(),a=v+s*f,l=t.priceToCoordinate(a,i);x.push({coeff:s,color:o,y:l,index:e})}const T=[],w=g?u.index-h.index:h.index-u.index,R=g?h.index:u.index;for(let e=1;e<=this._source.vLevelsCount();e++){const t="vlevel"+e,i=this._source.properties().child(t).childs();if(!i.visible.value())continue
;const n=i.coeff.value(),r=i.color.value(),s=Math.round(R+n*w),o=this._model.timeScale().indexToCoordinate(s);T.push({coeff:n,color:r,x:o,index:e})}const m=new l.CompositeRenderer,y=this._points[0],b=this._points[1],L=Math.min(y.x,b.x),P=Math.min(y.y,b.y),S=Math.max(y.x,b.x),M=Math.max(y.y,b.y),C=p.grid.childs().color.value(),I=p.grid.childs().linewidth.value(),A=p.grid.childs().linestyle.value(),D=(0,s.getNumericFormatter)();for(let e=0;e<x.length;e++){const t=new r.Point(L,x[e].y),i=new r.Point(S,x[e].y);if(p.grid.childs().visible.value()){const e={points:[t,i],color:C,linewidth:I,linestyle:A,extendleft:!1,extendright:!1,leftend:d.LineEnd.Normal,rightend:d.LineEnd.Normal},n=new c.TrendLineRenderer;n.setData(e),m.append(n)}if(p.showLeftLabels.value()){const i={points:[t],text:D.format(x[e].coeff),color:x[e].color,vertAlign:"middle",horzAlign:"right",font:_.CHART_FONT_FAMILY,offsetX:5,offsetY:0,fontsize:12,forceTextAlign:!0};this._leftTextRenderers[e].setData(i),m.append(this._leftTextRenderers[e])}if(p.showRightLabels.value()){const t={points:[i],text:D.format(x[e].coeff),color:x[e].color,vertAlign:"middle",horzAlign:"left",font:_.CHART_FONT_FAMILY,offsetX:5,offsetY:0,fontsize:12,forceTextAlign:!0};this._rightTextRenderers[e].setData(t),m.append(this._rightTextRenderers[e])}}for(let e=0;e<T.length;e++){const t=new r.Point(T[e].x,P),i=new r.Point(T[e].x,M);if(p.grid.childs().visible.value()){const e={points:[t,i],color:C,linewidth:I,linestyle:A,extendleft:!1,extendright:!1,leftend:d.LineEnd.Normal,rightend:d.LineEnd.Normal},n=new c.TrendLineRenderer;n.setData(e),m.append(n)}if(p.showTopLabels.value()){const i={points:[t],text:D.format(T[e].coeff),color:T[e].color,vertAlign:"bottom",horzAlign:"center",font:_.CHART_FONT_FAMILY,offsetX:0,offsetY:5,fontsize:12};this._topTextRenderers[e].setData(i),m.append(this._topTextRenderers[e])}if(p.showBottomLabels.value()){const t={points:[i],text:D.format(T[e].coeff),color:T[e].color,vertAlign:"top",horzAlign:"center",font:_.CHART_FONT_FAMILY,offsetX:0,offsetY:5,fontsize:12};this._bottomTextRenderers[e].setData(t),m.append(this._bottomTextRenderers[e])}}const k=p.fillBackground.value(),N=p.transparency.value();for(let e=0;e<x.length;e++){const t=new r.Point(b.x,x[e].y);if(e>0&&k){const i={p1:y,p2:t,p3:y,p4:new r.Point(b.x,x[e-1].y),color:x[e].color,transparency:N,hittestOnBackground:!0,extendLeft:!1},n=new a.ChannelRenderer;n.setData(i),m.append(n)}{const i={points:[y,t],color:x[e].color,linewidth:p.linewidth.value(),linestyle:p.linestyle.value(),extendleft:!1,extendright:!0,leftend:d.LineEnd.Normal,rightend:d.LineEnd.Normal},n=new c.TrendLineRenderer;n.setData(i),n.setHitTest(new o.HitTestResult(o.HitTarget.MovePoint,void 0,{type:"h",index:x[e].index})),m.append(n)}}for(let e=0;e<T.length;e++){const t=new r.Point(T[e].x,b.y);if(e>0&&k){const i={p1:y,p2:t,p3:y,p4:new r.Point(T[e-1].x,b.y),color:T[e].color,transparency:N,hittestOnBackground:!0,extendLeft:!1},n=new a.ChannelRenderer;n.setData(i),m.append(n)}{const i={points:[y,t],color:T[e].color,linewidth:p.linewidth.value(),
linestyle:p.linestyle.value(),extendleft:!1,extendright:!0,leftend:d.LineEnd.Normal,rightend:d.LineEnd.Normal},n=new c.TrendLineRenderer;n.setData(i);const r={type:"v",index:T[e].index};n.setHitTest(new o.HitTestResult(o.HitTarget.MovePoint,void 0,r)),m.append(n)}}this.addAnchors(m),this._renderer=m}}},39029:(e,t,i)=>{"use strict";i.r(t),i.d(t,{FibSpiralPaneView:()=>_});var n=i(95201),r=i(49857),s=i(91046),o=i(27916),a=i(56468),l=i(37743),d=i(61993),h=i(75919);const c=[0,1,2,3,5,8,13,21,34,55,89];class u extends h.MediaCoordinatesPaneRenderer{constructor(){super(...arguments),this._data=null}setData(e){this._data=e}hitTest(e){if(null===this._data||this._data.points.length<2)return null;const t=this._data.points[0],i=this._data.points[1].subtract(t),n=e.subtract(t),r=i.normalized(),s=r.transposed(),o=n.normalized();let l=Math.acos(r.dotProduct(o));Math.asin(s.dotProduct(o))<0&&(l=2*Math.PI-l);const h=this._data.counterclockwise?-1:1,c=n.length(),u=(0,d.interactionTolerance)().curve;for(let e=0;e<4;e++){const t=h*l/(.5*Math.PI);let n=this._continiusFib(t+4*e);if(null!==n&&(n=n*i.length()/5,Math.abs(n-c)<u))return new a.HitTestResult(a.HitTarget.MovePoint)}return null}_drawImpl(e){if(null===this._data||this._data.points.length<2)return;const t=e.context;t.lineCap="round",t.strokeStyle=this._data.color;const i=this._data.points[0],n=this._data.points[1];t.translate(i.x,i.y);let r=n.subtract(i);const s=r.length();r=r.normalized();let o=Math.acos(r.x);Math.asin(r.y)<0&&(o=2*Math.PI-o),t.rotate(o),t.scale(s/5,s/5),t.lineWidth=this._data.linewidth,(0,l.setLineStyle)(t,this._data.linestyle);const a=Math.PI/100;t.moveTo(0,0);const d=this._data.counterclockwise?-1:1;for(let e=0;e<50*(c.length-1);e++){const i=d*e*a,n=this._continiusFib(e/50);if(null===n)break;const r=Math.cos(i)*n,s=Math.sin(i)*n;t.lineTo(r,s)}t.scale(5/s,5/s),t.rotate(-o),t.stroke()}_continiusFib(e){const t=Math.floor(e),i=Math.ceil(e);if(i>=c.length)return null;let n=e-t;n=Math.pow(n,1.15);return c[t]+(c[i]-c[t])*n}}class _ extends o.LineSourcePaneView{constructor(){super(...arguments),this._trendLineRenderer=new s.TrendLineRenderer,this._spiralRenderer=new u,this._renderer=null}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){if(super._updateImpl(e),this._renderer=null,this._points.length<2)return;const t=new n.CompositeRenderer,i=this._source.properties().childs();{const e={points:[this._points[0],this._points[1]],color:i.linecolor.value(),linewidth:i.linewidth.value(),linestyle:i.linestyle.value(),extendleft:!1,extendright:!0,leftend:r.LineEnd.Normal,rightend:r.LineEnd.Normal};this._trendLineRenderer.setData(e),t.append(this._trendLineRenderer)}{const e={points:this._points,color:i.linecolor.value(),linewidth:i.linewidth.value(),linestyle:i.linestyle.value(),counterclockwise:i.counterclockwise.value()};this._spiralRenderer.setData(e),t.append(this._spiralRenderer)}this.addAnchors(t),this._renderer=t}}},18449:(e,t,i)=>{"use strict";i.r(t),i.d(t,{FibTimeZonePaneView:()=>p})
;var n=i(86441),r=i(95173),s=i(17330),o=i(62189),a=i(91046),l=i(56468),d=i(95201),h=i(49857),c=i(15938),u=i(27916),_=i(36036);class p extends u.LineSourcePaneView{constructor(e,t){super(e,t),this._levels=[],this._trendRenderer=new a.TrendLineRenderer,this._textRenderers=[],this._renderer=new d.CompositeRenderer;for(let t=0;t<e.levelsCount();t++)this._textRenderers.push(new s.TextRenderer)}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){var t;if(super._updateImpl(e),this._renderer.clear(),this._source.points().length<1)return;const i=this._model.timeScale();if(i.isEmpty())return;const[a,d]=this._source.points(),u=this._source.properties().childs(),p=a.index;if(null===i.visibleBarsStrictRange())return;this._levels=[];const g=d?d.index-a.index:1;for(let e=1;e<=this._source.levelsCount();e++){const t=u["level"+e].childs();if(!t.visible.value())continue;const n=Math.round(p+t.coeff.value()*g),r={index:e,x:i.indexToCoordinate(n),color:t.color.value(),width:t.linewidth.value(),style:t.linestyle.value(),text:String(t.coeff.value())};this._levels.push(r)}const{mediaSize:{width:x,height:f}}=e;if(u.fillBackground.value()){const e=u.transparency.value();for(let t=1;t<this._levels.length;t++){const i=this._levels[t-1],r={points:[new n.Point(this._levels[t].x,0),new n.Point(i.x,f)],color:this._levels[t].color,linewidth:0,backcolor:this._levels[t].color,fillBackground:!0,transparency:e,extendLeft:!1,extendRight:!1},s=new o.RectangleRenderer(!0);s.setData(r),this._renderer.append(s)}}let v=u.horzLabelsAlign.value();v="left"===v?"right":"right"===v?"left":"center";const T=u.vertLabelsAlign.value();for(let e=0;e<this._levels.length;e++){let i;const o=this._levels[e].color;if(u.showLabels.value()){let r;switch(T){case"top":r=new n.Point(this._levels[e].x,0);break;case"middle":r=new n.Point(this._levels[e].x,.5*f);break;default:r=new n.Point(this._levels[e].x,f)}const a={points:[r],text:this._levels[e].text,color:o,vertAlign:T,horzAlign:v,font:c.CHART_FONT_FAMILY,offsetX:2,offsetY:0,fontsize:12},l=this._textRenderers[e];l.setData(a),this._needLabelExclusionPath(l)&&(i=null!==(t=(0,s.getTextBoundaries)(l,x,f))&&void 0!==t?t:void 0),this._renderer.append(l)}const a={x:this._levels[e].x,color:o,linewidth:this._levels[e].width,linestyle:this._levels[e].style,excludeBoundaries:i},d=new l.HitTestResult(l.HitTarget.MovePoint,void 0,this._levels[e].index),h=new r.VerticalLineRenderer;h.setData(a),h.setHitTest(d),this._renderer.append(h)}if(2===this._points.length){const e=u.trendline.childs(),t={points:[this._points[0],this._points[1]],color:e.color.value(),linewidth:e.linewidth.value(),linestyle:e.linestyle.value(),extendleft:!1,extendright:!1,leftend:h.LineEnd.Normal,rightend:h.LineEnd.Normal};this._trendRenderer.setData(t),this._renderer.append(this._trendRenderer)}2===this._source.points().length?this._renderer.append(this.createLineAnchor({points:this._points},0)):this._points.length>0&&this._renderer.append(this.createLineAnchor({points:[(0,_.anchor)({x:this._points[0].x,y:f/2,
hitTarget:l.HitTarget.MovePoint})]},0))}_needLabelExclusionPath(e){return"center"===this._source.properties().childs().horzLabelsAlign.value()}}},84417:(e,t,i)=>{"use strict";i.r(t),i.d(t,{FibWedgePaneView:()=>c});var n=i(50151),r=i(86441),s=i(72609),o=i(91046),a=i(56468),l=i(95201),d=i(55053),h=i(49857);class c extends s.LineToolPaneViewFibWithLabels{constructor(){super(...arguments),this._renderer=null,this._levels=[],this._baseTrendRenderer=new o.TrendLineRenderer,this._edgeTrendRenderer=new o.TrendLineRenderer}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){if(super._updateImpl(e),this._renderer=null,this._levels=[],this._points.length<3)return void this._updateRenderer();const[t,i,s]=this._points,o=i.subtract(t).normalized(),a=s.subtract(t).normalized(),l=new r.Point(1,0),d=new r.Point(0,1);let h=Math.acos(o.dotProduct(l));o.dotProduct(d)<0&&(h=2*Math.PI-h);let c=Math.acos(a.dotProduct(l));if(a.dotProduct(d)<0&&(c=2*Math.PI-c),c<h&&([h,c]=[c,h]),Math.abs(h-c)>Math.PI){const e=Math.min(h,c);h=Math.max(h,c),c=e+2*Math.PI}const u=this._source.properties();for(let e=1;e<=this._source.levelsCount();e++){const r="level"+e,s=(0,n.ensureDefined)(u.child(r));if(!s.childs().visible.value())continue;const l=s.childs().coeff.value(),d=s.childs().color.value(),h=Math.abs(i.subtract(t).length()*l),c=o.add(a).scaled(.5).normalized().scaled(h),_=t.add(c);this._levels.push({coeff:l,color:d,radius:h,labelPoint:_,p1:t.add(o.scaled(h)),p2:t.add(a.scaled(h)),linewidth:s.childs().linewidth.value(),linestyle:s.childs().linestyle.value(),index:e})}this._points.length<2||this._updateRenderer(h,c)}_updateRenderer(e=NaN,t=NaN){if(this._points.length<2)return;const i=new l.CompositeRenderer,n=this._source.properties().childs(),[r,s]=this._points,o=n.trendline.childs().visible.value()?n.trendline.childs().linewidth.value():0,c=n.trendline.childs().linestyle.value();if(this._baseTrendRenderer.setData({points:[r,s],color:n.trendline.childs().color.value(),linewidth:o,linestyle:c,extendleft:!1,extendright:!1,leftend:h.LineEnd.Normal,rightend:h.LineEnd.Normal}),i.append(this._baseTrendRenderer),this._points.length<3)return this.addAnchors(i),void(this._renderer=i);let u=this._points[2];const _=u.pointIndex,p=s.subtract(r).length(),g=u.subtract(r).normalized();u=r.add(g.scaled(p)),u.pointIndex=_,this._edgeTrendRenderer.setData({points:[r,u],color:n.trendline.childs().color.value(),linewidth:o,linestyle:c,extendleft:!1,extendright:!1,leftend:h.LineEnd.Normal,rightend:h.LineEnd.Normal}),i.append(this._edgeTrendRenderer);for(let r=this._levels.length-1;r>=0;r--){const s=this._levels[r],o=new d.ArcWedgeRenderer;o.setData({center:this._points[0],radius:s.radius,prevRadius:r>0?this._levels[r-1].radius:0,color:s.color,linewidth:s.linewidth,angle1:e,angle2:t,p1:s.p1,p2:s.p2,fillBackground:n.fillBackground.value(),transparency:n.transparency.value(),color1:"",color2:""}),o.setHitTest(new a.HitTestResult(a.HitTarget.MovePoint,void 0,s.index)),i.append(o);const l=this._updateLabelForLevel({levelIndex:s.index,
color:s.color,leftPoint:s.labelPoint,rightPoint:s.labelPoint,price:0,horzAlign:"left",vertAlign:"middle"});null!==l&&i.append(l)}const x=[r,s];this._model.lineBeingCreated()!==this._source&&x.push(u),i.append(this.createLineAnchor({points:x},0)),this._renderer=i}}},36134:(e,t,i)=>{"use strict";i.r(t),i.d(t,{FlagMarkPaneView:()=>u});var n,r=i(27916),s=i(95201),o=i(11064),a=i(56468),l=i(34026),d=i(37743),h=i(75919);!function(e){e[e.Width=20]="Width",e[e.Height=22]="Height"}(n||(n={}));class c extends h.MediaCoordinatesPaneRenderer{constructor(){super(...arguments),this._data=null}setData(e){this._data=e}hitTest(e){if(null===this._data)return null;const{x:t,y:i}=this._data.point;return e.x<t||e.x>t+20||e.y<i-22||e.y>i?null:new a.HitTestResult(a.HitTarget.MovePoint)}doesIntersectWithBox(e){return null!==this._data&&(0,l.pointInBox)(this._data.point,e)}_drawImpl(e){if(null===this._data)return;const t=e.context;t.translate(Math.round(this._data.point.x)-.5,Math.round(this._data.point.y-22)-.5),t.fillStyle="#434651",(0,d.drawRoundRect)(t,0,0,2,22,1),t.fill(),t.fillStyle=this._data.color,t.beginPath(),t.moveTo(6.87,0),t.bezierCurveTo(5.62,0,4.46,.23,3.32,.69),t.bezierCurveTo(3.26,.71,3.2,.75,3.15,.8),t.bezierCurveTo(3.06,.89,3,1.02,3,1.16),t.lineTo(3,1.19),t.lineTo(3,12.5),t.bezierCurveTo(3,12.8,3.3,13.02,3.59,12.93),t.bezierCurveTo(4.61,12.64,5.94,12.44,6.87,12.44),t.bezierCurveTo(8.5,12.44,10.09,12.83,11.63,13.21),t.bezierCurveTo(13.19,13.6,14.79,14,16.45,14),t.bezierCurveTo(17.59,14,18.65,13.81,19.69,13.43),t.bezierCurveTo(19.88,13.36,20,13.18,20,12.98),t.lineTo(20,1.19),t.bezierCurveTo(20,1.06,19.83,.93,19.66,.99),t.bezierCurveTo(18.63,1.38,17.58,1.56,16.45,1.56),t.bezierCurveTo(14.82,1.56,13.23,1.17,11.69,.79),t.bezierCurveTo(10.14,.4,8.53,0,6.87,0),t.closePath(),t.fill()}}class u extends r.LineSourcePaneView{constructor(){super(...arguments),this._flagMarkRenderer=new c,this._renderer=null,this._anchorsOffset=null}setAnchors(e){this._anchorsOffset=e}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){if(super._updateImpl(e),this._renderer=null,1!==this._points.length)return;this._flagMarkRenderer.setData({point:this._points[0],color:this._getSource().properties().childs().flagColor.value()});const t=this._getModel();this._renderer=new s.CompositeRenderer,this._renderer.append(this._flagMarkRenderer);const i=[this._anchorsOffset?this._points[0].add(this._anchorsOffset):this._points[0].clone()];this._renderer.append(new o.SelectionRenderer({points:i,bgColors:this._lineAnchorColors(i),visible:this.areAnchorsVisible(),barSpacing:t.timeScale().barSpacing(),hittestResult:a.HitTarget.MovePoint}))}}},73050:(e,t,i)=>{"use strict";i.r(t),i.d(t,{FlatBottomPaneView:()=>_});var n=i(50151),r=i(86441),s=i(49483),o=i(62317),a=i(91046),l=i(17330),d=i(95201),h=i(15938),c=i(27916),u=i(36036);class _ extends c.LineSourcePaneView{constructor(){super(...arguments),this._trendLineRendererPoints12=new a.TrendLineRenderer,this._trendLineRendererPoints43=new a.TrendLineRenderer,
this._disjointChannelRenderer=new o.DisjointChannelRenderer,this._p1LabelRenderer=new l.TextRenderer,this._p2LabelRenderer=new l.TextRenderer,this._p3LabelRenderer=new l.TextRenderer,this._p4LabelRenderer=new l.TextRenderer,this._labelTextRenderer=new l.TextRenderer,this._renderer=new d.CompositeRenderer}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){var t;if(super._updateImpl(e),this._renderer.clear(),this._source.points().length<2)return;const i=this._source.priceScale(),o=null===(t=this._source.ownerSource())||void 0===t?void 0:t.firstValue();if(!i||null==o)return;const[a,l]=this._source.points(),d=i.formatPrice(a.price,o),c=i.formatPrice(l.price,o);let _;if(3===this._source.points().length){const e=this._source.points()[2];_=i.formatPrice(e.price,o)}if(this._points.length<2)return;const[p,g]=this._points;let x,f;const v=this._source.properties().childs();if(3===this._points.length){if(x=(0,r.point)(g.x,this._points[2].y),f=(0,r.point)(p.x,x.y),v.fillBackground.value()){const e=v.extendLeft.value(),t=v.extendRight.value();this._disjointChannelRenderer.setData({extendleft:e,extendright:t,points:[p,g,x,f],backcolor:v.backgroundColor.value(),transparency:v.transparency.value(),hittestOnBackground:s.CheckMobile.any()}),this._renderer.append(this._disjointChannelRenderer)}v.labelVisible.value()&&v.labelText.value()&&this._renderer.append(this._getLabelTextRenderer(p,g,f,x))}const T=(e,t)=>({points:[e,t],color:v.linecolor.value(),linewidth:v.linewidth.value(),linestyle:v.linestyle.value(),extendleft:v.extendLeft.value(),extendright:v.extendRight.value(),leftend:v.leftEnd.value(),rightend:v.rightEnd.value()});if(this._trendLineRendererPoints12.setData(T(p,g)),this._renderer.append(this._trendLineRendererPoints12),2===this._points.length)return void this.addAnchors(this._renderer);const w=(e,t,i,n,r,s)=>{v.showPrices.value()&&(e.setData({points:[i],text:r,color:v.textcolor.value(),horzAlign:i.x>n.x?"left":"right",vertAlign:"middle",font:h.CHART_FONT_FAMILY,offsetX:6,offsetY:0,boxPadding:0,bold:v.bold.value(),italic:v.italic.value(),fontsize:v.fontsize.value(),forceTextAlign:!0}),this._renderer.append(e),t.setData({points:[n],text:s,color:v.textcolor.value(),horzAlign:i.x<n.x?"left":"right",vertAlign:"middle",font:h.CHART_FONT_FAMILY,offsetX:6,offsetY:0,boxPadding:0,bold:v.bold.value(),italic:v.italic.value(),fontsize:v.fontsize.value(),forceTextAlign:!0}),this._renderer.append(t))};if(w(this._p1LabelRenderer,this._p2LabelRenderer,p,g,d,c),!x||!f)return void this.addAnchors(this._renderer);this._trendLineRendererPoints43.setData(T(f,x)),this._renderer.append(this._trendLineRendererPoints43),w(this._p3LabelRenderer,this._p4LabelRenderer,x,f,(0,n.ensureDefined)(_),(0,n.ensureDefined)(_));const R=[(0,u.anchor)({...p}),(0,u.anchor)({...g}),(0,u.anchor)({...x,pointIndex:2}),(0,u.anchor)({...f,pointIndex:3})];this._model.lineBeingCreated()===this._source&&R.pop(),this._renderer.append(this.createLineAnchor({points:R},0)),p&&g&&this._addAlertRenderer(this._renderer,[p,g])}
_getLabelTextRenderer(e,t,i,n){const r=this._source.properties().childs();let s,o;const a=r.labelFontSize.value()/3;let l=0;switch(r.labelVertAlign.value()){case"bottom":e.y<i.y?(s=e,o=t):(s=i,o=n);break;case"top":e.y>i.y?(s=e,o=t):(s=i,o=n);break;case"middle":s=e.add(i).scaled(.5),o=t.add(n).scaled(.5),l=a}const d=s.x<o.x?s:o,c=d===s?o:s;let u;switch(r.labelHorzAlign.value()){case"left":u=d;break;case"right":u=c;break;default:u=d.add(c).scaled(.5)}return this._labelTextRenderer.setData({points:[u],color:r.labelTextColor.value(),fontSize:r.labelFontSize.value(),text:r.labelText.value(),font:h.CHART_FONT_FAMILY,bold:r.labelBold.value(),italic:r.labelItalic.value(),vertAlign:r.labelVertAlign.value(),horzAlign:r.labelHorzAlign.value(),offsetX:0,offsetY:0,boxPaddingVert:a,boxPaddingHorz:l,forceTextAlign:!0,angle:Math.atan((d.y-c.y)/(d.x-c.x))}),this._labelTextRenderer}}},17307:(e,t,i)=>{"use strict";i.d(t,{GannArcRenderer:()=>a});var n=i(86441),r=i(19063),s=i(56468),o=i(75919);class a extends o.MediaCoordinatesPaneRenderer{constructor(){super(),this._data=null}setData(e){this._data=e}hitTest(e){if(null===this._data)return null;e=e.subtract(this._data.center);const t=this._data.edge.subtract(this._data.center),i=t.y/t.x;e=new n.Point(e.x,e.y/i);let r=this._data.point.subtract(this._data.center);r=new n.Point(r.x,r.y/i);const o=r.length(),a=e.length();let l=this._data.prevPoint.subtract(this._data.center);l=new n.Point(l.x,l.y/i);const d=l.length();return Math.abs(a-o)<5&&t.x*e.x>=0&&t.y*e.y>=0?new s.HitTestResult(s.HitTarget.MovePoint):this._data.fillBack&&a>=d&&a<=o&&t.x*e.x>=0&&t.y*e.y>=0?new s.HitTestResult(s.HitTarget.MovePointBackground):null}_drawImpl(e){if(null===this._data)return;const t=e.context;t.lineCap="butt",t.strokeStyle=this._data.color,t.lineWidth=this._data.linewidth,t.translate(this._data.center.x,this._data.center.y);const i=this._data.edge.subtract(this._data.center),s=i.y/i.x;let o=this._data.point.subtract(this._data.center);o=new n.Point(o.x,o.y/s);let a=o.length(),l=this._data.prevPoint.subtract(this._data.center);l=new n.Point(l.x,l.y/s);let d=l.length();t.scale(1,s);const h=Math.abs(this._data.edge.x-this._data.center.x);if(Math.abs(a)>h){const e=Math.sign(this._data.edge.x-this._data.center.x)*h;t.rect(0,0,e,e),t.clip()}this._data.fillBack&&(this._data.point.x<this._data.center.x&&(a=-a,d=-d),t.beginPath(),t.moveTo(d,0),t.lineTo(a,0),t.arcTo(a,a,0,a,Math.abs(a)),t.lineTo(0,d),t.arcTo(d,d,d,0,Math.abs(d)),t.fillStyle=(0,r.generateColor)(this._data.color,this._data.transparency,!0),t.fill()),t.beginPath(),this._data.point.x>this._data.center.x?t.arc(0,0,Math.abs(a),0,Math.PI/2,!1):t.arc(0,0,Math.abs(a),-Math.PI/2,-Math.PI,!0),t.scale(1,1/s),t.stroke()}}},73017:(e,t,i)=>{"use strict";i.r(t),i.d(t,{GannComplexPaneView:()=>_});var n,r=i(86441),s=i(27916),o=i(91046),a=i(17330),l=i(95201),d=i(49857),h=i(51056),c=i(17307),u=i(63273);!function(e){e[e.LabelsOffset=10]="LabelsOffset",e[e.LabelsOffsetTop=8]="LabelsOffsetTop",e[e.LabelsOffsetBottom=10]="LabelsOffsetBottom",e[e.ArcMaxLevel=5]="ArcMaxLevel"
}(n||(n={}));class _ extends s.LineSourcePaneView{constructor(e,t){super(e,t),this._verticalLevelsRenderers=[],this._horizontalLevelsRenderers=[],this._fanRenderers=[],this._arcRenderers=[],this._priceDiffTextRenderer=new a.TextRenderer,this._indexDiffTextRenderer=new a.TextRenderer,this._ratioTextRenderer=new a.TextRenderer,this._renderer=null,this._initRenderers()}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){super._updateImpl(e),this._renderer=null;const t=new l.CompositeRenderer,i=this._getPoints();if(i.length<2)return this.addAnchors(t),void(this._renderer=t);let[n,r]=i;const s=this._getSource(),o=s.isReversed();o&&([r,n]=i);const a=r.x-n.x,d=r.y-n.y,h=n,c=r,u=this._getModel(),_={barsCoordsRange:a,priceCoordsRange:d,startPoint:h,endPoint:c,p1:n,p2:r,isLabelsVisible:s.isLabelsVisible(),reversed:o};this._prepareLevels(t,_),this._prepareFanLines(t,_),this._prepareArcs(t,_),this._prepareLabels(t,_);const p=[n,r];u.lineBeingCreated()===s&&p.pop(),t.append(this.createLineAnchor({points:p},0)),this._renderer=t}_initRenderers(){const e=this._getSource(),t=e.levelsCount();for(let e=0;e<t;e++)this._verticalLevelsRenderers.push(new o.TrendLineRenderer),this._horizontalLevelsRenderers.push(new o.TrendLineRenderer);const i=e.fanLinesCount();for(let e=0;e<i;e++)this._fanRenderers.push(new o.TrendLineRenderer);const n=e.arcsCount();for(let e=0;e<n;e++)this._arcRenderers.push(new c.GannArcRenderer)}_prepareLevels(e,t){const{startPoint:i,endPoint:n,barsCoordsRange:s,priceCoordsRange:o}=t,a=this._getSource().levels();for(const t of a){if(!t.visible)continue;const a=t.index/5,l=i.x+a*s,c={points:[new r.Point(l,i.y),new r.Point(l,n.y)],color:t.color,linewidth:t.width,linestyle:h.LINESTYLE_SOLID,extendleft:!1,extendright:!1,leftend:d.LineEnd.Normal,rightend:d.LineEnd.Normal},u=this._verticalLevelsRenderers[t.index];u.setData(c),e.append(u);const _=i.y+a*o,p={points:[new r.Point(i.x,_),new r.Point(n.x,_)],color:t.color,linewidth:t.width,linestyle:h.LINESTYLE_SOLID,extendleft:!1,extendright:!1,leftend:d.LineEnd.Normal,rightend:d.LineEnd.Normal},g=this._horizontalLevelsRenderers[t.index];g.setData(p),e.append(g)}}_prepareFanLines(e,t){const{p1:i,startPoint:n,endPoint:s,barsCoordsRange:o,priceCoordsRange:a}=t,l=this._getSource().fanLines();for(const t of l){if(!t.visible)continue;const l=t.x,c=t.y;let u,_;if(l>c){u=s.x;const e=c/l;_=n.y+e*a}else{_=s.y;const e=l/c;u=n.x+e*o}const p={points:[i,new r.Point(u,_)],color:t.color,linewidth:t.width,linestyle:h.LINESTYLE_SOLID,extendleft:!1,extendright:!1,leftend:d.LineEnd.Normal,rightend:d.LineEnd.Normal},g=this._fanRenderers[t.index];g.setData(p),e.append(g)}}_prepareArcs(e,t){const{p1:i,startPoint:n,endPoint:s,barsCoordsRange:o,priceCoordsRange:a}=t;let l=i;const d=this._getSource(),h=d.isArcsBackgroundFilled(),c=d.arcsBackgroundTransparency(),u=d.arcs();for(const t of u){if(!t.visible)continue;const i=t.x/5,d=t.y/5,u=n.x+i*o,_=n.y+d*a,p={center:n,point:new r.Point(u,_),edge:s,color:t.color,linewidth:t.width,fillBack:h,transparency:c,prevPoint:l
},g=this._arcRenderers[t.index];g.setData(p),e.append(g),l=p.point}}_prepareLabels(e,t){const{p1:i,p2:n,isLabelsVisible:s,reversed:o}=t;if(!s)return;const a=this._getSource(),l=a.ownerSource();let d=a.getPriceDiff(),h=a.getIndexDiff();if(null===d||null===h||null===l)return;o&&(d=-d,h=-h);const c=new r.Point(i.x,n.y),_=(0,u.forceLTRStr)(l.formatter().format(d)),p=this._getLabelData(c,_);p.horzAlign=h>0?"right":"left",p.vertAlign=d>0?"bottom":"top",p.offsetX=10,p.offsetY=d>0?8:10,p.forceTextAlign=!0,this._priceDiffTextRenderer.setData(p),e.append(this._priceDiffTextRenderer);const g=new r.Point(n.x,i.y),x=(0,u.forceLTRStr)(h.toString()),f=this._getLabelData(g,x);f.horzAlign=h>0?"left":"right",f.vertAlign=d>0?"top":"bottom",f.offsetX=10,f.offsetY=d>0?10:8,f.forceTextAlign=!0,this._indexDiffTextRenderer.setData(f),e.append(this._indexDiffTextRenderer);const v=a.getScaleRatio();if(null===v)return;const T=a.getScaleRatioFormatter(),w=(0,u.forceLTRStr)(T.format(v)),R=this._getLabelData(n,w);R.horzAlign=h>0?"left":"right",R.vertAlign=d>0?"bottom":"top",R.offsetX=10,R.offsetY=d>0?8:10,R.forceTextAlign=!0,this._ratioTextRenderer.setData(R),e.append(this._ratioTextRenderer)}_getLabelData(e,t){const i=this._getSource(),{textColor:n,font:r,fontSize:s,bold:o,italic:a}=i.getLabelsStyle();return{points:[e],backgroundColor:"transparent",text:t,font:r,bold:o,italic:a,fontsize:s,color:n,vertAlign:"top",horzAlign:"center",offsetX:0,offsetY:0,backgroundRoundRect:4}}}},79890:(e,t,i)=>{"use strict";i.r(t),i.d(t,{GannFanPaneView:()=>u});var n=i(86441),r=i(56468),s=i(65395),o=i(95201),a=i(49857),l=i(17330),d=i(91046),h=i(27916),c=i(15938);class u extends h.LineSourcePaneView{constructor(e,t){super(e,t),this._textRenderers=[],this._renderer=null;for(let t=0;t<e.levelsCount();t++)this._textRenderers.push(new l.TextRenderer)}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){if(super._updateImpl(e),this._renderer=null,this._source.points().length<2)return;const t=this._source.priceScale();if(!t||t.isEmpty()||this._model.timeScale().isEmpty())return;if(this._points.length<2)return;const i=this._points[0],l=this._points[1],h=[],u=l.x-i.x,_=l.y-i.y,p=this._source.properties().childs();for(let e=1;e<=this._source.levelsCount();e++){const t="level"+e,n=this._source.properties().child(t).childs();if(!n.visible.value())continue;const r=n.coeff1.value(),s=n.coeff2.value(),o=r/s,a=n.color.value(),d=r+"/"+s;let c,p;r>s?(c=l.x,p=i.y+_/o):(c=i.x+u*o,p=l.y),h.push({label:d,color:a,x:c,y:p,linewidth:n.linewidth.value(),linestyle:n.linestyle.value(),index:e})}const g=new o.CompositeRenderer,x=p.fillBackground.value(),f=p.transparency.value();for(let e=0;e<h.length;e++){const t=new n.Point(h[e].x,h[e].y);if(x)if(h[e].index<4){const r={p1:i,p2:t,p3:i,p4:new n.Point(h[e+1].x,h[e+1].y),color:h[e].color,transparency:f,hittestOnBackground:!0,extendLeft:!1},o=new s.ChannelRenderer;o.setData(r),g.append(o)}else if(h[e].index>4&&e>0){const r={p1:i,p2:t,p3:i,p4:new n.Point(h[e-1].x,h[e-1].y),color:h[e].color,transparency:f,
hittestOnBackground:!0,extendLeft:!1},o=new s.ChannelRenderer;o.setData(r),g.append(o)}{const n={points:[i,t],color:h[e].color,linewidth:h[e].linewidth,linestyle:h[e].linestyle,extendleft:!1,extendright:!0,leftend:a.LineEnd.Normal,rightend:a.LineEnd.Normal},s=new d.TrendLineRenderer;s.setData(n),s.setHitTest(new r.HitTestResult(r.HitTarget.MovePoint,void 0,h[e].index)),g.append(s)}if(p.showLabels.value()){const i={points:[t],text:h[e].label,color:h[e].color,vertAlign:"middle",horzAlign:"left",font:c.CHART_FONT_FAMILY,offsetX:0,offsetY:5,fontsize:12},n=this._textRenderers[e];n.setData(i),g.append(n)}}this.addAnchors(g),this._renderer=g}}},57736:(e,t,i)=>{"use strict";i.r(t),i.d(t,{GannFixedPaneView:()=>c});var n,r=i(86441),s=i(27916),o=i(91046),a=i(95201),l=i(49857),d=i(51056),h=i(17307);!function(e){e[e.ArcMaxLevel=5]="ArcMaxLevel"}(n||(n={}));class c extends s.LineSourcePaneView{constructor(e,t){super(e,t),this._verticalLevelsRenderers=[],this._horizontalLevelsRenderers=[],this._fanRenderers=[],this._arcRenderers=[],this._renderer=null,this._initRenderers()}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){super._updateImpl(e),this._renderer=null;const t=this._getSource(),i=this._getPoints(),n=t.getScreenPoints();if(i.length<2||n.length<2)return;const[r,s]=n;i[1]=r,i[1].pointIndex=1,i[2]=s;const o=this._getPoints(),l=new a.CompositeRenderer;if(o.length<2)return this.addAnchors(l),void(this._renderer=l);const d=o[0],h=3===o.length?o[2]:o[1],c=h.x-d.x,u=h.y-d.y,_=d,p=h,g=this._getModel(),x={barsCoordsRange:c,priceCoordsRange:u,startPoint:_,endPoint:p,p1:d,p2:h};this._prepareLevels(l,x),this._prepareFanLines(l,x),this._prepareArcs(l,x);const f=[d,o[1]];g.lineBeingCreated()===t&&f.pop(),l.append(this.createLineAnchor({points:f},0)),this._renderer=l}_initRenderers(){const e=this._getSource(),t=e.levelsCount();for(let e=0;e<t;e++)this._verticalLevelsRenderers.push(new o.TrendLineRenderer),this._horizontalLevelsRenderers.push(new o.TrendLineRenderer);const i=e.fanLinesCount();for(let e=0;e<i;e++)this._fanRenderers.push(new o.TrendLineRenderer);const n=e.arcsCount();for(let e=0;e<n;e++)this._arcRenderers.push(new h.GannArcRenderer)}_prepareLevels(e,t){const{startPoint:i,endPoint:n,barsCoordsRange:s,priceCoordsRange:o}=t,a=this._getSource().levels();for(const t of a){if(!t.visible)continue;const a=t.index/5,h=i.x+a*s,c={points:[new r.Point(h,i.y),new r.Point(h,n.y)],color:t.color,linewidth:t.width,linestyle:d.LINESTYLE_SOLID,extendleft:!1,extendright:!1,leftend:l.LineEnd.Normal,rightend:l.LineEnd.Normal},u=this._verticalLevelsRenderers[t.index];u.setData(c),e.append(u);const _=i.y+a*o,p={points:[new r.Point(i.x,_),new r.Point(n.x,_)],color:t.color,linewidth:t.width,linestyle:d.LINESTYLE_SOLID,extendleft:!1,extendright:!1,leftend:l.LineEnd.Normal,rightend:l.LineEnd.Normal},g=this._horizontalLevelsRenderers[t.index];g.setData(p),e.append(g)}}_prepareFanLines(e,t){const{p1:i,startPoint:n,endPoint:s,barsCoordsRange:o,priceCoordsRange:a}=t,h=this._getSource().fanLines();for(const t of h){
if(!t.visible)continue;const h=t.x,c=t.y;let u,_;if(h>c){u=s.x;const e=c/h;_=n.y+e*a}else{_=s.y;const e=h/c;u=n.x+e*o}const p={points:[i,new r.Point(u,_)],color:t.color,linewidth:t.width,linestyle:d.LINESTYLE_SOLID,extendleft:!1,extendright:!1,leftend:l.LineEnd.Normal,rightend:l.LineEnd.Normal},g=this._fanRenderers[t.index];g.setData(p),e.append(g)}}_prepareArcs(e,t){const{p1:i,startPoint:n,endPoint:s,barsCoordsRange:o,priceCoordsRange:a}=t;let l=i;const d=this._getSource(),h=d.isArcsBackgroundFilled(),c=d.arcsBackgroundTransparency(),u=d.arcs();for(const t of u){if(!t.visible)continue;const i=t.x/5,d=t.y/5,u=n.x+i*o,_=n.y+d*a,p={center:n,point:new r.Point(u,_),edge:s,color:t.color,linewidth:t.width,fillBack:h,transparency:c,prevPoint:l},g=this._arcRenderers[t.index];g.setData(p),e.append(g),l=p.point}}}},10349:(e,t,i)=>{"use strict";var n=i(86441).Point,r=i(27916).LineSourcePaneView,s=i(17330).TextRenderer,o=i(62189).RectangleRenderer,a=i(91046).TrendLineRenderer,l=i(95201).CompositeRenderer,d=i(43290).getNumericFormatter,h=i(49857).LineEnd,c=i(15938);t.GannSquarePaneView=class extends r{constructor(e,t){super(e,t),this._renderer=null,this._leftLabelRenderers=[],this._rightLabelRenderers=[],this._topLabelRenderers=[],this._bottomLabelRenderers=[];for(var i=0;i<e.hLevelsCount();i++)this._leftLabelRenderers.push(new s),this._rightLabelRenderers.push(new s);for(i=0;i<e.vLevelsCount();i++)this._topLabelRenderers.push(new s),this._bottomLabelRenderers.push(new s)}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){if(super._updateImpl(e),this._renderer=null,this._source.points().length<2)return;if(!this._source.priceScale()||this._source.priceScale().isEmpty()||this._model.timeScale().isEmpty())return;var t=this._source.points()[0],i=this._source.points()[1],r=(C=this._source.properties()).reverse&&C.reverse.value();this._hlevels=[];for(var s=r?t.price-i.price:i.price-t.price,u=r?i.price:t.price,_=this._source.ownerSource().firstValue(),p=1;p<=this._source.hLevelsCount();p++){if((R=C["hlevel"+p]).visible.value()){var g=R.coeff.value(),x=R.color.value(),f=u+g*s,v=this._source.priceScale().priceToCoordinate(f,_);this._hlevels.push({coeff:g,color:x,y:v})}}this._vlevels=[];var T=r?t.index-i.index:i.index-t.index,w=r?i.index:t.index;for(p=1;p<=this._source.vLevelsCount();p++){var R;if((R=C["vlevel"+p]).visible.value()){g=R.coeff.value(),x=R.color.value();var m=Math.round(w+g*T),y=this._model.timeScale().indexToCoordinate(m);this._vlevels.push({coeff:g,color:x,x:y})}}if(this._hfans=[],this._vfans=[],C.fans.visible.value())for(p=1;p<=this._source.hLevelsCount();p++){m=Math.round(w+C["hlevel"+p].coeff.value()*T),f=u+C["vlevel"+p].coeff.value()*s;this._hfans.push(this._model.timeScale().indexToCoordinate(m)),this._vfans.push(this._source.priceScale().priceToCoordinate(f,_))}var b=new l;if(this._points.length<2)return this.addAnchors(b),void(this._renderer=b);t=this._points[0],i=this._points[1]
;var L=Math.min(t.x,i.x),P=Math.min(t.y,i.y),S=Math.max(t.x,i.x),M=Math.max(t.y,i.y),C=this._source.properties(),I=this._source.properties().fillHorzBackground.value(),A=this._source.properties().horzTransparency.value(),D=this._source.properties().fillVertBackground.value(),k=this._source.properties().vertTransparency.value();const N=d();for(p=0;p<this._hlevels.length;p++){if(p>0&&I){var B=this._hlevels[p-1],H={points:[t=new n(L,this._hlevels[p].y),i=new n(S,B.y)],color:this._hlevels[p].color,linewidth:0,backcolor:this._hlevels[p].color,fillBackground:!0,transparency:A,extendLeft:!1,extendRight:!1};(F=new o(!0)).setData(H),b.append(F)}var z={points:[t=new n(L,this._hlevels[p].y),i=new n(S,this._hlevels[p].y)],width:this._model.timeScale().width(),height:this._source.priceScale().height(),color:this._hlevels[p].color,linewidth:C.linewidth.value(),linestyle:C.linestyle.value(),extendleft:!1,extendright:!1,leftend:h.Normal,rightend:h.Normal};if((Y=new a).setData(z),b.append(Y),C.showLeftLabels.value()){var E={points:[t],text:N.format(this._hlevels[p].coeff),color:this._hlevels[p].color,vertAlign:"middle",horzAlign:"right",font:c.CHART_FONT_FAMILY,offsetX:5,offsetY:0,fontsize:12,forceTextAlign:!0},W=this._leftLabelRenderers[p];W.setData(E),b.append(W)}if(C.showRightLabels.value()){var O={points:[i],text:N.format(this._hlevels[p].coeff),color:this._hlevels[p].color,vertAlign:"middle",horzAlign:"left",font:c.CHART_FONT_FAMILY,offsetX:5,offsetY:0,fontsize:12},V=this._rightLabelRenderers[p];V.setData(O),b.append(V)}}for(p=0;p<this._vlevels.length;p++){t=new n(this._vlevels[p].x,P),i=new n(this._vlevels[p].x,M);if(p>0&&D){var F;B=this._vlevels[p-1],H={points:[new n(B.x,P),i],color:this._vlevels[p].color,linewidth:0,backcolor:this._vlevels[p].color,fillBackground:!0,transparency:k,extendLeft:!1,extendRight:!1};(F=new o(!0)).setData(H),b.append(F)}var Y;z={points:[t,i],width:this._model.timeScale().width(),height:this._source.priceScale().height(),color:this._vlevels[p].color,linewidth:C.linewidth.value(),linestyle:C.linestyle.value(),extendleft:!1,extendright:!1,leftend:h.Normal,rightend:h.Normal};if((Y=new a).setData(z),b.append(Y),C.showTopLabels.value()){var j={points:[t],text:N.format(this._vlevels[p].coeff),color:this._vlevels[p].color,vertAlign:"bottom",horzAlign:"center",font:c.CHART_FONT_FAMILY,offsetX:0,offsetY:3,fontsize:12},U=this._topLabelRenderers[p];U.setData(j),b.append(U)}if(C.showBottomLabels.value()){var Q={points:[i],text:N.format(this._vlevels[p].coeff),color:this._vlevels[p].color,vertAlign:"top",horzAlign:"center",font:c.CHART_FONT_FAMILY,offsetX:0,offsetY:5,fontsize:12},q=this._bottomLabelRenderers[p];q.setData(Q),b.append(q)}}var X=this;function Z(e,t,i){var r=new n(L,P),s=new n(S,P),o=new n(L,M),l=new n(S,M),d={width:X._model.timeScale().width(),height:X._source.priceScale().height(),color:C.fans.color.value(),linewidth:C.linewidth.value(),linestyle:C.linestyle.value(),extendleft:!1,extendright:!1,leftend:h.Normal,rightend:h.Normal};function c(t){var i=new a;i.setData(Object.assign({},d,{points:t})),
e.append(i)}for(var u=0;u<t.length;++u){var _=i?M:t[u],p=i?P:t[u],g=i?t[u]:L,x=i?t[u]:S,f=new n(x,_),v=new n(g,_),T=new n(x,p),w=new n(g,p);c([o,T]),c([l,w]),c([r,f]),c([s,v])}}Z(b,this._hfans,!0),Z(b,this._vfans,!1);var G=new n(this._points[0].x,this._points[1].y);G.pointIndex=2;var J=new n(this._points[1].x,this._points[0].y);J.pointIndex=3,b.append(this.createLineAnchor({points:[...this._points,G,J]},0)),this._renderer=b}}},14049:(e,t,i)=>{"use strict";i.r(t),i.d(t,{GhostFeedPaneView:()=>p});var n=i(19625),r=i(51056),s=i(37265),o=i(56468),a=i(95201),l=i(61993),d=i(49857),h=i(91046),c=i(25201),u=i(27916);const _=n.colorsPalette["color-cold-gray-500"];class p extends u.LineSourcePaneView{constructor(){super(...arguments),this._renderer=null,this._segments=[]}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){var t,i;super._updateImpl(e),this._renderer=null,this._segments=[];const n=this._source.priceScale(),u=null!==(i=null===(t=this._source.ownerSource())||void 0===t?void 0:t.firstValue())&&void 0!==i?i:null;if(this._points.length<2||null===n||n.isEmpty()||null===u)return;const p=this._source.points(),g=this._source.properties().childs(),x=g.candleStyle.childs(),f=x.upColor.value(),v=x.downColor.value(),T=x.borderUpColor.value(),w=x.borderDownColor.value();this._segments=this._source.segments().map(((e,t)=>{if(t>=this._points.length-1)return null;let i=this._points[t],r=this._points[t+1],s=p[t].price,o=p[t+1].price,a=1;i.x>r.x&&(i=this._points[t+1],r=this._points[t],s=p[t+1].price,o=p[t].price,a=-1);const l=i.x,d=this._model.timeScale().coordinateToIndex(l),h=n.priceToCoordinate(s,u),c=n.priceToCoordinate(o,u),_=e.bars(),g=(c-h)/(_.length-1),x=[];let R=a>0?0:_.length-1;const m=a>0?_.length:-1;for(let e=0;R!==m;R+=a,e++){const t=h+e*g,i=n.coordinateToPrice(t,u),r=_[R],s=r.c>=r.o;x.push({open:n.priceToCoordinate(i+r.o,u),high:n.priceToCoordinate(i+r.h,u),low:n.priceToCoordinate(i+r.l,u),close:n.priceToCoordinate(i+r.c,u),color:s?f:v,borderColor:s?T:w,hollow:!1,center:NaN,left:NaN,right:NaN,timePointIndex:d+e})}return this._model.timeScale().fillBarBorders(x),{bars:x}})).filter(s.notNull);const R=new a.CompositeRenderer;for(let e=1;e<this._points.length;e++){const t={points:[this._points[e-1],this._points[e]],color:_,linewidth:1,linestyle:r.LINESTYLE_SOLID,extendleft:!1,extendright:!1,leftend:d.LineEnd.Normal,rightend:d.LineEnd.Normal},i=new h.TrendLineRenderer;i.setData(t),i.setHitTest(new o.HitTestResult(o.HitTarget.MovePoint)),R.append(i)}const m=x.drawWick.value(),y=x.drawBorder.value(),b=x.borderColor.value(),L=x.wickColor.value(),P=new a.CompositeRenderer;P.setGlobalAlpha(1-g.transparency.value()/100);const S=this._model.timeScale().barSpacing();for(let e=0;e<this._segments.length;e++){const t={bars:this._segments[e].bars,barSpacing:S,wickVisible:m,bodyVisible:!0,borderVisible:y,borderColor:b,wickColor:L,barWidth:(0,l.optimalBarWidth)(S),hittest:new o.HitTestResult(o.HitTarget.MovePoint),isPriceScaleInverted:n.isInverted()};P.append(new c.PaneRendererCandles(t))}R.append(P),
this.addAnchors(R),this._renderer=R}}},29634:(e,t,i)=>{"use strict";i.r(t),i.d(t,{LineToolHeadAndShouldersPaneView:()=>g});var n=i(5531),r=i(11542),s=i(51056),o=i(91046),a=i(1161),l=i(17330),d=i(95201),h=i(49857),c=i(74011),u=i(27916),_=i(15938);const p={leftShoulder:r.t(null,void 0,i(27443)),rightShoulder:r.t(null,void 0,i(14719)),head:r.t(null,void 0,i(1472))};class g extends u.LineSourcePaneView{constructor(){super(...arguments),this._trendLineRenderer=new o.TrendLineRenderer,this._triangleRendererPoints234=new a.TriangleRenderer,this._intersect1Renderer=new a.TriangleRenderer,this._intersect2Renderer=new a.TriangleRenderer,this._polyLineRenderer=new c.PolygonRenderer,this._leftShoulderLabelRenderer=new l.TextRenderer,this._headLabelRenderer=new l.TextRenderer,this._rightShoulderLabelRenderer=new l.TextRenderer,this._renderer=null}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){let t,i;super._updateImpl(e),this._renderer=null;const[r,o,a,l,c,u,g]=this._points;if(this._points.length>=5){const e=(0,n.intersectLineSegments)(a,c,r,o);if(null!==e){const i=c.subtract(a);t=a.add(i.scaled(e))}if(7===this._points.length){const e=(0,n.intersectLineSegments)(a,c,u,g);if(null!==e){const t=c.subtract(a);i=a.add(t.scaled(e))}}}if(this._points.length<2)return;const x=this._source.properties().childs(),f=new d.CompositeRenderer,v=(e,t)=>({points:[e],text:t,color:x.textcolor.value(),horzAlign:"center",vertAlign:"middle",font:_.CHART_FONT_FAMILY,offsetX:0,offsetY:0,bold:x.bold&&x.bold.value(),italic:x.italic&&x.italic.value(),fontsize:x.fontsize.value(),backgroundColor:x.color.value(),backgroundRoundRect:4}),T=(e,t,i)=>({points:[e,t,i],color:"rgba(0, 0, 0, 0)",linewidth:0,backcolor:x.backgroundColor.value(),fillBackground:x.fillBackground.value(),transparency:x.transparency.value()}),w={points:this._points,color:x.color.value(),linewidth:x.linewidth.value(),linestyle:s.LINESTYLE_SOLID,backcolor:"rgba(0, 0, 0, 0)",fillBackground:!1,filled:!1};if(this._polyLineRenderer.setData(w),f.append(this._polyLineRenderer),this._points.length>=5){let e,n,r=!1,o=!1;t?e=t:(e=a,r=!0),i?n=i:(n=c,o=!0);const d={points:[e,n],color:x.color.value(),linewidth:x.linewidth.value(),linestyle:s.LINESTYLE_DOTTED,extendleft:!1,extendright:!1,leftend:h.LineEnd.Normal,rightend:h.LineEnd.Normal};d.extendleft=r,d.extendright=o,this._trendLineRenderer.setData(d),f.append(this._trendLineRenderer);const u=T(a,l,c);this._triangleRendererPoints234.setData(u),f.append(this._triangleRendererPoints234)}if(t){const e=T(t,o,a);this._intersect1Renderer.setData(e),f.append(this._intersect1Renderer)}if(i){const e=T(c,u,i);this._intersect2Renderer.setData(e),f.append(this._intersect2Renderer)}if(this._points.length>=2){const e=v(o,p.leftShoulder);o.y<r.y?(e.vertAlign="bottom",e.offsetY=5):(e.vertAlign="top",e.offsetY=5),this._leftShoulderLabelRenderer.setData(e),f.append(this._leftShoulderLabelRenderer)}if(this._points.length>=4){const e=v(l,p.head);l.y<a.y?(e.vertAlign="bottom",e.offsetY=5):(e.vertAlign="top",e.offsetY=5),
this._headLabelRenderer.setData(e),f.append(this._headLabelRenderer)}if(this._points.length>=6){const e=v(u,p.rightShoulder);u.y<c.y?(e.vertAlign="bottom",e.offsetY=5):(e.vertAlign="top",e.offsetY=5),this._rightShoulderLabelRenderer.setData(e),f.append(this._rightShoulderLabelRenderer)}this.addAnchors(f),this._renderer=f}}},64332:(e,t,i)=>{"use strict";i.r(t),i.d(t,{HighlighterPaneView:()=>s});var n=i(51056),r=i(70849);class s extends r.BrushBasePaneView{_createPolygonRendererData(){const e=this._source.properties().childs();return{points:this._points,color:e.linecolor.value(),linewidth:20,backcolor:"rgba(0, 0, 0, 0)",fillBackground:!1,linestyle:n.LINESTYLE_SOLID,filled:!1,transparency:e.transparency.value()}}}},38117:(e,t,i)=>{"use strict";i.r(t),i.d(t,{HorzLinePaneView:()=>p});var n=i(86441),r=i(56468),s=i(72791),o=i(17330),a=i(62689),l=i(50600),d=i(95201),h=i(36036),c=i(13075),u=i(15938),_=i(32211);class p extends _.InplaceTextLineSourcePaneView{constructor(e,t,i,n,s){super(e,t,i,n,s),this._renderer=null,this._lineRenderer=new l.HorizontalLineRenderer,this._lineRenderer.setHitTest(new r.HitTestResult(r.HitTarget.MovePoint)),this._labelRenderer=new a.LineToolTextRenderer(void 0,new r.HitTestResult(r.HitTarget.MovePoint,(0,_.inplaceEditHandlers)(this._tryActivateEditMode.bind(this,null)))),this._source.setAdditionalCursorData((()=>({color:this._source.editableTextStyle().cursorColor,...this._labelRenderer.getTextInfo()})),this._labelRenderer.positionToCoordinate.bind(this._labelRenderer))}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){var t,i;if(super._updateImpl(e),this._renderer=null,0===this._points.length)return;const a=this._source.properties().childs(),l=new d.CompositeRenderer;let _=!0;const{mediaSize:{width:p,height:g}}=e,x=a.text.value(),f=this._isTextEditMode(),v=this._placeHolderMode();let T,w=!1;if((null===(t=a.showLabel)||void 0===t?void 0:t.value())&&x||v||f){const e=a.vertLabelsAlign.value(),t=a.horzLabelsAlign.value();let r=0,s=0;"left"===t?s=3:"right"===t?(s=p,r=3):(s=p/2,w=!0);const d=new n.Point(s,this._points[0].y);this._labelRenderer.setData({points:[d],text:this._textData(),color:this._textColor(),vertAlign:e,horzAlign:t,font:u.CHART_FONT_FAMILY,offsetX:r,offsetY:0,bold:a.bold.value(),italic:a.italic.value(),fontsize:a.fontsize.value(),forceTextAlign:!0,decorator:v?c.PlusTextRendererDecorator.instance():void 0,...this._inplaceTextHighlight()}),this._labelRenderer.setCursorType(this._textCursorType()),l.append(this._labelRenderer),this._needLabelExclusionPath(this._labelRenderer)&&(T=null!==(i=(0,o.getTextBoundaries)(this._labelRenderer,p,g))&&void 0!==i?i:void 0),_=this._labelRenderer.isOutOfScreen(p,g),_?this.closeTextEditor():this._updateInplaceText(this._labelRenderer.getTextInfo())}const R={y:this._points[0].y,color:a.linecolor.value(),linewidth:a.linewidth.value(),linestyle:a.linestyle.value(),excludeBoundaries:T};this._lineRenderer.setData(R),this._lineRenderer.setHitTest(new r.HitTestResult(r.HitTarget.MovePoint,{
snappingPrice:this._source.points()[0].price}));const m=R.linewidth/2+1;if(_=_&&(R.y<-m||R.y>g+m),l.insert(this._lineRenderer,0),!_){if(1===this._points.length&&!this._isTextEditMode()){const e=(0,h.anchor)({x:w?.9*p:p/2,y:this._points[0].y,pointIndex:0,square:!0,cursorType:s.PaneCursorType.VerticalResize});l.append(this.createLineAnchor({points:[e]},0))}if(1===this._points.length){const e=new n.Point(this._model.timeScale().width()/2,this._points[0].y);this._addAlertRenderer(l,[e])}this._renderer=l}}}},66451:(e,t,i)=>{"use strict";i.r(t),i.d(t,{HorzRayPaneView:()=>x});var n,r=i(86441),s=i(95201),o=i(17330),a=i(15938),l=i(56468),d=i(27916),h=i(37743),c=i(61993),u=i(7114),_=i(30125),p=i(51056);class g extends _.BitmapCoordinatesPaneRenderer{constructor(){super(...arguments),this._data=null}setData(e){this._data=e}hitTest(e){if(null===this._data||0===this._data.points.length)return null;if(e.x<this._data.points[0].x)return null;const t=(0,c.interactionTolerance)().line;return Math.abs(e.y-this._data.points[0].y)<=t?new l.HitTestResult(this._data.hitTestResult,{snappingPrice:this._data.snappingPrice}):null}_drawImpl(e){if(null===this._data||0===this._data.points.length)return;const{context:t,horizontalPixelRatio:i,verticalPixelRatio:n,bitmapSize:r}=e,s=r.width,o=this._data.points[0].y,a=Math.max(0,this._data.points[0].x),l=Math.max(s,this._data.points[0].x);t.lineCap=void 0===this._data.linestyle||this._data.linestyle===p.LINESTYLE_SOLID?"round":"butt",t.strokeStyle=this._data.color,t.lineWidth=Math.max(1,Math.floor(this._data.linewidth*i)),void 0!==this._data.linestyle&&(0,h.setLineStyle)(t,this._data.linestyle);const d=this._data.excludeBoundaries;void 0!==d&&(0,u.addExclusionAreaByScope)(e,d),(0,h.drawHorizontalLine)(t,Math.round(o*n),Math.round(a*i),Math.round(l*i))}}!function(e){e[e.RightOffset=3]="RightOffset"}(n||(n={}));class x extends d.LineSourcePaneView{constructor(e,t){super(e,t),this._horzRayRenderer=new g,this._labelRenderer=new o.TextRenderer,this._renderer=null,this._horzRayRenderer=new g,this._labelRenderer=new o.TextRenderer,this._renderer=null}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){var t;if(super._updateImpl(e),this._renderer=null,0===this._points.length)return;const i=this._source.properties().childs(),n=new s.CompositeRenderer;let d,h=this._points[0].clone();if(i.showLabel.value()&&1===this._points.length){const s=i.vertLabelsAlign.value(),l=i.horzLabelsAlign.value(),c=0;let u=0;const _=i.text.value(),p=i.bold.value(),g=i.italic.value(),x=a.CHART_FONT_FAMILY,f=i.fontsize.value();if("right"===l){const e=this._labelRenderer.measure().width,t=this._model.timeScale().width();h.x+e+3>=t?h=h.add((0,r.point)(e+3,0)):(h=(0,r.point)(t,h.y),u=3)}else"center"===l&&(h=(0,r.point)((h.x+this._model.timeScale().width())/2,h.y));const v={points:[h],text:_,color:i.textcolor.value(),vertAlign:s,horzAlign:l,font:x,offsetX:u,offsetY:c,bold:p,italic:g,fontsize:f,forceTextAlign:!0};if(this._labelRenderer.setData(v),n.append(this._labelRenderer),
this._needLabelExclusionPath(this._labelRenderer)){const{mediaSize:{width:i,height:n}}=e;d=null!==(t=(0,o.getTextBoundaries)(this._labelRenderer,i,n))&&void 0!==t?t:void 0}}const c={points:this._points,color:i.linecolor.value(),linewidth:i.linewidth.value(),linestyle:i.linestyle.value(),hitTestResult:l.HitTarget.MovePoint,snappingPrice:this._source.points()[0].price,excludeBoundaries:d};this._horzRayRenderer.setData(c),n.append(this._horzRayRenderer),this.addAnchors(n),1===this._points.length&&this._addAlertRenderer(n,[c.points[0]]),this._renderer=n}}},11735:(e,t,i)=>{"use strict";i.r(t),i.d(t,{IconPaneView:()=>r});var n=i(68498);class r extends n.SvgIconPaneView{_iconColor(){return this._source.properties().childs().color.value()}}},26087:(e,t,i)=>{"use strict";i.r(t),i.d(t,{ImagePaneView:()=>_});var n=i(86441),r=i(72791),s=i(95201),o=i(34026),a=i(56468),l=i(75919);class d extends l.MediaCoordinatesPaneRenderer{constructor(e){super(),this._data=null!=e?e:null}hitTest(e){if(!this._data)return null;const{point:t,cssWidth:i,cssHeight:r}=this._data,s=new n.Point(t.x,t.y),l=new n.Point(t.x+i,t.y+r);return(0,o.pointInBox)(e,(0,n.box)(s,l))?new a.HitTestResult(a.HitTarget.MovePoint):null}setData(e){this._data=e}_drawImpl(e){if(!this._data)return;const{angle:t,img:i,point:n,cssWidth:r,cssHeight:s,transparency:o}=this._data,a=e.context;a.globalAlpha=(100-o)/100,Math.abs(t)<1e-4?a.drawImage(i,n.x,n.y,r,s):(a.translate(n.x,n.y),a.rotate(t),a.drawImage(i,0,0,r,s))}}var h=i(36036),c=i(27916),u=i(3503);class _ extends c.LineSourcePaneView{constructor(e,t){super(e,t),this._imageRenderer=new d}renderer(e){const t=this._source.image();if(null===t||(0,u.imageIsOversized)(t))return null;if(this._invalidated&&this._updateImpl(e),!this._points.length)return null;const i=new s.CompositeRenderer;return i.append(this._imageRenderer),this._addAnchors(i),i}_updateImpl(e){super._updateImpl(e);const t=this._source.properties(),i=this._source.image(),n=this._calculateBox();null===i||void 0===n?this._imageRenderer.setData(null):this._imageRenderer.setData({point:n[0],img:i,cssWidth:this._source.cssWidth(),cssHeight:this._source.cssHeight(),angle:0,transparency:t.childs().transparency.value()})}_addAnchors(e){const t=this._calculateBox();if(void 0===t)return;const[i,n]=t,s=[(0,h.anchor)({x:i.x+1,y:i.y+1,pointIndex:0,cursorType:r.PaneCursorType.DiagonalNwSeResize}),(0,h.anchor)({x:n.x-1,y:i.y+1,pointIndex:1,cursorType:r.PaneCursorType.DiagonalNwSeResize}),(0,h.anchor)({x:i.x+1,y:n.y-1,pointIndex:2,cursorType:r.PaneCursorType.DiagonalNwSeResize}),(0,h.anchor)({x:n.x-1,y:n.y-1,pointIndex:3,cursorType:r.PaneCursorType.DiagonalNwSeResize})];e.append(this.createLineAnchor({points:s},0))}_calculateBox(){const e=this._source.cssWidth(),t=this._source.cssHeight(),i=this._source.originPoint(),r=this._source.dOffsetX();let s,o,[a]=this._points;if(a){switch(4!==i&&(a=new n.Point(a.x+r,a.y)),i){case 4:{const i=e/2,r=t/2;s=new n.Point(a.x-i,a.y-r),o=new n.Point(a.x+i,a.y+r);break}case 0:s=new n.Point(a.x,a.y),o=new n.Point(a.x+e,a.y+t);break;case 1:
s=new n.Point(a.x-e,a.y),o=new n.Point(a.x,a.y+t);break;case 2:s=new n.Point(a.x,a.y-t),o=new n.Point(a.x+e,a.y);break;case 3:s=new n.Point(a.x-e,a.y-t),o=new n.Point(a.x,a.y)}return[s,o]}}}},15852:(e,t,i)=>{"use strict";i.r(t),i.d(t,{InplaceTextCursorPaneView:()=>d});var n=i(50151);class r{constructor(e){this._data=null,this._positionToCoordinate=e}setData(e){this._data=e}draw(e,t){const{position:i,rotationPoint:r,lineHeight:s,lineSpacing:o,color:a,alpha:l}=(0,n.ensureNotNull)(this._data);if(void 0===i)return;e.save();const{horizontalPixelRatio:d,verticalPixelRatio:h}=t;if(r&&0!==r.angle){const t=r.x*d,i=r.y*h;e.translate(t,i),e.rotate(r.angle),e.translate(-t,-i)}const c=this._positionToCoordinate(i),u=Math.round(c.x*d),_=Math.round(c.y*h),p=Math.max(Math.floor(d),1),g=Math.round(s*h)+o;e.globalAlpha=null!=l?l:1,e.fillStyle=a,e.fillRect(u,_,p,g),e.restore()}hitTest(e){return null}}var s=i(78871);const o=1e3;function a(e){return{isActive:t=>t-e<o,data:t=>{const i=Math.min(t-e,o);return{alpha:s.easingFunc.linear(i/o)<.5?0:1}}}}var l=i(62288);class d{constructor(e,t){this._renderer=null,this._animation=null,this._animationTimeout=null,this._invalidated=!0,this._model=t,this._cursorRenderer=new r((e=>this._positionToCoordinate(e)))}update(){this._invalidated=!0}renderer(){return this._continueAnimationIfNeeded(),this._invalidated&&this._updateImpl(),this._renderer}setCursorPosition(e){this._cursorPosition!==e&&(this._cursorPosition=e,null!==this._animation&&this._stopAnimation(),this._startAnimation())}setAdditionalCursorData(e,t){this._textAdditionalData=e,this._positionToCoordinate=t}_updateImpl(){if(this._renderer=null,this._invalidated=!1,void 0===this._cursorPosition)return void this._stopAnimation();const e={...this._textAdditionalData(),position:this._cursorPosition},t=this._animationStepData();null!==t&&(e.alpha=t.alpha),this._cursorRenderer.setData(e),this._renderer=this._cursorRenderer}_startAnimation(){this._animationTimeout=setTimeout((()=>{this._animation=a(performance.now()),this._continueAnimationIfNeeded()}),300)}_stopAnimation(){null!==this._animationTimeout&&(clearTimeout(this._animationTimeout),this._animationTimeout=null),this._animation=null}_animationStepData(){if(null===this._animation)return null;const e=performance.now();return this._animation.isActive(e)||(this._animation=a(e)),this._animation.data(e)}_continueAnimationIfNeeded(){null!==this._animation&&this._model.invalidate(l.InvalidationMask.cursor())}}},32211:(e,t,i)=>{"use strict";i.d(t,{InplaceTextLineSourcePaneView:()=>p,inplaceEditHandlers:()=>_});var n=i(50151),r=i(11542),s=i(19063),o=i(91676),a=i(27916),l=i(56468),d=i(35236),h=i(69186),c=i(72791);const u=r.t(null,void 0,i(6060));function _(e){const t=(0,d.ignoreClickOrTapOnDblClickOrDblTapHandlers)({clickOrTap:(t,i)=>{i.sourceWasSelected&&e(t)},doubleClickOrDoubleTap:()=>{}});return{areaName:l.AreaName.Text,executeDefaultAction:{doubleClickHandler:!0,doubleTapHandler:!0},clickHandler:t.clickOrTap,tapHandler:t.clickOrTap,doubleClickHandler:t.doubleClickOrDoubleTap,
doubleTapHandler:t.doubleClickOrDoubleTap}}class p extends a.LineSourcePaneView{constructor(e,t,i,n,r){super(e,t),this._textInfo=new o.WatchedObject({}),this._isTextEditModeActivated=!1,this._textWasEdited=!1,this._showTextEditor=i,this._hideTextEditor=n,this._onSelectionChangeCb=r,this._editableTextSpawn=this._source.editableText().spawn(),this._editableTextSpawn.subscribe((()=>this._textWasEdited=!0))}destroy(){this._editableTextSpawn.destroy()}setSelectionRange(e){this._selectionRange=e}closeTextEditor(){var e;this._textWasEdited=!1,this._isTextEditModeActivated=!1,null===(e=this._hideTextEditor)||void 0===e||e.call(this)}_placeHolderMode(e){var t;return!this._isTextEditMode()&&this._model.hoveredSource()===this._source&&0===this._model.hoveredSourceOrigin()&&(!e||(null===(t=this._model.lastHittestData())||void 0===t?void 0:t.areaName)!==l.AreaName.AnchorPoint)&&!(0,h.lastMouseOrTouchEventInfo)().isTouch&&!this._source.editableTextProperties().text.value()&&this._model.selection().isSelected(this._source)}_textCursorType(){return this._model.selection().isSelected(this._source)&&!this._model.sourcesBeingMoved().includes(this._source)?c.PaneCursorType.Text:void 0}_updateInplaceText(e){this._textInfo.setValue(e),this._model.selection().isSelected(this._source)||this.closeTextEditor();const t=this._source.textEditingEl();t&&this._activateEditMode(t)}_tryActivateEditMode(e,t){const i=(0,n.ensureNotNull)(t.target&&t.target.closest(".chart-gui-wrapper"));this._activateEditMode(i)}_isTextEditMode(){return this._isTextEditModeActivated}_textData(){return this._text()||(this._textWasEdited?"​":u)}_textColor(){const e=this._source.editableTextProperties().textColor.value();return this._text()?e:(0,s.generateColor)(e,50,!0)}_inplaceTextHighlight(){const e=this._source.editableTextStyle();return this._selectionRange?{selectionHighlight:{start:this._selectionRange[0],end:this._selectionRange[1],color:(0,s.generateColor)(e.selectionColor,80,!0)}}:{}}_activateEditMode(e){var t;null===(t=this._showTextEditor)||void 0===t||t.call(this,e,this._textInfo,u,this.closeTextEditor.bind(this),(e=>{var t;return null===(t=this._onSelectionChangeCb)||void 0===t?void 0:t.call(this,e)})),this._isTextEditModeActivated=!0}_text(){return this._isTextEditMode()?this._source.editableText().value():this._source.editableTextProperties().text.value()}}},27916:(e,t,i)=>{"use strict";i.d(t,{LineSourcePaneView:()=>p,thirdPointCursorType:()=>_});var n=i(19625),r=i(50151),s=i(69186),o=i(56468),a=i(11064),l=i(36036),d=i(72791),h=i(17330);const c=n.colorsPalette["color-tv-blue-600"];var u;function _(e,t){const i=t.x-e.x,n=t.y-e.y,r=Math.abs(Math.atan2(i,n));return r>Math.PI/4&&r<3*Math.PI/4?d.PaneCursorType.VerticalResize:d.PaneCursorType.HorizontalResize}!function(e){e[e.RegularAnchorRadius=6]="RegularAnchorRadius",e[e.TouchAnchorRadius=13]="TouchAnchorRadius",e[e.RegularStrokeWidth=1]="RegularStrokeWidth",e[e.TouchStrokeWidth=3]="TouchStrokeWidth",e[e.RegularSelectedStrokeWidth=3]="RegularSelectedStrokeWidth",
e[e.TouchSelectedStrokeWidth=0]="TouchSelectedStrokeWidth"}(u||(u={}));class p{constructor(e,t){this._invalidated=!0,this._points=[],this._middlePoint=null,this._selectionRenderers=[],this._lineAnchorRenderers=[],this._source=e,this._model=t}priceToCoordinate(e){const t=this._source.priceScale();if(null===t)return null;const i=this._source.ownerSource(),n=null!==i?i.firstValue():null;return null===n?null:t.priceToCoordinate(e,n)}currentPoint(){return this._model.crossHairSource().currentPoint()}anchorColor(){return c}isHoveredSource(){return this._source===this._model.hoveredSource()}isSelectedSource(){return this._model.selection().isSelected(this._source)}isBeingEdited(){return this._model.lineBeingEdited()===this._source}isEditMode(){return!this._model.isSnapshot()}areAnchorsVisible(){return(this.isHoveredSource()&&!this.isLocked()||this.isSelectedSource())&&this.isEditMode()}update(){this._invalidated=!0}isLocked(){return Boolean(this._source.isLocked&&this._source.isLocked())}addAnchors(e,t){let i=this._getPoints();this._model.lineBeingCreated()===this._source&&(i=i.slice(0,-1));const n=i.map(((e,t)=>{const i=this._source.points()[t],n=e;return n.snappingPrice=null==i?void 0:i.price,n.snappingIndex=null==i?void 0:i.index,n}));e.append(this.createLineAnchor({...null!=t?t:{},points:n},0))}createLineAnchor(e,t){if(this.isLocked()){const i=this._getSelectionRenderer(t);return i.setData({bgColors:this._lineAnchorColors(e.points),points:e.points,visible:this.areAnchorsVisible(),hittestResult:o.HitTarget.Regular,barSpacing:this._model.timeScale().barSpacing()}),i}const i=(0,s.lastMouseOrTouchEventInfo)().isTouch,n=this._getLineAnchorRenderer(t);return n.setData({...e,color:this.anchorColor(),backgroundColors:this._lineAnchorColors(e.points),currentPoint:this.currentPoint(),linePointBeingEdited:this.isBeingEdited()?this._model.linePointBeingEdited():null,radius:this._anchorRadius(),strokeWidth:i?u.TouchStrokeWidth:u.RegularStrokeWidth,selected:this.isSelectedSource(),selectedStrokeWidth:i?u.TouchSelectedStrokeWidth:u.RegularSelectedStrokeWidth,visible:this.areAnchorsVisible()}),n}_anchorRadius(){return(0,s.lastMouseOrTouchEventInfo)().isTouch?u.TouchAnchorRadius:u.RegularAnchorRadius}_lineAnchorColors(e){const t=(0,r.ensureNotNull)(this._model.paneForSource(this._source)).height();return e.map((e=>this._model.backgroundColorAtYPercentFromTop(e.y/t)))}_updateImpl(e){this._points=[];if(this._model.timeScale().isEmpty())return;if(!this._validatePriceScale())return;const t=this._source.points();for(let e=0;e<t.length;e++){const i=t[e],n=this._source.pointToScreenPoint(i);if(!n)return;const r=n;r.pointIndex=e,this._points.push(r)}2===this._points.length&&(this._middlePoint=this._source.calcMiddlePoint(this._points[0],this._points[1])),this._invalidated=!1}_validatePriceScale(){const e=this._source.priceScale();return null!==e&&!e.isEmpty()}_getSource(){return this._source}_getPoints(){return this._points}_getModel(){return this._model}_height(){const e=this._source.priceScale();return null!==e?e.height():0}_width(){
return this._model.timeScale().width()}_needLabelExclusionPath(e,t){const i=this._source.properties().childs();return"middle"===(null!=t?t:i.vertLabelsAlign.value())&&(0,h.needTextExclusionPath)(e)}_addAlertRenderer(e,t){}_getAlertRenderer(e,t=this._source.properties().linecolor.value(),i){return null}_getSelectionRenderer(e){for(;this._selectionRenderers.length<=e;)this._selectionRenderers.push(new a.SelectionRenderer);return this._selectionRenderers[e]}_getLineAnchorRenderer(e){for(;this._lineAnchorRenderers.length<=e;)this._lineAnchorRenderers.push(new l.LineAnchorRenderer);return this._lineAnchorRenderers[e]}}},8938:(e,t,i)=>{"use strict";i.r(t),i.d(t,{LineToolBeingCreatedPaneView:()=>c});var n=i(19625),r=i(27916),s=i(51056),o=i(49857),a=i(95201),l=i(95173),d=i(91046);const h=n.colorsPalette["color-cold-gray-500"];class c extends r.LineSourcePaneView{constructor(){super(...arguments),this._lineRenderer1=new l.VerticalLineRenderer,this._lineRenderer2=new l.VerticalLineRenderer,this._medianRenderer=new d.TrendLineRenderer,this._renderer=null}renderer(e){return this._invalidated&&(this._updateImpl(e),this._invalidated=!1),this._renderer}_updateImpl(e){super._updateImpl(e),this._renderer=null;const t=this._getPoints();if(t.length<1)return;this._renderer=new a.CompositeRenderer;const[i,n]=t;this._lineRenderer1.setData({x:i.x,color:h,linewidth:1,linestyle:s.LINESTYLE_SOLID}),this._renderer.append(this._lineRenderer1),t.length>1&&(this._lineRenderer2.setData({x:n.x,color:h,linewidth:1,linestyle:s.LINESTYLE_SOLID}),this._medianRenderer.setData({points:[i,n],color:h,linewidth:1,linestyle:s.LINESTYLE_SOLID,extendleft:!1,extendright:!1,leftend:o.LineEnd.Normal,rightend:o.LineEnd.Normal}),this._renderer.append(this._lineRenderer2),this._renderer.append(this._medianRenderer))}}},52898:(e,t,i)=>{"use strict";i.r(t),i.d(t,{NotePaneView:()=>M});var n,r,s=i(50151),o=i(19625),a=i(19063),l=i(68979),d=i(95201),h=i(11064),c=i(56468),u=i(15938),_=i(17330),p=i(2844),g=i(32211),x=i(86441),f=i(34026),v=i(63273),T=i(7114),w=i(64034),R=i(12027),m=i(36696),y=i(64099);function b(e){const{ctx:t,renderingInfo:i,left:n,top:r,width:s,height:o,point:a,caretPos:l,mode:d}=e,{horizontalPixelRatio:h,verticalPixelRatio:c}=i,u=Math.max(1,Math.floor(h))%2/2,_=Math.round(a.x*h)+u,p=0===d?Math.max(1,1*Math.floor(h)):0,g=Math.round(n*h)+p/2,x=Math.round(r*c)+p/2,f=Math.round(o*c)-p,v=Math.round(s*h)-p,T=Math.round(12*h),w=Math.round(10*c),R=_-T/2,m=_+T/2,y=Math.atan(10/6),b=4*Math.cos(y),L=4*Math.sin(y),P=4*h;if(t.beginPath(),t.moveTo(g,x+P),t.arcTo(g,x,g+P,x,P),"top"===l){const e=x,i=x-w;t.lineTo(R-P,e),t.arcTo(R,e,R+b,e-L,P),t.lineTo(_-1.2*b,i+1.2*L),t.arcTo(_,i,_+1.2*b,i+1.2*L,1.2*b),t.lineTo(m-b,e-L),t.arcTo(m,e,m+P,e,P)}if(t.lineTo(g+v-P,x),t.arcTo(g+v,x,g+v,x+P,P),t.lineTo(g+v,x+f-P),t.arcTo(g+v,x+f,g+v-P,x+f,P),"bottom"===l){const e=x+f,i=e+w;t.lineTo(m+P,e),t.arcTo(m,e,m-b,e+L,P),t.lineTo(_+1.2*b,i-1.2*L),t.arcTo(_,i,_-1.2*b,i-1.2*L,1.2*b),t.lineTo(R+b,e+L),t.arcTo(R,e,R-P,e,P)}t.lineTo(g+P,x+f),t.arcTo(g,x+f,g,x+f-P,P),t.closePath()}!function(e){
e[e.MinTooltipWidth=20]="MinTooltipWidth",e[e.TooltipMinWidth=236]="TooltipMinWidth",e[e.TooltipVertMargin=13]="TooltipVertMargin",e[e.TooltipHorzPadding=12]="TooltipHorzPadding",e[e.TooltipVertPadding=12]="TooltipVertPadding",e[e.TooltipLineSpacing=5]="TooltipLineSpacing",e[e.TooltipBorderLineWidth=1]="TooltipBorderLineWidth",e[e.TooltipBorderRadius=4]="TooltipBorderRadius",e[e.TooltipApexBorderRadiusCoeff=1.2]="TooltipApexBorderRadiusCoeff",e[e.CorrectPositionYDistance=10]="CorrectPositionYDistance",e[e.CorrectPositionXDistance=10]="CorrectPositionXDistance",e[e.CaretWidth=12]="CaretWidth",e[e.CaretHeight=10]="CaretHeight",e[e.NoCaretEdgeXDistance=24]="NoCaretEdgeXDistance",e[e.ShadowBlur=4]="ShadowBlur",e[e.ShadowOffsetX=0]="ShadowOffsetX",e[e.ShadowOffsetY=2]="ShadowOffsetY",e[e.SpaceBetweenMarkerAndTooltipTolerance=8]="SpaceBetweenMarkerAndTooltipTolerance"}(n||(n={})),function(e){e[e.Stroke=0]="Stroke",e[e.Fill=1]="Fill"}(r||(r={}));const L=(0,R.svgRenderer)(y);class P{constructor(e){this._data=null,this._tooltipHitTest=new c.HitTestResult(c.HitTarget.MovePoint,{...e,areaName:c.AreaName.Text})}setData(e){this._data=e}setCursorType(e){this._tooltipHitTest.mergeData({cursorType:e})}draw(e,t){if(null===this._data)return;const{horizontalPixelRatio:i,verticalPixelRatio:n}=t,r=Math.max(1,Math.floor(i))%2/2,s=Math.max(1,Math.floor(n))%2/2,{point:o,markerColor:a}=this._data,l=Math.round(o.x*i)+r,d=Math.round(o.y*n)+s,h=L.viewBox();e.fillStyle=a,L.render(e,{targetViewBox:{x:l-i*h.width/2,y:d-i*h.height,width:i*h.width,height:i*h.height},doNotApplyColors:!0}),this._data.tooltipVisible&&this._drawTooltipOn(e,t)}hitTest(e,t){if(null!==this._data){const{point:t,left:i,top:n,width:r,height:s,tooltipVisible:o}=this._data,a=t.x,l=t.y,d=L.viewBox(),h=(0,x.box)((0,x.point)(a-d.width/2,l-d.height),(0,x.point)(a+d.width/2,l));if((0,f.pointInBox)(e,h))return new c.HitTestResult(c.HitTarget.MovePoint);if(o){const t=(0,x.box)((0,x.point)(i,n),(0,x.point)(i+r,n+s));if((0,f.pointInBox)(e,t))return this._tooltipHitTest;const o=t.min.y<h.min.y?t.max.y:h.max.y,a=t.min.y<h.min.y?h.min.y:t.min.y,l=(0,x.box)((0,x.point)(h.min.x-8,o),(0,x.point)(h.max.x+8,a));if((0,f.pointInBox)(e,l))return new c.HitTestResult(c.HitTarget.MovePoint)}}return null}getTextInfo(){const{font:e,fontSize:t,width:i,left:n,top:r,height:o,lineSpacing:a}=(0,s.ensureNotNull)(this._data);return{font:e,fontSize:t,lineHeight:t,lineSpacing:a,textTop:r+12,textBottom:r+o-12,textLeft:n+12,textRight:n+i-12,textAlign:(0,v.isRtl)()?"right":"left"}}positionToCoordinate(e,t){const i=(0,s.ensureNotNull)(this._data),n=this.getTextInfo(),{x:r,y:o,lineNumber:a}=(0,m.getSymbolCoordinatesInfo)({symbolPosition:t,textWidth:n.textRight-n.textLeft,textByLines:i.linesIncludingHidden,lineHeight:i.fontSize,lineSpacing:i.lineSpacing,font:i.font,textAlign:n.textAlign});return{x:r+n.textLeft,y:o+n.textTop,lineNumber:a}}_drawTooltipOn(e,t){e.save();const i=(0,
s.ensureNotNull)(this._data),{point:n,textColor:r,font:o,fontSize:a,backgroundColor:l,borderColor:d,boxShadowColor:h,width:c,textWidth:u,left:_,top:p,height:g,lineSpacing:x,caretPos:f,lines:R,selectionHighlight:y}=i;e.font=o;const{horizontalPixelRatio:L,verticalPixelRatio:P}=t;if(l){e.fillStyle=l;let i=!1;h&&(e.save(),e.shadowColor=h,e.shadowBlur=4,e.shadowOffsetX=0,e.shadowOffsetY=2,i=!0),b({ctx:e,renderingInfo:t,left:_,top:p,width:c,height:g,point:n,caretPos:f,mode:1}),e.fill(),i&&e.restore()}d&&(e.lineWidth=Math.max(1,1*Math.floor(L)),e.strokeStyle=d,b({ctx:e,renderingInfo:t,left:_,top:p,width:c,height:g,point:n,caretPos:f,mode:0}),e.stroke()),e.textBaseline="middle",e.fillStyle=r,e.textAlign=(0,v.isRtl)()?"right":"left";const S=_+12+(0,T.calcTextHorizontalShift)(e,u);let M=p+12+a/2;(0,T.drawScaled)(e,L,P,(()=>{if(y){const t=this.positionToCoordinate(!1,y.start),n=this.positionToCoordinate(!1,y.end),r=this.getTextInfo();(0,m.drawSelection)(e,w.dpr1PixelRatioInfo,{lines:i.linesIncludingHidden,selectionStart:t,selectionEnd:n,left:r.textLeft,right:r.textRight,color:y.color,font:o,lineHeight:a,lineSpacing:x})}for(const t of R)e.fillText(t,S,M),M+=a+x})),e.restore()}}const S=(0,a.generateColor)((0,o.getHexColorByName)("color-black"),80);class M extends g.InplaceTextLineSourcePaneView{constructor(e,t,i,n,r){super(e,t,i,n,r),this._renderer=null,this._textWidthCache=new p.TextWidthCache,this._noteRenderer=new P((0,g.inplaceEditHandlers)(this._tryActivateEditMode.bind(this,null))),this._source.setAdditionalCursorData((()=>({color:this._source.editableTextStyle().cursorColor,...this._noteRenderer.getTextInfo()})),this._noteRenderer.positionToCoordinate.bind(this._noteRenderer,!0))}isLabelVisible(){return this.isHoveredSource()||this.isSelectedSource()}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){super._updateImpl(e),this._renderer=null;const t=this._getSource();if(!this._points||this._source.isFixed()&&void 0===t.fixedPoint())return;const i=this._source.isFixed()?[(0,s.ensureDefined)(t.fixedPoint())]:this._points;if(i.length<1)return;const n=new d.CompositeRenderer,r=this.isLabelVisible(),o=this._source.properties().childs(),p=(0,l.makeFont)(o.fontSize.value(),u.CHART_FONT_FAMILY,o.italic.value()?"italic":void 0,o.bold.value()?"bold":void 0);let g,x;o.drawBackground.value()&&(g=(0,a.generateColor)(o.backgroundColor.value(),o.backgroundTransparency.value()),x=S);const f=Math.min(236,e.mediaSize.width),v=f-24,T=i[0],{mediaSize:w}=e,R=(0,_.wordWrap)(this._textData(),p,this._textWidthCache,!1,v),m=R.filter((e=>!e.hidden)).map((e=>e.text)),y=o.fontSize.value();let b=m.length*y+24;m.length>1&&(b+=5*(m.length-1));let P=Math.round(T.x-f/2);const M=L.viewBox();let C=Math.round(T.y-M.height-b-13);const I=T.x<24||T.x+24>w.width;let A=I?null:"top";C<10?C=T.y+13:I||(A="bottom"),P<10?P=10:P+f+10>w.width&&(P=w.width-f-10),this._noteRenderer.setData({linesIncludingHidden:R,lines:m,font:p,fontSize:y,backgroundColor:g,boxShadowColor:x,borderColor:o.drawBorder.value()?o.borderColor.value():void 0,
textColor:this._textColor(),markerColor:o.markerColor.value(),point:T,tooltipVisible:r,width:f,height:b,left:P,top:C,caretPos:A,lineSpacing:5,textWidth:v,...this._inplaceTextHighlight()}),this._noteRenderer.setCursorType(this._textCursorType()),this._updateInplaceText(this._noteRenderer.getTextInfo()),n.append(this._noteRenderer),n.append(new h.SelectionRenderer({points:i,bgColors:this._lineAnchorColors(i),visible:this.areAnchorsVisible(),barSpacing:this._model.timeScale().barSpacing(),hittestResult:c.HitTarget.MovePoint})),this._renderer=n}}},31352:(e,t,i)=>{"use strict";function n(e,t,i){const n=t-i;if("percentage"===e.getLineLengthUnit()){const r=Math.max(e.getLineLength()/100*t,1),s=Math.round(t-Math.min(n,r));return{right:s,left:s-i}}const r=e.getLineLength();if(r<0){const e=Math.round(Math.min(n,-1*r));return{left:e,right:e+i}}{const e=Math.round(t-Math.min(n,r));return{right:e,left:e-i}}}i.d(t,{orderLineLocation:()=>n})},92281:(e,t,i)=>{"use strict";i.r(t),i.d(t,{OrderPaneView:()=>f});var n=i(86441),r=i(27916),s=i(95201),o=i(11542),a=i(37743),l=i(75919),d=i(56468),h=i(34585),c=i(91920),u=i(31352);const _=o.t(null,void 0,i(67710)),p=o.t(null,void 0,i(95931));class g extends l.MediaCoordinatesPaneRenderer{constructor(e){super(),this._data=null,this._cache={},this._data=null,this._adapter=e}setData(e){this._data=e}hitTest(e){if(null===this._data||0===this._data.points.length)return null;const t=this._cache;if(e.y<t.top||e.y>t.bottom)return null;if(this._adapter.getBlocked()&&e.x>=t.left&&e.x<t.right)return new d.HitTestResult(d.HitTarget.Custom,{});if(this._adapter.getEditable()&&e.x>=t.left&&e.x<t.bodyRight){const e=this._adapter.hasMoveCallback()?d.HitTarget.MovePoint:d.HitTarget.Regular;return 0===this._adapter.getTooltip().length?new d.HitTestResult(e):new d.HitTestResult(e,{tooltip:{text:this._adapter.getTooltip(),forceHideOnMove:this._adapter.hasMoveCallback(),rect:{x:t.left,y:t.top,w:t.bodyRight-t.left,h:t.bottom-t.top}}})}return this._adapter.getEditable()&&e.x>=t.bodyRight&&e.x<t.quantityRight?this._adapter.hasModifyCallback()?new d.HitTestResult(d.HitTarget.Custom,{clickHandler:this._adapter.callOnModify.bind(this._adapter),tapHandler:this._adapter.callOnModify.bind(this._adapter),tooltip:{text:this._adapter.getModifyTooltip()||(0,h.appendEllipsis)(_),rect:{x:t.bodyRight,y:t.top,w:t.quantityRight-t.bodyRight,h:t.bottom-t.top}}}):new d.HitTestResult(d.HitTarget.Regular):this._adapter.getCancellable()&&e.x>=t.quantityRight&&e.x<t.right?new d.HitTestResult(d.HitTarget.Custom,{clickHandler:this._adapter.callOnCancel.bind(this._adapter),tapHandler:this._adapter.callOnCancel.bind(this._adapter),tooltip:{text:this._adapter.getCancelTooltip()||p,rect:{x:t.quantityRight,y:t.top,w:t.right-t.quantityRight,h:t.bottom-t.top}}}):null}_drawImpl(e){if(null===this._data||!this._data.points||this._data.points.length<1)return;const t=e.context,i=e.mediaSize.width,n=this._bodyWidth(t),r=this._quantityWidth(t),s=n+r+this._cancelButtonWidth(),{left:o,right:a}=(0,
u.orderLineLocation)(this._adapter,i,s),l=Math.round(this._data.points[0].y),d=Math.round(l-(this._height()+1)/2);this._cache.bodyRight=o+n,this._cache.quantityRight=o+n+r,this._cache.top=d,this._cache.bottom=d+this._height(),this._cache.left=o,this._cache.right=a,this._drawLines(t,o,a,l,i);let h=!1;0!==n&&(this._drawBody(t,o,d),this._adapter.hasMoveCallback()&&this._drawMovePoints(t,o,d),this._drawBodyText(t,o,d),h=!0),0!==r&&(this._drawQuantity(t,o+n,d,h),this._drawQuantityText(t,o+n,d),h=!0),0!==this._cancelButtonWidth()&&this._drawCancelButton(t,o+n+r,d,h)}_height(){return Math.max(20,1+Math.max(c.fontHeight(this._adapter.getBodyFont()),c.fontHeight(this._adapter.getQuantityFont())))}_bodyWidth(e){if(0===this._adapter.getText().length)return 0;e.save(),e.font=this._adapter.getBodyFont();const t=e.measureText(this._adapter.getText()).width;return e.restore(),Math.round(20+t)}_getQuantity(){return this._adapter.getQuantity()}_quantityWidth(e){if(0===this._getQuantity().length)return 0;e.save(),e.font=this._adapter.getQuantityFont();const t=e.measureText(this._getQuantity()).width;return e.restore(),Math.round(Math.max(this._height(),10+t))}_cancelButtonWidth(){return this._adapter.isOnCancelCallbackPresent()?this._height():0}_drawLines(e,t,i,n,r){e.save(),e.strokeStyle=this._adapter.getLineColor(),(0,a.setLineStyle)(e,this._adapter.getLineStyle()),e.lineWidth=this._adapter.getLineWidth(),(0,a.drawLine)(e,i,n,r,n),this._adapter.getExtendLeft()&&(0,a.drawLine)(e,0,n,t,n),e.restore()}_drawMovePoints(e,t,i){e.save(),e.strokeStyle=this._adapter.getBodyBorderColor(),e.fillStyle=this._adapter.getBodyBorderColor();const n=t+4,r=n+2,s=Math.floor((this._height()-10)/2)+1;for(let t=0;t<s;++t){const s=i+5+2*t;(0,a.drawLine)(e,n,s,r,s)}e.restore()}_drawBody(e,t,i){e.strokeStyle=this._adapter.getBodyBorderColor(),e.fillStyle=this._adapter.getBodyBackgroundColor();const n=this._bodyWidth(e),r=this._height();e.fillRect(t+.5,i+.5,n-1,r-1),e.strokeRect(t,i,n,r)}_drawBodyText(e,t,i){e.textAlign="center",e.textBaseline="middle",e.font=this._adapter.getBodyFont(),e.fillStyle=this._adapter.getBodyTextColor();const n=t+this._bodyWidth(e)/2,r=i+this._height()/2;e.fillText(this._adapter.getText(),5+n-2,r)}_drawQuantity(e,t,i,n){e.save(),e.strokeStyle=this._adapter.getQuantityBorderColor(),e.fillStyle=this._adapter.getQuantityBackgroundColor();const r=this._quantityWidth(e),s=this._height();e.fillRect(t+.5,i+.5,r-1,s-1),n&&e.clip&&(e.beginPath(),e.rect(t+.5,i-.5,r+1,s+1),e.clip()),e.strokeRect(t,i,r,s),e.restore()}_drawQuantityText(e,t,i){e.save(),e.textAlign="center",e.textBaseline="middle",e.font=this._adapter.getQuantityFont(),e.fillStyle=this._adapter.getQuantityTextColor();const n=t+this._quantityWidth(e)/2,r=i+this._height()/2;e.fillText(this._getQuantity(),n,r),e.restore()}_drawCancelButton(e,t,i,r){e.strokeStyle=this._adapter.getCancelButtonBorderColor(),e.fillStyle=this._adapter.getCancelButtonBackgroundColor();const s=this._cancelButtonWidth(),o=this._height();e.fillRect(t+.5,i+.5,s-1,o-1),
this._adapter.getBlocked()&&(e.fillStyle="rgba(140, 140, 140, 0.75)",e.fillRect(t+.5,i+.5,s-1,o-1)),e.save(),r&&e.clip&&(e.beginPath(),e.rect(t+.5,i-.5,s+1,o+1),e.clip()),e.strokeRect(t,i,s,o),e.restore();const l=t+s,d=i+o;e.strokeStyle=this._adapter.getCancelButtonIconColor();const h=(this._cancelButtonWidth()-8)/2,c=(this._height()-8)/2;(0,a.drawPoly)(e,[new n.Point(t+h,i+c),new n.Point(l-h,d-c)],!0),(0,a.drawPoly)(e,[new n.Point(l-h,i+c),new n.Point(t+h,d-c)],!0)}}var x=i(11064);class f extends r.LineSourcePaneView{constructor(e,t){super(e,t),this._renderer=new s.CompositeRenderer,this._selectionRenderer=new x.SelectionRenderer,this._selectionData=null,this._adapter=e.adapter(),this._orderRenderer=new g(e.adapter()),this._renderer.append(this._orderRenderer),this._renderer.append(this._selectionRenderer)}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){if(super._updateImpl(e),this._selectionData=null,this.isSelectedSource()&&this._points.length>0){const t=this._points[0].y,i=e.mediaSize.width-3.5-1,r=this._adapter.hasMoveCallback()?d.HitTarget.MovePoint:d.HitTarget.Regular,s=[new n.Point(i,t)];this._selectionData={barSpacing:this._model.timeScale().barSpacing(),points:s,bgColors:this._lineAnchorColors(s),hittestResult:r,visible:!0}}this._orderRenderer.setData({points:this._points}),this._selectionRenderer.setData(this._selectionData)}}},86595:(e,t,i)=>{"use strict";i.r(t),i.d(t,{ParallelChannelPaneView:()=>u});var n=i(86441),r=i(19063),s=i(15938),o=i(72791),a=i(95201),l=i(36036),d=i(90241),h=i(27916),c=i(17330);class u extends h.LineSourcePaneView{constructor(){super(...arguments),this._channelRenderer=new d.ParallelChannelRenderer,this._labelTextRenderer=new c.TextRenderer,this._renderer=null}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){super._updateImpl(e),this._renderer=null;const t=this._source.priceScale();if(!t||t.isEmpty())return;if(0===this._source.points().length)return;if(this._points.length<=1)return;const i=this._source.properties().childs(),s=this._points[0],d=this._points[1];let h=null,c=null,u=null,_=null;if(3===this._points.length){const e=this._points[2].y-this._points[0].y;h=s.add((0,n.point)(0,e)),c=d.add((0,n.point)(0,e)),i.showMidline.value()&&(u=s.add(h).scaled(.5),_=d.add(c).scaled(.5))}const p=i.linewidth.value(),g=i.linestyle.value(),x=i.linecolor.value(),f={line1:{color:x,lineStyle:g,lineWidth:p,points:[s,d]},line2:null===h||null===c?void 0:{color:x,lineStyle:g,lineWidth:p,points:[h,c]},middleLine:null===u||null===_?void 0:{color:i.midlinecolor.value(),lineStyle:i.midlinestyle.value(),lineWidth:i.midlinewidth.value(),points:[u,_]},extendLeft:i.extendLeft.value(),extendRight:i.extendRight.value(),fillBackground:i.fillBackground.value(),backColor:(0,r.generateColor)(i.backgroundColor.value(),i.transparency.value()),hittestOnBackground:!0};this._channelRenderer.setData(f);const v=new a.CompositeRenderer;v.append(this._channelRenderer);const T=this._getLabelTextRenderer(s,d,h,c);T&&v.append(T);const w=[]
;this._points[0]&&w.push(this._points[0]),this._points[1]&&w.push(this._points[1]),h&&c&&(w.push((0,l.anchor)({...h,pointIndex:2})),w.push((0,l.anchor)({...c,pointIndex:3})),w.push((0,l.anchor)({...h.add(c).scaled(.5),pointIndex:4,square:!0,cursorType:o.PaneCursorType.VerticalResize})),w.push((0,l.anchor)({...w[0].add(w[1]).scaled(.5),pointIndex:5,square:!0,cursorType:o.PaneCursorType.VerticalResize})));const R=3===this._points.length&&!h;if(this._model.lineBeingCreated()!==this._source||R||(w.pop(),w.pop()),v.append(this.createLineAnchor({points:w},0)),this._points.length>=2){const e=this._points;this._addAlertRenderer(v,[e[0],e[1]])}this._renderer=v}_getLabelTextRenderer(e,t,i,n){const r=this._source.properties().childs();if(!r.labelVisible.value()||!r.labelText.value())return null;let o,a;const l=r.labelFontSize.value()/3;let d=0;switch(r.labelVertAlign.value()){case"bottom":!i||!n||e.y<i.y?(o=e,a=t):(o=i,a=n);break;case"top":!i||!n||e.y>i.y?(o=e,a=t):(o=i,a=n);break;case"middle":i&&n?(o=e.add(i).scaled(.5),a=t.add(n).scaled(.5)):(o=e,a=t),d=l}const h=o.x<a.x?o:a,c=h===o?a:o;let u,_;switch(r.labelHorzAlign.value()){case"left":_=h;break;case"right":_=c;break;default:_=h.add(c).scaled(.5)}switch(r.labelVertAlign.value()){case"bottom":u="bottom";break;case"top":u="top";break;case"middle":u=r.showMidline.value()?"bottom":"middle"}return this._labelTextRenderer.setData({points:[_],color:r.labelTextColor.value(),fontSize:r.labelFontSize.value(),text:r.labelText.value(),font:s.CHART_FONT_FAMILY,bold:r.labelBold.value(),italic:r.labelItalic.value(),vertAlign:u,horzAlign:r.labelHorzAlign.value(),offsetX:0,offsetY:0,boxPaddingVert:l,boxPaddingHorz:d,forceTextAlign:!0,angle:Math.atan((h.y-c.y)/(h.x-c.x))}),this._labelTextRenderer}}},99035:(e,t,i)=>{"use strict";i.r(t),i.d(t,{PathPaneView:()=>o});var n=i(74011),r=i(95201),s=i(27916);class o extends s.LineSourcePaneView{constructor(e,t){super(e,t),this._polygonRenderer=new n.PolygonRenderer,this._renderer=new r.CompositeRenderer}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){super._updateImpl(e),this._renderer.clear();const t=this._source.properties().childs(),i={points:this._points,color:t.lineColor.value(),linewidth:t.lineWidth.value(),linestyle:t.lineStyle.value(),leftend:t.leftEnd.value(),rightend:t.rightEnd.value(),filled:!1,backcolor:"",fillBackground:!1,transparency:0};this._polygonRenderer.setData(i),this._renderer.append(this._polygonRenderer),this.addAnchors(this._renderer)}}},4086:(e,t,i)=>{"use strict";i.r(t),i.d(t,{PitchfanLinePaneView:()=>d});var n=i(56468),r=i(65395),s=i(95201),o=i(49857),a=i(91046),l=i(27916);class d extends l.LineSourcePaneView{constructor(){super(...arguments),this._medianRenderer=new a.TrendLineRenderer,this._sideRenderer=new a.TrendLineRenderer,this._renderer=null,this._medianPoint=null}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){if(super._updateImpl(e),this._renderer=null,0===this._points.length)return
;if(3===this._points.length?(this._medianPoint=this._points[1].add(this._points[2]).scaled(.5),this._medianPoint.pointIndex=3):2===this._points.length?(this._medianPoint=this._points[1].clone(),this._medianPoint.pointIndex=3):(this._medianPoint=this._points[0].clone(),this._medianPoint.pointIndex=3),this._points.length<2)return;if(!this._medianPoint)return;const t=new s.CompositeRenderer,i=this._source.properties().childs(),l=i.median.childs(),d={points:[this._points[0],this._medianPoint],color:l.color.value(),linewidth:l.linewidth.value(),linestyle:l.linestyle.value(),extendleft:!1,extendright:!0,leftend:o.LineEnd.Normal,rightend:o.LineEnd.Normal};if(this._medianRenderer.setData(d),t.append(this._medianRenderer),this._points.length<3)return this.addAnchors(t),void(this._renderer=t);const h={points:[this._points[1],this._points[2]],color:l.color.value(),linewidth:l.linewidth.value(),linestyle:l.linestyle.value(),extendleft:!1,extendright:!1,leftend:o.LineEnd.Normal,rightend:o.LineEnd.Normal};this._sideRenderer.setData(h),t.append(this._sideRenderer);let c=0;const u=this._points[2].subtract(this._points[1]).scaled(.5),_=i.fillBackground.value(),p=i.transparency.value();for(let e=0;e<=8;e++){const i="level"+e,s=this._source.properties().child(i);if(s.childs().visible.value()){const i=this._medianPoint.addScaled(u,s.childs().coeff.value()),l=this._medianPoint.addScaled(u,-s.childs().coeff.value());if(_){{const e={p1:this._points[0],p2:i,p3:this._points[0],p4:this._medianPoint.addScaled(u,c),color:s.childs().color.value(),transparency:p,hittestOnBackground:!0,extendLeft:!1},n=new r.ChannelRenderer;n.setData(e),t.append(n)}{const e={p1:this._points[0],p2:l,p3:this._points[0],p4:this._medianPoint.addScaled(u,-c),color:s.childs().color.value(),transparency:p,hittestOnBackground:!0,extendLeft:!1},i=new r.ChannelRenderer;i.setData(e),t.append(i)}}c=s.childs().coeff.value();{const r={points:[this._points[0],i],color:s.childs().color.value(),linewidth:s.childs().linewidth.value(),linestyle:s.childs().linestyle.value(),extendleft:!1,extendright:!0,leftend:o.LineEnd.Normal,rightend:o.LineEnd.Normal},l=new a.TrendLineRenderer;l.setData(r),l.setHitTest(new n.HitTestResult(n.HitTarget.MovePoint,void 0,e)),t.append(l)}{const i={points:[this._points[0],l],color:s.childs().color.value(),linewidth:s.childs().linewidth.value(),linestyle:s.childs().linestyle.value(),extendleft:!1,extendright:!0,leftend:o.LineEnd.Normal,rightend:o.LineEnd.Normal},r=new a.TrendLineRenderer;r.setData(i),r.setHitTest(new n.HitTestResult(n.HitTarget.MovePoint,void 0,e)),t.append(r)}}}this.addAnchors(t),this._renderer=t}}},68470:(e,t,i)=>{"use strict";i.r(t),i.d(t,{InsidePitchforkLinePaneView:()=>_,PitchforkLinePaneView:()=>h,SchiffPitchfork2LinePaneView:()=>u,SchiffPitchforkLinePaneView:()=>c});var n=i(86441),r=i(56468),s=i(65395),o=i(95201),a=i(49857),l=i(91046),d=i(27916);class h extends d.LineSourcePaneView{constructor(){super(...arguments),this._medianRenderer=new l.TrendLineRenderer,this._sideRenderer=new l.TrendLineRenderer,this._renderer=null,
this._medianPoint=null}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){super._updateImpl(e),this._renderer=null,0!==this._points.length&&(3===this._points.length?(this._medianPoint=this._points[1].add(this._points[2]).scaled(.5),this._medianPoint.pointIndex=3):2===this._points.length?(this._medianPoint=this._points[1].clone(),this._medianPoint.pointIndex=3):(this._medianPoint=this._points[0].clone(),this._medianPoint.pointIndex=3),this._updateRenderer())}_updateRenderer(){if(this._points.length<2)return;if(!this._medianPoint)return;const e=this._source.properties(),t=e.childs().median.childs(),i=new o.CompositeRenderer,n={points:[this._points[0],this._medianPoint],color:t.color.value(),linewidth:t.linewidth.value(),linestyle:t.linestyle.value(),extendleft:e.childs().extendLines.value(),extendright:!0,leftend:a.LineEnd.Normal,rightend:a.LineEnd.Normal};if(this._medianRenderer.setData(n),i.append(this._medianRenderer),this._points.length<3)return this.addAnchors(i),void(this._renderer=i);const d={points:[this._points[1],this._points[2]],color:t.color.value(),linewidth:t.linewidth.value(),linestyle:t.linestyle.value(),extendleft:!1,extendright:!1,leftend:a.LineEnd.Normal,rightend:a.LineEnd.Normal};this._sideRenderer.setData(d),i.append(this._sideRenderer);const h=this._points[2].subtract(this._points[1]).scaled(.5),c=this._medianPoint.subtract(this._points[0]);let u=0;const _=e.childs().fillBackground.value(),p=e.childs().transparency.value();for(let t=0;t<=8;t++){const n="level"+t,o=e.childs()[n];if(o.childs().visible.value()){const n=this._medianPoint.addScaled(h,o.childs().coeff.value()),d=n.add(c),g=this._medianPoint.addScaled(h,-o.childs().coeff.value()),x=g.add(c);if(_){{const t=this._medianPoint.addScaled(h,u),r={p1:n,p2:d,p3:t,p4:t.add(c),color:o.childs().color.value(),transparency:p,hittestOnBackground:!0,extendLeft:e.childs().extendLines.value()},a=new s.ChannelRenderer;a.setData(r),i.append(a)}{const t=this._medianPoint.addScaled(h,-u),n={p1:g,p2:x,p3:t,p4:t.add(c),color:o.childs().color.value(),transparency:p,hittestOnBackground:!0,extendLeft:e.childs().extendLines.value()},r=new s.ChannelRenderer;r.setData(n),i.append(r)}}u=o.childs().coeff.value();const f={points:[n,d],color:o.childs().color.value(),linewidth:o.childs().linewidth.value(),linestyle:o.childs().linestyle.value(),extendleft:e.childs().extendLines.value(),extendright:!0,leftend:a.LineEnd.Normal,rightend:a.LineEnd.Normal},v=new l.TrendLineRenderer;v.setData(f),v.setHitTest(new r.HitTestResult(r.HitTarget.MovePoint,void 0,t)),i.append(v);const T={points:[g,x],color:o.childs().color.value(),linewidth:o.childs().linewidth.value(),linestyle:o.childs().linestyle.value(),extendleft:e.childs().extendLines.value(),extendright:!0,leftend:a.LineEnd.Normal,rightend:a.LineEnd.Normal},w=new l.TrendLineRenderer;w.setData(T),w.setHitTest(new r.HitTestResult(r.HitTarget.MovePoint,void 0,t)),i.append(w)}}this.addAnchors(i),this._renderer=i}}class c extends h{constructor(){super(...arguments),this._modifiedBase=null,
this._backSideRenderer=new l.TrendLineRenderer}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateRenderer(){if(this._points.length<2)return;this._calcMofifiedBase();const e=this._source.properties(),t=new o.CompositeRenderer,i=e.childs().median.childs();{const e={points:[this._points[0],this._points[1]],color:i.color.value(),linewidth:i.linewidth.value(),linestyle:i.linestyle.value(),extendleft:!1,extendright:!1,leftend:a.LineEnd.Normal,rightend:a.LineEnd.Normal};if(this._backSideRenderer.setData(e),t.append(this._backSideRenderer),!this._medianPoint||!this._modifiedBase)return this.addAnchors(t),void(this._renderer=t)}{const n={points:[this._modifiedBase,this._medianPoint],color:i.color.value(),linewidth:i.linewidth.value(),linestyle:i.linestyle.value(),extendleft:e.childs().extendLines.value(),extendright:!0,leftend:a.LineEnd.Normal,rightend:a.LineEnd.Normal};if(this._medianRenderer.setData(n),t.append(this._medianRenderer),this._points.length<3)return this.addAnchors(t),void(this._renderer=t)}{const e={points:[this._points[1],this._points[2]],color:i.color.value(),linewidth:i.linewidth.value(),linestyle:i.linestyle.value(),extendleft:!1,extendright:!1,leftend:a.LineEnd.Normal,rightend:a.LineEnd.Normal};this._sideRenderer.setData(e),t.append(this._sideRenderer)}{const i=this._points[2].subtract(this._points[1]).scaled(.5),n=this._medianPoint.subtract(this._modifiedBase);let o=0;const d=e.childs().fillBackground.value(),h=e.childs().transparency.value();for(let c=0;c<=8;c++){const u="level"+c,_=e.child(u);if(_.childs().visible.value()){const u=this._medianPoint.addScaled(i,_.childs().coeff.value()),p=u.add(n),g=this._medianPoint.addScaled(i,-_.childs().coeff.value()),x=g.add(n);if(d){const r=this._medianPoint.addScaled(i,o);{const i={p1:u,p2:p,p3:r,p4:r.add(n),color:_.childs().color.value(),transparency:h,hittestOnBackground:!0,extendLeft:e.childs().extendLines.value()},o=new s.ChannelRenderer;o.setData(i),t.append(o)}{const r=this._medianPoint.addScaled(i,-o),a={p1:g,p2:x,p3:r,p4:r.add(n),color:_.childs().color.value(),transparency:h,hittestOnBackground:!0,extendLeft:e.childs().extendLines.value()},l=new s.ChannelRenderer;l.setData(a),t.append(l)}}o=_.childs().coeff.value();const f={points:[u,p],color:_.childs().color.value(),linewidth:_.childs().linewidth.value(),linestyle:_.childs().linestyle.value(),extendleft:e.childs().extendLines.value(),extendright:!0,leftend:a.LineEnd.Normal,rightend:a.LineEnd.Normal},v=new l.TrendLineRenderer;v.setData(f),v.setHitTest(new r.HitTestResult(r.HitTarget.MovePoint,void 0,c)),t.append(v);const T={points:[g,x],color:_.childs().color.value(),linewidth:_.childs().linewidth.value(),linestyle:_.childs().linestyle.value(),extendleft:e.childs().extendLines.value(),extendright:!0,leftend:a.LineEnd.Normal,rightend:a.LineEnd.Normal},w=new l.TrendLineRenderer;w.setData(T),w.setHitTest(new r.HitTestResult(r.HitTarget.MovePoint,void 0,c)),t.append(w)}}}this.addAnchors(t),this._renderer=t}_calcMofifiedBase(){
this._points.length>1&&(this._modifiedBase=this._points[0].add(this._points[1]).scaled(.5))}}class u extends c{_calcMofifiedBase(){if(this._points.length>2){const e=this._points[0].x,t=.5*(this._points[0].y+this._points[1].y),i=new n.Point(e,t);this._modifiedBase=i}}}class _ extends h{constructor(){super(...arguments),this._backSideRenderer=new l.TrendLineRenderer,this._centerRenderer=new l.TrendLineRenderer,this._modifiedBase=null}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateRenderer(){if(this._points.length>1&&(this._modifiedBase=this._points[0].add(this._points[1]).scaled(.5)),this._points.length<2)return;const e=new o.CompositeRenderer;if(!this._medianPoint||!this._modifiedBase)return void this.addAnchors(e);const t=this._source.properties(),i=t.childs().median.childs();if(3===this._points.length){const t={points:[this._modifiedBase,this._points[2]],color:i.color.value(),linewidth:i.linewidth.value(),linestyle:i.linestyle.value(),extendleft:!1,extendright:!1,leftend:a.LineEnd.Normal,rightend:a.LineEnd.Normal};this._medianRenderer.setData(t),e.append(this._medianRenderer)}{const t={points:[this._points[0],this._points[1]],color:i.color.value(),linewidth:i.linewidth.value(),linestyle:i.linestyle.value(),extendleft:!1,extendright:!1,leftend:a.LineEnd.Normal,rightend:a.LineEnd.Normal};if(this._backSideRenderer.setData(t),e.append(this._backSideRenderer),this._points.length<3)return this.addAnchors(e),void(this._renderer=e)}{const t={points:[this._points[1],this._points[2]],color:i.color.value(),linewidth:i.linewidth.value(),linestyle:i.linestyle.value(),extendleft:!1,extendright:!1,leftend:a.LineEnd.Normal,rightend:a.LineEnd.Normal};this._sideRenderer.setData(t),e.append(this._sideRenderer)}{const n=this._points[2].subtract(this._points[1]).scaled(.5),o=this._points[2].subtract(this._modifiedBase);let d=0;const h=t.childs().fillBackground.value(),c=t.childs().transparency.value(),u={points:[this._medianPoint,this._medianPoint.add(o)],color:i.color.value(),linewidth:i.linewidth.value(),linestyle:i.linestyle.value(),extendleft:t.childs().extendLines.value(),extendright:!0,leftend:a.LineEnd.Normal,rightend:a.LineEnd.Normal};this._centerRenderer.setData(u),e.append(this._centerRenderer);for(let i=0;i<=8;i++){const u="level"+i,_=t.child(u).childs();if(_.visible.value()){const u=this._medianPoint.addScaled(n,_.coeff.value()),p=u.add(o),g=this._medianPoint.addScaled(n,-_.coeff.value()),x=g.add(o);if(h){{const i=this._medianPoint.addScaled(n,d),r={p1:u,p2:p,p3:i,p4:i.add(o),color:_.color.value(),transparency:c,hittestOnBackground:!0,extendLeft:t.childs().extendLines.value()},a=new s.ChannelRenderer;a.setData(r),e.append(a)}{const i=this._medianPoint.addScaled(n,-d),r={p1:g,p2:x,p3:i,p4:i.add(o),color:_.color.value(),transparency:c,hittestOnBackground:!0,extendLeft:t.childs().extendLines.value()},a=new s.ChannelRenderer;a.setData(r),e.append(a)}}d=_.coeff.value();const f={points:[u,p],color:_.color.value(),linewidth:_.linewidth.value(),linestyle:_.linestyle.value(),
extendleft:t.childs().extendLines.value(),extendright:!0,leftend:a.LineEnd.Normal,rightend:a.LineEnd.Normal},v=new l.TrendLineRenderer;v.setData(f),v.setHitTest(new r.HitTestResult(r.HitTarget.MovePoint,void 0,i)),e.append(v);const T={points:[g,x],color:_.color.value(),linewidth:_.linewidth.value(),linestyle:_.linestyle.value(),extendleft:t.childs().extendLines.value(),extendright:!0,leftend:a.LineEnd.Normal,rightend:a.LineEnd.Normal},w=new l.TrendLineRenderer;w.setData(T),w.setHitTest(new r.HitTestResult(r.HitTarget.MovePoint,void 0,i)),e.append(w)}}}this.addAnchors(e),this._renderer=e}}},65765:(e,t,i)=>{"use strict";i.r(t),i.d(t,{PolylinePaneView:()=>o});var n=i(74011),r=i(95201),s=i(27916);class o extends s.LineSourcePaneView{constructor(e,t){super(e,t),this._polygonRenderer=new n.PolygonRenderer,this._renderer=new r.CompositeRenderer}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){super._updateImpl(e),this._renderer.clear();const t=this._source.properties().childs(),i={points:this._points,color:t.linecolor.value(),linewidth:t.linewidth.value(),linestyle:t.linestyle.value(),filled:t.filled.value(),backcolor:t.backgroundColor.value(),fillBackground:t.fillBackground.value(),transparency:t.transparency.value()};this._polygonRenderer.setData(i),this._renderer.append(this._polygonRenderer),this.addAnchors(this._renderer)}}},38420:(e,t,i)=>{"use strict";var n=i(27916).LineSourcePaneView,r=i(91920),s=i(56468).HitTestResult,o=i(56468).HitTarget,a=i(63273),l=i(34585).appendEllipsis,d=i(75919).MediaCoordinatesPaneRenderer,h=i(31352).orderLineLocation;const{setLineStyle:c,drawLine:u,drawPoly:_}=i(37743),{LINESTYLE_SOLID:p}=i(51056);class g extends d{constructor(e,t){super(),this._data=null,this._cache=e,this._adapter=t}setData(e){this._data=e}_height(){return Math.max(20,1+Math.max(r.fontHeight(this._adapter.getBodyFont()),r.fontHeight(this._adapter.getQuantityFont())))}_bodyWidth(e){if(0===this._adapter.getText().length)return 0;e.save(),e.font=this._adapter.getBodyFont();var t=e.measureText(this._adapter.getText()).width;return e.restore(),Math.round(10+t)}_getQuantity(){return this._adapter.getQuantity()}_quantityWidth(e){if(0===this._getQuantity().length)return 0;e.save(),e.font=this._adapter.getQuantityFont();var t=e.measureText(this._getQuantity()).width;return e.restore(),Math.round(Math.max(this._height(),10+t))}_reverseButtonWidth(){return this._adapter.isOnReverseCallbackPresent()?this._height():0}_closeButtonWidth(){return this._adapter.isOnCloseCallbackPresent()?this._height():0}_drawLines(e,t,i,n,r){e.save(),e.strokeStyle=this._adapter.getLineColor(),c(e,this._adapter.getLineStyle()),e.lineWidth=this._adapter.getLineWidth(),u(e,i,n,r,n),this._adapter.getExtendLeft()&&u(e,0,n,t,n),e.restore()}_drawBody(e,t,i){e.strokeStyle=this._adapter.getBodyBorderColor(),e.fillStyle=this._adapter.getBodyBackgroundColor();var n=this._bodyWidth(e),r=this._height();e.fillRect(t+.5,i+.5,n-1,r-1),e.strokeRect(t,i,n,r)}_drawBodyText(e,t,i){e.save(),e.textAlign="center",e.textBaseline="middle",
e.font=this._adapter.getBodyFont(),e.fillStyle=this._adapter.getBodyTextColor();var n=t+this._bodyWidth(e)/2,r=i+this._height()/2;e.fillText(this._adapter.getText(),n,r),e.restore()}_drawQuantity(e,t,i){e.strokeStyle=this._adapter.getQuantityBorderColor(),e.fillStyle=this._adapter.getQuantityBackgroundColor();var n=this._quantityWidth(e),r=this._height();e.fillRect(t+.5,i+.5,n-1,r-1),e.strokeRect(t,i,n,r)}_drawQuantityText(e,t,i){e.save(),e.textAlign="center",e.textBaseline="middle",e.font=this._adapter.getQuantityFont(),e.fillStyle=this._adapter.getQuantityTextColor();var n=t+this._quantityWidth(e)/2,r=i+this._height()/2;e.fillText(a.startWithLTR(this._getQuantity()+""),n,r),e.restore()}_drawReverseButton(e,t,i){e.save(),e.strokeStyle=this._adapter.getReverseButtonBorderColor(),e.fillStyle=this._adapter.getReverseButtonBackgroundColor();var n=this._reverseButtonWidth(),r=this._height();e.fillRect(t+.5,i+.5,n-1,r-1),e.strokeRect(t,i,n,r),e.strokeStyle=this._adapter.getReverseButtonIconColor();var s=function(e,t){c(e,p),u(e,0,0,0,t),u(e,-1,1,1,1),u(e,-2,2,2,2)},o=t+Math.round((this._reverseButtonWidth()-6)/2),a=i+5;e.save(),e.translate(o,a),s(e,10),e.translate(6,10),e.rotate(Math.PI),s(e,10),e.restore(),this._adapter._blocked&&(e.fillStyle="rgba(140, 140, 140, 0.75)",e.fillRect(t+.5,i+.5,n-1,r-1)),e.restore()}_drawCloseButton(e,t,i){e.save(),e.strokeStyle=this._adapter.getCloseButtonBorderColor(),e.fillStyle=this._adapter.getCloseButtonBackgroundColor();var n=this._closeButtonWidth(),r=this._height();e.fillRect(t+.5,i+.5,n-1,r-1),e.strokeRect(t,i,n,r);var s=t+n,o=i+r;e.strokeStyle=this._adapter.getCloseButtonIconColor();var a=(this._closeButtonWidth()-8)/2,l=(this._height()-8)/2;_(e,[{x:t+a,y:i+l},{x:s-a,y:o-l}],!0),_(e,[{x:s-a,y:i+l},{x:t+a,y:o-l}],!0),this._adapter._blocked&&(e.fillStyle="rgba(140, 140, 140, 0.75)",e.fillRect(t+.5,i+.5,n-1,r-1)),e.restore()}_drawImpl(e){if(null===this._data||!this._data.points||this._data.points.length<1)return;var t=e.context,i=this._data.width,n=this._bodyWidth(t),r=this._quantityWidth(t),s=this._reverseButtonWidth(t),o=n+r+s+this._closeButtonWidth();const{left:a,right:l}=h(this._adapter,i,o);var d=Math.round(this._data.points[0].y),c=Math.round(d-(this._height()+1)/2);this._cache.bodyRight=a+n,this._cache.quantityRight=this._cache.bodyRight+r,this._cache.reverseButtonRight=this._cache.quantityRight+s,this._cache.top=c,this._cache.bottom=c+this._height(),this._cache.left=a,this._cache.right=l,this._drawLines(t,a,l,d,i),0!==n&&(this._drawBody(t,a,c),this._drawBodyText(t,a,c)),0!==r&&(this._drawQuantity(t,this._cache.bodyRight,c),this._drawQuantityText(t,this._cache.bodyRight,c)),0!==s&&this._drawReverseButton(t,this._cache.quantityRight,c),0!==this._closeButtonWidth()&&this._drawCloseButton(t,this._cache.reverseButtonRight,c)}hitTest(e){
return null===this._data||0===this._data.points.length||e.y<this._cache.top||e.y>this._cache.bottom||e.x<this._cache.left||this._cache.right<e.x?null:this._adapter._blocked?new s(o.Custom,{}):e.x>=this._cache.bodyRight&&e.x<this._cache.quantityRight&&this._adapter._onModifyCallback?new s(o.Custom,{clickHandler:this._adapter.callOnModify.bind(this._adapter),tapHandler:this._adapter.callOnModify.bind(this._adapter),tooltip:{text:this._adapter.getProtectTooltip()||l(i.i18next(null,void 0,i(24927))),rect:{x:this._cache.bodyRight,y:this._cache.top,w:this._cache.quantityRight-this._cache.bodyRight,h:this._cache.bottom-this._cache.top}}}):e.x>=this._cache.quantityRight&&e.x<this._cache.reverseButtonRight?new s(o.Custom,{clickHandler:this._adapter.callOnReverse.bind(this._adapter),tapHandler:this._adapter.callOnReverse.bind(this._adapter),tooltip:{text:this._adapter.getReverseTooltip()||i.i18next(null,void 0,i(98818)),rect:{x:this._cache.quantityRight,y:this._cache.top,w:this._cache.reverseButtonRight-this._cache.quantityRight,h:this._cache.bottom-this._cache.top}}}):e.x>=this._cache.reverseButtonRight&&e.x<this._cache.right?new s(o.Custom,{clickHandler:this._adapter.callOnClose.bind(this._adapter),tapHandler:this._adapter.callOnClose.bind(this._adapter),tooltip:{text:this._adapter.getCloseTooltip()||i.i18next(null,void 0,i(59783)),rect:{x:this._cache.reverseButtonRight,y:this._cache.top,w:this._cache.right-this._cache.reverseButtonRight,h:this._cache.bottom-this._cache.top}}}):new s(o.Custom,{clickHandler:function(){},tapHandler:function(){},tooltip:{text:this._adapter.getTooltip(),rect:{x:this._cache.left,y:this._cache.top,w:this._cache.bodyRight-this._cache.left,h:this._cache.bottom-this._cache.top}}})}}t.PositionPaneView=class extends n{constructor(e,t){super(e,t),this._rendererCache={},this._renderer=new g(this._rendererCache,e._adapter)}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer.setData({points:this._points,width:this._model.timeScale().width()}),this._renderer}}},75872:(e,t,i)=>{"use strict";i.r(t),i.d(t,{PredictionPaneView:()=>k});var n,r=i(27147),s=i(50151),o=i(11542),a=i(19938),l=i(19063),d=i(63273),h=i(82826),c=i(43290),u=i(15344),_=i(92953),p=i(85049),g=i(95201),x=i(27916),f=i(86441),v=i(9859),T=i(7114),w=i(15938),R=i(68979),m=i(56468),y=i(37743),b=i(75919),L=i(2844),P=i(27442);!function(e){e[e.TargetFontSize1=14]="TargetFontSize1",e[e.TargetFontSize2=11]="TargetFontSize2",e[e.SourceFontSize1=12]="SourceFontSize1",e[e.SourceFontSize2=10]="SourceFontSize2",e[e.ArrowOffset=6]="ArrowOffset",e[e.ArrowHeight=5]="ArrowHeight",e[e.ArrowWidth=5]="ArrowWidth",e[e.Radius=3]="Radius",e[e.LabelsLeftOffset=4]="LabelsLeftOffset",e[e.LabelsTopOffset=3]="LabelsTopOffset",e[e.LabelsBoxLineWidth=2]="LabelsBoxLineWidth",e[e.LabelsBoxWidthDelta=15]="LabelsBoxWidthDelta",e[e.LabelsMinLeftOffset=20]="LabelsMinLeftOffset",e[e.LabelsCircleRadius=3]="LabelsCircleRadius",e[e.StartLabelTopOffset=2]="StartLabelTopOffset"}(n||(n={}));const S=(0,R.makeFont)(14,w.CHART_FONT_FAMILY,"normal"),M=(0,
R.makeFont)(14,w.CHART_FONT_FAMILY,"bold"),C=(0,R.makeFont)(11,w.CHART_FONT_FAMILY,"normal"),I=(0,R.makeFont)(12,w.CHART_FONT_FAMILY,"normal"),A=(0,R.makeFont)(10,w.CHART_FONT_FAMILY,"normal");class D extends b.MediaCoordinatesPaneRenderer{constructor(){super(...arguments),this._data=null,this._sourceWidth=void 0,this._sourceHeight=void 0,this._sourceRectLeftOffset=void 0,this._targetWidth=void 0,this._targetHeight=void 0,this._targetRectLeftOffset=void 0,this._target1TextWidthCache=new L.TextWidthCache,this._target1BoldTextWidthCache=new L.TextWidthCache,this._target2TextWidthCache=new L.TextWidthCache,this._source1TextWidthCache=new L.TextWidthCache,this._source2TextWidthCache=new L.TextWidthCache}setData(e){this._data=e}hitTest(e){if(null===this._data||this._data.points.length<2)return null;const t=this._data.points[0],i=this._data.points[1].subtract(t),n=e.subtract(t),r=Math.abs(i.x),s=Math.abs(i.y),o=(0,v.sign)(i.y)*(s-s*Math.sqrt(1-n.x*n.x/(r*r)));if(Math.abs(o-n.y)<3)return new m.HitTestResult(m.HitTarget.MovePoint);const a=this._targetLabelHitTest(e);return a||this._sourceLabelHitTest(e)}_drawImpl(e){if(null===this._data||this._data.points.length<2)return;const t=e.context;t.lineCap="butt",t.strokeStyle=this._data.color,t.lineWidth=this._data.linewidth;const i=this._data.points[0],n=this._data.points[1],r=n.subtract(i),s=Math.abs(r.x),o=Math.abs(r.y);let a,l,d;r.y<0?(a=Math.PI/2,l=r.x>0?0:Math.PI,d=1):(a=-Math.PI/2,l=r.x>0?0:-Math.PI,d=-1),t.beginPath(),t.ellipse(i.x,n.y,s,o,0,a,l,a>l),t.stroke(),this._drawTargetLabel(t,e.mediaSize),this._drawStartLabel(t,e.mediaSize);const h=Math.max(8,4*this._data.linewidth);let c;if(Math.abs(r.x)<1||Math.abs(r.y)<1)c=Math.atan(r.x/r.y);else{let e=0,t=Math.PI/2,i=(e+t)/2,n=0,a=0;if(r.length()>h){let r=0;for(;10!==r;){n=s*Math.sin(i),a=o*(1-Math.cos(i));const l=Math.sqrt((n-s)*(n-s)+(a-o)*(a-o));if(Math.abs(l-h)<1)break;l>h?e=i:t=i,i=(e+t)/2,r++}}c=Math.atan((s-n)/(o-a)),r.x*r.y<0&&(c=-c)}t.fillStyle=this._data.color,t.save(),t.beginPath(),t.translate(n.x,n.y),t.rotate(-c),t.moveTo(0,0),t.lineTo(-h/2,d*h),t.lineTo(h/2,d*h),t.lineTo(0,0),t.restore(),t.fill()}_drawBalloon(e,t,i,n,r,s=20){e.beginPath();if(r===P.Direction.Down){const r=new f.Point(t.x-s,t.y-6-5-n);return e.moveTo(r.x+3,r.y),e.lineTo(r.x+i-3,r.y),e.arcTo(r.x+i,r.y,r.x+i,r.y+3,3),e.lineTo(r.x+i,r.y+n-3),e.arcTo(r.x+i,r.y+n,r.x+i-3,r.y+n,3),e.lineTo(r.x+s+5,r.y+n),e.lineTo(r.x+s,r.y+n+5),e.lineTo(r.x+s-5,r.y+n),e.lineTo(r.x+3,r.y+n),e.arcTo(r.x,r.y+n,r.x,r.y+n-3,3),e.lineTo(r.x,r.y+3),e.arcTo(r.x,r.y,r.x+3,r.y,3),r}{const r=new f.Point(t.x-s,t.y+6+5+n);return e.moveTo(r.x+3,r.y),e.lineTo(r.x+i-3,r.y),e.arcTo(r.x+i,r.y,r.x+i,r.y-3,3),e.lineTo(r.x+i,r.y-n+3),e.arcTo(r.x+i,r.y-n,r.x+i-3,r.y-n,3),e.lineTo(r.x+s+5,r.y-n),e.lineTo(r.x+s,r.y-n-5),e.lineTo(r.x+s-5,r.y-n),e.lineTo(r.x+3,r.y-n),e.arcTo(r.x,r.y-n,r.x,r.y-n+3,3),e.lineTo(r.x,r.y-3),e.arcTo(r.x,r.y,r.x+3,r.y,3),new f.Point(r.x,r.y-n)}}_drawTargetLabel(e,t){if(null===this._data)return;e.save()
;const n=this._data.targetLine1,r=this._data.targetLine2,s=this._data.targetLine3,a=this._data.targetLine4;e.font=S;const l=this._target1TextWidthCache.measureText(e,n),h=this._target1TextWidthCache.measureText(e,r),c=this._target1TextWidthCache.measureText(e," ");e.font=C;const u=this._target2TextWidthCache.measureText(e,s),_=this._target2TextWidthCache.measureText(e,a),p=this._target2TextWidthCache.measureText(e," "),g=this._data.clockWhite&&this._data.clockWhite.width||0;this._targetWidth=Math.max(l+h+c,u+_+g+2*p)+8+4,this._targetHeight=38;const x=this._data.points[1],f=x.x+this._targetWidth-t.width+5;this._targetRectLeftOffset=Math.max(20,Math.min(this._targetWidth-15,f));const v=this._data.direction===P.Direction.Up?P.Direction.Down:P.Direction.Up,w=this._drawBalloon(e,x,this._targetWidth,this._targetHeight,v,this._targetRectLeftOffset);e.fillStyle=this._data.targetBackColor,e.fill(),e.lineWidth=2,e.strokeStyle=this._data.targetStrokeColor,e.stroke(),e.beginPath(),e.arc(x.x,x.y,3,0,2*Math.PI,!1),e.fillStyle=this._data.centersColor,e.fill(),e.textBaseline="top",e.fillStyle=this._data.targetTextColor;const R=2+w.x+4,m=2+w.y+3,b=this._targetWidth-8-4;e.font=S,e.textAlign=(0,d.isRtl)()?"right":"left";const L=(0,T.calcTextHorizontalShift)(e,b-h-c);e.fillText(n,R+L,m);const I=(0,T.calcTextHorizontalShift)(e,b-l);e.fillText(r,R+l+c+I,m),e.font=C;const A=m+14+3,D=(0,T.calcTextHorizontalShift)(e,b-_-g-p);e.fillText(s,R+D,A);const k=(0,T.calcTextHorizontalShift)(e,b-u-p-g-_);this._data.clockWhite&&e.drawImage(this._data.clockWhite,R+u+p+k,A+1);const N=(0,T.calcTextHorizontalShift)(e,b-u-g);if(e.fillText(a,R+u+g+2*p+N,A),!this._data.status)return void e.restore();let B,H,z,E;if(e.font=M,this._data.status===P.AlertStatus.Success)B=o.t(null,void 0,i(26409)),H=this._data.successBackground,z=this._data.successTextColor,E=this._data.successIcon;else B=o.t(null,void 0,i(47545)),H=this._data.failureBackground,z=this._data.failureTextColor,E=this._data.failureIcon;const W=18,O=this._target1BoldTextWidthCache.measureText(e,B),V=Math.round((this._targetWidth-O)/2),F=(0,T.calcTextHorizontalShift)(e,O);e.fillStyle=H,this._data.direction===P.Direction.Up?((0,y.drawRoundRect)(e,w.x-1,w.y-W-2,this._targetWidth+2,W,5),e.fill(),e.fillStyle=z,e.fillText(B,w.x+V+F,w.y-W+1),E&&e.drawImage(E,w.x+V-E.width-4,w.y-W-2+Math.abs(W-E.height)/2)):((0,y.drawRoundRect)(e,w.x-1,w.y+this._targetHeight+2,this._targetWidth+2,W,5),e.fill(),e.fillStyle=z,e.fillText(B,w.x+V+F,w.y+this._targetHeight+5),E&&e.drawImage(E,w.x+V-E.width-4,w.y+this._targetHeight+10-Math.abs(W-E.height)/2)),e.restore()}_drawStartLabel(e,t){if(null===this._data)return;e.save();e.font=I;const i=this._source1TextWidthCache.measureText(e,this._data.sourceLine1);e.font=A;const n=this._source2TextWidthCache.measureText(e,this._data.sourceLine2);this._sourceWidth=Math.max(i,n)+6+4,this._sourceHeight=32;const r=this._data.points[0],s=r.x+this._sourceWidth-t.width+5;this._sourceRectLeftOffset=Math.max(20,Math.min(this._sourceWidth-15,s))
;const o=this._drawBalloon(e,r,this._sourceWidth,this._sourceHeight,this._data.direction,this._sourceRectLeftOffset);e.fillStyle=this._data.sourceBackColor,e.fill(),e.lineWidth=2,e.strokeStyle=this._data.sourceStrokeColor,e.stroke(),e.textAlign=(0,d.isRtl)()?"right":"left",e.textBaseline="top",e.fillStyle=this._data.sourceTextColor;const a=(0,T.calcTextHorizontalShift)(e,this._sourceWidth-6-4),l=2+o.x+3+a,h=2+o.y+2;e.font=I,e.fillText(this._data.sourceLine1,l,h),e.font=A,e.fillText(this._data.sourceLine2,l,h+12+2),e.beginPath(),e.arc(r.x,r.y,3,0,2*Math.PI,!1),e.fillStyle=this._data.centersColor,e.fill(),e.restore()}_targetLabelHitTest(e){if(null===this._data||void 0===this._targetWidth||void 0===this._targetHeight||void 0===this._targetRectLeftOffset)return null;let t=this._targetHeight+5;this._data.status&&(t+=24);const i=this._data.direction===P.Direction.Up?-1:1,n=this._data.points[1],r=n.x-this._targetRectLeftOffset,s=n.y+3*i,o=n.y+i*(t+3),a=Math.min(s,o),l=Math.max(s,o);return e.x>=r&&e.x<=r+this._targetWidth&&e.y>=a&&e.y<=l?new m.HitTestResult(m.HitTarget.MovePoint):null}_sourceLabelHitTest(e){if(null===this._data||void 0===this._sourceHeight||void 0===this._sourceWidth||void 0===this._sourceRectLeftOffset)return null;const t=this._data.direction===P.Direction.Up?1:-1,i=this._data.points[0],n=i.x-this._sourceRectLeftOffset,r=i.y+3*t,s=i.y+(3+this._sourceHeight+5)*t,o=Math.min(r,s),a=Math.max(r,s);return e.x>=n&&e.x<=n+this._sourceWidth&&e.y>=o&&e.y<=a?new m.HitTestResult(m.HitTarget.MovePoint):null}}class k extends x.LineSourcePaneView{constructor(e,t){super(e,t),this._clockWhite=null,this._successIcon=null,this._failureIcon=null,this._pendingIcons=3,this._predictionRenderer=new D,this._renderer=new g.CompositeRenderer;const n=()=>{this._pendingIcons-=1,0===this._pendingIcons&&this._source.model().updateSource(this._source)};(0,a.getImage)("prediction-clock-white",i(99620)).then((e=>{this._clockWhite=e,n()})),(0,a.getImage)("prediction-success-white",i(14012)).then((e=>{this._successIcon=e,n()})),(0,a.getImage)("prediction-failure-white",i(88249)).then((e=>{this._failureIcon=e,n()}))}iconsReady(){return 0===this._pendingIcons}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){super._updateImpl(e),this._renderer.clear();const t=this._source.points();if(t.length<2)return;if(!this._source.priceScale())return;const n=(0,s.ensureNotNull)(this._source.ownerSource()).formatter(),a=t[1],g=t[0],x=(0,d.forceLTRStr)(n.format(a.price)),f=a.price-g.price,v=f/Math.abs(g.price)*100,T=(0,d.forceLTRStr)(`${n.format(f)} (${(0,c.getPercentageFormatter)().format(v)})`),w=this._model.timeScale();let R=w.indexToUserTime(g.index),m=w.indexToUserTime(a.index);g.time&&a.time&&(R=(0,r.default)(g.time)?new Date(Date.parse(g.time)):new Date(1e3*g.time),m=(0,r.default)(a.time)?new Date(Date.parse(a.time)):new Date(1e3*a.time));const y=this._model.mainSeries().isDWM(),b=p.Interval.parse(this._model.mainSeries().interval()),L=b.isSeconds()||b.isTicks();let P="",S="";if(m&&R){
const e=y?"":`  ${new u.TimeFormatter(L?u.hourMinuteSecondFormat:u.hourMinuteFormat).format(m)}`;S=`${(new h.DateFormatter).format(m)}${e}`;const t=(m.valueOf()-R.valueOf())/1e3;P=`${o.t(null,{context:"dates"},i(71682))} ${(0,d.startWithLTR)((new _.TimeSpanFormatter).format(t))}`}const M=n.format(g.price);let C="";const I=w.indexToUserTime(g.index);if(I){const e=y?"":` ${new u.TimeFormatter(L?u.hourMinuteSecondFormat:u.hourMinuteFormat).format(I)}`;C=`${(new h.DateFormatter).format(I)}${e}`}const A=this._model.lineBeingCreated()!==this._source&&this._model.lineBeingEdited()!==this._source&&!this._model.sourcesBeingMoved().includes(this._source),D=this._source.properties().childs(),k=D.transparency.value(),N={points:this._points,color:D.linecolor.value(),linewidth:D.linewidth.value(),targetLine1:T,targetLine2:P,targetLine3:x,targetLine4:S,status:D.status.value(),targetBackColor:(0,l.generateColor)(D.targetBackColor.value(),k),targetStrokeColor:(0,l.generateColor)(D.targetStrokeColor.value(),k),targetTextColor:D.targetTextColor.value(),sourceBackColor:(0,l.generateColor)(D.sourceBackColor.value(),k),sourceStrokeColor:(0,l.generateColor)(D.sourceStrokeColor.value(),k),sourceTextColor:D.sourceTextColor.value(),successBackground:(0,l.generateColor)(D.successBackground.value(),k),successTextColor:D.successTextColor.value(),failureBackground:(0,l.generateColor)(D.failureBackground.value(),k),failureTextColor:D.failureTextColor.value(),intermediateBackColor:D.intermediateBackColor.value(),intermediateTextColor:D.intermediateTextColor.value(),sourceLine1:M,sourceLine2:C,direction:this._source.direction(),clockWhite:this._clockWhite,successIcon:this._successIcon,failureIcon:this._failureIcon,finished:A,centersColor:this._model.backgroundCounterColor().value()};this._predictionRenderer.setData(N),this._renderer.append(this._predictionRenderer),this.addAnchors(this._renderer)}}},97105:(e,t,i)=>{"use strict";var n=i(86441),r=n.Point,s=n.box,o=i(34026).pointInBox,a=i(27916).LineSourcePaneView,l=i(11064).SelectionRenderer,d=i(56468).HitTestResult,h=i(56468).HitTarget,c=i(95201).CompositeRenderer,u=i(19063),_=i(7114).calcTextHorizontalShift,p=i(63273).isRtl,g=i(75919).MediaCoordinatesPaneRenderer,x=i(15938);class f extends g{constructor(e,t){super(),this._data=null,this._measureCache=e,this._chartModel=t,this._points=null}setData(e){this._data=e,this._points=e.points}_drawImpl(e){if(null!==this._data&&null!==this._points&&0!==this._points.length){var t=e.context;t.font=[this._data.fontWeight,this._data.fontSize+"px",this._data.fontFamily].join(" ");var i=t.measureText(this._data.label);i.height=this._data.fontSize;var n=10,r=5,s=i.width+2*n,o=i.height+2*r,a=this._points[0].x- -9,l=this._points[0].y-(o+15);t.textAlign=p()?"right":"left";var d=_(t,i.width);this._measureCache&&Object.assign(this._measureCache,{innerWidth:s,innerHeight:o,tailLeft:-9,tailHeight:15}),t.translate(.5+a,.5+l),t.beginPath(),t.moveTo(12,o),t.lineTo(-9,o+15),t.lineTo(-10,o+15-1),t.lineTo(5,o),t.lineTo(3,o),t.arcTo(0,o,0,0,3),t.lineTo(0,3),t.arcTo(0,0,s,0,3),
t.lineTo(s-3,0),t.arcTo(s,0,s,o,3),t.lineTo(s,o-3),t.arcTo(s,o,0,o,3),t.lineTo(12,o),t.fillStyle=u.generateColor(this._data.backgroundColor,this._data.transparency),t.fill(),t.strokeStyle=this._data.borderColor,t.lineWidth=2,t.stroke(),t.closePath(),t.textBaseline="alphabetic",t.fillStyle=this._data.color,t.fillText(this._data.label,n+d,o/2+Math.floor(.35*this._data.fontSize)),t.translate(-.5,-.5),t.beginPath(),t.arc(-9,o+15,2.5,0,2*Math.PI,!1),t.fillStyle=u.generateColor(this._data.borderColor,this._data.transparency),t.fill(),t.strokeStyle=this._chartModel.backgroundColor().value(),t.lineWidth=1,t.stroke(),t.closePath()}}hitTest(e){if(null===this._data||null===this._points||0===this._points.length)return null;var t=this._points[0].x-this._measureCache.tailLeft,i=this._points[0].y-(this._measureCache.innerHeight+this._measureCache.tailHeight),n=s(new r(t,i),new r(t+this._measureCache.innerWidth,i+this._measureCache.innerHeight));return o(e,n)?new d(h.MovePoint):null}}t.PriceLabelPaneView=class extends a{constructor(e,t,i){super(e,t),this._rendererCache={},this._priceLabelRenderer=new f(this._rendererCache,t),this._renderer=null}_updateImpl(e){if(super._updateImpl(e),this._renderer=null,this._source.points().length>0){var t=this._source.points()[0].price,i=this._source.priceScale();if(!i||i.isEmpty())return;var n=this._source.ownerSource().firstValue();this._priceLabel=i.formatPrice(t,n)}var r={};if(r.points=this._points,r.borderColor=this._source.properties().borderColor.value(),r.backgroundColor=this._source.properties().backgroundColor.value(),r.color=this._source.properties().color.value(),r.fontWeight=this._source.properties().fontWeight.value(),r.fontSize=this._source.properties().fontsize.value(),r.fontFamily=x.CHART_FONT_FAMILY,r.transparency=this._source.properties().transparency.value(),r.label=this._priceLabel,this._priceLabelRenderer.setData(r),1===r.points.length){var s=new c;return s.append(this._priceLabelRenderer),s.append(new l({points:r.points,bgColors:this._lineAnchorColors(r.points),visible:this.areAnchorsVisible()})),void(this._renderer=s)}this._renderer=this._priceLabelRenderer}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}}},1174:(e,t,i)=>{"use strict";i.r(t),i.d(t,{PriceNotePaneView:()=>R});var n,r,s=i(50151),o=i(86441),a=i(19625),l=i(95201),d=i(17330),h=i(27916),c=i(4652),u=i(51056),_=i(7114),p=i(15938),g=i(69186),x=i(56468),f=i(37743),v=i(61993);function T(e){let t,i;return e>=-135&&e<=-45?(t="center",i="bottom"):e>-45&&e<45?(t="left",i="middle"):e>=45&&e<=135?(t="center",i="top"):(t="right",i="middle"),{horzAlign:t,vertAlign:i}}!function(e){e[e.Label=1]="Label"}(n||(n={})),function(e){e[e.Tolerance=3]="Tolerance",e[e.TouchTolerance=20]="TouchTolerance",e[e.LineWidth=1]="LineWidth",e[e.CircleRadius=2]="CircleRadius",e[e.CircleStrokeWidth=1]="CircleStrokeWidth",e[e.BackgroundRoundRect=4]="BackgroundRoundRect",e[e.PriceLabelFontSize=12]="PriceLabelFontSize",e[e.LabelVertPadding=6]="LabelVertPadding",e[e.LabelHorzPadding=8]="LabelHorzPadding"}(r||(r={}));class w{
constructor(){this._data=null,this._priceLabelRenderer=new d.TextRenderer(void 0,new x.HitTestResult(x.HitTarget.MovePoint,{areaName:x.AreaName.Style,activeItem:1})),this._hittest=new x.HitTestResult(x.HitTarget.MovePoint,{areaName:x.AreaName.Style})}setData(e){this._data=e;const t=e.points[0],i=e.points[1],n=Math.round(180*Math.atan2(i.y-t.y,i.x-t.x)/Math.PI);this._priceLabelRenderer.setData({...T(n),points:[i],text:e.text,color:e.textColor,font:p.CHART_FONT_FAMILY,fontSize:e.fontSize,bold:e.bold,italic:e.italic,offsetX:0,offsetY:0,borderColor:e.borderColor,borderWidth:1,backgroundColor:e.backgroundColor,backgroundRoundRect:4,boxPaddingVert:6,boxPaddingHorz:8})}setHitTest(e){this._hittest=e}draw(e,t){const i=this._data;if(null===i||i.points.length<2)return;e.save();const{horizontalPixelRatio:n,verticalPixelRatio:r}=t,s=Math.round(i.points[0].x*n),o=Math.round(i.points[0].y*r),a=Math.round(i.points[1].x*n),l=Math.round(i.points[1].y*r);e.lineCap="round",(0,f.setLineStyle)(e,u.LINESTYLE_SOLID),e.strokeStyle=i.lineColor,e.fillStyle=i.lineColor,e.lineWidth=Math.round(1*n);const d=(0,v.fillScaledRadius)(2,n);(0,f.createCircle)(e,s,o,d),e.fill(),void 0!==i.excludeBoundaries&&(e.save(),(0,_.addExclusionArea)(e,t,i.excludeBoundaries)),(0,f.drawLine)(e,s,o,a,l),void 0!==i.excludeBoundaries&&e.restore(),this._priceLabelRenderer.draw(e,t);const h=1*n;e.strokeStyle=i.circleBorderColor,e.lineWidth=h;const c=d+h/2;(0,f.createCircle)(e,s,o,c),e.stroke(),e.restore()}hitTest(e){const t=this._data;if(null===t)return null;const i=(0,g.lastMouseOrTouchEventInfo)().isTouch?20:3;return(0,c.distanceToSegment)(t.points[0],t.points[1],e).distance<=i?this._hittest:this._priceLabelRenderer.hitTest(e)}}class R extends h.LineSourcePaneView{constructor(){super(...arguments),this._renderer=new l.CompositeRenderer,this._priceNoteRenderer=new w,this._customLabelRenderer=new d.TextRenderer}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){var t;super._updateImpl(e),this._renderer.clear();const i=this._source.priceScale();if(!i||i.isEmpty())return;const n=this._points;if(n.length<2)return;const r=(0,s.ensureNotNull)(this._source.ownerSource()).firstValue();if(null===r)return;const l=this._source.properties().childs(),h=this._model.dark().value()?a.colorsPalette["color-cold-gray-900"]:a.colorsPalette["color-white"],c=this._source.points()[0].price,u={text:i.formatPrice(c,r),points:n,lineColor:l.lineColor.value(),circleBorderColor:h,backgroundColor:l.priceLabelBackgroundColor.value(),borderColor:l.priceLabelBorderColor.value(),textColor:l.priceLabelTextColor.value(),fontSize:l.priceLabelFontSize.value(),bold:l.priceLabelBold.value(),italic:l.priceLabelItalic.value()};if(l.showLabel&&l.showLabel.value()){const i=n[0],r=n[1],s=i.x<r.x?i:r,a=s===i?r:i,h=l.vertLabelsAlign.value(),c=l.horzLabelsAlign.value();let _;_="left"===c?s.clone():"right"===c?a.clone():new o.Point((i.x+r.x)/2,(i.y+r.y)/2);const g=Math.atan((a.y-s.y)/(a.x-s.x)),x={points:[_],text:l.text.value(),color:l.textColor.value(),vertAlign:h,horzAlign:c,
font:p.CHART_FONT_FAMILY,offsetX:0,offsetY:0,bold:l.bold.value(),italic:l.italic.value(),fontsize:l.fontSize.value(),forceTextAlign:!0,angle:g};if(this._customLabelRenderer.setData(x),this._renderer.append(this._customLabelRenderer),"middle"===h){const{mediaSize:{width:i,height:n}}=e;u.excludeBoundaries=null!==(t=(0,d.getTextBoundaries)(this._customLabelRenderer,i,n))&&void 0!==t?t:void 0}}this._renderer.append(this._priceNoteRenderer),this._priceNoteRenderer.setData(u),this._renderer.append(this.createLineAnchor({points:n},0))}}},64661:(e,t,i)=>{"use strict";i.r(t),i.d(t,{PriceRangePaneView:()=>x});var n=i(50151),r=i(86441),s=i(63273),o=i(17330),a=i(62189),l=i(91046),d=i(95201),h=i(43290),c=i(51056),u=i(49857),_=i(15938),p=i(27436);const g=(0,h.getPercentageFormatter)();class x extends p.DateAndPriceRangeBasePaneView{constructor(){super(...arguments),this._topBorderRenderer=new l.TrendLineRenderer,this._bottomBorderRenderer=new l.TrendLineRenderer,this._distanceRenderer=new l.TrendLineRenderer,this._backgroundRenderer=new a.RectangleRenderer,this._labelRenderer=new o.TextRenderer,this._renderer=new d.CompositeRenderer}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){var t,i,a;if(super._updateImpl(e),this._renderer.clear(),this._points.length<2||this._source.points().length<2)return;const l=this._source.properties().childs(),d=l.extendLeft.value(),p=l.extendRight.value(),[x,f]=this._points,v=Math.min(x.x,f.x),T=Math.max(x.x,f.x);l.fillBackground.value()&&(this._backgroundRenderer.setData({points:[new r.Point(v,x.y),new r.Point(T,f.y)],color:"white",linewidth:0,backcolor:l.backgroundColor.value(),fillBackground:!0,transparency:l.backgroundTransparency.value(),extendLeft:d,extendRight:p}),this._renderer.append(this._backgroundRenderer));const w=(e,t,i)=>{e.setData({points:[t,i],color:l.linecolor.value(),linewidth:l.linewidth.value(),linestyle:c.LINESTYLE_SOLID,extendleft:d,extendright:p,leftend:u.LineEnd.Normal,rightend:u.LineEnd.Normal}),this._renderer.append(e)};let R=v,m=T;R===m&&(d&&(R-=1),p&&(m+=1)),w(this._topBorderRenderer,new r.Point(R,x.y),new r.Point(m,x.y)),w(this._bottomBorderRenderer,new r.Point(R,f.y),new r.Point(m,f.y));const y=Math.round((x.x+f.x)/2),b=new r.Point(y,x.y),L=new r.Point(y,f.y),{mediaSize:{width:P,height:S}}=e,M=this._updateCustomTextRenderer(S,P);this._distanceRenderer.setData({points:[b,L],color:l.linecolor.value(),linewidth:l.linewidth.value(),linestyle:c.LINESTYLE_SOLID,extendleft:!1,extendright:!1,leftend:u.LineEnd.Normal,rightend:Math.abs(b.y-L.y)>=15*l.linewidth.value()?u.LineEnd.Arrow:u.LineEnd.Normal,excludeBoundaries:M?[M]:void 0}),this._renderer.append(this._distanceRenderer);const C=this._source.points()[0].price,I=this._source.points()[1].price,A=I-C,D=100*A/Math.abs(C),k=this._model.mainSeries().symbolInfo(),N=k&&(0,h.getPipFormatter)(k),B=(0,n.ensureNotNull)(this._source.ownerSource()).formatter(),H=null!==(i=null===(t=B.formatChange)||void 0===t?void 0:t.call(B,I,C))&&void 0!==i?i:B.format(A),z=(0,
s.forceLTRStr)(H+" ("+g.format(D)+") "+(N?N.format(A):""));let E;E=I>C?new r.Point(.5*(x.x+f.x),f.y-2*l.fontsize.value()):new r.Point(.5*(x.x+f.x),f.y+.7*l.fontsize.value());const W={x:0,y:10},O=l.fontsize.value(),V={points:[E],text:z,color:l.textcolor.value(),font:_.CHART_FONT_FAMILY,offsetX:W.x,offsetY:W.y,padding:8,vertAlign:"middle",horzAlign:"center",fontsize:O,backgroundRoundRect:4,boxPaddingHorz:.4*O+O/3,boxPaddingVert:.2*O+O/3};(null===(a=l.fillLabelBackground)||void 0===a?void 0:a.value())&&(V.boxShadow={shadowColor:l.shadow.value(),shadowBlur:4,shadowOffsetY:1},V.backgroundColor=l.labelBackgroundColor.value()),this._labelRenderer.setData(V);const F=this._labelRenderer.measure(),Y=(0,o.calculateLabelPosition)(F,x,f,W,S);this._labelRenderer.setPoints([Y]),this._renderer.append(this._labelRenderer),this._renderer.append(this._customTextrenderer),this.addAnchors(this._renderer)}_needLabelExclusionPath(e){return e.getLinesInfo().lines.length>0}}},31869:(e,t,i)=>{"use strict";i.r(t),i.d(t,{ProjectionLinePaneView:()=>l});var n=i(55053),r=i(84417),s=i(91046),o=i(95201),a=i(49857);class l extends r.FibWedgePaneView{constructor(e,t){super(e,t),this._arcWedgeRenderer=new n.ArcWedgeRenderer,this._baseTrendRenderer=new s.TrendLineRenderer,this._edgeTrendRenderer=new s.TrendLineRenderer,this._arcWedgeRenderer=new n.ArcWedgeRenderer}_getPoints(){if(this._points.length<3)return this._points;const e=this._points,t=e[0],i=e[1];let n=e[2];const r=n.pointIndex,s=i.subtract(t).length(),o=n.subtract(t).normalized();return n=t.add(o.scaled(s)),n.pointIndex=r,[t,i,n]}_updateRenderer(e=NaN,t=NaN){if(this._points.length<2)return;const i=new o.CompositeRenderer,n=this._source.properties().childs(),[r,s,l]=this._getPoints(),d=n.trendline.childs().color.value(),h=n.linewidth.value(),c=n.trendline.childs().linestyle.value();if(this._baseTrendRenderer.setData({points:[r,s],color:d,linewidth:h,linestyle:c,extendleft:!1,extendright:!1,leftend:a.LineEnd.Normal,rightend:a.LineEnd.Normal}),i.append(this._baseTrendRenderer),this._points.length<3)return this.addAnchors(i),void(this._renderer=i);this._edgeTrendRenderer.setData({points:[r,l],color:d,linewidth:h,linestyle:c,extendleft:!1,extendright:!1,leftend:a.LineEnd.Normal,rightend:a.LineEnd.Normal}),i.append(this._edgeTrendRenderer);const u=this._levels[0];this._arcWedgeRenderer.setData({center:this._points[0],radius:u.radius,prevRadius:0,color:d,color1:n.color1.value(),color2:n.color2.value(),linewidth:h,angle1:e,angle2:t,p1:u.p1,p2:u.p2,fillBackground:n.fillBackground.value(),transparency:n.transparency.value(),gradient:!0}),i.append(this._arcWedgeRenderer),this.addAnchors(i),this._renderer=i}}},41883:(e,t,i)=>{"use strict";i.r(t),i.d(t,{RectanglePaneView:()=>g});var n=i(86441),r=i(19063),s=i(15938),o=i(62189),a=i(95201),l=i(72791),d=i(56468),h=i(36036),c=i(17330),u=i(13075),_=i(32211),p=i(62689);class g extends _.InplaceTextLineSourcePaneView{constructor(e,t,i,n,r){super(e,t,i,n,r),this._rectangleRenderer=new o.RectangleRenderer,this._renderer=null,
this._textRenderer=new p.LineToolTextRenderer(void 0,new d.HitTestResult(d.HitTarget.MovePoint,(0,_.inplaceEditHandlers)(this._tryActivateEditMode.bind(this,null)))),this._source.setAdditionalCursorData((()=>{var e;return{color:this._source.editableTextStyle().cursorColor,rotationPoint:null!==(e=this._textRenderer.rotation())&&void 0!==e?e:void 0,...this._textRenderer.getTextInfo()}}),this._textRenderer.positionToCoordinate.bind(this._textRenderer))}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){var t,i;if(super._updateImpl(e),this._renderer=null,this._points.length<2)return;const o=this._getSource().properties().childs(),l={points:this._points,color:o.color.value(),linewidth:o.linewidth.value(),backcolor:o.fillBackground.value()?(0,r.generateColor)(o.backgroundColor.value(),o.transparency.value()):"transparent",fillBackground:!0,extendLeft:o.extendLeft.value(),extendRight:o.extendRight.value(),backgroundHitTarget:this._model.selection().isSelected(this._source)?d.HitTarget.MovePoint:void 0},h=new a.CompositeRenderer;h.append(this._rectangleRenderer);const _=this._points[0],p=this._points[1],g=this._placeHolderMode();let x=!1;if((null===(t=o.showLabel)||void 0===t?void 0:t.value())&&o.text.value()||g||this._isTextEditMode()){const t=Math.min(_.x,p.x),i=Math.max(_.x,p.x),r=Math.min(_.y,p.y),a=Math.max(_.y,p.y);let l,d,c,f;const v=o.fontSize.value()/3;let T,w,R=0;switch(o.vertLabelsAlign.value()){case"middle":f=(r+a)/2,d="middle",R=v;break;case"top":f=a,d="top";break;case"bottom":f=r,d="bottom"}switch(o.horzLabelsAlign.value()){case"center":c=(t+i)/2,l="center";break;case"left":c=t,l="left";break;case"right":c=i,l="right"}"middle"===d&&(T=i-t-2*R,w=a-r),this._textRenderer.setData({points:[new n.Point(c,f)],text:this._textData(),color:this._textColor(),fontSize:o.fontSize.value(),font:s.CHART_FONT_FAMILY,bold:o.bold.value(),italic:o.italic.value(),horzAlign:l,vertAlign:d,wordWrapWidth:g?void 0:T,maxHeight:g?void 0:w,offsetX:0,offsetY:0,boxPaddingVert:v,boxPaddingHorz:R,forceTextAlign:!0,forceCalculateMaxLineWidth:!0,decorator:g?u.PlusTextRendererDecorator.instance():void 0,...this._inplaceTextHighlight()});let m=!1;if(void 0!==T&&g&&this._textRenderer.measure().width>T&&(this._textRenderer.updateData({text:""}),m=this._textRenderer.measure().width>T),!m){this._textRenderer.setCursorType(this._textCursorType());const{mediaSize:{width:t,height:i}}=e;this._textRenderer.isOutOfScreen(t,i)?this.closeTextEditor():this._updateInplaceText(this._textRenderer.getTextInfo()),h.append(this._textRenderer),x=!0}}if(o.middleLine.childs().showLine.value()){let t;if(x&&"middle"===o.vertLabelsAlign.value()&&(0,c.needTextExclusionPath)(this._textRenderer)){const{mediaSize:{width:n,height:r}}=e;t=null!==(i=(0,c.getTextBoundaries)(this._textRenderer,n,r))&&void 0!==i?i:void 0}const{lineColor:n,lineWidth:r,lineStyle:s}=o.middleLine.state();l.middleLine={lineColor:n,lineWidth:r,lineStyle:s,excludeBoundaries:t}}this._rectangleRenderer.setData(l),this._addAnchors(_,p,h),this._renderer=h}
_addAnchors(e,t,i){const n=e.x-t.x,r=e.y-t.y,s=Math.sign(n*r),o=s<0?l.PaneCursorType.DiagonalNeSwResize:l.PaneCursorType.DiagonalNwSeResize,a=s<0?l.PaneCursorType.DiagonalNwSeResize:l.PaneCursorType.DiagonalNeSwResize,d=[(0,h.anchor)({...e,cursorType:o}),(0,h.anchor)({...t,cursorType:o}),(0,h.anchor)({x:e.x,y:t.y,pointIndex:2,cursorType:a}),(0,h.anchor)({x:t.x,y:e.y,pointIndex:3,cursorType:a}),(0,h.anchor)({x:e.x,y:.5*(e.y+t.y),pointIndex:4,square:!0,cursorType:l.PaneCursorType.HorizontalResize}),(0,h.anchor)({x:t.x,y:.5*(e.y+t.y),pointIndex:5,square:!0,cursorType:l.PaneCursorType.HorizontalResize}),(0,h.anchor)({x:.5*(e.x+t.x),y:e.y,pointIndex:6,square:!0,cursorType:l.PaneCursorType.VerticalResize}),(0,h.anchor)({x:.5*(e.x+t.x),y:t.y,pointIndex:7,square:!0,cursorType:l.PaneCursorType.VerticalResize})];i.append(this.createLineAnchor({points:d},0))}}},90185:(e,t,i)=>{"use strict";i.r(t),i.d(t,{RegressionTrendPaneView:()=>g});var n=i(50151),r=i(19063),s=i(56468),o=i(95201),a=i(90241),l=i(17330),d=i(91046),h=i(11064),c=i(86441),u=i(49857),_=i(15938);var p=i(27916);class g extends p.LineSourcePaneView{constructor(e,t){super(e,t),this._data=null,this._pearsonsLabelRenderer=new l.TextRenderer,this._renderer=null,this._renderer=null}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){super._updateImpl(e),this._data=function(e,t){const i={lines:[],pearsons:null};if(!t.properties().visible.value())return i;const r=e.timeScale(),s=t.priceScale(),o=e.mainSeries().firstBar();if(!s||s.isEmpty()||r.isEmpty()||!o)return i;const a=t.startIndex(),l=t.endIndex();if(null===a||null===l)return i;const d=[t.baseLine(),t.downLine(),t.upLine()],h=Math.round(r.indexToCoordinate(a)),p=Math.round(r.indexToCoordinate(l)),g=t.properties(),x=[g.styles.baseLine,g.styles.downLine,g.styles.upLine],f=o[4];for(let r=0;r<d.length;r++){if(1!=(1&x[r].display.value()))continue;const o=(0,n.ensureNotNull)(d[r]).startPrice,a=(0,n.ensureNotNull)(d[r]).endPrice;if(void 0===o||void 0===a)continue;const l=s.priceToCoordinate(o,f),_=s.priceToCoordinate(a,f),v=new c.Point(h,l),T=new c.Point(p,_),w=x[r].color.value(),R=x[r].linewidth.value(),m=x[r].linestyle.value(),y={points:[v,T],width:e.timeScale().width(),height:(0,n.ensureNotNull)(t.priceScale()).height(),color:w,linewidth:R,linestyle:m,extendleft:!1,extendright:g.styles.extendLines.value(),leftend:u.LineEnd.Normal,rightend:u.LineEnd.Normal};i.lines.push(y)}const v=(0,n.ensureNotNull)(t.downLine());if(g.styles.showPearsons.value()&&void 0!==v.startPrice){const e=s.priceToCoordinate(v.startPrice,f),n=new c.Point(h,e);i.pearsons={points:[n],text:""+t.pearsons(),color:g.styles.downLine.color.value(),vertAlign:"top",horzAlign:"center",font:_.CHART_FONT_FAMILY,offsetX:0,offsetY:4,fontsize:12}}return i}(this._model,this._source),this._renderer=null;const t=new o.CompositeRenderer;let i=[];const l=[this._data.lines[1],this._data.lines[0],this._data.lines[2]].filter((e=>!!e)),p=this._source.properties().childs().styles.childs().transparency.value();for(let e=1;e<l.length;e++){
const i=l[e].color,n=l[e].linewidth,o=l[e].linestyle,d=l[e].extendright,h={line1:{color:i,lineStyle:o,lineWidth:n,points:[l[e].points[0],l[e].points[1]]},line2:{color:i,lineStyle:o,lineWidth:n,points:[l[e-1].points[0],l[e-1].points[1]]},extendLeft:!1,extendRight:d,backColor:(0,r.generateColor)(l[e].color,p),skipLines:!0,fillBackground:!0},c=new a.ParallelChannelRenderer(new s.HitTestResult(s.HitTarget.Regular));c.setData(h),t.append(c)}const g=this._getTransparencyResetLines();for(let e=0;e<l.length;e++){const n=new d.TrendLineRenderer;n.setData(g[e]),n.setHitTest(new s.HitTestResult(s.HitTarget.Regular)),t.append(n),0!==e&&(i=i.concat(l[e].points))}this._data.pearsons&&(this._data.pearsons.color=(0,r.resetTransparency)(this._data.pearsons.color),this._pearsonsLabelRenderer.setData(this._data.pearsons),t.append(this._pearsonsLabelRenderer)),this._data.lines.length>=1&&t.append(new h.SelectionRenderer({points:i,bgColors:this._lineAnchorColors(i),visible:this.areAnchorsVisible(),hittestResult:s.HitTarget.Regular,barSpacing:this._model.timeScale().barSpacing()})),this._renderer=t}_getTransparencyResetLines(){return(0,n.ensureNotNull)(this._data).lines.map((e=>({...e,color:(0,r.resetTransparency)(e.color)})))}}},1971:(e,t,i)=>{"use strict";i.r(t),i.d(t,{RiskRewardPaneView:()=>C});var n=i(86441),r=i(50151),s=i(11542),o=i(27916),a=i(91046),l=i(17330),d=i(62189),h=i(56468),c=i(95201),u=i(43290),_=i(19063),p=i(49857),g=i(76050),x=i(63273),f=i(72791),v=i(88145),T=i(15938),w=i(36036),R=i(51056);const m=s.t(null,void 0,i(76250)),y=s.t(null,{context:"line_tool_position"},i(18741)),b=s.t(null,{context:"line_tool_position"},i(72630)),L=s.t(null,void 0,i(36825)),P=s.t(null,void 0,i(79463)),S=s.t(null,void 0,i(72274)),M=s.t(null,void 0,i(10780));class C extends o.LineSourcePaneView{constructor(){super(...arguments),this._entryLineRenderer=new a.TrendLineRenderer,this._stopLineRenderer=new a.TrendLineRenderer,this._targetLineRenderer=new a.TrendLineRenderer,this._positionLineRenderer=new a.TrendLineRenderer,this._fullStopBgRenderer=new d.RectangleRenderer,this._stopBgRenderer=new d.RectangleRenderer,this._fullTargetBgRenderer=new d.RectangleRenderer,this._targetBgRenderer=new d.RectangleRenderer,this._stopLabelRenderer=new l.TextRenderer,this._middleLabelRenderer=new l.TextRenderer,this._profitLabelRenderer=new l.TextRenderer,this._renderer=new c.CompositeRenderer}isLabelVisible(){return this.isHoveredSource()||this.isSelectedSource()||this._source.properties().childs().alwaysShowStats.value()}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){super._updateImpl(e),this._renderer.clear();const t=this._model.timeScale(),i=this._source.priceScale();if(!i||i.isEmpty()||t.isEmpty())return;const n=this._source.points();if(n.length<2||this._points.length<2)return;const s=(0,r.ensureNotNull)(this._source.ownerSource()),o=null==s?void 0:s.barsProvider().bars();if(!o||o.isEmpty())return;if(null===o.last())return;const a=4===n.length,l=this._source.lastBarData();if(!l)return
;const d=l.closePrice,h=this._source.stopPrice(),c=this._source.profitPrice(),_=this._source.calculatePL(d),p=s.symbolSource().symbolInfo();if(!p)return;const x=s.firstValue();if(null===x)return;const v=this._points[g.RiskRewardPointIndex.Entry].y,T=i.priceToCoordinate(h,x),R=i.priceToCoordinate(c,x),m=i.priceToCoordinate(l.closePrice,x),y=t.indexToCoordinate(l.index),b=this._points[g.RiskRewardPointIndex.Entry].x,L=this._points[g.RiskRewardPointIndex.ActualEntry]?this._points[g.RiskRewardPointIndex.ActualEntry].x:this._points[g.RiskRewardPointIndex.Close].x,P=this._points[g.RiskRewardPointIndex.ActualClose]?this._points[g.RiskRewardPointIndex.ActualClose].x:this._points[g.RiskRewardPointIndex.Close].x,S=this._points[g.RiskRewardPointIndex.Close].x,M=this._source.entryPrice(),C=this._source.stopPrice(),I=this._source.profitPrice(),A={pl:_,isClosed:a,entryLevel:v,stopLevel:T,profitLevel:R,closeLevel:m,closeBar:y,left:b,entryX:L,right:P,edge:S,entryPrice:M,stopPrice:C,profitPrice:I,currentPrice:d},{mediaSize:{width:D,height:k}}=e;let N=S<-5||b>D+5;if(this._createBackgroundRenderers(A,this._renderer),this._createLinesRenderers(A,this._renderer),this._createLabelsRenderers(A,this._renderer,p,(0,u.getPipFormatter)(p)),N=[this._profitLabelRenderer,this._stopLabelRenderer,this._middleLabelRenderer].reduce(((e,t)=>e&&t.isOutOfScreen(D,k)),N),N)return;const B=[(0,w.anchor)({x:b,y:this._points[0].y,pointIndex:0,snappingPrice:M}),(0,w.anchor)({x:S,y:this._points[0].y,pointIndex:1,square:!0,snappingIndex:l.index,cursorType:f.PaneCursorType.HorizontalResize}),(0,w.anchor)({x:b,y:T,pointIndex:2,square:!0,snappingPrice:C,cursorType:f.PaneCursorType.VerticalResize}),(0,w.anchor)({x:b,y:R,pointIndex:3,square:!0,snappingPrice:I,cursorType:f.PaneCursorType.VerticalResize})];this._renderer.append(this.createLineAnchor({points:B},0))}_createBackgroundRenderers(e,t){const i=this._source.properties().childs();{const t={points:[new n.Point(e.left,e.entryLevel),new n.Point(e.edge,e.stopLevel)],color:"white",linewidth:0,backcolor:i.stopBackground.value(),fillBackground:!0,transparency:i.stopBackgroundTransparency.value(),extendLeft:!1,extendRight:!1,backgroundHitTarget:h.HitTarget.MovePoint};this._fullStopBgRenderer.setData(t),this._renderer.append(this._fullStopBgRenderer)}if(e.pl<0&&e.entryX!==e.right){const t=new n.Point(e.entryX,e.entryLevel),r=new n.Point(e.right,e.closeLevel),s=.01*i.stopBackgroundTransparency.value(),o=100-100*(1-s*s*s),a={points:[t,r],color:"white",linewidth:0,backcolor:i.stopBackground.value(),fillBackground:!0,transparency:o,extendLeft:!1,extendRight:!1,backgroundHitTarget:h.HitTarget.MovePoint};this._stopBgRenderer.setData(a),this._renderer.append(this._stopBgRenderer)}{const t={points:[new n.Point(e.left,e.entryLevel),new n.Point(e.edge,e.profitLevel)],color:"white",linewidth:0,backcolor:i.profitBackground.value(),fillBackground:!0,transparency:i.profitBackgroundTransparency.value(),extendLeft:!1,extendRight:!1,backgroundHitTarget:h.HitTarget.MovePoint};this._fullTargetBgRenderer.setData(t),
this._renderer.append(this._fullTargetBgRenderer)}if(e.pl>0&&e.entryX!==e.right){const t=new n.Point(e.entryX,e.entryLevel),r=new n.Point(e.right,e.closeLevel),s=.01*i.profitBackgroundTransparency.value(),o=100-100*(1-s*s*s),a={points:[t,r],color:"white",linewidth:0,backcolor:i.profitBackground.value(),fillBackground:!0,transparency:o,extendLeft:!1,extendRight:!1,backgroundHitTarget:h.HitTarget.MovePoint};this._targetBgRenderer.setData(a),this._renderer.append(this._targetBgRenderer)}}_createLinesRenderers(e,t){const i=this._source.properties().childs(),r=(e,t,n,r)=>{const s={points:[t,n],color:null!=r?r:i.linecolor.value(),linewidth:i.linewidth.value(),linestyle:R.LINESTYLE_SOLID,extendleft:!1,extendright:!1,leftend:p.LineEnd.Normal,rightend:p.LineEnd.Normal};e.setData(s),this._renderer.append(e)};if(this._points[g.RiskRewardPointIndex.ActualEntry]){const t={points:[this._points[g.RiskRewardPointIndex.ActualEntry],e.isClosed?this._points[g.RiskRewardPointIndex.ActualClose]:new n.Point(e.closeBar,e.closeLevel)],color:this._source.properties().childs().linecolor.value(),linewidth:1,linestyle:R.LINESTYLE_DASHED,extendleft:!1,extendright:!1,leftend:p.LineEnd.Normal,rightend:p.LineEnd.Arrow};this._positionLineRenderer.setData(t),this._renderer.append(this._positionLineRenderer)}{const t=new n.Point(e.left,this._points[g.RiskRewardPointIndex.Entry].y),i=new n.Point(e.edge,this._points[g.RiskRewardPointIndex.Entry].y);r(this._entryLineRenderer,t,i)}{const t=new n.Point(e.left,e.stopLevel),s=new n.Point(e.edge,e.stopLevel);r(this._stopLineRenderer,t,s,i.stopBackground.value())}{const t=new n.Point(e.left,e.profitLevel),s=new n.Point(e.edge,e.profitLevel);r(this._targetLineRenderer,t,s,i.profitBackground.value())}}_addCenterLabel(e,t,i){const n=this._source.properties().childs(),r={font:T.CHART_FONT_FAMILY,offsetX:3,horzAlign:"center",backgroundRoundRect:4,boxPaddingHorz:4,points:[i.p],text:i.txt,color:n.textcolor.value(),offsetY:i.offsetY,vertAlign:i.vertAlign,backgroundColor:(0,_.resetTransparency)(i.color),fontsize:n.fontsize.value(),borderColor:i.border};return t.setData(r),e.append(t),r}_creareMiddleLabel(e,t,i){const{entryPrice:s,profitPrice:o,stopPrice:a,currentPrice:l,pl:d,left:h,edge:c,isClosed:_}=e,p=Math.abs(s-o)/Math.abs(s-a),g=this._source.properties().childs(),x=(0,r.ensureNotNull)(this._source.ownerSource()),f=new n.Point((h+c)/2,Math.round(this._points[0].y));let T="",w="";const R=(0,u.getNumericFormatter)().format(Math.round(100*p)/100);if(this._points[1]){const e=x.formatter();if(e.formatChange){const t=Math.max(l,s),i=Math.min(l,s);w=d>=0?e.formatChange(t,i):e.formatChange(i,t)}else w=e.format(d)}const P=g.qty.value()/g.lotSize.value(),S="futures"===i.type||(0,v.hasCryptoTypespec)(i.typespecs||[])?Math.round(1e3*P)/1e3:Math.floor(P);if(g.compact.value())T+=w?w+" ~ ":"",T+=S+"\n",T+=R;else{const e=_?b:y;T+=w?m.format({status:e,pnl:w})+", ":"",T+=M.format({qty:""+S})+"\n",T+=L.format({ratio:R})+" "}let C=g.linecolor.value();return d<0?C=g.stopBackground.value():d>0&&(C=g.profitBackground.value()),
this._addCenterLabel(t,this._middleLabelRenderer,{p:f,txt:T,color:C,vertAlign:"middle",offsetY:0,border:"white"})}_createStopLabel(e,t,i){var s,o;const{stopPrice:a,entryPrice:l,left:d,edge:h,stopLevel:c}=e,_=this._source.properties().childs(),p=(0,r.ensureNotNull)(this._source.ownerSource()),g=Math.abs(a-l),f=Math.round(1e4*g/l)/100,v=new n.Point((d+h)/2,c);let T="";const w=p.formatter(),R=null!==(o=null===(s=w.formatChange)||void 0===s?void 0:s.call(w,Math.max(a,l),Math.min(a,l)))&&void 0!==o?o:w.format(g),m=(0,u.getPercentageFormatter)(),y=m.format(f);return T=_.compact.value()?R+" ("+y+") "+_.amountStop.value():P.format({stopChange:(0,x.forceLTRStr)(R),stopChangePercent:(0,x.forceLTRStr)(m.format(f)),stopChangePip:i?(0,x.forceLTRStr)(i.format(g)):"",amount:(0,x.forceLTRStr)(""+_.amountStop.value())}),this._addCenterLabel(t,this._stopLabelRenderer,{p:v,txt:T,color:_.stopBackground.value(),vertAlign:l<a?"bottom":"top",offsetY:0})}_createTargetLabel(e,t,i){var s,o;const{profitPrice:a,entryPrice:l,stopPrice:d,left:h,edge:c,profitLevel:_}=e,p=this._source.properties().childs(),g=(0,r.ensureNotNull)(this._source.ownerSource()),f=Math.abs(a-l),v=Math.round(1e4*f/l)/100,T=new n.Point((h+c)/2,_);let w="";const R=g.formatter(),m=null!==(o=null===(s=R.formatChange)||void 0===s?void 0:s.call(R,Math.max(a,l),Math.min(a,l)))&&void 0!==o?o:R.format(f),y=(0,u.getPercentageFormatter)(),b=y.format(v);return w=p.compact.value()?m+" ("+b+") "+p.amountTarget.value():S.format({profitChange:m,profitChangePercent:(0,x.forceLTRStr)(y.format(v)),profitChangePip:i?(0,x.forceLTRStr)(i.format(f)):"",amount:(0,x.forceLTRStr)(""+p.amountTarget.value())}),this._addCenterLabel(t,this._profitLabelRenderer,{p:T,txt:w,color:p.profitBackground.value(),vertAlign:l<d?"top":"bottom",offsetY:0})}_createLabelsRenderers(e,t,i,s){var o;if(!this.isLabelVisible())return;const a=this._creareMiddleLabel(e,t,i),l=this._createStopLabel(e,t,s),d=this._createTargetLabel(e,t,s),h=[this._profitLabelRenderer,this._stopLabelRenderer,this._middleLabelRenderer].reduce(((e,t)=>Math.max(e,t.measure().width)),0),c=e.edge-e.left,u=this._anchorRadius();if(c-h-u<=8&&(d&&(d.offsetY+=u+8,this._profitLabelRenderer.setData(d)),l&&(l.offsetY+=u+8,this._stopLabelRenderer.setData(l)),a)){let t;if(null===(o=this._source.priceScale())||void 0===o?void 0:o.isLog()){const i=Math.abs(this._points[0].y-e.stopLevel);t=Math.abs(this._points[0].y-e.profitLevel)>i?-1:1}else{const i=Math.abs(e.stopPrice-e.entryPrice);t=Math.abs(e.profitPrice-e.entryPrice)>i?-1:1}const i=e.profitLevel<e.stopLevel?1:-1,s=(0,r.ensureDefined)(a.points)[0].add(new n.Point(0,i*t*(.5*this._middleLabelRenderer.measure().height+u+8)));a.points=[s],this._middleLabelRenderer.setData(a)}}}},78270:(e,t,i)=>{"use strict";var n=i(86441).Point,r=i(4652).distanceToLine,s=i(27916).LineSourcePaneView,o=i(27916).thirdPointCursorType,a=i(91046).TrendLineRenderer,l=i(74011).PolygonRenderer,d=i(95201).CompositeRenderer,h=i(49857).LineEnd;const{LINESTYLE_SOLID:c}=i(51056);t.RotatedRectanglePaneView=class extends s{constructor(e,t){
super(e,t),this._poligonRenderer=new l,this._renderer=null}_updateImpl(e){if(super._updateImpl(e),this._renderer=null,this._distance=0,3===this._points.length&&(this._distance=r(this._points[0],this._points[1],this._points[2]).distance),0!==this._points.length){var t,i,s,l,u=new d,_=this._source.properties(),p=this._points[0],g=this._points[1];if(2===this._points.length){(f={}).points=this._points,f.floatPoints=this._floatPoints,f.width=this._model.timeScale().width(),f.height=this._source.priceScale().height(),f.color=_.color.value(),f.linewidth=1,f.linestyle=c,f.extendleft=!1,f.extendright=!1,f.leftend=h.Normal,f.rightend=h.Normal;var x=new a;x.setData(f),u.append(x)}else if(3===this._points.length){var f,v=g.subtract(p),T=new n(v.y,-v.x).normalized().scaled(this._distance),w=T.scaled(-1);t=p.add(T),i=g.add(T),s=p.add(w),l=g.add(w),(f={}).points=[t,i,l,s],f.color=_.color.value(),f.linewidth=this._source.properties().linewidth.value(),f.linestyle=c,f.filled=!0,f.backcolor=_.backgroundColor.value(),f.fillBackground=_.fillBackground.value(),f.transparency=_.transparency.value(),this._poligonRenderer.setData(f),u.append(this._poligonRenderer)}var R=[];if(R.push(p),this._points.length>=2&&R.push(g),3===this._points.length){var m=o(p,g);t.pointIndex=2,t.cursorType=m,s.pointIndex=2,s.cursorType=m,i.pointIndex=2,i.cursorType=m,l.pointIndex=2,l.cursorType=m,R.push(t,s,i,l)}u.append(this.createLineAnchor({points:R},0)),this._renderer=u}}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}}},3646:(e,t,i)=>{"use strict";i.r(t),i.d(t,{Constants:()=>r,SignpostItemIndex:()=>I,SignpostPaneView:()=>A});var n,r,s,o=i(86441),a=i(19625),l=i(67580),d=i(19365),h=i(56468),c=i(72791),u=i(95201),_=i(36036),p=i(12027),g=i(15511),x=i(32211),f=i(62689),v=i(15938),T=i(4652),w=i(34026),R=i(61993),m=i(69186);function y(e){return void 0===e?0:e.poleTailHeight+2*e.circleRadius}function b(e){return e.poleStartY}function L(e){const{inverseAnchorPosition:t,anchorCoordinates:i}=e;return t?i.y:i.y+(e.labelHeight+y(e.plate))*e.direction}function P(e){const{inverseAnchorPosition:t,anchorCoordinates:i,direction:n}=e;return t?i.y+y(e.plate)*n:i.y+e.labelHeight*n}!function(e){e[e.HitTestTolerance=3]="HitTestTolerance",e[e.HitTestToleranceTouch=20]="HitTestToleranceTouch",e[e.ShadowYOffset=1]="ShadowYOffset",e[e.ShadowBlur=4]="ShadowBlur"}(n||(n={}));class S{constructor(e){this._data=null,this._phantomMode=Boolean(e)}setData(e){this._data=e}hitTest(e){if(null===this._data)return null;const[t,i]=function(e,t){const i=(0,m.lastMouseOrTouchEventInfo)().isTouch?20:3,{anchorCoordinates:n,plate:r}=e,s=(0,T.distanceToSegment)(new o.Point(n.x,b(e)),new o.Point(n.x,L(e)),t).distance<i;let a=!1;if(!s&&void 0!==r){const s=e.inverseAnchorPosition?e.anchorCoordinates.y+r.circleRadius*e.direction:e.anchorCoordinates.y+(e.labelHeight+r.poleTailHeight+r.circleRadius)*e.direction;a=r.circleRadius>0&&(0,w.pointInCircle)(t,new o.Point(n.x,s),r.circleRadius+i)}return[s,a]}(this._data,e);if(t||i){const e={hideCrosshairLinesOnHover:!0,
activeItem:this._data.itemIndex};return i&&(e.areaName=h.AreaName.Style),new h.HitTestResult(h.HitTarget.MovePoint,e)}return null}draw(e,t){if(null===this._data)return;e.save(),this._phantomMode&&(e.globalAlpha=.5);const{poleColor:i,emojiRadius:n}=this._data,{horizontalPixelRatio:r,verticalPixelRatio:s}=t,o=Math.max(1,Math.floor(r)),a=o%2?.5:0;e.beginPath(),e.strokeStyle=i,e.lineWidth=o;const l=Math.round(this._data.anchorCoordinates.x*r)+a;e.moveTo(l,Math.round(b(this._data)*s)),e.lineTo(l,Math.round(L(this._data)*s)),void 0!==this._data.plate&&0!==this._data.plate.poleTailHeight&&(e.moveTo(l,Math.round(P(this._data)*s)),e.lineTo(l,Math.round(function(e){var t,i;const n=null!==(i=null===(t=e.plate)||void 0===t?void 0:t.poleTailHeight)&&void 0!==i?i:0;return e.inverseAnchorPosition?P(e)-n*e.direction:P(e)+n*e.direction}(this._data)*s))),e.stroke(),void 0!==this._data.plate&&function(e,t,i,n,r){const{horizontalPixelRatio:s,verticalPixelRatio:o}=r,{circleRadius:a,poleTailHeight:l,circleBorderColor:d,circleBackgroundColor:h}=i;e.strokeStyle=d,e.fillStyle=h;const c=(0,R.fillScaledRadius)(a,s),u=Math.round(t.anchorCoordinates.x*s),_=t.inverseAnchorPosition?Math.round(t.anchorCoordinates.y*o)+Math.round(a*o)*t.direction:Math.round(t.anchorCoordinates.y*o)+Math.round((t.labelHeight+l+a)*o)*t.direction,p=u+Math.max(1,Math.floor(s))%2/2,g=_+Math.max(1,Math.floor(o))%2/2;if(e.shadowOffsetY=1,e.shadowColor=i.shadowColor,e.shadowBlur=4,e.beginPath(),e.arc(p,g,c,0,2*Math.PI,!0),e.closePath(),e.fill(),e.shadowColor="transparent",t.svgRenderer){const i=2*(0,R.fillScaledRadius)(n,s);t.svgRenderer.render(e,{targetViewBox:{x:p-i/2,y:g-i/2,width:i,height:i}})}const x=Math.round(i.circleBorderWidth*s),f=(0,R.strokeScaledRadius)(a,s,x);if(e.lineWidth=x,e.beginPath(),e.arc(p,g,f,0,2*Math.PI,!0),e.closePath(),e.stroke(),i.outsideBorderWidth){e.save();const t=Math.round(i.outsideBorderWidth*s),n=f+x/2+t/2;e.lineWidth=t,e.strokeStyle=i.outsideBorderColor,e.beginPath(),e.arc(p,g,n,0,2*Math.PI,!0),e.closePath(),e.stroke(),e.restore()}}(e,this._data,this._data.plate,n,t),e.restore()}}!function(e){e[e.EmojiRadius=16]="EmojiRadius",e[e.LabelFontSize=12]="LabelFontSize",e[e.LabelBorderRadius=4]="LabelBorderRadius",e[e.LabelLineSpacing=3]="LabelLineSpacing",e[e.LabelHorzPadding=8]="LabelHorzPadding",e[e.LabelWordWrapWidth=134]="LabelWordWrapWidth",e[e.AdditionalTopBottomSpace=2]="AdditionalTopBottomSpace",e[e.CalculationEpsilon=1e-10]="CalculationEpsilon",e[e.SourceLabelBorderWidth=1]="SourceLabelBorderWidth"}(r||(r={})),function(e){e[e.CircleRadius=35]="CircleRadius",e[e.CircleBorderWidth=1]="CircleBorderWidth",e[e.HoveredCircleBorderWidth=1]="HoveredCircleBorderWidth",e[e.SelectedCircleBorderWidth=2]="SelectedCircleBorderWidth",e[e.LabelOffset=10]="LabelOffset",e[e.SourceLabelSize=24]="SourceLabelSize",e[e.LabelVertPadding=6]="LabelVertPadding"}(s||(s={}));const M={circleBorderColor:(0,a.getHexColorByName)("color-cold-gray-900"),labelBackgroundColor:(0,a.getHexColorByName)("color-cold-gray-900"),labelBorderColor:(0,
a.getHexColorByName)("color-cold-gray-800"),labelTextColor:(0,a.getHexColorByName)("color-cold-gray-200"),poleColor:(0,a.getHexColorByName)("color-cold-gray-500"),shadowColor:"rgba(0,0,0,0.4)",selectionColor:(0,a.getHexColorByName)("color-tv-blue-500")},C={circleBorderColor:(0,a.getHexColorByName)("color-white"),labelBackgroundColor:(0,a.getHexColorByName)("color-white"),labelBorderColor:(0,a.getHexColorByName)("color-cold-gray-150"),labelTextColor:(0,a.getHexColorByName)("color-cold-gray-900"),poleColor:(0,a.getHexColorByName)("color-cold-gray-500"),shadowColor:"rgba(0,0,0,0.2)",selectionColor:(0,a.getHexColorByName)("color-tv-blue-500")};var I;!function(e){e[e.Label=0]="Label",e[e.Body=1]="Body"}(I||(I={}));class A extends x.InplaceTextLineSourcePaneView{constructor(e,t,i,n,r){super(e,t,i,n,r),this._renderer=new u.CompositeRenderer,this._emojiCache=null,this._destroyed=!1,this._signpostRenderer=new S(e.isPhantom()),this._labelRenderer=new f.LineToolTextRenderer(void 0,new h.HitTestResult(h.HitTarget.MovePoint,(0,x.inplaceEditHandlers)(this._tryActivateEditMode.bind(this,null)))),this._source.setAdditionalCursorData((()=>({color:this._source.editableTextStyle().cursorColor,...this._labelRenderer.getTextInfo()})),this._labelRenderer.positionToCoordinate.bind(this._labelRenderer)),e.properties().childs().emoji.subscribe(this,this._updateEmoji),this._updateEmoji()}destroy(){this._source.properties().childs().emoji.unsubscribeAll(this),this._destroyed=!0,super.destroy()}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){super._updateImpl(e),this._renderer.clear();const t=this._getBasePosition();if(!t)return;const i=this._updateLabelTextRenderer(e,t);if(this._updateTimelineRenderer(t,i),this._renderer.append(this._signpostRenderer),this._renderer.append(this._labelRenderer),!this._source.isPhantom()){const e=this._points[0],t=(0,_.anchor)({x:e.x,y:i.y,pointIndex:e.pointIndex,square:!0,cursorType:c.PaneCursorType.VerticalResize});this._renderer.append(this.createLineAnchor({points:[t]},0))}}_updateTimelineRenderer(e,t){var i;const{poleStartY:n,inverse:r,direction:s}=e,o=this._source.properties().childs(),a=this._getCurrentColorTheme(),l={emojiRadius:16,poleColor:a.poleColor,svgRenderer:null===(i=this._emojiCache)||void 0===i?void 0:i.emojiSvgRenderer,poleStartY:n,itemIndex:1,anchorCoordinates:t,direction:s,inverseAnchorPosition:r,labelHeight:this._labelRenderer.measure().height};o.showImage.value()&&(l.plate={circleBackgroundColor:o.backgroundsColors.value(),outsideBorderWidth:0,circleBorderColor:a.circleBorderColor,circleBorderWidth:1,poleTailHeight:o.text.value()?10:0,circleRadius:35,shadowColor:a.shadowColor,outsideBorderColor:a.selectionColor}),this._signpostRenderer.setData(l)}async _updateEmoji(){var e;const t=this._source.properties().childs().emoji.value();if(null!==this._emojiCache&&this._emojiCache.emoji===t)return;null!==this._emojiCache&&(null===(e=this._emojiCache.abortController)||void 0===e||e.abort(),this._emojiCache.abortController=void 0);const i=(0,
d.getTwemojiUrl)(t,"svg"),n=new AbortController,r={emoji:t,abortController:n};this._emojiCache=r;const s=await(0,l.fetch)(i,{signal:n.signal}).then((e=>e.text()));!this._destroyed&&r.abortController&&(r.emojiSvgRenderer=(0,p.svgRenderer)(s),this._model.updateSource(this._source))}_updateLabelTextRenderer(e,t){const i=this._source.properties().childs(),n=this._getCurrentColorTheme(),{positionPointDirection:r,indexCoordinate:s,priceCoordinate:a,inverse:l,direction:d}=t,{height:h,width:c}=e.mediaSize;let u=(0,g.positionToCoordinate)(i.position.value(),h,a,r);u>=-1e-10&&u<=h+1e-10&&(u=Math.min(h-2,Math.max(2,u)));const _={...this._inplaceTextHighlight(),text:this._textData(),fontSize:i.fontSize.value(),bold:i.bold.value(),italic:i.italic.value(),offsetX:0,offsetY:0,points:[new o.Point(s,u)],forceCalculateMaxLineWidth:!0,vertAlign:-1===r?"bottom":"top",horzAlign:"center",horzTextAlign:"center",font:v.CHART_FONT_FAMILY,backgroundRoundRect:4,padding:3,boxPaddingVert:6,boxPaddingHorz:8,wordWrapWidth:134,color:this._textColor(),borderColor:n.labelBorderColor,borderWidth:1,backgroundColor:n.labelBackgroundColor};this._labelRenderer.setData(_);const p=this._labelRenderer.measure().height;let x={x:s,y:u};if(l){if(i.showImage.value()){const e=70+(i.text.value()?10:0);this._labelRenderer.setData({..._,points:[new o.Point(s,u+e*d)]})}}else{const{poleStartY:e}=t,i=1===d?Math.min(e-p,u):Math.max(e+p,u);u!==i&&(this._labelRenderer.setData({..._,points:[new o.Point(s,i)]}),x={x:s,y:i})}return this._labelRenderer.setCursorType(this._textCursorType()),this._labelRenderer.isOutOfScreen(c,h)?this.closeTextEditor():this._updateInplaceText(this._labelRenderer.getTextInfo()),x}_getCurrentColorTheme(){return this._model.dark().value()?M:C}_getBasePosition(){const e=this._source.ownerSource();if(!e)return null;const t=e.priceScale(),i=this._model.timeScale(),n=e.firstValue();if(i.isEmpty()||!t||t.isEmpty()||!n)return null;const r=this._model.mainSeries(),s=this._source.customEvent(),o=e===r?(0,g.getSeriesPosition)(r,s):(0,g.getNoDataPosition)(s,t,i,n);if(!o)return null;const{visualDirection:a,positionPointDirection:l}=o,d=a!==l;return{...o,inverse:d,direction:a*(d?-1:1)}}}},14661:(e,t,i)=>{"use strict";i.r(t),i.d(t,{SineLinePaneView:()=>c});var n,r=i(86441),s=i(27916),o=i(95201),a=i(75919),l=i(56468),d=i(37743);!function(e){e[e.Tolerance=3]="Tolerance",e[e.SegmentsPerHalfPeriod=30]="SegmentsPerHalfPeriod"}(n||(n={}));class h extends a.MediaCoordinatesPaneRenderer{constructor(e){super(),this._data=e}hitTest(e){const t=(e.x-this._data.point.x)*Math.PI/this._data.width;let i=Math.sin(t-Math.PI/2)*this._data.height/2;return i=this._data.point.y+i+this._data.height/2,Math.abs(i-e.y)<=3?new l.HitTestResult(l.HitTarget.MovePoint):null}_drawImpl(e){const t=e.context;t.strokeStyle=this._data.color,t.lineWidth=this._data.lineWidth,(0,d.setLineStyle)(t,this._data.lineStyle),t.beginPath(),t.moveTo(this._data.point.x,this._data.point.y);const i=Math.max(1,this._data.width/30),n=e.mediaSize.width-this._data.point.x+i;for(let e=1;e<=n;e+=i){
const i=e*Math.PI/this._data.width,n=Math.sin(i-Math.PI/2)*this._data.height/2;t.lineTo(this._data.point.x+e,this._data.point.y+n+this._data.height/2)}t.stroke()}}class c extends s.LineSourcePaneView{constructor(e,t){super(e,t),this._renderer=new o.CompositeRenderer}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){const{mediaSize:{height:t}}=e;if(super._updateImpl(e),this._renderer.clear(),this._points.length<2)return;const[i,n]=this._source.points();if(0===2*Math.abs(i.index-n.index))return void this.addAnchors(this._renderer);const[s,o]=this._points,a=Math.abs(s.x-o.x),l=o.y-s.y,d=this._source.properties().childs(),c=d.linewidth.value();if(s.y<-c&&o.y<-c||s.y>t+c&&o.y>t+c)return;const u=2*a,_=s.x>0?s.x-Math.ceil(s.x/u)*u:s.x+Math.floor(-s.x/u)*u,p={point:new r.Point(_,s.y),width:a,height:l,color:d.linecolor.value(),lineWidth:d.linewidth.value(),lineStyle:d.linestyle.value()};this._renderer.append(new h(p)),this.addAnchors(this._renderer)}}},44386:(e,t,i)=>{"use strict";i.r(t),i.d(t,{StickerPaneView:()=>r});var n=i(68498);class r extends n.SvgIconPaneView{_iconColor(){return null}}},19583:(e,t,i)=>{"use strict";i.r(t),i.d(t,{StudyLineDataSourceAnchorsPaneView:()=>r});var n=i(27916);class r extends n.LineSourcePaneView{renderer(e){return this._invalidated&&(this._updateImpl(e),this._invalidated=!1),this.createLineAnchor({points:this._getPoints()},0)}}},68498:(e,t,i)=>{"use strict";i.d(t,{SvgIconPaneView:()=>g});var n=i(86441),r=i(25422),s=i(50151),o=i(19063),a=i(95201),l=i(36036),d=i(27916),h=i(19625),c=i(75919),u=i(56468);const _=(0,h.getHexColorByName)("color-tv-blue-600");class p extends c.MediaCoordinatesPaneRenderer{constructor(){super(...arguments),this._data=null}setData(e){this._data=e}hitTest(e){if(null===this._data)return null;const{size:t,angle:i,point:n}=this._data,s=(0,r.rotationMatrix)(-i),o=(0,r.transformPoint)(s,e.subtract(n));return Math.abs(o.y)<=t/2&&Math.abs(o.x)<=t/2?new u.HitTestResult(u.HitTarget.MovePoint):null}isOutOfScreen(e,t){if(null===this._data)return!0;const{size:i,point:n,angle:r}=this._data;let s;return s=r%(Math.PI/2)==0?i/2:Math.sqrt(i**2*2)/2,n.x+s<0||n.x-s>t||n.y+s<0||n.y-s>e}_drawImpl(e){if(null===this._data)return;const{size:t,svg:i,point:n,angle:r,color:s,background:o,selected:a}=this._data,l=e.context;l.translate(n.x,n.y);const d=r-Math.PI/2;l.rotate(d);const h=t/2;a&&(l.fillStyle=o,l.strokeStyle=_,l.beginPath(),l.rect(-h,-h,t,t),l.closePath(),l.fill(),l.stroke()),i&&(l.translate(-h,-h),null!==s&&(l.fillStyle=s),i.render(l,{targetViewBox:{x:0,y:0,width:t,height:t},doNotApplyColors:null!==s}))}}class g extends d.LineSourcePaneView{constructor(e,t,i){super(e,t),this._iconRenderer=new p,this._renderer=new a.CompositeRenderer,this._svg=i}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){if(super._updateImpl(e),this._renderer.clear(),this._points.length<1)return;const t=this._source.properties().childs(),i=t.size.value(),s={point:this._points[0],color:this._iconColor(),size:i,svg:this._svg,
angle:t.angle.value(),selected:this.areAnchorsVisible(),background:this._calculateBackgroundColor()};this._iconRenderer.setData(s);const{mediaSize:{width:o,height:a}}=e;this._iconRenderer.isOutOfScreen(a,o)||this._renderer.append(this._iconRenderer);const h=this._points[0],c=this._source.getAnchorLimit();let u=new n.Point(Math.max(c,i)/2,0),_=new n.Point(0,Math.max(c,i)/2);const p=(0,r.rotationMatrix)(t.angle.value());u=(0,r.transformPoint)(p,u),_=(0,r.transformPoint)(p,_);const g=h.add(u),x=h.subtract(u),f=(0,d.thirdPointCursorType)(g,x),v=[(0,l.anchor)({...g,pointIndex:0,nonDiscreteIndex:!0}),(0,l.anchor)({...x,pointIndex:1,nonDiscreteIndex:!0}),(0,l.anchor)({...h.add(_),pointIndex:2,nonDiscreteIndex:!0,cursorType:f}),(0,l.anchor)({...h.subtract(_),pointIndex:3,square:!0,nonDiscreteIndex:!0,cursorType:f})];this._renderer.append(this.createLineAnchor({points:v},0))}_calculateBackgroundColor(){return(0,o.generateColor)(this._model.backgroundColorAtYPercentFromTop(this._points[0].y/(0,s.ensureNotNull)(this._model.paneForSource(this._source)).height()),60,!0)}}},13589:(e,t,i)=>{"use strict";i.r(t),i.d(t,{TextPaneView:()=>p});var n=i(50151),r=i(86441),s=i(15938),o=i(72791),a=i(95201),l=i(11064),d=i(49256),h=i(36036),c=i(56468),u=i(32211),_=i(62689);class p extends u.InplaceTextLineSourcePaneView{constructor(e,t,i,n,r,s,o,a,l,d,h){super(e,t,l,d,h),this._noSelection=!1,this._renderer=null,this._offsetX=i,this._offsetY=n,this._vertAlign=r,this._horzAlign=s,this._forceTextAlign=Boolean(o),this._noSelection=!1,this._renderer=null,this._recalculateSourcePointsOnFirstUpdate=a,this._textRenderer=new _.LineToolTextRenderer(void 0,new c.HitTestResult(c.HitTarget.MovePoint,(0,u.inplaceEditHandlers)(this._tryActivateEditMode.bind(this,null)))),this._source.setAdditionalCursorData((()=>({color:this._source.editableTextStyle().cursorColor,...this._textRenderer.getTextInfo()})),this._textRenderer.positionToCoordinate.bind(this._textRenderer))}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}disableSelection(){this._noSelection=!0}isEditMode(){return!this._getModel().readOnly()}_updateImpl(e){super._updateImpl(e),this._renderer=null;const t=this._getSource(),i=t.priceScale();if(!i||i.isEmpty())return;const u=t.properties().childs(),_=this._getModel(),p={text:this._textData(),color:this._textColor(),fontSize:u.fontsize.value(),boxPadding:u.fontsize.value()/6,font:s.CHART_FONT_FAMILY,vertAlign:this._vertAlign||"top",horzAlign:this._horzAlign||"left",offsetX:this._offsetX||0,offsetY:this._offsetY||0,forceTextAlign:this._forceTextAlign,...this._inplaceTextHighlight()};p.points=t.isFixed()?[(0,n.ensureDefined)(t.fixedPoint())]:this._points,u.fillBackground&&u.fillBackground.value()&&(p.backgroundColor=u.backgroundColor.value()),u.drawBorder&&u.drawBorder.value()&&(p.borderColor=u.borderColor.value()),u.wordWrap&&u.wordWrap.value()&&(p.wordWrapWidth=u.wordWrapWidth.value()),p.bold=u.bold&&u.bold.value(),p.italic=u.italic&&u.italic.value();if(_.selection().isSelected(t)&&(p.highlightBorder=!0,
p.highlightBorderColor=u.color.value()),!t.isFixed()&&u.fixedSize&&!u.fixedSize.value()){p.scaleX=_.timeScale().barSpacing()/t.barSpacing();const e=(0,n.ensureNotNull)(i.priceRange());let r=i.height()/e.length();const s=i.logFormula();t.isPriceDencityLog()&&!i.isLog()&&(r=i.height()/((0,d.toLog)(e.maxValue(),s)-(0,d.toLog)(e.minValue(),s))),!t.isPriceDencityLog()&&i.isLog()&&(r=i.height()/((0,d.fromLog)(e.maxValue(),s)-(0,d.fromLog)(e.minValue(),s)));const o=t.priceDencity();void 0!==o&&(p.scaleY=r/o),(void 0===o||void 0===p.scaleY||p.scaleY<=0)&&delete p.scaleY}this._textRenderer.setData(p),this._textRenderer.setCursorType(this._textCursorType());const{mediaSize:{width:g,height:x}}=e;if(this._textRenderer.isOutOfScreen(g,x))return void this.closeTextEditor();const f=1===p.points.length;if(this._updateInplaceText(this._textRenderer.getTextInfo()),f&&void 0!==this._recalculateSourcePointsOnFirstUpdate){this._renderer=null;const e=this._textRenderer.measure();return this._recalculateSourcePointsOnFirstUpdate(e.width,e.height),void(this._recalculateSourcePointsOnFirstUpdate=void 0)}if(!f||this._noSelection)this._renderer=this._textRenderer;else{const e=new a.CompositeRenderer;e.append(this._textRenderer);const t=p.points[0].clone(),i=this._textRenderer.measure(),n=i.width,s=i.height;if(p.wordWrapWidth){const i=[(0,h.anchor)({x:t.x+n,y:t.y+s/2,pointIndex:1,cursorType:o.PaneCursorType.HorizontalResize})];e.append(this.createLineAnchor({points:i},1))}if(!this._isTextEditMode()){const i=new r.Point(t.x+n/2,t.y+s);i.pointIndex=0,e.append(new l.SelectionRenderer({points:[i],bgColors:this._lineAnchorColors([i]),visible:this.areAnchorsVisible(),hittestResult:c.HitTarget.MovePoint,barSpacing:_.timeScale().barSpacing()}))}this._renderer=e}}}},62689:(e,t,i)=>{"use strict";i.d(t,{LineToolTextRenderer:()=>r});var n=i(17330);class r extends n.TextRenderer{getTextInfo(){var e;const t=this._getInternalData(),i=this.fontStyle(),n=this._getFontInfo();return{...t,lineHeight:this.lineHeight(),lineSpacing:this.lineSpacing(),font:i,fontSize:n.fontSize,centerRotation:null!==(e=this.centerTextRotation())&&void 0!==e?e:void 0}}setCursorType(e){this._hitTest.mergeData({cursorType:e})}}},6822:(e,t,i)=>{"use strict";i.r(t),i.d(t,{LineToolThreeDrivesPaneView:()=>u});var n=i(51056),r=i(95201),s=i(17330),o=i(43290),a=i(91046),l=i(49857),d=i(74011),h=i(27916),c=i(15938);class u extends h.LineSourcePaneView{constructor(){super(...arguments),this._retrace1LabelRenderer=new s.TextRenderer,this._retrace12LabelRenderer=new s.TextRenderer,this._polyLineRenderer=new d.PolygonRenderer,this._renderer=null}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){super._updateImpl(e),this._renderer=null;let t=NaN,i=NaN;if(this._source.points().length>=4){const[,e,i,n]=this._source.points();t=Math.round(100*Math.abs((n.price-i.price)/(i.price-e.price)))/100}if(this._source.points().length>=6){const[,,,e,t,n]=this._source.points();i=Math.round(100*Math.abs((n.price-t.price)/(t.price-e.price)))/100}if(this._points.length<2)return
;const s=this._source.properties().childs(),d=new r.CompositeRenderer,h=(e,t)=>({points:[e],text:t,color:s.textcolor.value(),vertAlign:"middle",horzAlign:"center",font:c.CHART_FONT_FAMILY,offsetX:0,offsetY:0,bold:s.bold&&s.bold.value(),italic:s.italic&&s.italic.value(),fontsize:s.fontsize.value(),backgroundColor:s.color.value(),backgroundRoundRect:4}),u=(e,t)=>({points:[e,t],color:s.color.value(),linewidth:s.linewidth.value(),linestyle:n.LINESTYLE_DOTTED,extendleft:!1,extendright:!1,leftend:l.LineEnd.Normal,rightend:l.LineEnd.Normal}),_={points:this._points,color:s.color.value(),linewidth:s.linewidth.value(),linestyle:n.LINESTYLE_SOLID,leftend:l.LineEnd.Normal,rightend:l.LineEnd.Normal,backcolor:"rgba(0, 0, 0, 0)",fillBackground:!1,filled:!1};this._polyLineRenderer.setData(_),d.append(this._polyLineRenderer);const p=(0,o.getNumericFormatter)();if(!isNaN(t)){const e=new a.TrendLineRenderer;e.setData(u(this._points[1],this._points[3])),d.append(e);const i=h(this._points[1].add(this._points[3]).scaled(.5),p.format(t));this._retrace1LabelRenderer.setData(i),d.append(this._retrace1LabelRenderer)}if(!isNaN(i)){const e=new a.TrendLineRenderer;e.setData(u(this._points[3],this._points[5])),d.append(e);const t=h(this._points[5].add(this._points[3]).scaled(.5),p.format(i));this._retrace12LabelRenderer.setData(t),d.append(this._retrace12LabelRenderer)}this.addAnchors(d),this._renderer=d}}},80485:(e,t,i)=>{"use strict";i.r(t),i.d(t,{TimeCyclesPaneView:()=>c});var n=i(86441),r=i(95201),s=i(27916),o=i(19063),a=i(56468),l=i(37743),d=i(75919);class h extends d.MediaCoordinatesPaneRenderer{constructor(){super(...arguments),this._data=null}setData(e){this._data=e}hitTest(e){if(null===this._data||e.y>this._data.point.y)return null;if(e.x<this._data.point.x||e.x>this._data.point.x+this._data.width)return null;const t=new n.Point(this._data.point.x+this._data.width/2,this._data.point.y);let i=e.subtract(t);const r=this._data.height/this._data.width;i=new n.Point(i.x,i.y/r);const s=i.length();return Math.abs(s-this._data.width/2)<3?new a.HitTestResult(a.HitTarget.MovePoint):null}_drawImpl(e){if(null===this._data)return;const t=e.context;t.strokeStyle=this._data.color,t.lineWidth=this._data.linewidth,(0,l.setLineStyle)(t,this._data.linestyle),t.save(),t.translate(this._data.point.x+1,this._data.point.y),t.scale(this._data.width,this._data.height),t.beginPath(),t.arc(.5,0,.5,Math.PI,0,!1),t.restore(),t.stroke(),this._data.fillBackground&&(t.fillStyle=(0,o.generateColor)(this._data.backcolor,this._data.transparency),t.fill())}}class c extends s.LineSourcePaneView{constructor(){super(...arguments),this._renderer=null}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){if(super._updateImpl(e),this._renderer=null,this._points.length<2)return;const t=this._source.points(),i=t[0],s=t[1],o=Math.min(i.index,s.index),a=Math.max(i.index,s.index),l=a-o,d=this._points[0],c=this._points[1],u=Math.abs(d.x-c.x),_=new r.CompositeRenderer,p=this._source.properties().childs(),g=this._model.timeScale();if(0===l)return
;let x=Math.min(d.x,c.x);const f=[];for(let e=o;x>-u;e-=l)x=g.indexToCoordinate(e),f.push(x);x=Math.max(d.x,c.x);for(let e=a;x<g.width();e+=l)x=g.indexToCoordinate(e),f.push(x);for(let e=0;e<f.length;e++){const t={point:new n.Point(f[e],d.y),width:u,height:u,color:p.linecolor.value(),linewidth:p.linewidth.value(),linestyle:p.linestyle.value(),fillBackground:p.fillBackground.value(),backcolor:p.backgroundColor.value(),transparency:p.transparency.value()},i=new h;i.setData(t),_.append(i)}this.addAnchors(_),this._renderer=_}}},82595:(e,t,i)=>{"use strict";i.r(t),i.d(t,{TrendAnglePaneView:()=>w});var n=i(50151),r=i(86441),s=i(5531),o=i(34026),a=i(63273),l=i(56468),d=i(49857),h=i(39429),c=i(17330),u=i(91046),_=i(11064),p=i(15938),g=i(28081),x=i(36036),f=i(75919);class v extends f.MediaCoordinatesPaneRenderer{constructor(){super(...arguments),this._data=null}setData(e){this._data=e}hitTest(e){return null}_drawImpl(e){if(null===this._data)return;const t=e.context;t.translate(this._data.point.x,this._data.point.y),t.strokeStyle=this._data.color,t.setLineDash([1,2]);const i=this._data.size;t.beginPath(),t.moveTo(0,0),t.lineTo(i,0),t.arc(0,0,i,0,-this._data.angle,this._data.angle>0),t.stroke()}}var T=i(90139);class w extends T.TrendToolWithStatsPaneView{constructor(e,t){super(e,t),this._secondPoint=null,this._trendRenderer=new u.TrendLineRenderer,this._angleRenderer=new v,this._angleLabelRenderer=new c.TextRenderer}_getPointsForStats(){return[this._points[0],(0,n.ensureNotNull)(this._middlePoint),(0,n.ensureNotNull)(this._secondPoint)]}_updateImpl(e){var t;this._renderer.clear(),super._updateImpl(e);const i=this._source,n=i.angle();if(this._points.length>0&&null!==n){const e=Math.cos(n),t=-Math.sin(n),s=new r.Point(e,t);this._secondPoint=this._points[0].addScaled(s,i.distance()),this._middlePoint=this._source.calcMiddlePoint(this._points[0],this._secondPoint)}this._invalidated=!1;const c=this._source.priceScale(),u=this._model.timeScale();if(!c||c.isEmpty()||u.isEmpty())return;if(null===this._model.timeScale().visibleBarsStrictRange())return;if(this._source.points().length<2)return;if(this._points.length<2||null===this._secondPoint)return;const f=this._points[0],v=this._points[1],T=this._source.properties().childs();T.showBarsRange.value()||T.showPriceRange.value()||T.showPercentPriceRange.value()||T.showPipsPriceRange.value()||(this._label=null,this._labelData&&(this._labelData.text=""));const w=T.linecolor.value(),R={points:[f,this._secondPoint],color:w,linewidth:T.linewidth.value(),linestyle:T.linestyle.value(),extendleft:T.extendLeft.value(),extendright:T.extendRight.value(),leftend:d.LineEnd.Normal,rightend:d.LineEnd.Normal};this._trendRenderer.setData(R),this._renderer.append(this._trendRenderer);const m=(0,r.box)(new r.Point(0,0),new r.Point(e.mediaSize.width,e.mediaSize.height));let y=!1;T.statsPosition.value()===h.StatsPosition.Auto&&(y=(0,r.equalPoints)(f,v)?!(0,o.pointInBox)(f,m):null===(0,s.intersectLineSegmentAndBox)((0,r.lineSegment)(f,v),m))
;if((this.isHoveredSource()||this.isSelectedSource()||T.alwaysShowStats.value())&&!y&&2===this._points.length){const e=new g.PaneRendererCachedImage(this,0);this._renderer.append(e)}const b=(this.isHoveredSource()||this.isSelectedSource())&&T.showMiddlePoint.value();this._middlePoint&&this._renderer.append(new _.SelectionRenderer({points:[(0,x.anchor)({...this._middlePoint,pointIndex:0})],bgColors:this._lineAnchorColors([this._middlePoint]),color:w,visible:b&&this.areAnchorsVisible(),hittestResult:l.HitTarget.Regular,barSpacing:0}));const L={point:f,angle:null!==(t=i.angle())&&void 0!==t?t:0,color:T.linecolor.value(),size:50};this._angleRenderer.setData(L),this._renderer.append(this._angleRenderer);const P=Math.round(180*L.angle/Math.PI)+"º",S={points:[new r.Point(f.x+50,f.y)],text:(0,a.forceLTRStr)(P),color:T.textcolor.value(),horzAlign:"left",font:p.CHART_FONT_FAMILY,offsetX:5,offsetY:0,bold:T.bold.value(),italic:T.italic.value(),fontsize:T.fontsize.value(),vertAlign:"middle"};this._angleLabelRenderer.setData(S),this._renderer.append(this._angleLabelRenderer),R.points.length>=2&&this._addAlertRenderer(this._renderer,R.points);const M=(0,x.anchor)({...this._secondPoint,pointIndex:1});this._renderer.append(this.createLineAnchor({points:[f,M]},0))}}},90413:(e,t,i)=>{"use strict";i.r(t),i.d(t,{TrendBasedFibExtensionPaneView:()=>d});var n=i(86441),r=i(62189),s=i(91046),o=i(49857),a=i(70011),l=i(23033);class d extends l.FibHorizontalLevelsPaneViewBase{constructor(){super(...arguments),this._trendLineRendererPoints12=new s.TrendLineRenderer,this._trendLineRendererPoints23=new s.TrendLineRenderer,this._rectangleRenderers={},this._levels=[]}_tryActivateEditMode(e,t){this._source.setInplaceEditLevelIndex(e),super._tryActivateEditMode(e,t)}_updateImpl(e){var t;if(super._updateImpl(e),this._renderer.clear(),this._points.length<2)return;const[i,s]=this._points,l=this._source.properties().childs();if(3===this._source.points().length){const e=this._source.priceScale();if(!e||e.isEmpty()||this._model.timeScale().isEmpty())return;const i=null===(t=this._source.ownerSource())||void 0===t?void 0:t.firstValue();if(null==i)return;const[n,r,s]=this._source.points();let o=!1;l.reverse&&l.reverse.value()&&(o=l.reverse.value()),this._levels=[];const d=o?n.price:r.price,h=o?r.price:n.price,c=d-h;let u,_,p;const g=e.isLog()&&l.fibLevelsBasedOnLogScale.value();if(g){u=e.priceToCoordinate(d,i);_=u-e.priceToCoordinate(h,i),p=e.priceToCoordinate(s.price,i)}const x={price:s.price,coordinate:p},f={price:c,coordinate:_},v=this._source.levelsCount();for(let t=1;t<=v;t++){const n=l["level"+t].childs();if(!n.visible.value())continue;const r=n.coeff.value(),s=n.color.value(),o=n.text.value(),d=(0,a.fibLevelCoordinate)(x,f,r,e,i,g),h=(0,a.fibLevelPrice)(x,f,r,e,i,g);this._levels.push({color:s,price:h,y:d,linewidth:l.levelsStyle.childs().linewidth.value(),linestyle:l.levelsStyle.childs().linestyle.value(),index:t,text:o})}}const d=l.trendline.childs();if(d.visible.value()){const e={points:[i,s],color:d.color.value(),linewidth:d.linewidth.value(),
linestyle:d.linestyle.value(),extendleft:!1,extendright:!1,leftend:o.LineEnd.Normal,rightend:o.LineEnd.Normal};this._trendLineRendererPoints12.setData(e),this._renderer.append(this._trendLineRendererPoints12)}if(this._points.length<3)return void this.addAnchors(this._renderer);let h=null;const c=this._points[2];d.visible.value()&&(h=this._trendLineRendererPoints23,h.setData({points:[s,c],color:d.color.value(),linewidth:d.linewidth.value(),linestyle:d.linestyle.value(),extendleft:!1,extendright:!1,leftend:o.LineEnd.Normal,rightend:o.LineEnd.Normal}));const u=Math.min(c.x,s.x),_=Math.max(c.x,s.x),p=l.fillBackground.value(),g=l.transparency.value(),x=l.extendLinesLeft.value(),f=l.extendLines.value();if(p)for(let e=0;e<this._levels.length;e++)if(e>0&&p){const t=this._levels[e-1],i={points:[new n.Point(u,this._levels[e].y),new n.Point(_,t.y)],color:this._levels[e].color,linewidth:0,backcolor:this._levels[e].color,fillBackground:!0,transparency:g,extendLeft:x,extendRight:f};this._rectangleRenderers.hasOwnProperty(e)||(this._rectangleRenderers[e]=new r.RectangleRenderer(!0));const s=this._rectangleRenderers[e];s.setData(i),this._renderer.append(s)}this._addLevels({mediaSize:e.mediaSize,levels:this._levels,left:u,right:_,showLabel:l.showCoeffs.value()||l.showPrices.value(),showText:l.showText.value(),horzLabelsAlign:l.horzLabelsAlign.value(),vertLabelsAlign:l.vertLabelsAlign.value(),horzTextAlign:l.horzTextAlign.value(),vertTextAlign:l.vertTextAlign.value(),extendLeft:x,extendRight:f,fontSize:l.labelFontSize.value(),isOnScreen:!0,trendLineRenderer:h}),this.addAnchors(this._renderer),this._model.selection().isSelected(this._source)||this.closeTextEditor()}}},57175:(e,t,i)=>{"use strict";i.r(t),i.d(t,{TrendBasedFibTimePaneView:()=>_});var n=i(86441),r=i(95173),s=i(17330),o=i(62189),a=i(91046),l=i(56468),d=i(95201),h=i(49857),c=i(15938),u=i(27916);class _ extends u.LineSourcePaneView{constructor(e,t){super(e,t),this._trendLineRendererPoints12=new a.TrendLineRenderer,this._trendLineRendererPoints23=new a.TrendLineRenderer,this._textRenderers=[],this._renderer=new d.CompositeRenderer,this._levels=[];for(let t=0;t<e.levelsCount();t++)this._textRenderers.push(new s.TextRenderer)}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){var t;super._updateImpl(e),this._renderer.clear();const i=this._source.properties().childs();if(3===this._source.points().length){const e=this._model.timeScale();if(e.isEmpty())return;const[t,n,r]=this._source.points();if(this._levels=[],n.index===t.index)return;const s=n.index-t.index,o=r.index;if(null===e.visibleBarsStrictRange())return;for(let t=1;t<=this._source.levelsCount();t++){const n=i["level"+t].childs();if(!n.visible.value())continue;const r=n.coeff.value(),a=n.color.value(),l=Math.round(o+r*s),d={x:e.indexToCoordinate(l),coeff:r,color:a,linewidth:n.linewidth.value(),linestyle:n.linestyle.value(),index:t,text:String(r)};this._levels.push(d)}}if(this._points.length<2)return;const a=new d.CompositeRenderer,[u,_]=this._points,p=i.trendline.childs()
;if(p.visible.value()){const e={points:[u,_],color:p.color.value(),linewidth:p.linewidth.value(),linestyle:p.linestyle.value(),extendleft:!1,extendright:!1,leftend:h.LineEnd.Normal,rightend:h.LineEnd.Normal};this._trendLineRendererPoints12.setData(e),a.append(this._trendLineRendererPoints12)}if(this._points.length<3)return this.addAnchors(a),void(this._renderer=a);const g=this._points[2];if(p.visible.value()){const e={points:[_,g],color:p.color.value(),linewidth:p.linewidth.value(),linestyle:p.linestyle.value(),extendleft:!1,extendright:!1,leftend:h.LineEnd.Normal,rightend:h.LineEnd.Normal};this._trendLineRendererPoints23.setData(e),a.append(this._trendLineRendererPoints23)}const{mediaSize:{width:x,height:f}}=e;if(i.fillBackground.value()){const e=i.transparency.value();for(let t=1;t<this._levels.length;t++){const i=this._levels[t-1],r={points:[new n.Point(i.x,0),new n.Point(this._levels[t].x,f)],color:this._levels[t].color,linewidth:0,backcolor:this._levels[t].color,fillBackground:!0,transparency:e,extendLeft:!1,extendRight:!1},s=new o.RectangleRenderer(!0);s.setData(r),a.append(s)}}let v=i.horzLabelsAlign.value();v="left"===v?"right":"right"===v?"left":"center";const T=i.vertLabelsAlign.value(),w=i.showCoeffs.value();for(let e=0;e<this._levels.length;e++){let i;if(w){let r;switch(T){case"top":r=new n.Point(this._levels[e].x,0);break;case"middle":r=new n.Point(this._levels[e].x,.5*f);break;default:r=new n.Point(this._levels[e].x,f)}const o={points:[r],text:this._levels[e].text,color:this._levels[e].color,vertAlign:T,horzAlign:v,font:c.CHART_FONT_FAMILY,offsetX:2,offsetY:0,fontsize:12},l=this._textRenderers[e];l.setData(o),this._needLabelExclusionPath(l)&&(i=null!==(t=(0,s.getTextBoundaries)(l,x,f))&&void 0!==t?t:void 0),a.append(l)}const o={x:this._levels[e].x,color:this._levels[e].color,linewidth:this._levels[e].linewidth,linestyle:this._levels[e].linestyle,excludeBoundaries:i},d=new l.HitTestResult(l.HitTarget.MovePoint,void 0,this._levels[e].index),h=new r.VerticalLineRenderer;h.setData(o),h.setHitTest(d),a.append(h)}this.addAnchors(a),this._renderer=a}_needLabelExclusionPath(e){return"center"===this._source.properties().childs().horzLabelsAlign.value()}}},29710:(e,t,i)=>{"use strict";i.r(t),i.d(t,{TrendLinePaneView:()=>v});var n=i(50151),r=i(86441),s=i(34026),o=i(5531),a=i(28081),l=i(17330),d=i(62689),h=i(13075),c=i(39429),u=i(11064),_=i(91046),p=i(15938),g=i(56468),x=i(90139),f=i(32211);class v extends x.TrendToolWithStatsPaneView{constructor(e,t,i,n,r){super(e,t,i,n,r),this._trendRenderer=new _.TrendLineRenderer,this._labelRenderer=new d.LineToolTextRenderer(void 0,new g.HitTestResult(g.HitTarget.MovePoint,(0,f.inplaceEditHandlers)(this._tryActivateEditMode.bind(this,null)))),this._source.setAdditionalCursorData((()=>{var e;return{color:this._source.editableTextStyle().cursorColor,rotationPoint:null!==(e=this._labelRenderer.rotation())&&void 0!==e?e:void 0,...this._labelRenderer.getTextInfo()}}),this._labelRenderer.positionToCoordinate.bind(this._labelRenderer))}_getPointsForStats(){return[this._points[0],(0,
n.ensureNotNull)(this._middlePoint),this._points[1]]}_updateImpl(e){var t,i;this._renderer.clear(),this._invalidated=!1;const n=this._source.priceScale(),d=this._model.timeScale();if(!n||n.isEmpty()||d.isEmpty())return;const _=this._model.timeScale().visibleBarsStrictRange();if(null===_)return;const x=this._source.points();if(x.length<2)return;const f=x[0],v=x[1],T=this._source.properties().childs();if(f.index<_.firstBar()&&v.index<_.firstBar()&&!T.extendLeft.value()&&!T.extendRight.value())return;if(super._updateImpl(e),this._points.length<2)return;T.showPriceRange.value()||T.showPercentPriceRange.value()||T.showPipsPriceRange.value()||T.showBarsRange.value()||T.showDateTimeRange.value()||T.showDistance.value()||T.showAngle.value()||(this._label=null,this._labelData&&(this._labelData.text=""));const w=this._points[0],R=this._points[1],{mediaSize:{width:m,height:y}}=e;let b;const L=T.text.value(),P=this._isTextEditMode(),S=this._placeHolderMode(!0);if((null===(t=T.showLabel)||void 0===t?void 0:t.value())&&L||S||P){const e=w.x<R.x?w:R,t=e===w?R:w,n=T.vertLabelsAlign.value(),s=T.horzLabelsAlign.value();let o;o="left"===s?e.clone():"right"===s?t.clone():new r.Point((w.x+R.x)/2,(w.y+R.y)/2);const a=Math.atan((t.y-e.y)/(t.x-e.x));this._labelRenderer.setData({points:[o],text:this._textData(),color:this._textColor(),vertAlign:n,horzAlign:s,font:p.CHART_FONT_FAMILY,offsetX:0,offsetY:0,bold:T.bold.value(),italic:T.italic.value(),fontsize:T.fontsize.value(),forceTextAlign:!0,angle:a,decorator:S?h.PlusTextRendererDecorator.instance():void 0,...this._inplaceTextHighlight()}),this._labelRenderer.setCursorType(this._textCursorType()),this._renderer.append(this._labelRenderer),this._needLabelExclusionPath(this._labelRenderer)&&(b=null!==(i=(0,l.getTextBoundaries)(this._labelRenderer,m,y))&&void 0!==i?i:void 0),this._labelRenderer.isOutOfScreen(m,y)?this.closeTextEditor():this._updateInplaceText(this._labelRenderer.getTextInfo())}const M=T.linecolor.value(),C={points:this._points,color:M,linewidth:T.linewidth.value(),linestyle:T.linestyle.value(),extendleft:T.extendLeft.value(),extendright:T.extendRight.value(),leftend:T.leftEnd.value(),rightend:T.rightEnd.value(),excludeBoundaries:b?[b]:void 0};this._trendRenderer.setData(C),this._renderer.insert(this._trendRenderer,0);const I=(0,r.box)(new r.Point(0,0),new r.Point(m,y));let A=!1;T.statsPosition.value()===c.StatsPosition.Auto&&(A=(0,r.equalPoints)(w,R)?!(0,s.pointInBox)(w,I):null===(0,o.intersectLineSegmentAndBox)((0,r.lineSegment)(w,R),I));if(((this.isHoveredSource()||this.isSelectedSource())&&this.isEditMode()||T.alwaysShowStats.value())&&!A&&2===this._points.length){const e=new a.PaneRendererCachedImage(this,0);this._renderer.append(e)}if(this._middlePoint&&!P){const e=(this.isHoveredSource()||this.isSelectedSource())&&T.showMiddlePoint.value();this._renderer.append(new u.SelectionRenderer({points:[this._middlePoint],bgColors:this._lineAnchorColors([this._middlePoint]),color:M,visible:e&&this.areAnchorsVisible(),hittestResult:g.HitTarget.Regular,barSpacing:0}))}
this.addAnchors(this._renderer),C.points.length>=2&&this._addAlertRenderer(this._renderer,C.points)}}},90139:(e,t,i)=>{"use strict";i.d(t,{TrendToolWithStatsPaneView:()=>Y});var n=i(50151),r=i(86441),s=i(5531),o=i(11542),a=i(63273),l=i(95201),d=i(43290),h=i(92953),c=i(64034),u=i(27714),_=i(34026),p=i(49483),g=i(17330),x=i(56468),f=i(37743),v=i(37265),T=i(7114),w=i(2844),R=i(52033);class m{constructor(e,t,i){this._ready=!1,this._img=function(e,t,i){const n=new Image;return n.width=t,n.height=t,n.onload=i,n.src=e,n}(e,t,(()=>{this._ready=!0,i()}))}ready(){return this._ready}image(){return this._img}}let y=null;const b=18,L=new class{constructor(e,t){this._icons=new Map,this._onAllIconsLoaded=new R.Delegate,this._pendingLoading=e.length;const i=()=>{0==--this._pendingLoading&&this._onAllIconsLoaded.fire()};e.forEach((e=>{const n=this._icons.get(e.name)||new Map;n.set(e.theme,new m(e.imageData,t,i)),this._icons.set(e.name,n)}))}getIcon(e,t){return(0,n.ensureDefined)((0,n.ensureDefined)(this._icons.get(e)).get(t))}onAllIconsReady(){return this._onAllIconsLoaded}}([{name:"angle",theme:"dark",imageData:"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHZpZXdCb3g9IjAgMCAxOCAxOCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik0zLjQ5OTk5IDE1SDIuNjU3NzFMMy4wNjEwNCAxNC4yNjA2TDkuMDYxMDQgMy4yNjA1N0w5LjMwMDQ2IDIuODIxNjJMMTAuMTc4NCAzLjMwMDQ4TDkuOTM4OTMgMy43Mzk0Mkw3LjUxMzg1IDguMTg1NDJDMTAuNTYyMSA5LjY3MjA1IDEwLjk0NTEgMTIuNjI2MSAxMC45OTMxIDE0SDE0LjVIMTVWMTVIMTQuNUgzLjQ5OTk5Wk05Ljk5MTk3IDE0QzkuOTQyMzYgMTIuNzI1OSA5LjU4NjI5IDEwLjI4OCA3LjAzNDM1IDkuMDY0NDlMNC4zNDIyNiAxNEg5Ljk5MTk3WiIgZmlsbD0iI0Y4RjlGRCIvPgo8L3N2Zz4K"},{name:"angle",theme:"light",imageData:"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHZpZXdCb3g9IjAgMCAxOCAxOCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4NCjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNMy40OTk5OSAxNUgyLjY1NzcxTDMuMDYxMDQgMTQuMjYwNkw5LjA2MTA0IDMuMjYwNTdMOS4zMDA0NiAyLjgyMTYyTDEwLjE3ODQgMy4zMDA0OEw5LjkzODkzIDMuNzM5NDJMNy41MTM4NSA4LjE4NTQyQzEwLjU2MjEgOS42NzIwNSAxMC45NDUxIDEyLjYyNjEgMTAuOTkzMSAxNEgxNC41SDE1VjE1SDE0LjVIMy40OTk5OVpNOS45OTE5NyAxNEM5Ljk0MjM2IDEyLjcyNTkgOS41ODYyOSAxMC4yODggNy4wMzQzNSA5LjA2NDQ5TDQuMzQyMjYgMTRIOS45OTE5N1oiIGZpbGw9IiMyQTJFMzkiLz4NCjwvc3ZnPg0K"},{name:"barsRange",theme:"dark",
imageData:"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHZpZXdCb3g9IjAgMCAxOCAxOCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4NCjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNMiAzVjMuNVY1SDFWNlYxM1YxNEgyVjE1LjVWMTZIM1YxNS41VjE0SDRWMTNWNlY1SDNWMy41VjNIMlpNOC4yMDcxMSA3LjVMNy44NTM1NSA3Ljg1MzU1TDYuNzA3MTEgOUgxMS4yOTI5TDEwLjE0NjQgNy44NTM1NUw5Ljc5Mjg5IDcuNUwxMC41IDYuNzkyODlMMTAuODUzNiA3LjE0NjQ1TDEyLjg1MzYgOS4xNDY0NUwxMy4yMDcxIDkuNUwxMi44NTM2IDkuODUzNTVMMTAuODUzNiAxMS44NTM2TDEwLjUgMTIuMjA3MUw5Ljc5Mjg5IDExLjVMMTAuMTQ2NCAxMS4xNDY0TDExLjI5MjkgMTBINi43MDcxMUw3Ljg1MzU1IDExLjE0NjRMOC4yMDcxMSAxMS41TDcuNSAxMi4yMDcxTDcuMTQ2NDUgMTEuODUzNkw1LjE0NjQ1IDkuODUzNTVMNC43OTI4OSA5LjVMNS4xNDY0NSA5LjE0NjQ1TDcuMTQ2NDUgNy4xNDY0NUw3LjUgNi43OTI4OUw4LjIwNzExIDcuNVpNMyA2SDJWMTNIM1Y2Wk0xNSAzLjVWM0gxNlYzLjVWNUgxN1Y2VjEzVjE0SDE2VjE1LjVWMTZIMTVWMTUuNVYxNEgxNFYxM1Y2VjVIMTVWMy41Wk0xNSA2SDE2VjEzSDE1VjZaIiBmaWxsPSIjRjhGOUZEIi8+DQo8L3N2Zz4NCg=="},{name:"barsRange",theme:"light",imageData:"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHZpZXdCb3g9IjAgMCAxOCAxOCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4NCjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNMiAzVjMuNVY1SDFWNlYxM1YxNEgyVjE1LjVWMTZIM1YxNS41VjE0SDRWMTNWNlY1SDNWMy41VjNIMlpNOC4yMDcxMSA3LjVMNy44NTM1NSA3Ljg1MzU1TDYuNzA3MTEgOUgxMS4yOTI5TDEwLjE0NjQgNy44NTM1NUw5Ljc5Mjg5IDcuNUwxMC41IDYuNzkyODlMMTAuODUzNiA3LjE0NjQ1TDEyLjg1MzYgOS4xNDY0NUwxMy4yMDcxIDkuNUwxMi44NTM2IDkuODUzNTVMMTAuODUzNiAxMS44NTM2TDEwLjUgMTIuMjA3MUw5Ljc5Mjg5IDExLjVMMTAuMTQ2NCAxMS4xNDY0TDExLjI5MjkgMTBINi43MDcxMUw3Ljg1MzU1IDExLjE0NjRMOC4yMDcxMSAxMS41TDcuNSAxMi4yMDcxTDcuMTQ2NDUgMTEuODUzNkw1LjE0NjQ1IDkuODUzNTVMNC43OTI4OSA5LjVMNS4xNDY0NSA5LjE0NjQ1TDcuMTQ2NDUgNy4xNDY0NUw3LjUgNi43OTI4OUw4LjIwNzExIDcuNVpNMyA2SDJWMTNIM1Y2Wk0xNSAzLjVWM0gxNlYzLjVWNUgxN1Y2VjEzVjE0SDE2VjE1LjVWMTZIMTVWMTUuNVYxNEgxNFYxM1Y2VjVIMTVWMy41Wk0xNSA2SDE2VjEzSDE1VjZaIiBmaWxsPSIjMkEyRTM5Ii8+DQo8L3N2Zz4NCg=="},{name:"priceRange",theme:"dark",imageData:"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHZpZXdCb3g9IjAgMCAxOCAxOCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4NCjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNMyAySDMuNUgxMy41SDE0VjNIMTMuNUgzLjVIM1YyWk04LjUgMy43OTI4OUw4Ljg1MzU1IDQuMTQ2NDVMMTAuODUzNiA2LjE0NjQ1TDExLjIwNzEgNi41TDEwLjUgNy4yMDcxMUwxMC4xNDY0IDYuODUzNTVMOSA1LjcwNzExVjEyLjI5MjlMMTAuMTQ2NCAxMS4xNDY0TDEwLjUgMTAuNzkyOUwxMS4yMDcxIDExLjVMMTAuODUzNiAxMS44NTM2TDguODUzNTUgMTMuODUzNkw4LjUgMTQuMjA3MUw4LjE0NjQ1IDEzLjg1MzZMNi4xNDY0NSAxMS44NTM2TDUuNzkyODkgMTEuNUw2LjUgMTAuNzkyOUw2Ljg1MzU1IDExLjE0NjRMOCAxMi4yOTI5VjUuNzA3MTFMNi44NTM1NSA2Ljg1MzU1TDYuNSA3LjIwNzExTDUuNzkyODkgNi41TDYuMTQ2NDUgNi4xNDY0NUw4LjE0NjQ1IDQuMTQ2NDVMOC41IDMuNzkyODlaTTMuNSAxNkgzVjE1SDMuNUgxMy41SDE0VjE2SDEzLjVIMy41WiIgZmlsbD0iI0Y4RjlGRCIvPg0KPC9zdmc+DQo="},{name:"priceRange",theme:"light",
imageData:"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHZpZXdCb3g9IjAgMCAxOCAxOCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4NCjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNMyAySDMuNUgxMy41SDE0VjNIMTMuNUgzLjVIM1YyWk04LjUgMy43OTI4OUw4Ljg1MzU1IDQuMTQ2NDVMMTAuODUzNiA2LjE0NjQ1TDExLjIwNzEgNi41TDEwLjUgNy4yMDcxMUwxMC4xNDY0IDYuODUzNTVMOSA1LjcwNzExVjEyLjI5MjlMMTAuMTQ2NCAxMS4xNDY0TDEwLjUgMTAuNzkyOUwxMS4yMDcxIDExLjVMMTAuODUzNiAxMS44NTM2TDguODUzNTUgMTMuODUzNkw4LjUgMTQuMjA3MUw4LjE0NjQ1IDEzLjg1MzZMNi4xNDY0NSAxMS44NTM2TDUuNzkyODkgMTEuNUw2LjUgMTAuNzkyOUw2Ljg1MzU1IDExLjE0NjRMOCAxMi4yOTI5VjUuNzA3MTFMNi44NTM1NSA2Ljg1MzU1TDYuNSA3LjIwNzExTDUuNzkyODkgNi41TDYuMTQ2NDUgNi4xNDY0NUw4LjE0NjQ1IDQuMTQ2NDVMOC41IDMuNzkyODlaTTMuNSAxNkgzVjE1SDMuNUgxMy41SDE0VjE2SDEzLjVIMy41WiIgZmlsbD0iIzJBMkUzOSIvPg0KPC9zdmc+DQo="}],b),P=new Map;class S{constructor(e,t,i){this._fontSize=0,this._preRendered=!1,this._boundingBox=null,this._rect=null,this._padding=null,this._textPoint=null,this._textWidthCache=new w.TextWidthCache,this._textSizeCache=t,this._data=e,this._fontSize=e.fontSize?e.fontSize:12,this._lineSpacing=(0,v.isNumber)(this._data.lineSpacing)&&this._data.lineSpacing?this._data.lineSpacing:0,e.lines=this._lines=null===e.text?[]:(0,g.wordWrap)(e.text,this.fontStyle(),this._textWidthCache,!0,e.wordWrapWidth).map((e=>e.text)),this._hittest=i||new x.HitTestResult(x.HitTarget.MovePoint)}fontStyle(){return`${this._data.bold?"bold ":""}${this._data.italic?"italic ":""}${this._fontSize}px ${this._data.font}`}draw(e,t){if(0===this._data.points.length||null===this._data.text)return{width:0};this._preRender();const i=this._fontSize+this._lineSpacing;e.textBaseline="top",e.font=this.fontStyle();const r=(0,n.ensureNotNull)(this._rect);if(this._rect){if("right"!==this._data.horzAlign&&"center"!==this._data.horzAlign||!0!==this._data.doNotAlignText&&(e.textAlign="right"===this._data.horzAlign?"end":"center"),this._data.backgroundRoundRect?((0,f.drawRoundRect)(e,r.x,r.y,r.w,r.h,this._data.backgroundRoundRect),e.fillStyle=this._data.backgroundColor,e.fill(),e.globalAlpha=1):(e.fillStyle=this._data.backgroundColor,e.fillRect(r.x,r.y,r.w,r.h),e.globalAlpha=1),this._data.icons){let s=0;const o=Math.ceil((b-this._fontSize)/2),a=(0,n.ensureNotNull)(this._padding);for(const n of this._data.icons){const l=Math.round(r.x+a.left),d=Math.round(r.y+a.top+i*s-o);this._drawIcon(e,l,d,n,Boolean(this._data.isDark),t),s+=1}}}else"right"===this._data.horzAlign?e.textAlign="end":"center"===this._data.horzAlign&&(e.textAlign="center");const s=(0,n.ensureNotNull)(this._textPoint),o=s.x;let a=s.y;e.fillStyle=this._data.color;for(const t of this._lines)e.fillText(t,o,a),a+=i;return{width:r.w+2}}hitTest(e){return 0===this._data.points.length?null:(this._preRender(),this._boundingBox&&(0,_.pointInBox)(e,this._boundingBox)?this._hittest:null)}_preRender(){if(this._preRendered)return;const e=function(){if(null!==y)return y;const e=(0,T.createDisconnectedCanvas)(document,(0,u.size)({width:0,height:0}));return y=(0,
T.getPrescaledContext2D)(e),y}(),t=this._data.points[0].x;let i=t;const n=this._data.points[0].y;let s=n;const o=this._fontSize,a=this._lineSpacing,l=(o+a)*this._lines.length-a;e.textBaseline="top",e.font=this.fontStyle();const d=[];let h;if(this._data.wordWrapWidth){h=this._data.wordWrapWidth;for(let e=0;e<this._lines.length;e++)d.push(this._data.wordWrapWidth)}else{h=0;for(let t=0;t<this._lines.length;t++){const i=e.measureText(this._lines[t]).width;d.push(i),h=Math.max(h,i)}}const c={top:this._data.paddingTop,right:this._data.paddingRight,bottom:this._data.paddingBottom,left:this._data.paddingLeft},_={x:Math.floor(t),y:Math.floor(n),w:Math.ceil(h+c.left+c.right),h:Math.ceil(l+c.top+c.bottom)};if(i+=c.left,s+=c.top,this._data.icons){const e=void 0!==this._data.textPadding?this._data.textPadding:Math.round(o/2);i+=b+e,_.w+=b+e}if("bottom"===this._data.vertAlign||"middle"===this._data.vertAlign){const e="middle"===this._data.vertAlign?n-_.h/2:n-_.h-(_.y-n);s+=e-_.y,_.y=e}if("right"===this._data.horzAlign||"center"===this._data.horzAlign){const n="center"===this._data.horzAlign?t-_.w/2:t-_.w-(_.x-t);i+=n-_.x,_.x=n,!0!==this._data.doNotAlignText&&("right"===this._data.horzAlign?(e.textAlign="end",i+=h):(e.textAlign="center",i+=h/2))}_.w%2!=0&&_.w++,_.x+=.5,_.y+=.5,this._boundingBox=(0,r.box)(new r.Point(_.x,_.y),new r.Point(_.x+_.w,_.y+_.h)),this._rect=_,this._padding=c,this._textPoint={x:i,y:s},this._textSizeCache&&(this._textSizeCache.widths=d),this._preRendered=!0}_drawIcon(e,t,i,r,s,o){const{horizontalPixelRatio:a,verticalPixelRatio:l}=o,d=`${r}${this._data.isDark}${a}_${l}`;let h=P.get(d);if(!h){h=document.createElement("canvas"),h.width=b*a,h.height=b*l,h.style.width="18px",h.style.height="18px";const e=(0,n.ensureNotNull)(h.getContext("2d"));e.setTransform(1,0,0,1,0,0),p.isEdge||e.scale(a,l);const t=L.getIcon(r,s?"dark":"light");t.ready()&&(e.drawImage(t.image(),0,0),P.set(d,h))}e.drawImage(h,t-.5,i-.5,b,b)}}var M,C=i(15938),I=i(39429),A=i(31955);!function(e){e.offset=8,e.fontSize=12,e.lineSpacing=16,e.rectRadius=4,e.bgColorLight="rgba(227,242,253,0.9)",e.bgColorDark="rgba(67,70,81,0.9)",e.textColorLight="#2A2E39",e.textColorDark="#F8F9FD",e.textPadding=10,e.paddingTopBottom=13,e.paddingLeftRight=10}(M||(M={}));var D=M.fontSize,k=M.lineSpacing,N=M.paddingTopBottom;const B=(0,A.getLogger)("Chart.LineToolTrendLine");function H(e,t){return!(!e&&!t)&&(Boolean(e)!==Boolean(t)||(e.index!==t.index||e.price!==t.price))}function z(e,t){return!(!e&&!t)&&(Boolean(e)!==Boolean(t)||e.valueOf()!==t.valueOf())}class E{constructor(e){this._sourcesToRow=new Map,this._rowsToSources=new Map,this._currentWidth=400,this._actualCapacity=1,this._currentSymbol="",this._renderingInfo=e;const t=k,i=D+t;this._maxRowHeight=3*i-t+2*N+2,this._recreateCanvas()}destroy(){delete this._canvas,delete this._ctx}canvas(){return this._canvas}topByRow(e){return e*this._maxRowHeight}rowHeight(e){const t=(0,n.ensureDefined)(this._rowsToSources.get(e)),i=(0,n.ensureDefined)(this._sourcesToRow.get(t)).effectiveState
;return null!==i?i.realRowHeight:this._maxRowHeight}rowWidth(e){const t=(0,n.ensureDefined)(this._rowsToSources.get(e));return(0,n.ensureDefined)(this._sourcesToRow.get(t)).width}currentWidth(){return this._currentWidth}updateSource(e,t){const i=e.properties().symbol.value();this._currentSymbol!==i&&(B.logDebug("TrendLineCache. Clearing canvas because of changing symbol from "+this._currentSymbol+" to "+i),this._currentSymbol=i,this._sourcesToRow.clear(),this._rowsToSources.clear());const r=e.id();let s=this._sourcesToRow.get(r);if(void 0===s){const e=this._findEmptyRow(r);s={effectiveState:null,rowIndex:e,width:0},this._sourcesToRow.set(r,s),this._rowsToSources.set(e,r)}const o=s.effectiveState,a=this._effectiveState(e);if(!function(e,t){if(null!==e&&null===t)return!1;if(null===e&&null!==t)return!1;const i=(0,n.ensureNotNull)(e),r=(0,n.ensureNotNull)(t);if(H(i.p1,r.p1))return!1;if(H(i.p2,r.p2))return!1;if(i.dark!==r.dark||i.showBars!==r.showBars||i.showTimeRange!==r.showTimeRange||i.showDistance!==r.showDistance||i.showPriceRange!==r.showPriceRange||i.showPercentPriceRange!==r.showPercentPriceRange||i.showPipsPriceRange!==r.showPipsPriceRange||i.showAngle!==r.showAngle)return!1;if(i.showAngle||i.showDistance){if(i.priceRange.min!==r.priceRange.min)return!1;if(i.priceRange.max!==r.priceRange.max)return!1;if(i.barSpacing!==r.barSpacing)return!1}return!z(i.leftUserTime,r.leftUserTime)&&!z(i.rightUserTime,r.rightUserTime)}(o,a)){const e=t();this._repaintSource(r,s.rowIndex,e),s.effectiveState=a}return s}_findEmptyRow(e){let t=0;for(;void 0!==this._rowsToSources.get(t);)t++;return this._rowsToSources.set(t,e),t>=this._actualCapacity&&(this._actualCapacity++,this._recreateCanvas()),t}_effectiveState(e){var t,i;const r=e.properties(),s=r.showBarsRange&&r.showBarsRange.value(),o=r.showDateTimeRange&&r.showDateTimeRange.value(),a=r.showDistance&&r.showDistance.value(),l=r.showPriceRange&&r.showPriceRange.value(),d=null===(t=r.showPercentPriceRange)||void 0===t?void 0:t.value(),h=null===(i=r.showPipsPriceRange)||void 0===i?void 0:i.value(),c=r.showAngle&&r.showAngle.value();let u=0;(s||o||a)&&u++,c&&u++,(l||d||h)&&u++;const _=(D+k)*u-k+2*N+2,p=e.points()[0],g=e.points()[1],x=e.model();return{p1:Object.assign({},p),p2:Object.assign({},g),leftUserTime:p?x.timeScale().indexToUserTime(p.index):null,rightUserTime:g?x.timeScale().indexToUserTime(g.index):null,props:e.properties(),showBars:s,showTimeRange:o,showDistance:a,showPriceRange:l,showPipsPriceRange:h,showPercentPriceRange:d,showAngle:c,dark:e.model().dark().value(),priceRange:(0,n.ensureNotNull)((0,n.ensureNotNull)(e.priceScale()).priceRange()).state(),barSpacing:e.model().timeScale().barSpacing(),realRowHeight:_}}_repaintSource(e,t,i){i.points[0]=new r.Point(0,0),delete i.horzAlign,delete i.vertAlign;const{horizontalPixelRatio:s,verticalPixelRatio:o}=this._renderingInfo;(0,T.drawScaled)(this._ctx,s,o,(()=>{this._ctx.translate(.5,this.topByRow(t)+.5),this._ctx.clearRect(0,0,this._currentWidth,this._maxRowHeight);const r=new S(i,{widths:[]
}).draw(this._ctx,this._renderingInfo);(0,n.ensureDefined)(this._sourcesToRow.get(e)).width=r.width}))}_recreateCanvas(){this._canvas=(0,n.ensureNotNull)(document.createElement("canvas"));const{horizontalPixelRatio:e,verticalPixelRatio:t}=this._renderingInfo;this._canvas.width=this._currentWidth*e,this._canvas.height=this._maxRowHeight*this._actualCapacity*t,this._ctx=(0,n.ensureNotNull)(this._canvas.getContext("2d")),this._ctx.font=`${D}px ${C.CHART_FONT_FAMILY}`,this._sourcesToRow.clear(),this._rowsToSources.clear()}}var W,O,V,F=i(32211);!function(e){e[e.Offset=8]="Offset",e[e.FontSize=12]="FontSize",e[e.LineSpacing=16]="LineSpacing",e[e.RectRadius=4]="RectRadius",e[e.TextPadding=10]="TextPadding",e[e.PaddingTopBottom=13]="PaddingTopBottom",e[e.PaddingLeftRight=10]="PaddingLeftRight"}(W||(W={})),function(e){e.Background="rgba(227,242,253,0.9)",e.Text="#2A2E39"}(O||(O={})),function(e){e.Background="rgba(67,70,81,0.9)",e.Text="#F8F9FD"}(V||(V={}));class Y extends F.InplaceTextLineSourcePaneView{constructor(e,t,i,n,r){super(e,t,i,n,r),this._renderer=new l.CompositeRenderer,this._labelData=null,this._label=null,this._cache=null,this._renderingInfo=null,this._cacheInvalidated=!0,this._statCache=null,this._iconsReady=!1,L.onAllIconsReady().subscribe(this,(()=>{this._cache&&(this._cache.destroy(),this._cache=null),this._iconsReady=!0,t.lightUpdate()}))}destroy(){this._cache&&(this._cache.destroy(),this._cache=null),L.onAllIconsReady().unsubscribeAll(this),super.destroy()}iconsReady(){return this._iconsReady}update(){super.update(),this._cacheInvalidated=!0}getCacheCanvas(e){return this._createCacheIfRequired(e).canvas()}getCacheRects(e,t){const i=this._createCacheIfRequired(e),o=(0,n.ensureNotNull)(this._statCache),a=this._source.properties().childs().statsPosition.value(),l=this._getPointsForStats(),d={left:0,top:i.topByRow(o.rowIndex),width:i.rowWidth(o.rowIndex),height:i.rowHeight(o.rowIndex)},h=a===I.StatsPosition.Auto?I.StatsPosition.Center:a;let c=l[h].x+10,u=l[h].y;const _=this._points[1].y<this._points[0].y&&this._points[1].x<this._points[0].x||this._points[1].y>this._points[0].y&&this._points[1].x>this._points[0].x;_?u-=10+d.height:u+=10;const{mediaSize:p}=e;a!==I.StatsPosition.Auto||(0,r.equalPoints)(l[I.StatsPosition.Left],l[I.StatsPosition.Right])||(c<0?c=0:c+d.width>p.width&&(c=p.width-d.width),u<0?u=0:u+d.height>p.height&&(u=p.height-d.height),(0,s.intersectLineSegmentAndBox)((0,r.lineSegment)(l[I.StatsPosition.Left],l[I.StatsPosition.Right]),(0,r.box)((0,r.point)(c,u),(0,r.point)(c+d.width,u+d.height)))&&(u=_?l[h].y+10:l[h].y-10-d.height,c=Math.min(l[I.StatsPosition.Center].x,p.width)-d.width));return{cacheRect:d,targetRect:{left:Math.floor(c),top:Math.floor(u),width:d.width,height:d.height}}}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_createCacheIfRequired(e){return null!==this._cache&&null!==this._renderingInfo&&(0,c.equalRenderingInfos)(e,this._renderingInfo)||(this._cache&&this._cache.destroy(),this._cache=new E(e),
this._statCache=this._cache.updateSource(this._source,(()=>this._statLabelData())),this._renderingInfo=e,this._cacheInvalidated=!1),this._cacheInvalidated&&(this._statCache=this._cache.updateSource(this._source,(()=>this._statLabelData())),this._cacheInvalidated=!1),this._cache}_updateImpl(e){super._updateImpl(e)}_priceRange(){var e,t;const[i,r]=this._source.points(),s=this._source.properties().childs(),o=s.showPriceRange.value(),a=s.showPercentPriceRange.value(),l=s.showPipsPriceRange.value(),h=(0,n.ensureNotNull)(this._source.ownerSource());let c;if(this._source.priceScale()&&(o||a||l)){const n=[],s=r.price-i.price;if(o||a){const l=s/Math.abs(i.price),c=[];if(o){const n=h.formatter(),o=null!==(t=null===(e=n.formatChange)||void 0===e?void 0:e.call(n,r.price,i.price))&&void 0!==t?t:n.format(s);c.push(o)}if(a){const e=(0,d.getPercentageFormatter)().format(100*l);c.push(o?`(${e})`:e)}n.push(c.join(" "))}const u=this._model.mainSeries().symbolInfo(),_=u&&(0,d.getPipFormatter)(u);l&&_&&n.push(_.format(s)),c=n.join(", ")}return c}_statLabelData(){const[e,t]=this._source.points(),r=this._source.properties().childs(),s=[];let l,c,u,_,p;const g=this._priceRange();void 0!==g&&s.push("priceRange");const x=r.showBarsRange.value(),f=r.showDateTimeRange&&r.showDateTimeRange.value(),v=r.showDistance&&r.showDistance.value(),T=r.showAngle&&r.showAngle.value();if(T||v){const i=(0,n.ensureNotNull)(this._source.pointToScreenPoint(e));_=(0,n.ensureNotNull)(this._source.pointToScreenPoint(t)).subtract(i),p=Math.round(1e5*_.length())/1e5}if(x||f||v){if(l="",x&&(u=t.index-e.index,l+=o.t(null,void 0,i(41643)).format({count:(0,a.forceLTRStr)(String(u))})),f){const i=this._model.timeScale().indexToUserTime(e.index),n=this._model.timeScale().indexToUserTime(t.index);if(i&&n){const e=(n.valueOf()-i.valueOf())/1e3,t=(0,a.startWithLTR)((new h.TimeSpanFormatter).format(e));t&&(l+=x?" ("+t+")":t)}}v&&(l&&(l+=", "),l+=o.t(null,void 0,i(44994)).format({number:(0,a.forceLTRStr)((0,d.getNumericFormatter)().format(Math.round(Number(p))))})),l&&s.push("barsRange")}if(T){let e;void 0!==p&&p>0&&void 0!==_&&(_=_.normalized(),e=Math.acos(_.x),_.y>0&&(e=-e)),"number"!=typeof e||isNaN(e)||(c=Math.round(180*e/Math.PI)+"º",s.push("angle"))}this._label=[(0,a.forceLTRStr)(g),l,c].filter((e=>null!=e)).join("\n")||null;const w=this._model.dark().value(),R=w?"rgba(67,70,81,0.9)":"rgba(227,242,253,0.9)",m=w?"#F8F9FD":"#2A2E39",y={points:[this._points[1]],text:this._label,color:m,isDark:w,font:C.CHART_FONT_FAMILY,fontSize:12,lineSpacing:16,backgroundColor:R,backgroundRoundRect:4,paddingLeft:10,paddingRight:10,paddingTop:13,paddingBottom:13,textPadding:10,doNotAlignText:!0,icons:s,bold:!1,italic:!1,lines:[],wordWrapWidth:0};return this._points[1].y<this._points[0].y&&(y.vertAlign="bottom"),this._points[1].x<this._points[0].x&&(y.horzAlign="right"),this._labelData=y,y}}},41123:(e,t,i)=>{"use strict";i.r(t),i.d(t,{LineToolTrianglePatternPaneView:()=>_});var n=i(50151),r=i(86441),s=i(51056),o=i(95201),a=i(91046),l=i(1161),d=i(17330),h=i(49857),c=i(27916),u=i(15938)
;class _ extends c.LineSourcePaneView{constructor(){super(...arguments),this._trendLineRendererPoints01=new a.TrendLineRenderer,this._trendLineRendererPoints12=new a.TrendLineRenderer,this._trendLineRendererPoints23=new a.TrendLineRenderer,this._intersectionRenderer=new l.TriangleRenderer,this._aLabelRenderer=new d.TextRenderer,this._bLabelRenderer=new d.TextRenderer,this._cLabelRenderer=new d.TextRenderer,this._dLabelRenderer=new d.TextRenderer,this._renderer=null}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){super._updateImpl(e),this._renderer=null;const[t,i,a,l]=this._points;let d,c,_;if(4===this._points.length){if(Math.abs(a.x-t.x)<1||Math.abs(l.x-i.x)<1)return;let e=Math.min(t.x,i.x);e=Math.min(e,a.x),e=Math.min(e,l.x);const n=(a.y-t.y)/(a.x-t.x),s=t.y+(e-t.x)*n,o=(l.y-i.y)/(l.x-i.x),h=i.y+(e-i.x)*o;if(Math.abs(n-o)<1e-6)return;c=new r.Point(e,s),_=new r.Point(e,h);const u=(i.y-t.y+(t.x*n-i.x*o))/(n-o);if(u<e){let e=Math.max(t.x,i.x);e=Math.max(e,a.x),e=Math.max(e,l.x),c=new r.Point(e,t.y+(e-t.x)*n),_=new r.Point(e,i.y+(e-i.x)*o)}const p=t.y+(u-t.x)*n;d=new r.Point(u,p)}if(this._points.length<2)return;const p=this._source.properties().childs(),g=new o.CompositeRenderer,x=(e,t)=>({points:[e],text:t,color:p.textcolor.value(),vertAlign:"middle",horzAlign:"center",font:u.CHART_FONT_FAMILY,offsetX:0,offsetY:0,bold:p.bold&&p.bold.value(),italic:p.italic&&p.italic.value(),fontsize:p.fontsize.value(),backgroundColor:p.color.value(),backgroundRoundRect:4}),f=(e,t)=>({points:[e,t],color:p.color.value(),linewidth:p.linewidth.value(),linestyle:s.LINESTYLE_SOLID,extendleft:!1,extendright:!1,leftend:h.LineEnd.Normal,rightend:h.LineEnd.Normal});if(this._trendLineRendererPoints01.setData(f(t,i)),g.append(this._trendLineRendererPoints01),this._points.length>=3&&(this._trendLineRendererPoints12.setData(f(i,a)),g.append(this._trendLineRendererPoints12)),4===this._points.length&&(this._trendLineRendererPoints23.setData(f(a,l)),g.append(this._trendLineRendererPoints23),d)){const e={points:[(0,n.ensureDefined)(c),(0,n.ensureDefined)(_),d],color:p.color.value(),linewidth:p.linewidth.value(),backcolor:p.backgroundColor.value(),fillBackground:p.fillBackground.value(),transparency:p.transparency.value(),linestyle:s.LINESTYLE_DOTTED};this._intersectionRenderer.setData(e),g.append(this._intersectionRenderer)}const v=x(t,"A");i.y>t.y?(v.vertAlign="bottom",v.offsetY=5):(v.vertAlign="top",v.offsetY=5),this._aLabelRenderer.setData(v),g.append(this._aLabelRenderer);const T=x(i,"B");if(i.y<t.y?(T.vertAlign="bottom",T.offsetY=5):(T.vertAlign="top",T.offsetY=5),this._bLabelRenderer.setData(T),g.append(this._bLabelRenderer),this._points.length>2){const e=x(a,"C");a.y<i.y?(e.vertAlign="bottom",e.offsetY=5):(e.vertAlign="top",e.offsetY=5),this._cLabelRenderer.setData(e),g.append(this._cLabelRenderer)}if(this._points.length>3){const e=x(l,"D");l.y<a.y?(e.vertAlign="bottom",e.offsetY=5):(e.vertAlign="top",e.offsetY=5),this._dLabelRenderer.setData(e),g.append(this._dLabelRenderer)}this.addAnchors(g),
this._renderer=g}}},86448:(e,t,i)=>{"use strict";var n=i(27916).LineSourcePaneView,r=i(95201).CompositeRenderer,s=i(1161).TriangleRenderer;t.TrianglePaneView=class extends n{constructor(e,t){super(e,t),this._triangleRenderer=new s,this._renderer=null}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_updateImpl(e){super._updateImpl(e),this._renderer=null;var t={};t.points=this._points,t.color=this._source.properties().color.value(),t.linewidth=this._source.properties().linewidth.value(),t.backcolor=this._source.properties().backgroundColor.value(),t.fillBackground=this._source.properties().fillBackground.value(),t.transparency=this._source.properties().transparency.value(),this._triangleRenderer.setData(t);var i=new r;i.append(this._triangleRenderer),this.addAnchors(i),this._renderer=i}}},63307:(e,t,i)=>{"use strict";i.r(t),i.d(t,{VertLinePaneView:()=>p});var n=i(86441),r=i(95201),s=i(17330),o=i(72791),a=i(95173),l=i(36036),d=i(13075),h=i(15938),c=i(56468),u=i(32211),_=i(62689);class p extends u.InplaceTextLineSourcePaneView{constructor(e,t,i,n,r,s){super(e,t,n,r,s),this._lineRenderer=new a.VerticalLineRenderer,this._renderer=null,this._pane=i,this._labelRenderer=new _.LineToolTextRenderer(void 0,new c.HitTestResult(c.HitTarget.MovePoint,(0,u.inplaceEditHandlers)(this._tryActivateEditMode.bind(this,null))))}additionalCursorData(){var e;const t=this._labelRenderer.getTextInfo();return{color:this._source.editableTextStyle().cursorColor,lineSpacing:t.lineSpacing,lineHeight:t.lineHeight,rotationPoint:null!==(e=this._labelRenderer.rotation())&&void 0!==e?e:void 0}}positionToCoordinate(e){return this._labelRenderer.positionToCoordinate(e)}renderer(e){return this._invalidated&&this._updateImpl(e),this._renderer}_validatePriceScale(){return!0}_updateImpl(e){var t,i;if(super._updateImpl(e),this._renderer=null,0===this._points.length)return;const{mediaSize:{width:a,height:u}}=e,_=this._source.properties().childs(),p=new r.CompositeRenderer,g=_.text.value(),x=this._isTextEditMode(),f=this._placeHolderMode();let v,T=!0,w=!1;if(1===this._points.length){const e=new n.Point(this._points[0].x,u/2);this._addAlertRenderer(p,[e])}if(this._source.model().paneForSource(this._source)===this._pane&&((null===(t=_.showLabel)||void 0===t?void 0:t.value())&&g||f||x)){let e=0,t=5,r="center",o="middle";const l=this._points[0].x;let c=0;switch(_.vertLabelsAlign.value()){case"top":c=u;break;case"middle":c=u/2,w=!0;break;case"bottom":c=0}if("horizontal"===_.textOrientation.value()){switch(_.horzLabelsAlign.value()){case"left":r="right";break;case"right":r="left";break;case"center":r="center"}switch(_.vertLabelsAlign.value()){case"top":o="bottom";break;case"middle":o="middle";break;case"bottom":o="top"}}else{switch(e=-Math.PI/2,t=0,_.horzLabelsAlign.value()){case"left":o="bottom";break;case"right":o="top";break;case"center":o="middle"}switch(_.vertLabelsAlign.value()){case"top":r="left";break;case"middle":r="center";break;case"bottom":r="right"}}this._labelRenderer.setData({points:[new n.Point(l,c)],text:this._textData(),
color:this._textColor(),vertAlign:o,horzAlign:r,font:h.CHART_FONT_FAMILY,offsetX:t,offsetY:0,bold:_.bold.value(),italic:_.italic.value(),fontsize:_.fontsize.value(),forceTextAlign:!0,angle:e,decorator:f?d.PlusTextRendererDecorator.instance():void 0,...this._inplaceTextHighlight()}),this._labelRenderer.setCursorType(this._textCursorType()),p.append(this._labelRenderer),this._needLabelExclusionPath(this._labelRenderer)&&(v=null!==(i=(0,s.getTextBoundaries)(this._labelRenderer,a,u))&&void 0!==i?i:void 0),T=this._labelRenderer.isOutOfScreen(a,u),T?this.closeTextEditor():this._updateInplaceText(this._labelRenderer.getTextInfo())}const R={x:this._points[0].x,color:_.linecolor.value(),linewidth:_.linewidth.value(),linestyle:_.linestyle.value(),excludeBoundaries:v},m=R.linewidth/2+1;if(T=T&&(R.x<-m||R.x>a+m),this._lineRenderer.setData(R),this._lineRenderer.setHitTest(new c.HitTestResult(c.HitTarget.MovePoint,{snappingIndex:this._source.points()[0].index})),p.insert(this._lineRenderer,0),!T){if(1===this._points.length&&!this._isTextEditMode()){const e=[(0,l.anchor)({x:this._points[0].x,y:w?.9*u:u/2,pointIndex:0,square:!0,snappingIndex:this._source.points()[0].index,cursorType:o.PaneCursorType.HorizontalResize})];p.append(this.createLineAnchor({points:e},0))}this._renderer=p}}_needLabelExclusionPath(e){const t=this._source.properties().childs(),i="horizontal"===t.textOrientation.value(),n=t.text.value();if(i)return""!==n.trim();if("center"!==t.horzLabelsAlign.value())return!1;const r=e.getLinesInfo().lines;if(r.length%2==0)return!1;if(""===r[Math.floor(r.length/2)].text.trim())return!1;return!0}}},28081:(e,t,i)=>{"use strict";i.d(t,{PaneRendererCachedImage:()=>o});var n=i(86441),r=i(34026),s=i(56468);class o{constructor(e,t){this._cacheRect=null,this._targetRect=null,this._cacheProvider=e,this._index=t}draw(e,t){const i=this._cacheProvider.getCacheRects(t,this._index);if(null===i)return this._cacheRect=null,void(this._targetRect=null);if(this._cacheRect=i.cacheRect,this._targetRect=i.targetRect,0===this._cacheRect.width||0===this._cacheRect.height||0===this._targetRect.width||0===this._targetRect.height)return;e.save(),e.setTransform(1,0,0,1,0,0);const{horizontalPixelRatio:n,verticalPixelRatio:r}=t,s=this._cacheProvider.getCacheCanvas(t);e.drawImage(s,Math.round(this._cacheRect.left*n),Math.round(this._cacheRect.top*r),this._cacheRect.width*n,this._cacheRect.height*r,Math.round(this._targetRect.left*n),Math.round(this._targetRect.top*r),this._targetRect.width*n,this._targetRect.height*r),e.restore()}hitTest(e){if(null===this._targetRect)return null;const t=new n.Point(this._targetRect.left,this._targetRect.top),i=t.add(new n.Point(this._targetRect.width,this._targetRect.height));return(0,r.pointInBox)(e,(0,n.box)(t,i))?new s.HitTestResult(s.HitTarget.Regular):null}}},55053:(e,t,i)=>{"use strict";i.d(t,{ArcWedgeRenderer:()=>a});var n,r=i(56468),s=i(19063),o=i(75919);!function(e){e[e.HitTestTolerance=4]="HitTestTolerance"}(n||(n={}));class a extends o.MediaCoordinatesPaneRenderer{constructor(){super(...arguments),
this._data=null,this._hitTest=new r.HitTestResult(r.HitTarget.MovePoint),this._backHitTest=new r.HitTestResult(r.HitTarget.MovePointBackground)}setData(e){this._data=e}setHitTest(e){this._hitTest=e}hitTest(e){if(null===this._data)return null;const t=e.subtract(this._data.center),i=t.length();if(Math.abs(i-this._data.radius)<=4){const t=e.subtract(this._data.p1).length(),i=e.subtract(this._data.p2).length();if(Math.max(t,i)<=this._data.p1.subtract(this._data.p2).length())return this._hitTest}if(this._data.fillBackground&&i<=this._data.radius){const e=this._data.p1.subtract(this._data.center).normalized(),i=this._data.p2.subtract(this._data.center).normalized(),n=t.normalized(),r=e.dotProduct(i),s=n.dotProduct(e),o=n.dotProduct(i);if(s>=r&&o>=r)return this._backHitTest}return null}_drawImpl(e){if(null===this._data)return;const t=e.context;if(t.strokeStyle=this._data.color,t.lineWidth=this._data.linewidth,t.beginPath(),t.arc(this._data.center.x,this._data.center.y,this._data.radius,this._data.angle1,this._data.angle2),t.stroke(),this._data.fillBackground){if(t.arc(this._data.center.x,this._data.center.y,this._data.prevRadius,this._data.angle2,this._data.angle1,!0),this._data.gradient){const e=t.createRadialGradient(this._data.center.x,this._data.center.y,this._data.prevRadius,this._data.center.x,this._data.center.y,this._data.radius);e.addColorStop(0,(0,s.generateColor)(this._data.color1,this._data.transparency)),e.addColorStop(1,(0,s.generateColor)(this._data.color2,this._data.transparency)),t.fillStyle=e}else t.fillStyle=(0,s.generateColor)(this._data.color,this._data.transparency,!0);t.fill()}}}},66825:(e,t,i)=>{"use strict";i.d(t,{cubicBezierHitTest:()=>l,extendQuadroBezier:()=>d,quadroBezierHitTest:()=>o});var n,r=i(4652),s=i(9859);function o(e,t,i,n,s){const o=i.subtract(e).length()+i.subtract(t).length(),a=Math.max(3/o,.02);let l;for(let o=0;;o+=a){o>1&&(o=1);const a=e.scaled((1-o)*(1-o)),d=i.scaled(2*o*(1-o)),h=t.scaled(o*o),c=a.add(d).add(h);if(void 0!==l){if((0,r.distanceToSegment)(c,l,n).distance<s)return!0}else if(c.subtract(n).length()<s)return!0;if(l=c,1===o)break}return!1}function a(e,t,i,n,r){r=(0,s.clamp)(r,0,1);const o=e.scaled((1-r)*(1-r)*(1-r)),a=t.scaled(3*(1-r)*(1-r)*r),l=i.scaled(3*(1-r)*r*r),d=n.scaled(r*r*r);return o.add(a).add(l).add(d)}function l(e,t,i,n,s,o){const l=t.subtract(e).length()+i.subtract(t).length()+n.subtract(i).length(),d=Math.max(3/l,.02);let h;for(let l=0;;l+=d){const d=a(e,t,i,n,l);if(void 0!==h){if((0,r.distanceToSegment)(d,h,s).distance<o)return!0}else if(d.subtract(s).length()<o)return!0;if(h=d,l>=1)break}return!1}function d(e,t,i,n,r){const s=i.subtract(e).length()+i.subtract(t).length();if(!s)return[];const o=function(e,t,i,n,r){const s=[],o=h(e.y,t.y,i.y,0).concat(h(e.y,t.y,i.y,r));for(let r=0;r<o.length;r++){const a=c(e.x,t.x,i.x,o[r]);a>=0&&a<=n&&s.push(o[r])}const a=h(e.x,t.x,i.x,0).concat(h(e.x,t.x,i.x,n));for(let n=0;n<a.length;n++){const o=c(e.y,t.y,i.y,a[n]);o>=0&&o<=r&&s.push(a[n])}return s}(e,t,i,n,r).filter((e=>e>1)).sort(((e,t)=>e-t))
;t.x>=0&&t.x<=n&&t.y>=0&&t.y<=r&&o.unshift(1);const a=3/s,l=[];for(let n=0;n<o.length-1;n+=2){let r=a,s=o[n],d=o[n+1]+r;const h=[];for(;s<=d;){const n=e.scaled((1-s)*(1-s)),o=i.scaled(2*s*(1-s)),a=t.scaled(s*s),l=n.add(o).add(a);if(h.length>0){h[h.length-1].subtract(l).length()<2&&(d+=r,r*=2)}h.push(l),s+=r}h.length>0&&l.push(h)}return l}function h(e,t,i,n){const r=[],s=e-2*i+t,o=2*i-2*e,a=e-n;if(Math.abs(s)>1e-8){const e=o*o-4*s*a;e>=0&&(r.push((-o+Math.sqrt(e))/(2*s)),r.push((-o-Math.sqrt(e))/(2*s)))}else r.push(-a/o);return r}function c(e,t,i,n){return(1-n)*(1-n)*e+2*(1-n)*n*i+n*n*t}!function(e){e[e.MaxHitTestSegments=50]="MaxHitTestSegments"}(n||(n={}))},65395:(e,t,i)=>{"use strict";i.d(t,{ChannelRenderer:()=>c});var n=i(50151),r=i(86441),s=i(34026),o=i(4652),a=i(56468),l=i(37743),d=i(19063),h=i(75919);class c extends h.MediaCoordinatesPaneRenderer{constructor(){super(...arguments),this._data=null}setData(e){this._data=e}hitTest(e,t){if(null===this._data||!this._data.hittestOnBackground)return null;const i=this._visiblePolygon(t.mediaSize);return null!==i&&(0,s.pointInPolygon)(e,i)?new a.HitTestResult(a.HitTarget.MovePointBackground):null}_drawImpl(e){if(null===this._data)return;const t=e.context,i=this._visiblePolygon(e.mediaSize);if(null!==i){t.beginPath(),t.moveTo(i[0].x,i[0].y);for(let e=1;e<i.length;e++)t.lineTo(i[e].x,i[e].y);t.fillStyle=(0,d.generateColor)(this._data.color,this._data.transparency,!0),t.fill()}}_visiblePolygon(e){const t=(0,n.ensureNotNull)(this._data),i=t.p1,s=t.p2,a=t.p3,d=t.p4;if((0,r.equalPoints)(i,s)||(0,r.equalPoints)(a,d)||(0,o.distanceToLine)(i,s,a).distance<1e-6&&(0,o.distanceToLine)(i,s,d).distance<1e-6)return null;if(e.width<=0||e.height<=0)return null;let h=[new r.Point(0,0),new r.Point(e.width,0),new r.Point(e.width,e.height),new r.Point(0,e.height)];return h=(0,l.clipPolygonByEdge)(h,i,s,[d,a]),h=(0,l.clipPolygonByEdge)(h,d,a,[i,s]),(0,r.equalPoints)(a,i)||t.extendLeft||(h=(0,l.clipPolygonByEdge)(h,a,i,[s,d])),h}}},62317:(e,t,i)=>{"use strict";i.d(t,{DisjointChannelRenderer:()=>p});var n=i(50151),r=i(86441),s=i(34026),o=i(4652),a=i(5531),l=i(51056),d=i(56468),h=i(90241),c=i(37743),u=i(19063),_=i(75919);class p{constructor(){this._parallelChannelRenderer=new h.ParallelChannelRenderer,this._disjointChannelIntersectionRenderer=new g,this._selectedRenderer=this._disjointChannelIntersectionRenderer}setData(e){if(e.points.length<4)return;const[t,i,n,s]=e.points;if((0,r.equalPoints)(t,i)||(0,r.equalPoints)(n,s)||(0,o.distanceToLine)(t,i,n).distance<1e-6&&(0,o.distanceToLine)(t,i,s).distance<1e-6)this._selectedRenderer=null;else{null!==(0,a.intersectLines)((0,r.lineThroughPoints)(t,i),(0,r.lineThroughPoints)(n,s))?(this._disjointChannelIntersectionRenderer.setData(e),this._selectedRenderer=this._disjointChannelIntersectionRenderer):(this._parallelChannelRenderer.setData({line1:{color:"rgba(0,0,0,0)",lineStyle:l.LINESTYLE_SOLID,lineWidth:0,points:[t,i]},line2:{color:"rgba(0,0,0,0)",lineStyle:l.LINESTYLE_SOLID,lineWidth:0,points:[s,n]},extendLeft:e.extendleft,extendRight:e.extendright,
skipLines:!0,fillBackground:!0,backColor:(0,u.generateColor)(e.backcolor,e.transparency),hittestOnBackground:e.hittestOnBackground}),this._selectedRenderer=this._parallelChannelRenderer)}}hitTest(e,t){return null!==this._selectedRenderer?this._selectedRenderer.hitTest(e,t):null}draw(e,t){null!==this._selectedRenderer&&this._selectedRenderer.draw(e,t)}}class g extends _.MediaCoordinatesPaneRenderer{constructor(){super(...arguments),this._data=null}setData(e){this._data=e}hitTest(e,t){if(null===this._data||!this._data.hittestOnBackground)return null;for(const i of this._visiblePolygons(t.mediaSize))if((0,s.pointInPolygon)(e,i))return new d.HitTestResult(d.HitTarget.MovePointBackground);return null}_drawImpl(e){if(null===this._data||this._data.points.length<4)return;const t=e.context;t.fillStyle=(0,u.generateColor)(this._data.backcolor,this._data.transparency);for(const i of this._visiblePolygons(e.mediaSize)){t.beginPath(),t.moveTo(i[0].x,i[0].y);for(let e=1;e<i.length;e++)t.lineTo(i[e].x,i[e].y);t.fill()}}_visiblePolygons(e){const t=(0,n.ensureNotNull)(this._data),[i,s,o,l]=t.points;if(e.width<=0||e.height<=0)return[];const d=(0,a.intersectLines)((0,r.lineThroughPoints)(i,s),(0,r.lineThroughPoints)(o,l));if(null===d)return[];const h=[new r.Point(0,0),new r.Point(e.width,0),new r.Point(e.width,e.height),new r.Point(0,e.height)],u=[];{let e=h;const n=i.subtract(s).add(d),r=l.subtract(o).add(d);e=(0,c.clipPolygonByEdge)(e,d,n,[r,r]),e=f(e,t),e=(0,c.clipPolygonByEdge)(e,r,d,[n,n]),null!==e&&u.push(e)}{let e=h;const n=s.subtract(i).add(d),r=o.subtract(l).add(d);e=(0,c.clipPolygonByEdge)(e,d,n,[r,r]),e=f(e,t),e=(0,c.clipPolygonByEdge)(e,r,d,[n,n]),null!==e&&u.push(e)}return u}}function x(e,t,i){return null!==e?(0,a.intersectPolygonAndHalfplane)(e,(0,r.halfplaneThroughPoint)((n=t,(0,r.line)(1,0,-n)),new r.Point(i,0))):null;var n}function f(e,t){const[i,n]=t.points;return t.extendleft||(e=x(e,i.x,n.x)),t.extendright||(e=x(e,n.x,i.x)),e}},36155:(e,t,i)=>{"use strict";i.d(t,{EllipseRendererSimple:()=>h});var n,r=i(56468),s=i(9859),o=i(86441),a=i(19063),l=i(75919),d=i(37743);!function(e){e[e.HitTestTolerance=3]="HitTestTolerance"}(n||(n={}));class h extends l.MediaCoordinatesPaneRenderer{constructor(e,t,i){super(),this._data=e,this._hitTest=t||new r.HitTestResult(r.HitTarget.MovePoint),this._backgroundHitTest=i||new r.HitTestResult(r.HitTarget.MovePointBackground)}hitTest(e){if(this._data.points.length<2)return null;const t=this._data.points[0],i=this._data.points[1],n=.5*Math.abs(t.x-i.x),r=Math.abs(t.x-i.x),a=Math.abs(t.y-i.y),l=t.add(i).scaled(.5);let d=e.subtract(l);if(r<1||a<1)return null;const h=(i.y-t.y)/(i.x-t.x);d=new o.Point(d.x,d.y/h);let c=d.x*d.x+d.y*d.y-n*n;return c=(0,s.sign)(c)*Math.sqrt(Math.abs(c/n)),Math.abs(c)<3?this._hitTest:this._data.fillBackground&&!this._data.noHitTestOnBackground&&c<3?this._backgroundHitTest:null}_drawImpl(e){const t=e.context;t.lineCap="butt",t.strokeStyle=this._data.color,t.lineWidth=this._data.linewidth,void 0!==this._data.linestyle&&(0,d.setLineStyle)(t,this._data.linestyle)
;const i=this._data.points[0],n=this._data.points[1],r=Math.abs(i.x-n.x),s=Math.abs(i.y-n.y),o=i.add(n).scaled(.5);if(r<1||s<1)return;let l=0;if(this._data.wholePoints){const e=this._data.wholePoints[0],t=this._data.wholePoints[1];l=Math.abs(e.x-t.x)}t.save(),t.translate(o.x,o.y),t.scale(1,s/r),t.beginPath(),t.arc(0,0,r/2,0,2*Math.PI,!1),t.restore(),t.stroke(),this._data.fillBackground&&(this._data.wholePoints&&(t.translate(o.x,o.y),t.scale(1,s/r),t.arc(0,0,l/2,0,2*Math.PI,!0)),t.fillStyle=(0,a.generateColor)(this._data.backcolor,this._data.transparency,!0),t.fill())}}},48633:(e,t,i)=>{"use strict";i.d(t,{intersectLineWithViewport:()=>o});var n=i(86441);function r(e,t,i){return e>=t&&e<=i?e:null}function s(e,t,i,n){return Math.sign(e.x-t.x)===Math.sign(i.x-n.x)&&Math.sign(e.y-t.y)===Math.sign(i.y-n.y)}function o(e,t,i,o,a,l,d){const h=e.x>=0&&e.x<=a&&e.y>=0&&e.y<=l,c=t.x>=0&&t.x<=a&&t.y>=0&&t.y<=l;if(h&&c&&!i&&!o)return[e,t];if(e.x<0&&t.x<0&&(e.x<t.x?!o:!i)||e.x>a&&t.x>a&&(e.x<t.x?!i:!o)||e.y<0&&t.y<0&&(e.y<t.y?!o:!i)||e.y>l&&t.y>l&&(e.y<t.y?!i:!o))return null;const u=[];if(e.x===t.x){if(e.x<0||e.x>a)return null;e.y<t.y?u.push(new n.Point(e.x,0===d?0:e.y<0?e.y%d:-(d-e.y%d)),new n.Point(t.x,l)):u.push(new n.Point(e.x,0===d?l:e.y>l?l+(e.y-l)%d:l+(d-(l-e.y)%d)),new n.Point(t.x,0))}else if(e.y===t.y){if(e.y<0||e.y>l)return null;e.x<t.x?u.push(new n.Point(0===d?0:e.x<0?e.x%d:-(d-e.x%d),e.y),new n.Point(a,t.y)):u.push(new n.Point(0===d?a:e.x>a?a+(e.x-a)%d:a+(d-(a-e.x)%d),e.y),new n.Point(0,t.y))}else{const s=(t.y-e.y)/(t.x-e.x),o=e.y-s*e.x;let h=0,c=0;const _=r(o,0,l);if(null!==_)if(d>0&&(e.x<=0||i&&e.x<t.x)){const t=e.x<=0?Math.sqrt(Math.pow(0-e.x,2)+Math.pow(_-e.y,2))%d:d-Math.sqrt(Math.pow(0-e.x,2)+Math.pow(_-e.y,2))%d;h=Math.cos(Math.atan(s))*t,c=s*h,u.push(new n.Point(-h,_-c))}else u.push(new n.Point(0,_));const p=r(s*a+o,0,l);if(null!==p)if(d>0&&(e.x>=a||i&&e.x>t.x)){const t=e.x>=a?Math.sqrt(Math.pow(e.x-a,2)+Math.pow(e.y-p,2))%d:d-Math.sqrt(Math.pow(e.x-a,2)+Math.pow(e.y-p,2))%d;h=Math.cos(Math.atan(s))*t,c=s*h,u.push(new n.Point(a+h,p+c))}else u.push(new n.Point(a,p));const g=r(-o/s,0,a);if(null!==g&&(0!==g||0!==_))if(d>0&&(e.y<=0||i&&e.y<t.y)){const t=e.y<=0?Math.sqrt(Math.pow(e.x-g,2)+Math.pow(e.y-0,2))%d:d-Math.sqrt(Math.pow(e.x-g,2)+Math.pow(e.y-0,2))%d;h=Math.cos(Math.atan(s))*t,c=s*h,u.push(new n.Point(g-Math.sign(s)*h,-Math.sign(s)*c))}else u.push(new n.Point(g,0));const x=r((l-o)/s,0,a);if(null!==x&&(0!==x||p!==l))if(d>0&&(e.y>=l||i&&e.y>t.y)){const t=e.y>=l?Math.sqrt(Math.pow(e.x-x,2)+Math.pow(e.y-l,2))%d:d-Math.sqrt(Math.pow(e.x-x,2)+Math.pow(e.y-l,2))%d;h=Math.cos(Math.atan(s))*t,c=s*h,u.push(new n.Point(x+Math.sign(s)*h,l+Math.sign(s)*c))}else u.push(new n.Point(x,l))}if(u.length<1)return null;if(u.length<2&&u.push(u[0]),!i&&h){return[e,s(u[0],u[1],e,t)?u[1]:u[0]]}if(!o&&c){return[s(u[0],u[1],e,t)?u[0]:u[1],t]}return s(u[0],u[1],e,t)?[u[0],u[1]]:[u[1],u[0]]}},36036:(e,t,i)=>{"use strict";i.d(t,{LineAnchorRenderer:()=>w,anchor:()=>p})
;var n=i(14314),r=i(86441),s=i(34026),o=i(50151),a=i(37743),l=i(37265),d=i(56468),h=i(72791),c=i(61993),u=i(30125);class _ extends r.Point{constructor(e){super(e.x,e.y),(0,n.default)(this,this,e)}}function p(e){return new _(e)}function g(e,t,i,n){const r=i+n/2;(0,a.drawRoundRect)(e,t.x-r,t.y-r,2*r,2*r,(i+n)/2),e.closePath(),e.lineWidth=n}function x(e,t,i,n){e.globalAlpha=.2,g(e,t,i,n),e.stroke(),e.globalAlpha=1}function f(e,t,i,n){g(e,t,i-n,n),e.fill(),e.stroke()}function v(e,t,i,n){e.globalAlpha=.2,e.beginPath(),e.arc(t.x,t.y,i+n/2,0,2*Math.PI,!0),e.closePath(),e.lineWidth=n,e.stroke(),e.globalAlpha=1}function T(e,t,i,n){e.beginPath(),e.arc(t.x,t.y,i-n/2,0,2*Math.PI,!0),e.closePath(),e.lineWidth=n,e.fill(),e.stroke()}class w extends u.BitmapCoordinatesPaneRenderer{constructor(e){super(),this._data=null!=e?e:null}setData(e){this._data=e}hitTest(e){var t,i;if(null===this._data||this._data.disableInteractions)return null;const{radius:n,points:r}=this._data,s=(0,c.interactionTolerance)().anchor;for(let o=0;o<r.length;++o){const a=r[o];if(a.subtract(e).length()<=n+s)return new d.HitTestResult(null!==(t=a.hitTarget)&&void 0!==t?t:d.HitTarget.ChangePoint,{pointIndex:a.pointIndex,cursorType:null!==(i=a.cursorType)&&void 0!==i?i:h.PaneCursorType.Default,activeItem:a.activeItem,snappingPrice:a.snappingPrice,snappingIndex:a.snappingIndex,nonDiscreteIndex:a.nonDiscreteIndex,areaName:d.AreaName.AnchorPoint,possibleMovingDirections:a.possibleMovingDirections})}return null}doesIntersectWithBox(e){return null!==this._data&&this._data.points.some((t=>(0,s.pointInBox)(t,e)))}_drawImpl(e){if(null===this._data||!this._data.visible)return;const t=[],i=[],n=[],r=[];for(let e=0;e<this._data.points.length;++e){const s=this._data.points[e],o=this._data.backgroundColors[e];s.square?(t.push(s),i.push(o)):(n.push(s),r.push(o))}t.length&&this._drawPoints(e,t,i,f,x),n.length&&this._drawPoints(e,n,r,T,v)}_drawPoints(e,t,i,n,r){const{context:s,horizontalPixelRatio:a,verticalPixelRatio:d}=e,h=(0,o.ensureNotNull)(this._data),u=h.radius;let _=Math.max(1,Math.floor((h.strokeWidth||2)*a));h.selected&&(_+=Math.max(1,Math.floor(a/2)));const g=Math.max(1,Math.floor(a));let x=Math.round(u*a*2);x%2!=g%2&&(x+=1);const f=g%2/2,v=(0,c.interactionTolerance)().anchor;s.strokeStyle=h.color;for(let e=0;e<t.length;++e){const o=t[e];s.fillStyle=i[e];if(!((0,l.isInteger)(o.pointIndex)&&h.linePointBeingEdited===o.pointIndex)){if(n(s,p({...o,x:Math.round(o.x*a)+f,y:Math.round(o.y*d)+f}),x/2,_),!h.disableInteractions){if(o.subtract(h.currentPoint).length()<=u+v){const e=Math.max(1,Math.floor(h.selectedStrokeWidth*a));let t=Math.round(u*a*2);t%2!=g%2&&(t+=1);r(s,p({...o,x:Math.round(o.x*a)+f,y:Math.round(o.y*d)+f}),t/2,e)}}}}}}},90241:(e,t,i)=>{"use strict";i.d(t,{ParallelChannelRenderer:()=>g});const n=function(e,t){for(var i,n=-1,r=e.length;++n<r;){var s=t(e[n]);void 0!==s&&(i=void 0===i?s:i+s)}return i};var r=i(99097);const s=function(e){return e&&e.length?n(e,r.default):0}
;var o=i(34026),a=i(86441),l=i(4652),d=i(5531),h=i(56468),c=i(61993),u=i(75919),_=i(37743),p=i(48633);class g extends u.MediaCoordinatesPaneRenderer{constructor(e,t){super(),this._data=null,this._backgroundPolygon=null,this._clippedLines=new Map,this._hittestResult=e||new h.HitTestResult(h.HitTarget.MovePoint),this._backHittestResult=t||new h.HitTestResult(h.HitTarget.MovePointBackground)}setData(e){this._data=e,this._backgroundPolygon=null,this._clippedLines.clear()}hitTest(e,t){if(null===this._data)return null;const{line1:i,line2:n,middleLine:r}=this._data,s=t.mediaSize,a=(0,c.interactionTolerance)().line;for(const t of[i,n,r]){if(!t)continue;const i=this._getClippedLine(t,this._data,s);if(i){if((0,l.distanceToSegment)(i.points[0],i.points[1],e).distance<=a)return this._hittestResult}}if(this._data.hittestOnBackground&&this._data.fillBackground){const t=this._getBackgroundPolygon(this._data,s);if(t.length>0&&(0,o.pointInPolygon)(e,t))return this._backHittestResult}return null}_drawImpl(e){if(null===this._data)return;const{line1:t,line2:i,middleLine:n,skipLines:r,skipTopLine:s,fillBackground:o,backColor:a}=this._data,l=e.context;if(l.lineCap="round",r||this._drawLine(l,t,this._data,e.mediaSize),r||s||!i||this._drawLine(l,i,this._data,e.mediaSize),o&&i){const t=this._getBackgroundPolygon(this._data,e.mediaSize);if(t.length>0){l.beginPath(),l.moveTo(t[0].x,t[0].y);for(let e=1;e<t.length;e++)l.lineTo(t[e].x,t[e].y);l.fillStyle=a,l.fill()}}n&&!this._data.skipLines&&this._drawLine(l,n,this._data,e.mediaSize)}_drawLine(e,t,i,n){const r=this._getClippedLine(t,i,n);if(!r)return;e.strokeStyle=r.color,e.lineWidth=r.lineWidth,(0,_.setLineStyle)(e,r.lineStyle),e.strokeStyle=r.color;const[s,o]=r.points;(0,_.drawPixelPerfectLine)(e,s.x,s.y,o.x,o.y)}_getClippedLine(e,t,i){let n=this._clippedLines.get(e);if(void 0===n){const{lineWidth:r,lineStyle:o,points:a}=e,{extendLeft:l,extendRight:d}=t,h=(0,p.intersectLineWithViewport)(a[0],a[1],l,d,i.width,i.height,s((0,_.computeDashPattern)(r,o)));n=null==h?null:{...e,points:h},this._clippedLines.set(e,n)}return n}_getBackgroundPolygon(e,t){var i;return this._backgroundPolygon||(this._backgroundPolygon=null!==(i=this._getBackgroundPolygonImpl(e,t))&&void 0!==i?i:[]),this._backgroundPolygon}_getBackgroundPolygonImpl(e,t){if(void 0===e.line2)return null;const[i,n]=e.line1.points,[r,s]=e.line2.points;if((0,a.equalPoints)(i,n)||(0,a.equalPoints)(r,s)||(0,l.distanceToLine)(i,n,r).distance<1e-6||(0,l.distanceToLine)(i,n,s).distance<1e-6)return null;if(t.width<=0||t.height<=0)return null;let o=[new a.Point(0,0),new a.Point(t.width,0),new a.Point(t.width,t.height),new a.Point(0,t.height)];return o=x(o,i,n,s),e.extendRight||(o=x(o,n,s,r)),o=x(o,s,r,i),e.extendLeft||(o=x(o,r,i,n)),o}}function x(e,t,i,n){return null!==e?(0,d.intersectPolygonAndHalfplane)(e,(0,a.halfplaneThroughPoint)((0,a.lineThroughPoints)(t,i),n)):null}},74011:(e,t,i)=>{"use strict";i.d(t,{PolygonRenderer:()=>p})
;var n=i(34026),r=i(4652),s=i(91046),o=i(49857),a=i(56468),l=i(19063),d=i(75919),h=i(61993),c=i(37743),u=i(51056),_=i(64034);class p extends d.MediaCoordinatesPaneRenderer{constructor(e){super(),this._data=null,this._backHittest=new a.HitTestResult(a.HitTarget.MovePointBackground),this._points=[],this._hittest=null!=e?e:new a.HitTestResult(a.HitTarget.MovePoint)}setData(e){this._data=e,this._points=e.points}hitTest(e){if(null===this._data||void 0!==this._data.mouseTouchable&&!this._data.mouseTouchable)return null;const t=Math.max((0,h.interactionTolerance)().line,Math.ceil(this._data.linewidth/2)),i=this._points.length;if(1===i){return(0,n.pointInCircle)(e,this._points[0],t)?this._hittest:null}for(let n=1;n<i;n++){const i=this._points[n-1],s=this._points[n];if((0,r.distanceToSegment)(i,s,e).distance<=t)return this._hittest}if(this._data.filled&&this._data.fillBackground&&i>0){const n=this._points[0],s=this._points[i-1];if((0,r.distanceToSegment)(n,s,e).distance<=t)return this._hittest}return this._data.filled&&this._data.fillBackground&&(0,n.pointInPolygon)(e,this._data.points)?this._backHittest:null}_drawImpl(e){var t,i;const n=e.context,r=this._points.length;if(null===this._data||0===r)return;if(1===r)return void this._drawPoint(n,this._points[0],this._data.linewidth/2,this._data.color);n.beginPath();const a=this._data.linestyle===u.LINESTYLE_SOLID?"round":"butt",d=null!==(t=this._data.linecap)&&void 0!==t?t:a;n.lineCap=d,n.strokeStyle=this._data.color,n.lineWidth=this._data.linewidth,n.lineJoin=null!==(i=this._data.linejoin)&&void 0!==i?i:"round",(0,c.setLineStyle)(n,this._data.linestyle);const h=this._points[0];n.moveTo(h.x,h.y);for(const e of this._points)n.lineTo(e.x,e.y);if(this._data.filled&&this._data.fillBackground&&(n.fillStyle=(0,l.generateColor)(this._data.backcolor,this._data.transparency),n.fill()),this._data.filled&&!this._data.skipClosePath&&n.closePath(),r>1){if(this._data.leftend===o.LineEnd.Arrow){const e=this._correctArrowPoints(this._points[1],this._points[0],n.lineWidth,d);(0,s.drawArrow)(e[0],e[1],n,n.lineWidth,_.dpr1PixelRatioInfo)}if(this._data.rightend===o.LineEnd.Arrow){const e=this._correctArrowPoints(this._points[r-2],this._points[r-1],n.lineWidth,d);(0,s.drawArrow)(e[0],e[1],n,n.lineWidth,_.dpr1PixelRatioInfo)}}this._data.linewidth>0&&n.stroke()}_drawPoint(e,t,i,n){0!==i&&(e.beginPath(),e.fillStyle=n,e.arc(t.x,t.y,i,0,2*Math.PI,!0),e.fill(),e.closePath())}_correctArrowPoints(e,t,i,n){const r=t.subtract(e),s=r.length();if("butt"===n||s<1)return[e,t];const o=s+i/2;return[e,r.scaled(o/s).add(e)]}}},13075:(e,t,i)=>{"use strict";i.d(t,{PlusTextRendererDecorator:()=>r});var n=i(17330);class r{geometry(e){const t=(0,n.fontSize)(e);return{decoratorAndTextMargin:t/3,width:Math.round(.8*t),ignoreRtl:!1}}draw(e,t,i,n){const{horizontalPixelRatio:r,verticalPixelRatio:s}=t,o=Math.max(1,Math.round(r*n.decoratorWidth/8)),a=o%2/2,l=Math.round((n.textTop+n.textBottom)/2*s)+a,d=Math.round((n.decoratorLeft+n.decoratorWidth/2)*r)+a,h=Math.round(n.decoratorWidth*r);e.strokeStyle=i.color,e.lineWidth=o
;let c=h/2;d%2/2!=c%2/2&&(c+=.5),e.beginPath(),e.moveTo(d-c,l),e.lineTo(d+c,l),e.moveTo(d,l-c),e.lineTo(d,l+c),e.stroke()}static instance(){var e;return this._instance=null!==(e=this._instance)&&void 0!==e?e:new r,this._instance}}r._instance=null},1161:(e,t,i)=>{"use strict";i.d(t,{TriangleRenderer:()=>_});var n=i(86441),r=i(4652),s=i(34026),o=i(75919),a=i(56468),l=i(19063),d=i(61993),h=i(37743),c=i(51056),u=i(48633);class _ extends o.MediaCoordinatesPaneRenderer{constructor(){super(...arguments),this._data=null}setData(e){this._data=e}hitTest(e){if(null===this._data||this._data.points.length<2)return null;const[t,i]=this._data.points;let n=(0,r.distanceToSegment)(t,i,e);const o=(0,d.interactionTolerance)().line;if(n.distance<=o)return new a.HitTestResult(a.HitTarget.MovePoint);if(3!==this._data.points.length)return null;const l=this._data.points[2];return n=(0,r.distanceToSegment)(i,l,e),n.distance<=o?new a.HitTestResult(a.HitTarget.MovePoint):(n=(0,r.distanceToSegment)(l,t,e),n.distance<=o?new a.HitTestResult(a.HitTarget.MovePoint):this._data.fillBackground&&(0,s.pointInTriangle)(e,t,i,l)?new a.HitTestResult(a.HitTarget.MovePointBackground):null)}_drawImpl(e){var t;if(null===this._data||this._data.points.length<2)return;const i=e.context,r=(null!==(t=this._data.linestyle)&&void 0!==t?t:c.LINESTYLE_SOLID)===c.LINESTYLE_SOLID?"round":"butt";i.lineCap=r,i.lineJoin="round",i.strokeStyle=this._data.color,i.lineWidth=this._data.linewidth,void 0!==this._data.linestyle&&(0,h.setLineStyle)(i,this._data.linestyle);const[s,o,a=o]=this._data.points,{mediaSize:d}=e;if(this._data.fillBackground&&Math.abs((s.x-a.x)*(o.y-a.y)-(o.x-a.x)*(s.y-a.y))>1e-10){let e=[new n.Point(0,0),new n.Point(d.width,0),new n.Point(d.width,d.height),new n.Point(0,d.height)];if(e=(0,h.clipPolygonByEdge)(e,s,o,[o,a]),e=(0,h.clipPolygonByEdge)(e,o,a,[a,s]),e=(0,h.clipPolygonByEdge)(e,a,s,[s,o]),e&&e.length>1){i.save(),i.beginPath(),i.moveTo(e[0].x,e[0].y);for(let t=1;t<e.length;t++)i.lineTo(e[t].x,e[t].y);i.fillStyle=(0,l.generateColor)(this._data.backcolor,this._data.transparency),i.fill(),i.restore()}}const _=[],p=i.getLineDash().reduce(((e,t)=>e+t),0);[[s,o],[o,a],[a,s]].forEach((([e,t])=>{const i=(0,u.intersectLineWithViewport)(e,t,!1,!1,d.width,d.height,p);i&&_.push(i)})),_.length&&(i.beginPath(),_.forEach((([e,t])=>{i.moveTo(e.x,e.y),i.lineTo(t.x,t.y)})),i.stroke())}}},64099:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 30" width="24" height="30"><path fill="#2962FF" fill-rule="evenodd" d="m12 30 .88-.77C20.25 22.73 24 17.07 24 12.09 24 5.04 18.54 0 12 0S0 5.04 0 12.1c0 4.97 3.75 10.64 11.12 17.13L12 30Zm0-13a5 5 0 1 0 0-10 5 5 0 0 0 0 10Z"/></svg>'},99620:(e,t,i)=>{"use strict";e.exports=i.p+"prediction-clock-white.c4675d37769f1df4c9ec.png"},88249:(e,t,i)=>{"use strict";e.exports=i.p+"prediction-failure-white.a838a6689f951970e715.png"},14012:(e,t,i)=>{"use strict";e.exports=i.p+"prediction-success-white.2fb9966b4c0f3529a2ea.png"}}]);