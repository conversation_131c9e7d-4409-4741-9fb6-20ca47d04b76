(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[92],{36136:e=>{e.exports={"nav-button":"nav-button-znwuaSC1",link:"link-znwuaSC1",background:"background-znwuaSC1",icon:"icon-znwuaSC1","flip-icon":"flip-icon-znwuaSC1","size-large":"size-large-znwuaSC1","preserve-paddings":"preserve-paddings-znwuaSC1","size-medium":"size-medium-znwuaSC1","size-small":"size-small-znwuaSC1","size-xsmall":"size-xsmall-znwuaSC1","size-xxsmall":"size-xxsmall-znwuaSC1","visually-hidden":"visually-hidden-znwuaSC1"}},377:e=>{e.exports={container:"container-TCHLKPuQ","container-danger":"container-danger-TCHLKPuQ","light-title":"light-title-TCHLKPuQ","light-icon":"light-icon-TCHLKPuQ",icon:"icon-TCHLKPuQ",header:"header-TCHLKPuQ","light-container-danger":"light-container-danger-TCHLKPuQ","container-warning":"container-warning-TCHLKPuQ","light-container-warning":"light-container-warning-TCHLKPuQ","container-success":"container-success-TCHLKPuQ","light-container-success":"light-container-success-TCHLKPuQ","container-default":"container-default-TCHLKPuQ","light-container-default":"light-container-default-TCHLKPuQ","text-wrap":"text-wrap-TCHLKPuQ","light-text-wrap":"light-text-wrap-TCHLKPuQ","close-button":"close-button-TCHLKPuQ","light-close-button":"light-close-button-TCHLKPuQ",informerBody:"informerBody-TCHLKPuQ",mainProblem:"mainProblem-TCHLKPuQ","header-inline":"header-inline-TCHLKPuQ","header-new-line":"header-new-line-TCHLKPuQ"}},6388:e=>{e.exports={container:"container-Q8oybhDM",centerElement:"centerElement-Q8oybhDM",notice:"notice-Q8oybhDM",noticeShowed:"noticeShowed-Q8oybhDM"}},57340:(e,t,n)=>{"use strict";n.d(t,{CloseButton:()=>d});var r=n(50959),i=n(64388),a=n(17105),o=n(15130),s=n(38822),l=n(63346),c=n(34983);function u(e="large"){switch(e){case"large":return a;case"medium":default:return o;case"small":return s;case"xsmall":return l;case"xxsmall":return c}}const d=r.forwardRef(((e,t)=>r.createElement(i.NavButton,{...e,ref:t,icon:u(e.size)})))},64388:(e,t,n)=>{"use strict";n.d(t,{NavButton:()=>c});var r=n(50959),i=n(97754),a=n(9745),o=(n(78572),n(36136));function s(e){const{size:t="large",preservePaddings:n,isLink:r,flipIconOnRtl:a,className:s}=e;return i(o["nav-button"],o[`size-${t}`],n&&o["preserve-paddings"],a&&o["flip-icon"],r&&o.link,s)}function l(e){const{children:t,icon:n}=e;return r.createElement(r.Fragment,null,r.createElement("span",{className:o.background}),r.createElement(a.Icon,{icon:n,className:o.icon,"aria-hidden":!0}),t&&r.createElement("span",{className:o["visually-hidden"]},t))}const c=(0,r.forwardRef)(((e,t)=>{const{icon:n,type:i="button",preservePaddings:a,flipIconOnRtl:o,size:c,"aria-label":u,...d}=e;return r.createElement("button",{...d,className:s({...e,children:u}),ref:t,type:i},r.createElement(l,{icon:n},u))}));c.displayName="NavButton";var u=n(21593),d=n(53017);(0,r.forwardRef)(((e,t)=>{const{icon:n,renderComponent:i,"aria-label":a,...o}=e,c=null!=i?i:u.CustomComponentDefaultLink;return r.createElement(c,{...o,className:s({...e,children:a,isLink:!0}),reference:(0,d.isomorphicRef)(t)
},r.createElement(l,{icon:n},a))})).displayName="NavAnchorButton"},78572:(e,t,n)=>{"use strict";var r,i,a,o;!function(e){e.Primary="primary",e.QuietPrimary="quiet-primary",e.Secondary="secondary",e.Ghost="ghost"}(r||(r={})),function(e){e.XXSmall="xxsmall",e.XSmall="xsmall",e.Small="small",e.Medium="medium",e.Large="large",e.XLarge="xlarge",e.XXLarge="xxlarge"}(i||(i={})),function(e){e.Brand="brand",e.Gray="gray",e.LightGray="light-gray",e.Green="green",e.Red="red",e.Black="black",e.Gradient="gradient",e.BlackFriday="black-friday",e.CyberMonday="cyber-monday"}(a||(a={})),function(e){e.Semibold18px="semibold18px",e.Semibold16px="semibold16px",e.Semibold14px="semibold14px",e.Medium16px="medium16px",e.Regular16px="regular16px",e.Regular14px="regular14px"}(o||(o={}))},38952:(e,t,n)=>{"use strict";function r(e){const{reference:t,...n}=e;return{...n,ref:t}}n.d(t,{renameRef:()=>r})},21593:(e,t,n)=>{"use strict";n.d(t,{CustomComponentDefaultLink:()=>a});var r=n(50959),i=n(38952);function a(e){return r.createElement("a",{...(0,i.renameRef)(e)})}r.PureComponent},9745:(e,t,n)=>{"use strict";n.d(t,{Icon:()=>i});var r=n(50959);const i=r.forwardRef(((e,t)=>{const{icon:n="",title:i,ariaLabel:a,ariaLabelledby:o,ariaHidden:s,...l}=e,c=!!(i||a||o);return r.createElement("span",{...l,ref:t,role:"img","aria-label":a,"aria-labelledby":o,"aria-hidden":s||!c,title:i,dangerouslySetInnerHTML:{__html:n}})}))},90186:(e,t,n)=>{"use strict";function r(e){return a(e,o)}function i(e){return a(e,s)}function a(e,t){const n=Object.entries(e).filter(t),r={};for(const[e,t]of n)r[e]=t;return r}function o(e){const[t,n]=e;return 0===t.indexOf("data-")&&"string"==typeof n}function s(e){return 0===e[0].indexOf("aria-")}n.d(t,{filterAriaProps:()=>i,filterDataProps:()=>r,filterProps:()=>a,isAriaAttribute:()=>s,isDataAttribute:()=>o})},53017:(e,t,n)=>{"use strict";function r(e){return t=>{e.forEach((e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)}))}}function i(e){return r([e])}n.d(t,{isomorphicRef:()=>i,mergeRefs:()=>r})},1524:(e,t,n)=>{"use strict";n.r(t),n.d(t,{ChartScreenshotHintRenderer:()=>y});var r,i,a=n(50959),o=n(32227),s=n(12481),l=n(97754),c=n(9745),u=n(57340),d=n(90186),m=n(30162),h=n(27941),g=n(99084),w=n(377),p=n.n(w);!function(e){e.Danger="danger",e.Warning="warning",e.Success="success",e.Default="default"}(r||(r={})),function(e){e.Inline="inline",e.NewLine="new-line"}(i||(i={}));const f={danger:m,warning:m,success:g,default:h};function v(e){const{informerIntent:t,content:n,className:r,header:i,isIconShown:o=!0,isCloseButtonShown:s,icon:m,onCloseClick:h,closeButtonLabel:g="Close",headerPlacement:w="inline",children:v,isLight:x}=e;return a.createElement("div",{className:l(p().container,p()[`container-${t}`],x&&p()[`light-container-${t}`],r),...(0,d.filterDataProps)(e),...(0,d.filterAriaProps)(e)},a.createElement("div",{className:p().informerBody},n&&a.createElement("div",{className:p().mainProblem},o&&a.createElement(c.Icon,{className:l(p().icon,x&&p()["light-icon"]),icon:null!=m?m:f[t]}),a.createElement("div",{
className:l(p()["text-wrap"],x&&p()["light-text-wrap"])},i&&a.createElement("span",{className:l(x&&p()["light-title"],p().header,p()[`header-${x?"new-line":w}`])},i),a.createElement("span",{"aria-live":"assertive"}," ",n))),v),s&&a.createElement(u.CloseButton,{"aria-label":g,onClick:h,className:l(x&&p()["light-close-button"],p()["close-button"]),size:x?"xxsmall":"xsmall",preservePaddings:!x}))}var x=n(29196),C=n(6388);function b(e){const{text:t,style:n,hideTimeout:r,informerIntent:i="success"}=e,o=(0,x.useHintShowAnimation)(r);return a.createElement("div",{className:C.container,style:n},a.createElement("div",{className:C.centerElement},a.createElement(v,{content:t,informerIntent:i,className:l(C.notice,o&&C.noticeShowed)})))}class y{constructor(e,t){this._showed=!1,this._wrap=document.createElement("div"),this._container=e,this._debouncedHide=(0,s.default)((()=>this.hide()),3e3),this._bottomPadding=t.bottomPadding}show(e){this._wrap&&!this._showed&&(this._showed=!0,this._container.append(this._wrap),o.render(a.createElement(b,{text:e,style:this._bottomPadding?{bottom:70}:void 0}),this._wrap),this._debouncedHide())}hide(){this._wrap&&(this._showed=!1,o.unmountComponentAtNode(this._wrap),this._wrap.remove())}destroy(){this.hide(),delete this._wrap}}},29196:(e,t,n)=>{"use strict";n.d(t,{useHintShowAnimation:()=>i});var r=n(50959);function i(e){const[t,n]=(0,r.useState)(!1);return(0,r.useLayoutEffect)((()=>{const t=setTimeout((()=>n(!0)),50),r=setTimeout((()=>n(!1)),null!=e?e:2500);return()=>{clearTimeout(t),clearTimeout(r)}}),[]),t}},99084:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M9 17A8 8 0 1 0 9 1a8 8 0 0 0 0 16Zm3.87-12.15c.36.2.49.66.28 1.02l-4 7a.75.75 0 0 1-1.18.16l-3-3a.75.75 0 1 1 1.06-1.06l2.3 2.3 3.52-6.14a.75.75 0 0 1 1.02-.28Z"/></svg>'},30162:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" fill-rule="evenodd" d="M9 17A8 8 0 1 0 9 1a8 8 0 0 0 0 16ZM9 4c-.79 0-1.38.7-1.25 1.48l.67 4.03a.59.59 0 0 0 1.16 0l.67-4.03A1.27 1.27 0 0 0 9 4Zm0 8a1 1 0 1 0 0 2 1 1 0 0 0 0-2Z"/></svg>'},27941:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M9 17A8 8 0 1 0 9 1a8 8 0 0 0 0 16ZM8 6a1 1 0 1 1 2 0 1 1 0 0 1-2 0Zm1 2c.49 0 1 .59 1 1v3.01c0 .42-.51.99-1 .99s-1-.57-1-.99V9c0-.41.51-1 1-1Z"/></svg>'},17105:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path stroke="currentColor" stroke-width="1.2" d="m1.5 1.5 21 21m0-21-21 21"/></svg>'},15130:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path stroke="currentColor" stroke-width="1.2" d="m1.5 1.5 15 15m0-15-15 15"/></svg>'},38822:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 14 14" width="14" height="14"><path stroke="currentColor" stroke-width="1.2" d="m1.5 1.5 11 11m0-11-11 11"/></svg>'},63346:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" width="12" height="12"><path stroke="currentColor" stroke-width="1.2" d="m1.5 1.5 9 9m0-9-9 9"/></svg>'},34983:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 10 10" width="10" height="10"><path stroke="currentColor" stroke-width="1.2" d="m1.5 1.5 7 7m0-7-7 7"/></svg>'}}]);