(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[7038],{36136:e=>{e.exports={"nav-button":"nav-button-znwuaSC1",link:"link-znwuaSC1",background:"background-znwuaSC1",icon:"icon-znwuaSC1","flip-icon":"flip-icon-znwuaSC1","size-large":"size-large-znwuaSC1","preserve-paddings":"preserve-paddings-znwuaSC1","size-medium":"size-medium-znwuaSC1","size-small":"size-small-znwuaSC1","size-xsmall":"size-xsmall-znwuaSC1","size-xxsmall":"size-xxsmall-znwuaSC1","visually-hidden":"visually-hidden-znwuaSC1"}},68976:e=>{e.exports={button:"button-D4RPB3ZC",content:"content-D4RPB3ZC",iconOnly:"iconOnly-D4RPB3ZC",link:"link-D4RPB3ZC",brand:"brand-D4RPB3ZC",primary:"primary-D4RPB3ZC",secondary:"secondary-D4RPB3ZC",gray:"gray-D4RPB3ZC",green:"green-D4RPB3ZC",red:"red-D4RPB3ZC",black:"black-D4RPB3ZC","black-friday":"black-friday-D4RPB3ZC","cyber-monday":"cyber-monday-D4RPB3ZC",xsmall:"xsmall-D4RPB3ZC",startIconWrap:"startIconWrap-D4RPB3ZC",endIconWrap:"endIconWrap-D4RPB3ZC",withStartIcon:"withStartIcon-D4RPB3ZC",withEndIcon:"withEndIcon-D4RPB3ZC",small:"small-D4RPB3ZC",medium:"medium-D4RPB3ZC",large:"large-D4RPB3ZC",xlarge:"xlarge-D4RPB3ZC",animated:"animated-D4RPB3ZC",stretch:"stretch-D4RPB3ZC",grouped:"grouped-D4RPB3ZC",adjustPosition:"adjustPosition-D4RPB3ZC",firstRow:"firstRow-D4RPB3ZC",firstCol:"firstCol-D4RPB3ZC","no-corner-top-left":"no-corner-top-left-D4RPB3ZC","no-corner-top-right":"no-corner-top-right-D4RPB3ZC","no-corner-bottom-right":"no-corner-bottom-right-D4RPB3ZC","no-corner-bottom-left":"no-corner-bottom-left-D4RPB3ZC",textWrap:"textWrap-D4RPB3ZC",multilineContent:"multilineContent-D4RPB3ZC",secondaryText:"secondaryText-D4RPB3ZC",primaryText:"primaryText-D4RPB3ZC"}},60903:e=>{e.exports={"small-height-breakpoint":"screen and (max-height: 360px)",footer:"footer-PhMf7PhQ",submitButton:"submitButton-PhMf7PhQ",buttons:"buttons-PhMf7PhQ"}},53330:e=>{e.exports={"tablet-normal-breakpoint":"screen and (max-width: 768px)","small-height-breakpoint":"screen and (max-height: 360px)","tablet-small-breakpoint":"screen and (max-width: 430px)"}},8473:e=>{e.exports={dialog:"dialog-b8SxMnzX",wrapper:"wrapper-b8SxMnzX",separator:"separator-b8SxMnzX",bounded:"bounded-b8SxMnzX"}},80822:e=>{e.exports={"small-height-breakpoint":"screen and (max-height: 360px)",container:"container-BZKENkhT",unsetAlign:"unsetAlign-BZKENkhT",title:"title-BZKENkhT",subtitle:"subtitle-BZKENkhT",textWrap:"textWrap-BZKENkhT",ellipsis:"ellipsis-BZKENkhT",close:"close-BZKENkhT",icon:"icon-BZKENkhT"}},39916:e=>{e.exports={container:"container-N4SM7hWm",textBlockHeadline:"textBlockHeadline-N4SM7hWm",dropzone:"dropzone-N4SM7hWm",textBlock:"textBlock-N4SM7hWm",textBlockError:"textBlockError-N4SM7hWm",img:"img-N4SM7hWm",remove:"remove-N4SM7hWm",backdrop:"backdrop-N4SM7hWm",mainText:"mainText-N4SM7hWm"}},58510:e=>{e.exports={wrap:"wrap-b6_0ORMg",disabled:"disabled-b6_0ORMg"}},3252:e=>{e.exports={opacity:"opacity-EnWts7Xu",opacitySlider:"opacitySlider-EnWts7Xu",opacitySliderGradient:"opacitySliderGradient-EnWts7Xu",pointer:"pointer-EnWts7Xu",
dragged:"dragged-EnWts7Xu",opacityPointerWrap:"opacityPointerWrap-EnWts7Xu",opacityInputWrap:"opacityInputWrap-EnWts7Xu",opacityInput:"opacityInput-EnWts7Xu",opacityInputPercent:"opacityInputPercent-EnWts7Xu",accessible:"accessible-EnWts7Xu"}},47625:e=>{e.exports={separator:"separator-Pf4rIzEt"}},94720:(e,t,n)=>{"use strict";var r,o,a;function i(e="default"){switch(e){case"default":return"primary";case"stroke":return"secondary"}}function s(e="primary"){switch(e){case"primary":return"brand";case"success":return"green";case"default":return"gray";case"danger":return"red"}}function l(e="m"){switch(e){case"s":return"xsmall";case"m":return"small";case"l":return"large"}}n.d(t,{Button:()=>_}),function(e){e.Primary="primary",e.Success="success",e.Default="default",e.Danger="danger"}(r||(r={})),function(e){e.Small="s",e.Medium="m",e.Large="l"}(o||(o={})),function(e){e.Default="default",e.Stroke="stroke"}(a||(a={}));var c=n(50959),u=n(97754),d=n(95604),p=n(9745),m=n(68976),h=n.n(m);const g="apply-overflow-tooltip apply-overflow-tooltip--check-children-recursively apply-overflow-tooltip--allow-text apply-common-tooltip";function f(e){const{color:t="brand",size:n="medium",variant:r="primary",stretch:o=!1,icon:a,startIcon:i,endIcon:s,iconOnly:l=!1,className:c,isGrouped:p,cellState:m,disablePositionAdjustment:f=!1,primaryText:b,secondaryText:v,isAnchor:C=!1}=e,y=function(e){let t="";return 0!==e&&(1&e&&(t=u(t,h()["no-corner-top-left"])),2&e&&(t=u(t,h()["no-corner-top-right"])),4&e&&(t=u(t,h()["no-corner-bottom-right"])),8&e&&(t=u(t,h()["no-corner-bottom-left"]))),t}((0,d.getGroupCellRemoveRoundBorders)(m));return u(c,h().button,h()[n],h()[t],h()[r],o&&h().stretch,(a||i)&&h().withStartIcon,s&&h().withEndIcon,l&&h().iconOnly,y,p&&h().grouped,p&&!f&&h().adjustPosition,p&&m.isTop&&h().firstRow,p&&m.isLeft&&h().firstCol,b&&v&&h().multilineContent,C&&h().link,g)}function b(e){const{startIcon:t,icon:n,iconOnly:r,children:o,endIcon:a,primaryText:i,secondaryText:s}=e,l=null!=t?t:n,d=!(t||n||a||r)&&!o&&i&&s;return c.createElement(c.Fragment,null,l&&c.createElement(p.Icon,{icon:l,className:h().startIconWrap}),o&&c.createElement("span",{className:h().content},o),a&&!r&&c.createElement(p.Icon,{icon:a,className:h().endIconWrap}),d&&function(e){return e.primaryText&&e.secondaryText&&c.createElement("div",{className:u(h().textWrap,g)},c.createElement("span",{className:h().primaryText}," ",e.primaryText," "),"string"==typeof e.secondaryText?c.createElement("span",{className:h().secondaryText}," ",e.secondaryText," "):c.createElement("span",{className:h().secondaryText},c.createElement("span",null,e.secondaryText.firstLine),c.createElement("span",null,e.secondaryText.secondLine)))}(e))}var v=n(34094),C=n(86332),y=n(90186);function x(e){const{className:t,color:n,variant:r,size:o,stretch:a,animated:i,icon:s,iconOnly:l,startIcon:c,endIcon:u,primaryText:d,secondaryText:p,...m}=e;return{...m,...(0,y.filterDataProps)(e),...(0,y.filterAriaProps)(e)}}function E(e){
const{reference:t,tooltipText:n,...r}=e,{isGrouped:o,cellState:a,disablePositionAdjustment:i}=(0,c.useContext)(C.ControlGroupContext),s=f({...r,isGrouped:o,cellState:a,disablePositionAdjustment:i});return c.createElement("button",{...x(r),className:s,ref:t,"data-overflow-tooltip-text":null!=n?n:e.primaryText?[e.primaryText,e.secondaryText].join(" "):(0,v.getTextForTooltip)(e.children)},c.createElement(b,{...r}))}n(78572);function w(e){const{intent:t,size:n,appearance:r,useFullWidth:o,icon:a,...c}=e;return{...c,color:s(t),size:l(n),variant:i(r),stretch:o,startIcon:a}}function _(e){return c.createElement(E,{...w(e)})}},57340:(e,t,n)=>{"use strict";n.d(t,{CloseButton:()=>d});var r=n(50959),o=n(64388),a=n(17105),i=n(15130),s=n(38822),l=n(63346),c=n(34983);function u(e="large"){switch(e){case"large":return a;case"medium":default:return i;case"small":return s;case"xsmall":return l;case"xxsmall":return c}}const d=r.forwardRef(((e,t)=>r.createElement(o.NavButton,{...e,ref:t,icon:u(e.size)})))},64388:(e,t,n)=>{"use strict";n.d(t,{NavButton:()=>c});var r=n(50959),o=n(97754),a=n(9745),i=(n(78572),n(36136));function s(e){const{size:t="large",preservePaddings:n,isLink:r,flipIconOnRtl:a,className:s}=e;return o(i["nav-button"],i[`size-${t}`],n&&i["preserve-paddings"],a&&i["flip-icon"],r&&i.link,s)}function l(e){const{children:t,icon:n}=e;return r.createElement(r.Fragment,null,r.createElement("span",{className:i.background}),r.createElement(a.Icon,{icon:n,className:i.icon,"aria-hidden":!0}),t&&r.createElement("span",{className:i["visually-hidden"]},t))}const c=(0,r.forwardRef)(((e,t)=>{const{icon:n,type:o="button",preservePaddings:a,flipIconOnRtl:i,size:c,"aria-label":u,...d}=e;return r.createElement("button",{...d,className:s({...e,children:u}),ref:t,type:o},r.createElement(l,{icon:n},u))}));c.displayName="NavButton";var u=n(21593),d=n(53017);(0,r.forwardRef)(((e,t)=>{const{icon:n,renderComponent:o,"aria-label":a,...i}=e,c=null!=o?o:u.CustomComponentDefaultLink;return r.createElement(c,{...i,className:s({...e,children:a,isLink:!0}),reference:(0,d.isomorphicRef)(t)},r.createElement(l,{icon:n},a))})).displayName="NavAnchorButton"},78572:(e,t,n)=>{"use strict";var r,o,a,i;!function(e){e.Primary="primary",e.QuietPrimary="quiet-primary",e.Secondary="secondary",e.Ghost="ghost"}(r||(r={})),function(e){e.XXSmall="xxsmall",e.XSmall="xsmall",e.Small="small",e.Medium="medium",e.Large="large",e.XLarge="xlarge",e.XXLarge="xxlarge"}(o||(o={})),function(e){e.Brand="brand",e.Gray="gray",e.LightGray="light-gray",e.Green="green",e.Red="red",e.Black="black",e.Gradient="gradient",e.BlackFriday="black-friday",e.CyberMonday="cyber-monday"}(a||(a={})),function(e){e.Semibold18px="semibold18px",e.Semibold16px="semibold16px",e.Semibold14px="semibold14px",e.Medium16px="medium16px",e.Regular16px="regular16px",e.Regular14px="regular14px"}(i||(i={}))},86332:(e,t,n)=>{"use strict";n.d(t,{ControlGroupContext:()=>r});const r=n(50959).createContext({isGrouped:!1,cellState:{isTop:!0,isRight:!0,isBottom:!0,isLeft:!0}})},95604:(e,t,n)=>{"use strict";function r(e){
let t=0;return e.isTop&&e.isLeft||(t+=1),e.isTop&&e.isRight||(t+=2),e.isBottom&&e.isLeft||(t+=8),e.isBottom&&e.isRight||(t+=4),t}n.d(t,{getGroupCellRemoveRoundBorders:()=>r})},38952:(e,t,n)=>{"use strict";function r(e){const{reference:t,...n}=e;return{...n,ref:t}}n.d(t,{renameRef:()=>r})},21593:(e,t,n)=>{"use strict";n.d(t,{CustomComponentDefaultLink:()=>a});var r=n(50959),o=n(38952);function a(e){return r.createElement("a",{...(0,o.renameRef)(e)})}r.PureComponent},34094:(e,t,n)=>{"use strict";n.d(t,{getTextForTooltip:()=>i});var r=n(50959);const o=e=>(0,r.isValidElement)(e)&&Boolean(e.props.children),a=e=>null==e||"boolean"==typeof e||"{}"===JSON.stringify(e)?"":e.toString()+" ",i=e=>Array.isArray(e)||(0,r.isValidElement)(e)?r.Children.toArray(e).reduce(((e,t)=>{let n="";return n=(0,r.isValidElement)(t)&&o(t)?i(t.props.children):(0,r.isValidElement)(t)&&!o(t)?"":a(t),e.concat(n)}),"").trim():a(e)},50182:(e,t,n)=>{"use strict";n.d(t,{AdaptiveConfirmDialog:()=>m});var r,o=n(50959),a=n(97754),i=n.n(a),s=n(94720),l=n(50151),c=n(11542),u=n(68335),d=n(79418),p=n(60903);!function(e){e.Submit="submit",e.Cancel="cancel",e.None="none"}(r||(r={}));class m extends o.PureComponent{constructor(){super(...arguments),this._dialogRef=o.createRef(),this._handleClose=()=>{const{defaultActionOnClose:e,onSubmit:t,onCancel:n,onClose:r}=this.props;switch(e){case"submit":t();break;case"cancel":n()}r()},this._handleCancel=()=>{this.props.onCancel(),this.props.onClose()},this._handleKeyDown=e=>{const{onSubmit:t,submitButtonDisabled:n,submitOnEnterKey:r}=this.props;13===(0,u.hashFromEvent)(e)&&r&&(e.preventDefault(),n||t())}}render(){const{render:e,onClose:t,onSubmit:n,onCancel:r,footerLeftRenderer:a,submitButtonText:i,submitButtonDisabled:s,defaultActionOnClose:l,submitOnEnterKey:c,...u}=this.props;return o.createElement(d.AdaptivePopupDialog,{...u,ref:this._dialogRef,onKeyDown:this._handleKeyDown,render:this._renderChildren(),onClose:this._handleClose})}focus(){(0,l.ensureNotNull)(this._dialogRef.current).focus()}_renderChildren(){return e=>{const{render:t,footerLeftRenderer:r,additionalButtons:a,submitButtonText:l,submitButtonDisabled:u,onSubmit:d,cancelButtonText:m,showCancelButton:h=!0,showSubmitButton:g=!0,submitButtonClassName:f,cancelButtonClassName:b,buttonsWrapperClassName:v}=this.props;return o.createElement(o.Fragment,null,t(e),o.createElement("div",{className:p.footer},r&&r(e.isSmallWidth),o.createElement("div",{className:i()(p.buttons,v)},a,h&&o.createElement(s.Button,{className:b,name:"cancel",appearance:"stroke",onClick:this._handleCancel},null!=m?m:c.t(null,void 0,n(4543))),g&&o.createElement("span",{className:p.submitButton},o.createElement(s.Button,{className:f,disabled:u,name:"submit",onClick:d,"data-name":"submit-button"},null!=l?l:c.t(null,void 0,n(19295)))))))}}}m.defaultProps={defaultActionOnClose:"submit",submitOnEnterKey:!0}},24437:(e,t,n)=>{"use strict";n.d(t,{DialogBreakpoints:()=>o});var r=n(53330);const o={SmallHeight:r["small-height-breakpoint"],TabletSmall:r["tablet-small-breakpoint"],
TabletNormal:r["tablet-normal-breakpoint"]}},79418:(e,t,n)=>{"use strict";n.d(t,{AdaptivePopupDialog:()=>P});var r=n(50959),o=n(50151),a=n(97754),i=n.n(a),s=n(68335),l=n(63273),c=n(35749),u=n(82206),d=n(1109),p=n(24437),m=n(90692),h=n(95711);var g=n(52092),f=n(76422),b=n(11542),v=n(57340);const C=r.createContext({setHideClose:()=>{}});var y=n(80822);function x(e){const{title:t,titleTextWrap:o=!1,subtitle:a,showCloseIcon:s=!0,onClose:l,onCloseButtonKeyDown:c,renderBefore:u,renderAfter:d,draggable:p,className:m,unsetAlign:h,closeAriaLabel:g=b.t(null,void 0,n(47742)),closeButtonReference:f}=e,[x,E]=(0,r.useState)(!1);return r.createElement(C.Provider,{value:{setHideClose:E}},r.createElement("div",{className:i()(y.container,m,(a||h)&&y.unsetAlign)},u,r.createElement("div",{"data-dragg-area":p,className:y.title},r.createElement("div",{className:i()(o?y.textWrap:y.ellipsis)},t),a&&r.createElement("div",{className:i()(y.ellipsis,y.subtitle)},a)),d,s&&!x&&r.createElement(v.CloseButton,{className:y.close,"data-name":"close","aria-label":g,onClick:l,onKeyDown:c,ref:f,size:"medium",preservePaddings:!0})))}var E=n(53017),w=n(90186),_=n(56570),S=n(8473);const N={vertical:20},B={vertical:0};class P extends r.PureComponent{constructor(){super(...arguments),this._controller=null,this._reference=null,this._orientationMediaQuery=null,this._embedResizerOverridesEnabled=_.enabled("embed_resizer_overrides"),this._renderChildren=(e,t)=>(this._controller=e,this.props.render({requestResize:this._requestResize,centerAndFit:this._centerAndFit,isSmallWidth:t})),this._handleReference=e=>this._reference=e,this._handleCloseBtnClick=()=>{this.props.onKeyboardClose&&this.props.onKeyboardClose(),this._handleClose()},this._handleClose=()=>{this.props.onClose()},this._handleOpen=()=>{void 0!==this.props.onOpen&&this.props.isOpened&&this.props.onOpen(this.props.fullScreen||window.matchMedia(p.DialogBreakpoints.TabletSmall).matches)},this._handleKeyDown=e=>{if(!e.defaultPrevented){if(this.props.onKeyDown&&this.props.onKeyDown(e),27===(0,s.hashFromEvent)(e)){if(e.defaultPrevented)return;if(this.props.forceCloseOnEsc&&this.props.forceCloseOnEsc())return this.props.onKeyboardClose&&this.props.onKeyboardClose(),void this._handleClose();const{activeElement:n}=document;if(null!==n){if(e.preventDefault(),"true"===(t=n).getAttribute("data-haspopup")&&"true"!==t.getAttribute("data-expanded"))return void this._handleClose();const r=this._reference;if(null!==r&&(0,c.isTextEditingField)(n))return void r.focus();if(null==r?void 0:r.contains(n))return this.props.onKeyboardClose&&this.props.onKeyboardClose(),void this._handleClose()}}var t,n;(function(e){if("function"==typeof e)return e();return Boolean(e)})(this.props.disableTabNavigationContainment)||(n=e,[9,s.Modifiers.Shift+9].includes((0,s.hashFromEvent)(n))&&n.stopPropagation())}},this._requestResize=()=>{null!==this._controller&&this._controller.recalculateBounds()},this._centerAndFit=()=>{null!==this._controller&&this._controller.centerAndFit()},this._calculatePositionWithOffsets=(e,t)=>{const n=(0,
o.ensureDefined)(this.props.fullScreenViewOffsets).value();return{top:n.top,left:(0,l.isRtl)()?-n.right:n.left,width:t.clientWidth-n.left-n.right,height:t.clientHeight-n.top-n.bottom}}}componentDidMount(){this.props.ignoreClosePopupsAndDialog||f.subscribe(g.CLOSE_POPUPS_AND_DIALOGS_COMMAND,this._handleClose,null),this._handleOpen(),void 0!==this.props.onOpen&&(this._orientationMediaQuery=window.matchMedia("(orientation: portrait)"),this._orientationMediaQuery.addEventListener("change",this._handleOpen)),this.props.fullScreenViewOffsets&&this.props.fullScreen&&this.props.fullScreenViewOffsets.subscribe(this._requestResize)}componentWillUnmount(){this.props.ignoreClosePopupsAndDialog||f.unsubscribe(g.CLOSE_POPUPS_AND_DIALOGS_COMMAND,this._handleClose,null),null!==this._orientationMediaQuery&&this._orientationMediaQuery.removeEventListener("change",this._handleOpen),this.props.fullScreenViewOffsets&&this.props.fullScreen&&this.props.fullScreenViewOffsets.unsubscribe(this._requestResize)}focus(){(0,o.ensureNotNull)(this._reference).focus()}getElement(){return this._reference}contains(e){var t,n;return null!==(n=null===(t=this._reference)||void 0===t?void 0:t.contains(e))&&void 0!==n&&n}render(){const{className:e,wrapperClassName:t,headerClassName:n,isOpened:o,title:a,titleTextWrap:s,dataName:l,onClickOutside:c,additionalElementPos:g,additionalHeaderElement:f,backdrop:b,shouldForceFocus:v=!0,shouldReturnFocus:C,onForceFocus:y,showSeparator:_,subtitle:P,draggable:D=!0,fullScreen:R=!1,showCloseIcon:k=!0,rounded:T=!0,isAnimationEnabled:O,growPoint:z,dialogTooltip:M,unsetHeaderAlign:W,onDragStart:I,dataDialogName:A,closeAriaLabel:L,containerAriaLabel:F,reference:Z,containerTabIndex:K,closeButtonReference:U,onCloseButtonKeyDown:X,shadowed:q,fullScreenViewOffsets:G,fixedBody:Q,onClick:V}=this.props,j="after"!==g?f:void 0,H="after"===g?f:void 0,$="string"==typeof a?a:A||"",J=(0,w.filterDataProps)(this.props),Y=(0,E.mergeRefs)([this._handleReference,Z]);return r.createElement(m.MatchMedia,{rule:p.DialogBreakpoints.SmallHeight},(g=>r.createElement(m.MatchMedia,{rule:p.DialogBreakpoints.TabletSmall},(p=>r.createElement(u.PopupDialog,{rounded:!(p||R)&&T,className:i()(S.dialog,R&&G&&S.bounded,e),isOpened:o,reference:Y,onKeyDown:this._handleKeyDown,onClickOutside:c,onClickBackdrop:c,fullscreen:p||R,guard:g?B:N,boundByScreen:p||R,shouldForceFocus:v,onForceFocus:y,shouldReturnFocus:C,backdrop:b,draggable:D,isAnimationEnabled:O,growPoint:z,name:this.props.dataName,dialogTooltip:M,onDragStart:I,containerAriaLabel:F,containerTabIndex:K,calculateDialogPosition:R&&G?this._calculatePositionWithOffsets:void 0,shadowed:q,fixedBody:Q,onClick:V,...J},r.createElement("div",{className:i()(S.wrapper,t),"data-name":l,"data-dialog-name":$},void 0!==a&&r.createElement(x,{draggable:D&&!(p||R),onClose:this._handleCloseBtnClick,renderAfter:H,renderBefore:j,subtitle:P,title:a,titleTextWrap:s,showCloseIcon:k,className:n,unsetAlign:W,closeAriaLabel:L,closeButtonReference:U,onCloseButtonKeyDown:X}),_&&r.createElement(d.Separator,{className:S.separator
}),r.createElement(h.PopupContext.Consumer,null,(e=>this._renderChildren(e,p||R)))))))))}}},88601:(e,t,n)=>{"use strict";n.d(t,{Transparency:()=>l});var r=n(50959),o=n(97754),a=n(54368),i=n(19625),s=n(58510);function l(e){const{value:t,disabled:n,onChange:l,className:c}=e;return r.createElement("div",{className:o(s.wrap,c,{[s.disabled]:n})},r.createElement(a.Opacity,{hideInput:!0,color:i.colorsPalette["color-tv-blue-500"],opacity:1-t/100,onChange:function(e){n||l(100-100*e)},disabled:n}))}},54368:(e,t,n)=>{"use strict";n.d(t,{Opacity:()=>c});var r=n(50959),o=n(97754),a=n(50151),i=n(9859),s=n(68335),l=n(3252);class c extends r.PureComponent{constructor(e){super(e),this._container=null,this._pointer=null,this._raf=null,this._refContainer=e=>{this._container=e},this._refPointer=e=>{this._pointer=e},this._handlePosition=e=>{null===this._raf&&(this._raf=requestAnimationFrame((()=>{const t=(0,a.ensureNotNull)(this._container),n=(0,a.ensureNotNull)(this._pointer),r=t.getBoundingClientRect(),o=n.offsetWidth,s=e.clientX-o/2-r.left,l=(0,i.clamp)(s/(r.width-o),0,1);this.setState({inputOpacity:Math.round(100*l).toString()}),this.props.onChange(l),this._raf=null})))},this._onSliderClick=e=>{this._handlePosition(e.nativeEvent),this._dragSubscribe()},this._mouseUp=e=>{this.setState({isPointerDragged:!1}),this._dragUnsubscribe(),this._handlePosition(e)},this._mouseMove=e=>{this.setState({isPointerDragged:!0}),this._handlePosition(e)},this._onTouchStart=e=>{this._handlePosition(e.nativeEvent.touches[0])},this._handleTouch=e=>{this.setState({isPointerDragged:!0}),this._handlePosition(e.nativeEvent.touches[0])},this._handleTouchEnd=()=>{this.setState({isPointerDragged:!1})},this._handleInput=e=>{const t=e.currentTarget.value,n=Number(t)/100;this.setState({inputOpacity:t}),Number.isNaN(n)||n>1||this.props.onChange(n)},this._handleKeyDown=e=>{const t=(0,s.hashFromEvent)(e);if(37!==t&&39!==t)return;e.preventDefault();const n=Number(this.state.inputOpacity);37===t&&0!==n&&this._changeOpacity(n-1),39===t&&100!==n&&this._changeOpacity(n+1)},this.state={inputOpacity:Math.round(100*e.opacity).toString(),isPointerDragged:!1}}componentWillUnmount(){null!==this._raf&&(cancelAnimationFrame(this._raf),this._raf=null),this._dragUnsubscribe()}render(){const{color:e,opacity:t,hideInput:n,disabled:a}=this.props,{inputOpacity:i,isPointerDragged:s}=this.state,c={color:e||void 0};return r.createElement("div",{className:l.opacity},r.createElement("div",{className:o(l.opacitySlider,l.accessible),style:c,tabIndex:a?-1:0,ref:this._refContainer,onMouseDown:this._onSliderClick,onTouchStart:this._onTouchStart,onTouchMove:this._handleTouch,onTouchEnd:this._handleTouchEnd,onKeyDown:this._handleKeyDown,"aria-disabled":a},r.createElement("div",{className:l.opacitySliderGradient,style:{backgroundImage:`linear-gradient(90deg, transparent, ${e})`}}),r.createElement("div",{className:l.opacityPointerWrap},r.createElement("div",{className:o(l.pointer,s&&l.dragged),style:{left:100*t+"%"},ref:this._refPointer}))),!n&&r.createElement("div",{className:l.opacityInputWrap
},r.createElement("input",{type:"text",className:l.opacityInput,value:i,onChange:this._handleInput}),r.createElement("span",{className:l.opacityInputPercent},"%")))}_dragSubscribe(){const e=(0,a.ensureNotNull)(this._container).ownerDocument;e&&(e.addEventListener("mouseup",this._mouseUp),e.addEventListener("mousemove",this._mouseMove))}_dragUnsubscribe(){const e=(0,a.ensureNotNull)(this._container).ownerDocument;e&&(e.removeEventListener("mousemove",this._mouseMove),e.removeEventListener("mouseup",this._mouseUp))}_changeOpacity(e){this.setState({inputOpacity:e.toString()}),this.props.onChange(e/100)}}},90692:(e,t,n)=>{"use strict";n.d(t,{MatchMedia:()=>o});var r=n(50959);class o extends r.PureComponent{constructor(e){super(e),this._handleChange=()=>{this.forceUpdate()},this.state={query:window.matchMedia(this.props.rule)}}componentDidMount(){this._subscribe(this.state.query)}componentDidUpdate(e,t){this.state.query!==t.query&&(this._unsubscribe(t.query),this._subscribe(this.state.query))}componentWillUnmount(){this._unsubscribe(this.state.query)}render(){return this.props.children(this.state.query.matches)}static getDerivedStateFromProps(e,t){return e.rule!==t.query.media?{query:window.matchMedia(e.rule)}:null}_subscribe(e){e.addEventListener("change",this._handleChange)}_unsubscribe(e){e.removeEventListener("change",this._handleChange)}}},1109:(e,t,n)=>{"use strict";n.d(t,{Separator:()=>i});var r=n(50959),o=n(97754),a=n(47625);function i(e){return r.createElement("div",{className:o(a.separator,e.className)})}},63932:(e,t,n)=>{"use strict";n.d(t,{Spinner:()=>i});var r=n(50959),o=n(97754),a=n(58096);n(7727);function i(e){const t=o(e.className,"tv-spinner","tv-spinner--shown",`tv-spinner--size_${a.spinnerSizeMap[e.size||a.DEFAULT_SIZE]}`);return r.createElement("div",{className:t,style:e.style,role:"progressbar"})}},80256:e=>{e.exports={dialog:"dialog-Rg1Vw_8R",transparencyTitle:"transparencyTitle-Rg1Vw_8R",transparencyControl:"transparencyControl-Rg1Vw_8R",transparencyWrap:"transparencyWrap-Rg1Vw_8R",column:"column-Rg1Vw_8R",dropzone:"dropzone-Rg1Vw_8R",content:"content-Rg1Vw_8R"}},14653:(e,t,n)=>{"use strict";n.r(t),n.d(t,{renderImageDialog:()=>S});var r=n(50959),o=n(97754),a=n.n(o),i=n(11542),s=n(88601),l=n(3503),c=n(50182),u=n(9745),d=n(63932),p=n(33849),m=n.n(p);const h=i.t(null,void 0,n(18916));function g(e,t=null,o,a,s,l){const[c,u]=(0,r.useState)({imgUrl:t,error:null,isLoading:!1,isDragging:!1});return(0,r.useEffect)((()=>{null===t&&null!==c.imgUrl&&u((e=>({...e,imgUrl:null}))),null!==t&&null===c.imgUrl&&u((e=>({...e,imgUrl:t})))}),[t]),(0,r.useEffect)((()=>{const t=function(e,t,r){let o;const{type:a,autoProcessQueue:s=!0,withCredentials:l=!1,maxFileSize:c=.1,maxSizeLabel:u="100KB",...d}=r,p=new(m())(e,{url:"/upload-image/",maxFiles:1,maxFilesize:c,paramName:"file",uploadMultiple:!1,acceptedFiles:"image/jpg,image/jpeg,image/png,image",dictDefaultMessage:i.t(null,void 0,n(32910)),previewTemplate:"<div>",dictFileTooBig:i.t(null,{replace:{value:u}},n(93738)),dictResponseError:h,
dictInvalidFileType:i.t(null,void 0,n(16992)),params:a?{type:a,meta:"{}"}:void 0,autoProcessQueue:s,withCredentials:l,...d});return p.on("addedfile",(e=>{o&&p.removeFile(o),o=e,t.onAddedFile()})),p.on("thumbnail",(()=>{t.onQueuedFile&&t.onQueuedFile(o,p)})),p.on("success",((e,n)=>{t.onSuccess(e,n)})),p.on("error",((e,n)=>{t.onError(n)})),p.on("sending",((e,n,r)=>{t.onSending&&t.onSending(e,n,r)})),p.on("canceled",(e=>{t.onError(h)})),p}(e.current,{onAddedFile:()=>u({error:null,imgUrl:null,isLoading:!0,isDragging:!1}),onSuccess:(e,t)=>{a&&a(t);const n=t?t.url:URL.createObjectURL(e);u({error:null,imgUrl:n,isLoading:!1,isDragging:!1})},onError:e=>u({error:e,imgUrl:null,isLoading:!1,isDragging:!1}),onQueuedFile:s,onSending:l},o);return t.on("dragenter",(()=>{c.isDragging||u((e=>({...e,isDragging:!0})))})),t.on("drop",(()=>{u((e=>({...e,isDragging:!1})))})),t.on("dragleave",(()=>{u((e=>({...e,isDragging:!1})))})),()=>t.destroy()}),[]),[c.imgUrl,c.error,c.isLoading,c.isDragging]}var f=n(93544),b=n(39916);function v(e){const{onFileUpload:t,onRemove:a,maxSizeLabel:s="100KB",className:l,imgUrl:c,onQueuedFile:p,onSending:m,customErrorMessage:h,...v}=e,C=(0,r.useRef)(null),y={...v,maxSizeLabel:s},[x,E,w,_]=g(C,c,y,t,p,m);return r.createElement("div",{className:o(b.container,l)},w?r.createElement(d.Spinner,null):r.createElement(r.Fragment,null,!x||h?r.createElement("span",{className:b.textBlock},function(e){return Boolean(e)?r.createElement("span",{className:o(b.textBlockHeadline,b.textBlockError)},e):r.createElement("span",{className:b.textBlockHeadline},i.t(null,void 0,n(32910)))}(null!=h?h:E),r.createElement("span",null,i.t(null,void 0,n(46280))),r.createElement("span",null,i.t(null,{replace:{value:s}},n(7654)))):r.createElement("img",{className:b.img,src:x})),_&&r.createElement("div",{className:b.backdrop},r.createElement("p",{className:b.mainText},i.t(null,void 0,n(98918)))),r.createElement("div",{ref:C,className:b.dropzone}),x&&a&&r.createElement(u.Icon,{className:b.remove,icon:f,onClick:a}))}var C=n(28124),y=n(80256);const x=i.t(null,void 0,n(68065)),E=i.t(null,void 0,n(19788));let w=null,_=null;function S(e){w||(w=document.createElement("div"));const t=()=>{null!==w&&(null==_||_.unmount(),_=null,w=null)},n={onConfirm:t=>{e.onConfirm(t),e.onClose()},onClose:e.onClose},o=r.createElement(B,{...n});return _?(_.render(o),t):(_=(0,C.createReactRoot)(o,w),t)}const N={"Cache-Control":void 0,"X-Requested-With":void 0};function B(e){const{onConfirm:t,onClose:o}=e,[u,d]=(0,r.useState)(0),[p,m]=(0,r.useState)(void 0),[h,g]=(0,r.useState)(),f=(0,r.useRef)(null),b=(0,r.useRef)(null),C=(0,r.useRef)(null);return r.createElement(c.AdaptiveConfirmDialog,{dataName:"create-image-drawing-dialog",title:x,isOpened:!0,onSubmit:B,onCancel:o,onClickOutside:function(){B(),o()},onClose:o,render:function(){return r.createElement("div",{className:y.content},r.createElement(v,{className:y.dropzone,imgUrl:p,onSending:S,onQueuedFile:w,onFileUpload:_,thumbnailWidth:800,thumbnailHeight:600,autoProcessQueue:!1,headers:N,customErrorMessage:h,
maxSizeLabel:(0,l.getMaxImageSizeLabel)(),maxFileSize:(0,l.getMaxImageSizeInBytes)()/1e3,trackProgress:!1}),r.createElement("div",{className:y.transparencyWrap},r.createElement("span",{className:a()(y.transparencyTitle,y.column)},E),r.createElement(s.Transparency,{className:a()(y.transparencyControl,y.column),value:u,onChange:d,disabled:!1})))},submitOnEnterKey:!1,wrapperClassName:y.dialog});async function w(e,t){if(!e)return;if(g(void 0),!await(0,l.checkImageSize)(e))return g(i.t(null,void 0,n(73007))),t.cancelUpload(e),void m(void 0);const r=URL.createObjectURL(e);C.current=r,(0,l.generateLink)(e).catch((()=>(t.cancelUpload(e),m(void 0),null))).then((e=>{if(!e||C.current!==r)return;const{data:n,filepath:o}=e,{url:a}=n;t.options.url=a,f.current=e.data.fields,b.current=a+o,t.processQueue()}))}function _(){b.current&&C.current&&m(b.current)}function S(e,t,n){if(!f.current)return;const r=f.current;for(const e of Object.keys(r))n.append(e,r[e]);f.current=null}function B(){p&&C.current?(h&&g(void 0),t({blobUrl:C.current,url:Promise.resolve(p),transparency:u})):g(i.t(null,void 0,n(49580)))}}},17105:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path stroke="currentColor" stroke-width="1.2" d="m1.5 1.5 21 21m0-21-21 21"/></svg>'},15130:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path stroke="currentColor" stroke-width="1.2" d="m1.5 1.5 15 15m0-15-15 15"/></svg>'},38822:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 14 14" width="14" height="14"><path stroke="currentColor" stroke-width="1.2" d="m1.5 1.5 11 11m0-11-11 11"/></svg>'},63346:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" width="12" height="12"><path stroke="currentColor" stroke-width="1.2" d="m1.5 1.5 9 9m0-9-9 9"/></svg>'},34983:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 10 10" width="10" height="10"><path stroke="currentColor" stroke-width="1.2" d="m1.5 1.5 7 7m0-7-7 7"/></svg>'}}]);