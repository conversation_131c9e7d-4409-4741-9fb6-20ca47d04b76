(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[2312],{53310:e=>{e.exports={en:["Re"],hu_HU:["Re"]}},94073:e=>{e.exports={en:["A"],hu_HU:["A"]}},66384:e=>{e.exports={en:["L"],hu_HU:["L"]}},85119:e=>{e.exports={en:["Dark"],hu_HU:["Dark"]}},96870:e=>{e.exports={en:["Light"],hu_HU:["Light"]}},85886:e=>{e.exports={en:["d"],hu_HU:["n"]}},44634:e=>{e.exports={en:["h"],hu_HU:["ó"]}},5977:e=>{e.exports={en:["m"],hu_HU:["hó"]}},21492:e=>{e.exports={en:["s"],hu_HU:["s"]}},97559:e=>{e.exports={en:["{title} copy"],hu_HU:["{title} copy"]}},38691:e=>{e.exports={en:["D"],hu_HU:["D"]}},77995:e=>{e.exports={en:["M"],hu_HU:["M"]}},93934:e=>{e.exports={en:["R"],hu_HU:["R"]}},82901:e=>{e.exports={en:["T"],hu_HU:["T"]}},7408:e=>{e.exports={en:["W"],hu_HU:["W"]}},38048:e=>{e.exports={en:["h"],hu_HU:["h"]}},68430:e=>{e.exports={en:["m"],hu_HU:["m"]}},68823:e=>{e.exports={en:["s"],hu_HU:["s"]}},2696:e=>{e.exports={en:["C"],hu_HU:["Z"]}},43253:e=>{e.exports={en:["H"],hu_HU:["Max"]}},61372:e=>{e.exports={en:["HL2"],hu_HU:["HL2"]}},55096:e=>{e.exports={en:["HLC3"],hu_HU:["HLC3"]}},94174:e=>{e.exports={en:["OHLC4"],hu_HU:["OHLC4"]}},89923:e=>{e.exports={en:["L"],hu_HU:["Min"]}},46728:e=>{e.exports={en:["O"],hu_HU:["Ny"]}},32856:e=>{e.exports=Object.create(null),e.exports.Close_input={en:["Close"],hu_HU:["Zárás"]},e.exports.Back_input={en:["Back"],hu_HU:["Back"]},e.exports.Minimize_input={en:["Minimize"],hu_HU:["Minimize"]},e.exports["Hull MA_input"]={en:["Hull MA"],hu_HU:["Hull MA"]},e.exports.from_input={en:["from"],hu_HU:["from"]},e.exports.to_input={en:["to"],hu_HU:["to"]},e.exports["{number} item_combobox_input"]={en:["{number} item","{number} items"],hu_HU:["{number} items"]},e.exports.Length_input={en:["Length"],hu_HU:["Hossz"]},e.exports.Plot_input={en:["Plot"],hu_HU:["Plot"]},e.exports.Zero_input={en:["Zero"],hu_HU:["Zero"]},e.exports.Signal_input={en:["Signal"],hu_HU:["Signal"]},e.exports.Long_input={en:["Long"],hu_HU:["Long"]},e.exports.Short_input={en:["Short"],hu_HU:["Short"]},e.exports.UpperLimit_input={en:["UpperLimit"],hu_HU:["UpperLimit"]},e.exports.LowerLimit_input={en:["LowerLimit"],hu_HU:["LowerLimit"]},e.exports.Offset_input={en:["Offset"],hu_HU:["Offset"]},e.exports.length_input={en:["length"],hu_HU:["hossz"]},e.exports.mult_input={en:["mult"],hu_HU:["mult"]},e.exports.short_input={en:["short"],hu_HU:["short"]},e.exports.long_input={en:["long"],hu_HU:["long"]},e.exports.Limit_input={en:["Limit"],hu_HU:["Limit"]},e.exports.Move_input={en:["Move"],hu_HU:["Move"]},e.exports.Value_input={en:["Value"],hu_HU:["Value"]},e.exports.Method_input={en:["Method"],hu_HU:["Method"]},e.exports["Values in status line_input"]={en:["Values in status line"],hu_HU:["Values in status line"]},e.exports["Labels on price scale_input"]={en:["Labels on price scale"],hu_HU:["Labels on price scale"]},e.exports["Accumulation/Distribution_input"]={en:["Accumulation/Distribution"],hu_HU:["Akkumuláció/Disztribúció"]},e.exports.ADR_B_input={en:["ADR_B"],hu_HU:["ADR_B"]},e.exports["Equality Line_input"]={en:["Equality Line"],
hu_HU:["Equality Line"]},e.exports["Window Size_input"]={en:["Window Size"],hu_HU:["Ablakméret"]},e.exports.Sigma_input={en:["Sigma"],hu_HU:["Sigma"]},e.exports["Aroon Up_input"]={en:["Aroon Up"],hu_HU:["Aroon Up"]},e.exports["Aroon Down_input"]={en:["Aroon Down"],hu_HU:["Aroon Down"]},e.exports.Upper_input={en:["Upper"],hu_HU:["Upper"]},e.exports.Lower_input={en:["Lower"],hu_HU:["Lower"]},e.exports.Deviation_input={en:["Deviation"],hu_HU:["Deviation"]},e.exports["Levels Format_input"]={en:["Levels Format"],hu_HU:["Levels Format"]},e.exports["Labels Position_input"]={en:["Labels Position"],hu_HU:["Labels Position"]},e.exports["0 Level Color_input"]={en:["0 Level Color"],hu_HU:["0 Level Color"]},e.exports["0.236 Level Color_input"]={en:["0.236 Level Color"],hu_HU:["0.236 Level Color"]},e.exports["0.382 Level Color_input"]={en:["0.382 Level Color"],hu_HU:["0.382 Level Color"]},e.exports["0.5 Level Color_input"]={en:["0.5 Level Color"],hu_HU:["0.5 Level Color"]},e.exports["0.618 Level Color_input"]={en:["0.618 Level Color"],hu_HU:["0.618 Level Color"]},e.exports["0.65 Level Color_input"]={en:["0.65 Level Color"],hu_HU:["0.65 Level Color"]},e.exports["0.786 Level Color_input"]={en:["0.786 Level Color"],hu_HU:["0.786 Level Color"]},e.exports["1 Level Color_input"]={en:["1 Level Color"],hu_HU:["1 Level Color"]},e.exports["1.272 Level Color_input"]={en:["1.272 Level Color"],hu_HU:["1.272 Level Color"]},e.exports["1.414 Level Color_input"]={en:["1.414 Level Color"],hu_HU:["1.414 Level Color"]},e.exports["1.618 Level Color_input"]={en:["1.618 Level Color"],hu_HU:["1.618 Level Color"]},e.exports["1.65 Level Color_input"]={en:["1.65 Level Color"],hu_HU:["1.65 Level Color"]},e.exports["2.618 Level Color_input"]={en:["2.618 Level Color"],hu_HU:["2.618 Level Color"]},e.exports["2.65 Level Color_input"]={en:["2.65 Level Color"],hu_HU:["2.65 Level Color"]},e.exports["3.618 Level Color_input"]={en:["3.618 Level Color"],hu_HU:["3.618 Level Color"]},e.exports["3.65 Level Color_input"]={en:["3.65 Level Color"],hu_HU:["3.65 Level Color"]},e.exports["4.236 Level Color_input"]={en:["4.236 Level Color"],hu_HU:["4.236 Level Color"]},e.exports["-0.236 Level Color_input"]={en:["-0.236 Level Color"],hu_HU:["-0.236 Level Color"]},e.exports["-0.382 Level Color_input"]={en:["-0.382 Level Color"],hu_HU:["-0.382 Level Color"]},e.exports["-0.618 Level Color_input"]={en:["-0.618 Level Color"],hu_HU:["-0.618 Level Color"]},e.exports["-0.65 Level Color_input"]={en:["-0.65 Level Color"],hu_HU:["-0.65 Level Color"]},e.exports.ADX_input={en:["ADX"],hu_HU:["ADX"]},e.exports["ADX Smoothing_input"]={en:["ADX Smoothing"],hu_HU:["ADX Smoothing"]},e.exports["DI Length_input"]={en:["DI Length"],hu_HU:["DI Length"]},e.exports.Smoothing_input={en:["Smoothing"],hu_HU:["Smoothing"]},e.exports.ATR_input={en:["ATR"],hu_HU:["ATR"]},e.exports.Growing_input={en:["Growing"],hu_HU:["Growing"]},e.exports.Falling_input={en:["Falling"],hu_HU:["Falling"]},e.exports["Color 0_input"]={en:["Color 0"],hu_HU:["Color 0"]},e.exports["Color 1_input"]={en:["Color 1"],hu_HU:["Color 1"]},
e.exports.Source_input={en:["Source"],hu_HU:["Source"]},e.exports.StdDev_input={en:["StdDev"],hu_HU:["StdDev"]},e.exports.Basis_input={en:["Basis"],hu_HU:["Basis"]},e.exports.Median_input={en:["Median"],hu_HU:["Median"]},e.exports["Bollinger Bands %B_input"]={en:["Bollinger Bands %B"],hu_HU:["Bollinger Bands %B"]},e.exports.Overbought_input={en:["Overbought"],hu_HU:["Overbought"]},e.exports.Oversold_input={en:["Oversold"],hu_HU:["Oversold"]},e.exports["Bollinger Bands Width_input"]={en:["Bollinger Bands Width"],hu_HU:["Bollinger Bands Width"]},e.exports["RSI Length_input"]={en:["RSI Length"],hu_HU:["RSI Length"]},e.exports["UpDown Length_input"]={en:["UpDown Length"],hu_HU:["UpDown Length"]},e.exports["ROC Length_input"]={en:["ROC Length"],hu_HU:["ROC Hossz"]},e.exports.MF_input={en:["MF"],hu_HU:["MF"]},e.exports.resolution_input={en:["resolution"],hu_HU:["resolution"]},e.exports["Fast Length_input"]={en:["Fast Length"],hu_HU:["Fast Length"]},e.exports["Slow Length_input"]={en:["Slow Length"],hu_HU:["Slow Length"]},e.exports["Chaikin Oscillator_input"]={en:["Chaikin Oscillator"],hu_HU:["Chaikin Oscillator"]},e.exports.P_input={en:["P"],hu_HU:["P"]},e.exports.X_input={en:["X"],hu_HU:["X"]},e.exports.Q_input={en:["Q"],hu_HU:["Q"]},e.exports.p_input={en:["p"],hu_HU:["p"]},e.exports.x_input={en:["x"],hu_HU:["x"]},e.exports.q_input={en:["q"],hu_HU:["q"]},e.exports.Price_input={en:["Price"],hu_HU:["Price"]},e.exports["Chande MO_input"]={en:["Chande MO"],hu_HU:["Chande MO"]},e.exports["Zero Line_input"]={en:["Zero Line"],hu_HU:["Zero Line"]},e.exports["Color 2_input"]={en:["Color 2"],hu_HU:["Color 2"]},e.exports["Color 3_input"]={en:["Color 3"],hu_HU:["Color 3"]},e.exports["Color 4_input"]={en:["Color 4"],hu_HU:["Color 4"]},e.exports["Color 5_input"]={en:["Color 5"],hu_HU:["Color 5"]},e.exports["Color 6_input"]={en:["Color 6"],hu_HU:["Color 6"]},e.exports["Color 7_input"]={en:["Color 7"],hu_HU:["Color 7"]},e.exports["Color 8_input"]={en:["Color 8"],hu_HU:["Color 8"]},e.exports.CHOP_input={en:["CHOP"],hu_HU:["CHOP"]},e.exports["Upper Band_input"]={en:["Upper Band"],hu_HU:["Upper Band"]},e.exports["Lower Band_input"]={en:["Lower Band"],hu_HU:["Lower Band"]},e.exports.CCI_input={en:["CCI"],hu_HU:["CCI"]},e.exports["Smoothing Line_input"]={en:["Smoothing Line"],hu_HU:["Smoothing Line"]},e.exports["Smoothing Length_input"]={en:["Smoothing Length"],hu_HU:["Smoothing Length"]},e.exports["WMA Length_input"]={en:["WMA Length"],hu_HU:["WMA hosszúság"]},e.exports["Long RoC Length_input"]={en:["Long RoC Length"],hu_HU:["Long RoC Length"]},e.exports["Short RoC Length_input"]={en:["Short RoC Length"],hu_HU:["Short RoC Length"]},e.exports.sym_input={en:["sym"],hu_HU:["sym"]},e.exports.Symbol_input={en:["Symbol"],hu_HU:["Symbol"]},e.exports.Correlation_input={en:["Correlation"],hu_HU:["Correlation"]},e.exports.Period_input={en:["Period"],hu_HU:["Period"]},e.exports.Centered_input={en:["Centered"],hu_HU:["Centered"]},e.exports["Detrended Price Oscillator_input"]={en:["Detrended Price Oscillator"],hu_HU:["Detrended Price Oscillator"]},
e.exports.isCentered_input={en:["isCentered"],hu_HU:["isCentered"]},e.exports.DPO_input={en:["DPO"],hu_HU:["DPO"]},e.exports["ADX smoothing_input"]={en:["ADX smoothing"],hu_HU:["ADX smoothing"]},e.exports["+DI_input"]={en:["+DI"],hu_HU:["+DI"]},e.exports["-DI_input"]={en:["-DI"],hu_HU:["-DI"]},e.exports.DEMA_input={en:["DEMA"],hu_HU:["DEMA"]},e.exports["Multi timeframe_input"]={en:["Multi timeframe"],hu_HU:["Multi timeframe"]},e.exports.Timeframe_input={en:["Timeframe"],hu_HU:["Timeframe"]},e.exports["Wait for timeframe closes_input"]={en:["Wait for timeframe closes"],hu_HU:["Wait for timeframe closes"]},e.exports.Divisor_input={en:["Divisor"],hu_HU:["Divisor"]},e.exports.EOM_input={en:["EOM"],hu_HU:["EOM"]},e.exports["Elder's Force Index_input"]={en:["Elder's Force Index"],hu_HU:["Elder's Force Index"]},e.exports.Percent_input={en:["Percent"],hu_HU:["Percent"]},e.exports.Exponential_input={en:["Exponential"],hu_HU:["Exponential"]},e.exports.Average_input={en:["Average"],hu_HU:["Average"]},e.exports["Upper Percentage_input"]={en:["Upper Percentage"],hu_HU:["Upper Percentage"]},e.exports["Lower Percentage_input"]={en:["Lower Percentage"],hu_HU:["Lower Percentage"]},e.exports.Fisher_input={en:["Fisher"],hu_HU:["Fisher"]},e.exports.Trigger_input={en:["Trigger"],hu_HU:["Trigger"]},e.exports.Level_input={en:["Level"],hu_HU:["Level"]},e.exports["Trader EMA 1 length_input"]={en:["Trader EMA 1 length"],hu_HU:["Trader EMA 1 length"]},e.exports["Trader EMA 2 length_input"]={en:["Trader EMA 2 length"],hu_HU:["Trader EMA 2 length"]},e.exports["Trader EMA 3 length_input"]={en:["Trader EMA 3 length"],hu_HU:["Trader EMA 3 length"]},e.exports["Trader EMA 4 length_input"]={en:["Trader EMA 4 length"],hu_HU:["Trader EMA 4 length"]},e.exports["Trader EMA 5 length_input"]={en:["Trader EMA 5 length"],hu_HU:["Trader EMA 5 length"]},e.exports["Trader EMA 6 length_input"]={en:["Trader EMA 6 length"],hu_HU:["Trader EMA 6 length"]},e.exports["Investor EMA 1 length_input"]={en:["Investor EMA 1 length"],hu_HU:["Investor EMA 1 length"]},e.exports["Investor EMA 2 length_input"]={en:["Investor EMA 2 length"],hu_HU:["Investor EMA 2 length"]},e.exports["Investor EMA 3 length_input"]={en:["Investor EMA 3 length"],hu_HU:["Investor EMA 3 length"]},e.exports["Investor EMA 4 length_input"]={en:["Investor EMA 4 length"],hu_HU:["Investor EMA 4 length"]},e.exports["Investor EMA 5 length_input"]={en:["Investor EMA 5 length"],hu_HU:["Investor EMA 5 length"]},e.exports["Investor EMA 6 length_input"]={en:["Investor EMA 6 length"],hu_HU:["Investor EMA 6 length"]},e.exports.HV_input={en:["HV"],hu_HU:["HV"]},e.exports["Conversion Line Periods_input"]={en:["Conversion Line Periods"],hu_HU:["Conversion Line Periods"]},e.exports["Base Line Periods_input"]={en:["Base Line Periods"],hu_HU:["Base Line Periods"]},e.exports["Lagging Span_input"]={en:["Lagging Span"],hu_HU:["Lagging Span"]},e.exports["Conversion Line_input"]={en:["Conversion Line"],hu_HU:["Conversion Line"]},e.exports["Base Line_input"]={en:["Base Line"],hu_HU:["Base Line"]},
e.exports["Leading Span A_input"]={en:["Leading Span A"],hu_HU:["Lead 1"]},e.exports["Leading Span B_input"]={en:["Leading Span B"],hu_HU:["Lead 2"]},e.exports["Plots Background_input"]={en:["Plots Background"],hu_HU:["Plots Background"]},e.exports["yay Color 0_input"]={en:["yay Color 0"],hu_HU:["yay Color 0"]},e.exports["yay Color 1_input"]={en:["yay Color 1"],hu_HU:["yay Color 1"]},e.exports.Multiplier_input={en:["Multiplier"],hu_HU:["Multiplier"]},e.exports["Bands style_input"]={en:["Bands style"],hu_HU:["Bands style"]},e.exports.Middle_input={en:["Middle"],hu_HU:["Middle"]},e.exports.useTrueRange_input={en:["useTrueRange"],hu_HU:["useTrueRange"]},e.exports.ROCLen1_input={en:["ROCLen1"],hu_HU:["ROCLen1"]},e.exports.ROCLen2_input={en:["ROCLen2"],hu_HU:["ROCLen2"]},e.exports.ROCLen3_input={en:["ROCLen3"],hu_HU:["ROCLen3"]},e.exports.ROCLen4_input={en:["ROCLen4"],hu_HU:["ROCLen4"]},e.exports.SMALen1_input={en:["SMALen1"],hu_HU:["SMALen1"]},e.exports.SMALen2_input={en:["SMALen2"],hu_HU:["SMALen2"]},e.exports.SMALen3_input={en:["SMALen3"],hu_HU:["SMALen3"]},e.exports.SMALen4_input={en:["SMALen4"],hu_HU:["SMALen4"]},e.exports.SigLen_input={en:["SigLen"],hu_HU:["SigLen"]},e.exports.KST_input={en:["KST"],hu_HU:["KST"]},e.exports.Sig_input={en:["Sig"],hu_HU:["Sig"]},e.exports.roclen1_input={en:["roclen1"],hu_HU:["roclen1"]},e.exports.roclen2_input={en:["roclen2"],hu_HU:["roclen2"]},e.exports.roclen3_input={en:["roclen3"],hu_HU:["roclen3"]},e.exports.roclen4_input={en:["roclen4"],hu_HU:["roclen4"]},e.exports.smalen1_input={en:["smalen1"],hu_HU:["smalen1"]},e.exports.smalen2_input={en:["smalen2"],hu_HU:["smalen2"]},e.exports.smalen3_input={en:["smalen3"],hu_HU:["smalen3"]},e.exports.smalen4_input={en:["smalen4"],hu_HU:["smalen4"]},e.exports.siglen_input={en:["siglen"],hu_HU:["siglen"]},e.exports["Upper Deviation_input"]={en:["Upper Deviation"],hu_HU:["Upper Deviation"]},e.exports["Lower Deviation_input"]={en:["Lower Deviation"],hu_HU:["Lower Deviation"]},e.exports["Use Upper Deviation_input"]={en:["Use Upper Deviation"],hu_HU:["Use Upper Deviation"]},e.exports["Use Lower Deviation_input"]={en:["Use Lower Deviation"],hu_HU:["Use Lower Deviation"]},e.exports.Count_input={en:["Count"],hu_HU:["Count"]},e.exports.Crosses_input={en:["Crosses"],hu_HU:["Crosses"]},e.exports.MOM_input={en:["MOM"],hu_HU:["MOM"]},e.exports.MA_input={en:["MA"],hu_HU:["MA"]},e.exports["Length EMA_input"]={en:["Length EMA"],hu_HU:["EMA Hossz"]},e.exports["Length MA_input"]={en:["Length MA"],hu_HU:["MA Hossz"]},e.exports["Fast length_input"]={en:["Fast length"],hu_HU:["Fast length"]},e.exports["Slow length_input"]={en:["Slow length"],hu_HU:["Slow length"]},e.exports["Signal smoothing_input"]={en:["Signal smoothing"],hu_HU:["Signal smoothing"]},e.exports["Simple ma(oscillator)_input"]={en:["Simple ma(oscillator)"],hu_HU:["Simple ma(oscillator)"]},e.exports["Simple ma(signal line)_input"]={en:["Simple ma(signal line)"],hu_HU:["Simple ma(signal line)"]},e.exports.Histogram_input={en:["Histogram"],hu_HU:["Histogram"]},e.exports.MACD_input={en:["MACD"],
hu_HU:["MACD"]},e.exports.fastLength_input={en:["fastLength"],hu_HU:["fastLength"]},e.exports.slowLength_input={en:["slowLength"],hu_HU:["slowLength"]},e.exports.signalLength_input={en:["signalLength"],hu_HU:["signalLength"]},e.exports.NV_input={en:["NV"],hu_HU:["NV"]},e.exports.OnBalanceVolume_input={en:["OnBalanceVolume"],hu_HU:["OnBalanceVolume"]},e.exports.Start_input={en:["Start"],hu_HU:["Kezdés"]},e.exports.Increment_input={en:["Increment"],hu_HU:["Increment"]},e.exports["Max value_input"]={en:["Max value"],hu_HU:["Max value"]},e.exports.ParabolicSAR_input={en:["ParabolicSAR"],hu_HU:["ParabolicSAR"]},e.exports.start_input={en:["start"],hu_HU:["start"]},e.exports.increment_input={en:["increment"],hu_HU:["increment"]},e.exports.maximum_input={en:["maximum"],hu_HU:["maximum"]},e.exports["Short length_input"]={en:["Short length"],hu_HU:["Short length"]},e.exports["Long length_input"]={en:["Long length"],hu_HU:["Long length"]},e.exports.OSC_input={en:["OSC"],hu_HU:["OSC"]},e.exports.shortlen_input={en:["shortlen"],hu_HU:["shortlen"]},e.exports.longlen_input={en:["longlen"],hu_HU:["longlen"]},e.exports.PVT_input={en:["PVT"],hu_HU:["PVT"]},e.exports.ROC_input={en:["ROC"],hu_HU:["ROC"]},e.exports.RSI_input={en:["RSI"],hu_HU:["RSI"]},e.exports.RVGI_input={en:["RVGI"],hu_HU:["RVGI"]},e.exports.RVI_input={en:["RVI"],hu_HU:["RVI"]},e.exports["Long period_input"]={en:["Long period"],hu_HU:["Long period"]},e.exports["Short period_input"]={en:["Short period"],hu_HU:["Short period"]},e.exports["Signal line period_input"]={en:["Signal line period"],hu_HU:["Signal line period"]},e.exports.SMI_input={en:["SMI"],hu_HU:["SMI"]},e.exports["SMI Ergodic Oscillator_input"]={en:["SMI Ergodic Oscillator"],hu_HU:["SMI Ergodic Oscillator"]},e.exports.Indicator_input={en:["Indicator"],hu_HU:["Indicator"]},e.exports.Oscillator_input={en:["Oscillator"],hu_HU:["Oscillator"]},e.exports.K_input={en:["K"],hu_HU:["K"]},e.exports.D_input={en:["D"],hu_HU:["D"]},e.exports.smoothK_input={en:["smoothK"],hu_HU:["smoothK"]},e.exports.smoothD_input={en:["smoothD"],hu_HU:["smoothD"]},e.exports["%K_input"]={en:["%K"],hu_HU:["%K"]},e.exports["%D_input"]={en:["%D"],hu_HU:["%D"]},e.exports["Stochastic Length_input"]={en:["Stochastic Length"],hu_HU:["Stochastic Length"]},e.exports["RSI Source_input"]={en:["RSI Source"],hu_HU:["RSI Source"]},e.exports.lengthRSI_input={en:["lengthRSI"],hu_HU:["lengthRSI"]},e.exports.lengthStoch_input={en:["lengthStoch"],hu_HU:["lengthStoch"]},e.exports.TRIX_input={en:["TRIX"],hu_HU:["TRIX"]},e.exports.TEMA_input={en:["TEMA"],hu_HU:["TEMA"]},e.exports["Long Length_input"]={en:["Long Length"],hu_HU:["Long Length"]},e.exports["Short Length_input"]={en:["Short Length"],hu_HU:["Short Length"]},e.exports["Signal Length_input"]={en:["Signal Length"],hu_HU:["Signal Length"]},e.exports.Length1_input={en:["Length1"],hu_HU:["Length1"]},e.exports.Length2_input={en:["Length2"],hu_HU:["Length2"]},e.exports.Length3_input={en:["Length3"],hu_HU:["Hossz3"]},e.exports.length7_input={en:["length7"],hu_HU:["length7"]},e.exports.length14_input={
en:["length14"],hu_HU:["length14"]},e.exports.length28_input={en:["length28"],hu_HU:["length28"]},e.exports.UO_input={en:["UO"],hu_HU:["UO"]},e.exports.VWMA_input={en:["VWMA"],hu_HU:["VWMA"]},e.exports.len_input={en:["len"],hu_HU:["len"]},e.exports["VI +_input"]={en:["VI +"],hu_HU:["VI +"]},e.exports["VI -_input"]={en:["VI -"],hu_HU:["VI -"]},e.exports["%R_input"]={en:["%R"],hu_HU:["%R"]},e.exports["Jaw Length_input"]={en:["Jaw Length"],hu_HU:["Jaw Length"]},e.exports["Teeth Length_input"]={en:["Teeth Length"],hu_HU:["Teeth Length"]},e.exports["Lips Length_input"]={en:["Lips Length"],hu_HU:["Lips Length"]},e.exports.Jaw_input={en:["Jaw"],hu_HU:["Jaw"]},e.exports.Teeth_input={en:["Teeth"],hu_HU:["Teeth"]},e.exports.Lips_input={en:["Lips"],hu_HU:["Lips"]},e.exports["Jaw Offset_input"]={en:["Jaw Offset"],hu_HU:["Jaw Offset"]},e.exports["Teeth Offset_input"]={en:["Teeth Offset"],hu_HU:["Teeth Offset"]},e.exports["Lips Offset_input"]={en:["Lips Offset"],hu_HU:["Lips Offset"]},e.exports["Down fractals_input"]={en:["Down fractals"],hu_HU:["Down fractals"]},e.exports["Up fractals_input"]={en:["Up fractals"],hu_HU:["Up fractals"]},e.exports.Periods_input={en:["Periods"],hu_HU:["Periods"]},e.exports.Shapes_input={en:["Shapes"],hu_HU:["Shapes"]},e.exports["show MA_input"]={en:["show MA"],hu_HU:["show MA"]},e.exports["MA Length_input"]={en:["MA Length"],hu_HU:["MA hosszúság"]},e.exports["Color based on previous close_input"]={en:["Color based on previous close"],hu_HU:["Szín az előző záróár alapján"]},e.exports["Rows Layout_input"]={en:["Rows Layout"],hu_HU:["Rows Layout"]},e.exports["Row Size_input"]={en:["Row Size"],hu_HU:["Row Size"]},e.exports.Volume_input={en:["Volume"],hu_HU:["Volume"]},e.exports["Value Area volume_input"]={en:["Value Area volume"],hu_HU:["Value Area volume"]},e.exports["Extend Right_input"]={en:["Extend Right"],hu_HU:["Extend Right"]},e.exports["Extend POC Right_input"]={en:["Extend POC Right"],hu_HU:["Extend POC Right"]},e.exports["Extend VAH Right_input"]={en:["Extend VAH Right"],hu_HU:["Extend VAH Right"]},e.exports["Extend VAL Right_input"]={en:["Extend VAL Right"],hu_HU:["Extend VAL Right"]},e.exports["Value Area Volume_input"]={en:["Value Area Volume"],hu_HU:["Value Area Volume"]},e.exports.Placement_input={en:["Placement"],hu_HU:["Placement"]},e.exports.POC_input={en:["POC"],hu_HU:["POC"]},e.exports["Developing Poc_input"]={en:["Developing Poc"],hu_HU:["Developing Poc"]},e.exports["Up Volume_input"]={en:["Up Volume"],hu_HU:["Up Volume"]},e.exports["Down Volume_input"]={en:["Down Volume"],hu_HU:["Down Volume"]},e.exports["Value Area_input"]={en:["Value Area"],hu_HU:["Value Area"]},e.exports["Histogram Box_input"]={en:["Histogram Box"],hu_HU:["Histogram Box"]},e.exports["Value Area Up_input"]={en:["Value Area Up"],hu_HU:["Value Area Up"]},e.exports["Value Area Down_input"]={en:["Value Area Down"],hu_HU:["Value Area Down"]},e.exports["Number Of Rows_input"]={en:["Number Of Rows"],hu_HU:["Number Of Rows"]},e.exports["Ticks Per Row_input"]={en:["Ticks Per Row"],hu_HU:["Ticks Per Row"]},
e.exports["Up/Down_input"]={en:["Up/Down"],hu_HU:["Up/Down"]},e.exports.Total_input={en:["Total"],hu_HU:["Total"]},e.exports.Delta_input={en:["Delta"],hu_HU:["Delta"]},e.exports.Bar_input={en:["Bar"],hu_HU:["Bar"]},e.exports.Day_input={en:["Day"],hu_HU:["Day"]},e.exports["Deviation (%)_input"]={en:["Deviation (%)"],hu_HU:["Deviation (%)"]},e.exports.Depth_input={en:["Depth"],hu_HU:["Depth"]},e.exports["Extend to last bar_input"]={en:["Extend to last bar"],hu_HU:["Extend to last bar"]},e.exports.Simple_input={en:["Simple"],hu_HU:["Simple"]},e.exports.Weighted_input={en:["Weighted"],hu_HU:["Weighted"]},e.exports["Wilder's Smoothing_input"]={en:["Wilder's Smoothing"],hu_HU:["Wilder's Smoothing"]},e.exports["1st Period_input"]={en:["1st Period"],hu_HU:["1st Period"]},e.exports["2nd Period_input"]={en:["2nd Period"],hu_HU:["2nd Period"]},e.exports["3rd Period_input"]={en:["3rd Period"],hu_HU:["3rd Period"]},e.exports["4th Period_input"]={en:["4th Period"],hu_HU:["4th Period"]},e.exports["5th Period_input"]={en:["5th Period"],hu_HU:["5th Period"]},e.exports["6th Period_input"]={en:["6th Period"],hu_HU:["6th Period"]},e.exports["Rate of Change Lookback_input"]={en:["Rate of Change Lookback"],hu_HU:["Rate of Change Lookback"]},e.exports["Instrument 1_input"]={en:["Instrument 1"],hu_HU:["Instrument 1"]},e.exports["Instrument 2_input"]={en:["Instrument 2"],hu_HU:["Instrument 2"]},e.exports["Rolling Period_input"]={en:["Rolling Period"],hu_HU:["Rolling Period"]},e.exports["Standard Errors_input"]={en:["Standard Errors"],hu_HU:["Standard Errors"]},e.exports["Averaging Periods_input"]={en:["Averaging Periods"],hu_HU:["Averaging Periods"]},e.exports["Days Per Year_input"]={en:["Days Per Year"],hu_HU:["Days Per Year"]},e.exports["Market Closed Percentage_input"]={en:["Market Closed Percentage"],hu_HU:["Market Closed Percentage"]},e.exports["ATR Mult_input"]={en:["ATR Mult"],hu_HU:["ATR Mult"]},e.exports.VWAP_input={en:["VWAP"],hu_HU:["VWAP"]},e.exports["Anchor Period_input"]={en:["Anchor Period"],hu_HU:["Anchor Period"]},e.exports.Session_input={en:["Session"],hu_HU:["Session"]},e.exports.Week_input={en:["Week"],hu_HU:["Week"]},e.exports.Month_input={en:["Month"],hu_HU:["Month"]},e.exports.Year_input={en:["Year"],hu_HU:["Year"]},e.exports.Decade_input={en:["Decade"],hu_HU:["Decade"]},e.exports.Century_input={en:["Century"],hu_HU:["Century"]},e.exports.Sessions_input={en:["Sessions"],hu_HU:["Sessions"]},e.exports["Each (pre-market, market, post-market)_input"]={en:["Each (pre-market, market, post-market)"],hu_HU:["Each (pre-market, market, post-market)"]},e.exports["Pre-market only_input"]={en:["Pre-market only"],hu_HU:["Pre-market only"]},e.exports["Market only_input"]={en:["Market only"],hu_HU:["Market only"]},e.exports["Post-market only_input"]={en:["Post-market only"],hu_HU:["Post-market only"]},e.exports["Main chart symbol_input"]={en:["Main chart symbol"],hu_HU:["Main chart symbol"]},e.exports["Another symbol_input"]={en:["Another symbol"],hu_HU:["Another symbol"]},e.exports.Line_input={en:["Line"],hu_HU:["Vonal"]},
e.exports["Nothing selected_combobox_input"]={en:["Nothing selected"],hu_HU:["Nothing selected"]},e.exports["All items_combobox_input"]={en:["All items"],hu_HU:["All items"]},e.exports.Cancel_input={en:["Cancel"],hu_HU:["Cancel"]},e.exports.Open_input={en:["Open"],hu_HU:["Nyitás"]},e.exports.MM_month_input={en:["MM"],hu_HU:["MM"]},e.exports.YY_year_input={en:["YY"],hu_HU:["YY"]},e.exports.Style_input={en:["Style"],hu_HU:["Style"]},e.exports["Box size assignment method_input"]={en:["Box size assignment method"],hu_HU:["Box size assignment method"]},e.exports["Color bars based on previous close_input"]={en:["Color bars based on previous close"],hu_HU:["Color bars based on previous close"]},e.exports.Candles_input={en:["Candles"],hu_HU:["Candles"]},e.exports.Borders_input={en:["Borders"],hu_HU:["Borders"]},e.exports.Wick_input={en:["Wick"],hu_HU:["Wick"]},e.exports["HLC bars_input"]={en:["HLC bars"],hu_HU:["HLC bars"]},e.exports["Price source_input"]={en:["Price source"],hu_HU:["Price source"]},e.exports.Type_input={en:["Type"],hu_HU:["Type"]},e.exports["Show real prices on price scale (instead of Heikin-Ashi price)_input"]={en:["Show real prices on price scale (instead of Heikin-Ashi price)"],hu_HU:["Valódi árak mutatása az ártáblázaton (a Heikin-Ashi árak helyett)"]},e.exports["Up bars_input"]={en:["Up bars"],hu_HU:["Up bars"]},e.exports["Down bars_input"]={en:["Down bars"],hu_HU:["Down bars"]},e.exports["Projection up bars_input"]={en:["Projection up bars"],hu_HU:["Projection up bars"]},e.exports["Projection down bars_input"]={en:["Projection down bars"],hu_HU:["Projection down bars"]},e.exports["Projection up color_input"]={en:["Projection up color"],hu_HU:["Projection up color"]},e.exports["Projection down color_input"]={en:["Projection down color"],hu_HU:["Projection down color"]},e.exports.Fill_input={en:["Fill"],hu_HU:["Fill"]},e.exports["Up color_input"]={en:["Up color"],hu_HU:["Up color"]},e.exports["Down color_input"]={en:["Down color"],hu_HU:["Down color"]},e.exports.Traditional_input={en:["Traditional"],hu_HU:["Traditional"]},e.exports.PercentageLTP_input={en:["PercentageLTP"],hu_HU:["PercentageLTP"]},e.exports["Box size_input"]={en:["Box size"],hu_HU:["Box size"]},e.exports["Number of line_input"]={en:["Number of line"],hu_HU:["Number of line"]},e.exports["ATR length_input"]={en:["ATR length"],hu_HU:["ATR Hossz"]},e.exports.Percentage_input={en:["Percentage"],hu_HU:["Percentage"]},e.exports["Reversal amount_input"]={en:["Reversal amount"],hu_HU:["Reversal amount"]},e.exports["Phantom bars_input"]={en:["Phantom bars"],hu_HU:["Phantom bars"]},e.exports["One step back building_input"]={en:["One step back building"],hu_HU:["One step back building"]},e.exports.Wicks_input={en:["Wicks"],hu_HU:["Wicks"]},e.exports.Range_input={en:["Range"],hu_HU:["Range"]},e.exports.All_input={en:["All"],hu_HU:["All"]},e.exports.Custom_input={en:["Custom"],hu_HU:["Custom"]},e.exports["Leading Span Periods_input"]={en:["Leading Span Periods"],hu_HU:["Lagging Span 2 Periods"]},e.exports["Lagging Span Periods_input"]={
en:["Lagging Span Periods"],hu_HU:["Lagging Span Periods"]},e.exports["Leading Shift Periods_input"]={en:["Leading Shift Periods"],hu_HU:["Leading Shift Periods"]},e.exports["ATR({atrValue})_input"]={en:["ATR({atrValue})"],hu_HU:["ATR({atrValue})"]},e.exports["PercentageLTP({percentageLTPValue}%)_input"]={en:["PercentageLTP({percentageLTPValue}%)"],hu_HU:["PercentageLTP({percentageLTPValue}%)"]}},50873:e=>{e.exports={en:["ATR({atrValue})"],hu_HU:["ATR({atrValue})"]}},28037:e=>{e.exports={en:["PercentageLTP({percentageLTPValue}%)"],hu_HU:["PercentageLTP({percentageLTPValue}%)"]}},40566:e=>{e.exports={en:["Traditional"],hu_HU:["Traditional"]}},75163:e=>{e.exports={en:["Invert scale"],hu_HU:["Invert Scale"]}},35210:e=>{e.exports={en:["Indexed to 100"],hu_HU:["Indexed to 100"]}},31340:e=>{e.exports={en:["Logarithmic"],hu_HU:["Logarithmic"]}},19405:e=>{e.exports={en:["No overlapping labels"],hu_HU:["No Overlapping Labels"]}},34954:e=>{e.exports={en:["Percent"],hu_HU:["Percent"]}},55300:e=>{e.exports={en:["Regular"],hu_HU:["Regular"]}},8029:e=>{e.exports={en:["ETH"],hu_HU:["ETH"]}},34647:e=>{e.exports={en:["Electronic trading hours"],hu_HU:["Electronic trading hours"]}},36862:e=>{e.exports={en:["Extended trading hours"],hu_HU:["Bővített kereskedési órák"]}},7807:e=>{e.exports={en:["POST"],hu_HU:["POST"]}},46273:e=>{e.exports={en:["PRE"],hu_HU:["PRE"]}},50434:e=>{e.exports={en:["Postmarket"],hu_HU:["Postmarket"]}},59330:e=>{e.exports={en:["Premarket"],hu_HU:["Premarket"]}},35342:e=>{e.exports={en:["RTH"],hu_HU:["RTH"]}},84246:e=>{e.exports={en:["Regular trading hours"],hu_HU:["Regular trading hours"]}},13132:e=>{e.exports={en:["May"],hu_HU:["Május"]}},83477:e=>{e.exports=Object.create(null),e.exports.Technicals_study={en:["Technicals"],hu_HU:["Technikaiak"]},e.exports["Average Day Range_study"]={en:["Average Day Range"],hu_HU:["Average Day Range"]},e.exports["Bull Bear Power_study"]={en:["Bull Bear Power"],hu_HU:["Bull Bear Power"]},e.exports["Capital expenditures_study"]={en:["Capital expenditures"],hu_HU:["Tőkeberuházások"]},e.exports["Cash to debt ratio_study"]={en:["Cash to debt ratio"],hu_HU:["Cash to debt ratio"]},e.exports["Debt to EBITDA ratio_study"]={en:["Debt to EBITDA ratio"],hu_HU:["Debt to EBITDA ratio"]},e.exports["Directional Movement Index_study"]={en:["Directional Movement Index"],hu_HU:["Directional Movement Index"]},e.exports.DMI_study={en:["DMI"],hu_HU:["DMI"]},e.exports["Dividend payout ratio %_study"]={en:["Dividend payout ratio %"],hu_HU:["Dividend payout ratio %"]},e.exports["Equity to assets ratio_study"]={en:["Equity to assets ratio"],hu_HU:["Equity to assets ratio"]},e.exports["Enterprise value to EBIT ratio_study"]={en:["Enterprise value to EBIT ratio"],hu_HU:["Enterprise value to EBIT ratio"]},e.exports["Enterprise value to EBITDA ratio_study"]={en:["Enterprise value to EBITDA ratio"],hu_HU:["Enterprise value to EBITDA ratio"]},e.exports["Enterprise value to revenue ratio_study"]={en:["Enterprise value to revenue ratio"],hu_HU:["Enterprise value to revenue ratio"]},
e.exports["Goodwill, net_study"]={en:["Goodwill, net"],hu_HU:["Goodwill, net"]},e.exports["Ichimoku Cloud_study"]={en:["Ichimoku Cloud"],hu_HU:["Ichimoku Felhő"]},e.exports.Ichimoku_study={en:["Ichimoku"],hu_HU:["Ichimoku"]},e.exports["Moving Average Convergence Divergence_study"]={en:["Moving Average Convergence Divergence"],hu_HU:["Moving Average Convergence Divergence"]},e.exports["Operating income_study"]={en:["Operating income"],hu_HU:["Operating income"]},e.exports["Price to book ratio_study"]={en:["Price to book ratio"],hu_HU:["Price to book ratio"]},e.exports["Price to cash flow ratio_study"]={en:["Price to cash flow ratio"],hu_HU:["Price to cash flow ratio"]},e.exports["Price to earnings ratio_study"]={en:["Price to earnings ratio"],hu_HU:["Price to earnings ratio"]},e.exports["Price to free cash flow ratio_study"]={en:["Price to free cash flow ratio"],hu_HU:["Price to free cash flow ratio"]},e.exports["Price to sales ratio_study"]={en:["Price to sales ratio"],hu_HU:["Price to sales ratio"]},e.exports["Float shares outstanding_study"]={en:["Float shares outstanding"],hu_HU:["Float shares outstanding"]},e.exports["Total common shares outstanding_study"]={en:["Total common shares outstanding"],hu_HU:["Total common shares outstanding"]},e.exports["Volume Weighted Average Price_study"]={en:["Volume Weighted Average Price"],hu_HU:["Volume Weighted Average Price"]},e.exports["Volume Weighted Moving Average_study"]={en:["Volume Weighted Moving Average"],hu_HU:["Volume Weighted Moving Average"]},e.exports["Williams Percent Range_study"]={en:["Williams Percent Range"],hu_HU:["Williams Percent Range"]},e.exports.Doji_study={en:["Doji"],hu_HU:["Doji"]},e.exports["Spinning Top Black_study"]={en:["Spinning Top Black"],hu_HU:["Fekete Forgó Top"]},e.exports["Spinning Top White_study"]={en:["Spinning Top White"],hu_HU:["Fehér Forgó Top"]},e.exports["Accounts payable_study"]={en:["Accounts payable"],hu_HU:["Accounts payable"]},e.exports["Accounts receivables, gross_study"]={en:["Accounts receivables, gross"],hu_HU:["Accounts receivables, gross"]},e.exports["Accounts receivable - trade, net_study"]={en:["Accounts receivable - trade, net"],hu_HU:["Accounts receivable - trade, net"]},e.exports.Accruals_study={en:["Accruals"],hu_HU:["Accruals"]},e.exports["Accrued payroll_study"]={en:["Accrued payroll"],hu_HU:["Accrued payroll"]},e.exports["Accumulated depreciation, total_study"]={en:["Accumulated depreciation, total"],hu_HU:["Accumulated depreciation, total"]},e.exports["Additional paid-in capital/Capital surplus_study"]={en:["Additional paid-in capital/Capital surplus"],hu_HU:["Additional paid-in capital/Capital surplus"]},e.exports["After tax other income/expense_study"]={en:["After tax other income/expense"],hu_HU:["After tax other income/expense"]},e.exports["Altman Z-score_study"]={en:["Altman Z-score"],hu_HU:["Altman Z-score"]},e.exports.Amortization_study={en:["Amortization"],hu_HU:["Amortization"]},e.exports["Amortization of intangibles_study"]={en:["Amortization of intangibles"],hu_HU:["Amortization of intangibles"]},
e.exports["Amortization of deferred charges_study"]={en:["Amortization of deferred charges"],hu_HU:["Amortization of deferred charges"]},e.exports["Asset turnover_study"]={en:["Asset turnover"],hu_HU:["Asset turnover"]},e.exports["Average basic shares outstanding_study"]={en:["Average basic shares outstanding"],hu_HU:["Average basic shares outstanding"]},e.exports["Bad debt / Doubtful accounts_study"]={en:["Bad debt / Doubtful accounts"],hu_HU:["Bad debt / Doubtful accounts"]},e.exports["Basic EPS_study"]={en:["Basic EPS"],hu_HU:["Basic EPS"]},e.exports["Basic earnings per share (Basic EPS)_study"]={en:["Basic earnings per share (Basic EPS)"],hu_HU:["Basic earnings per share (Basic EPS)"]},e.exports["Beneish M-score_study"]={en:["Beneish M-score"],hu_HU:["Beneish M-score"]},e.exports["Book value per share_study"]={en:["Book value per share"],hu_HU:["Book value per share"]},e.exports["Buyback yield %_study"]={en:["Buyback yield %"],hu_HU:["Buyback yield %"]},e.exports["Capital and operating lease obligations_study"]={en:["Capital and operating lease obligations"],hu_HU:["Capital and operating lease obligations"]},e.exports["Capital expenditures - fixed assets_study"]={en:["Capital expenditures - fixed assets"],hu_HU:["Capital expenditures - fixed assets"]},e.exports["Capital expenditures - other assets_study"]={en:["Capital expenditures - other assets"],hu_HU:["Capital expenditures - other assets"]},e.exports["Capitalized lease obligations_study"]={en:["Capitalized lease obligations"],hu_HU:["Capitalized lease obligations"]},e.exports["Cash and short term investments_study"]={en:["Cash and short term investments"],hu_HU:["Cash and short term investments"]},e.exports["Cash conversion cycle_study"]={en:["Cash conversion cycle"],hu_HU:["Cash conversion cycle"]},e.exports["Cash & equivalents_study"]={en:["Cash & equivalents"],hu_HU:["Cash & equivalents"]},e.exports["Cash from financing activities_study"]={en:["Cash from financing activities"],hu_HU:["Cash from financing activities"]},e.exports["Cash from investing activities_study"]={en:["Cash from investing activities"],hu_HU:["Cash from investing activities"]},e.exports["Cash from operating activities_study"]={en:["Cash from operating activities"],hu_HU:["Cash from operating activities"]},e.exports["Change in accounts payable_study"]={en:["Change in accounts payable"],hu_HU:["Change in accounts payable"]},e.exports["Change in accounts receivable_study"]={en:["Change in accounts receivable"],hu_HU:["Change in accounts receivable"]},e.exports["Change in accrued expenses_study"]={en:["Change in accrued expenses"],hu_HU:["Change in accrued expenses"]},e.exports["Change in inventories_study"]={en:["Change in inventories"],hu_HU:["Change in inventories"]},e.exports["Change in other assets/liabilities_study"]={en:["Change in other assets/liabilities"],hu_HU:["Change in other assets/liabilities"]},e.exports["Change in taxes payable_study"]={en:["Change in taxes payable"],hu_HU:["Change in taxes payable"]},e.exports["Changes in working capital_study"]={
en:["Changes in working capital"],hu_HU:["Changes in working capital"]},e.exports["COGS to revenue ratio_study"]={en:["COGS to revenue ratio"],hu_HU:["COGS to revenue ratio"]},e.exports["Common dividends paid_study"]={en:["Common dividends paid"],hu_HU:["Common dividends paid"]},e.exports["Common equity, total_study"]={en:["Common equity, total"],hu_HU:["Common equity, total"]},e.exports["Common stock par/Carrying value_study"]={en:["Common stock par/Carrying value"],hu_HU:["Common stock par/Carrying value"]},e.exports["Cost of goods_study"]={en:["Cost of goods"],hu_HU:["Cost of goods"]},e.exports["Cost of goods sold_study"]={en:["Cost of goods sold"],hu_HU:["Cost of goods sold"]},e.exports["Current portion of LT debt and capital leases_study"]={en:["Current portion of LT debt and capital leases"],hu_HU:["Current portion of LT debt and capital leases"]},e.exports["Current ratio_study"]={en:["Current ratio"],hu_HU:["Current ratio"]},e.exports["Days inventory_study"]={en:["Days inventory"],hu_HU:["Days inventory"]},e.exports["Days payable_study"]={en:["Days payable"],hu_HU:["Days payable"]},e.exports["Days sales outstanding_study"]={en:["Days sales outstanding"],hu_HU:["Days sales outstanding"]},e.exports["Debt to assets ratio_study"]={en:["Debt to assets ratio"],hu_HU:["Debt to assets ratio"]},e.exports["Debt to equity ratio_study"]={en:["Debt to equity ratio"],hu_HU:["Debt to equity ratio"]},e.exports["Debt to revenue ratio_study"]={en:["Debt to revenue ratio"],hu_HU:["Debt to revenue ratio"]},e.exports["Deferred income, current_study"]={en:["Deferred income, current"],hu_HU:["Deferred income, current"]},e.exports["Deferred income, non-current_study"]={en:["Deferred income, non-current"],hu_HU:["Deferred income, non-current"]},e.exports["Deferred tax assets_study"]={en:["Deferred tax assets"],hu_HU:["Deferred tax assets"]},e.exports["Deferred taxes (cash flow)_study"]={en:["Deferred taxes (cash flow)"],hu_HU:["Deferred taxes (cash flow)"]},e.exports["Deferred tax liabilities_study"]={en:["Deferred tax liabilities"],hu_HU:["Deferred tax liabilities"]},e.exports.Depreciation_study={en:["Depreciation"],hu_HU:["Depreciation"]},e.exports["Deprecation and amortization_study"]={en:["Deprecation and amortization"],hu_HU:["Deprecation and amortization"]},e.exports["Depreciation & amortization (cash flow)_study"]={en:["Depreciation & amortization (cash flow)"],hu_HU:["Depreciation & amortization (cash flow)"]},e.exports["Depreciation/depletion_study"]={en:["Depreciation/depletion"],hu_HU:["Depreciation/depletion"]},e.exports["Diluted EPS_study"]={en:["Diluted EPS"],hu_HU:["Higított EPS"]},e.exports["Diluted earnings per share (Diluted EPS)_study"]={en:["Diluted earnings per share (Diluted EPS)"],hu_HU:["Diluted earnings per share (Diluted EPS)"]},e.exports["Diluted net income available to common stockholders_study"]={en:["Diluted net income available to common stockholders"],hu_HU:["Diluted net income available to common stockholders"]},e.exports["Diluted shares outstanding_study"]={en:["Diluted shares outstanding"],
hu_HU:["Diluted shares outstanding"]},e.exports["Dilution adjustment_study"]={en:["Dilution adjustment"],hu_HU:["Dilution adjustment"]},e.exports["Discontinued operations_study"]={en:["Discontinued operations"],hu_HU:["Discontinued operations"]},e.exports["Dividends payable_study"]={en:["Dividends payable"],hu_HU:["Dividends payable"]},e.exports["Dividends per share - common stock primary issue_study"]={en:["Dividends per share - common stock primary issue"],hu_HU:["Dividends per share - common stock primary issue"]},e.exports["Dividend yield %_study"]={en:["Dividend yield %"],hu_HU:["Dividend yield %"]},e.exports["Earnings yield_study"]={en:["Earnings yield"],hu_HU:["Earnings yield"]},e.exports.EBIT_study={en:["EBIT"],hu_HU:["EBIT"]},e.exports.EBITDA_study={en:["EBITDA"],hu_HU:["EBITDA"]},e.exports["EBITDA margin %_study"]={en:["EBITDA margin %"],hu_HU:["EBITDA margin %"]},e.exports["Effective interest rate on debt %_study"]={en:["Effective interest rate on debt %"],hu_HU:["Effective interest rate on debt %"]},e.exports["Enterprise value_study"]={en:["Enterprise value"],hu_HU:["Enterprise value"]},e.exports["EPS basic one year growth_study"]={en:["EPS basic one year growth"],hu_HU:["EPS basic one year growth"]},e.exports["EPS diluted one year growth_study"]={en:["EPS diluted one year growth"],hu_HU:["EPS diluted one year growth"]},e.exports["EPS estimates_study"]={en:["EPS estimates"],hu_HU:["EPS estimates"]},e.exports["Equity in earnings_study"]={en:["Equity in earnings"],hu_HU:["Equity in earnings"]},e.exports["Financing activities – other sources_study"]={en:["Financing activities – other sources"],hu_HU:["Financing activities – other sources"]},e.exports["Financing activities – other uses_study"]={en:["Financing activities – other uses"],hu_HU:["Financing activities – other uses"]},e.exports["Free cash flow_study"]={en:["Free cash flow"],hu_HU:["Szabad Cash Flow"]},e.exports["Free cash flow margin %_study"]={en:["Free cash flow margin %"],hu_HU:["Free cash flow margin %"]},e.exports["Fulmer H factor_study"]={en:["Fulmer H factor"],hu_HU:["Fulmer H factor"]},e.exports["Funds from operations_study"]={en:["Funds from operations"],hu_HU:["Funds from operations"]},e.exports["Goodwill to assets ratio_study"]={en:["Goodwill to assets ratio"],hu_HU:["Goodwill to assets ratio"]},e.exports["Graham's number_study"]={en:["Graham's number"],hu_HU:["Graham's number"]},e.exports["Gross margin %_study"]={en:["Gross margin %"],hu_HU:["Gross margin %"]},e.exports["Gross profit_study"]={en:["Gross profit"],hu_HU:["Bruttó Profit"]},e.exports["Gross profit to assets ratio_study"]={en:["Gross profit to assets ratio"],hu_HU:["Gross profit to assets ratio"]},e.exports["Gross property/plant/equipment_study"]={en:["Gross property/plant/equipment"],hu_HU:["Gross property/plant/equipment"]},e.exports.Impairments_study={en:["Impairments"],hu_HU:["Impairments"]},e.exports["Income Tax Credits_study"]={en:["Income Tax Credits"],hu_HU:["Income Tax Credits"]},e.exports["Income tax, current_study"]={en:["Income tax, current"],
hu_HU:["Income tax, current"]},e.exports["Income tax, current - domestic_study"]={en:["Income tax, current - domestic"],hu_HU:["Income tax, current - domestic"]},e.exports["Income Tax, current - foreign_study"]={en:["Income Tax, current - foreign"],hu_HU:["Income Tax, current - foreign"]},e.exports["Income tax, deferred_study"]={en:["Income tax, deferred"],hu_HU:["Income tax, deferred"]},e.exports["Income tax, deferred - domestic_study"]={en:["Income tax, deferred - domestic"],hu_HU:["Income tax, deferred - domestic"]},e.exports["Income tax, deferred - foreign_study"]={en:["Income tax, deferred - foreign"],hu_HU:["Income tax, deferred - foreign"]},e.exports["Income tax payable_study"]={en:["Income tax payable"],hu_HU:["Income tax payable"]},e.exports["Interest capitalized_study"]={en:["Interest capitalized"],hu_HU:["Interest capitalized"]},e.exports["Interest coverage_study"]={en:["Interest coverage"],hu_HU:["Interest coverage"]},e.exports["Interest expense, net of interest capitalized_study"]={en:["Interest expense, net of interest capitalized"],hu_HU:["Interest expense, net of interest capitalized"]},e.exports["Interest expense on debt_study"]={en:["Interest expense on debt"],hu_HU:["Interest expense on debt"]},e.exports["Inventories - finished goods_study"]={en:["Inventories - finished goods"],hu_HU:["Inventories - finished goods"]},e.exports["Inventories - progress payments & other_study"]={en:["Inventories - progress payments & other"],hu_HU:["Inventories - progress payments & other"]},e.exports["Inventories - raw materials_study"]={en:["Inventories - raw materials"],hu_HU:["Inventories - raw materials"]},e.exports["Inventories - work in progress_study"]={en:["Inventories - work in progress"],hu_HU:["Inventories - work in progress"]},e.exports["Inventory to revenue ratio_study"]={en:["Inventory to revenue ratio"],hu_HU:["Inventory to revenue ratio"]},e.exports["Inventory turnover_study"]={en:["Inventory turnover"],hu_HU:["Inventory turnover"]},e.exports["Investing activities – other sources_study"]={en:["Investing activities – other sources"],hu_HU:["Investing activities – other sources"]},e.exports["Investing activities – other uses_study"]={en:["Investing activities – other uses"],hu_HU:["Investing activities – other uses"]},e.exports["Investments in unconsolidated subsidiaries_study"]={en:["Investments in unconsolidated subsidiaries"],hu_HU:["Investments in unconsolidated subsidiaries"]},e.exports["Issuance of long term debt_study"]={en:["Issuance of long term debt"],hu_HU:["Issuance of long term debt"]},e.exports["Issuance/retirement of debt, net_study"]={en:["Issuance/retirement of debt, net"],hu_HU:["Issuance/retirement of debt, net"]},e.exports["Issuance/retirement of long term debt_study"]={en:["Issuance/retirement of long term debt"],hu_HU:["Issuance/retirement of long term debt"]},e.exports["Issuance/retirement of other debt_study"]={en:["Issuance/retirement of other debt"],hu_HU:["Issuance/retirement of other debt"]},e.exports["Issuance/retirement of short term debt_study"]={
en:["Issuance/retirement of short term debt"],hu_HU:["Issuance/retirement of short term debt"]},e.exports["Issuance/retirement of stock, net_study"]={en:["Issuance/retirement of stock, net"],hu_HU:["Issuance/retirement of stock, net"]},e.exports["KZ index_study"]={en:["KZ index"],hu_HU:["KZ index"]},e.exports["Legal claim expense_study"]={en:["Legal claim expense"],hu_HU:["Legal claim expense"]},e.exports["Long term debt_study"]={en:["Long term debt"],hu_HU:["Long term debt"]},e.exports["Long term debt excl. lease liabilities_study"]={en:["Long term debt excl. lease liabilities"],hu_HU:["Long term debt excl. lease liabilities"]},e.exports["Long term debt to total assets ratio_study"]={en:["Long term debt to total assets ratio"],hu_HU:["Long term debt to total assets ratio"]},e.exports["Long term debt to total equity ratio_study"]={en:["Long term debt to total equity ratio"],hu_HU:["Long term debt to total equity ratio"]},e.exports["Long term investments_study"]={en:["Long term investments"],hu_HU:["Long term investments"]},e.exports["Market capitalization_study"]={en:["Market capitalization"],hu_HU:["Market capitalization"]},e.exports["Minority interest_study"]={en:["Minority interest"],hu_HU:["Kisebbségi Részesedés"]},e.exports["Miscellaneous non-operating expense_study"]={en:["Miscellaneous non-operating expense"],hu_HU:["Miscellaneous non-operating expense"]},e.exports["Net current asset value per share_study"]={en:["Net current asset value per share"],hu_HU:["Net current asset value per share"]},e.exports["Net debt_study"]={en:["Net debt"],hu_HU:["Net debt"]},e.exports["Net income_study"]={en:["Net income"],hu_HU:["Nettó Jövedelem"]},e.exports["Net income before discontinued operations_study"]={en:["Net income before discontinued operations"],hu_HU:["Net income before discontinued operations"]},e.exports["Net income (cash flow)_study"]={en:["Net income (cash flow)"],hu_HU:["Net income (cash flow)"]},e.exports["Net income per employee_study"]={en:["Net income per employee"],hu_HU:["Net income per employee"]},e.exports["Net intangible assets_study"]={en:["Net intangible assets"],hu_HU:["Net intangible assets"]},e.exports["Net margin %_study"]={en:["Net margin %"],hu_HU:["Net margin %"]},e.exports["Net property/plant/equipment_study"]={en:["Net property/plant/equipment"],hu_HU:["Net property/plant/equipment"]},e.exports["Non-cash items_study"]={en:["Non-cash items"],hu_HU:["Non-cash items"]},e.exports["Non-controlling/minority interest_study"]={en:["Non-controlling/minority interest"],hu_HU:["Non-controlling/minority interest"]},e.exports["Non-operating income, excl. interest expenses_study"]={en:["Non-operating income, excl. interest expenses"],hu_HU:["Non-operating income, excl. interest expenses"]},e.exports["Non-operating income, total_study"]={en:["Non-operating income, total"],hu_HU:["Non-operating income, total"]},e.exports["Non-operating interest income_study"]={en:["Non-operating interest income"],hu_HU:["Non-operating interest income"]},e.exports["Note receivable - long term_study"]={
en:["Note receivable - long term"],hu_HU:["Note receivable - long term"]},e.exports["Notes payable_study"]={en:["Notes payable"],hu_HU:["Notes payable"]},e.exports["Number of employees_study"]={en:["Number of employees"],hu_HU:["Number of employees"]},e.exports["Number of shareholders_study"]={en:["Number of shareholders"],hu_HU:["Number of shareholders"]},e.exports["Operating earnings yield %_study"]={en:["Operating earnings yield %"],hu_HU:["Operating earnings yield %"]},e.exports["Operating expenses (excl. COGS)_study"]={en:["Operating expenses (excl. COGS)"],hu_HU:["Operating expenses (excl. COGS)"]},e.exports["Operating lease liabilities_study"]={en:["Operating lease liabilities"],hu_HU:["Operating lease liabilities"]},e.exports["Operating margin %_study"]={en:["Operating margin %"],hu_HU:["Operating margin %"]},e.exports["Other COGS_study"]={en:["Other COGS"],hu_HU:["Other COGS"]},e.exports["Other common equity_study"]={en:["Other common equity"],hu_HU:["Other common equity"]},e.exports["Other current assets, total_study"]={en:["Other current assets, total"],hu_HU:["Other current assets, total"]},e.exports["Other current liabilities_study"]={en:["Other current liabilities"],hu_HU:["Other current liabilities"]},e.exports["Other cost of goods sold_study"]={en:["Other cost of goods sold"],hu_HU:["Other cost of goods sold"]},e.exports["Other exceptional charges_study"]={en:["Other exceptional charges"],hu_HU:["Other exceptional charges"]},e.exports["Other financing cash flow items, total_study"]={en:["Other financing cash flow items, total"],hu_HU:["Other financing cash flow items, total"]},e.exports["Other intangibles, net_study"]={en:["Other intangibles, net"],hu_HU:["Other intangibles, net"]},e.exports["Other investing cash flow items, total_study"]={en:["Other investing cash flow items, total"],hu_HU:["Other investing cash flow items, total"]},e.exports["Other investments_study"]={en:["Other investments"],hu_HU:["Other investments"]},e.exports["Other liabilities, total_study"]={en:["Other liabilities, total"],hu_HU:["Other liabilities, total"]},e.exports["Other long term assets, total_study"]={en:["Other long term assets, total"],hu_HU:["Other long term assets, total"]},e.exports["Other non-current liabilities, total_study"]={en:["Other non-current liabilities, total"],hu_HU:["Other non-current liabilities, total"]},e.exports["Other operating expenses, total_study"]={en:["Other operating expenses, total"],hu_HU:["Other operating expenses, total"]},e.exports["Other receivables_study"]={en:["Other receivables"],hu_HU:["Other receivables"]},e.exports["Other short term debt_study"]={en:["Other short term debt"],hu_HU:["Other short term debt"]},e.exports["Paid in capital_study"]={en:["Paid in capital"],hu_HU:["Paid in capital"]},e.exports["PEG ratio_study"]={en:["PEG ratio"],hu_HU:["PEG ratio"]},e.exports["Piotroski F-score_study"]={en:["Piotroski F-score"],hu_HU:["Piotroski F-score"]},e.exports["Preferred dividends_study"]={en:["Preferred dividends"],hu_HU:["Preferred dividends"]},
e.exports["Preferred dividends paid_study"]={en:["Preferred dividends paid"],hu_HU:["Preferred dividends paid"]},e.exports["Preferred stock, carrying value_study"]={en:["Preferred stock, carrying value"],hu_HU:["Preferred stock, carrying value"]},e.exports["Prepaid expenses_study"]={en:["Prepaid expenses"],hu_HU:["Prepaid expenses"]},e.exports["Pretax equity in earnings_study"]={en:["Pretax equity in earnings"],hu_HU:["Pretax equity in earnings"]},e.exports["Pretax income_study"]={en:["Pretax income"],hu_HU:["Pretax income"]},e.exports["Price earnings ratio forward_study"]={en:["Price earnings ratio forward"],hu_HU:["Price earnings ratio forward"]},e.exports["Price sales ratio forward_study"]={en:["Price sales ratio forward"],hu_HU:["Price sales ratio forward"]},e.exports["Price to tangible book ratio_study"]={en:["Price to tangible book ratio"],hu_HU:["Price to tangible book ratio"]},e.exports["Provision for risks & charge_study"]={en:["Provision for risks & charge"],hu_HU:["Provision for risks & charge"]},e.exports["Purchase/acquisition of business_study"]={en:["Purchase/acquisition of business"],hu_HU:["Purchase/acquisition of business"]},e.exports["Purchase of investments_study"]={en:["Purchase of investments"],hu_HU:["Purchase of investments"]},e.exports["Purchase/sale of business, net_study"]={en:["Purchase/sale of business, net"],hu_HU:["Purchase/sale of business, net"]},e.exports["Purchase/sale of investments, net_study"]={en:["Purchase/sale of investments, net"],hu_HU:["Purchase/sale of investments, net"]},e.exports["Quality ratio_study"]={en:["Quality ratio"],hu_HU:["Quality ratio"]},e.exports["Quick ratio_study"]={en:["Quick ratio"],hu_HU:["Quick ratio"]},e.exports["Reduction of long term debt_study"]={en:["Reduction of long term debt"],hu_HU:["Reduction of long term debt"]},e.exports["Repurchase of common & preferred stock_study"]={en:["Repurchase of common & preferred stock"],hu_HU:["Repurchase of common & preferred stock"]},e.exports["Research & development_study"]={en:["Research & development"],hu_HU:["Research & development"]},e.exports["Research & development to revenue ratio_study"]={en:["Research & development to revenue ratio"],hu_HU:["Research & development to revenue ratio"]},e.exports["Restructuring charge_study"]={en:["Restructuring charge"],hu_HU:["Restructuring charge"]},e.exports["Retained earnings_study"]={en:["Retained earnings"],hu_HU:["Retained earnings"]},e.exports["Return on assets %_study"]={en:["Return on assets %"],hu_HU:["Return on assets %"]},e.exports["Return on equity %_study"]={en:["Return on equity %"],hu_HU:["Return on equity %"]},e.exports["Return on equity adjusted to book value %_study"]={en:["Return on equity adjusted to book value %"],hu_HU:["Return on equity adjusted to book value %"]},e.exports["Return on invested capital %_study"]={en:["Return on invested capital %"],hu_HU:["Return on invested capital %"]},e.exports["Return on tangible assets %_study"]={en:["Return on tangible assets %"],hu_HU:["Return on tangible assets %"]},
e.exports["Return on tangible equity %_study"]={en:["Return on tangible equity %"],hu_HU:["Return on tangible equity %"]},e.exports["Revenue estimates_study"]={en:["Revenue estimates"],hu_HU:["Revenue estimates"]},e.exports["Revenue one year growth_study"]={en:["Revenue one year growth"],hu_HU:["Revenue one year growth"]},e.exports["Revenue per employee_study"]={en:["Revenue per employee"],hu_HU:["Revenue per employee"]},e.exports["Sale/maturity of investments_study"]={en:["Sale/maturity of investments"],hu_HU:["Sale/maturity of investments"]},e.exports["Sale of common & preferred stock_study"]={en:["Sale of common & preferred stock"],hu_HU:["Sale of common & preferred stock"]},e.exports["Sale of fixed assets & businesses_study"]={en:["Sale of fixed assets & businesses"],hu_HU:["Sale of fixed assets & businesses"]},e.exports["Selling/general/admin expenses, other_study"]={en:["Selling/general/admin expenses, other"],hu_HU:["Selling/general/admin expenses, other"]},e.exports["Selling/general/admin expenses, total_study"]={en:["Selling/general/admin expenses, total"],hu_HU:["Selling/general/admin expenses, total"]},e.exports["Shareholders' equity_study"]={en:["Shareholders' equity"],hu_HU:["Shareholders' equity"]},e.exports["Shares buyback ratio %_study"]={en:["Shares buyback ratio %"],hu_HU:["Shares buyback ratio %"]},e.exports["Short term debt_study"]={en:["Short term debt"],hu_HU:["Short term debt"]},e.exports["Short term debt excl. current portion of LT debt_study"]={en:["Short term debt excl. current portion of LT debt"],hu_HU:["Short term debt excl. current portion of LT debt"]},e.exports["Short term investments_study"]={en:["Short term investments"],hu_HU:["Short term investments"]},e.exports["Sloan ratio %_study"]={en:["Sloan ratio %"],hu_HU:["Sloan ratio %"]},e.exports["Springate score_study"]={en:["Springate score"],hu_HU:["Springate score"]},e.exports["Sustainable growth rate_study"]={en:["Sustainable growth rate"],hu_HU:["Sustainable growth rate"]},e.exports["Tangible book value per share_study"]={en:["Tangible book value per share"],hu_HU:["Tangible book value per share"]},e.exports["Tangible common equity ratio_study"]={en:["Tangible common equity ratio"],hu_HU:["Tangible common equity ratio"]},e.exports.Taxes_study={en:["Taxes"],hu_HU:["Taxes"]},e.exports["Tobin's Q (approximate)_study"]={en:["Tobin's Q (approximate)"],hu_HU:["Tobin's Q (approximate)"]},e.exports["Total assets_study"]={en:["Total assets"],hu_HU:["Mérlegfőösszeg"]},e.exports["Total cash dividends paid_study"]={en:["Total cash dividends paid"],hu_HU:["Total cash dividends paid"]},e.exports["Total current assets_study"]={en:["Total current assets"],hu_HU:["Forgóeszközök Összesen"]},e.exports["Total current liabilities_study"]={en:["Total current liabilities"],hu_HU:["Folyó Kötelezettségek Összesen"]},e.exports["Total debt_study"]={en:["Total debt"],hu_HU:["Teljes Hitelállomány"]},e.exports["Total equity_study"]={en:["Total equity"],hu_HU:["Total equity"]},e.exports["Total inventory_study"]={en:["Total inventory"],hu_HU:["Total inventory"]},
e.exports["Total liabilities_study"]={en:["Total liabilities"],hu_HU:["Összes Kötelezettség"]},e.exports["Total liabilities & shareholders' equities_study"]={en:["Total liabilities & shareholders' equities"],hu_HU:["Total liabilities & shareholders' equities"]},e.exports["Total non-current assets_study"]={en:["Total non-current assets"],hu_HU:["Total non-current assets"]},e.exports["Total non-current liabilities_study"]={en:["Total non-current liabilities"],hu_HU:["Total non-current liabilities"]},e.exports["Total operating expenses_study"]={en:["Total operating expenses"],hu_HU:["Működési Költségek Összesen"]},e.exports["Total receivables, net_study"]={en:["Total receivables, net"],hu_HU:["Total receivables, net"]},e.exports["Total revenue_study"]={en:["Total revenue"],hu_HU:["Összes Bevétel"]},e.exports["Treasury stock - common_study"]={en:["Treasury stock - common"],hu_HU:["Treasury stock - common"]},e.exports["Unrealized gain/loss_study"]={en:["Unrealized gain/loss"],hu_HU:["Unrealized gain/loss"]},e.exports["Unusual income/expense_study"]={en:["Unusual income/expense"],hu_HU:["Unusual income/expense"]},e.exports["Zmijewski score_study"]={en:["Zmijewski score"],hu_HU:["Zmijewski score"]},e.exports["Valuation ratios_study"]={en:["Valuation ratios"],hu_HU:["Valuation ratios"]},e.exports["Profitability ratios_study"]={en:["Profitability ratios"],hu_HU:["Profitability ratios"]},e.exports["Liquidity ratios_study"]={en:["Liquidity ratios"],hu_HU:["Liquidity ratios"]},e.exports["Solvency ratios_study"]={en:["Solvency ratios"],hu_HU:["Solvency ratios"]},e.exports["Key stats_study"]={en:["Key stats"],hu_HU:["Key stats"]},e.exports["Accumulation/Distribution_study"]={en:["Accumulation/Distribution"],hu_HU:["Akkumuláció/Disztribúció"]},e.exports["Accumulative Swing Index_study"]={en:["Accumulative Swing Index"],hu_HU:["Akkumulatív Swing Index"]},e.exports["Advance/Decline_study"]={en:["Advance/Decline"],hu_HU:["Advance/Decline"]},e.exports["All Chart Patterns_study"]={en:["All Chart Patterns"],hu_HU:["All Chart Patterns"]},e.exports["Arnaud Legoux Moving Average_study"]={en:["Arnaud Legoux Moving Average"],hu_HU:["Arnaud Legoux Mozgóátlag"]},e.exports.Aroon_study={en:["Aroon"],hu_HU:["Aroon"]},e.exports.ASI_study={en:["ASI"],hu_HU:["ASI"]},e.exports["Average Directional Index_study"]={en:["Average Directional Index"],hu_HU:["Átlagos Irányított Index"]},e.exports["Average True Range_study"]={en:["Average True Range"],hu_HU:["Átlagos Valós Tartomány"]},e.exports["Awesome Oscillator_study"]={en:["Awesome Oscillator"],hu_HU:["Awesome Oszcillátor"]},e.exports["Balance of Power_study"]={en:["Balance of Power"],hu_HU:["Erőegyensúly"]},e.exports["Bollinger Bands %B_study"]={en:["Bollinger Bands %B"],hu_HU:["Bollinger Szalagok %B"]},e.exports["Bollinger Bands Width_study"]={en:["Bollinger Bands Width"],hu_HU:["Bollinger Szalag Szélesség"]},e.exports["Bollinger Bands_study"]={en:["Bollinger Bands"],hu_HU:["Bollinger Szalagok"]},e.exports["Chaikin Money Flow_study"]={en:["Chaikin Money Flow"],hu_HU:["Chaikin Pénzáramlás"]},
e.exports["Chaikin Oscillator_study"]={en:["Chaikin Oscillator"],hu_HU:["Chaikin Oszcillátor"]},e.exports["Chande Kroll Stop_study"]={en:["Chande Kroll Stop"],hu_HU:["Chande Kroll Stop"]},e.exports["Chande Momentum Oscillator_study"]={en:["Chande Momentum Oscillator"],hu_HU:["Chande Momentum Oszcillátor"]},e.exports["Chop Zone_study"]={en:["Chop Zone"],hu_HU:["Oldalazó Zóna"]},e.exports["Choppiness Index_study"]={en:["Choppiness Index"],hu_HU:["Szaggatottság Index"]},e.exports["Commodity Channel Index_study"]={en:["Commodity Channel Index"],hu_HU:["Árucsatorna Index"]},e.exports["Connors RSI_study"]={en:["Connors RSI"],hu_HU:["Connors RSI"]},e.exports["Coppock Curve_study"]={en:["Coppock Curve"],hu_HU:["Coppock Görbe"]},e.exports["Correlation Coefficient_study"]={en:["Correlation Coefficient"],hu_HU:["Korrelációs Koefficiens"]},e.exports.CRSI_study={en:["CRSI"],hu_HU:["CRSI"]},e.exports["Detrended Price Oscillator_study"]={en:["Detrended Price Oscillator"],hu_HU:["Trendmentes Ár Oszcillátor"]},e.exports["Directional Movement_study"]={en:["Directional Movement"],hu_HU:["Irányított Mozgás"]},e.exports["Donchian Channels_study"]={en:["Donchian Channels"],hu_HU:["Donchian Csatornák"]},e.exports["Double EMA_study"]={en:["Double EMA"],hu_HU:["Dupla EMA"]},e.exports["Ease Of Movement_study"]={en:["Ease Of Movement"],hu_HU:["Mozgás Könnyedség"]},e.exports["Elder Force Index_study"]={en:["Elder Force Index"],hu_HU:["Nemes Erő Index"]},e.exports["EMA Cross_study"]={en:["EMA Cross"],hu_HU:["EMA Cross"]},e.exports.Envelopes_study={en:["Envelopes"],hu_HU:["Envelopes"]},e.exports["Fisher Transform_study"]={en:["Fisher Transform"],hu_HU:["Fisher Transzformáció"]},e.exports["Fixed Range_study"]={en:["Fixed Range"],hu_HU:["Fixed Range"]},e.exports["Fixed Range Volume Profile_study"]={en:["Fixed Range Volume Profile"],hu_HU:["Fixed Range Volume Profile"]},e.exports["Guppy Multiple Moving Average_study"]={en:["Guppy Multiple Moving Average"],hu_HU:["Guppy Multiple Moving Average"]},e.exports["Historical Volatility_study"]={en:["Historical Volatility"],hu_HU:["Histórikus Volatilitás"]},e.exports["Hull Moving Average_study"]={en:["Hull Moving Average"],hu_HU:["Hull Mozgóátlag"]},e.exports["Keltner Channels_study"]={en:["Keltner Channels"],hu_HU:["Keltner Csatornák"]},e.exports["Klinger Oscillator_study"]={en:["Klinger Oscillator"],hu_HU:["Klinger Oszcillátor"]},e.exports["Know Sure Thing_study"]={en:["Know Sure Thing"],hu_HU:["Biztosra Tudd Dolog"]},e.exports["Least Squares Moving Average_study"]={en:["Least Squares Moving Average"],hu_HU:["Least Squares Mozgóátlag"]},e.exports["Linear Regression Curve_study"]={en:["Linear Regression Curve"],hu_HU:["Linear Regression Curve"]},e.exports["MA Cross_study"]={en:["MA Cross"],hu_HU:["MA Kereszt"]},e.exports["MA with EMA Cross_study"]={en:["MA with EMA Cross"],hu_HU:["MA with EMA Cross"]},e.exports["MA/EMA Cross_study"]={en:["MA/EMA Cross"],hu_HU:["MA/EMA Cross"]},e.exports.MACD_study={en:["MACD"],hu_HU:["MACD"]},e.exports["Mass Index_study"]={en:["Mass Index"],hu_HU:["Tömeg Index"]},
e.exports["McGinley Dynamic_study"]={en:["McGinley Dynamic"],hu_HU:["McGinley Dinamika"]},e.exports.Median_study={en:["Median"],hu_HU:["Medián"]},e.exports.Momentum_study={en:["Momentum"],hu_HU:["Momentum"]},e.exports["Money Flow_study"]={en:["Money Flow"],hu_HU:["Pénzáramlás"]},e.exports["Moving Average Channel_study"]={en:["Moving Average Channel"],hu_HU:["Moving Average Channel"]},e.exports["Moving Average Exponential_study"]={en:["Moving Average Exponential"],hu_HU:["Mozgóátlag Exponenciális"]},e.exports["Moving Average Weighted_study"]={en:["Moving Average Weighted"],hu_HU:["Mozgóátlag Súlyozott"]},e.exports["Moving Average Simple_study"]={en:["Moving Average Simple"],hu_HU:["Moving Average Simple"]},e.exports["Net Volume_study"]={en:["Net Volume"],hu_HU:["Nettó Volumen"]},e.exports["On Balance Volume_study"]={en:["On Balance Volume"],hu_HU:["Egyensúly Volumen"]},e.exports["Parabolic SAR_study"]={en:["Parabolic SAR"],hu_HU:["Parabolikus SAR"]},e.exports["Pivot Points Standard_study"]={en:["Pivot Points Standard"],hu_HU:["Pivotális Pontok Standard"]},e.exports["Periodic Volume Profile_study"]={en:["Periodic Volume Profile"],hu_HU:["Periodic Volume Profile"]},e.exports["Price Channel_study"]={en:["Price Channel"],hu_HU:["Price Channel"]},e.exports["Price Oscillator_study"]={en:["Price Oscillator"],hu_HU:["Price Oszcillátor"]},e.exports["Price Volume Trend_study"]={en:["Price Volume Trend"],hu_HU:["Árvolumen Trend"]},e.exports["Rate Of Change_study"]={en:["Rate Of Change"],hu_HU:["Változás Üteme"]},e.exports["Relative Strength Index_study"]={en:["Relative Strength Index"],hu_HU:["Relatív Erő Index"]},e.exports["Relative Vigor Index_study"]={en:["Relative Vigor Index"],hu_HU:["Relatív Életerő Index"]},e.exports["Relative Volatility Index_study"]={en:["Relative Volatility Index"],hu_HU:["Relative Volatility Index"]},e.exports["Relative Volume at Time_study"]={en:["Relative Volume at Time"],hu_HU:["Relative Volume at Time"]},e.exports["Session Volume_study"]={en:["Session Volume"],hu_HU:["Session Volume"]},e.exports["Session Volume HD_study"]={en:["Session Volume HD"],hu_HU:["Session Volume"]},e.exports["Session Volume Profile_study"]={en:["Session Volume Profile"],hu_HU:["Session Volume Profile"]},e.exports["Session Volume Profile HD_study"]={en:["Session Volume Profile HD"],hu_HU:["Session Volume Profile HD"]},e.exports["SMI Ergodic Indicator/Oscillator_study"]={en:["SMI Ergodic Indicator/Oscillator"],hu_HU:["SMI Ergodic Indicator/Oscillator"]},e.exports["Smoothed Moving Average_study"]={en:["Smoothed Moving Average"],hu_HU:["Simított Mozgóátlag"]},e.exports["Stochastic Momentum Index_study"]={en:["Stochastic Momentum Index"],hu_HU:["Stochastic Momentum Index"]},e.exports.Stoch_study={en:["Stoch"],hu_HU:["Stoch"]},e.exports["Stochastic RSI_study"]={en:["Stochastic RSI"],hu_HU:["Sztochasztikus RSI"]},e.exports.Stochastic_study={en:["Stochastic"],hu_HU:["Sztochasztikus"]},e.exports["Time Weighted Average Price_study"]={en:["Time Weighted Average Price"],hu_HU:["Time Weighted Average Price"]},
e.exports["Triple EMA_study"]={en:["Triple EMA"],hu_HU:["Triple EMA"]},e.exports.TRIX_study={en:["TRIX"],hu_HU:["TRIX"]},e.exports["True Strength Indicator_study"]={en:["True Strength Indicator"],hu_HU:["True Strength Indikátor"]},e.exports["Ultimate Oscillator_study"]={en:["Ultimate Oscillator"],hu_HU:["Végső Oszcillátor"]},e.exports["Visible Range_study"]={en:["Visible Range"],hu_HU:["Visible Range"]},e.exports["Visible Range Volume Profile_study"]={en:["Visible Range Volume Profile"],hu_HU:["Visible Range Volume Profile"]},e.exports["Volume Oscillator_study"]={en:["Volume Oscillator"],hu_HU:["Volumen Oszcillátor"]},e.exports.Volume_study={en:["Volume"],hu_HU:["Volumen"]},e.exports.Vol_study={en:["Vol"],hu_HU:["Vol"]},e.exports["Vortex Indicator_study"]={en:["Vortex Indicator"],hu_HU:["Vortex Indikátor"]},e.exports.VWAP_study={en:["VWAP"],hu_HU:["VWAP"]},e.exports.VWMA_study={en:["VWMA"],hu_HU:["VWMA"]},e.exports["Williams %R_study"]={en:["Williams %R"],hu_HU:["Williams %R"]},e.exports["Williams Alligator_study"]={en:["Williams Alligator"],hu_HU:["Williams Alligátor"]},e.exports["Williams Fractal_study"]={en:["Williams Fractal"],hu_HU:["Williams Fraktál"]},e.exports["Zig Zag_study"]={en:["Zig Zag"],hu_HU:["Cikk Cakk"]},e.exports["24-hour Volume_study"]={en:["24-hour Volume"],hu_HU:["24-hour Volume"]},e.exports["Ease of Movement_study"]={en:["Ease of Movement"],hu_HU:["Mozgás Könnyedség"]},e.exports["Elders Force Index_study"]={en:["Elders Force Index"],hu_HU:["Elders Force Index"]},e.exports.Envelope_study={en:["Envelope"],hu_HU:["Boríték"]},e.exports.Gaps_study={en:["Gaps"],hu_HU:["Gaps"]},e.exports["Linear Regression Channel_study"]={en:["Linear Regression Channel"],hu_HU:["Linear Regression Channel"]},e.exports["Moving Average Ribbon_study"]={en:["Moving Average Ribbon"],hu_HU:["Moving Average Ribbon"]},e.exports["Multi-Time Period Charts_study"]={en:["Multi-Time Period Charts"],hu_HU:["Több Időszakos Chartok"]},e.exports["Open Interest_study"]={en:["Open Interest"],hu_HU:["Open Interest"]},e.exports["Rob Booker - Intraday Pivot Points_study"]={en:["Rob Booker - Intraday Pivot Points"],hu_HU:["Rob Booker - Intraday Pivot Points"]},e.exports["Rob Booker - Knoxville Divergence_study"]={en:["Rob Booker - Knoxville Divergence"],hu_HU:["Rob Booker - Knoxville Divergence"]},e.exports["Rob Booker - Missed Pivot Points_study"]={en:["Rob Booker - Missed Pivot Points"],hu_HU:["Rob Booker - Missed Pivot Points"]},e.exports["Rob Booker - Reversal_study"]={en:["Rob Booker - Reversal"],hu_HU:["Rob Booker - Reversal"]},e.exports["Rob Booker - Ziv Ghost Pivots_study"]={en:["Rob Booker - Ziv Ghost Pivots"],hu_HU:["Rob Booker - Ziv Ghost Pivots"]},e.exports.Supertrend_study={en:["Supertrend"],hu_HU:["Supertrend"]},e.exports["Technical Ratings_study"]={en:["Technical Ratings"],hu_HU:["Technical Ratings"]},e.exports["True Strength Index_study"]={en:["True Strength Index"],hu_HU:["True Strength Index"]},e.exports["Up/Down Volume_study"]={en:["Up/Down Volume"],hu_HU:["Up/Down Volume"]},e.exports["Visible Average Price_study"]={
en:["Visible Average Price"],hu_HU:["Visible Average Price"]},e.exports["Williams Fractals_study"]={en:["Williams Fractals"],hu_HU:["Williams Fractals"]},e.exports["Keltner Channels Strategy_study"]={en:["Keltner Channels Strategy"],hu_HU:["Keltner Channels Strategy"]},e.exports["Rob Booker - ADX Breakout_study"]={en:["Rob Booker - ADX Breakout"],hu_HU:["Rob Booker - ADX Breakout"]},e.exports["Supertrend Strategy_study"]={en:["Supertrend Strategy"],hu_HU:["Supertrend Strategy"]},e.exports["Technical Ratings Strategy_study"]={en:["Technical Ratings Strategy"],hu_HU:["Technical Ratings Strategy"]},e.exports["Auto Anchored Volume Profile_study"]={en:["Auto Anchored Volume Profile"],hu_HU:["Auto Anchored Volume Profile"]},e.exports["Auto Fib Extension_study"]={en:["Auto Fib Extension"],hu_HU:["Auto Fib Extension"]},e.exports["Auto Fib Retracement_study"]={en:["Auto Fib Retracement"],hu_HU:["Auto Fib Retracement"]},e.exports["Auto Pitchfork_study"]={en:["Auto Pitchfork"],hu_HU:["Auto Pitchfork"]},e.exports["Bearish Flag Chart Pattern_study"]={en:["Bearish Flag Chart Pattern"],hu_HU:["Bearish Flag Chart Pattern"]},e.exports["Bullish Flag Chart Pattern_study"]={en:["Bullish Flag Chart Pattern"],hu_HU:["Bullish Flag Chart Pattern"]},e.exports["Bearish Pennant Chart Pattern_study"]={en:["Bearish Pennant Chart Pattern"],hu_HU:["Bearish Pennant Chart Pattern"]},e.exports["Bullish Pennant Chart Pattern_study"]={en:["Bullish Pennant Chart Pattern"],hu_HU:["Bullish Pennant Chart Pattern"]},e.exports["Double Bottom Chart Pattern_study"]={en:["Double Bottom Chart Pattern"],hu_HU:["Double Bottom Chart Pattern"]},e.exports["Double Top Chart Pattern_study"]={en:["Double Top Chart Pattern"],hu_HU:["Double Top Chart Pattern"]},e.exports["Elliott Wave Chart Pattern_study"]={en:["Elliott Wave Chart Pattern"],hu_HU:["Elliott Wave Chart Pattern"]},e.exports["Falling Wedge Chart Pattern_study"]={en:["Falling Wedge Chart Pattern"],hu_HU:["Falling Wedge Chart Pattern"]},e.exports["Head And Shoulders Chart Pattern_study"]={},e.exports["Inverse Head And Shoulders Chart Pattern_study"]={},e.exports["Rectangle Chart Pattern_study"]={en:["Rectangle Chart Pattern"],hu_HU:["Rectangle Chart Pattern"]},e.exports["Rising Wedge Chart Pattern_study"]={en:["Rising Wedge Chart Pattern"],hu_HU:["Rising Wedge Chart Pattern"]},e.exports["Triangle Chart Pattern_study"]={en:["Triangle Chart Pattern"],hu_HU:["Triangle Chart Pattern"]},e.exports["Triple Bottom Chart Pattern_study"]={en:["Triple Bottom Chart Pattern"],hu_HU:["Triple Bottom Chart Pattern"]},e.exports["Triple Top Chart Pattern_study"]={en:["Triple Top Chart Pattern"],hu_HU:["Triple Top Chart Pattern"]},e.exports["VWAP Auto Anchored_study"]={en:["VWAP Auto Anchored"],hu_HU:["VWAP Auto Anchored"]},e.exports["*All Candlestick Patterns*_study"]={en:["*All Candlestick Patterns*"],hu_HU:["*All Candlestick Patterns*"]},e.exports["Abandoned Baby - Bearish_study"]={en:["Abandoned Baby - Bearish"],hu_HU:["Abandoned Baby - Bearish"]},e.exports["Abandoned Baby - Bullish_study"]={en:["Abandoned Baby - Bullish"],
hu_HU:["Abandoned Baby - Bullish"]},e.exports["Dark Cloud Cover - Bearish_study"]={en:["Dark Cloud Cover - Bearish"],hu_HU:["Dark Cloud Cover - Bearish"]},e.exports["Doji Star - Bearish_study"]={en:["Doji Star - Bearish"],hu_HU:["Doji Star - Bearish"]},e.exports["Doji Star - Bullish_study"]={en:["Doji Star - Bullish"],hu_HU:["Doji Star - Bullish"]},e.exports["Downside Tasuki Gap - Bearish_study"]={en:["Downside Tasuki Gap - Bearish"],hu_HU:["Downside Tasuki Gap - Bearish"]},e.exports["Dragonfly Doji - Bullish_study"]={en:["Dragonfly Doji - Bullish"],hu_HU:["Dragonfly Doji - Bullish"]},e.exports["Engulfing - Bearish_study"]={en:["Engulfing - Bearish"],hu_HU:["Engulfing - Bearish"]},e.exports["Engulfing - Bullish_study"]={en:["Engulfing - Bullish"],hu_HU:["Engulfing - Bullish"]},e.exports["Evening Doji Star - Bearish_study"]={en:["Evening Doji Star - Bearish"],hu_HU:["Evening Doji Star - Bearish"]},e.exports["Evening Star - Bearish_study"]={en:["Evening Star - Bearish"],hu_HU:["Evening Star - Bearish"]},e.exports["Falling Three Methods - Bearish_study"]={en:["Falling Three Methods - Bearish"],hu_HU:["Falling Three Methods - Bearish"]},e.exports["Falling Window - Bearish_study"]={en:["Falling Window - Bearish"],hu_HU:["Falling Window - Bearish"]},e.exports["Gravestone Doji - Bearish_study"]={en:["Gravestone Doji - Bearish"],hu_HU:["Gravestone Doji - Bearish"]},e.exports["Hammer - Bullish_study"]={en:["Hammer - Bullish"],hu_HU:["Hammer - Bullish"]},e.exports["Hanging Man - Bearish_study"]={en:["Hanging Man - Bearish"],hu_HU:["Hanging Man - Bearish"]},e.exports["Harami - Bearish_study"]={en:["Harami - Bearish"],hu_HU:["Harami - Bearish"]},e.exports["Harami - Bullish_study"]={en:["Harami - Bullish"],hu_HU:["Harami - Bullish"]},e.exports["Harami Cross - Bearish_study"]={en:["Harami Cross - Bearish"],hu_HU:["Harami Cross - Bearish"]},e.exports["Harami Cross - Bullish_study"]={en:["Harami Cross - Bullish"],hu_HU:["Harami Cross - Bullish"]},e.exports["Inverted Hammer - Bullish_study"]={en:["Inverted Hammer - Bullish"],hu_HU:["Inverted Hammer - Bullish"]},e.exports["Kicking - Bearish_study"]={en:["Kicking - Bearish"],hu_HU:["Kicking - Bearish"]},e.exports["Kicking - Bullish_study"]={en:["Kicking - Bullish"],hu_HU:["Kicking - Bullish"]},e.exports["Long Lower Shadow - Bullish_study"]={en:["Long Lower Shadow - Bullish"],hu_HU:["Long Lower Shadow - Bullish"]},e.exports["Long Upper Shadow - Bearish_study"]={en:["Long Upper Shadow - Bearish"],hu_HU:["Long Upper Shadow - Bearish"]},e.exports["Marubozu Black - Bearish_study"]={en:["Marubozu Black - Bearish"],hu_HU:["Marubozu Black - Bearish"]},e.exports["Marubozu White - Bullish_study"]={en:["Marubozu White - Bullish"],hu_HU:["Marubozu White - Bullish"]},e.exports["Morning Doji Star - Bullish_study"]={en:["Morning Doji Star - Bullish"],hu_HU:["Morning Doji Star - Bullish"]},e.exports["Morning Star - Bullish_study"]={en:["Morning Star - Bullish"],hu_HU:["Morning Star - Bullish"]},e.exports["On Neck - Bearish_study"]={en:["On Neck - Bearish"],hu_HU:["On Neck - Bearish"]},
e.exports["Piercing - Bullish_study"]={en:["Piercing - Bullish"],hu_HU:["Piercing - Bullish"]},e.exports["Rising Three Methods - Bullish_study"]={en:["Rising Three Methods - Bullish"],hu_HU:["Rising Three Methods - Bullish"]},e.exports["Rising Window - Bullish_study"]={en:["Rising Window - Bullish"],hu_HU:["Rising Window - Bullish"]},e.exports["Shooting Star - Bearish_study"]={en:["Shooting Star - Bearish"],hu_HU:["Shooting Star - Bearish"]},e.exports["Three Black Crows - Bearish_study"]={en:["Three Black Crows - Bearish"],hu_HU:["Three Black Crows - Bearish"]},e.exports["Three White Soldiers - Bullish_study"]={en:["Three White Soldiers - Bullish"],hu_HU:["Three White Soldiers - Bullish"]},e.exports["Tri-Star - Bearish_study"]={en:["Tri-Star - Bearish"],hu_HU:["Tri-Star - Bearish"]},e.exports["Tri-Star - Bullish_study"]={en:["Tri-Star - Bullish"],hu_HU:["Tri-Star - Bullish"]},e.exports["Tweezer Top - Bearish_study"]={en:["Tweezer Top - Bearish"],hu_HU:["Tweezer Top - Bearish"]},e.exports["Upside Tasuki Gap - Bullish_study"]={en:["Upside Tasuki Gap - Bullish"],hu_HU:["Upside Tasuki Gap - Bullish"]},e.exports.SuperTrend_study={en:["SuperTrend"],hu_HU:["SuperTrend"]},e.exports["Average Price_study"]={en:["Average Price"],hu_HU:["Average Price"]},e.exports["Typical Price_study"]={en:["Typical Price"],hu_HU:["Typical Price"]},e.exports["Median Price_study"]={en:["Median Price"],hu_HU:["Median Price"]},e.exports["Money Flow Index_study"]={en:["Money Flow Index"],hu_HU:["Money Flow Index"]},e.exports["Moving Average Double_study"]={en:["Moving Average Double"],hu_HU:["Moving Average Double"]},e.exports["Moving Average Triple_study"]={en:["Moving Average Triple"],hu_HU:["Moving Average Triple"]},e.exports["Moving Average Adaptive_study"]={en:["Moving Average Adaptive"],hu_HU:["Moving Average Adaptive"]},e.exports["Moving Average Hamming_study"]={en:["Moving Average Hamming"],hu_HU:["Moving Average Hamming"]},e.exports["Moving Average Modified_study"]={en:["Moving Average Modified"],hu_HU:["Moving Average Modified"]},e.exports["Moving Average Multiple_study"]={en:["Moving Average Multiple"],hu_HU:["Moving Average Multiple"]},e.exports["Linear Regression Slope_study"]={en:["Linear Regression Slope"],hu_HU:["Linear Regression Slope"]},e.exports["Standard Error_study"]={en:["Standard Error"],hu_HU:["Standard Error"]},e.exports["Standard Error Bands_study"]={en:["Standard Error Bands"],hu_HU:["Standard Error Bands"]},e.exports["Correlation - Log_study"]={en:["Correlation - Log"],hu_HU:["Correlation - Log"]},e.exports["Standard Deviation_study"]={en:["Standard Deviation"],hu_HU:["Standard Deviation"]},e.exports["Chaikin Volatility_study"]={en:["Chaikin Volatility"],hu_HU:["Chaikin Volatility"]},e.exports["Volatility Close-to-Close_study"]={en:["Volatility Close-to-Close"],hu_HU:["Volatility Close-to-Close"]},e.exports["Volatility Zero Trend Close-to-Close_study"]={en:["Volatility Zero Trend Close-to-Close"],hu_HU:["Volatility Zero Trend Close-to-Close"]},e.exports["Volatility O-H-L-C_study"]={en:["Volatility O-H-L-C"],
hu_HU:["Volatility O-H-L-C"]},e.exports["Volatility Index_study"]={en:["Volatility Index"],hu_HU:["Volatility Index"]},e.exports["Trend Strength Index_study"]={en:["Trend Strength Index"],hu_HU:["Trend Strength Index"]},e.exports["Majority Rule_study"]={en:["Majority Rule"],hu_HU:["Majority Rule"]},e.exports["Advance Decline Line_study"]={en:["Advance Decline Line"],hu_HU:["Növekedés Zuhanás Vonal"]},e.exports["Advance Decline Ratio_study"]={en:["Advance Decline Ratio"],hu_HU:["Növekedés Zuhanás Arány"]},e.exports["Advance/Decline Ratio (Bars)_study"]={en:["Advance/Decline Ratio (Bars)"],hu_HU:["Növekedés/Zuhanás Arány (Bárok)"]},e.exports["BarUpDn Strategy_study"]={en:["BarUpDn Strategy"],hu_HU:["BarUpDn Strategy"]},e.exports["Bollinger Bands Strategy directed_study"]={en:["Bollinger Bands Strategy directed"],hu_HU:["Bollinger Bands Strategy directed"]},e.exports["Bollinger Bands Strategy_study"]={en:["Bollinger Bands Strategy"],hu_HU:["Bollinger Szalagok Stratégia"]},e.exports.ChannelBreakOutStrategy_study={en:["ChannelBreakOutStrategy"],hu_HU:["ChannelBreakOutStrategy"]},e.exports.Compare_study={en:["Compare"],hu_HU:["Összehasonlít"]},e.exports["Conditional Expressions_study"]={en:["Conditional Expressions"],hu_HU:["Feltételes Kifejezések"]},e.exports.ConnorsRSI_study={en:["ConnorsRSI"],hu_HU:["ConnorsRSI"]},e.exports["Consecutive Up/Down Strategy_study"]={en:["Consecutive Up/Down Strategy"],hu_HU:["Consecutive Up/Down Strategy"]},e.exports["Cumulative Volume Index_study"]={en:["Cumulative Volume Index"],hu_HU:["Kumulatív Volumenindex"]},e.exports["Divergence Indicator_study"]={en:["Divergence Indicator"],hu_HU:["Divergencia Indikátor"]},e.exports["Greedy Strategy_study"]={en:["Greedy Strategy"],hu_HU:["Greedy Stratégia"]},e.exports["InSide Bar Strategy_study"]={en:["InSide Bar Strategy"],hu_HU:["InSide Bar Stratégia"]},e.exports["Keltner Channel Strategy_study"]={en:["Keltner Channel Strategy"],hu_HU:["Keltner Channel Stratégia"]},e.exports["Linear Regression_study"]={en:["Linear Regression"],hu_HU:["Lineáris Regresszió"]},e.exports["MACD Strategy_study"]={en:["MACD Strategy"],hu_HU:["MACD Stratégia"]},e.exports["Momentum Strategy_study"]={en:["Momentum Strategy"],hu_HU:["Momentum Stratégia"]},e.exports["Moon Phases_study"]={en:["Moon Phases"],hu_HU:["Holdfázisok"]},e.exports["Moving Average Convergence/Divergence_study"]={en:["Moving Average Convergence/Divergence"],hu_HU:["Mozgóátlag Konvergencia/Divergencia"]},e.exports["MovingAvg Cross_study"]={en:["MovingAvg Cross"],hu_HU:["MovingAvg Cross"]},e.exports["MovingAvg2Line Cross_study"]={en:["MovingAvg2Line Cross"],hu_HU:["MovingAvg2Line Cross"]},e.exports["OutSide Bar Strategy_study"]={en:["OutSide Bar Strategy"],hu_HU:["OutSide Bar Stratégia"]},e.exports.Overlay_study={en:["Overlay"],hu_HU:["Overlay"]},e.exports["Parabolic SAR Strategy_study"]={en:["Parabolic SAR Strategy"],hu_HU:["Parabolic SAR Stratégia"]},e.exports["Pivot Extension Strategy_study"]={en:["Pivot Extension Strategy"],hu_HU:["Pivot Extension Stratégia"]},
e.exports["Pivot Points High Low_study"]={en:["Pivot Points High Low"],hu_HU:["Pivotális Pontok Max Min"]},e.exports["Pivot Reversal Strategy_study"]={en:["Pivot Reversal Strategy"],hu_HU:["Pivot Reversal Stratégia"]},e.exports["Price Channel Strategy_study"]={en:["Price Channel Strategy"],hu_HU:["Price Channel Stratégia"]},e.exports["RSI Strategy_study"]={en:["RSI Strategy"],hu_HU:["RSI Stratégia"]},e.exports["SMI Ergodic Indicator_study"]={en:["SMI Ergodic Indicator"],hu_HU:["SMI Ergodikus Indikátor"]},e.exports["SMI Ergodic Oscillator_study"]={en:["SMI Ergodic Oscillator"],hu_HU:["SMI Ergodikus Oszcillátor"]},e.exports["Stochastic Slow Strategy_study"]={en:["Stochastic Slow Strategy"],hu_HU:["Stochastic Slow Stratégia"]},e.exports["Volatility Stop_study"]={en:["Volatility Stop"],hu_HU:["Volatilitás Stop"]},e.exports["Volty Expan Close Strategy_study"]={en:["Volty Expan Close Strategy"],hu_HU:["Volty Expan Close Stratégia"]},e.exports["Woodies CCI_study"]={en:["Woodies CCI"],hu_HU:["Woodies CCI"]},e.exports["Anchored Volume Profile_study"]={en:["Anchored Volume Profile"],hu_HU:["Anchored Volume Profile"]},e.exports["Trading Sessions_study"]={en:["Trading Sessions"],hu_HU:["Trading Sessions"]},e.exports["Cup and Handle Chart Pattern_study"]={en:["Cup and Handle Chart Pattern"],hu_HU:["Cup and Handle Chart Pattern"]},e.exports["Inverted Cup and Handle Chart Pattern_study"]={en:["Inverted Cup and Handle Chart Pattern"],hu_HU:["Inverted Cup and Handle Chart Pattern"]},e.exports["Head and Shoulders Chart Pattern_study"]={en:["Head and Shoulders Chart Pattern"],hu_HU:["Head and Shoulders Chart Pattern"]},e.exports["Inverted Head and Shoulders Chart Pattern_study"]={en:["Inverted Head and Shoulders Chart Pattern"],hu_HU:["Inverted Head and Shoulders Chart Pattern"]}},89633:e=>{e.exports={en:["Anchored Volume Profile"],hu_HU:["Anchored Volume Profile"]}},25705:e=>{e.exports={en:["Fixed Range Volume Profile"],hu_HU:["Fixed Range Volume Profile"]}},24261:e=>{e.exports={en:["Vol"],hu_HU:["Vol"]}},51077:e=>{e.exports={en:["Minor"],hu_HU:["Kicsi"]}},922:e=>{e.exports={en:["Minute"],hu_HU:["Perc"]}},91405:e=>{e.exports={en:["Text"],hu_HU:["Text"]}},78972:e=>{e.exports={en:["Couldn't copy"],hu_HU:["Couldn't copy"]}},10615:e=>{e.exports={en:["Couldn't cut"],hu_HU:["Couldn't cut"]}},81518:e=>{e.exports={en:["Couldn't paste"],hu_HU:["Couldn't paste"]}},83140:e=>{e.exports={en:["Countdown to bar close"],hu_HU:["Countdown To Bar Close"]}},10871:e=>{e.exports={en:["Colombo"],hu_HU:["Colombo"]}},55761:e=>{e.exports={en:["Columns"],hu_HU:["Oszlopok"]}},9818:e=>{e.exports={en:["Comment"],hu_HU:["Komment"]}},53942:e=>{e.exports={en:["Compare or Add Symbol"],hu_HU:["Összehasonlítás vagy Szimbólum Hozzáadása"]}},12086:e=>{e.exports={en:["Compilation error"],hu_HU:["Compilation error"]}},48141:e=>{e.exports={en:["Confirm Inputs"],hu_HU:["Inputok Megerősítése"]}},38917:e=>{e.exports={en:["Copenhagen"],hu_HU:["Copenhagen"]}},49680:e=>{e.exports={en:["Copy"],hu_HU:["Másolás"]}},66134:e=>{e.exports={en:["Copy Chart Layout"],
hu_HU:["Chart Elrendezés Másolása"]}},63553:e=>{e.exports={en:["Copy price"],hu_HU:["Copy price"]}},65736:e=>{e.exports={en:["Cairo"],hu_HU:["Cairo"]}},25381:e=>{e.exports={en:["Callout"],hu_HU:["Kiemelő"]}},45054:e=>{e.exports={en:["Candles"],hu_HU:["Gyertyák"]}},30948:e=>{e.exports={en:["Caracas"],hu_HU:["Caracas"]}},70409:e=>{e.exports={en:["Casablanca"],hu_HU:["Casablanca"]}},37276:e=>{e.exports={en:["Change"],hu_HU:["Változás"]}},85124:e=>{e.exports={en:["Change Symbol"],hu_HU:["Szimbólum Változtatás"]}},2569:e=>{e.exports={en:["Change interval"],hu_HU:["Intervallum Váltás"]}},9687:e=>{e.exports={en:["Change interval. Press number or comma"],hu_HU:["Change interval. Press number or comma"]}},36332:e=>{e.exports={en:["Change symbol. Start typing symbol name"],hu_HU:["Change symbol. Start typing symbol name"]}},48566:e=>{e.exports={en:["Change scale currency"],hu_HU:["Change scale currency"]}},85110:e=>{e.exports={en:["Change scale unit"],hu_HU:["Change scale unit"]}},56275:e=>{e.exports={en:["Chart #{index}"],hu_HU:["Chart #{index}"]}},39950:e=>{e.exports={en:["Chart Properties"],hu_HU:["Chart Tulajdonságok"]}},98856:e=>{e.exports={en:["Chart by TradingView"],hu_HU:["Chart by TradingView"]}},1136:e=>{e.exports={en:["Chart for {symbol}, {interval}"],hu_HU:["Chart for {symbol}, {interval}"]}},69804:e=>{e.exports={en:["Chart image copied to clipboard {emoji}"],hu_HU:["Chart image copied to clipboard {emoji}"]}},6655:e=>{e.exports={en:["Chart image embed code copied to clipboard {emoji}"],hu_HU:["Chart image embed code copied to clipboard {emoji}"]}},36549:e=>{e.exports={en:["Chatham Islands"],hu_HU:["Chatham-szigetek"]}},72452:e=>{e.exports={en:["Chicago"],hu_HU:["Chicago"]}},50349:e=>{e.exports={en:["Chongqing"],hu_HU:["Chongqing"]}},91944:e=>{e.exports={en:["Circle"],hu_HU:["Kör"]}},14985:e=>{e.exports={en:["Click to set a point"],hu_HU:["Klikkelj a pont megadásához"]}},12537:e=>{e.exports={en:["Clone"],hu_HU:["Klón"]}},62578:e=>{e.exports={en:["Close"],hu_HU:["Zárás"]}},264:e=>{e.exports={en:["Create limit order"],hu_HU:["Create limit order"]}},6969:e=>{e.exports={en:["Cross"],hu_HU:["Kereszt"]}},74334:e=>{e.exports={en:["Cross Line"],hu_HU:["Cross Line"]}},59396:e=>{e.exports={en:["Currencies"],hu_HU:["Devizák"]}},20177:e=>{e.exports={en:["Current interval and above"],hu_HU:["Current interval and above"]}},494:e=>{e.exports={en:["Current interval and below"],hu_HU:["Current interval and below"]}},60668:e=>{e.exports={en:["Current interval only"],hu_HU:["Current interval only"]}},78609:e=>{e.exports={en:["Curve"],hu_HU:["Görbe"]}},87380:e=>{e.exports={en:["Cycle"],hu_HU:["Ciklus"]}},84031:e=>{e.exports={en:["Cyclic Lines"],hu_HU:["Ciklikus Vonalak"]}},93191:e=>{e.exports={en:["Cypher Pattern"],hu_HU:["Rejtjel Minta"]}},7219:e=>{e.exports={en:["A layout with that name already exists"],hu_HU:["A layout with that name already exists"]}},67635:e=>{e.exports={en:["A layout with that name already exists. Do you want to overwrite it?"],hu_HU:["A layout with that name already exists. Do you want to overwrite it?"]}},
46712:e=>{e.exports={en:["ABCD Pattern"],hu_HU:["ABCD Minta"]}},36485:e=>{e.exports={en:["Amsterdam"],hu_HU:["Amsterdam"]}},42630:e=>{e.exports={en:["Anchorage"],hu_HU:["Anchorage"]}},63209:e=>{e.exports={en:["Anchored Note"],hu_HU:["Horgony Megjegyzés"]}},42669:e=>{e.exports={en:["Anchored Text"],hu_HU:["Horgony Szöveg"]}},84541:e=>{e.exports={en:["Anchored VWAP"],hu_HU:["Anchored VWAP"]}},77401:e=>{e.exports={en:["Access error"],hu_HU:["Access error"]}},46501:e=>{e.exports={en:["Add Symbol"],hu_HU:["Szimbólum Hozzáadása"]}},69709:e=>{e.exports={en:["Add alert on {title}"],hu_HU:["Add alert on {title}"]}},89295:e=>{e.exports={en:["Add alert on {title} at {price}"],hu_HU:["Add alert on {title} at {price}"]}},2439:e=>{e.exports={en:["Add financial metric for {instrumentName}"],hu_HU:["Add financial metric for {instrumentName}"]}},35088:e=>{e.exports={en:["Add indicator/strategy on {studyTitle}"],hu_HU:["Add indicator/strategy on {studyTitle}"]}},39873:e=>{e.exports={en:["Add text note for {symbol}"],hu_HU:["Add Text Note for {symbol}"]}},35679:e=>{e.exports={en:["Add this financial metric to entire layout"],hu_HU:["Add this financial metric to entire layout"]}},10996:e=>{e.exports={en:["Add this financial metric to favorites"],hu_HU:["Add this financial metric to favorites"]}},26090:e=>{e.exports={en:["Add this indicator to entire layout"],hu_HU:["Add this indicator to entire layout"]}},92957:e=>{e.exports={en:["Add this indicator to favorites"],hu_HU:["Add this indicator to favorites"]}},95754:e=>{e.exports={en:["Add this strategy to entire layout"],hu_HU:["Add this strategy to entire layout"]}},39010:e=>{e.exports={en:["Add this symbol to entire layout"],hu_HU:["Add this symbol to entire layout"]}},426:e=>{e.exports={en:["Adelaide"],hu_HU:["Adelaide"]}},40452:e=>{e.exports={en:["Always invisible"],hu_HU:["Mindig Láthatatlan"]}},36299:e=>{e.exports={en:["Always visible"],hu_HU:["Mindig Látható"]}},81442:e=>{e.exports={en:["All indicators and drawing tools"],hu_HU:["Összes Indikátor és Rajzeszköz"]}},58026:e=>{e.exports={en:["All intervals"],hu_HU:["All intervals"]}},78358:e=>{e.exports={en:["Apply default"],hu_HU:["Alapértelmezett Beállítás"]}},22437:e=>{e.exports={en:["Apply these indicators to entire layout"],hu_HU:["Apply these indicators to entire layout"]}},27072:e=>{e.exports={en:["Apr"],hu_HU:["Ápr"]}},59324:e=>{e.exports={en:["Arc"],hu_HU:["Ív"]}},34456:e=>{e.exports={en:["Area"],hu_HU:["Terület"]}},11858:e=>{e.exports={en:["Arrow"],hu_HU:["Nyíl"]}},34247:e=>{e.exports={en:["Arrow Down"],hu_HU:["Arrow Down"]}},36352:e=>{e.exports={en:["Arrow Marker"],hu_HU:["Arrow Marker"]}},73193:e=>{e.exports={en:["Arrow Mark Down"],hu_HU:["Nyíl Lefelé"]}},1949:e=>{e.exports={en:["Arrow Mark Left"],hu_HU:["Nyíl Balra"]}},86275:e=>{e.exports={en:["Arrow Mark Right"],hu_HU:["Nyíl Jobbra"]}},62453:e=>{e.exports={en:["Arrow Mark Up"],hu_HU:["Nyíl Felfelé"]}},77231:e=>{e.exports={en:["Arrow Up"],hu_HU:["Arrow Up"]}},98128:e=>{e.exports={en:["Astana"],hu_HU:["Astana"]}},63627:e=>{e.exports={en:["Ashgabat"],hu_HU:["Asgábád"]}},
72445:e=>{e.exports={en:["At close"],hu_HU:["At close"]}},73702:e=>{e.exports={en:["Athens"],hu_HU:["Athén"]}},21469:e=>{e.exports={en:["Auto"],hu_HU:["Auto"]}},24157:e=>{e.exports={en:["Auto (fits data to screen)"],hu_HU:["Auto (Fits Data To Screen)"]}},46450:e=>{e.exports={en:["Aug"],hu_HU:["Aug"]}},21841:e=>{e.exports={en:["Average close price label"],hu_HU:["Average close price label"]}},16138:e=>{e.exports={en:["Average close price line"],hu_HU:["Average close price line"]}},73025:e=>{e.exports={en:["Avg"],hu_HU:["Avg"]}},87580:e=>{e.exports={en:["Azores"]}},73905:e=>{e.exports={en:["Bogota"],hu_HU:["Bogotá"]}},90594:e=>{e.exports={en:["Bahrain"],hu_HU:["Bahrain"]}},70540:e=>{e.exports={en:["Balloon"],hu_HU:["Ballon"]}},47045:e=>{e.exports={en:["Bangkok"],hu_HU:["Bangkok"]}},76651:e=>{e.exports={en:["Bar Replay isn't available for this chart type. Do you want to exit Bar Replay?"],hu_HU:["Bar Replay isn't available for this chart type. Do you want to exit Bar Replay?"]}},68054:e=>{e.exports={en:["Bar Replay isn't available for this time interval. Do you want to exit Bar Replay?"],hu_HU:["Bar Replay isn't available for this time interval. Do you want to exit Bar Replay?"]}},27377:e=>{e.exports={en:["Bars"],hu_HU:["Bárok"]}},81994:e=>{e.exports={en:["Bars Pattern"],hu_HU:["Bár Minta"]}},59213:e=>{e.exports={en:["Baseline"],hu_HU:["Alapvonal"]}},71797:e=>{e.exports={en:["Belgrade"],hu_HU:["Belgrade"]}},64313:e=>{e.exports={en:["Berlin"],hu_HU:["Berlin"]}},43539:e=>{e.exports={en:["Brush"],hu_HU:["Ecset"]}},91499:e=>{e.exports={en:["Brussels"],hu_HU:["Brussels"]}},70876:e=>{e.exports={en:["Bratislava"],hu_HU:["Bratislava"]}},55481:e=>{e.exports={en:["Bring forward"],hu_HU:["Előterjesztés"]}},17293:e=>{e.exports={en:["Bring to front"],hu_HU:["Előrehozás"]}},79336:e=>{e.exports={en:["Brisbane"],hu_HU:["Brisbane"]}},33672:e=>{e.exports={en:["Bucharest"],hu_HU:["Bucharest"]}},20313:e=>{e.exports={en:["Budapest"],hu_HU:["Budapest"]}},25282:e=>{e.exports={en:["Buenos Aires"],hu_HU:["Buenos Aires"]}},46768:e=>{e.exports={en:["By TradingView"],hu_HU:["TradingView Által"]}},54280:e=>{e.exports={en:["Go to date"],hu_HU:["Ugrás dátumhoz"]}},74975:e=>{e.exports={en:["Go to {lineToolName}"],hu_HU:["Go to {lineToolName}"]}},15462:e=>{e.exports={en:["Got it"],hu_HU:["Got it"]}},47460:e=>{e.exports={en:["Gann Box"],hu_HU:["Gann Doboz"]}},48683:e=>{e.exports={en:["Gann Fan"],hu_HU:["Gann Legyező"]}},44763:e=>{e.exports={en:["Gann Square"],hu_HU:["Gann Négyszög"]}},60707:e=>{e.exports={en:["Gann Square Fixed"],hu_HU:["Gann Square Fixed"]}},46808:e=>{e.exports={en:["Ghost Feed"],hu_HU:["Ghost Hírfolyam"]}},57726:e=>{e.exports={en:["Grand supercycle"],hu_HU:["Nagy Szuperciklus"]}},34914:e=>{e.exports={en:["Do you really want to delete Study Template '{name}' ?"],hu_HU:["Biztos, hogy törölni akarod ezt a tanulmánysablont: {name}?"]}},77125:e=>{e.exports={en:["Double Curve"],hu_HU:["Dupla Görbe"]}},9430:e=>{e.exports={en:["Double-click any edge to reset layout grid"],hu_HU:["Double-click any edge to reset layout grid"]}},75296:e=>{
e.exports={en:["Double-click to finish Path"],hu_HU:["Double-click to finish Path"]}},17409:e=>{e.exports={en:["Double-click to finish Polyline"],hu_HU:["Double-click to finish Polyline"]}},57131:e=>{e.exports={en:["Data Provided by"],hu_HU:["Data Provided by"]}},62154:e=>{e.exports={en:["Date"],hu_HU:["Dátum"]}},85444:e=>{e.exports={en:["Date Range"],hu_HU:["Időintervallum"]}},47017:e=>{e.exports={en:["Date and Price Range"],hu_HU:["Dátum és Árfolyamtartomány"]}},32084:e=>{e.exports={en:["Dec"],hu_HU:["Dec"]}},23403:e=>{e.exports={en:["Degree"],hu_HU:["Fokozat"]}},27358:e=>{e.exports={en:["Denver"],hu_HU:["Denver"]}},24959:e=>{e.exports={en:["Dhaka"],hu_HU:["Dhaka"]}},15179:e=>{e.exports={en:["Diamond"],hu_HU:["Diamond"]}},91544:e=>{e.exports={en:["Disjoint Channel"],hu_HU:["Disjoint Channel"]}},70132:e=>{e.exports={en:["Displacement"],hu_HU:["Displacement"]}},93864:e=>{e.exports={en:["Drawings toolbar"],hu_HU:["Rajzok Eszköztár"]}},96890:e=>{e.exports={en:["Draw Horizontal Line at"],hu_HU:["Draw Horizontal Line at"]}},23650:e=>{e.exports={en:["Dubai"],hu_HU:["Dubaj"]}},79716:e=>{e.exports={en:["Dublin"],hu_HU:["Dublin"]}},73456:e=>{e.exports={en:["Emoji"],hu_HU:["Emoji"]}},9541:e=>{e.exports={en:["Enter a new chart layout name"],hu_HU:["Add meg az új chart elrendezés nevét"]}},80943:e=>{e.exports={en:["Elliott Correction Wave (ABC)"],hu_HU:["Elliot Korrekciós Hullám (ABC)"]}},75112:e=>{e.exports={en:["Elliott Double Combo Wave (WXY)"],hu_HU:["Elliott Dupla Kombinációs Hullám (WXY)"]}},61114:e=>{e.exports={en:["Elliott Impulse Wave (12345)"],hu_HU:["Elliott Impulzushullám (12345)"]}},72359:e=>{e.exports={en:["Elliott Triangle Wave (ABCDE)"],hu_HU:["Elliott Háromszög Hullám (ABCDE)"]}},76129:e=>{e.exports={en:["Elliott Triple Combo Wave (WXYXZ)"],hu_HU:["Elliott Tripla Kombinációs Hullám (WXYXZ)"]}},78996:e=>{e.exports={en:["Ellipse"],hu_HU:["Ellipszis"]}},52788:e=>{e.exports={en:["Extended Line"],hu_HU:["Extended Line"]}},86905:e=>{e.exports={en:["Exchange"],hu_HU:["Tőzsde"]}},19271:e=>{e.exports={en:["Existing pane above"],hu_HU:["Existing Pane Above"]}},46545:e=>{e.exports={en:["Existing pane below"],hu_HU:["Existing Pane Below"]}},20138:e=>{e.exports={en:["Forecast"],hu_HU:["Előrejelzés"]}},2507:e=>{e.exports={en:["Feb"],hu_HU:["Feb"]}},59005:e=>{e.exports={en:["Fib Channel"],hu_HU:["Fib Csatorna"]}},82330:e=>{e.exports={en:["Fib Circles"],hu_HU:["Fib Körök"]}},55986:e=>{e.exports={en:["Fib Retracement"],hu_HU:["Fib Retracement"]}},33880:e=>{e.exports={en:["Fib Speed Resistance Arcs"],hu_HU:["Fib Speed Ellenállás Ívek"]}},2395:e=>{e.exports={en:["Fib Speed Resistance Fan"],hu_HU:["Fib Speed Ellenállás Fan"]}},39014:e=>{e.exports={en:["Fib Spiral"],hu_HU:["Fib Spirál"]}},30622:e=>{e.exports={en:["Fib Time Zone"],hu_HU:["Fib Időzóna"]}},85042:e=>{e.exports={en:["Fib Wedge"],hu_HU:["Fib Ék"]}},33885:e=>{e.exports={en:["Flag"],hu_HU:["Flag"]}},14600:e=>{e.exports={en:["Flag Mark"],hu_HU:["Zászló Jel"]}},45051:e=>{e.exports={en:["Flat Top/Bottom"],hu_HU:["Lapos Felső/Alsó"]}},63271:e=>{e.exports={en:["Flipped"],
hu_HU:["Flippelt"]}},39643:e=>{e.exports={en:["Fraction part is invalid."],hu_HU:["Érvénytelen törtrész."]}},24077:e=>{e.exports={en:["Fundamental studies are no longer available on charts"],hu_HU:["Fundamental studies are no longer available on charts"]}},31561:e=>{e.exports={en:["Kolkata"],hu_HU:["Kalkutta"]}},54533:e=>{e.exports={en:["Kathmandu"],hu_HU:["Kathmandu"]}},83490:e=>{e.exports={en:["Kagi"],hu_HU:["Kagi"]}},70913:e=>{e.exports={en:["Karachi"],hu_HU:["Karachi"]}},76614:e=>{e.exports={en:["Kuwait"],hu_HU:["Kuwait"]}},38561:e=>{e.exports={en:["Kuala Lumpur"],hu_HU:["Kuala Lumpur"]}},99906:e=>{e.exports={en:["HLC area"],hu_HU:["HLC area"]}},34491:e=>{e.exports={en:["Ho Chi Minh"],hu_HU:["Ho Chi Minh"]}},13459:e=>{e.exports={en:["Hollow candles"],hu_HU:["Áttetsző Gyertyák"]}},48861:e=>{e.exports={en:["Hong Kong"],hu_HU:["Hong Kong"]}},79668:e=>{e.exports={en:["Honolulu"],hu_HU:["Honolulu"]}},21795:e=>{e.exports={en:["Horizontal Line"],hu_HU:["Vízszintes Vonal"]}},25487:e=>{e.exports={en:["Horizontal Ray"],hu_HU:["Vízszintes Sugár"]}},21928:e=>{e.exports={en:["Head and Shoulders"],hu_HU:["Head and Shoulders"]}},63876:e=>{e.exports={en:["Heikin Ashi"],hu_HU:["Heikin Ashi"]}},48203:e=>{e.exports={en:["Helsinki"],hu_HU:["Helsinki"]}},27298:e=>{e.exports={en:["Hide"],hu_HU:["Elrejt"]}},47074:e=>{e.exports={en:["Hide all"],hu_HU:["Hide all"]}},52563:e=>{e.exports={en:["Hide all drawings"],hu_HU:["Hide all drawings"]}},90763:e=>{e.exports={en:["Hide all drawings and indicators"],hu_HU:["Hide all drawings and indicators"]}},18216:e=>{e.exports={en:["Hide all drawings, indicators, positions & orders"],hu_HU:["Hide all drawings, indicators, positions & orders"]}},78525:e=>{e.exports={en:["Hide all indicators"],hu_HU:["Hide all indicators"]}},42164:e=>{e.exports={en:["Hide all positions & orders"],hu_HU:["Hide all positions & orders"]}},3217:e=>{e.exports={en:["Hide drawings"],hu_HU:["Hide drawings"]}},97878:e=>{e.exports={en:["Hide events on chart"],hu_HU:["Események Elrejtése a Chartról"]}},72351:e=>{e.exports={en:["Hide indicators"],hu_HU:["Hide indicators"]}},28345:e=>{e.exports={en:["Hide marks on bars"],hu_HU:["Jelölések Elrejtése a Bárokon"]}},92226:e=>{e.exports={en:["Hide positions & orders"],hu_HU:["Hide positions & orders"]}},78254:e=>{e.exports={en:["High"],hu_HU:["Max"]}},98236:e=>{e.exports={en:["High-low"],hu_HU:["High-low"]}},99479:e=>{e.exports={en:["High and low price labels"],hu_HU:["High and low price labels"]}},33766:e=>{e.exports={en:["High and low price lines"],hu_HU:["High and low price lines"]}},69476:e=>{e.exports={en:["Highlighter"],hu_HU:["Highlighter"]}},72819:e=>{e.exports={en:["Histogram is too large, please increase {boldHighlightStart}Row Size{boldHighlightEnd} in the settings window."],hu_HU:["Histogram is too large, please increase {boldHighlightStart}Row Size{boldHighlightEnd} in the settings window."]}},94966:e=>{e.exports={en:["Histogram is too large, please increase {boldHighlightStart}Ticks Per Row{boldHighlightEnd} in the settings window."],
hu_HU:["Histogram is too large, please increase {boldHighlightStart}Ticks Per Row{boldHighlightEnd} in the settings window."]}},66751:e=>{e.exports={en:["Histogram is too large, please reduce {boldHighlightStart}Row Size{boldHighlightEnd} in the settings window."],hu_HU:["Histogram is too large, please reduce {boldHighlightStart}Row Size{boldHighlightEnd} in the settings window."]}},68065:e=>{e.exports={en:["Image"],hu_HU:["Image"]}},80185:e=>{e.exports={en:["Intervals less than {resolution} are not supported for {ticker}."],hu_HU:["Intervals less than {resolution} are not supported for {ticker}."]}},10268:e=>{e.exports={en:["Intermediate"],hu_HU:["Közbülső"]}},14285:e=>{e.exports={en:["Invalid Symbol"],hu_HU:["Érvénytelen Szimbólum"]}},52969:e=>{e.exports={en:["Invalid symbol"],hu_HU:["Invalid symbol"]}},37189:e=>{e.exports={en:["Invert scale"],hu_HU:["Invert Scale"]}},89999:e=>{e.exports={en:["Indexed to 100"],hu_HU:["Indexed to 100"]}},46850:e=>{e.exports={en:["Indicators value labels"],hu_HU:["Indicators value labels"]}},54418:e=>{e.exports={en:["Indicators name labels"],hu_HU:["Indicators name labels"]}},40490:e=>{e.exports={en:["Indicators, Metrics and Strategies. Press slash"],hu_HU:["Indicators, Metrics and Strategies. Press slash"]}},15992:e=>{e.exports={en:["Info Line"],hu_HU:["Info Line"]}},87829:e=>{e.exports={en:["Insert indicator"],hu_HU:["Indikátor Beillesztés"]}},91612:e=>{e.exports={en:["Inside"],hu_HU:["Belső"]}},41686:e=>{e.exports={en:["Inside Pitchfork"],hu_HU:["Belső Villa"]}},37913:e=>{e.exports={en:["Icon"],hu_HU:["Ikon"]}},78326:e=>{e.exports={en:["Istanbul"],hu_HU:["Isztambul"]}},39585:e=>{e.exports={en:["Johannesburg"],hu_HU:["Johannesburg"]}},14995:e=>{e.exports={en:["Jakarta"],hu_HU:["Jakarta"]}},62310:e=>{e.exports={en:["Jan"],hu_HU:["Jan"]}},36057:e=>{e.exports={en:["Jerusalem"],hu_HU:["Jerusalem"]}},53786:e=>{e.exports={en:["Jul"],hu_HU:["Júl"]}},429:e=>{e.exports={en:["Jun"],hu_HU:["Jún"]}},67560:e=>{e.exports={en:["Juneau"],hu_HU:["Juneau"]}},62329:e=>{e.exports={en:["On the left"],hu_HU:["On the left"]}},55813:e=>{e.exports={en:["On the right"],hu_HU:["On the right"]}},64818:e=>{e.exports={en:["Only {availableResolutions} intervals are supported for {ticker}."],hu_HU:["Only {availableResolutions} intervals are supported for {ticker}."]}},21064:e=>{e.exports={en:["Oops!"],hu_HU:["Hoppá!"]}},51221:e=>{e.exports={en:["Object Tree"],hu_HU:["Object Tree"]}},12179:e=>{e.exports={en:["Oct"],hu_HU:["Okt"]}},16610:e=>{e.exports={en:["Open"],hu_HU:["Nyitó"]}},46005:e=>{e.exports={en:["Original"],hu_HU:["Eredeti"]}},75722:e=>{e.exports={en:["Oslo"],hu_HU:["Oslo"]}},65318:e=>{e.exports={en:["Low"],hu_HU:["Min"]}},55382:e=>{e.exports={en:["Load layout. Press period"],hu_HU:["Load layout. Press period"]}},5837:e=>{e.exports={en:["Lock"],hu_HU:["Zárás"]}},79777:e=>{e.exports={en:["Lock/unlock"],hu_HU:["Zárás/Feloldás"]}},66005:e=>{e.exports={en:["Lock vertical cursor line by time"],hu_HU:["Lock vertical cursor line by time"]}},14017:e=>{e.exports={en:["Lock price to bar ratio"],
hu_HU:["Lock Price To Bar Ratio"]}},16170:e=>{e.exports={en:["Logarithmic"],hu_HU:["Logarithmic"]}},19439:e=>{e.exports={en:["London"],hu_HU:["London"]}},74832:e=>{e.exports={en:["Long Position"],hu_HU:["Long Pozíció"]}},28733:e=>{e.exports={en:["Los Angeles"],hu_HU:["Los Angeles"]}},85924:e=>{e.exports={en:["Label Down"],hu_HU:["Label Down"]}},52402:e=>{e.exports={en:["Label Up"],hu_HU:["Label Up"]}},5119:e=>{e.exports={en:["Labels"],hu_HU:["Címkék"]}},19931:e=>{e.exports={en:["Lagos"],hu_HU:["Lagos"]}},63815:e=>{e.exports={en:["Last day change"],hu_HU:["Last day change"]}},59444:e=>{e.exports={en:["Lima"],hu_HU:["Lima"]}},3554:e=>{e.exports={en:["Line"],hu_HU:["Vonal"]}},9394:e=>{e.exports={en:["Line with markers"],hu_HU:["Jelölésekkel"]}},43588:e=>{e.exports={en:["Line break"],hu_HU:["Vonaltörés"]}},56982:e=>{e.exports={en:["Lines"],hu_HU:["Lines"]}},37367:e=>{e.exports={en:["Link to the chart image copied to clipboard {emoji}"],hu_HU:["Link to the chart image copied to clipboard {emoji}"]}},53375:e=>{e.exports={en:["Lisbon"],hu_HU:["Lisbon"]}},81038:e=>{e.exports={en:["Luxembourg"],hu_HU:["Luxembourg"]}},60663:e=>{e.exports={en:["Move the point to position the anchor then tap to place"],hu_HU:["Move the point to position the anchor then tap to place"]}},35049:e=>{e.exports={en:["Move to"],hu_HU:["Move to"]}},26493:e=>{e.exports={en:["Move scale to left"],hu_HU:["Move scale to left"]}},40789:e=>{e.exports={en:["Move scale to right"],hu_HU:["Move scale to right"]}},70382:e=>{e.exports={en:["Modified Schiff"],hu_HU:["Módosított Schiff"]}},57681:e=>{e.exports={en:["Modified Schiff Pitchfork"],hu_HU:["Módosított Schiff Villa"]}},93907:e=>{e.exports={en:["More settings"]}},64039:e=>{e.exports={en:["Moscow"],hu_HU:["Moszkva"]}},52066:e=>{e.exports={en:["Madrid"],hu_HU:["Madrid"]}},38365:e=>{e.exports={en:["Malta"],hu_HU:["Malta"]}},48991:e=>{e.exports={en:["Manila"],hu_HU:["Manila"]}},92767:e=>{e.exports={en:["Mar"],hu_HU:["Már"]}},73332:e=>{e.exports={en:["Mexico City"],hu_HU:["Mexikóváros"]}},88314:e=>{e.exports={en:["Merge all scales into one"],hu_HU:["Merge all scales into one"]}},54215:e=>{e.exports={en:["Mixed"],hu_HU:["Mixed"]}},24866:e=>{e.exports={en:["Micro"],hu_HU:["Mikro"]}},87957:e=>{e.exports={en:["Millennium"],hu_HU:["Évezred"]}},14724:e=>{e.exports={en:["Minuette"],hu_HU:["Menüett"]}},78273:e=>{e.exports={en:["Minuscule"],hu_HU:["Minuscule"]}},28941:e=>{e.exports={en:["Mirrored"],hu_HU:["Tükrözött"]}},9865:e=>{e.exports={en:["Muscat"],hu_HU:["Muscat"]}},96935:e=>{e.exports={en:["N/A"],hu_HU:["N/A"]}},36252:e=>{e.exports={en:["No data here"],hu_HU:["No data here"]}},11254:e=>{e.exports={en:["No scale (fullscreen)"],hu_HU:["No Scale (Fullscreen)"]}},9140:e=>{e.exports={en:["No sync"],hu_HU:["No sync"]}},50910:e=>{e.exports={en:["No volume data"],hu_HU:["No volume data"]}},94389:e=>{e.exports={en:["Note"],hu_HU:["Megjegyzés"]}},26899:e=>{e.exports={en:["Nov"],hu_HU:["Nov"]}},67891:e=>{e.exports={en:["Norfolk Island"],hu_HU:["Norfolk Island"]}},40977:e=>{e.exports={en:["Nairobi"],hu_HU:["Nairobi"]}},
40544:e=>{e.exports={en:["New York"],hu_HU:["New York"]}},66103:e=>{e.exports={en:["New Zealand"],hu_HU:["Új-Zéland"]}},15512:e=>{e.exports={en:["New pane above"],hu_HU:["New pane above"]}},52160:e=>{e.exports={en:["New pane below"],hu_HU:["New pane below"]}},15402:e=>{e.exports={en:["Next time you can use {shortcut} for quick paste"],hu_HU:["Next time you can use {shortcut} for quick paste"]}},94600:e=>{e.exports={en:["Nicosia"],hu_HU:["Nicosia"]}},73013:e=>{e.exports={en:["Something went wrong"],hu_HU:["Valami hiba történt"]}},4509:e=>{e.exports={en:["Something went wrong. Please try again later."],hu_HU:["Something went wrong. Please try again later."]}},43047:e=>{e.exports={en:["Save New Chart Layout"],hu_HU:["Új Chart Elrendezés Mentése"]}},76266:e=>{e.exports={en:["Save as"],hu_HU:["Mentés Másként"]}},55502:e=>{e.exports={en:["San Salvador"],hu_HU:["San Salvador"]}},30231:e=>{e.exports={en:["Santiago"],hu_HU:["Santiago"]}},91912:e=>{e.exports={en:["Sao Paulo"],hu_HU:["São Paulo"]}},43931:e=>{e.exports={en:["Scale currency"],hu_HU:["Scale currency"]}},43758:e=>{e.exports={en:["Scale price chart only"],hu_HU:["Csak az Árskála Chart"]}},40012:e=>{e.exports={en:["Scale unit"],hu_HU:["Scale unit"]}},69904:e=>{e.exports={en:["Schiff"],hu_HU:["Schiff"]}},42608:e=>{e.exports={en:["Schiff Pitchfork"],hu_HU:["Schiff Villa"]}},76078:e=>{e.exports={en:["Script may be not updated if you leave the page."],hu_HU:["Script may be not updated if you leave the page."]}},32514:e=>{e.exports={en:["Settings"],hu_HU:["Beállítások"]}},70784:e=>{e.exports={en:["Second fraction part is invalid."],hu_HU:["A második törtrész érvénytelen."]}},75594:e=>{e.exports={en:["Security info"],hu_HU:["Security info"]}},21973:e=>{e.exports={en:["Send to back"],hu_HU:["Visszaküldés"]}},71179:e=>{e.exports={en:["Send backward"],hu_HU:["Hátrébb Küldés"]}},26820:e=>{e.exports={en:["Seoul"],hu_HU:["Szöul"]}},6816:e=>{e.exports={en:["Sep"],hu_HU:["Szep"]}},94031:e=>{e.exports={en:["Session"],hu_HU:["Munkamenet"]}},83298:e=>{e.exports={en:["Session volume profile"],hu_HU:["Session volume profile"]}},66707:e=>{e.exports={en:["Session breaks"],hu_HU:["Munkamenet Szünetek"]}},1852:e=>{e.exports={en:["Shanghai"],hu_HU:["Sanghaj"]}},8075:e=>{e.exports={en:["Short Position"],hu_HU:["Short Pozíció"]}},98334:e=>{e.exports={en:["Show"],hu_HU:["Mutat"]}},85891:e=>{e.exports={en:["Show all drawings"],hu_HU:["Show all drawings"]}},25881:e=>{e.exports={en:["Show all drawings and indicators"],hu_HU:["Show all drawings and indicators"]}},86738:e=>{e.exports={en:["Show all drawings, indicators, positions & orders"],hu_HU:["Show all drawings, indicators, positions & orders"]}},98753:e=>{e.exports={en:["Show all indicators"],hu_HU:["Show all indicators"]}},55418:e=>{e.exports={en:["Show all ideas"],hu_HU:["Show All Ideas"]}},20506:e=>{e.exports={en:["Show all positions & orders"],hu_HU:["Show all positions & orders"]}},33158:e=>{e.exports={en:["Show continuous contract switch"],hu_HU:["Show continuous contract switch"]}},81465:e=>{e.exports={en:["Show contract expiration"],
hu_HU:["Show contract expiration"]}},29449:e=>{e.exports={en:["Show dividends"],hu_HU:["Osztalékok Mutatása"]}},37113:e=>{e.exports={en:["Show earnings"],hu_HU:["Nyereség Mutatása"]}},10261:e=>{e.exports={en:["Show ideas of followed users"],hu_HU:["Show Ideas of Followed Users"]}},68112:e=>{e.exports={en:["Show latest news and Minds"],hu_HU:["Show latest news and Minds"]}},44020:e=>{e.exports={en:["Show my ideas only"],hu_HU:["Show My Ideas Only"]}},50849:e=>{e.exports={en:["Show splits"],hu_HU:["Felosztások Mutatása"]}},67751:e=>{e.exports={en:["Signpost"],hu_HU:["Signpost"]}},77377:e=>{e.exports={en:["Singapore"],hu_HU:["Szingapúr"]}},39090:e=>{e.exports={en:["Sine Line"],hu_HU:["Szinuszvonal"]}},66205:e=>{e.exports={en:["Square"],hu_HU:["Négyzet"]}},86146:e=>{e.exports={en:["Studies limit exceeded: {number} studies per layout.\nPlease, remove some studies."],hu_HU:["Studies limit exceeded: {number} studies per layout.\nPlease, remove some studies."]}},92516:e=>{e.exports={en:["Style"],hu_HU:["Stílus"]}},61507:e=>{e.exports={en:["Stack on the left"],hu_HU:["Stack on the left"]}},97800:e=>{e.exports={en:["Stack on the right"],hu_HU:["Stack on the right"]}},85166:e=>{e.exports={en:["Start using keyboard navigation mode. Press {shortcut}"],hu_HU:["Start using keyboard navigation mode. Press {shortcut}"]}},4035:e=>{e.exports={en:["Stay in drawing mode"],hu_HU:["Rajzmódban Marad"]}},69217:e=>{e.exports={en:["Step line"],hu_HU:["Lépcső"]}},43114:e=>{e.exports={en:["Sticker"],hu_HU:["Sticker"]}},86716:e=>{e.exports={en:["Stockholm"],hu_HU:["Stockholm"]}},1145:e=>{e.exports={en:["Submicro"],hu_HU:["Szubmikro"]}},63375:e=>{e.exports={en:["Submillennium"],hu_HU:["Szubévezred"]}},30585:e=>{e.exports={en:["Subminuette"],hu_HU:["Szubminüett"]}},67948:e=>{e.exports={en:["Supercycle"],hu_HU:["Szuperciklus"]}},3348:e=>{e.exports={en:["Supermillennium"],hu_HU:["Szuperévezred"]}},18905:e=>{e.exports={en:["Switch to {resolution}"],hu_HU:["Switch to {resolution}"]}},31622:e=>{e.exports={en:["Sydney"],hu_HU:["Sydney"]}},70963:e=>{e.exports={en:["Symbol Error"],hu_HU:["Symbol Error"]}},32390:e=>{e.exports={en:["Symbol name label"],hu_HU:["Symbol Name Label"]}},10127:e=>{e.exports={en:["Symbol last price label"],hu_HU:["Symbol Last Value Label"]}},39079:e=>{e.exports={en:["Sync globally"],hu_HU:["Sync globally"]}},46607:e=>{e.exports={en:["Sync in layout"],hu_HU:["Sync To All Charts"]}},76519:e=>{e.exports={en:["Point & figure"],hu_HU:["Pont & Ábra"]}},39949:e=>{e.exports={en:["Polyline"],hu_HU:["Sokszögvonal"]}},371:e=>{e.exports={en:["Path"],hu_HU:["Path"]}},59256:e=>{e.exports={en:["Parallel Channel"],hu_HU:["Párhuzamos Csatorna"]}},61879:e=>{e.exports={en:["Paris"],hu_HU:["Párizs"]}},35140:e=>{e.exports={en:["Paste"],hu_HU:["Paste"]}},6919:e=>{e.exports={en:["Percent"],hu_HU:["Percent"]}},24436:e=>{e.exports={en:["Perth"],hu_HU:["Perth"]}},14055:e=>{e.exports={en:["Phoenix"],hu_HU:["Phoenix"]}},34156:e=>{e.exports={en:["Pitchfan"],hu_HU:["Pitchfan"]}},19634:e=>{e.exports={en:["Pitchfork"],hu_HU:["Villa"]}},33110:e=>{e.exports={
en:["Pin to new left scale"],hu_HU:["Pin to new left scale"]}},28280:e=>{e.exports={en:["Pin to new right scale"],hu_HU:["Pin to new right scale"]}},14115:e=>{e.exports={en:["Pin to left scale"],hu_HU:["Pin to left scale"]}},72046:e=>{e.exports={en:["Pin to left scale (hidden)"],hu_HU:["Pin to left scale (hidden)"]}},81054:e=>{e.exports={en:["Pin to right scale"],hu_HU:["Pin to right scale"]}},16986:e=>{e.exports={en:["Pin to right scale (hidden)"],hu_HU:["Pin to right scale (hidden)"]}},60035:e=>{e.exports={en:["Pin to scale (now left)"],hu_HU:["Pin to scale (now left)"]}},94210:e=>{e.exports={en:["Pin to scale (now no scale)"],hu_HU:["Pin to scale (now no scale)"]}},10761:e=>{e.exports={en:["Pin to scale (now right)"],hu_HU:["Pin to scale (now right)"]}},76150:e=>{e.exports={en:["Pin to scale (now {label})"],hu_HU:["Pin to scale (now {label})"]}},29436:e=>{e.exports={en:["Pin to scale {label}"],hu_HU:["Pin to scale {label}"]}},2165:e=>{e.exports={en:["Pin to scale {label} (hidden)"],hu_HU:["Pin to scale {label} (hidden)"]}},90095:e=>{e.exports={en:["Pinned to left scale"],hu_HU:["Pinned to left scale"]}},32538:e=>{e.exports={en:["Pinned to left scale (hidden)"],hu_HU:["Pinned to left scale (hidden)"]}},44579:e=>{e.exports={en:["Pinned to right scale"],hu_HU:["Pinned to right scale"]}},94559:e=>{e.exports={en:["Pinned to right scale (hidden)"],hu_HU:["Pinned to right scale (hidden)"]}},12645:e=>{e.exports={en:["Pinned to scale {label}"],hu_HU:["Pinned to scale {label}"]}},3564:e=>{e.exports={en:["Pinned to scale {label} (hidden)"],hu_HU:["Pinned to scale {label} (hidden)"]}},71566:e=>{e.exports={en:["Plus button"],hu_HU:["Plus button"]}},28298:e=>{e.exports={en:["Please give us a clipboard writing permission in your browser or press {keystroke}"],hu_HU:["Please give us a clipboard writing permission in your browser or press {keystroke}"]}},81248:e=>{e.exports={en:["Prague"],hu_HU:["Prague"]}},81712:e=>{e.exports={en:["Press and hold {key} while zooming to maintain the chart position"],hu_HU:["Press and hold {key} while zooming to maintain the chart position"]}},91282:e=>{e.exports={en:["Price Label"],hu_HU:["Árcímke"]}},97512:e=>{e.exports={en:["Price Note"],hu_HU:["Price Note"]}},68941:e=>{e.exports={en:["Price Range"],hu_HU:["Ártartomány"]}},66123:e=>{e.exports={en:["Price format is invalid."],hu_HU:["Érvénytelen árformátum."]}},72926:e=>{e.exports={en:["Price line"],hu_HU:["Árvonal"]}},59189:e=>{e.exports={en:["Primary"],hu_HU:["Elsődleges"]}},75747:e=>{e.exports={en:["Projection"],hu_HU:["Vetület"]}},55801:e=>{e.exports={en:["Published on {customer}, {date}"],hu_HU:["Published on {customer}, {date}"]}},28756:e=>{e.exports={en:["Qatar"],hu_HU:["Qatar"]}},57959:e=>{e.exports={en:["Quick search. Press {shortcut}"],hu_HU:["Quick search. Press {shortcut}"]}},56820:e=>{e.exports={en:["Rotated Rectangle"],hu_HU:["Elforgatott Téglalap"]}},52961:e=>{e.exports={en:["Rome"],hu_HU:["Rome"]}},50318:e=>{e.exports={en:["Ray"],hu_HU:["Sugár"]}},55169:e=>{e.exports={en:["Range"],hu_HU:["Tartomány"]}},13386:e=>{e.exports={
en:["Reykjavik"],hu_HU:["Reykjavik"]}},26001:e=>{e.exports={en:["Rectangle"],hu_HU:["Téglalap"]}},48236:e=>{e.exports={en:["Redo"],hu_HU:["Újra"]}},2460:e=>{e.exports={en:["Regression Trend"],hu_HU:["Regresszió Trend"]}},67410:e=>{e.exports={en:["Remove"],hu_HU:["Eltávolítás"]}},96374:e=>{e.exports={en:["Remove drawings"],hu_HU:["Remove drawings"]}},99984:e=>{e.exports={en:["Remove indicators"],hu_HU:["Indikátorok Eltávolítása"]}},3061:e=>{e.exports={en:["Remove this financial metric from favorites"],hu_HU:["Remove this financial metric from favorites"]}},58764:e=>{e.exports={en:["Remove this indicator from favorites"],hu_HU:["Remove this indicator from favorites"]}},22584:e=>{e.exports={en:["Rename Chart Layout"],hu_HU:["Chart Elrendezés Átnevezése"]}},88130:e=>{e.exports={en:["Renko"],hu_HU:["Renko"]}},75246:e=>{e.exports={en:["Reset chart view"],hu_HU:["Reset chart view"]}},88853:e=>{e.exports={en:["Reset points"],hu_HU:["Reset points"]}},15332:e=>{e.exports={en:["Reset price scale"],hu_HU:["Reset Price Scale"]}},54170:e=>{e.exports={en:["Reset time scale"],hu_HU:["Reset Time Scale"]}},37974:e=>{e.exports={en:["Riyadh"],hu_HU:["Riyadh"]}},94022:e=>{e.exports={en:["Riga"],hu_HU:["Riga"]}},60630:e=>{e.exports={en:["Runtime error"],hu_HU:["Runtime error"]}},66719:e=>{e.exports={en:["Warning"],hu_HU:["Figyelmeztetés"]}},5959:e=>{e.exports={en:["Warsaw"],hu_HU:["Varsó"]}},94465:e=>{e.exports={en:["Toggle auto scale"],hu_HU:["Toggle auto scale"]}},46992:e=>{e.exports={en:["Toggle log scale"],hu_HU:["Toggle log scale"]}},98549:e=>{e.exports={en:["Tokelau"],hu_HU:["Tokelau"]}},69122:e=>{e.exports={en:["Tokyo"],hu_HU:["Tokió"]}},10095:e=>{e.exports={en:["Toronto"],hu_HU:["Toronto"]}},11034:e=>{e.exports={en:["Taipei"],hu_HU:["Tajpej"]}},79995:e=>{e.exports={en:["Tallinn"],hu_HU:["Tallinn"]}},6686:e=>{e.exports={en:["Tehran"],hu_HU:["Teherán"]}},93553:e=>{e.exports={en:["Template"],hu_HU:["Sablon"]}},81657:e=>{e.exports={en:["The data vendor doesn't provide volume data for this symbol."],hu_HU:["The data vendor doesn't provide volume data for this symbol."]}},49947:e=>{e.exports={en:["The publication preview could not be loaded. Please disable your browser extensions and try again."],hu_HU:["The publication preview could not be loaded. Please disable your browser extensions and try again."]}},93738:e=>{e.exports={en:["This file is too big. Max size is {value}."],hu_HU:["This file is too big. Max size is {value}."]}},59519:e=>{e.exports={en:["This indicator cannot be applied to another indicator."],hu_HU:["Ezt az indikátort nem lehet alkalmazni egy másik indikátorra"]}},18260:e=>{e.exports={en:["This script contains an error. Please contact its author."],hu_HU:["This script contains an error. Please contact its author."]}},76989:e=>{e.exports={en:["This script is invite-only. To request access, please contact its author."],hu_HU:["This script is invite-only. To request access, please contact its author."]}},47773:e=>{e.exports={en:["This symbol is only available on {linkStart}TradingView{linkEnd}."],
hu_HU:["The symbol available only on {linkStart}TradingView{linkEnd}."]}},46982:e=>{e.exports={en:["Three Drives Pattern"],hu_HU:["Három Hajtás Minta"]}},24821:e=>{e.exports={en:["Ticks"],hu_HU:["Ticks"]}},80254:e=>{e.exports={en:["Tick-based intervals are not available for {ticker}."],hu_HU:["Tick-based intervals are not available for {ticker}."]}},12806:e=>{e.exports={en:["Time"],hu_HU:["Idő"]}},20909:e=>{e.exports={en:["Time zone"],hu_HU:["Időzóna"]}},46852:e=>{e.exports={en:["Time Cycles"],hu_HU:["Ciklusidők"]}},17809:e=>{e.exports={en:["Time Price Opportunity"],hu_HU:["Time Price Opportunity"]}},66823:e=>{e.exports={en:["Trade"],hu_HU:["Kereskedés"]}},7697:e=>{e.exports={en:["TradingView is interactive and has commands to use with a screen reader. The following is a list of keyboard commands available to interact on the platform"],hu_HU:["TradingView is interactive and has commands to use with a screen reader. The following is a list of keyboard commands available to interact on the platform"]}},35757:e=>{e.exports={en:["Trend Angle"],hu_HU:["Trendszög"]}},97339:e=>{e.exports={en:["Trend Line"],hu_HU:["Trendvonal"]}},80583:e=>{e.exports={en:["Trend-Based Fib Extension"],hu_HU:["Trendalapú Fib Kiterjesztés"]}},72159:e=>{e.exports={en:["Trend-Based Fib Time"],hu_HU:["Trendalapú Fib Idő"]}},1671:e=>{e.exports={en:["Triangle"],hu_HU:["Háromszög"]}},76152:e=>{e.exports={en:["Triangle Down"],hu_HU:["Triangle Down"]}},90148:e=>{e.exports={en:["Triangle Pattern"],hu_HU:["Háromszög Minta"]}},21236:e=>{e.exports={en:["Triangle Up"],hu_HU:["Triangle Up"]}},21007:e=>{e.exports={en:["Tunis"],hu_HU:["Tunis"]}},1833:e=>{e.exports={en:["UTC"],hu_HU:["UTC"]}},14804:e=>{e.exports={en:["Undo"],hu_HU:["Visszavonás"]}},15432:e=>{e.exports={en:["Units"],hu_HU:["Units"]}},11768:e=>{e.exports={en:["Unknown error"],hu_HU:["Ismeretlen hiba"]}},99894:e=>{e.exports={en:["Unlock"],hu_HU:["Feloldás"]}},75546:e=>{e.exports={en:["Unsupported interval"],hu_HU:["Unsupported interval"]}},8580:e=>{e.exports={en:["User-defined error"],hu_HU:["User-defined error"]}},40693:e=>{e.exports={en:["Volume Profile Fixed Range"],hu_HU:["Volume Profile Fixed Range"]}},39903:e=>{e.exports={en:["Volume Profile indicator available only on our upgraded plans."],hu_HU:["Volume Profile indicator available only on our upgraded plans."]}},93722:e=>{e.exports={en:["Volume candles"],hu_HU:["Volume candles"]}},69156:e=>{e.exports={en:["Volume data is not provided in BIST MIXED data plan."],hu_HU:["Volume data is not provided in BIST MIXED data plan."]}},92763:e=>{e.exports={en:["Volume footprint"],hu_HU:["Volume footprint"]}},32838:e=>{e.exports={en:["Vancouver"],hu_HU:["Vancouver"]}},29535:e=>{e.exports={en:["Vertical Line"],hu_HU:["Függőleges Vonal"]}},23160:e=>{e.exports={en:["Vienna"],hu_HU:["Vienna"]}},60534:e=>{e.exports={en:["Vilnius"],hu_HU:["Vilnius"]}},40091:e=>{e.exports={en:["Visibility"],hu_HU:["Láthatóság"]}},54853:e=>{e.exports={en:["Visibility on intervals"],hu_HU:["Visibility on intervals"]}},10309:e=>{e.exports={en:["Visible on mouse over"],
hu_HU:["Az Egér Föléhúzásakor Látható"]}},4077:e=>{e.exports={en:["Visual order"],hu_HU:["Vizuális Elrendezés"]}},11316:e=>{e.exports={en:["X Cross"],hu_HU:["X Cross"]}},42231:e=>{e.exports={en:["XABCD Pattern"],hu_HU:["XABCD Minta"]}},25059:e=>{e.exports={en:["You cannot see this pivot timeframe on this resolution"],hu_HU:["You cannot see this pivot timeframe on this resolution"]}},53168:e=>{e.exports={en:["Yangon"],hu_HU:["Yangon"]}},62859:e=>{e.exports={en:["Zurich"],hu_HU:["Zürich"]}},47977:e=>{e.exports={en:["change Elliott degree"],hu_HU:["change Elliott degree"]}},61557:e=>{e.exports={en:["change no overlapping labels"],hu_HU:["change no overlapping labels"]}},76852:e=>{e.exports={en:["change average close price label visibility"],hu_HU:["change average close price label visibility"]}},1022:e=>{e.exports={en:["change average close price line visibility"],hu_HU:["change average close price line visibility"]}},69362:e=>{e.exports={en:["change bid and ask labels visibility"],hu_HU:["change bid and ask labels visibility"]}},52919:e=>{e.exports={en:["change bid and ask lines visibility"],hu_HU:["change bid and ask lines visibility"]}},32302:e=>{e.exports={en:["change currency"],hu_HU:["change currency"]}},68846:e=>{e.exports={en:["change chart layout to {title}"],hu_HU:["change chart layout to {title}"]}},18867:e=>{e.exports={en:["change continuous contract switch visibility"],hu_HU:["change continuous contract switch visibility"]}},39383:e=>{e.exports={en:["change countdown to bar close visibility"],hu_HU:["change countdown to bar close visibility"]}},16979:e=>{e.exports={en:["change date range"],hu_HU:["change date range"]}},53929:e=>{e.exports={en:["change dividends visibility"],hu_HU:["change dividends visibility"]}},6119:e=>{e.exports={en:["change events visibility on chart"],hu_HU:["change events visibility on chart"]}},6819:e=>{e.exports={en:["change earnings visibility"],hu_HU:["change earnings visibility"]}},85532:e=>{e.exports={en:["change futures contract expiration visibility"],hu_HU:["change futures contract expiration visibility"]}},24226:e=>{e.exports={en:["change high and low price labels visibility"],hu_HU:["change high and low price labels visibility"]}},80692:e=>{e.exports={en:["change high and low price lines visibility"],hu_HU:["change high and low price lines visibility"]}},24893:e=>{e.exports={en:["change indicators name labels visibility"],hu_HU:["change indicators name labels visibility"]}},64729:e=>{e.exports={en:["change indicators value labels visibility"],hu_HU:["change indicators value labels visibility"]}},50243:e=>{e.exports={en:["change latest news and Minds visibility"],hu_HU:["change latest news and Minds visibility"]}},88849:e=>{e.exports={en:["change linking group"],hu_HU:["change linking group"]}},14691:e=>{e.exports={en:["change pane height"],hu_HU:["change pane height"]}},96379:e=>{e.exports={en:["change plus button visibility"],hu_HU:["change plus button visibility"]}},30870:e=>{e.exports={en:["change pre/post market price label visibility"],
hu_HU:["change pre/post market price label visibility"]}},11718:e=>{e.exports={en:["change pre/post market price line visibility"],hu_HU:["change pre/post market price line visibility"]}},58419:e=>{e.exports={en:["change previous close price line visibility"],hu_HU:["change previous close price line visibility"]}},8662:e=>{e.exports={en:["change price line visibility"],hu_HU:["change price line visibility"]}},2509:e=>{e.exports={en:["change price to bar ratio"],hu_HU:["change price to bar ratio"]}},32829:e=>{e.exports={en:["change resolution"],hu_HU:["Felbontás Módosítása"]}},35400:e=>{e.exports={en:["change symbol"],hu_HU:["Szimbólum módosítása"]}},73357:e=>{e.exports={en:["change symbol labels visibility"],hu_HU:["change symbol labels visibility"]}},67453:e=>{e.exports={en:["change symbol last value visibility"],hu_HU:["change symbol last value visibility"]}},4729:e=>{e.exports={en:["change symbol previous close value visibility"],hu_HU:["change symbol previous close value visibility"]}},87041:e=>{e.exports={en:["change session"],hu_HU:["change session"]}},38413:e=>{e.exports={en:["change session breaks visibility"],hu_HU:["change session breaks visibility"]}},49965:e=>{e.exports={en:["change series style"],hu_HU:["change series style"]}},47474:e=>{e.exports={en:["change splits visibility"],hu_HU:["change splits visibility"]}},20137:e=>{e.exports={en:["change timezone"],hu_HU:["change timezone"]}},85975:e=>{e.exports={en:["change unit"],hu_HU:["change unit"]}},1924:e=>{e.exports={en:["change visibility"],hu_HU:["change visibility"]}},84331:e=>{e.exports={en:["change visibility at current interval"],hu_HU:["change visibility at current interval"]}},45800:e=>{e.exports={en:["change visibility at current interval and above"],hu_HU:["change visibility at current interval and above"]}},75645:e=>{e.exports={en:["change visibility at current interval and below"],hu_HU:["change visibility at current interval and below"]}},57916:e=>{e.exports={en:["change visibility at all intervals"],hu_HU:["change visibility at all intervals"]}},98463:e=>{e.exports={en:["change {title} style"],hu_HU:["change {title} style"]}},57122:e=>{e.exports={en:["change {title} text"],hu_HU:["change {title} text"]}},63058:e=>{e.exports={en:["change {pointIndex} point"],hu_HU:["change {pointIndex} point"]}},94566:e=>{e.exports={en:["charts by TradingView"],hu_HU:["TradingView chartok"]}},32943:e=>{e.exports={en:["clone line tools"],hu_HU:["clone line tools"]}},46219:e=>{e.exports={en:["create line tools group"],hu_HU:["create line tools group"]}},95394:e=>{e.exports={en:["create line tools group from selection"],hu_HU:["create line tools group from selection"]}},12898:e=>{e.exports={en:["create {tool}"],hu_HU:["create {tool}"]}},94227:e=>{e.exports={en:["cut sources"],hu_HU:["cut sources"]}},11500:e=>{e.exports={en:["cut {title}"],hu_HU:["cut {title}"]}},12570:e=>{e.exports={en:["add line tool {lineTool} to group {name}"],hu_HU:["add line tool {lineTool} to group {name}"]}},21162:e=>{e.exports={en:["add line tool(s) to group {group}"],
hu_HU:["add line tool(s) to group {group}"]}},67608:e=>{e.exports={en:["add this financial metric to entire layout"],hu_HU:["add this financial metric to entire layout"]}},96677:e=>{e.exports={en:["add this indicator to entire layout"],hu_HU:["add this indicator to entire layout"]}},58156:e=>{e.exports={en:["add this strategy to entire layout"],hu_HU:["add this strategy to entire layout"]}},79290:e=>{e.exports={en:["add this symbol to entire layout"],hu_HU:["add this symbol to entire layout"]}},68231:e=>{e.exports={en:["apply chart theme"],hu_HU:["apply chart theme"]}},99551:e=>{e.exports={en:["apply all chart properties"],hu_HU:["apply all chart properties"]}},89720:e=>{e.exports={en:["apply drawing template"],hu_HU:["apply drawing template"]}},27851:e=>{e.exports={en:["apply factory defaults to selected sources"],hu_HU:["apply factory defaults to selected sources"]}},70507:e=>{e.exports={en:["apply indicators to entire layout"],hu_HU:["apply indicators to entire layout"]}},69604:e=>{e.exports={en:["apply study template {template}"],hu_HU:["apply study template {template}"]}},86708:e=>{e.exports={en:["apply toolbars theme"],hu_HU:["apply toolbars theme"]}},1979:e=>{e.exports={en:["bring group {title} forward"],hu_HU:["bring group {title} forward"]}},53159:e=>{e.exports={en:["bring {title} to front"],hu_HU:["bring {title} to front"]}},41966:e=>{e.exports={en:["bring {title} forward"],hu_HU:["bring {title} forward"]}},44676:e=>{e.exports={en:["by TradingView"],hu_HU:["by TradingView"]}},58850:e=>{e.exports={en:["date range lock"],hu_HU:["date range lock"]}},35111:e=>{e.exports={en:["erase level line"],hu_HU:["erase level line"]}},99395:e=>{e.exports={en:["exclude line tools from group {group}"],hu_HU:["exclude line tools from group {group}"]}},82349:e=>{e.exports={en:["flip bars pattern"],hu_HU:["flip bars pattern"]}},13017:e=>{e.exports={en:["hide {title}"],hu_HU:["hide {title}"]}},62249:e=>{e.exports={en:["hide marks on bars"],hu_HU:["Jelölések Elrejtése a Bárokon"]}},56558:e=>{e.exports={en:["interval lock"],hu_HU:["interval lock"]}},6830:e=>{e.exports={en:["invert scale"],hu_HU:["Invert Scale"]}},48818:e=>{e.exports={en:["insert {title}"],hu_HU:["insert {title}"]}},56307:e=>{e.exports={en:["insert {title} after {targetTitle}"],hu_HU:["insert {title} after {targetTitle}"]}},32960:e=>{e.exports={en:["insert {title} after {target}"],hu_HU:["insert {title} after {target}"]}},57106:e=>{e.exports={en:["insert {title} before {target}"],hu_HU:["insert {title} before {target}"]}},46229:e=>{e.exports={en:["insert {title} before {targetTitle}"],hu_HU:["insert {title} before {targetTitle}"]}},43364:e=>{e.exports={en:["load default drawing template"],hu_HU:["load default drawing template"]}},62011:e=>{e.exports={en:["loading..."],hu_HU:["töltés..."]}},76104:e=>{e.exports={en:["lock {title}"],hu_HU:["lock {title}"]}},20453:e=>{e.exports={en:["lock group {group}"],hu_HU:["lock group {group}"]}},18942:e=>{e.exports={en:["lock objects"],hu_HU:["lock objects"]}},98277:e=>{e.exports={en:["move"],hu_HU:["move"]}},58228:e=>{
e.exports={en:["move {title} to new left scale"],hu_HU:["move {title} to new left scale"]}},77482:e=>{e.exports={en:["move {title} to new right scale"],hu_HU:["move {title} to new right scale"]}},64077:e=>{e.exports={en:["move all scales to left"],hu_HU:["move all scales to left"]}},19013:e=>{e.exports={en:["move all scales to right"],hu_HU:["move all scales to right"]}},52510:e=>{e.exports={en:["move drawing(s)"],hu_HU:["move drawing(s)"]}},79209:e=>{e.exports={en:["move left"],hu_HU:["move left"]}},60114:e=>{e.exports={en:["move right"],hu_HU:["move right"]}},44854:e=>{e.exports={en:["move scale"],hu_HU:["move scale"]}},10625:e=>{e.exports={en:["make {title} no scale (Full screen)"],hu_HU:["make {title} no scale (Full screen)"]}},76709:e=>{e.exports={en:["make group {group} invisible"],hu_HU:["make group {group} invisible"]}},45987:e=>{e.exports={en:["make group {group} visible"],hu_HU:["make group {group} visible"]}},78055:e=>{e.exports={en:["merge down"],hu_HU:["merge down"]}},41866:e=>{e.exports={en:["merge to pane"],hu_HU:["merge to pane"]}},52458:e=>{e.exports={en:["merge up"],hu_HU:["merge up"]}},20965:e=>{e.exports={en:["mirror bars pattern"],hu_HU:["mirror bars pattern"]}},90091:e=>{e.exports={en:["n/a"],hu_HU:["n/a"]}},94981:e=>{e.exports={en:["scale price"],hu_HU:["scale price"]}},63796:e=>{e.exports={en:["scale price chart only"],hu_HU:["Csak az Árskála Chart"]}},70771:e=>{e.exports={en:["scale time"],hu_HU:["scale time"]}},42070:e=>{e.exports={en:["scroll"],hu_HU:["scroll"]}},87840:e=>{e.exports={en:["scroll time"],hu_HU:["scroll time"]}},82241:e=>{e.exports={en:["set price scale selection strategy to {title}"],hu_HU:["set price scale selection strategy to {title}"]}},40962:e=>{e.exports={en:["send {title} backward"],hu_HU:["send {title} backward"]}},5005:e=>{e.exports={en:["send {title} to back"],hu_HU:["send {title} to back"]}},69546:e=>{e.exports={en:["send group {title} backward"],hu_HU:["send group {title} backward"]}},63934:e=>{e.exports={en:["share line tools globally"],hu_HU:["share line tools globally"]}},90221:e=>{e.exports={en:["share line tools in layout"],hu_HU:["share line tools in layout"]}},13336:e=>{e.exports={en:["show all ideas"],hu_HU:["show all ideas"]}},91395:e=>{e.exports={en:["show ideas of followed users"],hu_HU:["show ideas of followed users"]}},57460:e=>{e.exports={en:["show my ideas only"],hu_HU:["show my ideas only"]}},4114:e=>{e.exports={en:["stay in drawing mode"],hu_HU:["stay in drawing mode"]}},3350:e=>{e.exports={en:["stop syncing drawing"],hu_HU:["stop syncing drawing"]}},49183:e=>{e.exports={en:["stop syncing line tool(s)"],hu_HU:["stop syncing line tool(s)"]}},53278:e=>{e.exports={en:["symbol lock"],hu_HU:["symbol lock"]}},91677:e=>{e.exports={en:["sync time"],hu_HU:["sync time"]}},3140:e=>{e.exports={en:["powered by"],hu_HU:["powered by"]}},92800:e=>{e.exports={en:["powered by TradingView"],hu_HU:["támogatta a TradingView"]}},62192:e=>{e.exports={en:["paste drawing"],hu_HU:["paste drawing"]}},1064:e=>{e.exports={en:["paste indicator"],hu_HU:["paste indicator"]}},
57010:e=>{e.exports={en:["paste {title}"],hu_HU:["paste {title}"]}},78690:e=>{e.exports={en:["pin to left scale"],hu_HU:["pin to left scale"]}},7495:e=>{e.exports={en:["pin to right scale"],hu_HU:["pin to right scale"]}},81566:e=>{e.exports={en:["pin to scale {label}"],hu_HU:["pin to scale {label}"]}},2618:e=>{e.exports={en:["rearrange panes"],hu_HU:["rearrange panes"]}},43172:e=>{e.exports={en:["remove all studies"],hu_HU:["remove all studies"]}},56253:e=>{e.exports={en:["remove all studies and drawing tools"],hu_HU:["remove all studies and drawing tools"]}},27171:e=>{e.exports={en:["remove deselected empty line tools"],hu_HU:["remove deselected empty line tools"]}},30538:e=>{e.exports={en:["remove drawings"],hu_HU:["remove drawings"]}},1193:e=>{e.exports={en:["remove drawings group"],hu_HU:["remove drawings group"]}},38199:e=>{e.exports={en:["remove line data sources"],hu_HU:["remove line data sources"]}},93333:e=>{e.exports={en:["remove pane"],hu_HU:["remove pane"]}},94543:e=>{e.exports={en:["remove {title}"],hu_HU:["remove {title}"]}},41430:e=>{e.exports={en:["removing line tools group {name}"],hu_HU:["removing line tools group {name}"]}},80491:e=>{e.exports={en:["rename group {group} to {newName}"],hu_HU:["rename group {group} to {newName}"]}},85366:e=>{e.exports={en:["reset layout sizes"],hu_HU:["reset layout sizes"]}},3323:e=>{e.exports={en:["reset scales"],hu_HU:["reset scales"]}},17336:e=>{e.exports={en:["reset time scale"],hu_HU:["Reset Time Scale"]}},47418:e=>{e.exports={en:["resize layout"],hu_HU:["resize layout"]}},85815:e=>{e.exports={en:["restore defaults"],hu_HU:["restore defaults"]}},96881:e=>{e.exports={en:["restore study defaults"],hu_HU:["restore study defaults"]}},42240:e=>{e.exports={en:["toggle auto scale"],hu_HU:["toggle auto scale"]}},46054:e=>{e.exports={en:["toggle collapsed pane state"],hu_HU:["toggle collapsed pane state"]}},24736:e=>{e.exports={en:["toggle indexed to 100 scale"],hu_HU:["toggle indexed to 100 scale"]}},49695:e=>{e.exports={en:["toggle lock scale"],hu_HU:["toggle lock scale"]}},49403:e=>{e.exports={en:["toggle log scale"],hu_HU:["toggle log scale"]}},98994:e=>{e.exports={en:["toggle percentage scale"],hu_HU:["Toggle Percentage Scale"]}},80688:e=>{e.exports={en:["toggle regular scale"],hu_HU:["toggle regular scale"]}},46807:e=>{e.exports={en:["track time"],hu_HU:["track time"]}},8040:e=>{e.exports={en:["turn line tools sharing off"],hu_HU:["turn line tools sharing off"]}},23230:e=>{e.exports={en:["unlock objects"],hu_HU:["unlock objects"]}},74590:e=>{e.exports={en:["unlock group {group}"],hu_HU:["unlock group {group}"]}},12525:e=>{e.exports={en:["unlock {title}"],hu_HU:["unlock {title}"]}},81576:e=>{e.exports={en:["unmerge to new bottom pane"],hu_HU:["unmerge to new bottom pane"]}},79443:e=>{e.exports={en:["unmerge up"],hu_HU:["unmerge up"]}},46453:e=>{e.exports={en:["unmerge down"],hu_HU:["unmerge down"]}},94656:e=>{e.exports={en:["{chartStyle} chart type isn't currently available for tick-based intervals."],
hu_HU:["{chartStyle} chart type isn't currently available for tick-based intervals."]}},41643:e=>{e.exports={en:["{count} bars"],hu_HU:["{count} oszlop"]}},83470:e=>{e.exports={en:["{symbol} financials by TradingView"],hu_HU:["{symbol} TradingView pénzügyek"]}},40947:e=>{e.exports={en:["{userName} published on {customer}, {date}"],hu_HU:["{userName} published on {customer}, {date}"]}},91084:e=>{e.exports={en:["zoom"],hu_HU:["zoom"]}},49856:e=>{e.exports={en:["zoom in"],hu_HU:["zoom in"]}},73638:e=>{e.exports={en:["zoom out"],hu_HU:["zoom out"]}},41807:e=>{e.exports={en:["day","days"],hu_HU:["nap"]}},42328:e=>{e.exports={en:["hour","hours"],hu_HU:["óra"]}},98393:e=>{e.exports={en:["month","months"],hu_HU:["months"]}},78318:e=>{e.exports={en:["minute","minutes"],hu_HU:["perc"]}},33232:e=>{e.exports={en:["second","seconds"],hu_HU:["seconds"]}},89937:e=>{e.exports={en:["range","ranges"],hu_HU:["ranges"]}},48898:e=>{e.exports={en:["week","weeks"],hu_HU:["weeks"]}},11913:e=>{e.exports={en:["tick","ticks"],hu_HU:["ticks"]}},58590:e=>{e.exports={en:["{count}m","{count}m"],hu_HU:["{count}m"]}},47801:e=>{e.exports={en:["{count}d","{count}d"],hu_HU:["{count}d"]}},46766:e=>{e.exports={en:["{count}y","{count}y"],hu_HU:["{count}y"]}},56316:e=>{e.exports=Object.create(null),e.exports["#AAPL-symbol-description"]={en:["Apple Inc"],hu_HU:["APPLE INC"]},e.exports["#AUDCAD-symbol-description"]={en:["Australian Dollar/Canadian Dollar"],hu_HU:["ausztrál dollár/kanadai dollár"]},e.exports["#AUDCHF-symbol-description"]={en:["Australian Dollar / Swiss Franc"],hu_HU:["ausztrál dollár / svájci frank"]},e.exports["#AUDJPY-symbol-description"]={en:["Australian Dollar / Japanese Yen"],hu_HU:["ausztrál dollár / japán jen"]},e.exports["#AUDNZD-symbol-description"]={en:["Australian Dollar / New Zealand Dollar"],hu_HU:["ausztrál dollár / új-zélandi dollár"]},e.exports["#AUDRUB-symbol-description"]={en:["Australian Dollar / Russian Ruble"],hu_HU:["ausztrál dollár / orosz rúbel"]},e.exports["#AUDUSD-symbol-description"]={en:["Australian Dollar / U.S. Dollar"],hu_HU:["ausztrál dollár / amerikai dollár"]},e.exports["#BRLJPY-symbol-description"]={en:["Brazilian Real / Japanese Yen"],hu_HU:["brazil dollár / japán jen"]},e.exports["#BTCCAD-symbol-description"]={en:["Bitcoin / Canadian Dollar"],hu_HU:["bitcoin / kanadai dollár"]},e.exports["#BTCCNY-symbol-description"]={en:["Bitcoin / Chinese Yuan"],hu_HU:["bitcoin / kínai jüan"]},e.exports["#BTCEUR-symbol-description"]={en:["Bitcoin / Euro"],hu_HU:["bitcoin / euró"]},e.exports["#BTCKRW-symbol-description"]={en:["Bitcoin / South Korean Won"],hu_HU:["bitcoin / dél-koreai won"]},e.exports["#BTCRUR-symbol-description"]={en:["Bitcoin / Ruble"],hu_HU:["bitcoin / rubel"]},e.exports["#BTCUSD-symbol-description"]={en:["Bitcoin / U.S. Dollar"],hu_HU:["bitcoin / amerikai dollár"]},e.exports["#BVSP-symbol-description"]={en:["Brazil Bovespa Index"],hu_HU:["Brazil Bovespa Index"]},e.exports["#CADJPY-symbol-description"]={en:["Canadian Dollar / Japanese Yen"],hu_HU:["kanadai dollár / japán jen"]},
e.exports["#CHFJPY-symbol-description"]={en:["Swiss Franc / Japanese Yen"],hu_HU:["svájci frank/japán jen"]},e.exports["#COPPER-symbol-description"]={en:["CFDs on Copper"],hu_HU:["Réz"]},e.exports["#ES1-symbol-description"]={en:["S&P 500 E-Mini Futures"],hu_HU:["#ES1-symbol-description"]},e.exports["#ESP35-symbol-description"]={en:["IBEX 35 Index"],hu_HU:["#ESP35-symbol-description"]},e.exports["#EUBUND-symbol-description"]={en:["Euro Bund"],hu_HU:["eurókötvények"]},e.exports["#EURAUD-symbol-description"]={en:["Euro / Australian Dollar"],hu_HU:["euró / ausztrál dollár"]},e.exports["#EURBRL-symbol-description"]={en:["Euro / Brazilian Real"],hu_HU:["euró / brazil reál"]},e.exports["#EURCAD-symbol-description"]={en:["Euro / Canadian Dollar"],hu_HU:["euró / kanadai dollár"]},e.exports["#EURCHF-symbol-description"]={en:["Euro / Swiss Franc"],hu_HU:["Euró Fx/svájci frank"]},e.exports["#EURGBP-symbol-description"]={en:["Euro / British Pound"],hu_HU:["Euró Fx/brit font"]},e.exports["#EURJPY-symbol-description"]={en:["Euro / Japanese Yen"],hu_HU:["Euró Fx/japán jen"]},e.exports["#EURNZD-symbol-description"]={en:["Euro / New Zealand Dollar"],hu_HU:["euró / új-zélandi dollár"]},e.exports["#EURRUB-symbol-description"]={en:["Euro / Russian Ruble"],hu_HU:["EURÓ / OROSZ RUBEL"]},e.exports["#EURRUB_TOM-symbol-description"]={en:["Euro / Russian Ruble TOM"],hu_HU:["EUR/RUB TOM"]},e.exports["#EURSEK-symbol-description"]={en:["Euro / Swedish Krona"],hu_HU:["#EURSEK-symbol-description"]},e.exports["#EURTRY-symbol-description"]={en:["Euro / Turkish Lira"],hu_HU:["Euró Fx/új török líra"]},e.exports["#EURUSD-symbol-description"]={en:["Euro / U.S. Dollar"],hu_HU:["euró / amerikai dollár"]},e.exports["#EUSTX50-symbol-description"]={en:["Euro Stoxx 50 Index"],hu_HU:["Euro Stoxx 50 index európai jegyzett részvények"]},e.exports["#FRA40-symbol-description"]={en:["CAC 40 Index"],hu_HU:["#FRA40-symbol-description"]},e.exports["#GB10-symbol-description"]={en:["UK Government Bonds 10 yr"],hu_HU:["brit államkötvények 10 éves"]},e.exports["#GBPAUD-symbol-description"]={en:["British Pound / Australian Dollar"],hu_HU:["brit font / ausztrál dollár"]},e.exports["#GBPCAD-symbol-description"]={en:["British Pound / Canadian Dollar"],hu_HU:["brit font / kanadai dollár"]},e.exports["#GBPCHF-symbol-description"]={en:["British Pound / Swiss Franc"],hu_HU:["brit font/svájci frank"]},e.exports["#GBPEUR-symbol-description"]={en:["British Pound / Euro"],hu_HU:["FONT STERLING / EURÓ"]},e.exports["#GBPJPY-symbol-description"]={en:["British Pound / Japanese Yen"],hu_HU:["brit font//japán jen"]},e.exports["#GBPNZD-symbol-description"]={en:["British Pound / New Zealand Dollar"],hu_HU:["brit font / új-zélandi dollár"]},e.exports["#GBPRUB-symbol-description"]={en:["British Pound / Russian Ruble"],hu_HU:["font sterling / orosz rubel"]},e.exports["#GBPUSD-symbol-description"]={en:["British Pound / U.S. Dollar"],hu_HU:["brit font / amerikai dollár"]},e.exports["#GER30-symbol-description"]={en:["DAX Index"],hu_HU:["DAX index német jegyzett részvények"]},
e.exports["#GOOGL-symbol-description"]={en:["Alphabet Inc (Google) Class A"],hu_HU:["GOOGLE INC"]},e.exports["#ITA40-symbol-description"]={en:["FTSE MIB Index"],hu_HU:["FTSE MIB index olasz jegyzett részvények"]},e.exports["#JPN225-symbol-description"]={en:["Nikkei 225 Index"],hu_HU:["#JPN225-symbol-description"]},e.exports["#JPYKRW-symbol-description"]={en:["Japanese Yen / South Korean Won"],hu_HU:["JEN / WON"]},e.exports["#JPYRUB-symbol-description"]={en:["Japanese Yen / Russian Ruble"],hu_HU:["JEN / OROSZ RUBEL"]},e.exports["#KA1-symbol-description"]={en:["Sugar #11 Futures"],hu_HU:["#KA1-symbol-description"]},e.exports["#KG1-symbol-description"]={en:["Cotton Futures"],hu_HU:["#KG1-symbol-description"]},e.exports["#KT1-symbol-description"]={en:["Key Tronic Corр."],hu_HU:["#KT1-symbol-description"]},e.exports["#LKOH-symbol-description"]={en:["LUKOIL"],hu_HU:["LUKOIL"]},e.exports["#LTCBTC-symbol-description"]={en:["Litecoin / Bitcoin"],hu_HU:["litecoin / bitcoin"]},e.exports["#MGNT-symbol-description"]={en:["Magnit"],hu_HU:["MAGNIT"]},e.exports["#MICEX-symbol-description"]={en:["MICEX Index"],hu_HU:["MICEX INDEX"]},e.exports["#MNOD_ME.EQRP-symbol-description"]={en:["ADR GMK NORILSKIYNIKEL ORD SHS [REPO]"],hu_HU:["ADR GMK NORILSKIYNIKEL ORD SHS [REPO]"]},e.exports["#MSFT-symbol-description"]={en:["Microsoft Corp."],hu_HU:["MICROSOFT CORP"]},e.exports["#NAS100-symbol-description"]={en:["US 100 Cash CFD"],hu_HU:["NASDAQ 100 amerikai jegyzett részvények"]},e.exports["#NGAS-symbol-description"]={en:["Natural Gas (Henry Hub)"],hu_HU:["Földgáz (Henry Hub)"]},e.exports["#NKY-symbol-description"]={en:["Nikkei 225 Index"],hu_HU:["Nikkei 225 Index"]},e.exports["#NZDJPY-symbol-description"]={en:["New Zealand Dollar / Japanese Yen"],hu_HU:["új-zélandi dollár / japán jen"]},e.exports["#NZDUSD-symbol-description"]={en:["New Zealand Dollar / U.S. Dollar"],hu_HU:["új-zélandi dollár / amerikai dollár"]},e.exports["#RB1-symbol-description"]={en:["RBOB Gasoline Futures"],hu_HU:["#KT1-symbol-description"]},e.exports["#RTS-symbol-description"]={en:["Russian RTS Index"],hu_HU:["Orosz RTS Index"]},e.exports["#SBER-symbol-description"]={en:["SBERBANK"],hu_HU:["SBERBANK"]},e.exports["#SPX500-symbol-description"]={en:["S&P 500 Index"],hu_HU:["S&P 500 index amerikai jegyzett részvények"]},e.exports["#TWTR-symbol-description"]={en:["Twitter Inc"],hu_HU:["TWITTER INC"]},e.exports["#UK100-symbol-description"]={en:["FTSE 100 Index"],hu_HU:["#UK100-symbol-description"]},e.exports["#USDBRL-symbol-description"]={en:["U.S. Dollar / Brazilian Real"],hu_HU:["amerikai dollár / brazil reál"]},e.exports["#USDCAD-symbol-description"]={en:["U.S. Dollar / Canadian Dollar"],hu_HU:["amerikai dollár / kanadai dollár"]},e.exports["#USDCHF-symbol-description"]={en:["U.S. Dollar / Swiss Franc"],hu_HU:["amerikai dollár / svájci frank"]},e.exports["#USDCNY-symbol-description"]={en:["U.S. Dollar / Chinese Yuan"],hu_HU:["amerikai dollár / jüan renminbi"]},e.exports["#USDDKK-symbol-description"]={en:["U.S. Dollar / Danish Krone"],hu_HU:["amerikai follár / dán korona"]},
e.exports["#USDHKD-symbol-description"]={en:["U.S. Dollar / Hong Kong Dollar"],hu_HU:["amerikai dollár / hong kongi dollár"]},e.exports["#USDIDR-symbol-description"]={en:["U.S. Dollar / Rupiah"],hu_HU:["amerikai dollár / rúpia"]},e.exports["#USDINR-symbol-description"]={en:["U.S. Dollar / Indian Rupee"],hu_HU:["amerikai dollár / indiai rúpia"]},e.exports["#USDJPY-symbol-description"]={en:["U.S. Dollar / Japanese Yen"],hu_HU:["amerikai dollár / japán jen"]},e.exports["#USDKRW-symbol-description"]={en:["U.S. Dollar / South Korean"],hu_HU:["amerikai dollár / dél-koreai won"]},e.exports["#USDMXN-symbol-description"]={en:["U.S. Dollar / Mexican Peso"],hu_HU:["amerikai dollár / mexikói peso"]},e.exports["#USDPHP-symbol-description"]={en:["U.S. Dollar / Philippine peso"],hu_HU:["amerikai dollár /fülöp-szigeteki peso"]},e.exports["#USDRUB-symbol-description"]={en:["U.S. Dollar / Russian Ruble"],hu_HU:["amerikai dollár / orosz rúbel"]},e.exports["#USDRUB_TOM-symbol-description"]={en:["U.S. Dollar / Russian Ruble TOM"],hu_HU:["amerikai dollár / orosz rúbel TOM"]},e.exports["#USDSEK-symbol-description"]={en:["U.S. Dollar / Swedish Krona"],hu_HU:["amerikai dollár / svéd korona"]},e.exports["#USDSGD-symbol-description"]={en:["U.S. Dollar / Singapore Dollar"],hu_HU:["amerikai dollár / szingapúri dollár"]},e.exports["#USDTRY-symbol-description"]={en:["U.S. Dollar / Turkish Lira"],hu_HU:["amerikai dollár / török líra"]},e.exports["#VTBR-symbol-description"]={en:["VTB"],hu_HU:["VTB"]},e.exports["#XAGUSD-symbol-description"]={en:["Silver / U.S. Dollar"],hu_HU:["ezüst / amerikai dollár"]},e.exports["#XAUUSD-symbol-description"]={en:["Gold Spot / U.S. Dollar"],hu_HU:["arany / amerikai dollár"]},e.exports["#XPDUSD-symbol-description"]={en:["CFDs on Palladium"],hu_HU:["#XPDUSD-symbol-description"]},e.exports["#XPTUSD-symbol-description"]={en:["Platinum / U.S. Dollar"],hu_HU:["platina / amerikai dollár"]},e.exports["#ZS1-symbol-description"]={en:["Soybean Futures - ECBT"],hu_HU:["#ZS1-symbol-description"]},e.exports["#ZW1-symbol-description"]={en:["Wheat Futures - ECBT"],hu_HU:["#ZW1-symbol-description"]},e.exports["#BTCGBP-symbol-description"]={en:["Bitcoin / British Pound"],hu_HU:["bitcoin / brit font"]},e.exports["#MICEXINDEXCF-symbol-description"]={en:["MOEX Russia Index"],hu_HU:["MICEX Index"]},e.exports["#BTCAUD-symbol-description"]={en:["Bitcoin / Australian Dollar"],hu_HU:["bitcoin / ausztrál dollár"]},e.exports["#BTCJPY-symbol-description"]={en:["Bitcoin / Japanese Yen"],hu_HU:["bitcoin / japán jen"]},e.exports["#BTCBRL-symbol-description"]={en:["Bitcoin / Brazilian Real"],hu_HU:["bitcoin / brazil reál"]},e.exports["#PT10-symbol-description"]={en:["Portugal Government Bonds 10 yr"],hu_HU:["Portugál Államkötvények 10 éves"]},e.exports["#TXSX-symbol-description"]={en:["TSX 60 Index"],hu_HU:["TSX 60 Index"]},e.exports["#VIXC-symbol-description"]={en:["TSX 60 VIX Index"],hu_HU:["TSX 60 VIX"]},e.exports["#USDPLN-symbol-description"]={en:["U.S. Dollar / Polish Zloty"],hu_HU:["amerikai dollár / lengyel zloty"]},
e.exports["#EURPLN-symbol-description"]={en:["Euro / Polish Zloty"],hu_HU:["EUR/PLN"]},e.exports["#BTCPLN-symbol-description"]={en:["Bitcoin / Polish Zloty"],hu_HU:["bitcoin / lengyel zloty"]},e.exports["#CAC40-symbol-description"]={en:["CAC 40 Index"],hu_HU:["CAC 40"]},e.exports["#XBTCAD-symbol-description"]={en:["Bitcoin / Canadian Dollar"],hu_HU:["bitcoin / kanadai dollár"]},e.exports["#ITI2!-symbol-description"]={en:["Iron Ore Futures"],hu_HU:["Iron Ore Futures"]},e.exports["#ITIF2018-symbol-description"]={en:["Iron Ore Futures"],hu_HU:["Vasérc Határidősők"]},e.exports["#ITIF2019-symbol-description"]={en:["Iron Ore Futures"],hu_HU:["Vasérc Határidősők"]},e.exports["#ITIF2020-symbol-description"]={en:["Iron Ore Futures"],hu_HU:["Vasérc Határidősők"]},e.exports["#ITIG2018-symbol-description"]={en:["Iron Ore Futures"],hu_HU:["Vasérc Határidősők"]},e.exports["#ITIG2019-symbol-description"]={en:["Iron Ore Futures"],hu_HU:["Vasérc Határidősők"]},e.exports["#ITIG2020-symbol-description"]={en:["Iron Ore Futures"],hu_HU:["Vasérc Határidősők"]},e.exports["#ITIH2018-symbol-description"]={en:["Iron Ore Futures"],hu_HU:["Vasérc Határidősők"]},e.exports["#ITIH2019-symbol-description"]={en:["Iron Ore Futures"],hu_HU:["Vasérc Határidősők"]},e.exports["#ITIH2020-symbol-description"]={en:["Iron Ore Futures"],hu_HU:["Vasérc Határidősők"]},e.exports["#ITIJ2018-symbol-description"]={en:["Iron Ore Futures"],hu_HU:["Vasérc Határidősők"]},e.exports["#ITIJ2019-symbol-description"]={en:["Iron Ore Futures"],hu_HU:["Vasérc Határidősők"]},e.exports["#ITIJ2020-symbol-description"]={en:["Iron Ore Futures"],hu_HU:["Vasérc Határidősők"]},e.exports["#ITIK2018-symbol-description"]={en:["Iron Ore Futures"],hu_HU:["Vasérc Határidősők"]},e.exports["#ITIK2019-symbol-description"]={en:["Iron Ore Futures"],hu_HU:["Vasérc Határidősők"]},e.exports["#ITIK2020-symbol-description"]={en:["Iron Ore Futures"],hu_HU:["Vasérc Határidősők"]},e.exports["#ITIM2017-symbol-description"]={en:["Iron Ore Futures"],hu_HU:["Vasérc Határidősők"]},e.exports["#ITIM2018-symbol-description"]={en:["Iron Ore Futures"],hu_HU:["Vasérc Határidősők"]},e.exports["#ITIM2019-symbol-description"]={en:["Iron Ore Futures"],hu_HU:["Vasérc Határidősők"]},e.exports["#ITIM2020-symbol-description"]={en:["Iron Ore Futures"],hu_HU:["Vasérc Határidősők"]},e.exports["#ITIN2017-symbol-description"]={en:["Iron Ore Futures"],hu_HU:["Vasérc Határidősők"]},e.exports["#ITIN2018-symbol-description"]={en:["Iron Ore Futures"],hu_HU:["Vasérc Határidősők"]},e.exports["#ITIN2019-symbol-description"]={en:["Iron Ore Futures"],hu_HU:["Vasérc Határidősők"]},e.exports["#ITIN2020-symbol-description"]={en:["Iron Ore Futures"],hu_HU:["Vasérc Határidősők"]},e.exports["#ITIQ2017-symbol-description"]={en:["Iron Ore Futures"],hu_HU:["Vasérc Határidősők"]},e.exports["#ITIQ2018-symbol-description"]={en:["Iron Ore Futures"],hu_HU:["Vasérc Határidősők"]},e.exports["#ITIQ2019-symbol-description"]={en:["Iron Ore Futures"],hu_HU:["Vasérc Határidősők"]},e.exports["#ITIQ2020-symbol-description"]={en:["Iron Ore Futures"],
hu_HU:["Vasérc Határidősők"]},e.exports["#ITIU2017-symbol-description"]={en:["Iron Ore Futures"],hu_HU:["Vasérc Határidősők"]},e.exports["#ITIU2018-symbol-description"]={en:["Iron Ore Futures"],hu_HU:["Vasérc Határidősők"]},e.exports["#ITIU2019-symbol-description"]={en:["Iron Ore Futures"],hu_HU:["Vasérc Határidősők"]},e.exports["#ITIU2020-symbol-description"]={en:["Iron Ore Futures"],hu_HU:["Vasérc Határidősők"]},e.exports["#ITIV2017-symbol-description"]={en:["Iron Ore Futures"],hu_HU:["Vasérc Határidősők"]},e.exports["#ITIV2018-symbol-description"]={en:["Iron Ore Futures"],hu_HU:["Vasérc Határidősők"]},e.exports["#ITIV2019-symbol-description"]={en:["Iron Ore Futures"],hu_HU:["Vasérc Határidősők"]},e.exports["#ITIV2020-symbol-description"]={en:["Iron Ore Futures"],hu_HU:["Vasérc Határidősők"]},e.exports["#ITIX2017-symbol-description"]={en:["Iron Ore Futures"],hu_HU:["Vasérc Határidősők"]},e.exports["#ITIX2018-symbol-description"]={en:["Iron Ore Futures"],hu_HU:["Vasérc Határidősők"]},e.exports["#ITIX2019-symbol-description"]={en:["Iron Ore Futures"],hu_HU:["Vasérc Határidősők"]},e.exports["#ITIX2020-symbol-description"]={en:["Iron Ore Futures"],hu_HU:["Vasérc Határidősők"]},e.exports["#ITIZ2017-symbol-description"]={en:["Iron Ore Futures"],hu_HU:["Vasérc Határidősők"]},e.exports["#ITIZ2018-symbol-description"]={en:["Iron Ore Futures"],hu_HU:["Vasérc Határidősők"]},e.exports["#ITIZ2019-symbol-description"]={en:["Iron Ore Futures"],hu_HU:["Vasérc Határidősők"]},e.exports["#ITIZ2020-symbol-description"]={en:["Iron Ore Futures"],hu_HU:["Vasérc Határidősők"]},e.exports["#AMEX:GXF-symbol-description"]={en:["Global x FTSE Nordic Region ETF"],hu_HU:["Globális x FTSE Északi Régió ETF"]},e.exports["#ASX:XAF-symbol-description"]={en:["S&P/ASX All Australian 50 Index"],hu_HU:["S&P/ASX Összes Ausztrál 50"]},e.exports["#ASX:XAT-symbol-description"]={en:["S&P/ASX All Australian 200 Index"],hu_HU:["S&P/ASX Összes Ausztrál 200"]},e.exports["#BIST:XU100-symbol-description"]={en:["BIST 100 Index"],hu_HU:["BIST 100"]},e.exports["#GPW:WIG20-symbol-description"]={en:["WIG20 Index"],hu_HU:["WIG20"]},e.exports["#INDEX:JKSE-symbol-description"]={en:["Jakarta Composite Index"],hu_HU:["Dzsakarta Kompozit Index"]},e.exports["#INDEX:KLSE-symbol-description"]={en:["Bursa Malaysia KLCI Index"],hu_HU:["Bursa Malajzia KLCI Index"]},e.exports["#INDEX:NZD-symbol-description"]={en:["NZX 50 Index"],hu_HU:["NZX 50 Index"]},e.exports["#INDEX:STI-symbol-description"]={en:["STI Index"],hu_HU:["STI Index"]},e.exports["#INDEX:XLY0-symbol-description"]={en:["Shanghai Composite Index"],hu_HU:["Sanghaj Kompozit Index"]},e.exports["#MOEX:MICEXINDEXCF-symbol-description"]={en:["MOEX Russia Index"],hu_HU:["MOEX Oroszország Index"]},e.exports["#NYMEX:KT1!-symbol-description"]={en:["Coffee Futures"],hu_HU:["Kávé Határidősők"]},e.exports["#OANDA:NATGASUSD-symbol-description"]={en:["CFDs on Natural Gas"],hu_HU:["Földgáz"]},e.exports["#OANDA:USDPLN-symbol-description"]={en:["U.S. Dollar / Polish Zloty"],hu_HU:["amerikai dollár / lengyel zloty"]},
e.exports["#TSX:TX60-symbol-description"]={en:["S&P/TSX 60 Index"],hu_HU:["S&P/TSX 60 Index"]},e.exports["#TSX:VBU-symbol-description"]={en:["Vanguard U.S. Aggregate Bond Index ETF (CAD-hedged) UN"],hu_HU:["Vanguard US Aggregált BND INDX ETF(CAD-HEG)UN"]},e.exports["#TSX:VIXC-symbol-description"]={en:["S&P/TSX 60 VIX Index"],hu_HU:["S&P/TSX 60 VIX"]},e.exports["#TVC:CAC40-symbol-description"]={en:["CAC 40 Index"],hu_HU:["CAC 40"]},e.exports["#TVC:ES10-symbol-description"]={en:["Spain Government Bonds 10 YR"],hu_HU:["Spanyol Államkötvények 10 éves"]},e.exports["#TVC:EUBUND-symbol-description"]={en:["Euro Bund"],hu_HU:["Eurókötvény"]},e.exports["#TVC:GB02-symbol-description"]={en:["UK Government Bonds 2 YR"],hu_HU:["UK Államkötvények 2 éves"]},e.exports["#TVC:GB10-symbol-description"]={en:["UK Government Bonds 10 YR"],hu_HU:["UK Államkötvények 10 éves"]},e.exports["#TVC:GOLD-symbol-description"]={en:["CFDs on Gold (US$ / OZ)"],hu_HU:["ARANY (US$/OZ)"]},e.exports["#TVC:ID03-symbol-description"]={en:["Indonesia Government Bonds 3 YR"],hu_HU:["Indonéz Államkötvények 3 éves"]},e.exports["#TVC:ID10-symbol-description"]={en:["Indonesia Government Bonds 10 YR"],hu_HU:["Indonéz Államkötvények 10 éves"]},e.exports["#TVC:PALLADIUM-symbol-description"]={en:["CFDs on Palladium (US$ / OZ)"],hu_HU:["PALLÁDIUM (US$/OZ)"]},e.exports["#TVC:PT10-symbol-description"]={en:["Portugal Government Bonds 10 YR"],hu_HU:["Portugál Államkötvények 10 éves"]},e.exports["#TVC:SILVER-symbol-description"]={en:["CFDs on Silver (US$ / OZ)"],hu_HU:["EZÜST (US$/OZ)"]},e.exports["#TSX:TSX-symbol-description"]={en:["S&P/TSX Composite Index"],hu_HU:["S&P/TSX Kompozit"]},e.exports["#OANDA:CH20CHF-symbol-description"]={en:["Swiss 20 Index"],hu_HU:["Swiss 20 Index"]},e.exports["#TVC:SHCOMP-symbol-description"]={en:["Shanghai Composite Index"],hu_HU:["Shanghaj Kompozit"]},e.exports["#NZX:ALLC-symbol-description"]={en:["S&P/NZX All Index (Capital Index)"],hu_HU:["S&P/NZX ÖSSZES Index ( Tőkeindex)"]},e.exports["#AMEX:SHYG-symbol-description"]={en:["Shares 0-5 YEAR High Yield Corporate Bond ETF"],hu_HU:["Shares 0-5 YEAR High Yield Corporate Bond ETF"]},e.exports["#TVC:AU10-symbol-description"]={en:["Australia Government Bonds 10 YR"],hu_HU:["Australia Government Bonds 10 YR"]},e.exports["#TVC:CN10-symbol-description"]={en:["China Government Bonds 10 YR"],hu_HU:["China Government Bonds 10 YR"]},e.exports["#TVC:KR10-symbol-description"]={en:["Korea Government Bonds 10 YR"],hu_HU:["Korea Government Bonds 10 YR"]},e.exports["#NYMEX:RB1!-symbol-description"]={en:["RBOB Gasoline Futures"],hu_HU:["RBOB Gasoline Futures"]},e.exports["#NYMEX:HO1!-symbol-description"]={en:["NY Harbor ULSD Futures"],hu_HU:["NY Harbor ULSD Futures"]},e.exports["#NYMEX:AEZ1!-symbol-description"]={en:["NY Ethanol Futures"],hu_HU:["NY Ethanol Futures"]},e.exports["#OANDA:XCUUSD-symbol-description"]={en:["CFDs on Copper (US$ / lb)"],hu_HU:["CFDs on Copper (US$ / lb)"]},e.exports["#COMEX:ZA1!-symbol-description"]={en:["Zinc Futures"],hu_HU:["Zinc Futures"]},
e.exports["#CBOT:ZW1!-symbol-description"]={en:["Wheat Futures"],hu_HU:["Wheat Futures"]},e.exports["#NYMEX:KA1!-symbol-description"]={en:["Sugar #11 Futures"],hu_HU:["Sugar #11 Futures"]},e.exports["#CBOT:QBC1!-symbol-description"]={en:["Corn Futures"],hu_HU:["Corn Futures"]},e.exports["#CME:E61!-symbol-description"]={en:["Euro Futures"],hu_HU:["Euro Futures"]},e.exports["#CME:B61!-symbol-description"]={en:["British Pound Futures"],hu_HU:["British Pound Futures"]},e.exports["#CME:QJY1!-symbol-description"]={en:["Japanese Yen Futures"],hu_HU:["Japanese Yen Futures"]},e.exports["#CME:A61!-symbol-description"]={en:["Australian Dollar Futures"],hu_HU:["Ausztrál dollár határidős"]},e.exports["#CME:D61!-symbol-description"]={en:["Canadian Dollar Futures"],hu_HU:["Kanadai dollár határidős"]},e.exports["#CME:SP1!-symbol-description"]={en:["S&P 500 Futures"],hu_HU:["S&P 500 Futures"]},e.exports["#CME_MINI:NQ1!-symbol-description"]={en:["NASDAQ 100 E-mini Futures"],hu_HU:["NASDAQ 100 E-mini Futures"]},e.exports["#CBOT_MINI:YM1!-symbol-description"]={en:["E-mini Dow Jones ($5) Futures"],hu_HU:["E-mini Dow Jones ($5) Futures"]},e.exports["#CME:NY1!-symbol-description"]={en:["NIKKEI 225 Futures"],hu_HU:["NIKKEI 225 Futures"]},e.exports["#EUREX:DY1!-symbol-description"]={en:["DAX Index"],hu_HU:["DAX Index"]},e.exports["#CME:IF1!-symbol-description"]={en:["IBOVESPA Index Futures-US$"],hu_HU:["IBOVESPA Index Futures-US$"]},e.exports["#CBOT:TY1!-symbol-description"]={en:["10 Year T-Note Futures"],hu_HU:["10 Year T-Note Futures"]},e.exports["#CBOT:FV1!-symbol-description"]={en:["5 Year T-Note Futures"],hu_HU:["5 Year T-Note Futures"]},e.exports["#CBOT:ZE1!-symbol-description"]={en:["Treasury Notes - 3 Year Futures"],hu_HU:["Treasury Notes - 3 Year Futures"]},e.exports["#CBOT:TU1!-symbol-description"]={en:["2 Year T-Note Futures"],hu_HU:["2 Year T-Note Futures"]},e.exports["#CBOT:FF1!-symbol-description"]={en:["30-Day FED Funds Interest Rate Futures"],hu_HU:["30-Day FED Funds Interest Rate Futures"]},e.exports["#CBOT:US1!-symbol-description"]={en:["T-Bond Futures"],hu_HU:["T-Bond Futures"]},e.exports["#TVC:EXY-symbol-description"]={en:["Euro Currency Index"],hu_HU:["Euro Currency Index"]},e.exports["#TVC:JXY-symbol-description"]={en:["Japanese Yen Currency Index"],hu_HU:["Japanese Yen Currency Index"]},e.exports["#TVC:BXY-symbol-description"]={en:["British Pound Currency Index"],hu_HU:["British Pound Currency Index"]},e.exports["#TVC:AXY-symbol-description"]={en:["Australian Dollar Currency Index"],hu_HU:["Ausztrál Dollár Devizaindex"]},e.exports["#TVC:CXY-symbol-description"]={en:["Canadian Dollar Currency Index"],hu_HU:["Kanadai Dollár Devizaindex"]},e.exports["#FRED:GDP-symbol-description"]={en:["Gross Domestic Product, 1 Decimal"],hu_HU:["Gross Domestic Product, 1 Decimal"]},e.exports["#FRED:UNRATE-symbol-description"]={en:["Civilian Unemployment Rate"],hu_HU:["Civilian Unemployment Rate"]},e.exports["#FRED:POP-symbol-description"]={en:["Total Population: All Ages Including Armed Forces Overseas"],
hu_HU:["Total Population: All Ages Including Armed Forces Overseas"]},e.exports["#ETHUSD-symbol-description"]={en:["Ethereum / U.S. Dollar"],hu_HU:["ethereum / amerikai dollár"]},e.exports["#BMFBOVESPA:IBOV-symbol-description"]={en:["Bovespa Index"],hu_HU:["IBovespa Index"]},e.exports["#BMFBOVESPA:IBRA-symbol-description"]={en:["IBrasil Index"],hu_HU:["IBrasil Index"]},e.exports["#BMFBOVESPA:IBXL-symbol-description"]={en:["Brazil 50 Index"],hu_HU:["IBRX 50 Index"]},e.exports["#COMEX:HG1!-symbol-description"]={en:["Copper Futures"],hu_HU:["Copper Futures"]},e.exports["#INDEX:HSCE-symbol-description"]={en:["Hang Seng China Enterprises Index"],hu_HU:["Hang Seng China Enterprises Index"]},e.exports["#NYMEX:CL1!-symbol-description"]={en:["Light Crude Oil Futures"],hu_HU:["Light Crude Oil Futures"]},e.exports["#OTC:IHRMF-symbol-description"]={en:["Ishares MSCI Japan SHS"],hu_HU:["Ishares MSCI Japan SHS"]},e.exports["#TVC:DAX-symbol-description"]={en:["DAX Index"],hu_HU:["DAX Index"]},e.exports["#TVC:DE10-symbol-description"]={en:["German Government Bonds 10 YR"],hu_HU:["German Government Bonds 10 YR"]},e.exports["#TVC:DJI-symbol-description"]={en:["Dow Jones Industrial Average Index"],hu_HU:["Dow Jones Industrial Average Index"]},e.exports["#TVC:DXY-symbol-description"]={en:["U.S. Dollar Index"],hu_HU:["Amerikai Dollár Devizaindex"]},e.exports["#TVC:FR10-symbol-description"]={en:["France Government Bonds 10 YR"],hu_HU:["France Government Bonds 10 YR"]},e.exports["#TVC:HSI-symbol-description"]={en:["Hang Seng Index"],hu_HU:["Hang Seng Index"]},e.exports["#TVC:IBEX35-symbol-description"]={en:["IBEX 35 Index"],hu_HU:["IBEX 35 Index"]},e.exports["#FX:AUS200-symbol-description"]={en:["S&P/ASX Index"],hu_HU:["S&P/ASX Index"]},e.exports["#AMEX:SHY-symbol-description"]={en:["Ishares 1-3 Year Treasury Bond ETF"],hu_HU:["Ishares 1-3 Year Treasury Bond ETF"]},e.exports["#ASX:XJO-symbol-description"]={en:["S&P/ASX 200 Index"],hu_HU:["S&P/ASX 200 Index"]},e.exports["#BSE:SENSEX-symbol-description"]={en:["S&P BSE Sensex Index"],hu_HU:["S&P BSE Sensex Index"]},e.exports["#INDEX:MIB-symbol-description"]={en:["MIB Index"],hu_HU:["MIB Index"]},e.exports["#INDEX:MOY0-symbol-description"]={en:["Euro Stoxx 50 Index"],hu_HU:["Euro Stoxx 50 Index"]},e.exports["#MOEX:RTSI-symbol-description"]={en:["RTS Index"],hu_HU:["RTS Index"]},e.exports["#NSE:NIFTY-symbol-description"]={en:["Nifty 50 Index"],hu_HU:["Nifty 50 Index"]},e.exports["#NYMEX:NG1!-symbol-description"]={en:["Natural Gas Futures"],hu_HU:["Natural Gas Futures"]},e.exports["#NYMEX:ZC1!-symbol-description"]={en:["Corn Futures"],hu_HU:["Corn Futures"]},e.exports["#TVC:IN10-symbol-description"]={en:["India Government Bonds 10 YR"],hu_HU:["India Government Bonds 10 YR"]},e.exports["#TVC:IT10-symbol-description"]={en:["Italy Government Bonds 10 YR"],hu_HU:["Italy Government Bonds 10 YR"]},e.exports["#TVC:JP10-symbol-description"]={en:["Japan Government Bonds 10 YR"],hu_HU:["Japan Government Bonds 10 YR"]},e.exports["#TVC:NDX-symbol-description"]={en:["US 100 Index"],hu_HU:["NASDAQ 100 Index"]
},e.exports["#TVC:NI225-symbol-description"]={en:["Nikkei 225 Index"],hu_HU:["Nikkei 225 Index"]},e.exports["#TVC:SPX-symbol-description"]={en:["S&P 500 Index"],hu_HU:["S&P 500 Index"]},e.exports["#TVC:SX5E-symbol-description"]={en:["Euro Stoxx 50 Index"],hu_HU:["Euro Stoxx 50 Index"]},e.exports["#TVC:TR10-symbol-description"]={en:["Turkey Government Bonds 10 YR"],hu_HU:["Turkey Government Bonds 10 YR"]},e.exports["#TVC:UKOIL-symbol-description"]={en:["CFDs on Brent Crude Oil"],hu_HU:["CFDs on Brent Crude Oil"]},e.exports["#TVC:UKX-symbol-description"]={en:["UK 100 Index"],hu_HU:["UK 100 Index"]},e.exports["#TVC:US02-symbol-description"]={en:["US Government Bonds 2 YR"],hu_HU:["US Government Bonds 2 YR"]},e.exports["#TVC:US05-symbol-description"]={en:["US Government Bonds 5 YR"],hu_HU:["US Government Bonds 5 YR"]},e.exports["#TVC:US10-symbol-description"]={en:["US Government Bonds 10 YR"],hu_HU:["US Government Bonds 10 YR"]},e.exports["#TVC:USOIL-symbol-description"]={en:["CFDs on WTI Crude Oil"],hu_HU:["CFDs on WTI Crude Oil"]},e.exports["#NYMEX:ITI1!-symbol-description"]={en:["Iron Ore Futures"],hu_HU:["Iron Ore Futures"]},e.exports["#NASDAQ:SHY-symbol-description"]={en:["Ishares 1-3 Year Treasury Bond ETF"],hu_HU:["Ishares 1-3 Year Treasury Bond ETF"]},e.exports["#AMEX:ALD-symbol-description"]={en:["WisdomTree Asia Local Debt ETF"],hu_HU:["WisdomTree Asia Local Debt ETF"]},e.exports["#NASDAQ:AMD-symbol-description"]={en:["Advanced Micro Devices Inc"],hu_HU:["Advanced Micro Devices Inc"]},e.exports["#NYSE:BABA-symbol-description"]={en:["Alibaba Group Holdings Ltd."],hu_HU:["ALIBABA GROUP HLDG LTD"]},e.exports["#ICEEUR:CB-symbol-description"]={en:["Crude Oil Brent"],hu_HU:["Nyersolaj Brent"]},e.exports["#ICEEUR:CB1!-symbol-description"]={en:["Brent Crude Oil"],hu_HU:["Brent Nyersolaj"]},e.exports["#ICEUSA:CC-symbol-description"]={en:["Cocoa"],hu_HU:["Kakaó"]},e.exports["#NYMEX:CL-symbol-description"]={en:["Crude Oil WTI"],hu_HU:["Nyersolaj WTI"]},e.exports["#ICEUSA:CT-symbol-description"]={en:["Cotton #2"],hu_HU:["Gyapot #2"]},e.exports["#NASDAQ:CTRV-symbol-description"]={en:["ContraVir Pharmaceuticals Inc"],hu_HU:["CONTRAVIR PHARMACEUTICALS INC"]},e.exports["#CME:DL-symbol-description"]={en:["Class III Milk"],hu_HU:["Tej III. osztály"]},e.exports["#NYSE:F-symbol-description"]={en:["FORD MTR CO DEL"],hu_HU:["FORD MTR CO DEL"]},e.exports["#MOEX:GAZP-symbol-description"]={en:["GAZPROM"],hu_HU:["GAZPROM"]},e.exports["#COMEX:GC-symbol-description"]={en:["Gold"],hu_HU:["Arany"]},e.exports["#CME:GF-symbol-description"]={en:["Feeder Cattle"],hu_HU:["Feeder Szarvasmarha"]},e.exports["#CME:HE-symbol-description"]={en:["Lean Hogs"],hu_HU:["Lean Sertés"]},e.exports["#NASDAQ:IEF-symbol-description"]={en:["Ishares 7-10 Year Treasury Bond ETF"],hu_HU:["Ishares 7-10 Year Treasury Bond ETF"]},e.exports["#NASDAQ:IEI-symbol-description"]={en:["Ishares 3-7 Year Treasury Bond ETF"],hu_HU:["Ishares 3-7 Year Treasury Bond ETF"]},e.exports["#NYMEX:KA1-symbol-description"]={en:["Sugar #11 Futures"],hu_HU:["Sugar #11 Futures"]},
e.exports["#ICEUSA:KC-symbol-description"]={en:["Coffee"],hu_HU:["Kávé"]},e.exports["#NYMEX:KG1-symbol-description"]={en:["Cotton Futures"],hu_HU:["Cotton Futures"]},e.exports["#FWB:KT1-symbol-description"]={en:["Key Tronic Corр."],hu_HU:["Key Tronic Corр."]},e.exports["#CME:LE-symbol-description"]={en:["Live Cattle"],hu_HU:["Élő Szarvasmarha"]},e.exports["#ICEEUR:LO-symbol-description"]={en:["ICE Heating Oil"],hu_HU:["ICE Fűtőolaj"]},e.exports["#CME:LS-symbol-description"]={en:["Lumber"],hu_HU:["Fűrészárú"]},e.exports["#MOEX:MGNT-symbol-description"]={en:["MAGNIT"],hu_HU:["MAGNIT"]},e.exports["#LSIN:MNOD-symbol-description"]={en:["ADR GMK NORILSKIYNIKEL ORD SHS [REPO]"],hu_HU:["ADR GMK NORILSKIYNIKEL ORD SHS [REPO]"]},e.exports["#NYMEX:NG-symbol-description"]={en:["Natural Gas"],hu_HU:["Földgáz"]},e.exports["#ICEUSA:OJ-symbol-description"]={en:["Orange Juice"],hu_HU:["Narancslé"]},e.exports["#NYMEX:PA-symbol-description"]={en:["Palladium"],hu_HU:["Palládium"]},e.exports["#NYSE:PBR-symbol-description"]={en:["PETROLEO BRASILEIRO SA PETROBR"],hu_HU:["PETROLEO BRASILEIRO SA PETROBR"]},e.exports["#NYMEX:PL-symbol-description"]={en:["Platinum"],hu_HU:["Platina"]},e.exports["#COMEX_MINI:QC-symbol-description"]={en:["E-Mini Copper"],hu_HU:["E-Mini Réz"]},e.exports["#NYMEX:RB-symbol-description"]={en:["Gasoline RBOB"],hu_HU:["Benzin RBOB"]},e.exports["#NYMEX:RB1-symbol-description"]={en:["RBOB Gasoline Futures"],hu_HU:["#KT1-symbol-description"]},e.exports["#MOEX:SBER-symbol-description"]={en:["SBERBANK"],hu_HU:["SBERBANK"]},e.exports["#AMEX:SCHO-symbol-description"]={en:["Schwab Short-Term U.S. Treasury ETF"],hu_HU:["Schwab Short-Term U.S. Treasury ETF"]},e.exports["#COMEX:SI-symbol-description"]={en:["Silver"],hu_HU:["Ezüst"]},e.exports["#NASDAQ:TLT-symbol-description"]={en:["Ishares 20+ Year Treasury Bond ETF"],hu_HU:["Ishares 20+ Year Treasury Bond ETF"]},e.exports["#TVC:VIX-symbol-description"]={en:["Volatility S&P 500 Index"],hu_HU:["Volatility S&P 500 Index"]},e.exports["#MOEX:VTBR-symbol-description"]={en:["VTB"],hu_HU:["VTB"]},e.exports["#COMEX:ZA-symbol-description"]={en:["Zinc"],hu_HU:["Cink"]},e.exports["#CBOT:ZC-symbol-description"]={en:["Corn"],hu_HU:["Kukorica"]},e.exports["#CBOT:ZK-symbol-description"]={en:["Ethanol Futures"],hu_HU:["Etanol Határidős"]},e.exports["#CBOT:ZL-symbol-description"]={en:["Soybean Oil"],hu_HU:["Szójabab Olaj"]},e.exports["#CBOT:ZO-symbol-description"]={en:["Oats"],hu_HU:["Zab"]},e.exports["#CBOT:ZR-symbol-description"]={en:["Rough Rice"],hu_HU:["Hántolatlan Rizs"]},e.exports["#CBOT:ZS-symbol-description"]={en:["Soybeans"],hu_HU:["Szójabab"]},e.exports["#CBOT:ZS1-symbol-description"]={en:["Soybean Futures"],hu_HU:["Soybean Futures"]},e.exports["#CBOT:ZW-symbol-description"]={en:["Wheat"],hu_HU:["Búza"]},e.exports["#CBOT:ZW1-symbol-description"]={en:["Wheat Futures - ECBT"],hu_HU:["Wheat Futures - ECBT"]},e.exports["#NASDAQ:ITI-symbol-description"]={en:["Iteris Inc"],hu_HU:["Iteris Inc."]},e.exports["#NYMEX:ITI2!-symbol-description"]={en:["Iron Ore Futures"],hu_HU:["Iron Ore Futures"]
},e.exports["#CADUSD-symbol-description"]={en:["Canadian Dollar / U.S. Dollar"],hu_HU:["kanadai dollár / amerikai dollár"]},e.exports["#CHFUSD-symbol-description"]={en:["Swiss Franc / U.S. Dollar"],hu_HU:["svájci frank / amerikai dollár"]},e.exports["#GPW:ACG-symbol-description"]={en:["Acautogaz"],hu_HU:["Acautogaz"]},e.exports["#JPYUSD-symbol-description"]={en:["Japanese Yen / U.S. Dollar"],hu_HU:["japán jen / amerikai dollár"]},e.exports["#USDAUD-symbol-description"]={en:["U.S. Dollar / Australian Dollar"],hu_HU:["amerikai dollár / ausztrál dollár"]},e.exports["#USDEUR-symbol-description"]={en:["U.S. Dollar / Euro"],hu_HU:["amerikai dollár / euró"]},e.exports["#USDGBP-symbol-description"]={en:["U.S. Dollar / Pound Sterling"],hu_HU:["amerikai dollár / brit font"]},e.exports["#USDNZD-symbol-description"]={en:["U.S. Dollar / New Zealand Dollar"],hu_HU:["amerikai dollár / új-zélandi dollár"]},e.exports["#UKOIL-symbol-description"]={en:["CFDs on Crude Oil (Brent)"],hu_HU:["CFDs on Crude Oil (Brent)"]},e.exports["#USOIL-symbol-description"]={en:["CFDs on Crude Oil (WTI)"],hu_HU:["CFDs on Crude Oil (WTI)"]},e.exports["#US30-symbol-description"]={en:["Dow Jones Industrial Average Index"],hu_HU:["Dow Jones Industrial Average Index"]},e.exports["#BCHUSD-symbol-description"]={en:["Bitcoin Cash / U.S. Dollar"],hu_HU:["bitcoin cash / amerikai dollár"]},e.exports["#ETCUSD-symbol-description"]={en:["Ethereum Classic / U.S. Dollar"],hu_HU:["ethereum classic / amerikai dollár"]},e.exports["#GOOG-symbol-description"]={en:["Alphabet Inc (Google) Class C"],hu_HU:["Alphabet Inc (Google) Class C"]},e.exports["#LTCUSD-symbol-description"]={en:["Litecoin / U.S. Dollar"],hu_HU:["litecoin / amerikai dollár"]},e.exports["#XRPUSD-symbol-description"]={en:["XRP / U.S. Dollar"],hu_HU:["ripple / amerikai dollár"]},e.exports["#SP:SPX-symbol-description"]={en:["S&P 500 Index"],hu_HU:["S&P 500 Index"]},e.exports["#ETCBTC-symbol-description"]={en:["Ethereum Classic / Bitcoin"],hu_HU:["Ethereum Classic / Bitcoin"]},e.exports["#ETHBTC-symbol-description"]={en:["Ethereum / Bitcoin"],hu_HU:["Ethereum / Bitcoin"]},e.exports["#XRPBTC-symbol-description"]={en:["XRP / Bitcoin"],hu_HU:["Ripple / Bitcoin"]},e.exports["#TVC:US30-symbol-description"]={en:["US Government Bonds 30 YR"],hu_HU:["US Government Bonds 30 YR"]},e.exports["#COMEX:SI1!-symbol-description"]={en:["Silver Futures"],hu_HU:["Silver Futures"]},e.exports["#BTGUSD-symbol-description"]={en:["Bitcoin Gold / U.S. Dollar"],hu_HU:["bitcoin gold / amerikai dollár"]},e.exports["#IOTUSD-symbol-description"]={en:["IOTA / U.S. Dollar"],hu_HU:["IOTA / amerikai dollár"]},e.exports["#CME:BTC1!-symbol-description"]={en:["Bitcoin CME Futures"],hu_HU:["Bitcoin CME Futures"]},e.exports["#COMEX:GC1!-symbol-description"]={en:["Gold Futures"],hu_HU:["Gold Futures"]},e.exports["#CORNUSD-symbol-description"]={en:["CFDs on Corn"],hu_HU:["CFDs on Corn"]},e.exports["#COTUSD-symbol-description"]={en:["CFDs on Cotton"],hu_HU:["CFDs on Cotton"]},e.exports["#DJ:DJA-symbol-description"]={
en:["Dow Jones Composite Average Index"],hu_HU:["Dow Jones Composite Average Index"]},e.exports["#DJ:DJI-symbol-description"]={en:["Dow Jones Industrial Average Index"],hu_HU:["Dow Jones Industrial Average Index"]},e.exports["#ETHEUR-symbol-description"]={en:["Ethereum / Euro"],hu_HU:["Ethereum / Euro"]},e.exports["#ETHGBP-symbol-description"]={en:["Ethereum / British Pound"],hu_HU:["Ethereum / British Pound"]},e.exports["#ETHJPY-symbol-description"]={en:["Ethereum / Japanese Yen"],hu_HU:["Ethereum / Japanese Yen"]},e.exports["#EURNOK-symbol-description"]={en:["Euro / Norwegian Krone"],hu_HU:["Euro / Norwegian Krone"]},e.exports["#GBPPLN-symbol-description"]={en:["British Pound / Polish Zloty"],hu_HU:["British Pound / Polish Zloty"]},e.exports["#MOEX:BR1!-symbol-description"]={en:["Brent Oil Futures"],hu_HU:["Brent Oil Futures"]},e.exports["#NYMEX:KG1!-symbol-description"]={en:["Cotton Futures"],hu_HU:["Cotton Futures"]},e.exports["#NYMEX:PL1!-symbol-description"]={en:["Platinum Futures"],hu_HU:["Platinum Futures"]},e.exports["#SOYBNUSD-symbol-description"]={en:["CFDs on Soybeans"],hu_HU:["CFDs on Soybeans"]},e.exports["#SUGARUSD-symbol-description"]={en:["CFDs on Sugar"],hu_HU:["CFDs on Sugar"]},e.exports["#TVC:IXIC-symbol-description"]={en:["US Composite Index"],hu_HU:["NASDAQ Composite Index"]},e.exports["#TVC:RU-symbol-description"]={en:["Russell 1000 Index"],hu_HU:["Russell 1000 Index"]},e.exports["#USDZAR-symbol-description"]={en:["U.S. Dollar / South African Rand"],hu_HU:["amerikai dollár / dél-afrikai rand"]},e.exports["#WHEATUSD-symbol-description"]={en:["CFDs on Wheat"],hu_HU:["CFDs on Wheat"]},e.exports["#XRPEUR-symbol-description"]={en:["XRP / Euro"],hu_HU:["Ripple / Euro"]},e.exports["#CBOT:S1!-symbol-description"]={en:["Soybean Futures"],hu_HU:["Soybean Futures"]},e.exports["#SP:MID-symbol-description"]={en:["S&P 400 Index"],hu_HU:["S&P 400 Index"]},e.exports["#TSX:XCUUSD-symbol-description"]={en:["CFDs on Copper"],hu_HU:["CFDs on Copper"]},e.exports["#TVC:NYA-symbol-description"]={en:["NYSE Composite Index"],hu_HU:["NYSE Composite Index"]},e.exports["#TVC:PLATINUM-symbol-description"]={en:["CFDs on Platinum (US$ / OZ)"],hu_HU:["CFDs on Platinum (US$ / OZ)"]},e.exports["#TVC:SSMI-symbol-description"]={en:["Swiss Market Index"],hu_HU:["Swiss Market Index"]},e.exports["#TVC:SXY-symbol-description"]={en:["Swiss Franc Currency Index"],hu_HU:["Swiss Franc Currency Index"]},e.exports["#MOEX:RI1!-symbol-description"]={en:["RTS Index Futures"],hu_HU:["RTS Index Futures"]},e.exports["#MOEX:MX1!-symbol-description"]={en:["MICEX Index Futures"],hu_HU:["MICEX Index Futures"]},e.exports["#CBOE:BG1!-symbol-description"]={en:["Bitcoin CBOE Futures"],hu_HU:["Bitcoin CBOE Futures"]},e.exports["#TVC:MY10-symbol-description"]={en:["Malaysia Government Bonds 10 YR"],hu_HU:["Malaysia Government Bonds 10 YR"]},e.exports["#CME:S61!-symbol-description"]={en:["Swiss Franc Futures"],hu_HU:["Swiss Franc Futures"]},e.exports["#TVC:DEU30-symbol-description"]={en:["DAX Index"],hu_HU:["DAX Index"]},
e.exports["#BCHEUR-symbol-description"]={en:["Bitcoin Cash / Euro"],hu_HU:["Bitcoin Cash / Euro"]},e.exports["#TVC:ZXY-symbol-description"]={en:["New Zealand Dollar Currency Index"],hu_HU:["Új-zélandi Dollár Devizaindex"]},e.exports["#MIL:FTSEMIB-symbol-description"]={en:["FTSE MIB Index"],hu_HU:["FTSE MIB Index"]},e.exports["#XETR:DAX-symbol-description"]={en:["DAX Index"],hu_HU:["DAX Index"]},e.exports["#MOEX:IMOEX-symbol-description"]={en:["MOEX Russia Index"],hu_HU:["MOEX Russia Index"]},e.exports["#FX:US30-symbol-description"]={en:["Dow Jones Industrial Average Index"],hu_HU:["Dow Jones Industrial Average Index"]},e.exports["#MOEX:RUAL-symbol-description"]={en:["United Company RUSAL PLC"],hu_HU:["United Company RUSAL PLC"]},e.exports["#MOEX:MX2!-symbol-description"]={en:["MICEX Index Futures"],hu_HU:["MICEX Index Futures"]},e.exports["#NEOUSD-symbol-description"]={en:["NEO / U.S. Dollar"],hu_HU:["NEO / amerikai dollár"]},e.exports["#XMRUSD-symbol-description"]={en:["Monero / U.S. Dollar"],hu_HU:["monero / amerikai dollár"]},e.exports["#ZECUSD-symbol-description"]={en:["Zcash / U.S. Dollar"],hu_HU:["Zcash / amerikai dollár"]},e.exports["#TVC:CAC-symbol-description"]={en:["CAC 40 Index"],hu_HU:["CAC 40"]},e.exports["#NASDAQ:ZS-symbol-description"]={en:["Zscaler Inc"],hu_HU:["Zscaler Inc"]},e.exports["#TVC:GB10Y-symbol-description"]={en:["UK Government Bonds 10 YR Yield"],hu_HU:["UK Államkötvények 10 éves"]},e.exports["#TVC:AU10Y-symbol-description"]={en:["Australia Government Bonds 10 YR Yield"],hu_HU:["Australia Government Bonds 10 YR Yield"]},e.exports["#TVC:CN10Y-symbol-description"]={en:["China Government Bonds 10 YR Yield"],hu_HU:["China Government Bonds 10 YR Yield"]},e.exports["#TVC:DE10Y-symbol-description"]={en:["German Government Bonds 10 YR Yield"],hu_HU:["German Government Bonds 10 YR Yield"]},e.exports["#TVC:ES10Y-symbol-description"]={en:["Spain Government Bonds 10 YR Yield"],hu_HU:["Spanyol Államkötvények 10 éves"]},e.exports["#TVC:FR10Y-symbol-description"]={en:["France Government Bonds 10 YR Yield"],hu_HU:["France Government Bonds 10 YR Yield"]},e.exports["#TVC:IN10Y-symbol-description"]={en:["India Government Bonds 10 YR Yield"],hu_HU:["Indiai államkötvények 10 éves"]},e.exports["#TVC:IT10Y-symbol-description"]={en:["Italy Government Bonds 10 YR Yield"],hu_HU:["Olasz államkötvények 10 éves"]},e.exports["#TVC:JP10Y-symbol-description"]={en:["Japan Government Bonds 10 YR Yield"],hu_HU:["japán államkötvények 10 éves"]},e.exports["#TVC:KR10Y-symbol-description"]={en:["Korea Government Bonds 10 YR Yield"],hu_HU:["Korea Government Bonds 10 YR Yield"]},e.exports["#TVC:MY10Y-symbol-description"]={en:["Malaysia Government Bonds 10 YR Yield"],hu_HU:["Malaysia Government Bonds 10 YR Yield"]},e.exports["#TVC:PT10Y-symbol-description"]={en:["Portugal Government Bonds 10 YR Yield"],hu_HU:["Portugál Államkötvények 10 éves"]},e.exports["#TVC:TR10Y-symbol-description"]={en:["Turkey Government Bonds 10 YR Yield"],hu_HU:["Turkey Government Bonds 10 YR"]},e.exports["#TVC:US02Y-symbol-description"]={
en:["US Government Bonds 2 YR Yield"],hu_HU:["US államkötvények 2 éves"]},e.exports["#TVC:US05Y-symbol-description"]={en:["US Government Bonds 5 YR Yield"],hu_HU:["US államkötvények 5 éves"]},e.exports["#TVC:US10Y-symbol-description"]={en:["US Government Bonds 10 YR Yield"],hu_HU:["US államkötvények 10 éves"]},e.exports["#INDEX:TWII-symbol-description"]={en:["Taiwan Weighted Index"],hu_HU:["Taiwan Weighted Index"]},e.exports["#CME:J61!-symbol-description"]={en:["Japanese Yen Futures"],hu_HU:["Japanese Yen Futures"]},e.exports["#CME_MINI:J71!-symbol-description"]={en:["Japanese Yen E-mini Futures"],hu_HU:["Japanese Yen E-mini Futures"]},e.exports["#CME_MINI:WM1!-symbol-description"]={en:["E-micro Japanese Yen / U.S. Dollar Futures"],hu_HU:["E-micro Japanese Yen / U.S. Dollar Futures"]},e.exports["#CME:M61!-symbol-description"]={en:["Mexican Peso Futures"],hu_HU:["Mexican Peso Futures"]},e.exports["#CME:T61!-symbol-description"]={en:["South African Rand Futures"],hu_HU:["South African Rand Futures"]},e.exports["#CME:SK1!-symbol-description"]={en:["Swedish Krona Futures"],hu_HU:["Swedish Krona Futures"]},e.exports["#CME:QT1!-symbol-description"]={en:["Chinese Renminbi / U.S. Dollar Futures"],hu_HU:["Chinese Renminbi / U.S. Dollar Futures"]},e.exports["#COMEX:AUP1!-symbol-description"]={en:["Aluminum MW U.S. Transaction Premium Platts (25MT) Futures"],hu_HU:["Aluminum MW U.S. Transaction Premium Platts (25MT) Futures"]},e.exports["#CME:L61!-symbol-description"]={en:["Brazilian Real Futures"],hu_HU:["Brazilian Real Futures"]},e.exports["#CME:WP1!-symbol-description"]={en:["Polish Zloty Futures"],hu_HU:["Polish Zloty Futures"]},e.exports["#CME:N61!-symbol-description"]={en:["New Zealand Dollar Futures"],hu_HU:["New Zealand Dollar Futures"]},e.exports["#CME_MINI:MG1!-symbol-description"]={en:["E-micro Australian Dollar / U.S. Dollar Futures"],hu_HU:["E-micro Australian Dollar / U.S. Dollar Futures"]},e.exports["#CME_MINI:WN1!-symbol-description"]={en:["E-micro Swiss Franc / U.S. Dollar Futures"],hu_HU:["E-micro Swiss Franc / U.S. Dollar Futures"]},e.exports["#CME_MINI:MF1!-symbol-description"]={en:["E-micro Euro / U.S. Dollar Futures"],hu_HU:["E-micro Euro / U.S. Dollar Futures"]},e.exports["#CME_MINI:E71!-symbol-description"]={en:["Euro E-mini Futures"],hu_HU:["Euro E-mini Futures"]},e.exports["#CBOT:ZK1!-symbol-description"]={en:["Denatured Fuel Ethanol Futures"],hu_HU:["Denatured Fuel Ethanol Futures"]},e.exports["#CME_MINI:MB1!-symbol-description"]={en:["E-micro British Pound / U.S. Dollar Futures"],hu_HU:["E-micro British Pound / U.S. Dollar Futures"]},e.exports["#NYMEX_MINI:QU1!-symbol-description"]={en:["E-mini Gasoline Futures"],hu_HU:["E-mini Gasoline Futures"]},e.exports["#NYMEX_MINI:QX1!-symbol-description"]={en:["E-mini Heating Oil Futures"],hu_HU:["E-mini Heating Oil Futures"]},e.exports["#COMEX_MINI:QC1!-symbol-description"]={en:["E-mini Copper Futures"],hu_HU:["E-mini Copper Futures"]},e.exports["#NYMEX_MINI:QG1!-symbol-description"]={en:["E-mini Natural Gas Futures"],hu_HU:["E-mini Natural Gas Futures"]},
e.exports["#CME:E41!-symbol-description"]={en:["U.S. Dollar / Turkish Lira Futures"],hu_HU:["U.S. Dollar / Turkish Lira Futures"]},e.exports["#COMEX_MINI:QI1!-symbol-description"]={en:["Silver (Mini) Futures"],hu_HU:["Silver (Mini) Futures"]},e.exports["#CME:DL1!-symbol-description"]={en:["Milk, Class III Futures"],hu_HU:["Milk, Class III Futures"]},e.exports["#NYMEX:UX1!-symbol-description"]={en:["Uranium Futures"],hu_HU:["Uranium Futures"]},e.exports["#CBOT:BO1!-symbol-description"]={en:["Soybean Oil Futures"],hu_HU:["Soybean Oil Futures"]},e.exports["#CME:HE1!-symbol-description"]={en:["Lean Hogs Futures"],hu_HU:["Lean Hogs Futures"]},e.exports["#NYMEX:IAC1!-symbol-description"]={en:["Newcastle Coal Futures"],hu_HU:["Newcastle Coal Futures"]},e.exports["#NYMEX_MINI:QM1!-symbol-description"]={en:["E-mini Light Crude Oil Futures"],hu_HU:["E-mini Light Crude Oil Futures"]},e.exports["#NYMEX:JMJ1!-symbol-description"]={en:["Mini Brent Financial Futures"],hu_HU:["Mini Brent Financial Futures"]},e.exports["#COMEX:AEP1!-symbol-description"]={en:["Aluminium European Premium Futures"],hu_HU:["Aluminium European Premium Futures"]},e.exports["#CBOT:ZQ1!-symbol-description"]={en:["30 Day Federal Funds Interest Rate Futures"],hu_HU:["30 Day Federal Funds Interest Rate Futures"]},e.exports["#CME:LE1!-symbol-description"]={en:["Live Cattle Futures"],hu_HU:["Live Cattle Futures"]},e.exports["#CME:UP1!-symbol-description"]={en:["Swiss Franc / Japanese Yen Futures"],hu_HU:["Swiss Franc / Japanese Yen Futures"]},e.exports["#CBOT:ZN1!-symbol-description"]={en:["10 Year T-Note Futures"],hu_HU:["10 Year T-Note Futures"]},e.exports["#CBOT:ZB1!-symbol-description"]={en:["T-Bond Futures"],hu_HU:["T-Bond Futures"]},e.exports["#CME:GF1!-symbol-description"]={en:["Feeder Cattle Futures"],hu_HU:["Feeder Cattle Futures"]},e.exports["#CBOT:UD1!-symbol-description"]={en:["Ultra T-Bond Futures"],hu_HU:["Ultra T-Bond Futures"]},e.exports["#CME:I91!-symbol-description"]={en:["CME Housing Futures — Washington DC"],hu_HU:["CME Housing Futures — Washington DC"]},e.exports["#CBOT:ZO1!-symbol-description"]={en:["Oat Futures"],hu_HU:["Oat Futures"]},e.exports["#CBOT:ZM1!-symbol-description"]={en:["Soybean Meal Futures"],hu_HU:["Soybean Meal Futures"]},e.exports["#CBOT_MINI:XN1!-symbol-description"]={en:["Corn Mini Futures"],hu_HU:["Corn Mini Futures"]},e.exports["#CBOT:ZC1!-symbol-description"]={en:["Corn Futures"],hu_HU:["Corn Futures"]},e.exports["#CME:LS1!-symbol-description"]={en:["Lumber Futures"],hu_HU:["Lumber Futures"]},e.exports["#CBOT_MINI:XW1!-symbol-description"]={en:["Wheat Mini Futures"],hu_HU:["Wheat Mini Futures"]},e.exports["#CBOT_MINI:XK1!-symbol-description"]={en:["Soybean Mini Futures"],hu_HU:["Soybean Mini Futures"]},e.exports["#CBOT:ZS1!-symbol-description"]={en:["Soybean Futures"],hu_HU:["Soybean Futures"]},e.exports["#NYMEX:PA1!-symbol-description"]={en:["Palladium Futures"],hu_HU:["Palladium Futures"]},e.exports["#CME:FTU1!-symbol-description"]={en:["E-mini FTSE 100 Index USD Futures"],hu_HU:["E-mini FTSE 100 Index USD Futures"]
},e.exports["#CBOT:ZR1!-symbol-description"]={en:["Rice Futures"],hu_HU:["Rice Futures"]},e.exports["#COMEX_MINI:GR1!-symbol-description"]={en:["Gold (E-micro) Futures"],hu_HU:["Gold (E-micro) Futures"]},e.exports["#COMEX_MINI:QO1!-symbol-description"]={en:["Gold (Mini) Futures"],hu_HU:["Gold (Mini) Futures"]},e.exports["#CME_MINI:RL1!-symbol-description"]={en:["E-mini Russell 1000 Futures"],hu_HU:["E-mini Russell 1000 Futures"]},e.exports["#CME_MINI:EW1!-symbol-description"]={en:["S&P 400 Midcap E-mini Futures"],hu_HU:["S&P 400 Midcap E-mini Futures"]},e.exports["#COMEX:LD1!-symbol-description"]={en:["Lead Futures"],hu_HU:["Lead Futures"]},e.exports["#CME_MINI:ES1!-symbol-description"]={en:["S&P 500 E-mini Futures"],hu_HU:["S&P 500 E-mini Futures"]},e.exports["#TVC:SA40-symbol-description"]={en:["South Africa Top 40 Index"],hu_HU:["South Africa Top 40 Index"]},e.exports["#BMV:ME-symbol-description"]={en:["S&P/BMV IPC Index"],hu_HU:["IPC Mexico Index"]},e.exports["#BCBA:IMV-symbol-description"]={en:["S&P MERVAL Index"],hu_HU:["MERVAL Index"]},e.exports["#HSI:HSI-symbol-description"]={en:["Hang Seng Index"],hu_HU:["Hang Seng Index"]},e.exports["#BVL:SPBLPGPT-symbol-description"]={en:["S&P / BVL Peru General Index (PEN)"],hu_HU:["S&P / BVL Peru General Index (PEN)"]},e.exports["#EGX:EGX30-symbol-description"]={en:["EGX 30 Index"],hu_HU:["EGX 30 Price Return Index"]},e.exports["#BVC:IGBC-symbol-description"]={en:["Indice General de la Bolsa de Valores de Colombia"],hu_HU:["Indice General de la Bolsa de Valores de Colombia"]},e.exports["#TWSE:TAIEX-symbol-description"]={en:["Taiwan Capitalization Weighted Stock Index"],hu_HU:["Taiwan Capitalization Weighted Stock Index"]},e.exports["#QSE:GNRI-symbol-description"]={en:["QE Index"],hu_HU:["QE Index"]},e.exports["#BME:IBC-symbol-description"]={en:["IBEX 35 Index"],hu_HU:["IBEX 35 Index"]},e.exports["#NZX:NZ50G-symbol-description"]={en:["S&P / NZX 50 Index Gross"],hu_HU:["S&P / NZX 50 Index Gross"]},e.exports["#SIX:SMI-symbol-description"]={en:["Swiss Market Index"],hu_HU:["Swiss Market Index"]},e.exports["#SZSE:399001-symbol-description"]={en:["Shenzhen Component Index"],hu_HU:["SZSE Component Index"]},e.exports["#TADAWUL:TASI-symbol-description"]={en:["Tadawul All Shares Index"],hu_HU:["Tadawul All Shares Index"]},e.exports["#IDX:COMPOSITE-symbol-description"]={en:["IDX Composite Index"],hu_HU:["IDX Composite Index"]},e.exports["#EURONEXT:PX1-symbol-description"]={en:["CAC 40 Index"],hu_HU:["CAC 40 Index"]},e.exports["#OMXHEX:OMXH25-symbol-description"]={en:["OMX Helsinki 25 Index"],hu_HU:["OMX Helsinki 25 Index"]},e.exports["#EURONEXT:BEL20-symbol-description"]={en:["BEL 20 Index"],hu_HU:["BEL 20 Index"]},e.exports["#TVC:STI-symbol-description"]={en:["Straits Times Index"],hu_HU:["Straits Times Index"]},e.exports["#DFM:DFMGI-symbol-description"]={en:["DFM Index"],hu_HU:["DFM Index"]},e.exports["#TVC:KOSPI-symbol-description"]={en:["Korea Composite Stock Price Index"],hu_HU:["Korea Composite Stock Price Index"]},e.exports["#FTSEMYX:FBMKLCI-symbol-description"]={
en:["FTSE Bursa Malaysia KLCI Index"],hu_HU:["FTSE Bursa Malaysia KLCI Index"]},e.exports["#TASE:TA35-symbol-description"]={en:["TA-35 Index"],hu_HU:["TA-35 Index"]},e.exports["#OMXSTO:OMXS30-symbol-description"]={en:["OMX Stockholm 30 Index"],hu_HU:["OMX Stockholm 30 Index"]},e.exports["#OMXICE:OMXI8-symbol-description"]={en:["OMX Iceland 8 Index"],hu_HU:["OMX Iceland 8 Index"]},e.exports["#NSENG:NSE30-symbol-description"]={en:["NSE 30 Index"],hu_HU:["NSE 30 Index"]},e.exports["#BAHRAIN:BSEX-symbol-description"]={en:["Bahrain All Share Index"],hu_HU:["Bahrain All Share Index"]},e.exports["#OMXTSE:OMXTGI-symbol-description"]={en:["OMX Tallinn Gross Index"],hu_HU:["OMX Tallinn GI"]},e.exports["#OMXCOP:OMXC25-symbol-description"]={en:["OMX Copenhagen 25 Index"],hu_HU:["OMX Copenhagen 25 Index"]},e.exports["#OMXRSE:OMXRGI-symbol-description"]={en:["OMX Riga Gross Index"],hu_HU:["OMX Riga GI"]},e.exports["#BELEX:BELEX15-symbol-description"]={en:["BELEX 15 Index"],hu_HU:["BELEX 15 Index"]},e.exports["#OMXVSE:OMXVGI-symbol-description"]={en:["OMX Vilnius Gross Index"],hu_HU:["OMX Vilnius GI"]},e.exports["#EURONEXT:AEX-symbol-description"]={en:["AEX Index"],hu_HU:["AEX Index"]},e.exports["#CBOE:VIX-symbol-description"]={en:["Volatility S&P 500 Index"],hu_HU:["Volatility S&P 500 Index"]},e.exports["#NASDAQ:XAU-symbol-description"]={en:["PHLX Gold and Silver Sector Index"],hu_HU:["PHLX Gold and Silver Sector Index"]},e.exports["#DJ:DJUSCL-symbol-description"]={en:["Dow Jones U.S. Coal Index"],hu_HU:["Dow Jones U.S. Coal Index"]},e.exports["#DJ:DJCIKC-symbol-description"]={en:["Dow Jones Commodity Index Coffee"],hu_HU:["Dow Jones Commodity Index Coffee"]},e.exports["#DJ:DJCIEN-symbol-description"]={en:["Dow Jones Commodity Index Energy"],hu_HU:["Dow Jones Commodity Index Energy"]},e.exports["#NASDAQ:OSX-symbol-description"]={en:["PHLX Oil Service Sector Index"],hu_HU:["PHLX Oil Service Sector Index"]},e.exports["#DJ:DJCISB-symbol-description"]={en:["Dow Jones Commodity Index Sugar"],hu_HU:["Dow Jones Commodity Index Sugar"]},e.exports["#DJ:DJCICC-symbol-description"]={en:["Dow Jones Commodity Index Cocoa"],hu_HU:["Dow Jones Commodity Index Cocoa"]},e.exports["#DJ:DJCIGR-symbol-description"]={en:["Dow Jones Commodity Index Grains"],hu_HU:["Dow Jones Commodity Index Grains"]},e.exports["#DJ:DJCIAGC-symbol-description"]={en:["Dow Jones Commodity Index Agriculture Capped Component"],hu_HU:["Dow Jones Commodity Index Agriculture Capped Component"]},e.exports["#DJ:DJCISI-symbol-description"]={en:["Dow Jones Commodity Index Silver"],hu_HU:["Dow Jones Commodity Index Silver"]},e.exports["#DJ:DJCIIK-symbol-description"]={en:["Dow Jones Commodity Index Nickel"],hu_HU:["Dow Jones Commodity Index Nickel"]},e.exports["#NASDAQ:HGX-symbol-description"]={en:["PHLX Housing Sector Index"],hu_HU:["PHLX Housing Sector Index"]},e.exports["#DJ:DJCIGC-symbol-description"]={en:["Dow Jones Commodity Index Gold"],hu_HU:["Dow Jones Commodity Index Gold"]},e.exports["#SP:SPGSCI-symbol-description"]={en:["S&P Goldman Sachs Commodity Index"],
hu_HU:["S&P Goldman Sachs Commodity Index"]},e.exports["#NASDAQ:UTY-symbol-description"]={en:["PHLX Utility Sector Index"],hu_HU:["PHLX Utility Sector Index"]},e.exports["#DJ:DJU-symbol-description"]={en:["Dow Jones Utility Average Index"],hu_HU:["Dow Jones Utility Average Index"]},e.exports["#SP:SVX-symbol-description"]={en:["S&P 500 Value Index"],hu_HU:["S&P 500 Value Index"]},e.exports["#SP:OEX-symbol-description"]={en:["S&P 100 Index"],hu_HU:["S&P 100 Index"]},e.exports["#CBOE:OEX-symbol-description"]={en:["S&P 100 Index"],hu_HU:["S&P 100 Index"]},e.exports["#NASDAQ:SOX-symbol-description"]={en:["Philadelphia Semiconductor Index"],hu_HU:["Philadelphia Semiconductor Index"]},e.exports["#RUSSELL:RUI-symbol-description"]={en:["Russell 1000 Index"],hu_HU:["Russell 1000 Index"]},e.exports["#RUSSELL:RUA-symbol-description"]={en:["Russell 3000 Index"],hu_HU:["Russell 3000 Index"]},e.exports["#RUSSELL:RUT-symbol-description"]={en:["Russell 2000 Index"],hu_HU:["Russell 2000 Index"]},e.exports["#NYSE:XMI-symbol-description"]={en:["NYSE ARCA Major Market Index"],hu_HU:["NYSE ARCA Major Market Index"]},e.exports["#NYSE:XAX-symbol-description"]={en:["AMEX Composite Index"],hu_HU:["AMEX Composite Index"]},e.exports["#NASDAQ:NDX-symbol-description"]={en:["Nasdaq 100 Index"],hu_HU:["Nasdaq 100 Index"]},e.exports["#NASDAQ:IXIC-symbol-description"]={en:["Nasdaq Composite Index"],hu_HU:["Nasdaq Composite Index"]},e.exports["#DJ:DJT-symbol-description"]={en:["Dow Jones Transportation Average Index"],hu_HU:["Dow Jones Transportation Average Index"]},e.exports["#NYSE:NYA-symbol-description"]={en:["NYSE Composite Index"],hu_HU:["NYSE Composite Index"]},e.exports["#NYMEX:CJ1!-symbol-description"]={en:["Cocoa Futures"],hu_HU:["Cocoa Futures"]},e.exports["#USDILS-symbol-description"]={en:["U.S. Dollar / Israeli Shekel"],hu_HU:["U.S. Dollar / Israeli Shekel"]},e.exports["#TSXV:F-symbol-description"]={en:["Fiore Gold Inc"],hu_HU:["Fiore Gold Inc"]},e.exports["#SIX:F-symbol-description"]={en:["Ford Motor Company"],hu_HU:["Ford Motor Company"]},e.exports["#BMV:F-symbol-description"]={en:["Ford Motor Company"],hu_HU:["Ford Motor Company"]},e.exports["#TWII-symbol-description"]={en:["Taiwan Weighted Index"],hu_HU:["Taiwan Weighted Index"]},e.exports["#TVC:PL10Y-symbol-description"]={en:["Poland Government Bonds 10 YR Yield"],hu_HU:["Poland Government Bonds 10 YR Yield"]},e.exports["#TVC:PL05Y-symbol-description"]={en:["Poland Government Bonds 5 YR Yield"],hu_HU:["Poland Government Bonds 5 YR Yield"]},e.exports["#SET:GC-symbol-description"]={en:["Global Connections Public Company"],hu_HU:["Global Connections Public Company"]},e.exports["#TSX:GC-symbol-description"]={en:["Great Canadian Gaming Corporation"],hu_HU:["Great Canadian Gaming Corporation"]},e.exports["#TVC:FTMIB-symbol-description"]={en:["Milano Italia Borsa Index"],hu_HU:["Milano Italia Borsa Index"]},e.exports["#OANDA:SPX500USD-symbol-description"]={en:["S&P 500 Index"],hu_HU:["S&P 500 Index"]},e.exports["#BMV:CT-symbol-description"]={en:["China SX20 RT"],hu_HU:["China SX20 RT"]},
e.exports["#TSXV:CT-symbol-description"]={en:["Centenera Mining Corporation"],hu_HU:["Centenera Mining Corporation"]},e.exports["#BYBIT:ETHUSD-symbol-description"]={en:["ETHUSD Perpetual Contract"],hu_HU:["ETHUSD Perpetual Contract"]},e.exports["#BYBIT:XRPUSD-symbol-description"]={en:["XRPUSD Perpetual Contract"],hu_HU:["XRPUSD Perpetual Contract"]},e.exports["#BYBIT:BTCUSD-symbol-description"]={en:["BTCUSD Perpetual Contract"],hu_HU:["BTCUSD Perpetual Contract"]},e.exports["#BITMEX:ETHUSD-symbol-description"]={en:["ETHUSD Perpetual Futures Contract"],hu_HU:["ETHUSD Perpetual Futures Contract"]},e.exports["#DERIBIT:BTCUSD-symbol-description"]={en:["BTCUSD Perpetual Futures Contract"],hu_HU:["BTCUSD Perpetual Futures Contract"]},e.exports["#DERIBIT:ETHUSD-symbol-description"]={en:["ETHUSD Perpetual Futures Contract"],hu_HU:["ETHUSD Perpetual Futures Contract"]},e.exports["#USDHUF-symbol-description"]={en:["U.S. Dollar / Hungarian Forint"],hu_HU:["U.S. Dollar / Hungarian Forint"]},e.exports["#USDTHB-symbol-description"]={en:["U.S. Dollar / Thai Baht"],hu_HU:["U.S. Dollar / Thai Baht"]},e.exports["#FOREXCOM:US2000-symbol-description"]={en:["US Small Cap 2000"],hu_HU:["US Small Cap 2000"]},e.exports["#TSXV:PBR-symbol-description"]={en:["Para Resources Inc"],hu_HU:["Para Resources Inc"]},e.exports["#NYSE:SI-symbol-description"]={en:["Silvergate Capital Corporation"],hu_HU:["Silvergate Capital Corporation"]},e.exports["#NASDAQ:LE-symbol-description"]={en:["Lands' End Inc"],hu_HU:["Lands' End Inc"]},e.exports["#CME:CB1!-symbol-description"]={en:["Butter Futures-Cash (Continuous: Current contract in front)"],hu_HU:["Butter Futures-Cash (Continuous: Current contract in front)"]},e.exports["#LSE:SCHO-symbol-description"]={en:["Scholium Group Plc Ord 1P"],hu_HU:["Scholium Group Plc Ord 1P"]},e.exports["#NEO:HE-symbol-description"]={en:["Hanwei Energy Services Corp."],hu_HU:["Hanwei Energy Services Corp."]},e.exports["#NYSE:HE-symbol-description"]={en:["Hawaiian Electric Industries"],hu_HU:["Hawaiian Electric Industries"]},e.exports["#OMXCOP:SCHO-symbol-description"]={en:["Schouw & Co A/S"],hu_HU:["Schouw & Co A/S"]},e.exports["#TSX:HE-symbol-description"]={en:["Hanwei Energy Services Corp."],hu_HU:["Hanwei Energy Services Corp."]},e.exports["#BSE:ITI-symbol-description"]={en:["ITI Ltd"],hu_HU:["ITI Ltd"]},e.exports["#NSE:ITI-symbol-description"]={en:["Indian Telephone Industries Limited"],hu_HU:["Indian Telephone Industries Limited"]},e.exports["#TSX:LS-symbol-description"]={en:["Middlefield Healthcare & Life Sciences Dividend Fund"],hu_HU:["Middlefield Healthcare & Life Sciences Dividend Fund"]},e.exports["#BITMEX:XBT-symbol-description"]={en:["Bitcoin / U.S. Dollar Index"],hu_HU:["Bitcoin / U.S. Dollar Index"]},e.exports["#CME_MINI:RTY1!-symbol-description"]={en:["E-Mini Russell 2000 Index Futures"],hu_HU:["E-Mini Russell 2000 Index Futures"]},e.exports["#CRYPTOCAP:TOTAL-symbol-description"]={en:["Crypto Total Market Cap, $"],hu_HU:["Crypto Total Market Cap, $"]},e.exports["#ICEUS:DX1!-symbol-description"]={
en:["U.S. Dollar Index Futures"],hu_HU:["U.S. Dollar Index Futures"]},e.exports["#NYMEX:TT1!-symbol-description"]={en:["Cotton Futures"],hu_HU:["Cotton Futures"]},e.exports["#PHEMEX:BTCUSD-symbol-description"]={en:["BTC Perpetual Futures Contract"],hu_HU:["BTC Perpetual Futures Contract"]},e.exports["#PHEMEX:ETHUSD-symbol-description"]={en:["ETH Perpetual Futures Contract"],hu_HU:["ETH Perpetual Futures Contract"]},e.exports["#PHEMEX:XRPUSD-symbol-description"]={en:["XRP Perpetual Futures Contract"],hu_HU:["XRP Perpetual Futures Contract"]},e.exports["#PHEMEX:LTCUSD-symbol-description"]={en:["LTC Perpetual Futures Contract"],hu_HU:["LTC Perpetual Futures Contract"]},e.exports["#BITCOKE:BCHUSD-symbol-description"]={en:["BCH Quanto Swap"],hu_HU:["BCH Quanto Swap"]},e.exports["#BITCOKE:BTCUSD-symbol-description"]={en:["BTC Quanto Swap"],hu_HU:["BTC Quanto Swap"]},e.exports["#BITCOKE:ETHUSD-symbol-description"]={en:["ETH Quanto Swap"],hu_HU:["ETH Quanto Swap"]},e.exports["#BITCOKE:LTCUSD-symbol-description"]={en:["LTC Quanto Swap"],hu_HU:["LTC Quanto Swap"]},e.exports["#TVC:CA10-symbol-description"]={en:["Canadian Government Bonds, 10 YR"],hu_HU:["Canadian Government Bonds, 10 YR"]},e.exports["#TVC:CA10Y-symbol-description"]={en:["Canadian Government Bonds 10 YR Yield"],hu_HU:["Canadian Government Bonds 10 YR Yield"]},e.exports["#TVC:ID10Y-symbol-description"]={en:["Indonesia Government Bonds 10 YR Yield"],hu_HU:["Indonesia Government Bonds 10 YR Yield"]},e.exports["#TVC:NL10-symbol-description"]={en:["Netherlands Government Bonds, 10 YR"],hu_HU:["Netherlands Government Bonds, 10 YR"]},e.exports["#TVC:NL10Y-symbol-description"]={en:["Netherlands Government Bonds 10 YR Yield"],hu_HU:["Netherlands Government Bonds 10 YR Yield"]},e.exports["#TVC:NZ10-symbol-description"]={en:["New Zealand Government Bonds, 10 YR"],hu_HU:["New Zealand Government Bonds, 10 YR"]},e.exports["#TVC:NZ10Y-symbol-description"]={en:["New Zealand Government Bonds 10 YR Yield"],hu_HU:["New Zealand Government Bonds 10 YR Yield"]},e.exports["#SOLUSD-symbol-description"]={en:["Solana / U.S. Dollar"],hu_HU:["Solana / U.S. Dollar"]},e.exports["#LUNAUSD-symbol-description"]={en:["Luna / U.S. Dollar"],hu_HU:["Luna / U.S. Dollar"]},e.exports["#UNIUSD-symbol-description"]={en:["Uniswap / U.S. Dollar"],hu_HU:["Uniswap / U.S. Dollar"]},e.exports["#LTCBRL-symbol-description"]={en:["Litecoin / Brazilian Real"],hu_HU:["Litecoin / Brazilian Real"]},e.exports["#ETCEUR-symbol-description"]={en:["Ethereum Classic / Euro"],hu_HU:["Ethereum Classic / Euro"]},e.exports["#ETHKRW-symbol-description"]={en:["Ethereum / South Korean Won"],hu_HU:["Ethereum / South Korean Won"]},e.exports["#BTCRUB-symbol-description"]={en:["Bitcoin / Russian Ruble"],hu_HU:["Bitcoin / Russian Ruble"]},e.exports["#BTCTHB-symbol-description"]={en:["Bitcoin / Thai Baht"],hu_HU:["Bitcoin / Thai Baht"]},e.exports["#ETHTHB-symbol-description"]={en:["Ethereum / Thai Baht"],hu_HU:["Ethereum / Thai Baht"]},e.exports["#TVC:EU10YY-symbol-description"]={en:["Euro Government Bonds 10 YR Yield"],
hu_HU:["Euro Government Bonds 10 YR Yield"]},e.exports["#NASDAQ:LCID-symbol-description"]={en:["Lucid Group, Inc."],hu_HU:["Lucid Group, Inc."]},e.exports["#TADAWUL:2370-symbol-description"]={en:["Middle East Specialized Cables Co."],hu_HU:["Middle East Specialized Cables Co."]},e.exports["#NASDAQ:GOOG-symbol-description"]={en:["#NASDAQ:GOOG-symbol-description"],hu_HU:["#NASDAQ:GOOG-symbol-description"]},e.exports["#NASDAQ:GOOGL-symbol-description"]={en:["#NASDAQ:GOOGL-symbol-description"],hu_HU:["#NASDAQ:GOOGL-symbol-description"]}}}]);