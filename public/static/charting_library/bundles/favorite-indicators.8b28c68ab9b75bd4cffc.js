"use strict";(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[9790],{23390:(e,i,n)=>{n.r(i),n.d(i,{favoriteAdded:()=>o,favoriteRemoved:()=>a,favoritesSynced:()=>s,isFavorite:()=>f,saveFavorites:()=>l,toggleFavorite:()=>u});var r=n(52033),t=n(56840);const o=new r.Delegate,a=new r.Delegate,s=new r.Delegate;let c=[];function u(e){return-1===d(e)?(function(e){!f(e)&&(c.push(e),l(),o.fire(e))}(e),!0):(function(e){const i=d(e);-1!==i&&(c.splice(i,1),l(),a.fire(e))}(e),!1)}function f(e){return-1!==d(e)}function d(e){return c.indexOf(e)}function v(){var e,i;c=[];const n=Boolean(void 0===(0,t.getValue)("chart.favoriteLibraryIndicators")),r=(0,t.getJSON)("chart.favoriteLibraryIndicators",[]);if(c.push(...r),0===c.length&&n&&"undefined"!=typeof window){const n=JSON.parse(null!==(i=null===(e=window.urlParams)||void 0===e?void 0:e.favorites)&&void 0!==i?i:"{}").indicators;n&&Array.isArray(n)&&c.push(...n)}s.fire()}function l(){const e=c.slice();(0,t.setJSON)("chart.favoriteLibraryIndicators",e)}v(),t.onSync.subscribe(null,v)}}]);