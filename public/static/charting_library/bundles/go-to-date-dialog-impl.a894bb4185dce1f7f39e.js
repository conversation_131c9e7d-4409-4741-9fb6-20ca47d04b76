(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[1859],{62978:e=>{e.exports={primary:"primary-PVWoXu5j",gray:"gray-PVWoXu5j",selected:"selected-PVWoXu5j",grouped:"grouped-PVWoXu5j",pills:"pills-PVWoXu5j",active:"active-PVWoXu5j",disableActiveOnTouch:"disableActiveOnTouch-PVWoXu5j",disableActiveStateStyles:"disableActiveStateStyles-PVWoXu5j",withGrouped:"withGrouped-PVWoXu5j","quiet-primary":"quiet-primary-PVWoXu5j",green:"green-PVWoXu5j",red:"red-PVWoXu5j",secondary:"secondary-PVWoXu5j",ghost:"ghost-PVWoXu5j"}},29075:e=>{e.exports={lightButton:"lightButton-bYDQcOkp",link:"link-bYDQcOkp",ltr:"ltr-bYDQcOkp",rtl:"rtl-bYDQcOkp","typography-regular16px":"typography-regular16px-bYDQcOkp","typography-medium16px":"typography-medium16px-bYDQcOkp","typography-regular14px":"typography-regular14px-bYDQcOkp","typography-semibold14px":"typography-semibold14px-bYDQcOkp","typography-semibold16px":"typography-semibold16px-bYDQcOkp",content:"content-bYDQcOkp",visuallyHidden:"visuallyHidden-bYDQcOkp",nowrap:"nowrap-bYDQcOkp",ellipsisContainer:"ellipsisContainer-bYDQcOkp",textWrapContainer:"textWrapContainer-bYDQcOkp",textWrapWithEllipsis:"textWrapWithEllipsis-bYDQcOkp",slot:"slot-bYDQcOkp",caret:"caret-bYDQcOkp",activeCaret:"activeCaret-bYDQcOkp",xsmall:"xsmall-bYDQcOkp",withStartSlot:"withStartSlot-bYDQcOkp",withEndSlot:"withEndSlot-bYDQcOkp",noContent:"noContent-bYDQcOkp",wrap:"wrap-bYDQcOkp",small:"small-bYDQcOkp",medium:"medium-bYDQcOkp"}},3196:e=>{e.exports={"tv-circle-logo":"tv-circle-logo-PsAlMQQF","tv-circle-logo--xxxsmall":"tv-circle-logo--xxxsmall-PsAlMQQF","tv-circle-logo--xxsmall":"tv-circle-logo--xxsmall-PsAlMQQF","tv-circle-logo--xsmall":"tv-circle-logo--xsmall-PsAlMQQF","tv-circle-logo--small":"tv-circle-logo--small-PsAlMQQF","tv-circle-logo--medium":"tv-circle-logo--medium-PsAlMQQF","tv-circle-logo--large":"tv-circle-logo--large-PsAlMQQF","tv-circle-logo--xlarge":"tv-circle-logo--xlarge-PsAlMQQF","tv-circle-logo--xxlarge":"tv-circle-logo--xxlarge-PsAlMQQF","tv-circle-logo--xxxlarge":"tv-circle-logo--xxxlarge-PsAlMQQF","tv-circle-logo--visually-hidden":"tv-circle-logo--visually-hidden-PsAlMQQF"}},53330:e=>{e.exports={"tablet-normal-breakpoint":"screen and (max-width: 768px)","small-height-breakpoint":"screen and (max-height: 360px)","tablet-small-breakpoint":"screen and (max-width: 430px)"}},2908:e=>{e.exports={"tablet-small-breakpoint":"screen and (max-width: 430px)",item:"item-jFqVJoPk",hovered:"hovered-jFqVJoPk",isDisabled:"isDisabled-jFqVJoPk",isActive:"isActive-jFqVJoPk",shortcut:"shortcut-jFqVJoPk",toolbox:"toolbox-jFqVJoPk",withIcon:"withIcon-jFqVJoPk","round-icon":"round-icon-jFqVJoPk",icon:"icon-jFqVJoPk",labelRow:"labelRow-jFqVJoPk",label:"label-jFqVJoPk",showOnHover:"showOnHover-jFqVJoPk","disclosure-item-circle-logo":"disclosure-item-circle-logo-jFqVJoPk",showOnFocus:"showOnFocus-jFqVJoPk"}},456:e=>{e.exports={"link-item":"link-item-eIA09f0e"}},10399:e=>{e.exports={"arrow-icon":"arrow-icon-NIrWNOPk",dropped:"dropped-NIrWNOPk","size-xsmall":"size-xsmall-NIrWNOPk",
"size-small":"size-small-NIrWNOPk","size-medium":"size-medium-NIrWNOPk","size-large":"size-large-NIrWNOPk","size-xlarge":"size-xlarge-NIrWNOPk"}},24554:e=>{e.exports={"underline-tab":"underline-tab-cfYYXvwA","disable-focus-outline":"disable-focus-outline-cfYYXvwA","enable-cursor-pointer":"enable-cursor-pointer-cfYYXvwA",selected:"selected-cfYYXvwA","disable-active-state-styles":"disable-active-state-styles-cfYYXvwA","size-xsmall":"size-xsmall-cfYYXvwA","size-small":"size-small-cfYYXvwA","size-medium":"size-medium-cfYYXvwA","size-large":"size-large-cfYYXvwA","size-xlarge":"size-xlarge-cfYYXvwA",fake:"fake-cfYYXvwA","margin-xsmall":"margin-xsmall-cfYYXvwA","margin-small":"margin-small-cfYYXvwA","margin-medium":"margin-medium-cfYYXvwA","margin-large":"margin-large-cfYYXvwA","margin-xlarge":"margin-xlarge-cfYYXvwA",collapse:"collapse-cfYYXvwA","ellipsis-children":"ellipsis-children-cfYYXvwA"}},7633:e=>{e.exports={"scroll-wrap":"scroll-wrap-SmxgjhBJ","size-xlarge":"size-xlarge-SmxgjhBJ","enable-scroll":"enable-scroll-SmxgjhBJ","underline-tabs":"underline-tabs-SmxgjhBJ","size-large":"size-large-SmxgjhBJ","size-medium":"size-medium-SmxgjhBJ","size-small":"size-small-SmxgjhBJ","size-xsmall":"size-xsmall-SmxgjhBJ","make-grid-column":"make-grid-column-SmxgjhBJ","stretch-tabs":"stretch-tabs-SmxgjhBJ","equal-tab-size":"equal-tab-size-SmxgjhBJ"}},29662:e=>{e.exports={underline:"underline-Pun8HxCz",center:"center-Pun8HxCz",corner:"corner-Pun8HxCz",disabled:"disabled-Pun8HxCz"}},27011:(e,t,n)=>{"use strict";function a(e,t){return t||null==e||("string"==typeof e||Array.isArray(e))&&0===e.length}n.d(t,{isIconOnly:()=>a})},14543:(e,t,n)=>{"use strict";n.d(t,{LightButton:()=>a.LightButton});n(9038);var a=n(15893);n(50959),n(21593),n(66860),n(29075),n(62978);n(78572)},9038:(e,t,n)=>{"use strict";n.d(t,{useLightButtonClasses:()=>c});var a=n(50959),l=n(97754),o=n(17946),r=n(27011),i=n(86332);const s=a.createContext({isInButtonGroup:!1,isGroupPrimary:!1}),c=(e,t,n)=>{const c=(0,a.useContext)(o.CustomBehaviourContext),{className:u,isSelected:d,children:m,showCaret:f,forceDirection:p,iconOnly:v,color:b="gray",variant:g="primary",size:h="medium",enableActiveStateStyles:C=c.enableActiveStateStyles,typography:x,isLink:w=!1,textWrap:y,isPills:k,isActive:S,startSlot:D,endSlot:E}=t,A=e[`typography-${((e,t,n)=>{if(n){const e=n.replace(/^\D+/g,"");return t?`semibold${e}`:n}switch(e){case"xsmall":return t?"semibold14px":"regular14px";case"small":case"medium":return t?"semibold16px":"regular16px";default:return""}})(h,d||k,x||void 0)}`],O=(0,a.useContext)(i.ControlGroupContext),{isInButtonGroup:R,isGroupPrimary:T}=(0,a.useContext)(s);return l(u,e.lightButton,w&&e.link,S&&e.active,d&&e.selected,(0,r.isIconOnly)(m,v)&&e.noContent,!!D&&e.withStartSlot,(f||!!E)&&e.withEndSlot,n&&e.withGrouped,p&&e[p],e[T?"primary":g],e[T?"gray":b],e[h],A,!C&&e.disableActiveStateStyles,O.isGrouped&&e.grouped,y&&e.wrap,R&&e.disableActiveOnTouch,k&&e.pills)}},66860:(e,t,n)=>{"use strict";n.d(t,{LightButtonContent:()=>d})
;var a=n(50959),l=n(97754),o=n(34094),r=n(27011),i=n(9745),s=n(2948),c=n(29075),u=n.n(c);function d(e){const{showCaret:t,iconOnly:n,ellipsis:c=!0,textWrap:d,tooltipText:m,children:f,endSlot:p,startSlot:v,isActiveCaret:b}=e;[p,t].filter((e=>!!e));return a.createElement(a.Fragment,null,v&&a.createElement("span",{className:l(u().slot,u().startSlot)},v),!(0,r.isIconOnly)(f,n)&&a.createElement("span",{className:l(u().content,!d&&u().nowrap,"apply-overflow-tooltip","apply-overflow-tooltip--check-children-recursively","apply-overflow-tooltip--allow-text"),"data-overflow-tooltip-text":null!=m?m:(0,o.getTextForTooltip)(f)},d||c?a.createElement(a.Fragment,null,a.createElement("span",{className:l(!d&&c&&u().ellipsisContainer,d&&u().textWrapContainer,d&&c&&u().textWrapWithEllipsis)},f),a.createElement("span",{className:u().visuallyHidden,"aria-hidden":!0},f)):a.createElement(a.Fragment,null,f,a.createElement("span",{className:u().visuallyHidden,"aria-hidden":!0},f))),p&&a.createElement("span",{className:l(u().slot,u().endSlot)},p),t&&(e=>a.createElement(i.Icon,{className:l(u().caret,e&&u().activeCaret),icon:s}))(b))}},15893:(e,t,n)=>{"use strict";n.d(t,{LightButton:()=>d});var a=n(50959),l=n(86332),o=n(9038),r=n(66860),i=n(29075),s=n.n(i),c=n(62978),u=n.n(c);function d(e){const{isGrouped:t}=a.useContext(l.ControlGroupContext),{reference:n,className:i,isSelected:c,children:d,iconOnly:m,ellipsis:f,showCaret:p,forceDirection:v,endSlot:b,startSlot:g,color:h,variant:C,size:x,enableActiveStateStyles:w,typography:y,textWrap:k=!1,maxLines:S,style:D={},isPills:E,isActive:A,tooltipText:O,role:R,...T}=e,I=k?null!=S?S:2:1,N=I>0?{...D,"--ui-lib-light-button-content-max-lines":I}:D;return a.createElement("button",{...T,className:(0,o.useLightButtonClasses)({...u(),...s()},{className:i,isSelected:c,children:d,iconOnly:m,showCaret:p,forceDirection:v,endSlot:b,startSlot:g,color:h,variant:C,size:x,enableActiveStateStyles:w,typography:y,textWrap:k,isPills:E,isActive:A},t),ref:n,style:N,role:R},a.createElement(r.LightButtonContent,{showCaret:p,isActiveCaret:p&&(E||A||c),iconOnly:m,ellipsis:f,textWrap:k,tooltipText:O,endSlot:b,startSlot:g},d))}},53885:(e,t,n)=>{"use strict";n.d(t,{getStyleClasses:()=>r,isCircleLogoWithUrlProps:()=>i});var a=n(97754),l=n(3196),o=n.n(l);function r(e,t){return a(o()["tv-circle-logo"],o()[`tv-circle-logo--${e}`],t)}function i(e){return"logoUrl"in e&&null!==e.logoUrl&&void 0!==e.logoUrl&&0!==e.logoUrl.length}},17946:(e,t,n)=>{"use strict";n.d(t,{CustomBehaviourContext:()=>a});const a=(0,n(50959).createContext)({enableActiveStateStyles:!0});a.displayName="CustomBehaviourContext"},95854:(e,t,n)=>{"use strict";var a;n.d(t,{useCollapsible:()=>m}),function(e){e.StartFirst="start-first",e.EndFirst="end-first"}(a||(a={}));var l=n(50959),o=n(67842),r=n(56073),i=n(78869),s=n(43010),c=n(53017);function u(e){const{itemsList:t,getItemId:n,calcVisibleAndHiddenItems:a,shouldKeepItemVisible:u,onMeasureCallback:m,forceUpdate:f=!1}=e,[p,v]=(0,i.useRefsMap)(),b=(0,l.useRef)(null),g=(0,l.useRef)({widthsMap:new Map,containerWidth:0,
moreButtonWidth:0}),[h,C]=(0,l.useState)({visible:t,hidden:[]}),x=(0,l.useMemo)((()=>t.reduce(((e,t,n)=>(u(t)&&e.push(n),e)),[])),[t,u]),w=(0,l.useCallback)((()=>{if(g.current.containerWidth){const e=a(g.current,x);(function(e,t){return!d(e.visible,t.visible)||!d(e.hidden,t.hidden)})(h,e)&&C(e)}}),[g,C,h,x,a]),y=(0,l.useCallback)((()=>{g.current.moreButtonWidth=b.current?(0,r.outerWidth)(b.current,!0):0;const e=new Map(g.current.widthsMap);for(const a of t){const t=n(a),l=p.current.get(t);if(l){const n=(0,r.outerWidth)(l,!0);e.set(t,n)}}g.current.widthsMap=e,m&&m()}),[g,t,n,p,m]),k=(0,l.useRef)(null),S=(0,l.useCallback)((([e])=>{e.contentRect.width!==g.current.containerWidth&&(k.current&&cancelAnimationFrame(k.current),g.current.containerWidth=e.contentRect.width,k.current=requestAnimationFrame((()=>{w()})))}),[g,w]),D=(0,l.useRef)(null),E=(0,l.useCallback)((([e])=>{D.current&&cancelAnimationFrame(D.current),y(),D.current=requestAnimationFrame((()=>{w()}))}),[y,w]),A=(0,o.useResizeObserver)(E),O=(0,o.useResizeObserver)(S),R=(0,l.useRef)(null),T=(0,c.mergeRefs)([O,R]),I=(0,l.useRef)(t),N=(0,l.useRef)(!0),P=(0,l.useRef)([]);return(0,s.useIsomorphicLayoutEffect)((()=>{!f&&!N.current&&d(I.current,t)&&d(x,P.current)||(w(),N.current=!1,I.current=t,P.current=x)}),[t,w,x,f]),{containerRefCallback:T,moreButtonRef:b,innerContainerRefCallback:A,itemsRefs:p,setItemRef:v,hiddenItems:h.hidden,visibleItems:h.visible,itemsMeasurements:g}}function d(e,t){return e.length===t.length&&e.reduce(((e,n,a)=>e&&n===t[a]),!0)}function m(e,t,n,o=a.EndFirst){const r=(0,l.useCallback)(((n,l)=>{const r=e.map((e=>{var a;return null!==(a=n.widthsMap.get(t(e)))&&void 0!==a?a:0}));return function({items:e,containerWidth:t,elementsWidths:n,menuItemWidth:l,keepVisible:o,direction:r}){const i=[...e],s=[],c=[];let u=0;for(const e of n)u+=e;if(u<=t)return{visible:i,hidden:c};const d=[...n];if(u=o.map((e=>d[e])).reduce(((e,t)=>e+t),0)+l,r===a.EndFirst)for(let e=0;e<i.length;e++)o.includes(e)?s.push(i[e]):(u+=d[e],u<=t?s.push(i[e]):c.push(i[e]));else for(let e=i.length-1;e>=0;e--)o.includes(e)?s.unshift(i[e]):(u+=d[e],u<=t?s.unshift(i[e]):c.unshift(i[e]));return{visible:s,hidden:c}}({items:e,containerWidth:n.containerWidth,elementsWidths:r,menuItemWidth:n.moreButtonWidth,keepVisible:l,direction:o})}),[e]);return u({itemsList:e,getItemId:t,calcVisibleAndHiddenItems:r,shouldKeepItemVisible:n})}},39416:(e,t,n)=>{"use strict";n.d(t,{useFunctionalRefObject:()=>o});var a=n(50959),l=n(43010);function o(e){const t=(0,a.useMemo)((()=>function(e){const t=n=>{e(n),t.current=n};return t.current=null,t}((e=>{i.current(e)}))),[]),n=(0,a.useRef)(null),o=t=>{if(null===t)return r(n.current,t),void(n.current=null);n.current!==e&&(n.current=e,r(n.current,t))},i=(0,a.useRef)(o);return i.current=o,(0,l.useIsomorphicLayoutEffect)((()=>{if(null!==t.current)return i.current(t.current),()=>i.current(null)}),[e]),t}function r(e,t){null!==e&&("function"==typeof e?e(t):e.current=t)}},86781:(e,t,n)=>{"use strict";n.d(t,{useMatchMedia:()=>o,useSafeMatchMedia:()=>l});var a=n(50959)
;function l(e,t=!1){const[n,l]=(0,a.useState)(t);return(0,a.useEffect)((()=>{const t=window.matchMedia(e);function n(){l(t.matches)}return n(),t.addEventListener("change",n),()=>{t.removeEventListener("change",n)}}),[e]),n}function o(e){const t=(0,a.useMemo)((()=>window.matchMedia(e).matches),[]);return l(e,t)}},78869:(e,t,n)=>{"use strict";n.d(t,{useRefsMap:()=>l});var a=n(50959);function l(){const e=(0,a.useRef)(new Map),t=(0,a.useCallback)((t=>n=>{null!==n?e.current.set(t,n):e.current.delete(t)}),[e]);return[e,t]}},67842:(e,t,n)=>{"use strict";n.d(t,{useResizeObserver:()=>r});var a=n(50959),l=n(43010),o=n(39416);function r(e,t=[]){const{callback:n,ref:r=null}=function(e){return"function"==typeof e?{callback:e}:e}(e),i=(0,a.useRef)(null),s=(0,a.useRef)(n);s.current=n;const c=(0,o.useFunctionalRefObject)(r),u=(0,a.useCallback)((e=>{c(e),null!==i.current&&(i.current.disconnect(),null!==e&&i.current.observe(e))}),[c,i]);return(0,l.useIsomorphicLayoutEffect)((()=>(i.current=new ResizeObserver(((e,t)=>{s.current(e,t)})),c.current&&u(c.current),()=>{var e;null===(e=i.current)||void 0===e||e.disconnect()})),[c,...t]),u}},16212:(e,t,n)=>{"use strict";n.d(t,{useTabs:()=>p});var a=n(50959),l=n(37507),o=n(47201),r=n(29202),i=n(16921),s=n(50151),c=n(66686),u=n(36762);function d(){return!1}function m(e){const{activationType:t="manual"}=e,n=(0,a.useMemo)((()=>t),[]);return(0,s.assert)(t===n,"Activation type must be invariant."),"automatic"===t?function(e){const{isRtl:t,items:n,preventDefaultIfHandled:l=!0,isHighlighted:o,onHighlight:r,onActivate:i,isCollapsed:s=d,orientation:m}=e,f=(0,a.useCallback)((e=>{r(e),s(e)||i(e)}),[r,i,s]);return(0,c.useKeyboardEventHandler)([(0,u.useItemsKeyboardNavigation)(m,t,n,o,f,!0)],l)}(e):function(e){const{isRtl:t,items:n,preventDefaultIfHandled:l=!0,isHighlighted:o,onHighlight:r,onActivate:i,orientation:s}=e,d=n.find(o),m=(0,a.useCallback)((()=>{void 0!==d&&i(d)}),[d,i]),f=(0,a.useCallback)((e=>r(e)),[r]),p=(0,u.useItemsKeyboardNavigation)(s,t,n,o,f,!0),v=(0,c.useKeyboardActionHandler)([13,32],m);return(0,c.useKeyboardEventHandler)([p,v],l)}(e)}var f=n(5325);function p(e){var t,n,s;const{id:c,items:u,orientation:d,activationType:p="manual",disabled:v,tablistLabelId:b,tablistLabel:g,focusOnHighlight:h=!0,preventDefaultIfKeyboardActionHandled:C=!0,scrollIntoViewOptions:x,isActive:w,onActivate:y,isCollapsed:k,isRtl:S,isDisclosureOpened:D}=e,E=function(){const[e,t]=(0,a.useState)(!1);return(0,a.useEffect)((()=>{t(f.mobiletouch)}),[]),e}(),A=D?null:d||"horizontal",O=(0,a.useRef)(null!==(n=null===(t=e.itemsRefs)||void 0===t?void 0:t.current)&&void 0!==n?n:new Map),[R,T]=(0,a.useState)(),[I,N]=(0,r.useFocus)(),P=u.find(w),M=(0,a.useCallback)((e=>!v&&!e.disabled&&e===R),[v,R]),F=(0,a.useCallback)((e=>{const t=O.current.get(e);h&&void 0!==t&&t!==document.activeElement&&t.focus()}),[h]),B=(0,a.useRef)(),z=(0,a.useCallback)(((e,t)=>{v||e.disabled||(T(e),"number"==typeof t?(clearTimeout(B.current),B.current=setTimeout((()=>F(e)),t)):F(e))}),[v,T,F]),W=(0,a.useCallback)((e=>{v||e.disabled||(y(e),M(e)||z(e))
}),[v,y,M,z]),Y=m({isRtl:S,items:(0,a.useMemo)((()=>u.filter((e=>!v&&!e.disabled))),[u,v]),activationType:p,preventDefaultIfHandled:C,onActivate:W,isHighlighted:M,onHighlight:z,isCollapsed:k,orientation:A}),L=(0,a.useCallback)((e=>{let t=null;for(const[n,a]of O.current.entries())if(e.target===a){t=n;break}t&&!M(t)&&("automatic"===p&&k&&!k(t)?W(t):z(t))}),[p,M,z,W,k]);(0,a.useEffect)((()=>{E||void 0!==P&&T(P)}),[P,E]),(0,a.useEffect)((()=>{I||T(void 0)}),[I]),(0,a.useEffect)((()=>()=>clearTimeout(B.current)),[]);const V=null!==(s=null==x?void 0:x.additionalScroll)&&void 0!==s?s:0,[H,j]=(0,i.useKeepActiveItemIntoView)({...x,visibilityDetectionOffsetInline:V+24,snapToMiddle:!0,activeItem:null!=R?R:P,getKey:(0,a.useCallback)((e=>e.id),[])}),Q=(0,a.useCallback)(((e,t)=>{j(e,t),null!==t?O.current.set(e,t):O.current.delete(e)}),[j]),X=u.map((e=>{var t,n;const a=M(e),o=w(e),r=null!==(n=null!==(t=e.disabled)&&void 0!==t?t:v)&&void 0!==n&&n,i=!r&&(I?a:o);return{...(0,l.getTabAttributes)(e.id,i,o,e.tabpanelId,r),highlighted:a,active:o,handleItemRef:Q}}));return{tabsBindings:X,tablistBinding:{...(0,l.getTabListAttributes)((0,l.getTablistId)(c),d,v,b,g),onBlur:N.onBlur,onFocus:(0,o.createSafeMulticastEventHandler)(N.onFocus,L),onKeyDown:Y},scrollWrapBinding:{ref:H},onActivate:W,onHighlight:z,isHighlighted:M}}},37507:(e,t,n)=>{"use strict";n.d(t,{getTabAttributes:()=>u,getTabListAttributes:()=>c,getTablistId:()=>s});var a,l,o,r,i=n(22064);function s(e){return(0,i.createDomId)(e,"tablist")}function c(e,t="horizontal",n,a,l){return{id:e,role:"tablist","aria-orientation":t,"aria-label":l,"aria-labelledby":a,"aria-disabled":n}}function u(e,t,n,a,l){return{id:e,role:"tab",tabIndex:t?0:-1,disabled:l,"aria-selected":n,"aria-controls":a,"aria-disabled":l}}!function(e){e.Horizontal="horizontal",e.Vertical="vertical"}(a||(a={})),function(e){e.Automatic="automatic",e.Manual="manual"}(l||(l={})),function(e){e.Collapse="collapse",e.Scroll="scroll",e.Wrap="wrap",e.None="none"}(o||(o={})),function(e){e.SquareButtonTabs="square-button-tabs",e.UnderlineButtonTabs="underline-button-tabs",e.UnderlineAnchorTabs="underline-anchor-tabs",e.RoundAnchorTabs="round-anchor-tabs",e.RoundButtonTabs="round-button-tabs",e.LightButtonTabs="light-button-tabs"}(r||(r={}))},56073:(e,t,n)=>{"use strict";function a(e,t=!1){const n=getComputedStyle(e),a=[n.height];return"border-box"!==n.boxSizing&&a.push(n.paddingTop,n.paddingBottom,n.borderTopWidth,n.borderBottomWidth),t&&a.push(n.marginTop,n.marginBottom),a.reduce(((e,t)=>e+(parseFloat(t)||0)),0)}function l(e,t=!1){const n=getComputedStyle(e),a=[n.width];return"border-box"!==n.boxSizing&&a.push(n.paddingLeft,n.paddingRight,n.borderLeftWidth,n.borderRightWidth),t&&a.push(n.marginLeft,n.marginRight),a.reduce(((e,t)=>e+(parseFloat(t)||0)),0)}n.d(t,{outerHeight:()=>a,outerWidth:()=>l})},34094:(e,t,n)=>{"use strict";n.d(t,{getTextForTooltip:()=>r});var a=n(50959);const l=e=>(0,
a.isValidElement)(e)&&Boolean(e.props.children),o=e=>null==e||"boolean"==typeof e||"{}"===JSON.stringify(e)?"":e.toString()+" ",r=e=>Array.isArray(e)||(0,a.isValidElement)(e)?a.Children.toArray(e).reduce(((e,t)=>{let n="";return n=(0,a.isValidElement)(t)&&l(t)?r(t.props.children):(0,a.isValidElement)(t)&&!l(t)?"":o(t),e.concat(n)}),"").trim():o(e)},47201:(e,t,n)=>{"use strict";function a(...e){return t=>{for(const n of e)void 0!==n&&n(t)}}n.d(t,{createSafeMulticastEventHandler:()=>a})},24437:(e,t,n)=>{"use strict";n.d(t,{DialogBreakpoints:()=>l});var a=n(53330);const l={SmallHeight:a["small-height-breakpoint"],TabletSmall:a["tablet-small-breakpoint"],TabletNormal:a["tablet-normal-breakpoint"]}},59695:(e,t,n)=>{"use strict";n.d(t,{CircleLogo:()=>i,hiddenCircleLogoClass:()=>r});var a=n(50959),l=n(53885),o=n(3196);const r=n.n(o)()["tv-circle-logo--visually-hidden"];function i(e){var t,n;const o=(0,l.getStyleClasses)(e.size,e.className),r=null!==(n=null!==(t=e.alt)&&void 0!==t?t:e.title)&&void 0!==n?n:"";return(0,l.isCircleLogoWithUrlProps)(e)?a.createElement("img",{className:o,crossOrigin:"",src:e.logoUrl,alt:r,title:e.title,loading:e.loading,"aria-label":e["aria-label"],"aria-hidden":e["aria-hidden"]}):a.createElement("span",{className:o,title:e.title,"aria-label":e["aria-label"],"aria-hidden":e["aria-hidden"]},e.placeholderLetter)}},73775:(e,t,n)=>{"use strict";n.d(t,{useDisclosure:()=>a.useDisclosure});var a=n(7953)},42707:(e,t,n)=>{"use strict";n.d(t,{useSafeMatchMedia:()=>a.useSafeMatchMedia});var a=n(86781)},16396:(e,t,n)=>{"use strict";n.d(t,{DEFAULT_POPUP_MENU_ITEM_THEME:()=>u,PopupMenuItem:()=>m});var a=n(50959),l=n(97754),o=n(51768),r=n(59064),i=n(59695),s=n(76460),c=n(2908);const u=c;function d(e){e.stopPropagation()}function m(e){const{id:t,role:n,className:u,title:m,labelRowClassName:f,labelClassName:p,toolboxClassName:v,shortcut:b,forceShowShortcuts:g,icon:h,iconClassname:C,isActive:x,isDisabled:w,isHovered:y,appearAsDisabled:k,label:S,link:D,showToolboxOnHover:E,showToolboxOnFocus:A,target:O,rel:R,toolbox:T,reference:I,onMouseOut:N,onMouseOver:P,onKeyDown:M,suppressToolboxClick:F=!0,theme:B=c,tabIndex:z,tagName:W,renderComponent:Y,roundedIcon:L,iconAriaProps:V,circleLogo:H,dontClosePopup:j,onClick:Q,onClickArg:X,trackEventObject:U,trackMouseWheelClick:G,trackRightClick:_,...q}=e,J=(0,a.useRef)(null),$=(0,a.useMemo)((()=>function(e){function t(t){const{reference:n,...l}=t,o=null!=e?e:l.href?"a":"div",r="a"===o?l:function(e){const{download:t,href:n,hrefLang:a,media:l,ping:o,rel:r,target:i,type:s,referrerPolicy:c,...u}=e;return u}(l);return a.createElement(o,{...r,ref:n})}return t.displayName=`DefaultComponent(${e})`,t}(W)),[W]),K=null!=Y?Y:$;return a.createElement(K,{...q,id:t,role:n,className:l(u,B.item,h&&B.withIcon,{[B.isActive]:x,[B.isDisabled]:w||k,[B.hovered]:y}),title:m,href:D,target:O,rel:R,reference:function(e){J.current=e,"function"==typeof I&&I(e);"object"==typeof I&&(I.current=e)},onClick:function(e){if(w)return;U&&(0,o.trackEvent)(U.category,U.event,U.label);Q&&Q(X,e)
;j||(e.currentTarget.dispatchEvent(new CustomEvent("popup-menu-close-event",{bubbles:!0,detail:{clickType:(0,s.isKeyboardClick)(e)?"keyboard":"mouse"}})),(0,r.globalCloseMenu)())},onContextMenu:function(e){U&&_&&(0,o.trackEvent)(U.category,U.event,`${U.label}_rightClick`)},onMouseUp:function(e){if(1===e.button&&D&&U){let e=U.label;G&&(e+="_mouseWheelClick"),(0,o.trackEvent)(U.category,U.event,e)}},onMouseOver:P,onMouseOut:N,onKeyDown:M,tabIndex:z},H&&a.createElement(i.CircleLogo,{...V,className:c["disclosure-item-circle-logo"],size:"xxxsmall",logoUrl:H.logoUrl,placeholderLetter:"placeholderLetter"in H?H.placeholderLetter:void 0}),h&&a.createElement("span",{"aria-label":V&&V["aria-label"],"aria-hidden":V&&Boolean(V["aria-hidden"]),className:l(B.icon,L&&c["round-icon"],C),dangerouslySetInnerHTML:{__html:h}}),a.createElement("span",{className:l(B.labelRow,f)},a.createElement("span",{className:l(B.label,p)},S)),(void 0!==b||g)&&a.createElement("span",{className:B.shortcut},(Z=b)&&Z.split("+").join(" + ")),void 0!==T&&a.createElement("span",{onClick:F?d:void 0,className:l(v,B.toolbox,{[B.showOnHover]:E,[B.showOnFocus]:A})},T));var Z}},93081:(e,t,n)=>{"use strict";n.d(t,{UnderlineButtonTabs:()=>U});var a,l=n(50959),o=n(97754),r=n.n(o),i=n(11542),s=n(95854),c=n(38528),u=n(47201),d=n(73775),m=n(16212),f=n(26597);!function(e){e.XSmall="xsmall",e.Small="small",e.Medium="medium",e.Large="large",e.XLarge="xlarge"}(a||(a={}));const p=(0,l.createContext)({size:"small",overflowBehaviour:void 0});var v=n(17946),b=n(24554);function g(e){const{size:t="xsmall",active:n,fake:a,enableActiveStateStyles:l,anchor:r=!1,hideFocusOutline:i=!1,equalTabSize:s,className:c,overflowBehaviour:u}=e;return o(b["underline-tab"],b[`size-${t}`],n&&b.selected,!l&&b["disable-active-state-styles"],i&&b["disable-focus-outline"],a&&b.fake,r&&b["enable-cursor-pointer"],s&&b[`margin-${t}`],"collapse"===u&&b.collapse,c)}const h=(0,l.forwardRef)(((e,t)=>{const{size:n,overflowBehaviour:a}=(0,l.useContext)(p),o=(0,l.useContext)(v.CustomBehaviourContext),{active:i,fake:s,className:c,enableActiveStateStyles:u=o.enableActiveStateStyles,hideFocusOutline:d=!1,equalTabSize:m,children:f,...h}=e;return l.createElement("button",{...h,ref:t,className:g({size:n,active:i,fake:s,enableActiveStateStyles:u,hideFocusOutline:d,equalTabSize:m,className:c,overflowBehaviour:a})},m&&"string"==typeof f?l.createElement("span",{className:r()(b["ellipsis-children"],"apply-overflow-tooltip")},f):f)}));h.displayName="UnderlineTabsBaseButton";const C=(0,l.forwardRef)(((e,t)=>{const{item:n,highlighted:a,handleItemRef:o,onClick:r,"aria-disabled":i,...s}=e,c=(0,l.useCallback)((()=>{r&&r(n)}),[r,n]),u=(0,l.useCallback)((e=>{o&&o(n,e),t&&"object"==typeof t?t.current=e:"function"==typeof t&&t(e)}),[n,o,t]);return l.createElement(h,{...s,id:n.id,onClick:c,ref:u},n.label)}));C.displayName="UnderlineButtonTab";var x=n(50151),w=n(16396),y=n(4523),k=n(9745),S=n(47531),D=n(2948),E=n(63509),A=n(68874),O=n(10399);function R(e){switch(e){case"xsmall":return S;case"small":return D;case"medium":case"large":
return E;case"xlarge":return A}}function T(e){const{size:t,isDropped:n=!1}=e;return l.createElement(k.Icon,{icon:R(t),className:o(O["arrow-icon"],O[`size-${t}`],n&&O.dropped)})}var I=n(456);function N(e){const{size:t,disabled:n,isOpened:a,enableActiveStateStyles:o,hideFocusOutline:r,fake:i,items:s,buttonContent:u,buttonRef:d,isAnchorTabs:m,isHighlighted:f,onButtonClick:p,onItemClick:v,onClose:b}=e,g=(0,l.useRef)(null),C=(0,c.useMergedRefs)([d,g]),k=function(e,t){const n=(0,l.useRef)(M);return(0,l.useEffect)((()=>{const e=getComputedStyle((0,x.ensureNotNull)(t.current));n.current={xsmall:P(e,"xsmall"),small:P(e,"small"),medium:P(e,"medium"),large:P(e,"large"),xlarge:P(e,"xlarge")}}),[t]),(0,l.useCallback)((()=>{const a=(0,x.ensureNotNull)(t.current).getBoundingClientRect(),l=n.current[e];return{x:a.left,y:a.top+a.height+l+4,indentFromWindow:{top:4,bottom:4,left:4,right:4}}}),[t,e])}(t,g);return l.createElement(y.PopupMenuDisclosureView,{buttonRef:g,listboxTabIndex:-1,isOpened:a,onClose:b,listboxAria:{"aria-hidden":!0},popupPosition:k,button:l.createElement(h,{"aria-hidden":!0,disabled:n,active:a,onClick:p,ref:C,tabIndex:-1,enableActiveStateStyles:o,hideFocusOutline:r,fake:i},u,l.createElement(T,{size:t,isDropped:a})),popupChildren:s.map((e=>l.createElement(w.PopupMenuItem,{key:e.id,className:m?I["link-item"]:void 0,onClick:v,onClickArg:e,isActive:f(e),label:e.label,isDisabled:e.disabled,link:"href"in e?e.href:void 0,rel:"rel"in e?e.rel:void 0,target:"target"in e?e.target:void 0,renderComponent:"renderComponent"in e?e.renderComponent:void 0,dontClosePopup:!0})))})}function P(e,t){return parseInt(e.getPropertyValue(`--ui-lib-underline-tabs-tab-margin-bottom-${t}`),10)}const M={xsmall:0,small:0,medium:0,large:0,xlarge:0};var F=n(5325),B=n(42707),z=n(86240),W=n(7633);function Y(e){const{size:t,overflowBehaviour:n,className:a}=e;return o(W["scroll-wrap"],W[`size-${t}`],"scroll"===n&&W["enable-scroll"],a)}function L(){const[e,t]=(0,l.useState)(!1);return(0,l.useEffect)((()=>{t(F.mobiletouch)}),[]),e}var V=n(12481),H=n(63273),j=n(29662),Q=n.n(j);function X(e){const{disabled:t,translateX:n,transitionDuration:a}=e,r=e.scale/100;return l.createElement("div",{className:o(Q().underline,t&&Q().disabled),style:{transform:`translateX(${n}px) scaleX(${r})`,transitionDuration:`${a}ms`}},l.createElement("div",{className:Q().corner,style:{transform:`scaleX(${1/r})`}}),l.createElement("div",{className:Q().center,style:{transform:`scaleX(${1-30/e.scale})`}}),l.createElement("div",{className:Q().corner,style:{transform:`scaleX(${1/r})`}}))}function U(e){const{id:t,items:a,activationType:o,orientation:v,disabled:b,moreButtonContent:g=i.t(null,void 0,n(37117)),size:h="small",onActivate:x,isActive:w,className:y,style:k,overflowBehaviour:S,enableActiveStateStyles:D,tablistLabelId:E,tablistLabel:A,"data-name":O="underline-tabs-buttons",stretchTabs:R,equalTabSize:T}=e,I=L(),P=function(e){const t=(0,B.useSafeMatchMedia)(z["media-mf-phone-landscape"],!0),n=L();return null!=e?e:n||!t?"scroll":"collapse"}(S),M=(0,l.useRef)(!1),F=(0,
l.useCallback)((e=>e.id),[]),j="none"===P&&R,Q="none"===P&&T,U=null!=D?D:!I,{visibleItems:G,hiddenItems:_,containerRefCallback:q,innerContainerRefCallback:J,moreButtonRef:$,setItemRef:K}=(0,s.useCollapsible)(a,F,w),Z="collapse"===P?G:a,ee="collapse"===P?_:[],te=(0,l.useCallback)((e=>ee.includes(e)),[ee]),ne=(0,l.useRef)(new Map),{isOpened:ae,open:le,close:oe,onButtonClick:re}=(0,d.useDisclosure)({id:t,disabled:b}),ie=function(e="xsmall"){switch(e){case"xsmall":case"small":return 12;case"medium":return 16;case"large":case"xlarge":return 20}}(h),{tabsBindings:se,tablistBinding:ce,scrollWrapBinding:ue,onActivate:de,onHighlight:me,isHighlighted:fe}=(0,m.useTabs)({id:t,items:[...Z,...ee],activationType:o,orientation:v,disabled:b,tablistLabelId:E,tablistLabel:A,onActivate:x,isActive:w,isCollapsed:te,isRtl:H.isRtl,itemsRefs:ne,isDisclosureOpened:ae,scrollIntoViewOptions:{additionalScroll:ie}}),pe=a.find(w),ve=ee.find(fe),be=(0,l.useCallback)((()=>{pe&&me(pe)}),[me,pe]),ge=(0,l.useCallback)((e=>{var t;return null!==(t=se.find((t=>t.id===e.id)))&&void 0!==t?t:{}}),[se]),he=(0,l.useCallback)((()=>{oe(),be(),M.current=!0}),[oe,be]),Ce=(0,l.useCallback)((()=>{ve&&(de(ve),me(ve,200))}),[de,me,ve]);ue.ref=(0,c.useMergedRefs)([ue.ref,q]),ce.ref=(0,c.useMergedRefs)([ce.ref,J]),ce.onKeyDown=(0,u.createSafeMulticastEventHandler)((0,f.useKeyboardEventHandler)([(0,f.useKeyboardClose)(ae,he),(0,f.useKeyboardActionHandler)([13,32],Ce,(0,l.useCallback)((()=>Boolean(ve)),[ve]))]),ce.onKeyDown);const xe=(0,l.useCallback)((e=>{M.current=!0,re(e)}),[M,re]),we=(0,l.useCallback)((e=>{e&&de(e)}),[de]);(0,l.useEffect)((()=>{M.current?M.current=!1:(ve&&!ae&&le(),!ve&&ae&&oe())}),[ve,ae,le,oe]);const ye=function(e,t,n=[]){const[a,o]=(0,l.useState)(),r=(0,l.useRef)(),i=(0,l.useRef)(),s=e=>{var t;const n=null!==(t=e.parentElement)&&void 0!==t?t:void 0;if(void 0===n)return;const a=void 0===i.current||i.current===e?0:100;i.current=e;const{left:l,right:r,width:s}=e.getBoundingClientRect(),{left:c,right:u}=n.getBoundingClientRect(),d=(0,H.isRtl)()?r-u:l-c;o({translateX:d,scale:s,transitionDuration:a})};return(0,l.useEffect)((()=>{const e=(0,V.default)((e=>{const t=e[0].target;void 0!==t&&s(t)}),50);r.current=new ResizeObserver(e)}),[]),(0,l.useEffect)((()=>{var n;if(void 0===t)return;const a=e.get(t);return void 0!==a?(s(a),null===(n=r.current)||void 0===n||n.observe(a),()=>{var e;return null===(e=r.current)||void 0===e?void 0:e.disconnect()}):void 0}),n),a}(ne.current,null!=pe?pe:ve,[null!=pe?pe:ve,Z,h,j,P]);return l.createElement(p.Provider,{value:{size:h,overflowBehaviour:P}},l.createElement("div",{...ue,className:Y({size:h,overflowBehaviour:P,className:y}),style:k,"data-name":O},l.createElement("div",{...ce,className:r()(W["underline-tabs"],{[W["make-grid-column"]]:j||Q,[W["stretch-tabs"]]:j,[W["equal-tab-size"]]:Q})},Z.map((e=>l.createElement(C,{...ge(e),key:e.id,item:e,onClick:de,enableActiveStateStyles:U,hideFocusOutline:I,ref:K(F(e)),...e.dataId&&{"data-id":e.dataId},equalTabSize:Q}))),ee.map((e=>l.createElement(C,{...ge(e),ref:K(F(e)),key:e.id,
item:e,fake:!0}))),"collapse"===P&&l.createElement(N,{size:h,disabled:b,isOpened:ae,items:ee,buttonContent:g,buttonRef:$,isHighlighted:fe,onButtonClick:xe,onItemClick:we,onClose:oe,enableActiveStateStyles:U,hideFocusOutline:I,fake:0===ee.length}),ye?l.createElement(X,{...ye,disabled:b}):l.createElement("div",null))))}var G=n(38952);function _(e){return l.createElement("a",{...(0,G.renameRef)(e)})}(0,l.forwardRef)(((e,t)=>{var n;const{size:a,overflowBehaviour:o}=(0,l.useContext)(p),r=(0,l.useContext)(v.CustomBehaviourContext),{item:i,highlighted:s,handleItemRef:c,onClick:u,active:d,fake:m,className:f,enableActiveStateStyles:b=r.enableActiveStateStyles,hideFocusOutline:h=!1,disabled:C,"aria-disabled":x,...w}=e,y=(0,l.useCallback)((e=>{x?e.preventDefault():u&&u(i)}),[u,x,i]),k=(0,l.useCallback)((e=>{c&&c(i,e),t&&"object"==typeof t?t.current=e:"function"==typeof t&&t(e)}),[i,c,t]),S=null!==(n=i.renderComponent)&&void 0!==n?n:_;return l.createElement(S,{...w,id:i.id,"aria-disabled":x,onClick:y,reference:k,href:i.href,rel:i.rel,target:i.target,className:g({size:a,active:d,fake:m,enableActiveStateStyles:b,anchor:!0,hideFocusOutline:h,className:f,overflowBehaviour:o})},i.label)})).displayName="UnderlineAnchorTab"},4237:(e,t,n)=>{"use strict";var a=n(32227);t.createRoot=a.createRoot,a.hydrateRoot},72170:e=>{e.exports={calendar:"calendar-PM3TZruR"}},4308:e=>{e.exports={row:"row-Sj9z7O1v",mobileRow:"mobileRow-Sj9z7O1v"}},93098:e=>{e.exports={dialogWrapper:"dialogWrapper-P_IVoUsZ",dialogWrapperSmall:"dialogWrapperSmall-P_IVoUsZ",tabs:"tabs-P_IVoUsZ",content:"content-P_IVoUsZ",contentMobile:"contentMobile-P_IVoUsZ",bodyWrapper:"bodyWrapper-P_IVoUsZ"}},84828:(e,t,n)=>{"use strict";n.r(t),n.d(t,{showGoToDateDialog:()=>le});var a=n(50959),l=n(50151),o=n(82992),r=n(23935),i=n(51826),s=n(2740),c=n.n(s);const u=a.createContext(null);function d(e){const{initialGoToDate:t,children:n}=e,[l,o]=(0,a.useState)(t),i=l.valueOf()<=(0,r.resetToDayEnd)(new Date).valueOf(),s=(0,a.useMemo)((()=>({date:l,setDate:o,isValid:i})),[l,i]);return a.createElement(u.Provider,{value:s},n)}const m=a.createContext(null);function f(e){const{initialRanges:t,children:n}=e,[l,o]=(0,a.useState)(t.from),[r,i]=(0,a.useState)(t.to),s=l.valueOf()<=r.valueOf(),c=(0,a.useMemo)((()=>({dateFrom:l,dateTo:r,setDateFrom:o,setDateTo:i,isValid:s})),[l,r,s]);return a.createElement(m.Provider,{value:c},n)}var p=n(11542),v=n(97754),b=n.n(v),g=n(76422),h=n(56840),C=n.n(h),x=n(52092),w=n(24437),y=n(50182),k=n(4308);function S(e){const{children:t}=e;return a.createElement("div",{className:b()(k.row,j&&k.mobileRow)},t)}var D=n(85528),E=n(76056);const A=a.createContext({isActive:!1,isFocused:!1});function O(e){const{value:t,reference:n,isActive:l,onPick:o,onFocus:r,isDisabled:i,minValue:s,maxValue:c}=e,[u,d]=(0,a.useState)(!1);return a.createElement(A.Provider,{value:{isActive:l,isFocused:u}},a.createElement("div",{onFocus:function(){d(!0),r&&r()},onBlur:function(){d(!1)}},a.createElement(D.DatePicker,{initial:t,minDate:s,maxDate:c,inputReference:n,InputComponent:R,withCalendar:!1,
onPick:function(e){if(!e)return;o(new Date(e))},revertInvalidData:!0,name:e.name,disabled:i})))}function R(e){const{isActive:t,isFocused:n}=(0,a.useContext)(A);return a.createElement(E.DateInput,{...e,highlight:t||n})}var T=n(36565);function I(e){const{value:t,isDisabled:n,onPick:l,className:o}=e;return a.createElement(T.TimeInput,{value:(i=t,(0,r.twoDigitsFormat)(i.getHours())+":"+(0,r.twoDigitsFormat)(i.getMinutes())),onChange:l,disabled:n,className:o});var i}var N=n(93301),P=n(72170);function M(e){const{className:t,...n}=e;return a.createElement(N.Calendar,{...n,className:b()(P.calendar,t),popupStyle:!1})}function F(e,t){const n=new Date(t);return n.setFullYear(e.getFullYear()),n.setMonth(e.getMonth(),1),n.setDate(e.getDate()),n}function B(e,t){const n=new Date(t);return n.setHours(e.getHours()),n.setMinutes(e.getMinutes()),n}function z(e){const{dateOnly:t,onCalendarMonthSwitch:n,hideTimePick:o,minDate:r}=e,{date:i,setDate:s}=(0,l.ensureNotNull)((0,a.useContext)(u)),c=(0,a.useRef)(null),d=(0,a.useRef)(null);return(0,a.useEffect)((()=>{j||null===d.current||d.current.focus()}),[]),a.createElement("div",{ref:c,tabIndex:-1},a.createElement(S,null,a.createElement(O,{reference:function(e){d.current=e},value:new Date(i),onPick:function(e){const t=F(e,i);s(t)},isActive:!j,minValue:r}),!o&&a.createElement(I,{value:new Date(i),isDisabled:t,onPick:function(e){var t;const[n,a]=e.split(":"),l=new Date;l.setHours(Number(n)),l.setMinutes(Number(a));const o=B(l,i);s(o),j||null===(t=c.current)||void 0===t||t.focus({preventScroll:!0})}})),!j&&a.createElement(M,{key:`${i.getFullYear()}-${i.getMonth()}-${i.getDate()}`,selectedDate:new Date(i),onSelect:function(e){var t;const n=F(e,i);s(n),null===(t=c.current)||void 0===t||t.focus({preventScroll:!0})},onMonthSwitch:n,maxDate:new Date,minDate:r}))}function W(e){const{dateOnly:t,onCalendarMonthSwitch:n,onDateInputFocus:o}=e,{dateFrom:r,dateTo:i,setDateFrom:s,setDateTo:c}=(0,l.ensureNotNull)((0,a.useContext)(m)),[u,d]=(0,a.useState)("from"),f=(0,a.useRef)(null),p=(0,a.useRef)(null),v=(0,a.useRef)(null),b=(0,a.useMemo)((()=>"from"===u?new Date(r):new Date(i)),[u,i,r]);return(0,a.useEffect)((()=>{j||null===p.current||p.current.focus()}),[]),a.createElement("div",{ref:f,tabIndex:-1},a.createElement(S,null,a.createElement(O,{value:r,reference:function(e){p.current=e},isActive:!j&&"from"===u,onPick:function(e){const t=F(e,r);s(t)},onFocus:function(){d("from"),o()},name:"start-date-range"}),a.createElement(I,{value:r,isDisabled:t,onPick:function(e){g(e,r,s)}})),a.createElement(S,null,a.createElement(O,{value:i,reference:function(e){v.current=e},isActive:!j&&"to"===u,onPick:function(e){const t=F(e,i);c(t)},onFocus:function(){d("to"),o()},name:"end-date-range"}),a.createElement(I,{value:i,isDisabled:t,onPick:function(e){g(e,i,c)}})),!j&&a.createElement(M,{key:`${b.getFullYear()}-${b.getMonth()}-${b.getDate()}`,selectedDate:new Date(b),onSelect:function(e){const t=F(e,"from"===u?r:i);({from:()=>{var e;s(t),null===(e=v.current)||void 0===e||e.focus({preventScroll:!0})},to:()=>{var e;c(t),
null===(e=f.current)||void 0===e||e.focus({preventScroll:!0})}})[u]()},onMonthSwitch:n,highlightedFrom:new Date(r),highlightedTo:new Date(i),maxDate:"from"===u?new Date(i):void 0,minDate:"to"===u?new Date(r):void 0}));function g(e,t,n){var a;const[l,o]=e.split(":"),r=new Date;r.setHours(Number(l)),r.setMinutes(Number(o));n(B(r,t)),j||null===(a=f.current)||void 0===a||a.focus({preventScroll:!0})}}var Y=n(93081),L=n(90692),V=n(32563),H=n(93098);const j=V.mobiletouch;var Q;!function(e){e.Date="Date",e.CustomRange="CustomRange"}(Q||(Q={}));const X=()=>!0,U=[{label:p.t(null,void 0,n(62154)),id:"Date",dataId:"tab-item-date"},{label:p.t(null,void 0,n(81861)),id:"CustomRange",dataId:"tab-item-customrange"}];function G(e){const{dateOnly:t,onClose:o,onGoToDate:r,onGoToRange:i}=e,s=(0,a.useRef)(null),[c,d]=(0,a.useState)(C().getValue("GoToDialog.activeTab","Date")),[f,v]=(0,a.useState)(0),{date:h,isValid:k}=(0,l.ensureNotNull)((0,a.useContext)(u)),{dateFrom:S,dateTo:D,isValid:E}=(0,l.ensureNotNull)((0,a.useContext)(m));return(0,a.useEffect)((()=>(g.subscribe(x.CLOSE_POPUPS_AND_DIALOGS_COMMAND,T,null),()=>{g.unsubscribe(x.CLOSE_POPUPS_AND_DIALOGS_COMMAND,T,null)})),[o]),(0,a.useEffect)((()=>{null!==s.current&&s.current()}),[f,c,h,S,D]),a.createElement(L.MatchMedia,{rule:w.DialogBreakpoints.TabletSmall},(e=>a.createElement(y.AdaptiveConfirmDialog,{className:b()(H.dialogWrapper,e&&H.dialogWrapperSmall),title:p.t(null,void 0,n(42432)),dataName:"go-to-date-dialog",render:A,defaultActionOnClose:"cancel",onClose:T,onClickOutside:T,onCancel:T,onSubmit:R,submitButtonDisabled:O(),submitButtonText:p.t(null,void 0,n(42432)),forceCloseOnEsc:X,shouldForceFocus:!1,fullScreen:e,isOpened:!0})));function A({requestResize:e}){return s.current=e,a.createElement(a.Fragment,null,a.createElement("div",{className:H.tabs},a.createElement(Y.UnderlineButtonTabs,{id:"go-to-date-tabs",isActive:e=>e.id===c,items:U,onActivate:I,overflowBehaviour:"scroll"})),a.createElement("div",{className:b()(H.content,j&&H.contentMobile)},a.createElement("div",{className:H.bodyWrapper},a.createElement(_,{onCalendarMonthSwitch:N,onDateInputFocus:N,activeTab:c,dateOnly:t}))))}function O(){return{CustomRange:!E,Date:!k}[c]}function R(){switch(c){case"Date":r(h);break;case"CustomRange":i(S,D)}}function T(){o()}function I(e){d(e.id),C().setValue("GoToDialog.activeTab",e.id)}function N(){v(f+1)}}function _(e){const{activeTab:t,dateOnly:n,onCalendarMonthSwitch:l,onDateInputFocus:o}=e;switch(t){case"Date":return a.createElement(z,{dateOnly:n,onCalendarMonthSwitch:l});case"CustomRange":return a.createElement(W,{dateOnly:n,onCalendarMonthSwitch:l,onDateInputFocus:o})}}function q(e){const{dateOnly:t,onClose:n,onGoToDate:l,onGoToRange:o,initialGoToDate:r,initialRanges:i}=e;return a.createElement(d,{initialGoToDate:r},a.createElement(f,{initialRanges:i},a.createElement(G,{dateOnly:t,onClose:n,onGoToDate:l,onGoToRange:o})))}var J,$=n(10074),K=n(8025);!function(e){e.GoToDateTabLastPickedDate="goToDateTabLastPickedDate",e.DetailsKeyStatsExpanded="detailsKeyStatsExpanded",
e.DetailsIncomeStatementPeriodId="detailsIncomeStatementPeriodId"}(J||(J={}));const Z=new class{constructor(){this._hasError=!1}getItemOrDefault(e,t){return!sessionStorage||this._hasError?t:sessionStorage.getItem(e)}setItem(e,t="true"){try{sessionStorage.setItem(e,t),this._hasError=!1}catch(e){this._hasError=!0}}};var ee=n(27365),te=n(28124);const ne="goTo",ae=new i.DialogsOpenerManager;function le(e){if(ae.isOpened(ne))return;if(!e.hasModel())return;const t=e.model(),n=document.createElement("div"),l=a.createElement(q,{onClose:u,dateOnly:t.model().mainSeries().isDWM(),initialGoToDate:oe(),initialRanges:re(e),onGoToDate:e=>{!function(e,t){Z.setItem("goToDateTabLastPickedDate",String(t.valueOf()));if(void 0===e.model().timeScale().tickMarks().minIndex)return;const n=(0,r.addLocalTime)(t).valueOf();e.model().gotoTime(n).then((t=>{const n=e.model().mainSeries();void 0===t?n.clearGotoDateResult():n.setGotoDateResult(t)}))}(t,e),u()},onGoToRange:(t,n)=>{!function(e,t,n){const a=(0,ee.getTimezoneName)(e.model());if(!a)return;const l=o.linking.interval.value(),i=l&&(0,$.normalizeIntervalString)(l),u=c().get_timezone(a),d=e=>(0,s.cal_to_utc)(u,new Date(e)),m=(0,r.addLocalTime)(t).valueOf(),f=(0,r.addLocalTime)(n).valueOf(),p={val:{type:"time-range",from:d(m)/1e3,to:d(f)/1e3},res:i};e.chartWidgetCollection().setTimeFrame(p)}(e,t,n),u()}}),i=(0,te.createReactRoot)(l,n);function u(){i.unmount(),ae.setAsClosed(ne)}ae.setAsOpened(ne)}function oe(){const e=Z.getItemOrDefault("goToDateTabLastPickedDate",null);return null===e?(0,r.resetToDayStart)(new Date):new Date(Number(e))}function re(e){const t=function(e){const t=e.model().timeScale(),n=t.visibleBarsStrictRange();if(null===n)return;const a=e.model().mainSeries(),o=a.nearestIndex(n.firstBar(),K.PlotRowSearchMode.NearestRight),r=a.nearestIndex(n.lastBar(),K.PlotRowSearchMode.NearestLeft);if(void 0===o||void 0===r)return;return{from:(0,l.ensureNotNull)(t.indexToUserTime(o)),to:(0,l.ensureNotNull)(t.indexToUserTime(r))}}(e);return t?{from:(0,r.subtractLocalTime)(t.from),to:(0,r.subtractLocalTime)(t.to)}:{from:(0,r.subtractLocalTime)(new Date),to:(0,r.subtractLocalTime)(new Date)}}},47531:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="m4.67 7.38.66-.76L9 9.84l3.67-3.22.66.76L9 11.16 4.67 7.38Z"/></svg>'},63509:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M3.57 7.85 9 12.62l5.43-4.77-1.32-1.5L9 9.95l-4.11-3.6-1.32 1.5Z"/></svg>'},68874:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="m14 18.41-6.7-6.7 1.4-1.42 5.3 5.3 5.3-5.3 1.4 1.41-6.7 6.71Z"/></svg>'},55698:(e,t,n)=>{"use strict";n.d(t,{nanoid:()=>a});let a=(e=21)=>crypto.getRandomValues(new Uint8Array(e)).reduce(((e,t)=>e+=(t&=63)<36?t.toString(36):t<62?(t-26).toString(36).toUpperCase():t>62?"-":"_"),"")}}]);