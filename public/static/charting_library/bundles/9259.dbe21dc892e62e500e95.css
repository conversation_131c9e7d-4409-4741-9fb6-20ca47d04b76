.lollipopTooltipTitle-hkWvPxQc {
  align-items: center;
  column-gap: 8px;
  display: flex;
  justify-content: flex-start;
  margin-bottom: 12px;
  padding: 4px 0;
}
.lollipopTooltipTitle_minimal-hkWvPxQc .lollipopTooltipTitle__title-hkWvPxQc {
  font-size: 16px;
  line-height: 22px;
}
.lollipopTooltipTitle_mobile-hkWvPxQc .lollipopTooltipTitle__title-hkWvPxQc {
  font-size: 20px;
  line-height: 28px;
}
.lollipopTooltipTitle__icon-hkWvPxQc {
  color: currentColor;
  display: flex;
}
.lollipopTooltipTitle__title-hkWvPxQc {
  font-family:
    -apple-system,
    BlinkMacSystemFont,
    Trebuchet MS,
    Roboto,
    Ubuntu,
    sans-serif;
  font-feature-settings:
    "tnum" on,
    "lnum" on;
  font-style: normal;
  --ui-lib-typography-font-size: 18px;
  font-size: var(--ui-lib-typography-font-size);
  font-weight: 600;
  --ui-lib-typography-line-height: 24px;
  line-height: var(--ui-lib-typography-line-height);
}
.wrap-tm3FiOQl {
  background: var(
    --tv-color-popup-background,
    var(--themed-color-tooltip-background, #fff)
  );
}
html.theme-dark .wrap-tm3FiOQl {
  background: var(
    --tv-color-popup-background,
    var(--themed-color-tooltip-background, #1e222d)
  );
}
.content-tm3FiOQl.contentWithTab-tm3FiOQl {
  padding: 16px 0 0;
}
.content-tm3FiOQl.contentWithTab-tm3FiOQl .group-tm3FiOQl {
  padding: 12px 0 16px 12px;
}
.content-tm3FiOQl a,
.content-tm3FiOQl span {
  cursor: default;
}
.content-tm3FiOQl .subtitle-tm3FiOQl {
  font-size: 14px;
  line-height: 21px;
}
.content-tm3FiOQl .subtitle-tm3FiOQl .text-tm3FiOQl {
  flex: 1 0 0;
  overflow: hidden;
  text-overflow: ellipsis;
}
.content-tm3FiOQl .tabsWrapper-tm3FiOQl {
  padding-bottom: 12px;
  padding-left: 12px;
}
.content-tm3FiOQl .tabsContentWrapper-tm3FiOQl {
  width: 100%;
}
.content-tm3FiOQl .group-tm3FiOQl {
  padding-top: 12px;
}
.content-tm3FiOQl .groupIcon-tm3FiOQl {
  border-radius: 9px;
  display: inline-block;
  height: 18px;
  margin-left: 7px;
  vertical-align: top;
  width: 18px;
}
.content-tm3FiOQl .groupIcon-tm3FiOQl.beforeMarketOpen-tm3FiOQl {
  color: var(--themed-color-before-market-open, #fb8c00);
}
.content-tm3FiOQl .groupIcon-tm3FiOQl.beforeMarketOpen-tm3FiOQl,
html.theme-dark
  .content-tm3FiOQl
  .groupIcon-tm3FiOQl.beforeMarketOpen-tm3FiOQl {
  background-color: var(--themed-color-before-market-open-bg, #ffa72626);
}
html.theme-dark
  .content-tm3FiOQl
  .groupIcon-tm3FiOQl.beforeMarketOpen-tm3FiOQl {
  color: var(--themed-color-before-market-open, #fb8c00);
}
@media (any-hover: hover) {
  .content-tm3FiOQl .groupIcon-tm3FiOQl.beforeMarketOpen-tm3FiOQl:hover,
  html.theme-dark
    .content-tm3FiOQl
    .groupIcon-tm3FiOQl.beforeMarketOpen-tm3FiOQl:hover {
    background-color: var(--themed-color-before-market-open-bg, #ffa72640);
  }
}
.content-tm3FiOQl .groupIcon-tm3FiOQl.afterMarketClose-tm3FiOQl {
  color: var(--themed-color-after-market-close, #2962ff);
}
.content-tm3FiOQl .groupIcon-tm3FiOQl.afterMarketClose-tm3FiOQl,
html.theme-dark
  .content-tm3FiOQl
  .groupIcon-tm3FiOQl.afterMarketClose-tm3FiOQl {
  background-color: var(--themed-color-after-market-close-bg, #3179f526);
}
html.theme-dark
  .content-tm3FiOQl
  .groupIcon-tm3FiOQl.afterMarketClose-tm3FiOQl {
  color: var(--themed-color-after-market-close, #2962ff);
}
@media (any-hover: hover) {
  .content-tm3FiOQl .groupIcon-tm3FiOQl.afterMarketClose-tm3FiOQl:hover,
  html.theme-dark
    .content-tm3FiOQl
    .groupIcon-tm3FiOQl.afterMarketClose-tm3FiOQl:hover {
    background-color: var(--themed-color-after-market-close-bg, #3179f540);
  }
}
.content-tm3FiOQl .groupTitle-tm3FiOQl {
  color: var(--themed-color-default-gray, #6a6d78);
  font-size: 11px;
  font-weight: 400;
  letter-spacing: 0.4px;
  line-height: 16px;
  text-transform: uppercase;
}
html.theme-dark .content-tm3FiOQl .groupTitle-tm3FiOQl {
  color: var(--themed-color-default-gray, #868993);
}
.content-tm3FiOQl .groupRow-tm3FiOQl {
  display: flex;
  flex-direction: row;
}
.content-tm3FiOQl .groupCell-tm3FiOQl {
  flex: 1 0 0;
}
.content-tm3FiOQl .group-tm3FiOQl .text-tm3FiOQl {
  font-size: 14px;
  line-height: 22px;
  overflow: hidden;
  text-overflow: ellipsis;
}
.content-tm3FiOQl.mob-tm3FiOQl .group-tm3FiOQl .text-tm3FiOQl,
.content-tm3FiOQl.mob-tm3FiOQl .subtitle-tm3FiOQl {
  font-size: 16px;
  line-height: 24px;
}
.content-tm3FiOQl.mob-tm3FiOQl .tabsWrapper-tm3FiOQl {
  padding-left: 0;
}
.content-tm3FiOQl.mini-tm3FiOQl .subtitle-tm3FiOQl {
  font-size: 13px;
  line-height: 19px;
}
.generalContent-tm3FiOQl > div {
  background: var(--themed-color-content-item-bg, #fff);
}
html.theme-dark .generalContent-tm3FiOQl > div {
  background: var(--themed-color-content-item-bg, #1e222d);
}
@media (any-hover: hover) {
  .generalContent-tm3FiOQl > div:hover {
    background-color: var(--themed-color-bg-primary-hover, #f0f3fa);
    cursor: pointer;
  }
  html.theme-dark .generalContent-tm3FiOQl > div:hover {
    background-color: var(--themed-color-bg-primary-hover, #2a2e39);
  }
}
.drawer-xBKhVqal > :not(:last-child) {
  border-bottom: 1px solid var(--themed-color-cold-gray-150, #e0e3eb);
}
.drawerItem-xBKhVqal {
  color: var(--themed-color-tooltip-text, #131722);
  padding: 16px;
}
html.theme-dark .drawerItem-xBKhVqal {
  color: var(--themed-color-tooltip-text, #d1d4dc);
}
.menuWrap-xBKhVqal {
  background: var(--themed-color-menu-wrapper, #0000);
}
.menuWrap-xBKhVqal,
html.theme-dark .menuWrap-xBKhVqal {
  box-shadow: 0 0 var(--themed-color-menu-wrapper, #0000);
}
html.theme-dark .menuWrap-xBKhVqal {
  background: var(--themed-color-menu-wrapper, #0000);
}
.menuWrap-xBKhVqal .scrollWrap-xBKhVqal {
  overflow-y: hidden !important;
}
.menuWrap-xBKhVqal .menuBox-xBKhVqal {
  margin: 2px 4px 4px;
  padding: 0;
}
.card-xBKhVqal {
  border-left: 4px solid;
  border-radius: 4px;
  box-shadow: 0 2px 4px var(--themed-color-tooltip-card-shadow, #787b8666);
  box-sizing: border-box;
  color: var(--themed-color-tooltip-text, #131722);
  padding: 16px 16px 16px 12px;
  width: 300px;
}
html.theme-dark .card-xBKhVqal {
  box-shadow: 0 2px 4px var(--themed-color-tooltip-card-shadow, #0006);
  color: var(--themed-color-tooltip-text, #d1d4dc);
}
.card-xBKhVqal .cardRow-xBKhVqal {
  padding-left: 12px;
  padding-right: 16px;
}
.card-xBKhVqal.mini-xBKhVqal .cardRow-xBKhVqal {
  padding-right: 12px;
}
.card-xBKhVqal:not(:first-child) {
  margin-top: 8px;
}
.fadeTop-xBKhVqal {
  background: linear-gradient(
    var(--themed-color-scroll-fade, #fff),
    var(--themed-color-scroll-fade, #fff0)
  );
  height: 10px;
  position: absolute;
  top: 0;
}
html.theme-dark .fadeTop-xBKhVqal {
  background: linear-gradient(
    var(--themed-color-scroll-fade, #131722),
    var(--themed-color-scroll-fade, #13172200)
  );
}
.fadeBottom-xBKhVqal {
  background: linear-gradient(
    var(--themed-color-scroll-fade, #fff0),
    var(--themed-color-scroll-fade, #fff)
  );
  bottom: 0;
  height: 10px;
  position: absolute;
}
html.theme-dark .fadeBottom-xBKhVqal {
  background: linear-gradient(
    var(--themed-color-scroll-fade, #13172200),
    var(--themed-color-scroll-fade, #131722)
  );
}
