(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[2312],{53310:e=>{e.exports={en:["Re"],fa:["Re"]}},94073:e=>{e.exports={en:["A"],fa:["A"]}},66384:e=>{e.exports={en:["L"],fa:["L"]}},85119:e=>{e.exports={en:["Dark"],fa:["Dark"]}},96870:e=>{e.exports={en:["Light"],fa:["Light"]}},85886:e=>{e.exports={en:["d"],fa:["روز"]}},44634:e=>{e.exports={en:["h"],fa:["ساعت"]}},5977:e=>{e.exports={en:["m"],fa:["ماه"]}},21492:e=>{e.exports={en:["s"],fa:["s"]}},97559:e=>{e.exports={en:["{title} copy"],fa:["{title} copy"]}},38691:e=>{e.exports={en:["D"],fa:["D"]}},77995:e=>{e.exports={en:["M"],fa:["M"]}},93934:e=>{e.exports={en:["R"],fa:["R"]}},82901:e=>{e.exports={en:["T"],fa:["T"]}},7408:e=>{e.exports={en:["W"],fa:["W"]}},38048:e=>{e.exports={en:["h"],fa:["h"]}},68430:e=>{e.exports={en:["m"],fa:["m"]}},68823:e=>{e.exports={en:["s"],fa:["s"]}},2696:e=>{e.exports={en:["C"],fa:["پایانی"]}},43253:e=>{e.exports={en:["H"],fa:["بیشترین"]}},61372:e=>{e.exports={en:["HL2"],fa:["HL2"]}},55096:e=>{e.exports={en:["HLC3"],fa:["HLC3"]}},94174:e=>{e.exports={en:["OHLC4"],fa:["OHLC4"]}},89923:e=>{e.exports={en:["L"],fa:["کمترین"]}},46728:e=>{e.exports={en:["O"],fa:["باز"]}},32856:e=>{e.exports=Object.create(null),e.exports.Close_input={en:["Close"],fa:["Close"]},e.exports.Back_input={en:["Back"],fa:["Back"]},e.exports.Minimize_input={en:["Minimize"],fa:["Minimize"]},e.exports["Hull MA_input"]={en:["Hull MA"],fa:["Hull MA"]},e.exports.from_input={en:["from"],fa:["from"]},e.exports.to_input={en:["to"],fa:["to"]},e.exports["{number} item_combobox_input"]={en:["{number} item","{number} items"],fa:["{number} items"]},e.exports.Length_input={en:["Length"],fa:["Length"]},e.exports.Plot_input={en:["Plot"],fa:["Plot"]},e.exports.Zero_input={en:["Zero"],fa:["Zero"]},e.exports.Signal_input={en:["Signal"],fa:["Signal"]},e.exports.Long_input={en:["Long"],fa:["Long"]},e.exports.Short_input={en:["Short"],fa:["Short"]},e.exports.UpperLimit_input={en:["UpperLimit"],fa:["UpperLimit"]},e.exports.LowerLimit_input={en:["LowerLimit"],fa:["LowerLimit"]},e.exports.Offset_input={en:["Offset"],fa:["Offset"]},e.exports.length_input={en:["length"],fa:["length"]},e.exports.mult_input={en:["mult"],fa:["mult"]},e.exports.short_input={en:["short"],fa:["short"]},e.exports.long_input={en:["long"],fa:["long"]},e.exports.Limit_input={en:["Limit"],fa:["Limit"]},e.exports.Move_input={en:["Move"],fa:["Move"]},e.exports.Value_input={en:["Value"],fa:["Value"]},e.exports.Method_input={en:["Method"],fa:["Method"]},e.exports["Values in status line_input"]={en:["Values in status line"],fa:["Values in status line"]},e.exports["Labels on price scale_input"]={en:["Labels on price scale"],fa:["Labels on price scale"]},e.exports["Accumulation/Distribution_input"]={en:["Accumulation/Distribution"],fa:["Accumulation/Distribution"]},e.exports.ADR_B_input={en:["ADR_B"],fa:["ADR_B"]},e.exports["Equality Line_input"]={en:["Equality Line"],fa:["Equality Line"]},e.exports["Window Size_input"]={en:["Window Size"],fa:["Window Size"]},e.exports.Sigma_input={en:["Sigma"],fa:["Sigma"]},
e.exports["Aroon Up_input"]={en:["Aroon Up"],fa:["Aroon Up"]},e.exports["Aroon Down_input"]={en:["Aroon Down"],fa:["Aroon Down"]},e.exports.Upper_input={en:["Upper"],fa:["Upper"]},e.exports.Lower_input={en:["Lower"],fa:["Lower"]},e.exports.Deviation_input={en:["Deviation"],fa:["Deviation"]},e.exports["Levels Format_input"]={en:["Levels Format"],fa:["Levels Format"]},e.exports["Labels Position_input"]={en:["Labels Position"],fa:["Labels Position"]},e.exports["0 Level Color_input"]={en:["0 Level Color"],fa:["0 Level Color"]},e.exports["0.236 Level Color_input"]={en:["0.236 Level Color"],fa:["0.236 Level Color"]},e.exports["0.382 Level Color_input"]={en:["0.382 Level Color"],fa:["0.382 Level Color"]},e.exports["0.5 Level Color_input"]={en:["0.5 Level Color"],fa:["0.5 Level Color"]},e.exports["0.618 Level Color_input"]={en:["0.618 Level Color"],fa:["0.618 Level Color"]},e.exports["0.65 Level Color_input"]={en:["0.65 Level Color"],fa:["0.65 Level Color"]},e.exports["0.786 Level Color_input"]={en:["0.786 Level Color"],fa:["0.786 Level Color"]},e.exports["1 Level Color_input"]={en:["1 Level Color"],fa:["1 Level Color"]},e.exports["1.272 Level Color_input"]={en:["1.272 Level Color"],fa:["1.272 Level Color"]},e.exports["1.414 Level Color_input"]={en:["1.414 Level Color"],fa:["1.414 Level Color"]},e.exports["1.618 Level Color_input"]={en:["1.618 Level Color"],fa:["1.618 Level Color"]},e.exports["1.65 Level Color_input"]={en:["1.65 Level Color"],fa:["1.65 Level Color"]},e.exports["2.618 Level Color_input"]={en:["2.618 Level Color"],fa:["2.618 Level Color"]},e.exports["2.65 Level Color_input"]={en:["2.65 Level Color"],fa:["2.65 Level Color"]},e.exports["3.618 Level Color_input"]={en:["3.618 Level Color"],fa:["3.618 Level Color"]},e.exports["3.65 Level Color_input"]={en:["3.65 Level Color"],fa:["3.65 Level Color"]},e.exports["4.236 Level Color_input"]={en:["4.236 Level Color"],fa:["4.236 Level Color"]},e.exports["-0.236 Level Color_input"]={en:["-0.236 Level Color"],fa:["-0.236 Level Color"]},e.exports["-0.382 Level Color_input"]={en:["-0.382 Level Color"],fa:["-0.382 Level Color"]},e.exports["-0.618 Level Color_input"]={en:["-0.618 Level Color"],fa:["-0.618 Level Color"]},e.exports["-0.65 Level Color_input"]={en:["-0.65 Level Color"],fa:["-0.65 Level Color"]},e.exports.ADX_input={en:["ADX"],fa:["ADX"]},e.exports["ADX Smoothing_input"]={en:["ADX Smoothing"],fa:["ADX Smoothing"]},e.exports["DI Length_input"]={en:["DI Length"],fa:["DI Length"]},e.exports.Smoothing_input={en:["Smoothing"],fa:["Smoothing"]},e.exports.ATR_input={en:["ATR"],fa:["ATR"]},e.exports.Growing_input={en:["Growing"],fa:["Growing"]},e.exports.Falling_input={en:["Falling"],fa:["Falling"]},e.exports["Color 0_input"]={en:["Color 0"],fa:["Color 0"]},e.exports["Color 1_input"]={en:["Color 1"],fa:["Color 1"]},e.exports.Source_input={en:["Source"],fa:["Source"]},e.exports.StdDev_input={en:["StdDev"],fa:["StdDev"]},e.exports.Basis_input={en:["Basis"],fa:["Basis"]},e.exports.Median_input={en:["Median"],fa:["Median"]},e.exports["Bollinger Bands %B_input"]={
en:["Bollinger Bands %B"],fa:["Bollinger Bands %B"]},e.exports.Overbought_input={en:["Overbought"],fa:["Overbought"]},e.exports.Oversold_input={en:["Oversold"],fa:["Oversold"]},e.exports["Bollinger Bands Width_input"]={en:["Bollinger Bands Width"],fa:["Bollinger Bands Width"]},e.exports["RSI Length_input"]={en:["RSI Length"],fa:["RSI Length"]},e.exports["UpDown Length_input"]={en:["UpDown Length"],fa:["UpDown Length"]},e.exports["ROC Length_input"]={en:["ROC Length"],fa:["ROC Length"]},e.exports.MF_input={en:["MF"],fa:["MF"]},e.exports.resolution_input={en:["resolution"],fa:["resolution"]},e.exports["Fast Length_input"]={en:["Fast Length"],fa:["Fast Length"]},e.exports["Slow Length_input"]={en:["Slow Length"],fa:["Slow Length"]},e.exports["Chaikin Oscillator_input"]={en:["Chaikin Oscillator"],fa:["Chaikin Oscillator"]},e.exports.P_input={en:["P"],fa:["P"]},e.exports.X_input={en:["X"],fa:["X"]},e.exports.Q_input={en:["Q"],fa:["Q"]},e.exports.p_input={en:["p"],fa:["p"]},e.exports.x_input={en:["x"],fa:["x"]},e.exports.q_input={en:["q"],fa:["q"]},e.exports.Price_input={en:["Price"],fa:["Price"]},e.exports["Chande MO_input"]={en:["Chande MO"],fa:["Chande MO"]},e.exports["Zero Line_input"]={en:["Zero Line"],fa:["Zero Line"]},e.exports["Color 2_input"]={en:["Color 2"],fa:["Color 2"]},e.exports["Color 3_input"]={en:["Color 3"],fa:["Color 3"]},e.exports["Color 4_input"]={en:["Color 4"],fa:["Color 4"]},e.exports["Color 5_input"]={en:["Color 5"],fa:["Color 5"]},e.exports["Color 6_input"]={en:["Color 6"],fa:["Color 6"]},e.exports["Color 7_input"]={en:["Color 7"],fa:["Color 7"]},e.exports["Color 8_input"]={en:["Color 8"],fa:["Color 8"]},e.exports.CHOP_input={en:["CHOP"],fa:["CHOP"]},e.exports["Upper Band_input"]={en:["Upper Band"],fa:["Upper Band"]},e.exports["Lower Band_input"]={en:["Lower Band"],fa:["Lower Band"]},e.exports.CCI_input={en:["CCI"],fa:["CCI"]},e.exports["Smoothing Line_input"]={en:["Smoothing Line"],fa:["Smoothing Line"]},e.exports["Smoothing Length_input"]={en:["Smoothing Length"],fa:["Smoothing Length"]},e.exports["WMA Length_input"]={en:["WMA Length"],fa:["WMA Length"]},e.exports["Long RoC Length_input"]={en:["Long RoC Length"],fa:["Long RoC Length"]},e.exports["Short RoC Length_input"]={en:["Short RoC Length"],fa:["Short RoC Length"]},e.exports.sym_input={en:["sym"],fa:["sym"]},e.exports.Symbol_input={en:["Symbol"],fa:["Symbol"]},e.exports.Correlation_input={en:["Correlation"],fa:["Correlation"]},e.exports.Period_input={en:["Period"],fa:["Period"]},e.exports.Centered_input={en:["Centered"],fa:["Centered"]},e.exports["Detrended Price Oscillator_input"]={en:["Detrended Price Oscillator"],fa:["Detrended Price Oscillator"]},e.exports.isCentered_input={en:["isCentered"],fa:["isCentered"]},e.exports.DPO_input={en:["DPO"],fa:["DPO"]},e.exports["ADX smoothing_input"]={en:["ADX smoothing"],fa:["ADX smoothing"]},e.exports["+DI_input"]={en:["+DI"],fa:["+DI"]},e.exports["-DI_input"]={en:["-DI"],fa:["-DI"]},e.exports.DEMA_input={en:["DEMA"],fa:["DEMA"]},e.exports["Multi timeframe_input"]={en:["Multi timeframe"],
fa:["Multi timeframe"]},e.exports.Timeframe_input={en:["Timeframe"],fa:["Timeframe"]},e.exports["Wait for timeframe closes_input"]={en:["Wait for timeframe closes"],fa:["Wait for timeframe closes"]},e.exports.Divisor_input={en:["Divisor"],fa:["Divisor"]},e.exports.EOM_input={en:["EOM"],fa:["EOM"]},e.exports["Elder's Force Index_input"]={en:["Elder's Force Index"],fa:["Elder's Force Index"]},e.exports.Percent_input={en:["Percent"],fa:["Percent"]},e.exports.Exponential_input={en:["Exponential"],fa:["Exponential"]},e.exports.Average_input={en:["Average"],fa:["Average"]},e.exports["Upper Percentage_input"]={en:["Upper Percentage"],fa:["Upper Percentage"]},e.exports["Lower Percentage_input"]={en:["Lower Percentage"],fa:["Lower Percentage"]},e.exports.Fisher_input={en:["Fisher"],fa:["Fisher"]},e.exports.Trigger_input={en:["Trigger"],fa:["Trigger"]},e.exports.Level_input={en:["Level"],fa:["Level"]},e.exports["Trader EMA 1 length_input"]={en:["Trader EMA 1 length"],fa:["Trader EMA 1 length"]},e.exports["Trader EMA 2 length_input"]={en:["Trader EMA 2 length"],fa:["Trader EMA 2 length"]},e.exports["Trader EMA 3 length_input"]={en:["Trader EMA 3 length"],fa:["Trader EMA 3 length"]},e.exports["Trader EMA 4 length_input"]={en:["Trader EMA 4 length"],fa:["Trader EMA 4 length"]},e.exports["Trader EMA 5 length_input"]={en:["Trader EMA 5 length"],fa:["Trader EMA 5 length"]},e.exports["Trader EMA 6 length_input"]={en:["Trader EMA 6 length"],fa:["Trader EMA 6 length"]},e.exports["Investor EMA 1 length_input"]={en:["Investor EMA 1 length"],fa:["Investor EMA 1 length"]},e.exports["Investor EMA 2 length_input"]={en:["Investor EMA 2 length"],fa:["Investor EMA 2 length"]},e.exports["Investor EMA 3 length_input"]={en:["Investor EMA 3 length"],fa:["Investor EMA 3 length"]},e.exports["Investor EMA 4 length_input"]={en:["Investor EMA 4 length"],fa:["Investor EMA 4 length"]},e.exports["Investor EMA 5 length_input"]={en:["Investor EMA 5 length"],fa:["Investor EMA 5 length"]},e.exports["Investor EMA 6 length_input"]={en:["Investor EMA 6 length"],fa:["Investor EMA 6 length"]},e.exports.HV_input={en:["HV"],fa:["HV"]},e.exports["Conversion Line Periods_input"]={en:["Conversion Line Periods"],fa:["Conversion Line Periods"]},e.exports["Base Line Periods_input"]={en:["Base Line Periods"],fa:["Base Line Periods"]},e.exports["Lagging Span_input"]={en:["Lagging Span"],fa:["Lagging Span"]},e.exports["Conversion Line_input"]={en:["Conversion Line"],fa:["Conversion Line"]},e.exports["Base Line_input"]={en:["Base Line"],fa:["Base Line"]},e.exports["Leading Span A_input"]={en:["Leading Span A"],fa:["Lead 1"]},e.exports["Leading Span B_input"]={en:["Leading Span B"],fa:["Lead 2"]},e.exports["Plots Background_input"]={en:["Plots Background"],fa:["Plots Background"]},e.exports["yay Color 0_input"]={en:["yay Color 0"],fa:["yay Color 0"]},e.exports["yay Color 1_input"]={en:["yay Color 1"],fa:["yay Color 1"]},e.exports.Multiplier_input={en:["Multiplier"],fa:["Multiplier"]},e.exports["Bands style_input"]={en:["Bands style"],fa:["Bands style"]},e.exports.Middle_input={
en:["Middle"],fa:["Middle"]},e.exports.useTrueRange_input={en:["useTrueRange"],fa:["useTrueRange"]},e.exports.ROCLen1_input={en:["ROCLen1"],fa:["ROCLen1"]},e.exports.ROCLen2_input={en:["ROCLen2"],fa:["ROCLen2"]},e.exports.ROCLen3_input={en:["ROCLen3"],fa:["ROCLen3"]},e.exports.ROCLen4_input={en:["ROCLen4"],fa:["ROCLen4"]},e.exports.SMALen1_input={en:["SMALen1"],fa:["SMALen1"]},e.exports.SMALen2_input={en:["SMALen2"],fa:["SMALen2"]},e.exports.SMALen3_input={en:["SMALen3"],fa:["SMALen3"]},e.exports.SMALen4_input={en:["SMALen4"],fa:["SMALen4"]},e.exports.SigLen_input={en:["SigLen"],fa:["SigLen"]},e.exports.KST_input={en:["KST"],fa:["KST"]},e.exports.Sig_input={en:["Sig"],fa:["Sig"]},e.exports.roclen1_input={en:["roclen1"],fa:["roclen1"]},e.exports.roclen2_input={en:["roclen2"],fa:["roclen2"]},e.exports.roclen3_input={en:["roclen3"],fa:["roclen3"]},e.exports.roclen4_input={en:["roclen4"],fa:["roclen4"]},e.exports.smalen1_input={en:["smalen1"],fa:["smalen1"]},e.exports.smalen2_input={en:["smalen2"],fa:["smalen2"]},e.exports.smalen3_input={en:["smalen3"],fa:["smalen3"]},e.exports.smalen4_input={en:["smalen4"],fa:["smalen4"]},e.exports.siglen_input={en:["siglen"],fa:["siglen"]},e.exports["Upper Deviation_input"]={en:["Upper Deviation"],fa:["Upper Deviation"]},e.exports["Lower Deviation_input"]={en:["Lower Deviation"],fa:["Lower Deviation"]},e.exports["Use Upper Deviation_input"]={en:["Use Upper Deviation"],fa:["Use Upper Deviation"]},e.exports["Use Lower Deviation_input"]={en:["Use Lower Deviation"],fa:["Use Lower Deviation"]},e.exports.Count_input={en:["Count"],fa:["Count"]},e.exports.Crosses_input={en:["Crosses"],fa:["Crosses"]},e.exports.MOM_input={en:["MOM"],fa:["MOM"]},e.exports.MA_input={en:["MA"],fa:["MA"]},e.exports["Length EMA_input"]={en:["Length EMA"],fa:["Length EMA"]},e.exports["Length MA_input"]={en:["Length MA"],fa:["Length MA"]},e.exports["Fast length_input"]={en:["Fast length"],fa:["Fast length"]},e.exports["Slow length_input"]={en:["Slow length"],fa:["Slow length"]},e.exports["Signal smoothing_input"]={en:["Signal smoothing"],fa:["Signal smoothing"]},e.exports["Simple ma(oscillator)_input"]={en:["Simple ma(oscillator)"],fa:["Simple ma(oscillator)"]},e.exports["Simple ma(signal line)_input"]={en:["Simple ma(signal line)"],fa:["Simple ma(signal line)"]},e.exports.Histogram_input={en:["Histogram"],fa:["Histogram"]},e.exports.MACD_input={en:["MACD"],fa:["MACD"]},e.exports.fastLength_input={en:["fastLength"],fa:["fastLength"]},e.exports.slowLength_input={en:["slowLength"],fa:["slowLength"]},e.exports.signalLength_input={en:["signalLength"],fa:["signalLength"]},e.exports.NV_input={en:["NV"],fa:["NV"]},e.exports.OnBalanceVolume_input={en:["OnBalanceVolume"],fa:["OnBalanceVolume"]},e.exports.Start_input={en:["Start"],fa:["شروع"]},e.exports.Increment_input={en:["Increment"],fa:["Increment"]},e.exports["Max value_input"]={en:["Max value"],fa:["Max value"]},e.exports.ParabolicSAR_input={en:["ParabolicSAR"],fa:["ParabolicSAR"]},e.exports.start_input={en:["start"],fa:["start"]},e.exports.increment_input={
en:["increment"],fa:["increment"]},e.exports.maximum_input={en:["maximum"],fa:["maximum"]},e.exports["Short length_input"]={en:["Short length"],fa:["Short length"]},e.exports["Long length_input"]={en:["Long length"],fa:["Long length"]},e.exports.OSC_input={en:["OSC"],fa:["OSC"]},e.exports.shortlen_input={en:["shortlen"],fa:["shortlen"]},e.exports.longlen_input={en:["longlen"],fa:["longlen"]},e.exports.PVT_input={en:["PVT"],fa:["PVT"]},e.exports.ROC_input={en:["ROC"],fa:["ROC"]},e.exports.RSI_input={en:["RSI"],fa:["RSI"]},e.exports.RVGI_input={en:["RVGI"],fa:["RVGI"]},e.exports.RVI_input={en:["RVI"],fa:["RVI"]},e.exports["Long period_input"]={en:["Long period"],fa:["Long period"]},e.exports["Short period_input"]={en:["Short period"],fa:["Short period"]},e.exports["Signal line period_input"]={en:["Signal line period"],fa:["Signal line period"]},e.exports.SMI_input={en:["SMI"],fa:["SMI"]},e.exports["SMI Ergodic Oscillator_input"]={en:["SMI Ergodic Oscillator"],fa:["SMI Ergodic Oscillator"]},e.exports.Indicator_input={en:["Indicator"],fa:["Indicator"]},e.exports.Oscillator_input={en:["Oscillator"],fa:["Oscillator"]},e.exports.K_input={en:["K"],fa:["K"]},e.exports.D_input={en:["D"],fa:["D"]},e.exports.smoothK_input={en:["smoothK"],fa:["smoothK"]},e.exports.smoothD_input={en:["smoothD"],fa:["smoothD"]},e.exports["%K_input"]={en:["%K"],fa:["%K"]},e.exports["%D_input"]={en:["%D"],fa:["%D"]},e.exports["Stochastic Length_input"]={en:["Stochastic Length"],fa:["Stochastic Length"]},e.exports["RSI Source_input"]={en:["RSI Source"],fa:["RSI Source"]},e.exports.lengthRSI_input={en:["lengthRSI"],fa:["lengthRSI"]},e.exports.lengthStoch_input={en:["lengthStoch"],fa:["lengthStoch"]},e.exports.TRIX_input={en:["TRIX"],fa:["TRIX"]},e.exports.TEMA_input={en:["TEMA"],fa:["TEMA"]},e.exports["Long Length_input"]={en:["Long Length"],fa:["Long Length"]},e.exports["Short Length_input"]={en:["Short Length"],fa:["Short Length"]},e.exports["Signal Length_input"]={en:["Signal Length"],fa:["Signal Length"]},e.exports.Length1_input={en:["Length1"],fa:["Length1"]},e.exports.Length2_input={en:["Length2"],fa:["Length2"]},e.exports.Length3_input={en:["Length3"],fa:["Length3"]},e.exports.length7_input={en:["length7"],fa:["length7"]},e.exports.length14_input={en:["length14"],fa:["length14"]},e.exports.length28_input={en:["length28"],fa:["length28"]},e.exports.UO_input={en:["UO"],fa:["UO"]},e.exports.VWMA_input={en:["VWMA"],fa:["VWMA"]},e.exports.len_input={en:["len"],fa:["len"]},e.exports["VI +_input"]={en:["VI +"],fa:["VI +"]},e.exports["VI -_input"]={en:["VI -"],fa:["VI -"]},e.exports["%R_input"]={en:["%R"],fa:["%R"]},e.exports["Jaw Length_input"]={en:["Jaw Length"],fa:["Jaw Length"]},e.exports["Teeth Length_input"]={en:["Teeth Length"],fa:["Teeth Length"]},e.exports["Lips Length_input"]={en:["Lips Length"],fa:["Lips Length"]},e.exports.Jaw_input={en:["Jaw"],fa:["Jaw"]},e.exports.Teeth_input={en:["Teeth"],fa:["Teeth"]},e.exports.Lips_input={en:["Lips"],fa:["Lips"]},e.exports["Jaw Offset_input"]={en:["Jaw Offset"],fa:["Jaw Offset"]},
e.exports["Teeth Offset_input"]={en:["Teeth Offset"],fa:["Teeth Offset"]},e.exports["Lips Offset_input"]={en:["Lips Offset"],fa:["Lips Offset"]},e.exports["Down fractals_input"]={en:["Down fractals"],fa:["Down fractals"]},e.exports["Up fractals_input"]={en:["Up fractals"],fa:["Up fractals"]},e.exports.Periods_input={en:["Periods"],fa:["Periods"]},e.exports.Shapes_input={en:["Shapes"],fa:["Shapes"]},e.exports["show MA_input"]={en:["show MA"],fa:["show MA"]},e.exports["MA Length_input"]={en:["MA Length"],fa:["MA Length"]},e.exports["Color based on previous close_input"]={en:["Color based on previous close"],fa:["Color based on previous close"]},e.exports["Rows Layout_input"]={en:["Rows Layout"],fa:["Rows Layout"]},e.exports["Row Size_input"]={en:["Row Size"],fa:["Row Size"]},e.exports.Volume_input={en:["Volume"],fa:["Volume"]},e.exports["Value Area volume_input"]={en:["Value Area volume"],fa:["Value Area volume"]},e.exports["Extend Right_input"]={en:["Extend Right"],fa:["Extend Right"]},e.exports["Extend POC Right_input"]={en:["Extend POC Right"],fa:["Extend POC Right"]},e.exports["Extend VAH Right_input"]={en:["Extend VAH Right"],fa:["Extend VAH Right"]},e.exports["Extend VAL Right_input"]={en:["Extend VAL Right"],fa:["Extend VAL Right"]},e.exports["Value Area Volume_input"]={en:["Value Area Volume"],fa:["Value Area Volume"]},e.exports.Placement_input={en:["Placement"],fa:["Placement"]},e.exports.POC_input={en:["POC"],fa:["POC"]},e.exports["Developing Poc_input"]={en:["Developing Poc"],fa:["Developing Poc"]},e.exports["Up Volume_input"]={en:["Up Volume"],fa:["Up Volume"]},e.exports["Down Volume_input"]={en:["Down Volume"],fa:["Down Volume"]},e.exports["Value Area_input"]={en:["Value Area"],fa:["Value Area"]},e.exports["Histogram Box_input"]={en:["Histogram Box"],fa:["Histogram Box"]},e.exports["Value Area Up_input"]={en:["Value Area Up"],fa:["Value Area Up"]},e.exports["Value Area Down_input"]={en:["Value Area Down"],fa:["Value Area Down"]},e.exports["Number Of Rows_input"]={en:["Number Of Rows"],fa:["Number Of Rows"]},e.exports["Ticks Per Row_input"]={en:["Ticks Per Row"],fa:["Ticks Per Row"]},e.exports["Up/Down_input"]={en:["Up/Down"],fa:["Up/Down"]},e.exports.Total_input={en:["Total"],fa:["Total"]},e.exports.Delta_input={en:["Delta"],fa:["Delta"]},e.exports.Bar_input={en:["Bar"],fa:["Bar"]},e.exports.Day_input={en:["Day"],fa:["Day"]},e.exports["Deviation (%)_input"]={en:["Deviation (%)"],fa:["Deviation (%)"]},e.exports.Depth_input={en:["Depth"],fa:["Depth"]},e.exports["Extend to last bar_input"]={en:["Extend to last bar"],fa:["Extend to last bar"]},e.exports.Simple_input={en:["Simple"],fa:["Simple"]},e.exports.Weighted_input={en:["Weighted"],fa:["Weighted"]},e.exports["Wilder's Smoothing_input"]={en:["Wilder's Smoothing"],fa:["Wilder's Smoothing"]},e.exports["1st Period_input"]={en:["1st Period"],fa:["1st Period"]},e.exports["2nd Period_input"]={en:["2nd Period"],fa:["2nd Period"]},e.exports["3rd Period_input"]={en:["3rd Period"],fa:["3rd Period"]},e.exports["4th Period_input"]={en:["4th Period"],fa:["4th Period"]
},e.exports["5th Period_input"]={en:["5th Period"],fa:["5th Period"]},e.exports["6th Period_input"]={en:["6th Period"],fa:["6th Period"]},e.exports["Rate of Change Lookback_input"]={en:["Rate of Change Lookback"],fa:["Rate of Change Lookback"]},e.exports["Instrument 1_input"]={en:["Instrument 1"],fa:["Instrument 1"]},e.exports["Instrument 2_input"]={en:["Instrument 2"],fa:["Instrument 2"]},e.exports["Rolling Period_input"]={en:["Rolling Period"],fa:["Rolling Period"]},e.exports["Standard Errors_input"]={en:["Standard Errors"],fa:["Standard Errors"]},e.exports["Averaging Periods_input"]={en:["Averaging Periods"],fa:["Averaging Periods"]},e.exports["Days Per Year_input"]={en:["Days Per Year"],fa:["Days Per Year"]},e.exports["Market Closed Percentage_input"]={en:["Market Closed Percentage"],fa:["Market Closed Percentage"]},e.exports["ATR Mult_input"]={en:["ATR Mult"],fa:["ATR Mult"]},e.exports.VWAP_input={en:["VWAP"],fa:["VWAP"]},e.exports["Anchor Period_input"]={en:["Anchor Period"],fa:["Anchor Period"]},e.exports.Session_input={en:["Session"],fa:["Session"]},e.exports.Week_input={en:["Week"],fa:["Week"]},e.exports.Month_input={en:["Month"],fa:["Month"]},e.exports.Year_input={en:["Year"],fa:["Year"]},e.exports.Decade_input={en:["Decade"],fa:["Decade"]},e.exports.Century_input={en:["Century"],fa:["Century"]},e.exports.Sessions_input={en:["Sessions"],fa:["Sessions"]},e.exports["Each (pre-market, market, post-market)_input"]={en:["Each (pre-market, market, post-market)"],fa:["Each (pre-market, market, post-market)"]},e.exports["Pre-market only_input"]={en:["Pre-market only"],fa:["Pre-market only"]},e.exports["Market only_input"]={en:["Market only"],fa:["Market only"]},e.exports["Post-market only_input"]={en:["Post-market only"],fa:["Post-market only"]},e.exports["Main chart symbol_input"]={en:["Main chart symbol"],fa:["Main chart symbol"]},e.exports["Another symbol_input"]={en:["Another symbol"],fa:["Another symbol"]},e.exports.Line_input={en:["Line"],fa:["خط"]},e.exports["Nothing selected_combobox_input"]={en:["Nothing selected"],fa:["Nothing selected"]},e.exports["All items_combobox_input"]={en:["All items"],fa:["All items"]},e.exports.Cancel_input={en:["Cancel"],fa:["Cancel"]},e.exports.Open_input={en:["Open"],fa:["Open"]},e.exports.MM_month_input={en:["MM"],fa:["MM"]},e.exports.YY_year_input={en:["YY"],fa:["YY"]},e.exports.Style_input={en:["Style"],fa:["Style"]},e.exports["Box size assignment method_input"]={en:["Box size assignment method"],fa:["Box size assignment method"]},e.exports["Color bars based on previous close_input"]={en:["Color bars based on previous close"],fa:["Color bars based on previous close"]},e.exports.Candles_input={en:["Candles"],fa:["Candles"]},e.exports.Borders_input={en:["Borders"],fa:["Borders"]},e.exports.Wick_input={en:["Wick"],fa:["Wick"]},e.exports["HLC bars_input"]={en:["HLC bars"],fa:["HLC bars"]},e.exports["Price source_input"]={en:["Price source"],fa:["Price source"]},e.exports.Type_input={en:["Type"],fa:["Type"]},
e.exports["Show real prices on price scale (instead of Heikin-Ashi price)_input"]={en:["Show real prices on price scale (instead of Heikin-Ashi price)"],fa:["Show real prices on price scale (instead of Heikin-Ashi price)"]},e.exports["Up bars_input"]={en:["Up bars"],fa:["Up bars"]},e.exports["Down bars_input"]={en:["Down bars"],fa:["Down bars"]},e.exports["Projection up bars_input"]={en:["Projection up bars"],fa:["Projection up bars"]},e.exports["Projection down bars_input"]={en:["Projection down bars"],fa:["Projection down bars"]},e.exports["Projection up color_input"]={en:["Projection up color"],fa:["Projection up color"]},e.exports["Projection down color_input"]={en:["Projection down color"],fa:["Projection down color"]},e.exports.Fill_input={en:["Fill"],fa:["Fill"]},e.exports["Up color_input"]={en:["Up color"],fa:["Up color"]},e.exports["Down color_input"]={en:["Down color"],fa:["Down color"]},e.exports.Traditional_input={en:["Traditional"],fa:["Traditional"]},e.exports.PercentageLTP_input={en:["PercentageLTP"],fa:["PercentageLTP"]},e.exports["Box size_input"]={en:["Box size"],fa:["Box size"]},e.exports["Number of line_input"]={en:["Number of line"],fa:["Number of line"]},e.exports["ATR length_input"]={en:["ATR length"],fa:["ATR length"]},e.exports.Percentage_input={en:["Percentage"],fa:["Percentage"]},e.exports["Reversal amount_input"]={en:["Reversal amount"],fa:["Reversal amount"]},e.exports["Phantom bars_input"]={en:["Phantom bars"],fa:["Phantom bars"]},e.exports["One step back building_input"]={en:["One step back building"],fa:["One step back building"]},e.exports.Wicks_input={en:["Wicks"],fa:["Wicks"]},e.exports.Range_input={en:["Range"],fa:["Range"]},e.exports.All_input={en:["All"],fa:["All"]},e.exports.Custom_input={en:["Custom"],fa:["Custom"]},e.exports["Leading Span Periods_input"]={en:["Leading Span Periods"],fa:["Lagging Span 2 Periods"]},e.exports["Lagging Span Periods_input"]={en:["Lagging Span Periods"],fa:["Lagging Span Periods"]},e.exports["Leading Shift Periods_input"]={en:["Leading Shift Periods"],fa:["Leading Shift Periods"]},e.exports["ATR({atrValue})_input"]={en:["ATR({atrValue})"],fa:["ATR({atrValue})"]},e.exports["PercentageLTP({percentageLTPValue}%)_input"]={en:["PercentageLTP({percentageLTPValue}%)"],fa:["PercentageLTP({percentageLTPValue}%)"]}},50873:e=>{e.exports={en:["ATR({atrValue})"],fa:["ATR({atrValue})"]}},28037:e=>{e.exports={en:["PercentageLTP({percentageLTPValue}%)"],fa:["PercentageLTP({percentageLTPValue}%)"]}},40566:e=>{e.exports={en:["Traditional"],fa:["Traditional"]}},75163:e=>{e.exports={en:["Invert scale"],fa:["Invert Scale"]}},35210:e=>{e.exports={en:["Indexed to 100"],fa:["Indexed to 100"]}},31340:e=>{e.exports={en:["Logarithmic"],fa:["Logarithmic"]}},19405:e=>{e.exports={en:["No overlapping labels"],fa:["No Overlapping Labels"]}},34954:e=>{e.exports={en:["Percent"],fa:["Percent"]}},55300:e=>{e.exports={en:["Regular"],fa:["Regular"]}},8029:e=>{e.exports={en:["ETH"],fa:["ETH"]}},34647:e=>{e.exports={en:["Electronic trading hours"],fa:["Electronic trading hours"]}},
36862:e=>{e.exports={en:["Extended trading hours"],fa:["Extended trading hours"]}},7807:e=>{e.exports={en:["POST"],fa:["POST"]}},46273:e=>{e.exports={en:["PRE"],fa:["PRE"]}},50434:e=>{e.exports={en:["Postmarket"],fa:["Postmarket"]}},59330:e=>{e.exports={en:["Premarket"],fa:["Premarket"]}},35342:e=>{e.exports={en:["RTH"],fa:["RTH"]}},84246:e=>{e.exports={en:["Regular trading hours"],fa:["Regular trading hours"]}},13132:e=>{e.exports={en:["May"],fa:["می"]}},83477:e=>{e.exports=Object.create(null),e.exports.Technicals_study={en:["Technicals"],fa:["Technicals"]},e.exports["Average Day Range_study"]={en:["Average Day Range"],fa:["Average Day Range"]},e.exports["Bull Bear Power_study"]={en:["Bull Bear Power"],fa:["Bull Bear Power"]},e.exports["Capital expenditures_study"]={en:["Capital expenditures"],fa:["Capital expenditures"]},e.exports["Cash to debt ratio_study"]={en:["Cash to debt ratio"],fa:["Cash to debt ratio"]},e.exports["Debt to EBITDA ratio_study"]={en:["Debt to EBITDA ratio"],fa:["Debt to EBITDA ratio"]},e.exports["Directional Movement Index_study"]={en:["Directional Movement Index"],fa:["Directional Movement Index"]},e.exports.DMI_study={en:["DMI"],fa:["DMI"]},e.exports["Dividend payout ratio %_study"]={en:["Dividend payout ratio %"],fa:["Dividend payout ratio %"]},e.exports["Equity to assets ratio_study"]={en:["Equity to assets ratio"],fa:["Equity to assets ratio"]},e.exports["Enterprise value to EBIT ratio_study"]={en:["Enterprise value to EBIT ratio"],fa:["Enterprise value to EBIT ratio"]},e.exports["Enterprise value to EBITDA ratio_study"]={en:["Enterprise value to EBITDA ratio"],fa:["Enterprise value to EBITDA ratio"]},e.exports["Enterprise value to revenue ratio_study"]={en:["Enterprise value to revenue ratio"],fa:["Enterprise value to revenue ratio"]},e.exports["Goodwill, net_study"]={en:["Goodwill, net"],fa:["Goodwill, net"]},e.exports["Ichimoku Cloud_study"]={en:["Ichimoku Cloud"],fa:["Ichimoku Cloud"]},e.exports.Ichimoku_study={en:["Ichimoku"],fa:["Ichimoku"]},e.exports["Moving Average Convergence Divergence_study"]={en:["Moving Average Convergence Divergence"],fa:["Moving Average Convergence Divergence"]},e.exports["Operating income_study"]={en:["Operating income"],fa:["Operating income"]},e.exports["Price to book ratio_study"]={en:["Price to book ratio"],fa:["Price to book ratio"]},e.exports["Price to cash flow ratio_study"]={en:["Price to cash flow ratio"],fa:["Price to cash flow ratio"]},e.exports["Price to earnings ratio_study"]={en:["Price to earnings ratio"],fa:["Price to earnings ratio"]},e.exports["Price to free cash flow ratio_study"]={en:["Price to free cash flow ratio"],fa:["Price to free cash flow ratio"]},e.exports["Price to sales ratio_study"]={en:["Price to sales ratio"],fa:["Price to sales ratio"]},e.exports["Float shares outstanding_study"]={en:["Float shares outstanding"],fa:["Float shares outstanding"]},e.exports["Total common shares outstanding_study"]={en:["Total common shares outstanding"],fa:["Total common shares outstanding"]},e.exports["Volume Weighted Average Price_study"]={
en:["Volume Weighted Average Price"],fa:["Volume Weighted Average Price"]},e.exports["Volume Weighted Moving Average_study"]={en:["Volume Weighted Moving Average"],fa:["Volume Weighted Moving Average"]},e.exports["Williams Percent Range_study"]={en:["Williams Percent Range"],fa:["Williams Percent Range"]},e.exports.Doji_study={en:["Doji"],fa:["Doji"]},e.exports["Spinning Top Black_study"]={en:["Spinning Top Black"],fa:["Spinning Top Black"]},e.exports["Spinning Top White_study"]={en:["Spinning Top White"],fa:["Spinning Top White"]},e.exports["Accounts payable_study"]={en:["Accounts payable"],fa:["Accounts payable"]},e.exports["Accounts receivables, gross_study"]={en:["Accounts receivables, gross"],fa:["Accounts receivables, gross"]},e.exports["Accounts receivable - trade, net_study"]={en:["Accounts receivable - trade, net"],fa:["Accounts receivable - trade, net"]},e.exports.Accruals_study={en:["Accruals"],fa:["Accruals"]},e.exports["Accrued payroll_study"]={en:["Accrued payroll"],fa:["Accrued payroll"]},e.exports["Accumulated depreciation, total_study"]={en:["Accumulated depreciation, total"],fa:["Accumulated depreciation, total"]},e.exports["Additional paid-in capital/Capital surplus_study"]={en:["Additional paid-in capital/Capital surplus"],fa:["Additional paid-in capital/Capital surplus"]},e.exports["After tax other income/expense_study"]={en:["After tax other income/expense"],fa:["After tax other income/expense"]},e.exports["Altman Z-score_study"]={en:["Altman Z-score"],fa:["Altman Z-score"]},e.exports.Amortization_study={en:["Amortization"],fa:["Amortization"]},e.exports["Amortization of intangibles_study"]={en:["Amortization of intangibles"],fa:["Amortization of intangibles"]},e.exports["Amortization of deferred charges_study"]={en:["Amortization of deferred charges"],fa:["Amortization of deferred charges"]},e.exports["Asset turnover_study"]={en:["Asset turnover"],fa:["Asset turnover"]},e.exports["Average basic shares outstanding_study"]={en:["Average basic shares outstanding"],fa:["Average basic shares outstanding"]},e.exports["Bad debt / Doubtful accounts_study"]={en:["Bad debt / Doubtful accounts"],fa:["Bad debt / Doubtful accounts"]},e.exports["Basic EPS_study"]={en:["Basic EPS"],fa:["Basic EPS"]},e.exports["Basic earnings per share (Basic EPS)_study"]={en:["Basic earnings per share (Basic EPS)"],fa:["Basic earnings per share (Basic EPS)"]},e.exports["Beneish M-score_study"]={en:["Beneish M-score"],fa:["Beneish M-score"]},e.exports["Book value per share_study"]={en:["Book value per share"],fa:["Book value per share"]},e.exports["Buyback yield %_study"]={en:["Buyback yield %"],fa:["Buyback yield %"]},e.exports["Capital and operating lease obligations_study"]={en:["Capital and operating lease obligations"],fa:["Capital and operating lease obligations"]},e.exports["Capital expenditures - fixed assets_study"]={en:["Capital expenditures - fixed assets"],fa:["Capital expenditures - fixed assets"]},e.exports["Capital expenditures - other assets_study"]={en:["Capital expenditures - other assets"],
fa:["Capital expenditures - other assets"]},e.exports["Capitalized lease obligations_study"]={en:["Capitalized lease obligations"],fa:["Capitalized lease obligations"]},e.exports["Cash and short term investments_study"]={en:["Cash and short term investments"],fa:["Cash and short term investments"]},e.exports["Cash conversion cycle_study"]={en:["Cash conversion cycle"],fa:["Cash conversion cycle"]},e.exports["Cash & equivalents_study"]={en:["Cash & equivalents"],fa:["Cash & equivalents"]},e.exports["Cash from financing activities_study"]={en:["Cash from financing activities"],fa:["Cash from financing activities"]},e.exports["Cash from investing activities_study"]={en:["Cash from investing activities"],fa:["Cash from investing activities"]},e.exports["Cash from operating activities_study"]={en:["Cash from operating activities"],fa:["Cash from operating activities"]},e.exports["Change in accounts payable_study"]={en:["Change in accounts payable"],fa:["Change in accounts payable"]},e.exports["Change in accounts receivable_study"]={en:["Change in accounts receivable"],fa:["Change in accounts receivable"]},e.exports["Change in accrued expenses_study"]={en:["Change in accrued expenses"],fa:["Change in accrued expenses"]},e.exports["Change in inventories_study"]={en:["Change in inventories"],fa:["Change in inventories"]},e.exports["Change in other assets/liabilities_study"]={en:["Change in other assets/liabilities"],fa:["Change in other assets/liabilities"]},e.exports["Change in taxes payable_study"]={en:["Change in taxes payable"],fa:["Change in taxes payable"]},e.exports["Changes in working capital_study"]={en:["Changes in working capital"],fa:["Changes in working capital"]},e.exports["COGS to revenue ratio_study"]={en:["COGS to revenue ratio"],fa:["COGS to revenue ratio"]},e.exports["Common dividends paid_study"]={en:["Common dividends paid"],fa:["Common dividends paid"]},e.exports["Common equity, total_study"]={en:["Common equity, total"],fa:["Common equity, total"]},e.exports["Common stock par/Carrying value_study"]={en:["Common stock par/Carrying value"],fa:["Common stock par/Carrying value"]},e.exports["Cost of goods_study"]={en:["Cost of goods"],fa:["Cost of goods"]},e.exports["Cost of goods sold_study"]={en:["Cost of goods sold"],fa:["Cost of goods sold"]},e.exports["Current portion of LT debt and capital leases_study"]={en:["Current portion of LT debt and capital leases"],fa:["Current portion of LT debt and capital leases"]},e.exports["Current ratio_study"]={en:["Current ratio"],fa:["Current ratio"]},e.exports["Days inventory_study"]={en:["Days inventory"],fa:["Days inventory"]},e.exports["Days payable_study"]={en:["Days payable"],fa:["Days payable"]},e.exports["Days sales outstanding_study"]={en:["Days sales outstanding"],fa:["Days sales outstanding"]},e.exports["Debt to assets ratio_study"]={en:["Debt to assets ratio"],fa:["Debt to assets ratio"]},e.exports["Debt to equity ratio_study"]={en:["Debt to equity ratio"],fa:["Debt to equity ratio"]},e.exports["Debt to revenue ratio_study"]={
en:["Debt to revenue ratio"],fa:["Debt to revenue ratio"]},e.exports["Deferred income, current_study"]={en:["Deferred income, current"],fa:["Deferred income, current"]},e.exports["Deferred income, non-current_study"]={en:["Deferred income, non-current"],fa:["Deferred income, non-current"]},e.exports["Deferred tax assets_study"]={en:["Deferred tax assets"],fa:["Deferred tax assets"]},e.exports["Deferred taxes (cash flow)_study"]={en:["Deferred taxes (cash flow)"],fa:["Deferred taxes (cash flow)"]},e.exports["Deferred tax liabilities_study"]={en:["Deferred tax liabilities"],fa:["Deferred tax liabilities"]},e.exports.Depreciation_study={en:["Depreciation"],fa:["Depreciation"]},e.exports["Deprecation and amortization_study"]={en:["Deprecation and amortization"],fa:["Deprecation and amortization"]},e.exports["Depreciation & amortization (cash flow)_study"]={en:["Depreciation & amortization (cash flow)"],fa:["Depreciation & amortization (cash flow)"]},e.exports["Depreciation/depletion_study"]={en:["Depreciation/depletion"],fa:["Depreciation/depletion"]},e.exports["Diluted EPS_study"]={en:["Diluted EPS"],fa:["Diluted EPS"]},e.exports["Diluted earnings per share (Diluted EPS)_study"]={en:["Diluted earnings per share (Diluted EPS)"],fa:["Diluted earnings per share (Diluted EPS)"]},e.exports["Diluted net income available to common stockholders_study"]={en:["Diluted net income available to common stockholders"],fa:["Diluted net income available to common stockholders"]},e.exports["Diluted shares outstanding_study"]={en:["Diluted shares outstanding"],fa:["Diluted shares outstanding"]},e.exports["Dilution adjustment_study"]={en:["Dilution adjustment"],fa:["Dilution adjustment"]},e.exports["Discontinued operations_study"]={en:["Discontinued operations"],fa:["Discontinued operations"]},e.exports["Dividends payable_study"]={en:["Dividends payable"],fa:["Dividends payable"]},e.exports["Dividends per share - common stock primary issue_study"]={en:["Dividends per share - common stock primary issue"],fa:["Dividends per share - common stock primary issue"]},e.exports["Dividend yield %_study"]={en:["Dividend yield %"],fa:["Dividend yield %"]},e.exports["Earnings yield_study"]={en:["Earnings yield"],fa:["Earnings yield"]},e.exports.EBIT_study={en:["EBIT"],fa:["EBIT"]},e.exports.EBITDA_study={en:["EBITDA"],fa:["EBITDA"]},e.exports["EBITDA margin %_study"]={en:["EBITDA margin %"],fa:["EBITDA margin %"]},e.exports["Effective interest rate on debt %_study"]={en:["Effective interest rate on debt %"],fa:["Effective interest rate on debt %"]},e.exports["Enterprise value_study"]={en:["Enterprise value"],fa:["Enterprise value"]},e.exports["EPS basic one year growth_study"]={en:["EPS basic one year growth"],fa:["EPS basic one year growth"]},e.exports["EPS diluted one year growth_study"]={en:["EPS diluted one year growth"],fa:["EPS diluted one year growth"]},e.exports["EPS estimates_study"]={en:["EPS estimates"],fa:["EPS estimates"]},e.exports["Equity in earnings_study"]={en:["Equity in earnings"],fa:["Equity in earnings"]},
e.exports["Financing activities – other sources_study"]={en:["Financing activities – other sources"],fa:["Financing activities – other sources"]},e.exports["Financing activities – other uses_study"]={en:["Financing activities – other uses"],fa:["Financing activities – other uses"]},e.exports["Free cash flow_study"]={en:["Free cash flow"],fa:["Free cash flow"]},e.exports["Free cash flow margin %_study"]={en:["Free cash flow margin %"],fa:["Free cash flow margin %"]},e.exports["Fulmer H factor_study"]={en:["Fulmer H factor"],fa:["Fulmer H factor"]},e.exports["Funds from operations_study"]={en:["Funds from operations"],fa:["Funds from operations"]},e.exports["Goodwill to assets ratio_study"]={en:["Goodwill to assets ratio"],fa:["Goodwill to assets ratio"]},e.exports["Graham's number_study"]={en:["Graham's number"],fa:["Graham's number"]},e.exports["Gross margin %_study"]={en:["Gross margin %"],fa:["Gross margin %"]},e.exports["Gross profit_study"]={en:["Gross profit"],fa:["Gross profit"]},e.exports["Gross profit to assets ratio_study"]={en:["Gross profit to assets ratio"],fa:["Gross profit to assets ratio"]},e.exports["Gross property/plant/equipment_study"]={en:["Gross property/plant/equipment"],fa:["Gross property/plant/equipment"]},e.exports.Impairments_study={en:["Impairments"],fa:["Impairments"]},e.exports["Income Tax Credits_study"]={en:["Income Tax Credits"],fa:["Income Tax Credits"]},e.exports["Income tax, current_study"]={en:["Income tax, current"],fa:["Income tax, current"]},e.exports["Income tax, current - domestic_study"]={en:["Income tax, current - domestic"],fa:["Income tax, current - domestic"]},e.exports["Income Tax, current - foreign_study"]={en:["Income Tax, current - foreign"],fa:["Income Tax, current - foreign"]},e.exports["Income tax, deferred_study"]={en:["Income tax, deferred"],fa:["Income tax, deferred"]},e.exports["Income tax, deferred - domestic_study"]={en:["Income tax, deferred - domestic"],fa:["Income tax, deferred - domestic"]},e.exports["Income tax, deferred - foreign_study"]={en:["Income tax, deferred - foreign"],fa:["Income tax, deferred - foreign"]},e.exports["Income tax payable_study"]={en:["Income tax payable"],fa:["Income tax payable"]},e.exports["Interest capitalized_study"]={en:["Interest capitalized"],fa:["Interest capitalized"]},e.exports["Interest coverage_study"]={en:["Interest coverage"],fa:["Interest coverage"]},e.exports["Interest expense, net of interest capitalized_study"]={en:["Interest expense, net of interest capitalized"],fa:["Interest expense, net of interest capitalized"]},e.exports["Interest expense on debt_study"]={en:["Interest expense on debt"],fa:["Interest expense on debt"]},e.exports["Inventories - finished goods_study"]={en:["Inventories - finished goods"],fa:["Inventories - finished goods"]},e.exports["Inventories - progress payments & other_study"]={en:["Inventories - progress payments & other"],fa:["Inventories - progress payments & other"]},e.exports["Inventories - raw materials_study"]={en:["Inventories - raw materials"],fa:["Inventories - raw materials"]
},e.exports["Inventories - work in progress_study"]={en:["Inventories - work in progress"],fa:["Inventories - work in progress"]},e.exports["Inventory to revenue ratio_study"]={en:["Inventory to revenue ratio"],fa:["Inventory to revenue ratio"]},e.exports["Inventory turnover_study"]={en:["Inventory turnover"],fa:["Inventory turnover"]},e.exports["Investing activities – other sources_study"]={en:["Investing activities – other sources"],fa:["Investing activities – other sources"]},e.exports["Investing activities – other uses_study"]={en:["Investing activities – other uses"],fa:["Investing activities – other uses"]},e.exports["Investments in unconsolidated subsidiaries_study"]={en:["Investments in unconsolidated subsidiaries"],fa:["Investments in unconsolidated subsidiaries"]},e.exports["Issuance of long term debt_study"]={en:["Issuance of long term debt"],fa:["Issuance of long term debt"]},e.exports["Issuance/retirement of debt, net_study"]={en:["Issuance/retirement of debt, net"],fa:["Issuance/retirement of debt, net"]},e.exports["Issuance/retirement of long term debt_study"]={en:["Issuance/retirement of long term debt"],fa:["Issuance/retirement of long term debt"]},e.exports["Issuance/retirement of other debt_study"]={en:["Issuance/retirement of other debt"],fa:["Issuance/retirement of other debt"]},e.exports["Issuance/retirement of short term debt_study"]={en:["Issuance/retirement of short term debt"],fa:["Issuance/retirement of short term debt"]},e.exports["Issuance/retirement of stock, net_study"]={en:["Issuance/retirement of stock, net"],fa:["Issuance/retirement of stock, net"]},e.exports["KZ index_study"]={en:["KZ index"],fa:["KZ index"]},e.exports["Legal claim expense_study"]={en:["Legal claim expense"],fa:["Legal claim expense"]},e.exports["Long term debt_study"]={en:["Long term debt"],fa:["Long term debt"]},e.exports["Long term debt excl. lease liabilities_study"]={en:["Long term debt excl. lease liabilities"],fa:["Long term debt excl. lease liabilities"]},e.exports["Long term debt to total assets ratio_study"]={en:["Long term debt to total assets ratio"],fa:["Long term debt to total assets ratio"]},e.exports["Long term debt to total equity ratio_study"]={en:["Long term debt to total equity ratio"],fa:["Long term debt to total equity ratio"]},e.exports["Long term investments_study"]={en:["Long term investments"],fa:["Long term investments"]},e.exports["Market capitalization_study"]={en:["Market capitalization"],fa:["Market capitalization"]},e.exports["Minority interest_study"]={en:["Minority interest"],fa:["Minority interest"]},e.exports["Miscellaneous non-operating expense_study"]={en:["Miscellaneous non-operating expense"],fa:["Miscellaneous non-operating expense"]},e.exports["Net current asset value per share_study"]={en:["Net current asset value per share"],fa:["Net current asset value per share"]},e.exports["Net debt_study"]={en:["Net debt"],fa:["Net debt"]},e.exports["Net income_study"]={en:["Net income"],fa:["Net income"]},e.exports["Net income before discontinued operations_study"]={
en:["Net income before discontinued operations"],fa:["Net income before discontinued operations"]},e.exports["Net income (cash flow)_study"]={en:["Net income (cash flow)"],fa:["Net income (cash flow)"]},e.exports["Net income per employee_study"]={en:["Net income per employee"],fa:["Net income per employee"]},e.exports["Net intangible assets_study"]={en:["Net intangible assets"],fa:["Net intangible assets"]},e.exports["Net margin %_study"]={en:["Net margin %"],fa:["Net margin %"]},e.exports["Net property/plant/equipment_study"]={en:["Net property/plant/equipment"],fa:["Net property/plant/equipment"]},e.exports["Non-cash items_study"]={en:["Non-cash items"],fa:["Non-cash items"]},e.exports["Non-controlling/minority interest_study"]={en:["Non-controlling/minority interest"],fa:["Non-controlling/minority interest"]},e.exports["Non-operating income, excl. interest expenses_study"]={en:["Non-operating income, excl. interest expenses"],fa:["Non-operating income, excl. interest expenses"]},e.exports["Non-operating income, total_study"]={en:["Non-operating income, total"],fa:["Non-operating income, total"]},e.exports["Non-operating interest income_study"]={en:["Non-operating interest income"],fa:["Non-operating interest income"]},e.exports["Note receivable - long term_study"]={en:["Note receivable - long term"],fa:["Note receivable - long term"]},e.exports["Notes payable_study"]={en:["Notes payable"],fa:["Notes payable"]},e.exports["Number of employees_study"]={en:["Number of employees"],fa:["Number of employees"]},e.exports["Number of shareholders_study"]={en:["Number of shareholders"],fa:["Number of shareholders"]},e.exports["Operating earnings yield %_study"]={en:["Operating earnings yield %"],fa:["Operating earnings yield %"]},e.exports["Operating expenses (excl. COGS)_study"]={en:["Operating expenses (excl. COGS)"],fa:["Operating expenses (excl. COGS)"]},e.exports["Operating lease liabilities_study"]={en:["Operating lease liabilities"],fa:["Operating lease liabilities"]},e.exports["Operating margin %_study"]={en:["Operating margin %"],fa:["Operating margin %"]},e.exports["Other COGS_study"]={en:["Other COGS"],fa:["Other COGS"]},e.exports["Other common equity_study"]={en:["Other common equity"],fa:["Other common equity"]},e.exports["Other current assets, total_study"]={en:["Other current assets, total"],fa:["Other current assets, total"]},e.exports["Other current liabilities_study"]={en:["Other current liabilities"],fa:["Other current liabilities"]},e.exports["Other cost of goods sold_study"]={en:["Other cost of goods sold"],fa:["Other cost of goods sold"]},e.exports["Other exceptional charges_study"]={en:["Other exceptional charges"],fa:["Other exceptional charges"]},e.exports["Other financing cash flow items, total_study"]={en:["Other financing cash flow items, total"],fa:["Other financing cash flow items, total"]},e.exports["Other intangibles, net_study"]={en:["Other intangibles, net"],fa:["Other intangibles, net"]},e.exports["Other investing cash flow items, total_study"]={
en:["Other investing cash flow items, total"],fa:["Other investing cash flow items, total"]},e.exports["Other investments_study"]={en:["Other investments"],fa:["Other investments"]},e.exports["Other liabilities, total_study"]={en:["Other liabilities, total"],fa:["Other liabilities, total"]},e.exports["Other long term assets, total_study"]={en:["Other long term assets, total"],fa:["Other long term assets, total"]},e.exports["Other non-current liabilities, total_study"]={en:["Other non-current liabilities, total"],fa:["Other non-current liabilities, total"]},e.exports["Other operating expenses, total_study"]={en:["Other operating expenses, total"],fa:["Other operating expenses, total"]},e.exports["Other receivables_study"]={en:["Other receivables"],fa:["Other receivables"]},e.exports["Other short term debt_study"]={en:["Other short term debt"],fa:["Other short term debt"]},e.exports["Paid in capital_study"]={en:["Paid in capital"],fa:["Paid in capital"]},e.exports["PEG ratio_study"]={en:["PEG ratio"],fa:["PEG ratio"]},e.exports["Piotroski F-score_study"]={en:["Piotroski F-score"],fa:["Piotroski F-score"]},e.exports["Preferred dividends_study"]={en:["Preferred dividends"],fa:["Preferred dividends"]},e.exports["Preferred dividends paid_study"]={en:["Preferred dividends paid"],fa:["Preferred dividends paid"]},e.exports["Preferred stock, carrying value_study"]={en:["Preferred stock, carrying value"],fa:["Preferred stock, carrying value"]},e.exports["Prepaid expenses_study"]={en:["Prepaid expenses"],fa:["Prepaid expenses"]},e.exports["Pretax equity in earnings_study"]={en:["Pretax equity in earnings"],fa:["Pretax equity in earnings"]},e.exports["Pretax income_study"]={en:["Pretax income"],fa:["Pretax income"]},e.exports["Price earnings ratio forward_study"]={en:["Price earnings ratio forward"],fa:["Price earnings ratio forward"]},e.exports["Price sales ratio forward_study"]={en:["Price sales ratio forward"],fa:["Price sales ratio forward"]},e.exports["Price to tangible book ratio_study"]={en:["Price to tangible book ratio"],fa:["Price to tangible book ratio"]},e.exports["Provision for risks & charge_study"]={en:["Provision for risks & charge"],fa:["Provision for risks & charge"]},e.exports["Purchase/acquisition of business_study"]={en:["Purchase/acquisition of business"],fa:["Purchase/acquisition of business"]},e.exports["Purchase of investments_study"]={en:["Purchase of investments"],fa:["Purchase of investments"]},e.exports["Purchase/sale of business, net_study"]={en:["Purchase/sale of business, net"],fa:["Purchase/sale of business, net"]},e.exports["Purchase/sale of investments, net_study"]={en:["Purchase/sale of investments, net"],fa:["Purchase/sale of investments, net"]},e.exports["Quality ratio_study"]={en:["Quality ratio"],fa:["Quality ratio"]},e.exports["Quick ratio_study"]={en:["Quick ratio"],fa:["Quick ratio"]},e.exports["Reduction of long term debt_study"]={en:["Reduction of long term debt"],fa:["Reduction of long term debt"]},e.exports["Repurchase of common & preferred stock_study"]={
en:["Repurchase of common & preferred stock"],fa:["Repurchase of common & preferred stock"]},e.exports["Research & development_study"]={en:["Research & development"],fa:["Research & development"]},e.exports["Research & development to revenue ratio_study"]={en:["Research & development to revenue ratio"],fa:["Research & development to revenue ratio"]},e.exports["Restructuring charge_study"]={en:["Restructuring charge"],fa:["Restructuring charge"]},e.exports["Retained earnings_study"]={en:["Retained earnings"],fa:["Retained earnings"]},e.exports["Return on assets %_study"]={en:["Return on assets %"],fa:["Return on assets %"]},e.exports["Return on equity %_study"]={en:["Return on equity %"],fa:["Return on equity %"]},e.exports["Return on equity adjusted to book value %_study"]={en:["Return on equity adjusted to book value %"],fa:["Return on equity adjusted to book value %"]},e.exports["Return on invested capital %_study"]={en:["Return on invested capital %"],fa:["Return on invested capital %"]},e.exports["Return on tangible assets %_study"]={en:["Return on tangible assets %"],fa:["Return on tangible assets %"]},e.exports["Return on tangible equity %_study"]={en:["Return on tangible equity %"],fa:["Return on tangible equity %"]},e.exports["Revenue estimates_study"]={en:["Revenue estimates"],fa:["Revenue estimates"]},e.exports["Revenue one year growth_study"]={en:["Revenue one year growth"],fa:["Revenue one year growth"]},e.exports["Revenue per employee_study"]={en:["Revenue per employee"],fa:["Revenue per employee"]},e.exports["Sale/maturity of investments_study"]={en:["Sale/maturity of investments"],fa:["Sale/maturity of investments"]},e.exports["Sale of common & preferred stock_study"]={en:["Sale of common & preferred stock"],fa:["Sale of common & preferred stock"]},e.exports["Sale of fixed assets & businesses_study"]={en:["Sale of fixed assets & businesses"],fa:["Sale of fixed assets & businesses"]},e.exports["Selling/general/admin expenses, other_study"]={en:["Selling/general/admin expenses, other"],fa:["Selling/general/admin expenses, other"]},e.exports["Selling/general/admin expenses, total_study"]={en:["Selling/general/admin expenses, total"],fa:["Selling/general/admin expenses, total"]},e.exports["Shareholders' equity_study"]={en:["Shareholders' equity"],fa:["Shareholders' equity"]},e.exports["Shares buyback ratio %_study"]={en:["Shares buyback ratio %"],fa:["Shares buyback ratio %"]},e.exports["Short term debt_study"]={en:["Short term debt"],fa:["Short term debt"]},e.exports["Short term debt excl. current portion of LT debt_study"]={en:["Short term debt excl. current portion of LT debt"],fa:["Short term debt excl. current portion of LT debt"]},e.exports["Short term investments_study"]={en:["Short term investments"],fa:["Short term investments"]},e.exports["Sloan ratio %_study"]={en:["Sloan ratio %"],fa:["Sloan ratio %"]},e.exports["Springate score_study"]={en:["Springate score"],fa:["Springate score"]},e.exports["Sustainable growth rate_study"]={en:["Sustainable growth rate"],fa:["Sustainable growth rate"]},
e.exports["Tangible book value per share_study"]={en:["Tangible book value per share"],fa:["Tangible book value per share"]},e.exports["Tangible common equity ratio_study"]={en:["Tangible common equity ratio"],fa:["Tangible common equity ratio"]},e.exports.Taxes_study={en:["Taxes"],fa:["Taxes"]},e.exports["Tobin's Q (approximate)_study"]={en:["Tobin's Q (approximate)"],fa:["Tobin's Q (approximate)"]},e.exports["Total assets_study"]={en:["Total assets"],fa:["Total assets"]},e.exports["Total cash dividends paid_study"]={en:["Total cash dividends paid"],fa:["Total cash dividends paid"]},e.exports["Total current assets_study"]={en:["Total current assets"],fa:["Total current assets"]},e.exports["Total current liabilities_study"]={en:["Total current liabilities"],fa:["Total current liabilities"]},e.exports["Total debt_study"]={en:["Total debt"],fa:["Total debt"]},e.exports["Total equity_study"]={en:["Total equity"],fa:["Total equity"]},e.exports["Total inventory_study"]={en:["Total inventory"],fa:["Total inventory"]},e.exports["Total liabilities_study"]={en:["Total liabilities"],fa:["Total liabilities"]},e.exports["Total liabilities & shareholders' equities_study"]={en:["Total liabilities & shareholders' equities"],fa:["Total liabilities & shareholders' equities"]},e.exports["Total non-current assets_study"]={en:["Total non-current assets"],fa:["Total non-current assets"]},e.exports["Total non-current liabilities_study"]={en:["Total non-current liabilities"],fa:["Total non-current liabilities"]},e.exports["Total operating expenses_study"]={en:["Total operating expenses"],fa:["Total operating expenses"]},e.exports["Total receivables, net_study"]={en:["Total receivables, net"],fa:["Total receivables, net"]},e.exports["Total revenue_study"]={en:["Total revenue"],fa:["Total revenue"]},e.exports["Treasury stock - common_study"]={en:["Treasury stock - common"],fa:["Treasury stock - common"]},e.exports["Unrealized gain/loss_study"]={en:["Unrealized gain/loss"],fa:["Unrealized gain/loss"]},e.exports["Unusual income/expense_study"]={en:["Unusual income/expense"],fa:["Unusual income/expense"]},e.exports["Zmijewski score_study"]={en:["Zmijewski score"],fa:["Zmijewski score"]},e.exports["Valuation ratios_study"]={en:["Valuation ratios"],fa:["Valuation ratios"]},e.exports["Profitability ratios_study"]={en:["Profitability ratios"],fa:["Profitability ratios"]},e.exports["Liquidity ratios_study"]={en:["Liquidity ratios"],fa:["Liquidity ratios"]},e.exports["Solvency ratios_study"]={en:["Solvency ratios"],fa:["Solvency ratios"]},e.exports["Key stats_study"]={en:["Key stats"],fa:["Key stats"]},e.exports["Accumulation/Distribution_study"]={en:["Accumulation/Distribution"],fa:["Accumulation/Distribution"]},e.exports["Accumulative Swing Index_study"]={en:["Accumulative Swing Index"],fa:["Accumulative Swing Index"]},e.exports["Advance/Decline_study"]={en:["Advance/Decline"],fa:["Advance/Decline"]},e.exports["All Chart Patterns_study"]={en:["All Chart Patterns"],fa:["All Chart Patterns"]},e.exports["Arnaud Legoux Moving Average_study"]={
en:["Arnaud Legoux Moving Average"],fa:["Arnaud Legoux Moving Average"]},e.exports.Aroon_study={en:["Aroon"],fa:["Aroon"]},e.exports.ASI_study={en:["ASI"],fa:["ASI"]},e.exports["Average Directional Index_study"]={en:["Average Directional Index"],fa:["Average Directional Index"]},e.exports["Average True Range_study"]={en:["Average True Range"],fa:["Average True Range"]},e.exports["Awesome Oscillator_study"]={en:["Awesome Oscillator"],fa:["Awesome Oscillator"]},e.exports["Balance of Power_study"]={en:["Balance of Power"],fa:["Balance of Power"]},e.exports["Bollinger Bands %B_study"]={en:["Bollinger Bands %B"],fa:["نوارهای بولینگر %B"]},e.exports["Bollinger Bands Width_study"]={en:["Bollinger Bands Width"],fa:["Bollinger Bands Width"]},e.exports["Bollinger Bands_study"]={en:["Bollinger Bands"],fa:["نوارهای بولینگر"]},e.exports["Chaikin Money Flow_study"]={en:["Chaikin Money Flow"],fa:["Chaikin Money Flow"]},e.exports["Chaikin Oscillator_study"]={en:["Chaikin Oscillator"],fa:["Chaikin Oscillator"]},e.exports["Chande Kroll Stop_study"]={en:["Chande Kroll Stop"],fa:["Chande Kroll Stop"]},e.exports["Chande Momentum Oscillator_study"]={en:["Chande Momentum Oscillator"],fa:["Chande Momentum Oscillator"]},e.exports["Chop Zone_study"]={en:["Chop Zone"],fa:["Chop Zone"]},e.exports["Choppiness Index_study"]={en:["Choppiness Index"],fa:["Choppiness Index"]},e.exports["Commodity Channel Index_study"]={en:["Commodity Channel Index"],fa:["Commodity Channel Index"]},e.exports["Connors RSI_study"]={en:["Connors RSI"],fa:["Connors RSI"]},e.exports["Coppock Curve_study"]={en:["Coppock Curve"],fa:["Coppock Curve"]},e.exports["Correlation Coefficient_study"]={en:["Correlation Coefficient"],fa:["Correlation Coefficient"]},e.exports.CRSI_study={en:["CRSI"],fa:["CRSI"]},e.exports["Detrended Price Oscillator_study"]={en:["Detrended Price Oscillator"],fa:["Detrended Price Oscillator"]},e.exports["Directional Movement_study"]={en:["Directional Movement"],fa:["Directional Movement"]},e.exports["Donchian Channels_study"]={en:["Donchian Channels"],fa:["Donchian Channels"]},e.exports["Double EMA_study"]={en:["Double EMA"],fa:["Double EMA"]},e.exports["Ease Of Movement_study"]={en:["Ease Of Movement"],fa:["Ease Of Movement"]},e.exports["Elder Force Index_study"]={en:["Elder Force Index"],fa:["Elder's Force Index"]},e.exports["EMA Cross_study"]={en:["EMA Cross"],fa:["EMA Cross"]},e.exports.Envelopes_study={en:["Envelopes"],fa:["Envelopes"]},e.exports["Fisher Transform_study"]={en:["Fisher Transform"],fa:["Fisher Transform"]},e.exports["Fixed Range_study"]={en:["Fixed Range"],fa:["Fixed Range"]},e.exports["Fixed Range Volume Profile_study"]={en:["Fixed Range Volume Profile"],fa:["Fixed Range Volume Profile"]},e.exports["Guppy Multiple Moving Average_study"]={en:["Guppy Multiple Moving Average"],fa:["Guppy Multiple Moving Average"]},e.exports["Historical Volatility_study"]={en:["Historical Volatility"],fa:["Historical Volatility"]},e.exports["Hull Moving Average_study"]={en:["Hull Moving Average"],fa:["Hull Moving Average"]},
e.exports["Keltner Channels_study"]={en:["Keltner Channels"],fa:["Keltner Channels"]},e.exports["Klinger Oscillator_study"]={en:["Klinger Oscillator"],fa:["Klinger Oscillator"]},e.exports["Know Sure Thing_study"]={en:["Know Sure Thing"],fa:["Know Sure Thing"]},e.exports["Least Squares Moving Average_study"]={en:["Least Squares Moving Average"],fa:["Least Squares Moving Average"]},e.exports["Linear Regression Curve_study"]={en:["Linear Regression Curve"],fa:["Linear Regression Curve"]},e.exports["MA Cross_study"]={en:["MA Cross"],fa:["تقاطع میانگین متحرک"]},e.exports["MA with EMA Cross_study"]={en:["MA with EMA Cross"],fa:["MA with EMA Cross"]},e.exports["MA/EMA Cross_study"]={en:["MA/EMA Cross"],fa:["MA/EMA Cross"]},e.exports.MACD_study={en:["MACD"],fa:["MACD"]},e.exports["Mass Index_study"]={en:["Mass Index"],fa:["شاخص انبوه"]},e.exports["McGinley Dynamic_study"]={en:["McGinley Dynamic"],fa:["McGinley Dynamic"]},e.exports.Median_study={en:["Median"],fa:["خط میانی"]},e.exports.Momentum_study={en:["Momentum"],fa:["Momentum"]},e.exports["Money Flow_study"]={en:["Money Flow"],fa:["گردش پول"]},e.exports["Moving Average Channel_study"]={en:["Moving Average Channel"],fa:["Moving Average Channel"]},e.exports["Moving Average Exponential_study"]={en:["Moving Average Exponential"],fa:["نمایی میانگین متحرک"]},e.exports["Moving Average Weighted_study"]={en:["Moving Average Weighted"],fa:["Moving Average Weighted"]},e.exports["Moving Average Simple_study"]={en:["Moving Average Simple"],fa:["Moving Average Simple"]},e.exports["Net Volume_study"]={en:["Net Volume"],fa:["حجم خالص"]},e.exports["On Balance Volume_study"]={en:["On Balance Volume"],fa:["On Balance Volume"]},e.exports["Parabolic SAR_study"]={en:["Parabolic SAR"],fa:["Parabolic SAR"]},e.exports["Pivot Points Standard_study"]={en:["Pivot Points Standard"],fa:["Pivot Points Standard"]},e.exports["Periodic Volume Profile_study"]={en:["Periodic Volume Profile"],fa:["Periodic Volume Profile"]},e.exports["Price Channel_study"]={en:["Price Channel"],fa:["Price Channel"]},e.exports["Price Oscillator_study"]={en:["Price Oscillator"],fa:["Price Oscillator"]},e.exports["Price Volume Trend_study"]={en:["Price Volume Trend"],fa:["Price Volume Trend"]},e.exports["Rate Of Change_study"]={en:["Rate Of Change"],fa:["Rate Of Change"]},e.exports["Relative Strength Index_study"]={en:["Relative Strength Index"],fa:["Relative Strength Index"]},e.exports["Relative Vigor Index_study"]={en:["Relative Vigor Index"],fa:["Relative Vigor Index"]},e.exports["Relative Volatility Index_study"]={en:["Relative Volatility Index"],fa:["Relative Volatility Index"]},e.exports["Relative Volume at Time_study"]={en:["Relative Volume at Time"],fa:["Relative Volume at Time"]},e.exports["Session Volume_study"]={en:["Session Volume"],fa:["Session Volume"]},e.exports["Session Volume HD_study"]={en:["Session Volume HD"],fa:["Session Volume"]},e.exports["Session Volume Profile_study"]={en:["Session Volume Profile"],fa:["Session Volume Profile"]},e.exports["Session Volume Profile HD_study"]={
en:["Session Volume Profile HD"],fa:["Session Volume Profile HD"]},e.exports["SMI Ergodic Indicator/Oscillator_study"]={en:["SMI Ergodic Indicator/Oscillator"],fa:["SMI Ergodic Indicator/Oscillator"]},e.exports["Smoothed Moving Average_study"]={en:["Smoothed Moving Average"],fa:["Smoothed Moving Average"]},e.exports["Stochastic Momentum Index_study"]={en:["Stochastic Momentum Index"],fa:["Stochastic Momentum Index"]},e.exports.Stoch_study={en:["Stoch"],fa:["Stoch"]},e.exports["Stochastic RSI_study"]={en:["Stochastic RSI"],fa:["Stochastic RSI"]},e.exports.Stochastic_study={en:["Stochastic"],fa:["Stochastic"]},e.exports["Time Weighted Average Price_study"]={en:["Time Weighted Average Price"],fa:["Time Weighted Average Price"]},e.exports["Triple EMA_study"]={en:["Triple EMA"],fa:["Triple EMA"]},e.exports.TRIX_study={en:["TRIX"],fa:["TRIX"]},e.exports["True Strength Indicator_study"]={en:["True Strength Indicator"],fa:["True Strength Indicator"]},e.exports["Ultimate Oscillator_study"]={en:["Ultimate Oscillator"],fa:["Ultimate Oscillator"]},e.exports["Visible Range_study"]={en:["Visible Range"],fa:["Visible Range"]},e.exports["Visible Range Volume Profile_study"]={en:["Visible Range Volume Profile"],fa:["Visible Range Volume Profile"]},e.exports["Volume Oscillator_study"]={en:["Volume Oscillator"],fa:["Volume Oscillator"]},e.exports.Volume_study={en:["Volume"],fa:["حجم"]},e.exports.Vol_study={en:["Vol"],fa:["Vol"]},e.exports["Vortex Indicator_study"]={en:["Vortex Indicator"],fa:["Vortex Indicator"]},e.exports.VWAP_study={en:["VWAP"],fa:["VWAP"]},e.exports.VWMA_study={en:["VWMA"],fa:["VWMA"]},e.exports["Williams %R_study"]={en:["Williams %R"],fa:["Williams %R"]},e.exports["Williams Alligator_study"]={en:["Williams Alligator"],fa:["Williams Alligator"]},e.exports["Williams Fractal_study"]={en:["Williams Fractal"],fa:["Williams Fractal"]},e.exports["Zig Zag_study"]={en:["Zig Zag"],fa:["Zig Zag"]},e.exports["24-hour Volume_study"]={en:["24-hour Volume"],fa:["24-hour Volume"]},e.exports["Ease of Movement_study"]={en:["Ease of Movement"],fa:["Ease Of Movement"]},e.exports["Elders Force Index_study"]={en:["Elders Force Index"],fa:["Elders Force Index"]},e.exports.Envelope_study={en:["Envelope"],fa:["Envelope"]},e.exports.Gaps_study={en:["Gaps"],fa:["Gaps"]},e.exports["Linear Regression Channel_study"]={en:["Linear Regression Channel"],fa:["Linear Regression Channel"]},e.exports["Moving Average Ribbon_study"]={en:["Moving Average Ribbon"],fa:["Moving Average Ribbon"]},e.exports["Multi-Time Period Charts_study"]={en:["Multi-Time Period Charts"],fa:["Multi-Time Period Charts"]},e.exports["Open Interest_study"]={en:["Open Interest"],fa:["Open Interest"]},e.exports["Rob Booker - Intraday Pivot Points_study"]={en:["Rob Booker - Intraday Pivot Points"],fa:["Rob Booker - Intraday Pivot Points"]},e.exports["Rob Booker - Knoxville Divergence_study"]={en:["Rob Booker - Knoxville Divergence"],fa:["Rob Booker - Knoxville Divergence"]},e.exports["Rob Booker - Missed Pivot Points_study"]={en:["Rob Booker - Missed Pivot Points"],
fa:["Rob Booker - Missed Pivot Points"]},e.exports["Rob Booker - Reversal_study"]={en:["Rob Booker - Reversal"],fa:["Rob Booker - Reversal"]},e.exports["Rob Booker - Ziv Ghost Pivots_study"]={en:["Rob Booker - Ziv Ghost Pivots"],fa:["Rob Booker - Ziv Ghost Pivots"]},e.exports.Supertrend_study={en:["Supertrend"],fa:["Supertrend"]},e.exports["Technical Ratings_study"]={en:["Technical Ratings"],fa:["Technical Ratings"]},e.exports["True Strength Index_study"]={en:["True Strength Index"],fa:["True Strength Index"]},e.exports["Up/Down Volume_study"]={en:["Up/Down Volume"],fa:["Up/Down Volume"]},e.exports["Visible Average Price_study"]={en:["Visible Average Price"],fa:["Visible Average Price"]},e.exports["Williams Fractals_study"]={en:["Williams Fractals"],fa:["Williams Fractals"]},e.exports["Keltner Channels Strategy_study"]={en:["Keltner Channels Strategy"],fa:["Keltner Channels Strategy"]},e.exports["Rob Booker - ADX Breakout_study"]={en:["Rob Booker - ADX Breakout"],fa:["Rob Booker - ADX Breakout"]},e.exports["Supertrend Strategy_study"]={en:["Supertrend Strategy"],fa:["Supertrend Strategy"]},e.exports["Technical Ratings Strategy_study"]={en:["Technical Ratings Strategy"],fa:["Technical Ratings Strategy"]},e.exports["Auto Anchored Volume Profile_study"]={en:["Auto Anchored Volume Profile"],fa:["Auto Anchored Volume Profile"]},e.exports["Auto Fib Extension_study"]={en:["Auto Fib Extension"],fa:["Auto Fib Extension"]},e.exports["Auto Fib Retracement_study"]={en:["Auto Fib Retracement"],fa:["Auto Fib Retracement"]},e.exports["Auto Pitchfork_study"]={en:["Auto Pitchfork"],fa:["Auto Pitchfork"]},e.exports["Bearish Flag Chart Pattern_study"]={en:["Bearish Flag Chart Pattern"],fa:["Bearish Flag Chart Pattern"]},e.exports["Bullish Flag Chart Pattern_study"]={en:["Bullish Flag Chart Pattern"],fa:["Bullish Flag Chart Pattern"]},e.exports["Bearish Pennant Chart Pattern_study"]={en:["Bearish Pennant Chart Pattern"],fa:["Bearish Pennant Chart Pattern"]},e.exports["Bullish Pennant Chart Pattern_study"]={en:["Bullish Pennant Chart Pattern"],fa:["Bullish Pennant Chart Pattern"]},e.exports["Double Bottom Chart Pattern_study"]={en:["Double Bottom Chart Pattern"],fa:["Double Bottom Chart Pattern"]},e.exports["Double Top Chart Pattern_study"]={en:["Double Top Chart Pattern"],fa:["Double Top Chart Pattern"]},e.exports["Elliott Wave Chart Pattern_study"]={en:["Elliott Wave Chart Pattern"],fa:["Elliott Wave Chart Pattern"]},e.exports["Falling Wedge Chart Pattern_study"]={en:["Falling Wedge Chart Pattern"],fa:["Falling Wedge Chart Pattern"]},e.exports["Head And Shoulders Chart Pattern_study"]={},e.exports["Inverse Head And Shoulders Chart Pattern_study"]={},e.exports["Rectangle Chart Pattern_study"]={en:["Rectangle Chart Pattern"],fa:["Rectangle Chart Pattern"]},e.exports["Rising Wedge Chart Pattern_study"]={en:["Rising Wedge Chart Pattern"],fa:["Rising Wedge Chart Pattern"]},e.exports["Triangle Chart Pattern_study"]={en:["Triangle Chart Pattern"],fa:["Triangle Chart Pattern"]},e.exports["Triple Bottom Chart Pattern_study"]={
en:["Triple Bottom Chart Pattern"],fa:["Triple Bottom Chart Pattern"]},e.exports["Triple Top Chart Pattern_study"]={en:["Triple Top Chart Pattern"],fa:["Triple Top Chart Pattern"]},e.exports["VWAP Auto Anchored_study"]={en:["VWAP Auto Anchored"],fa:["VWAP Auto Anchored"]},e.exports["*All Candlestick Patterns*_study"]={en:["*All Candlestick Patterns*"],fa:["*All Candlestick Patterns*"]},e.exports["Abandoned Baby - Bearish_study"]={en:["Abandoned Baby - Bearish"],fa:["Abandoned Baby - Bearish"]},e.exports["Abandoned Baby - Bullish_study"]={en:["Abandoned Baby - Bullish"],fa:["Abandoned Baby - Bullish"]},e.exports["Dark Cloud Cover - Bearish_study"]={en:["Dark Cloud Cover - Bearish"],fa:["Dark Cloud Cover - Bearish"]},e.exports["Doji Star - Bearish_study"]={en:["Doji Star - Bearish"],fa:["Doji Star - Bearish"]},e.exports["Doji Star - Bullish_study"]={en:["Doji Star - Bullish"],fa:["Doji Star - Bullish"]},e.exports["Downside Tasuki Gap - Bearish_study"]={en:["Downside Tasuki Gap - Bearish"],fa:["Downside Tasuki Gap - Bearish"]},e.exports["Dragonfly Doji - Bullish_study"]={en:["Dragonfly Doji - Bullish"],fa:["Dragonfly Doji - Bullish"]},e.exports["Engulfing - Bearish_study"]={en:["Engulfing - Bearish"],fa:["Engulfing - Bearish"]},e.exports["Engulfing - Bullish_study"]={en:["Engulfing - Bullish"],fa:["Engulfing - Bullish"]},e.exports["Evening Doji Star - Bearish_study"]={en:["Evening Doji Star - Bearish"],fa:["Evening Doji Star - Bearish"]},e.exports["Evening Star - Bearish_study"]={en:["Evening Star - Bearish"],fa:["Evening Star - Bearish"]},e.exports["Falling Three Methods - Bearish_study"]={en:["Falling Three Methods - Bearish"],fa:["Falling Three Methods - Bearish"]},e.exports["Falling Window - Bearish_study"]={en:["Falling Window - Bearish"],fa:["Falling Window - Bearish"]},e.exports["Gravestone Doji - Bearish_study"]={en:["Gravestone Doji - Bearish"],fa:["Gravestone Doji - Bearish"]},e.exports["Hammer - Bullish_study"]={en:["Hammer - Bullish"],fa:["Hammer - Bullish"]},e.exports["Hanging Man - Bearish_study"]={en:["Hanging Man - Bearish"],fa:["Hanging Man - Bearish"]},e.exports["Harami - Bearish_study"]={en:["Harami - Bearish"],fa:["Harami - Bearish"]},e.exports["Harami - Bullish_study"]={en:["Harami - Bullish"],fa:["Harami - Bullish"]},e.exports["Harami Cross - Bearish_study"]={en:["Harami Cross - Bearish"],fa:["Harami Cross - Bearish"]},e.exports["Harami Cross - Bullish_study"]={en:["Harami Cross - Bullish"],fa:["Harami Cross - Bullish"]},e.exports["Inverted Hammer - Bullish_study"]={en:["Inverted Hammer - Bullish"],fa:["Inverted Hammer - Bullish"]},e.exports["Kicking - Bearish_study"]={en:["Kicking - Bearish"],fa:["Kicking - Bearish"]},e.exports["Kicking - Bullish_study"]={en:["Kicking - Bullish"],fa:["Kicking - Bullish"]},e.exports["Long Lower Shadow - Bullish_study"]={en:["Long Lower Shadow - Bullish"],fa:["Long Lower Shadow - Bullish"]},e.exports["Long Upper Shadow - Bearish_study"]={en:["Long Upper Shadow - Bearish"],fa:["Long Upper Shadow - Bearish"]},e.exports["Marubozu Black - Bearish_study"]={
en:["Marubozu Black - Bearish"],fa:["Marubozu Black - Bearish"]},e.exports["Marubozu White - Bullish_study"]={en:["Marubozu White - Bullish"],fa:["Marubozu White - Bullish"]},e.exports["Morning Doji Star - Bullish_study"]={en:["Morning Doji Star - Bullish"],fa:["Morning Doji Star - Bullish"]},e.exports["Morning Star - Bullish_study"]={en:["Morning Star - Bullish"],fa:["Morning Star - Bullish"]},e.exports["On Neck - Bearish_study"]={en:["On Neck - Bearish"],fa:["On Neck - Bearish"]},e.exports["Piercing - Bullish_study"]={en:["Piercing - Bullish"],fa:["Piercing - Bullish"]},e.exports["Rising Three Methods - Bullish_study"]={en:["Rising Three Methods - Bullish"],fa:["Rising Three Methods - Bullish"]},e.exports["Rising Window - Bullish_study"]={en:["Rising Window - Bullish"],fa:["Rising Window - Bullish"]},e.exports["Shooting Star - Bearish_study"]={en:["Shooting Star - Bearish"],fa:["Shooting Star - Bearish"]},e.exports["Three Black Crows - Bearish_study"]={en:["Three Black Crows - Bearish"],fa:["Three Black Crows - Bearish"]},e.exports["Three White Soldiers - Bullish_study"]={en:["Three White Soldiers - Bullish"],fa:["Three White Soldiers - Bullish"]},e.exports["Tri-Star - Bearish_study"]={en:["Tri-Star - Bearish"],fa:["Tri-Star - Bearish"]},e.exports["Tri-Star - Bullish_study"]={en:["Tri-Star - Bullish"],fa:["Tri-Star - Bullish"]},e.exports["Tweezer Top - Bearish_study"]={en:["Tweezer Top - Bearish"],fa:["Tweezer Top - Bearish"]},e.exports["Upside Tasuki Gap - Bullish_study"]={en:["Upside Tasuki Gap - Bullish"],fa:["Upside Tasuki Gap - Bullish"]},e.exports.SuperTrend_study={en:["SuperTrend"],fa:["SuperTrend"]},e.exports["Average Price_study"]={en:["Average Price"],fa:["Average Price"]},e.exports["Typical Price_study"]={en:["Typical Price"],fa:["Typical Price"]},e.exports["Median Price_study"]={en:["Median Price"],fa:["Median Price"]},e.exports["Money Flow Index_study"]={en:["Money Flow Index"],fa:["Money Flow Index"]},e.exports["Moving Average Double_study"]={en:["Moving Average Double"],fa:["Moving Average Double"]},e.exports["Moving Average Triple_study"]={en:["Moving Average Triple"],fa:["Moving Average Triple"]},e.exports["Moving Average Adaptive_study"]={en:["Moving Average Adaptive"],fa:["Moving Average Adaptive"]},e.exports["Moving Average Hamming_study"]={en:["Moving Average Hamming"],fa:["Moving Average Hamming"]},e.exports["Moving Average Modified_study"]={en:["Moving Average Modified"],fa:["Moving Average Modified"]},e.exports["Moving Average Multiple_study"]={en:["Moving Average Multiple"],fa:["Moving Average Multiple"]},e.exports["Linear Regression Slope_study"]={en:["Linear Regression Slope"],fa:["Linear Regression Slope"]},e.exports["Standard Error_study"]={en:["Standard Error"],fa:["Standard Error"]},e.exports["Standard Error Bands_study"]={en:["Standard Error Bands"],fa:["Standard Error Bands"]},e.exports["Correlation - Log_study"]={en:["Correlation - Log"],fa:["Correlation - Log"]},e.exports["Standard Deviation_study"]={en:["Standard Deviation"],fa:["Standard Deviation"]},
e.exports["Chaikin Volatility_study"]={en:["Chaikin Volatility"],fa:["Chaikin Volatility"]},e.exports["Volatility Close-to-Close_study"]={en:["Volatility Close-to-Close"],fa:["Volatility Close-to-Close"]},e.exports["Volatility Zero Trend Close-to-Close_study"]={en:["Volatility Zero Trend Close-to-Close"],fa:["Volatility Zero Trend Close-to-Close"]},e.exports["Volatility O-H-L-C_study"]={en:["Volatility O-H-L-C"],fa:["Volatility O-H-L-C"]},e.exports["Volatility Index_study"]={en:["Volatility Index"],fa:["Volatility Index"]},e.exports["Trend Strength Index_study"]={en:["Trend Strength Index"],fa:["Trend Strength Index"]},e.exports["Majority Rule_study"]={en:["Majority Rule"],fa:["Majority Rule"]},e.exports["Advance Decline Line_study"]={en:["Advance Decline Line"],fa:["Advance Decline Line"]},e.exports["Advance Decline Ratio_study"]={en:["Advance Decline Ratio"],fa:["Advance Decline Ratio"]},e.exports["Advance/Decline Ratio (Bars)_study"]={en:["Advance/Decline Ratio (Bars)"],fa:["Advance/Decline Ratio (Bars)"]},e.exports["BarUpDn Strategy_study"]={en:["BarUpDn Strategy"],fa:["BarUpDn Strategy"]},e.exports["Bollinger Bands Strategy directed_study"]={en:["Bollinger Bands Strategy directed"],fa:["Bollinger Bands Strategy directed"]},e.exports["Bollinger Bands Strategy_study"]={en:["Bollinger Bands Strategy"],fa:["Bollinger Bands Strategy"]},e.exports.ChannelBreakOutStrategy_study={en:["ChannelBreakOutStrategy"],fa:["ChannelBreakOutStrategy"]},e.exports.Compare_study={en:["Compare"],fa:["مقایسه"]},e.exports["Conditional Expressions_study"]={en:["Conditional Expressions"],fa:["Conditional Expressions"]},e.exports.ConnorsRSI_study={en:["ConnorsRSI"],fa:["ConnorsRSI"]},e.exports["Consecutive Up/Down Strategy_study"]={en:["Consecutive Up/Down Strategy"],fa:["Consecutive Up/Down Strategy"]},e.exports["Cumulative Volume Index_study"]={en:["Cumulative Volume Index"],fa:["Cumulative Volume Index"]},e.exports["Divergence Indicator_study"]={en:["Divergence Indicator"],fa:["Divergence Indicator"]},e.exports["Greedy Strategy_study"]={en:["Greedy Strategy"],fa:["Greedy Strategy"]},e.exports["InSide Bar Strategy_study"]={en:["InSide Bar Strategy"],fa:["InSide Bar Strategy"]},e.exports["Keltner Channel Strategy_study"]={en:["Keltner Channel Strategy"],fa:["Keltner Channel Strategy"]},e.exports["Linear Regression_study"]={en:["Linear Regression"],fa:["Linear Regression"]},e.exports["MACD Strategy_study"]={en:["MACD Strategy"],fa:["MACD Strategy"]},e.exports["Momentum Strategy_study"]={en:["Momentum Strategy"],fa:["Momentum Strategy"]},e.exports["Moon Phases_study"]={en:["Moon Phases"],fa:["Moon Phases"]},e.exports["Moving Average Convergence/Divergence_study"]={en:["Moving Average Convergence/Divergence"],fa:["Moving Average Convergence/Divergence"]},e.exports["MovingAvg Cross_study"]={en:["MovingAvg Cross"],fa:["MovingAvg Cross"]},e.exports["MovingAvg2Line Cross_study"]={en:["MovingAvg2Line Cross"],fa:["MovingAvg2Line Cross"]},e.exports["OutSide Bar Strategy_study"]={en:["OutSide Bar Strategy"],fa:["OutSide Bar Strategy"]},
e.exports.Overlay_study={en:["Overlay"],fa:["Overlay"]},e.exports["Parabolic SAR Strategy_study"]={en:["Parabolic SAR Strategy"],fa:["Parabolic SAR Strategy"]},e.exports["Pivot Extension Strategy_study"]={en:["Pivot Extension Strategy"],fa:["Pivot Extension Strategy"]},e.exports["Pivot Points High Low_study"]={en:["Pivot Points High Low"],fa:["Pivot Points High Low"]},e.exports["Pivot Reversal Strategy_study"]={en:["Pivot Reversal Strategy"],fa:["Pivot Reversal Strategy"]},e.exports["Price Channel Strategy_study"]={en:["Price Channel Strategy"],fa:["Price Channel Strategy"]},e.exports["RSI Strategy_study"]={en:["RSI Strategy"],fa:["RSI Strategy"]},e.exports["SMI Ergodic Indicator_study"]={en:["SMI Ergodic Indicator"],fa:["SMI Ergodic Indicator"]},e.exports["SMI Ergodic Oscillator_study"]={en:["SMI Ergodic Oscillator"],fa:["SMI Ergodic Oscillator"]},e.exports["Stochastic Slow Strategy_study"]={en:["Stochastic Slow Strategy"],fa:["Stochastic Slow Strategy"]},e.exports["Volatility Stop_study"]={en:["Volatility Stop"],fa:["Volatility Stop"]},e.exports["Volty Expan Close Strategy_study"]={en:["Volty Expan Close Strategy"],fa:["Volty Expan Close Strategy"]},e.exports["Woodies CCI_study"]={en:["Woodies CCI"],fa:["Woodies CCI"]},e.exports["Anchored Volume Profile_study"]={en:["Anchored Volume Profile"],fa:["Anchored Volume Profile"]},e.exports["Trading Sessions_study"]={en:["Trading Sessions"],fa:["Trading Sessions"]},e.exports["Cup and Handle Chart Pattern_study"]={en:["Cup and Handle Chart Pattern"],fa:["Cup and Handle Chart Pattern"]},e.exports["Inverted Cup and Handle Chart Pattern_study"]={en:["Inverted Cup and Handle Chart Pattern"],fa:["Inverted Cup and Handle Chart Pattern"]},e.exports["Head and Shoulders Chart Pattern_study"]={en:["Head and Shoulders Chart Pattern"],fa:["Head and Shoulders Chart Pattern"]},e.exports["Inverted Head and Shoulders Chart Pattern_study"]={en:["Inverted Head and Shoulders Chart Pattern"],fa:["Inverted Head and Shoulders Chart Pattern"]}},89633:e=>{e.exports={en:["Anchored Volume Profile"],fa:["Anchored Volume Profile"]}},25705:e=>{e.exports={en:["Fixed Range Volume Profile"],fa:["Fixed Range Volume Profile"]}},24261:e=>{e.exports={en:["Vol"],fa:["Vol"]}},51077:e=>{e.exports={en:["Minor"],fa:["Minor"]}},922:e=>{e.exports={en:["Minute"],fa:["دقیقه"]}},91405:e=>{e.exports={en:["Text"],fa:["Text"]}},78972:e=>{e.exports={en:["Couldn't copy"],fa:["Couldn't copy"]}},10615:e=>{e.exports={en:["Couldn't cut"],fa:["Couldn't cut"]}},81518:e=>{e.exports={en:["Couldn't paste"],fa:["Couldn't paste"]}},83140:e=>{e.exports={en:["Countdown to bar close"],fa:["Countdown To Bar Close"]}},10871:e=>{e.exports={en:["Colombo"],fa:["Colombo"]}},55761:e=>{e.exports={en:["Columns"],fa:["ستونی"]}},9818:e=>{e.exports={en:["Comment"],fa:["توضیحات"]}},53942:e=>{e.exports={en:["Compare or Add Symbol"],fa:["مقایسه یا افزودن نماد"]}},12086:e=>{e.exports={en:["Compilation error"],fa:["Compilation error"]}},48141:e=>{e.exports={en:["Confirm Inputs"],fa:["تایید ورودی‏ ها"]}},38917:e=>{e.exports={en:["Copenhagen"],
fa:["Copenhagen"]}},49680:e=>{e.exports={en:["Copy"],fa:["کپی"]}},66134:e=>{e.exports={en:["Copy Chart Layout"],fa:["کپی طرح نمودار"]}},63553:e=>{e.exports={en:["Copy price"],fa:["Copy price"]}},65736:e=>{e.exports={en:["Cairo"],fa:["Cairo"]}},25381:e=>{e.exports={en:["Callout"],fa:["Callout"]}},45054:e=>{e.exports={en:["Candles"],fa:["شمعی"]}},30948:e=>{e.exports={en:["Caracas"],fa:["Caracas"]}},70409:e=>{e.exports={en:["Casablanca"],fa:["Casablanca"]}},37276:e=>{e.exports={en:["Change"],fa:["تغییر"]}},85124:e=>{e.exports={en:["Change Symbol"],fa:["تغییر نماد"]}},2569:e=>{e.exports={en:["Change interval"],fa:["تغییر بازه"]}},9687:e=>{e.exports={en:["Change interval. Press number or comma"],fa:["Change interval. Press number or comma"]}},36332:e=>{e.exports={en:["Change symbol. Start typing symbol name"],fa:["Change symbol. Start typing symbol name"]}},48566:e=>{e.exports={en:["Change scale currency"],fa:["Change scale currency"]}},85110:e=>{e.exports={en:["Change scale unit"],fa:["Change scale unit"]}},56275:e=>{e.exports={en:["Chart #{index}"],fa:["Chart #{index}"]}},39950:e=>{e.exports={en:["Chart Properties"],fa:["تنظیمات نمودار"]}},98856:e=>{e.exports={en:["Chart by TradingView"],fa:["Chart by TradingView"]}},1136:e=>{e.exports={en:["Chart for {symbol}, {interval}"],fa:["Chart for {symbol}, {interval}"]}},69804:e=>{e.exports={en:["Chart image copied to clipboard {emoji}"],fa:["Chart image copied to clipboard {emoji}"]}},6655:e=>{e.exports={en:["Chart image embed code copied to clipboard {emoji}"],fa:["Chart image embed code copied to clipboard {emoji}"]}},36549:e=>{e.exports={en:["Chatham Islands"],fa:["Chatham Islands"]}},72452:e=>{e.exports={en:["Chicago"],fa:["شیکاگو"]}},50349:e=>{e.exports={en:["Chongqing"],fa:["Chongqing"]}},91944:e=>{e.exports={en:["Circle"],fa:["دایره"]}},14985:e=>{e.exports={en:["Click to set a point"],fa:["Click to set a point"]}},12537:e=>{e.exports={en:["Clone"],fa:["تکثیر"]}},62578:e=>{e.exports={en:["Close"],fa:["پایانی"]}},264:e=>{e.exports={en:["Create limit order"],fa:["Create limit order"]}},6969:e=>{e.exports={en:["Cross"],fa:["مکان‌نما"]}},74334:e=>{e.exports={en:["Cross Line"],fa:["Cross Line"]}},59396:e=>{e.exports={en:["Currencies"],fa:["ارزها"]}},20177:e=>{e.exports={en:["Current interval and above"],fa:["Current interval and above"]}},494:e=>{e.exports={en:["Current interval and below"],fa:["Current interval and below"]}},60668:e=>{e.exports={en:["Current interval only"],fa:["Current interval only"]}},78609:e=>{e.exports={en:["Curve"],fa:["منحنی"]}},87380:e=>{e.exports={en:["Cycle"],fa:["دوره"]}},84031:e=>{e.exports={en:["Cyclic Lines"],fa:["خطوط دایره ای"]}},93191:e=>{e.exports={en:["Cypher Pattern"],fa:["الگوی Cypher"]}},7219:e=>{e.exports={en:["A layout with that name already exists"],fa:["A layout with that name already exists"]}},67635:e=>{e.exports={en:["A layout with that name already exists. Do you want to overwrite it?"],fa:["A layout with that name already exists. Do you want to overwrite it?"]}},46712:e=>{e.exports={en:["ABCD Pattern"],fa:["الگوی ABCD"]}},
36485:e=>{e.exports={en:["Amsterdam"],fa:["Amsterdam"]}},42630:e=>{e.exports={en:["Anchorage"],fa:["Anchorage"]}},63209:e=>{e.exports={en:["Anchored Note"],fa:["یادداشت ثابت"]}},42669:e=>{e.exports={en:["Anchored Text"],fa:["متن ثابت"]}},84541:e=>{e.exports={en:["Anchored VWAP"],fa:["Anchored VWAP"]}},77401:e=>{e.exports={en:["Access error"],fa:["Access error"]}},46501:e=>{e.exports={en:["Add Symbol"],fa:["افزودن نماد"]}},69709:e=>{e.exports={en:["Add alert on {title}"],fa:["Add alert on {title}"]}},89295:e=>{e.exports={en:["Add alert on {title} at {price}"],fa:["Add alert on {title} at {price}"]}},2439:e=>{e.exports={en:["Add financial metric for {instrumentName}"],fa:["Add financial metric for {instrumentName}"]}},35088:e=>{e.exports={en:["Add indicator/strategy on {studyTitle}"],fa:["Add indicator/strategy on {studyTitle}"]}},39873:e=>{e.exports={en:["Add text note for {symbol}"],fa:["Add Text Note for {symbol}"]}},35679:e=>{e.exports={en:["Add this financial metric to entire layout"],fa:["Add this financial metric to entire layout"]}},10996:e=>{e.exports={en:["Add this financial metric to favorites"],fa:["Add this financial metric to favorites"]}},26090:e=>{e.exports={en:["Add this indicator to entire layout"],fa:["Add this indicator to entire layout"]}},92957:e=>{e.exports={en:["Add this indicator to favorites"],fa:["Add this indicator to favorites"]}},95754:e=>{e.exports={en:["Add this strategy to entire layout"],fa:["Add this strategy to entire layout"]}},39010:e=>{e.exports={en:["Add this symbol to entire layout"],fa:["Add this symbol to entire layout"]}},426:e=>{e.exports={en:["Adelaide"],fa:["Adelaide"]}},40452:e=>{e.exports={en:["Always invisible"],fa:["همواره مخفی"]}},36299:e=>{e.exports={en:["Always visible"],fa:["همواره آشکار"]}},81442:e=>{e.exports={en:["All indicators and drawing tools"],fa:["All Indicators And Drawing Tools"]}},58026:e=>{e.exports={en:["All intervals"],fa:["All intervals"]}},78358:e=>{e.exports={en:["Apply default"],fa:["Apply Default"]}},22437:e=>{e.exports={en:["Apply these indicators to entire layout"],fa:["Apply these indicators to entire layout"]}},27072:e=>{e.exports={en:["Apr"],fa:["آوریل"]}},59324:e=>{e.exports={en:["Arc"],fa:["کمان"]}},34456:e=>{e.exports={en:["Area"],fa:["ناحیه"]}},11858:e=>{e.exports={en:["Arrow"],fa:["پیکان"]}},34247:e=>{e.exports={en:["Arrow Down"],fa:["Arrow Down"]}},36352:e=>{e.exports={en:["Arrow Marker"],fa:["Arrow Marker"]}},73193:e=>{e.exports={en:["Arrow Mark Down"],fa:["پیکان رو به پایین"]}},1949:e=>{e.exports={en:["Arrow Mark Left"],fa:["پیکان رو به چپ"]}},86275:e=>{e.exports={en:["Arrow Mark Right"],fa:["پیکان رو به راست"]}},62453:e=>{e.exports={en:["Arrow Mark Up"],fa:["پیکان رو به بالا"]}},77231:e=>{e.exports={en:["Arrow Up"],fa:["Arrow Up"]}},98128:e=>{e.exports={en:["Astana"],fa:["Astana"]}},63627:e=>{e.exports={en:["Ashgabat"],fa:["Ashkhabad"]}},72445:e=>{e.exports={en:["At close"],fa:["At close"]}},73702:e=>{e.exports={en:["Athens"],fa:["آتن"]}},21469:e=>{e.exports={en:["Auto"],fa:["Auto"]}},24157:e=>{e.exports={
en:["Auto (fits data to screen)"],fa:["Auto (Fits Data To Screen)"]}},46450:e=>{e.exports={en:["Aug"],fa:["آگوست"]}},21841:e=>{e.exports={en:["Average close price label"],fa:["Average close price label"]}},16138:e=>{e.exports={en:["Average close price line"],fa:["Average close price line"]}},73025:e=>{e.exports={en:["Avg"],fa:["Avg"]}},87580:e=>{e.exports={en:["Azores"]}},73905:e=>{e.exports={en:["Bogota"],fa:["بوگوتا"]}},90594:e=>{e.exports={en:["Bahrain"],fa:["Bahrain"]}},70540:e=>{e.exports={en:["Balloon"],fa:["بالون"]}},47045:e=>{e.exports={en:["Bangkok"],fa:["بانگوک"]}},76651:e=>{e.exports={en:["Bar Replay isn't available for this chart type. Do you want to exit Bar Replay?"],fa:["Bar Replay isn't available for this chart type. Do you want to exit Bar Replay?"]}},68054:e=>{e.exports={en:["Bar Replay isn't available for this time interval. Do you want to exit Bar Replay?"],fa:["Bar Replay isn't available for this time interval. Do you want to exit Bar Replay?"]}},27377:e=>{e.exports={en:["Bars"],fa:["میله‌ای"]}},81994:e=>{e.exports={en:["Bars Pattern"],fa:["الگوی داده ها"]}},59213:e=>{e.exports={en:["Baseline"],fa:["Baseline"]}},71797:e=>{e.exports={en:["Belgrade"],fa:["Belgrade"]}},64313:e=>{e.exports={en:["Berlin"],fa:["برلین"]}},43539:e=>{e.exports={en:["Brush"],fa:["قلم"]}},91499:e=>{e.exports={en:["Brussels"],fa:["Brussels"]}},70876:e=>{e.exports={en:["Bratislava"],fa:["Bratislava"]}},55481:e=>{e.exports={en:["Bring forward"],fa:["جلو"]}},17293:e=>{e.exports={en:["Bring to front"],fa:["اولین"]}},79336:e=>{e.exports={en:["Brisbane"],fa:["Brisbane"]}},33672:e=>{e.exports={en:["Bucharest"],fa:["Bucharest"]}},20313:e=>{e.exports={en:["Budapest"],fa:["Budapest"]}},25282:e=>{e.exports={en:["Buenos Aires"],fa:["بوینس آیرس"]}},46768:e=>{e.exports={en:["By TradingView"],fa:["By TradingView"]}},54280:e=>{e.exports={en:["Go to date"],fa:["برو به تاریخ"]}},74975:e=>{e.exports={en:["Go to {lineToolName}"],fa:["Go to {lineToolName}"]}},15462:e=>{e.exports={en:["Got it"],fa:["Got it"]}},47460:e=>{e.exports={en:["Gann Box"],fa:["Gann Box"]}},48683:e=>{e.exports={en:["Gann Fan"],fa:["Gann Fan"]}},44763:e=>{e.exports={en:["Gann Square"],fa:["Gann Square"]}},60707:e=>{e.exports={en:["Gann Square Fixed"],fa:["Gann Square Fixed"]}},46808:e=>{e.exports={en:["Ghost Feed"],fa:["Ghost Feed"]}},57726:e=>{e.exports={en:["Grand supercycle"],fa:["Grand Supercycle"]}},34914:e=>{e.exports={en:["Do you really want to delete Study Template '{name}' ?"],fa:["Do you really want to delete Study Template '{name}' ?"]}},77125:e=>{e.exports={en:["Double Curve"],fa:["منحنی دوگانه"]}},9430:e=>{e.exports={en:["Double-click any edge to reset layout grid"],fa:["Double-click any edge to reset layout grid"]}},75296:e=>{e.exports={en:["Double-click to finish Path"],fa:["Double-click to finish Path"]}},17409:e=>{e.exports={en:["Double-click to finish Polyline"],fa:["Double-click to finish Polyline"]}},57131:e=>{e.exports={en:["Data Provided by"],fa:["Data Provided by"]}},62154:e=>{e.exports={en:["Date"],fa:["تاریخ"]}},85444:e=>{e.exports={
en:["Date Range"],fa:["بازه زمانی"]}},47017:e=>{e.exports={en:["Date and Price Range"],fa:["محدوده تاریخ و قیمت"]}},32084:e=>{e.exports={en:["Dec"],fa:["دسامبر"]}},23403:e=>{e.exports={en:["Degree"],fa:["Degree"]}},27358:e=>{e.exports={en:["Denver"],fa:["Denver"]}},24959:e=>{e.exports={en:["Dhaka"],fa:["Dhaka"]}},15179:e=>{e.exports={en:["Diamond"],fa:["Diamond"]}},91544:e=>{e.exports={en:["Disjoint Channel"],fa:["Disjoint Channel"]}},70132:e=>{e.exports={en:["Displacement"],fa:["Displacement"]}},93864:e=>{e.exports={en:["Drawings toolbar"],fa:["Drawings Toolbar"]}},96890:e=>{e.exports={en:["Draw Horizontal Line at"],fa:["Draw Horizontal Line at"]}},23650:e=>{e.exports={en:["Dubai"],fa:["Dubai"]}},79716:e=>{e.exports={en:["Dublin"],fa:["Dublin"]}},73456:e=>{e.exports={en:["Emoji"],fa:["Emoji"]}},9541:e=>{e.exports={en:["Enter a new chart layout name"],fa:["Enter a new chart layout name"]}},80943:e=>{e.exports={en:["Elliott Correction Wave (ABC)"],fa:["Elliott Correction Wave (ABC)"]}},75112:e=>{e.exports={en:["Elliott Double Combo Wave (WXY)"],fa:["Elliott Double Combo Wave (WXY)"]}},61114:e=>{e.exports={en:["Elliott Impulse Wave (12345)"],fa:["Elliott Impulse Wave (12345)"]}},72359:e=>{e.exports={en:["Elliott Triangle Wave (ABCDE)"],fa:["Elliott Triangle Wave (ABCDE)"]}},76129:e=>{e.exports={en:["Elliott Triple Combo Wave (WXYXZ)"],fa:["Elliott Triple Combo Wave (WXYXZ)"]}},78996:e=>{e.exports={en:["Ellipse"],fa:["Ellipse"]}},52788:e=>{e.exports={en:["Extended Line"],fa:["Extended Line"]}},86905:e=>{e.exports={en:["Exchange"],fa:["Exchange"]}},19271:e=>{e.exports={en:["Existing pane above"],fa:["Existing Pane Above"]}},46545:e=>{e.exports={en:["Existing pane below"],fa:["Existing Pane Below"]}},20138:e=>{e.exports={en:["Forecast"],fa:["پیش بینی"]}},2507:e=>{e.exports={en:["Feb"],fa:["فوریه"]}},59005:e=>{e.exports={en:["Fib Channel"],fa:["کانال فیبوناچی"]}},82330:e=>{e.exports={en:["Fib Circles"],fa:["دایره های فیبوناچی"]}},55986:e=>{e.exports={en:["Fib Retracement"],fa:["اصلاحی فیبوناچی"]}},33880:e=>{e.exports={en:["Fib Speed Resistance Arcs"],fa:["Fib Speed Resistance Arcs"]}},2395:e=>{e.exports={en:["Fib Speed Resistance Fan"],fa:["Fib Speed Resistance Fan"]}},39014:e=>{e.exports={en:["Fib Spiral"],fa:["Fib Spiral"]}},30622:e=>{e.exports={en:["Fib Time Zone"],fa:["منطقه زمانی فیبوناچی"]}},85042:e=>{e.exports={en:["Fib Wedge"],fa:["گوه فیبوناچی"]}},33885:e=>{e.exports={en:["Flag"],fa:["Flag"]}},14600:e=>{e.exports={en:["Flag Mark"],fa:["علامت گذاری"]}},45051:e=>{e.exports={en:["Flat Top/Bottom"],fa:["Flat Top/Bottom"]}},63271:e=>{e.exports={en:["Flipped"],fa:["چرخش عمودی"]}},39643:e=>{e.exports={en:["Fraction part is invalid."],fa:["Fraction part is invalid."]}},24077:e=>{e.exports={en:["Fundamental studies are no longer available on charts"],fa:["Fundamental studies are no longer available on charts"]}},31561:e=>{e.exports={en:["Kolkata"],fa:["کلکته"]}},54533:e=>{e.exports={en:["Kathmandu"],fa:["Kathmandu"]}},83490:e=>{e.exports={en:["Kagi"],fa:["Kagi"]}},70913:e=>{e.exports={en:["Karachi"],fa:["Karachi"]}},
76614:e=>{e.exports={en:["Kuwait"],fa:["Kuwait"]}},38561:e=>{e.exports={en:["Kuala Lumpur"],fa:["Kuala Lumpur"]}},99906:e=>{e.exports={en:["HLC area"],fa:["HLC area"]}},34491:e=>{e.exports={en:["Ho Chi Minh"],fa:["Ho Chi Minh"]}},13459:e=>{e.exports={en:["Hollow candles"],fa:["شمعی توخالی"]}},48861:e=>{e.exports={en:["Hong Kong"],fa:["هنگ کنگ"]}},79668:e=>{e.exports={en:["Honolulu"],fa:["Honolulu"]}},21795:e=>{e.exports={en:["Horizontal Line"],fa:["خط افقی"]}},25487:e=>{e.exports={en:["Horizontal Ray"],fa:["Horizontal Ray"]}},21928:e=>{e.exports={en:["Head and Shoulders"],fa:["Head and Shoulders"]}},63876:e=>{e.exports={en:["Heikin Ashi"],fa:["Heikin Ashi"]}},48203:e=>{e.exports={en:["Helsinki"],fa:["Helsinki"]}},27298:e=>{e.exports={en:["Hide"],fa:["عدم نمایش"]}},47074:e=>{e.exports={en:["Hide all"],fa:["Hide all"]}},52563:e=>{e.exports={en:["Hide all drawings"],fa:["Hide all drawings"]}},90763:e=>{e.exports={en:["Hide all drawings and indicators"],fa:["Hide all drawings and indicators"]}},18216:e=>{e.exports={en:["Hide all drawings, indicators, positions & orders"],fa:["Hide all drawings, indicators, positions & orders"]}},78525:e=>{e.exports={en:["Hide all indicators"],fa:["Hide all indicators"]}},42164:e=>{e.exports={en:["Hide all positions & orders"],fa:["Hide all positions & orders"]}},3217:e=>{e.exports={en:["Hide drawings"],fa:["Hide drawings"]}},97878:e=>{e.exports={en:["Hide events on chart"],fa:["Hide Events on Chart"]}},72351:e=>{e.exports={en:["Hide indicators"],fa:["Hide indicators"]}},28345:e=>{e.exports={en:["Hide marks on bars"],fa:["Hide Marks On Bars"]}},92226:e=>{e.exports={en:["Hide positions & orders"],fa:["Hide positions & orders"]}},78254:e=>{e.exports={en:["High"],fa:["بیشترین"]}},98236:e=>{e.exports={en:["High-low"],fa:["High-low"]}},99479:e=>{e.exports={en:["High and low price labels"],fa:["High and low price labels"]}},33766:e=>{e.exports={en:["High and low price lines"],fa:["High and low price lines"]}},69476:e=>{e.exports={en:["Highlighter"],fa:["Highlighter"]}},72819:e=>{e.exports={en:["Histogram is too large, please increase {boldHighlightStart}Row Size{boldHighlightEnd} in the settings window."],fa:["Histogram is too large, please increase {boldHighlightStart}Row Size{boldHighlightEnd} in the settings window."]}},94966:e=>{e.exports={en:["Histogram is too large, please increase {boldHighlightStart}Ticks Per Row{boldHighlightEnd} in the settings window."],fa:["Histogram is too large, please increase {boldHighlightStart}Ticks Per Row{boldHighlightEnd} in the settings window."]}},66751:e=>{e.exports={en:["Histogram is too large, please reduce {boldHighlightStart}Row Size{boldHighlightEnd} in the settings window."],fa:["Histogram is too large, please reduce {boldHighlightStart}Row Size{boldHighlightEnd} in the settings window."]}},68065:e=>{e.exports={en:["Image"],fa:["Image"]}},80185:e=>{e.exports={en:["Intervals less than {resolution} are not supported for {ticker}."],fa:["Intervals less than {resolution} are not supported for {ticker}."]}},10268:e=>{e.exports={en:["Intermediate"],
fa:["میانروز"]}},14285:e=>{e.exports={en:["Invalid Symbol"],fa:["نماد غیر معتبر"]}},52969:e=>{e.exports={en:["Invalid symbol"],fa:["Invalid symbol"]}},37189:e=>{e.exports={en:["Invert scale"],fa:["Invert Scale"]}},89999:e=>{e.exports={en:["Indexed to 100"],fa:["Indexed to 100"]}},46850:e=>{e.exports={en:["Indicators value labels"],fa:["Indicators value labels"]}},54418:e=>{e.exports={en:["Indicators name labels"],fa:["Indicators name labels"]}},40490:e=>{e.exports={en:["Indicators, Metrics and Strategies. Press slash"],fa:["Indicators, Metrics and Strategies. Press slash"]}},15992:e=>{e.exports={en:["Info Line"],fa:["Info Line"]}},87829:e=>{e.exports={en:["Insert indicator"],fa:["افزودن اندیکاتور"]}},91612:e=>{e.exports={en:["Inside"],fa:["داخلی"]}},41686:e=>{e.exports={en:["Inside Pitchfork"],fa:["Inside Pitchfork"]}},37913:e=>{e.exports={en:["Icon"],fa:["شمایل"]}},78326:e=>{e.exports={en:["Istanbul"],fa:["Istanbul"]}},39585:e=>{e.exports={en:["Johannesburg"],fa:["Johannesburg"]}},14995:e=>{e.exports={en:["Jakarta"],fa:["Jakarta"]}},62310:e=>{e.exports={en:["Jan"],fa:["ژانویه"]}},36057:e=>{e.exports={en:["Jerusalem"],fa:["Jerusalem"]}},53786:e=>{e.exports={en:["Jul"],fa:["Jul"]}},429:e=>{e.exports={en:["Jun"],fa:["ژوئن"]}},67560:e=>{e.exports={en:["Juneau"],fa:["Juneau"]}},62329:e=>{e.exports={en:["On the left"],fa:["On the left"]}},55813:e=>{e.exports={en:["On the right"],fa:["On the right"]}},64818:e=>{e.exports={en:["Only {availableResolutions} intervals are supported for {ticker}."],fa:["Only {availableResolutions} intervals are supported for {ticker}."]}},21064:e=>{e.exports={en:["Oops!"],fa:["Oops!"]}},51221:e=>{e.exports={en:["Object Tree"],fa:["Object Tree"]}},12179:e=>{e.exports={en:["Oct"],fa:["Oct"]}},16610:e=>{e.exports={en:["Open"],fa:["باز"]}},46005:e=>{e.exports={en:["Original"],fa:["اصلی"]}},75722:e=>{e.exports={en:["Oslo"],fa:["Oslo"]}},65318:e=>{e.exports={en:["Low"],fa:["کمترین"]}},55382:e=>{e.exports={en:["Load layout. Press period"],fa:["Load layout. Press period"]}},5837:e=>{e.exports={en:["Lock"],fa:["قفل"]}},79777:e=>{e.exports={en:["Lock/unlock"],fa:["قفل/باز"]}},66005:e=>{e.exports={en:["Lock vertical cursor line by time"],fa:["Lock vertical cursor line by time"]}},14017:e=>{e.exports={en:["Lock price to bar ratio"],fa:["Lock Price To Bar Ratio"]}},16170:e=>{e.exports={en:["Logarithmic"],fa:["Logarithmic"]}},19439:e=>{e.exports={en:["London"],fa:["لندن"]}},74832:e=>{e.exports={en:["Long Position"],fa:["وضعیت خرید"]}},28733:e=>{e.exports={en:["Los Angeles"],fa:["لس آنجلس"]}},85924:e=>{e.exports={en:["Label Down"],fa:["Label Down"]}},52402:e=>{e.exports={en:["Label Up"],fa:["Label Up"]}},5119:e=>{e.exports={en:["Labels"],fa:["برچسب‌ها"]}},19931:e=>{e.exports={en:["Lagos"],fa:["Lagos"]}},63815:e=>{e.exports={en:["Last day change"],fa:["Last day change"]}},59444:e=>{e.exports={en:["Lima"],fa:["Lima"]}},3554:e=>{e.exports={en:["Line"],fa:["خط"]}},9394:e=>{e.exports={en:["Line with markers"],fa:["نقاط قیمت"]}},43588:e=>{e.exports={en:["Line break"],fa:["Line Break"]}},56982:e=>{e.exports={
en:["Lines"],fa:["Lines"]}},37367:e=>{e.exports={en:["Link to the chart image copied to clipboard {emoji}"],fa:["Link to the chart image copied to clipboard {emoji}"]}},53375:e=>{e.exports={en:["Lisbon"],fa:["Lisbon"]}},81038:e=>{e.exports={en:["Luxembourg"],fa:["Luxembourg"]}},60663:e=>{e.exports={en:["Move the point to position the anchor then tap to place"],fa:["Move the point to position the anchor then tap to place"]}},35049:e=>{e.exports={en:["Move to"],fa:["Move to"]}},26493:e=>{e.exports={en:["Move scale to left"],fa:["Move scale to left"]}},40789:e=>{e.exports={en:["Move scale to right"],fa:["Move scale to right"]}},70382:e=>{e.exports={en:["Modified Schiff"],fa:["شیف تغییر داده‌شد"]}},57681:e=>{e.exports={en:["Modified Schiff Pitchfork"],fa:["Modified Schiff Pitchfork"]}},93907:e=>{e.exports={en:["More settings"]}},64039:e=>{e.exports={en:["Moscow"],fa:["مسکو"]}},52066:e=>{e.exports={en:["Madrid"],fa:["مادرید"]}},38365:e=>{e.exports={en:["Malta"],fa:["Malta"]}},48991:e=>{e.exports={en:["Manila"],fa:["Manila"]}},92767:e=>{e.exports={en:["Mar"],fa:["مارس"]}},73332:e=>{e.exports={en:["Mexico City"],fa:["Mexico City"]}},88314:e=>{e.exports={en:["Merge all scales into one"],fa:["Merge all scales into one"]}},54215:e=>{e.exports={en:["Mixed"],fa:["Mixed"]}},24866:e=>{e.exports={en:["Micro"],fa:["Micro"]}},87957:e=>{e.exports={en:["Millennium"],fa:["Millennium"]}},14724:e=>{e.exports={en:["Minuette"],fa:["دقیقه"]}},78273:e=>{e.exports={en:["Minuscule"],fa:["Minuscule"]}},28941:e=>{e.exports={en:["Mirrored"],fa:["جرخش افقی"]}},9865:e=>{e.exports={en:["Muscat"],fa:["Muscat"]}},96935:e=>{e.exports={en:["N/A"],fa:["N/A"]}},36252:e=>{e.exports={en:["No data here"],fa:["No data here"]}},11254:e=>{e.exports={en:["No scale (fullscreen)"],fa:["No Scale (Fullscreen)"]}},9140:e=>{e.exports={en:["No sync"],fa:["No sync"]}},50910:e=>{e.exports={en:["No volume data"],fa:["No volume data"]}},94389:e=>{e.exports={en:["Note"],fa:["یادداشت"]}},26899:e=>{e.exports={en:["Nov"],fa:["Nov"]}},67891:e=>{e.exports={en:["Norfolk Island"],fa:["Norfolk Island"]}},40977:e=>{e.exports={en:["Nairobi"],fa:["Nairobi"]}},40544:e=>{e.exports={en:["New York"],fa:["نیویورک"]}},66103:e=>{e.exports={en:["New Zealand"],fa:["New Zealand"]}},15512:e=>{e.exports={en:["New pane above"],fa:["New pane above"]}},52160:e=>{e.exports={en:["New pane below"],fa:["New pane below"]}},15402:e=>{e.exports={en:["Next time you can use {shortcut} for quick paste"],fa:["Next time you can use {shortcut} for quick paste"]}},94600:e=>{e.exports={en:["Nicosia"],fa:["Nicosia"]}},73013:e=>{e.exports={en:["Something went wrong"],fa:["Something went wrong"]}},4509:e=>{e.exports={en:["Something went wrong. Please try again later."],fa:["Something went wrong. Please try again later."]}},43047:e=>{e.exports={en:["Save New Chart Layout"],fa:["Save New Chart Layout"]}},76266:e=>{e.exports={en:["Save as"],fa:["ذخیره به عنوان"]}},55502:e=>{e.exports={en:["San Salvador"],fa:["San Salvador"]}},30231:e=>{e.exports={en:["Santiago"],fa:["Santiago"]}},91912:e=>{e.exports={en:["Sao Paulo"],
fa:["سائوپلو"]}},43931:e=>{e.exports={en:["Scale currency"],fa:["Scale currency"]}},43758:e=>{e.exports={en:["Scale price chart only"],fa:["Scale price chart only"]}},40012:e=>{e.exports={en:["Scale unit"],fa:["Scale unit"]}},69904:e=>{e.exports={en:["Schiff"],fa:["شیف"]}},42608:e=>{e.exports={en:["Schiff Pitchfork"],fa:["Schiff Pitchfork"]}},76078:e=>{e.exports={en:["Script may be not updated if you leave the page."],fa:["Script may be not updated if you leave the page."]}},32514:e=>{e.exports={en:["Settings"],fa:["تنظیمات"]}},70784:e=>{e.exports={en:["Second fraction part is invalid."],fa:["Second fraction part is invalid."]}},75594:e=>{e.exports={en:["Security info"],fa:["Security info"]}},21973:e=>{e.exports={en:["Send to back"],fa:["آخرین"]}},71179:e=>{e.exports={en:["Send backward"],fa:["عقب"]}},26820:e=>{e.exports={en:["Seoul"],fa:["سئول"]}},6816:e=>{e.exports={en:["Sep"],fa:["Sep"]}},94031:e=>{e.exports={en:["Session"],fa:["Session"]}},83298:e=>{e.exports={en:["Session volume profile"],fa:["Session volume profile"]}},66707:e=>{e.exports={en:["Session breaks"],fa:["تنفس معاملاتی"]}},1852:e=>{e.exports={en:["Shanghai"],fa:["شانگهای"]}},8075:e=>{e.exports={en:["Short Position"],fa:["Short Position"]}},98334:e=>{e.exports={en:["Show"],fa:["نمایش"]}},85891:e=>{e.exports={en:["Show all drawings"],fa:["Show all drawings"]}},25881:e=>{e.exports={en:["Show all drawings and indicators"],fa:["Show all drawings and indicators"]}},86738:e=>{e.exports={en:["Show all drawings, indicators, positions & orders"],fa:["Show all drawings, indicators, positions & orders"]}},98753:e=>{e.exports={en:["Show all indicators"],fa:["Show all indicators"]}},55418:e=>{e.exports={en:["Show all ideas"],fa:["Show All Ideas"]}},20506:e=>{e.exports={en:["Show all positions & orders"],fa:["Show all positions & orders"]}},33158:e=>{e.exports={en:["Show continuous contract switch"],fa:["Show continuous contract switch"]}},81465:e=>{e.exports={en:["Show contract expiration"],fa:["Show contract expiration"]}},29449:e=>{e.exports={en:["Show dividends"],fa:["Show Dividends"]}},37113:e=>{e.exports={en:["Show earnings"],fa:["Show Earnings"]}},10261:e=>{e.exports={en:["Show ideas of followed users"],fa:["Show Ideas of Followed Users"]}},68112:e=>{e.exports={en:["Show latest news and Minds"],fa:["Show latest news and Minds"]}},44020:e=>{e.exports={en:["Show my ideas only"],fa:["Show My Ideas Only"]}},50849:e=>{e.exports={en:["Show splits"],fa:["Show Splits"]}},67751:e=>{e.exports={en:["Signpost"],fa:["Signpost"]}},77377:e=>{e.exports={en:["Singapore"],fa:["سنگاپور"]}},39090:e=>{e.exports={en:["Sine Line"],fa:["Sine Line"]}},66205:e=>{e.exports={en:["Square"],fa:["Square"]}},86146:e=>{e.exports={en:["Studies limit exceeded: {number} studies per layout.\nPlease, remove some studies."],fa:["Studies limit exceeded: {number} studies per layout.\nPlease, remove some studies."]}},92516:e=>{e.exports={en:["Style"],fa:["نحوه نمایش"]}},61507:e=>{e.exports={en:["Stack on the left"],fa:["Stack on the left"]}},97800:e=>{e.exports={en:["Stack on the right"],
fa:["Stack on the right"]}},85166:e=>{e.exports={en:["Start using keyboard navigation mode. Press {shortcut}"],fa:["Start using keyboard navigation mode. Press {shortcut}"]}},4035:e=>{e.exports={en:["Stay in drawing mode"],fa:["ماندن در حالت ترسیم"]}},69217:e=>{e.exports={en:["Step line"],fa:["Step Line"]}},43114:e=>{e.exports={en:["Sticker"],fa:["Sticker"]}},86716:e=>{e.exports={en:["Stockholm"],fa:["Stockholm"]}},1145:e=>{e.exports={en:["Submicro"],fa:["Submicro"]}},63375:e=>{e.exports={en:["Submillennium"],fa:["Submillennium"]}},30585:e=>{e.exports={en:["Subminuette"],fa:["کمتر از دقیقه"]}},67948:e=>{e.exports={en:["Supercycle"],fa:["Supercycle"]}},3348:e=>{e.exports={en:["Supermillennium"],fa:["Supermillennium"]}},18905:e=>{e.exports={en:["Switch to {resolution}"],fa:["Switch to {resolution}"]}},31622:e=>{e.exports={en:["Sydney"],fa:["سیدنی"]}},70963:e=>{e.exports={en:["Symbol Error"],fa:["Symbol Error"]}},32390:e=>{e.exports={en:["Symbol name label"],fa:["Symbol Name Label"]}},10127:e=>{e.exports={en:["Symbol last price label"],fa:["Symbol Last Value Label"]}},39079:e=>{e.exports={en:["Sync globally"],fa:["Sync globally"]}},46607:e=>{e.exports={en:["Sync in layout"],fa:["Sync To All Charts"]}},76519:e=>{e.exports={en:["Point & figure"],fa:["Point & Figure"]}},39949:e=>{e.exports={en:["Polyline"],fa:["Polyline"]}},371:e=>{e.exports={en:["Path"],fa:["Path"]}},59256:e=>{e.exports={en:["Parallel Channel"],fa:["Parallel Channel"]}},61879:e=>{e.exports={en:["Paris"],fa:["پاریس"]}},35140:e=>{e.exports={en:["Paste"],fa:["Paste"]}},6919:e=>{e.exports={en:["Percent"],fa:["Percent"]}},24436:e=>{e.exports={en:["Perth"],fa:["Perth"]}},14055:e=>{e.exports={en:["Phoenix"],fa:["Phoenix"]}},34156:e=>{e.exports={en:["Pitchfan"],fa:["Pitchfan"]}},19634:e=>{e.exports={en:["Pitchfork"],fa:["Pitchfork"]}},33110:e=>{e.exports={en:["Pin to new left scale"],fa:["Pin to new left scale"]}},28280:e=>{e.exports={en:["Pin to new right scale"],fa:["Pin to new right scale"]}},14115:e=>{e.exports={en:["Pin to left scale"],fa:["Pin to left scale"]}},72046:e=>{e.exports={en:["Pin to left scale (hidden)"],fa:["Pin to left scale (hidden)"]}},81054:e=>{e.exports={en:["Pin to right scale"],fa:["Pin to right scale"]}},16986:e=>{e.exports={en:["Pin to right scale (hidden)"],fa:["Pin to right scale (hidden)"]}},60035:e=>{e.exports={en:["Pin to scale (now left)"],fa:["Pin to scale (now left)"]}},94210:e=>{e.exports={en:["Pin to scale (now no scale)"],fa:["Pin to scale (now no scale)"]}},10761:e=>{e.exports={en:["Pin to scale (now right)"],fa:["Pin to scale (now right)"]}},76150:e=>{e.exports={en:["Pin to scale (now {label})"],fa:["Pin to scale (now {label})"]}},29436:e=>{e.exports={en:["Pin to scale {label}"],fa:["Pin to scale {label}"]}},2165:e=>{e.exports={en:["Pin to scale {label} (hidden)"],fa:["Pin to scale {label} (hidden)"]}},90095:e=>{e.exports={en:["Pinned to left scale"],fa:["Pinned to left scale"]}},32538:e=>{e.exports={en:["Pinned to left scale (hidden)"],fa:["Pinned to left scale (hidden)"]}},44579:e=>{e.exports={
en:["Pinned to right scale"],fa:["Pinned to right scale"]}},94559:e=>{e.exports={en:["Pinned to right scale (hidden)"],fa:["Pinned to right scale (hidden)"]}},12645:e=>{e.exports={en:["Pinned to scale {label}"],fa:["Pinned to scale {label}"]}},3564:e=>{e.exports={en:["Pinned to scale {label} (hidden)"],fa:["Pinned to scale {label} (hidden)"]}},71566:e=>{e.exports={en:["Plus button"],fa:["Plus button"]}},28298:e=>{e.exports={en:["Please give us a clipboard writing permission in your browser or press {keystroke}"],fa:["Please give us a clipboard writing permission in your browser or press {keystroke}"]}},81248:e=>{e.exports={en:["Prague"],fa:["Prague"]}},81712:e=>{e.exports={en:["Press and hold {key} while zooming to maintain the chart position"],fa:["Press and hold {key} while zooming to maintain the chart position"]}},91282:e=>{e.exports={en:["Price Label"],fa:["Price Label"]}},97512:e=>{e.exports={en:["Price Note"],fa:["Price Note"]}},68941:e=>{e.exports={en:["Price Range"],fa:["محدوده قیمتی"]}},66123:e=>{e.exports={en:["Price format is invalid."],fa:["Price format is invalid."]}},72926:e=>{e.exports={en:["Price line"],fa:["خط قیمت"]}},59189:e=>{e.exports={en:["Primary"],fa:["اصلی"]}},75747:e=>{e.exports={en:["Projection"],fa:["Projection"]}},55801:e=>{e.exports={en:["Published on {customer}, {date}"],fa:["Published on {customer}, {date}"]}},28756:e=>{e.exports={en:["Qatar"],fa:["Qatar"]}},57959:e=>{e.exports={en:["Quick search. Press {shortcut}"],fa:["Quick search. Press {shortcut}"]}},56820:e=>{e.exports={en:["Rotated Rectangle"],fa:["Rotated Rectangle"]}},52961:e=>{e.exports={en:["Rome"],fa:["Rome"]}},50318:e=>{e.exports={en:["Ray"],fa:["Ray"]}},55169:e=>{e.exports={en:["Range"],fa:["Range"]}},13386:e=>{e.exports={en:["Reykjavik"],fa:["Reykjavik"]}},26001:e=>{e.exports={en:["Rectangle"],fa:["مستطیل"]}},48236:e=>{e.exports={en:["Redo"],fa:["حالت بعدی"]}},2460:e=>{e.exports={en:["Regression Trend"],fa:["روند رگراسیون"]}},67410:e=>{e.exports={en:["Remove"],fa:["حذف"]}},96374:e=>{e.exports={en:["Remove drawings"],fa:["Remove drawings"]}},99984:e=>{e.exports={en:["Remove indicators"],fa:["Remove Indicators"]}},3061:e=>{e.exports={en:["Remove this financial metric from favorites"],fa:["Remove this financial metric from favorites"]}},58764:e=>{e.exports={en:["Remove this indicator from favorites"],fa:["Remove this indicator from favorites"]}},22584:e=>{e.exports={en:["Rename Chart Layout"],fa:["Rename Chart Layout"]}},88130:e=>{e.exports={en:["Renko"],fa:["Renko"]}},75246:e=>{e.exports={en:["Reset chart view"],fa:["Reset chart view"]}},88853:e=>{e.exports={en:["Reset points"],fa:["Reset points"]}},15332:e=>{e.exports={en:["Reset price scale"],fa:["Reset Price Scale"]}},54170:e=>{e.exports={en:["Reset time scale"],fa:["Reset Time Scale"]}},37974:e=>{e.exports={en:["Riyadh"],fa:["Riyadh"]}},94022:e=>{e.exports={en:["Riga"],fa:["Riga"]}},60630:e=>{e.exports={en:["Runtime error"],fa:["Runtime error"]}},66719:e=>{e.exports={en:["Warning"],fa:["Warning"]}},5959:e=>{e.exports={en:["Warsaw"],fa:["ورشو"]}},94465:e=>{e.exports={
en:["Toggle auto scale"],fa:["Toggle auto scale"]}},46992:e=>{e.exports={en:["Toggle log scale"],fa:["Toggle log scale"]}},98549:e=>{e.exports={en:["Tokelau"],fa:["Tokelau"]}},69122:e=>{e.exports={en:["Tokyo"],fa:["توکیو"]}},10095:e=>{e.exports={en:["Toronto"],fa:["تورنتو"]}},11034:e=>{e.exports={en:["Taipei"],fa:["چین تایپه"]}},79995:e=>{e.exports={en:["Tallinn"],fa:["Tallinn"]}},6686:e=>{e.exports={en:["Tehran"],fa:["تهران"]}},93553:e=>{e.exports={en:["Template"],fa:["Template"]}},81657:e=>{e.exports={en:["The data vendor doesn't provide volume data for this symbol."],fa:["The data vendor doesn't provide volume data for this symbol."]}},49947:e=>{e.exports={en:["The publication preview could not be loaded. Please disable your browser extensions and try again."],fa:["The publication preview could not be loaded. Please disable your browser extensions and try again."]}},93738:e=>{e.exports={en:["This file is too big. Max size is {value}."],fa:["This file is too big. Max size is {value}."]}},59519:e=>{e.exports={en:["This indicator cannot be applied to another indicator."],fa:["This indicator cannot be applied to another indicator."]}},18260:e=>{e.exports={en:["This script contains an error. Please contact its author."],fa:["This script contains an error. Please contact its author."]}},76989:e=>{e.exports={en:["This script is invite-only. To request access, please contact its author."],fa:["This script is invite-only. To request access, please contact its author."]}},47773:e=>{e.exports={en:["This symbol is only available on {linkStart}TradingView{linkEnd}."],fa:["The symbol available only on {linkStart}TradingView{linkEnd}."]}},46982:e=>{e.exports={en:["Three Drives Pattern"],fa:["Three Drives Pattern"]}},24821:e=>{e.exports={en:["Ticks"],fa:["Ticks"]}},80254:e=>{e.exports={en:["Tick-based intervals are not available for {ticker}."],fa:["Tick-based intervals are not available for {ticker}."]}},12806:e=>{e.exports={en:["Time"],fa:["Time"]}},20909:e=>{e.exports={en:["Time zone"],fa:["منطقه زمانی"]}},46852:e=>{e.exports={en:["Time Cycles"],fa:["Time Cycles"]}},17809:e=>{e.exports={en:["Time Price Opportunity"],fa:["Time Price Opportunity"]}},66823:e=>{e.exports={en:["Trade"],fa:["Trade"]}},7697:e=>{e.exports={en:["TradingView is interactive and has commands to use with a screen reader. The following is a list of keyboard commands available to interact on the platform"],fa:["TradingView is interactive and has commands to use with a screen reader. The following is a list of keyboard commands available to interact on the platform"]}},35757:e=>{e.exports={en:["Trend Angle"],fa:["Trend Angle"]}},97339:e=>{e.exports={en:["Trend Line"],fa:["Trend Line"]}},80583:e=>{e.exports={en:["Trend-Based Fib Extension"],fa:["Trend-Based Fib Extension"]}},72159:e=>{e.exports={en:["Trend-Based Fib Time"],fa:["Trend-Based Fib Time"]}},1671:e=>{e.exports={en:["Triangle"],fa:["Triangle"]}},76152:e=>{e.exports={en:["Triangle Down"],fa:["Triangle Down"]}},90148:e=>{e.exports={en:["Triangle Pattern"],fa:["Triangle Pattern"]}},21236:e=>{e.exports={
en:["Triangle Up"],fa:["Triangle Up"]}},21007:e=>{e.exports={en:["Tunis"],fa:["Tunis"]}},1833:e=>{e.exports={en:["UTC"],fa:["UTC"]}},14804:e=>{e.exports={en:["Undo"],fa:["حالت قبلی"]}},15432:e=>{e.exports={en:["Units"],fa:["Units"]}},11768:e=>{e.exports={en:["Unknown error"],fa:["Unknown error"]}},99894:e=>{e.exports={en:["Unlock"],fa:["Unlock"]}},75546:e=>{e.exports={en:["Unsupported interval"],fa:["Unsupported interval"]}},8580:e=>{e.exports={en:["User-defined error"],fa:["User-defined error"]}},40693:e=>{e.exports={en:["Volume Profile Fixed Range"],fa:["Volume Profile Fixed Range"]}},39903:e=>{e.exports={en:["Volume Profile indicator available only on our upgraded plans."],fa:["Volume Profile indicator available only on our upgraded plans."]}},93722:e=>{e.exports={en:["Volume candles"],fa:["Volume candles"]}},69156:e=>{e.exports={en:["Volume data is not provided in BIST MIXED data plan."],fa:["Volume data is not provided in BIST MIXED data plan."]}},92763:e=>{e.exports={en:["Volume footprint"],fa:["Volume footprint"]}},32838:e=>{e.exports={en:["Vancouver"],fa:["ونکوور"]}},29535:e=>{e.exports={en:["Vertical Line"],fa:["Vertical Line"]}},23160:e=>{e.exports={en:["Vienna"],fa:["Vienna"]}},60534:e=>{e.exports={en:["Vilnius"],fa:["Vilnius"]}},40091:e=>{e.exports={en:["Visibility"],fa:["Visibility"]}},54853:e=>{e.exports={en:["Visibility on intervals"],fa:["Visibility on intervals"]}},10309:e=>{e.exports={en:["Visible on mouse over"],fa:["Visible on Mouse Over"]}},4077:e=>{e.exports={en:["Visual order"],fa:["ترتیب نمایش"]}},11316:e=>{e.exports={en:["X Cross"],fa:["X Cross"]}},42231:e=>{e.exports={en:["XABCD Pattern"],fa:["XABCD Pattern"]}},25059:e=>{e.exports={en:["You cannot see this pivot timeframe on this resolution"],fa:["You cannot see this pivot timeframe on this resolution"]}},53168:e=>{e.exports={en:["Yangon"],fa:["Yangon"]}},62859:e=>{e.exports={en:["Zurich"],fa:["Zurich"]}},47977:e=>{e.exports={en:["change Elliott degree"],fa:["change Elliott degree"]}},61557:e=>{e.exports={en:["change no overlapping labels"],fa:["change no overlapping labels"]}},76852:e=>{e.exports={en:["change average close price label visibility"],fa:["change average close price label visibility"]}},1022:e=>{e.exports={en:["change average close price line visibility"],fa:["change average close price line visibility"]}},69362:e=>{e.exports={en:["change bid and ask labels visibility"],fa:["change bid and ask labels visibility"]}},52919:e=>{e.exports={en:["change bid and ask lines visibility"],fa:["change bid and ask lines visibility"]}},32302:e=>{e.exports={en:["change currency"],fa:["change currency"]}},68846:e=>{e.exports={en:["change chart layout to {title}"],fa:["change chart layout to {title}"]}},18867:e=>{e.exports={en:["change continuous contract switch visibility"],fa:["change continuous contract switch visibility"]}},39383:e=>{e.exports={en:["change countdown to bar close visibility"],fa:["change countdown to bar close visibility"]}},16979:e=>{e.exports={en:["change date range"],fa:["change date range"]}},53929:e=>{e.exports={
en:["change dividends visibility"],fa:["change dividends visibility"]}},6119:e=>{e.exports={en:["change events visibility on chart"],fa:["change events visibility on chart"]}},6819:e=>{e.exports={en:["change earnings visibility"],fa:["change earnings visibility"]}},85532:e=>{e.exports={en:["change futures contract expiration visibility"],fa:["change futures contract expiration visibility"]}},24226:e=>{e.exports={en:["change high and low price labels visibility"],fa:["change high and low price labels visibility"]}},80692:e=>{e.exports={en:["change high and low price lines visibility"],fa:["change high and low price lines visibility"]}},24893:e=>{e.exports={en:["change indicators name labels visibility"],fa:["change indicators name labels visibility"]}},64729:e=>{e.exports={en:["change indicators value labels visibility"],fa:["change indicators value labels visibility"]}},50243:e=>{e.exports={en:["change latest news and Minds visibility"],fa:["change latest news and Minds visibility"]}},88849:e=>{e.exports={en:["change linking group"],fa:["change linking group"]}},14691:e=>{e.exports={en:["change pane height"],fa:["change pane height"]}},96379:e=>{e.exports={en:["change plus button visibility"],fa:["change plus button visibility"]}},30870:e=>{e.exports={en:["change pre/post market price label visibility"],fa:["change pre/post market price label visibility"]}},11718:e=>{e.exports={en:["change pre/post market price line visibility"],fa:["change pre/post market price line visibility"]}},58419:e=>{e.exports={en:["change previous close price line visibility"],fa:["change previous close price line visibility"]}},8662:e=>{e.exports={en:["change price line visibility"],fa:["change price line visibility"]}},2509:e=>{e.exports={en:["change price to bar ratio"],fa:["change price to bar ratio"]}},32829:e=>{e.exports={en:["change resolution"],fa:["تغییر رزولوشن"]}},35400:e=>{e.exports={en:["change symbol"],fa:["تغییر نماد"]}},73357:e=>{e.exports={en:["change symbol labels visibility"],fa:["change symbol labels visibility"]}},67453:e=>{e.exports={en:["change symbol last value visibility"],fa:["change symbol last value visibility"]}},4729:e=>{e.exports={en:["change symbol previous close value visibility"],fa:["change symbol previous close value visibility"]}},87041:e=>{e.exports={en:["change session"],fa:["change session"]}},38413:e=>{e.exports={en:["change session breaks visibility"],fa:["change session breaks visibility"]}},49965:e=>{e.exports={en:["change series style"],fa:["change series style"]}},47474:e=>{e.exports={en:["change splits visibility"],fa:["change splits visibility"]}},20137:e=>{e.exports={en:["change timezone"],fa:["change timezone"]}},85975:e=>{e.exports={en:["change unit"],fa:["change unit"]}},1924:e=>{e.exports={en:["change visibility"],fa:["change visibility"]}},84331:e=>{e.exports={en:["change visibility at current interval"],fa:["change visibility at current interval"]}},45800:e=>{e.exports={en:["change visibility at current interval and above"],fa:["change visibility at current interval and above"]}},
75645:e=>{e.exports={en:["change visibility at current interval and below"],fa:["change visibility at current interval and below"]}},57916:e=>{e.exports={en:["change visibility at all intervals"],fa:["change visibility at all intervals"]}},98463:e=>{e.exports={en:["change {title} style"],fa:["change {title} style"]}},57122:e=>{e.exports={en:["change {title} text"],fa:["change {title} text"]}},63058:e=>{e.exports={en:["change {pointIndex} point"],fa:["change {pointIndex} point"]}},94566:e=>{e.exports={en:["charts by TradingView"],fa:["charts by TradingView"]}},32943:e=>{e.exports={en:["clone line tools"],fa:["clone line tools"]}},46219:e=>{e.exports={en:["create line tools group"],fa:["create line tools group"]}},95394:e=>{e.exports={en:["create line tools group from selection"],fa:["create line tools group from selection"]}},12898:e=>{e.exports={en:["create {tool}"],fa:["create {tool}"]}},94227:e=>{e.exports={en:["cut sources"],fa:["cut sources"]}},11500:e=>{e.exports={en:["cut {title}"],fa:["cut {title}"]}},12570:e=>{e.exports={en:["add line tool {lineTool} to group {name}"],fa:["add line tool {lineTool} to group {name}"]}},21162:e=>{e.exports={en:["add line tool(s) to group {group}"],fa:["add line tool(s) to group {group}"]}},67608:e=>{e.exports={en:["add this financial metric to entire layout"],fa:["add this financial metric to entire layout"]}},96677:e=>{e.exports={en:["add this indicator to entire layout"],fa:["add this indicator to entire layout"]}},58156:e=>{e.exports={en:["add this strategy to entire layout"],fa:["add this strategy to entire layout"]}},79290:e=>{e.exports={en:["add this symbol to entire layout"],fa:["add this symbol to entire layout"]}},68231:e=>{e.exports={en:["apply chart theme"],fa:["apply chart theme"]}},99551:e=>{e.exports={en:["apply all chart properties"],fa:["apply all chart properties"]}},89720:e=>{e.exports={en:["apply drawing template"],fa:["apply drawing template"]}},27851:e=>{e.exports={en:["apply factory defaults to selected sources"],fa:["apply factory defaults to selected sources"]}},70507:e=>{e.exports={en:["apply indicators to entire layout"],fa:["apply indicators to entire layout"]}},69604:e=>{e.exports={en:["apply study template {template}"],fa:["apply study template {template}"]}},86708:e=>{e.exports={en:["apply toolbars theme"],fa:["apply toolbars theme"]}},1979:e=>{e.exports={en:["bring group {title} forward"],fa:["bring group {title} forward"]}},53159:e=>{e.exports={en:["bring {title} to front"],fa:["bring {title} to front"]}},41966:e=>{e.exports={en:["bring {title} forward"],fa:["bring {title} forward"]}},44676:e=>{e.exports={en:["by TradingView"],fa:["by TradingView"]}},58850:e=>{e.exports={en:["date range lock"],fa:["date range lock"]}},35111:e=>{e.exports={en:["erase level line"],fa:["erase level line"]}},99395:e=>{e.exports={en:["exclude line tools from group {group}"],fa:["exclude line tools from group {group}"]}},82349:e=>{e.exports={en:["flip bars pattern"],fa:["flip bars pattern"]}},13017:e=>{e.exports={en:["hide {title}"],fa:["hide {title}"]}},62249:e=>{
e.exports={en:["hide marks on bars"],fa:["Hide Marks On Bars"]}},56558:e=>{e.exports={en:["interval lock"],fa:["interval lock"]}},6830:e=>{e.exports={en:["invert scale"],fa:["Invert Scale"]}},48818:e=>{e.exports={en:["insert {title}"],fa:["insert {title}"]}},56307:e=>{e.exports={en:["insert {title} after {targetTitle}"],fa:["insert {title} after {targetTitle}"]}},32960:e=>{e.exports={en:["insert {title} after {target}"],fa:["insert {title} after {target}"]}},57106:e=>{e.exports={en:["insert {title} before {target}"],fa:["insert {title} before {target}"]}},46229:e=>{e.exports={en:["insert {title} before {targetTitle}"],fa:["insert {title} before {targetTitle}"]}},43364:e=>{e.exports={en:["load default drawing template"],fa:["load default drawing template"]}},62011:e=>{e.exports={en:["loading..."],fa:["در حال بارگزاری ..."]}},76104:e=>{e.exports={en:["lock {title}"],fa:["lock {title}"]}},20453:e=>{e.exports={en:["lock group {group}"],fa:["lock group {group}"]}},18942:e=>{e.exports={en:["lock objects"],fa:["lock objects"]}},98277:e=>{e.exports={en:["move"],fa:["move"]}},58228:e=>{e.exports={en:["move {title} to new left scale"],fa:["move {title} to new left scale"]}},77482:e=>{e.exports={en:["move {title} to new right scale"],fa:["move {title} to new right scale"]}},64077:e=>{e.exports={en:["move all scales to left"],fa:["move all scales to left"]}},19013:e=>{e.exports={en:["move all scales to right"],fa:["move all scales to right"]}},52510:e=>{e.exports={en:["move drawing(s)"],fa:["move drawing(s)"]}},79209:e=>{e.exports={en:["move left"],fa:["move left"]}},60114:e=>{e.exports={en:["move right"],fa:["move right"]}},44854:e=>{e.exports={en:["move scale"],fa:["move scale"]}},10625:e=>{e.exports={en:["make {title} no scale (Full screen)"],fa:["make {title} no scale (Full screen)"]}},76709:e=>{e.exports={en:["make group {group} invisible"],fa:["make group {group} invisible"]}},45987:e=>{e.exports={en:["make group {group} visible"],fa:["make group {group} visible"]}},78055:e=>{e.exports={en:["merge down"],fa:["merge down"]}},41866:e=>{e.exports={en:["merge to pane"],fa:["merge to pane"]}},52458:e=>{e.exports={en:["merge up"],fa:["merge up"]}},20965:e=>{e.exports={en:["mirror bars pattern"],fa:["mirror bars pattern"]}},90091:e=>{e.exports={en:["n/a"],fa:["n/a"]}},94981:e=>{e.exports={en:["scale price"],fa:["scale price"]}},63796:e=>{e.exports={en:["scale price chart only"],fa:["scale price chart only"]}},70771:e=>{e.exports={en:["scale time"],fa:["scale time"]}},42070:e=>{e.exports={en:["scroll"],fa:["scroll"]}},87840:e=>{e.exports={en:["scroll time"],fa:["scroll time"]}},82241:e=>{e.exports={en:["set price scale selection strategy to {title}"],fa:["set price scale selection strategy to {title}"]}},40962:e=>{e.exports={en:["send {title} backward"],fa:["send {title} backward"]}},5005:e=>{e.exports={en:["send {title} to back"],fa:["send {title} to back"]}},69546:e=>{e.exports={en:["send group {title} backward"],fa:["send group {title} backward"]}},63934:e=>{e.exports={en:["share line tools globally"],
fa:["share line tools globally"]}},90221:e=>{e.exports={en:["share line tools in layout"],fa:["share line tools in layout"]}},13336:e=>{e.exports={en:["show all ideas"],fa:["show all ideas"]}},91395:e=>{e.exports={en:["show ideas of followed users"],fa:["show ideas of followed users"]}},57460:e=>{e.exports={en:["show my ideas only"],fa:["show my ideas only"]}},4114:e=>{e.exports={en:["stay in drawing mode"],fa:["stay in drawing mode"]}},3350:e=>{e.exports={en:["stop syncing drawing"],fa:["stop syncing drawing"]}},49183:e=>{e.exports={en:["stop syncing line tool(s)"],fa:["stop syncing line tool(s)"]}},53278:e=>{e.exports={en:["symbol lock"],fa:["symbol lock"]}},91677:e=>{e.exports={en:["sync time"],fa:["sync time"]}},3140:e=>{e.exports={en:["powered by"],fa:["powered by"]}},92800:e=>{e.exports={en:["powered by TradingView"],fa:["powered by TradingView"]}},62192:e=>{e.exports={en:["paste drawing"],fa:["paste drawing"]}},1064:e=>{e.exports={en:["paste indicator"],fa:["paste indicator"]}},57010:e=>{e.exports={en:["paste {title}"],fa:["paste {title}"]}},78690:e=>{e.exports={en:["pin to left scale"],fa:["pin to left scale"]}},7495:e=>{e.exports={en:["pin to right scale"],fa:["pin to right scale"]}},81566:e=>{e.exports={en:["pin to scale {label}"],fa:["pin to scale {label}"]}},2618:e=>{e.exports={en:["rearrange panes"],fa:["rearrange panes"]}},43172:e=>{e.exports={en:["remove all studies"],fa:["remove all studies"]}},56253:e=>{e.exports={en:["remove all studies and drawing tools"],fa:["remove all studies and drawing tools"]}},27171:e=>{e.exports={en:["remove deselected empty line tools"],fa:["remove deselected empty line tools"]}},30538:e=>{e.exports={en:["remove drawings"],fa:["remove drawings"]}},1193:e=>{e.exports={en:["remove drawings group"],fa:["remove drawings group"]}},38199:e=>{e.exports={en:["remove line data sources"],fa:["remove line data sources"]}},93333:e=>{e.exports={en:["remove pane"],fa:["remove pane"]}},94543:e=>{e.exports={en:["remove {title}"],fa:["remove {title}"]}},41430:e=>{e.exports={en:["removing line tools group {name}"],fa:["removing line tools group {name}"]}},80491:e=>{e.exports={en:["rename group {group} to {newName}"],fa:["rename group {group} to {newName}"]}},85366:e=>{e.exports={en:["reset layout sizes"],fa:["reset layout sizes"]}},3323:e=>{e.exports={en:["reset scales"],fa:["reset scales"]}},17336:e=>{e.exports={en:["reset time scale"],fa:["Reset Time Scale"]}},47418:e=>{e.exports={en:["resize layout"],fa:["resize layout"]}},85815:e=>{e.exports={en:["restore defaults"],fa:["restore defaults"]}},96881:e=>{e.exports={en:["restore study defaults"],fa:["restore study defaults"]}},42240:e=>{e.exports={en:["toggle auto scale"],fa:["toggle auto scale"]}},46054:e=>{e.exports={en:["toggle collapsed pane state"],fa:["toggle collapsed pane state"]}},24736:e=>{e.exports={en:["toggle indexed to 100 scale"],fa:["toggle indexed to 100 scale"]}},49695:e=>{e.exports={en:["toggle lock scale"],fa:["toggle lock scale"]}},49403:e=>{e.exports={en:["toggle log scale"],fa:["toggle log scale"]}},98994:e=>{
e.exports={en:["toggle percentage scale"],fa:["Toggle Percentage Scale"]}},80688:e=>{e.exports={en:["toggle regular scale"],fa:["toggle regular scale"]}},46807:e=>{e.exports={en:["track time"],fa:["track time"]}},8040:e=>{e.exports={en:["turn line tools sharing off"],fa:["turn line tools sharing off"]}},23230:e=>{e.exports={en:["unlock objects"],fa:["unlock objects"]}},74590:e=>{e.exports={en:["unlock group {group}"],fa:["unlock group {group}"]}},12525:e=>{e.exports={en:["unlock {title}"],fa:["unlock {title}"]}},81576:e=>{e.exports={en:["unmerge to new bottom pane"],fa:["unmerge to new bottom pane"]}},79443:e=>{e.exports={en:["unmerge up"],fa:["unmerge up"]}},46453:e=>{e.exports={en:["unmerge down"],fa:["unmerge down"]}},94656:e=>{e.exports={en:["{chartStyle} chart type isn't currently available for tick-based intervals."],fa:["{chartStyle} chart type isn't currently available for tick-based intervals."]}},41643:e=>{e.exports={en:["{count} bars"],fa:["{count} میله"]}},83470:e=>{e.exports={en:["{symbol} financials by TradingView"],fa:["{symbol} financials by TradingView"]}},40947:e=>{e.exports={en:["{userName} published on {customer}, {date}"],fa:["{userName} published on {customer}, {date}"]}},91084:e=>{e.exports={en:["zoom"],fa:["zoom"]}},49856:e=>{e.exports={en:["zoom in"],fa:["zoom in"]}},73638:e=>{e.exports={en:["zoom out"],fa:["zoom out"]}},41807:e=>{e.exports={en:["day","days"],fa:["days"]}},42328:e=>{e.exports={en:["hour","hours"],fa:["hours"]}},98393:e=>{e.exports={en:["month","months"],fa:["months"]}},78318:e=>{e.exports={en:["minute","minutes"],fa:["minutes"]}},33232:e=>{e.exports={en:["second","seconds"],fa:["seconds"]}},89937:e=>{e.exports={en:["range","ranges"],fa:["ranges"]}},48898:e=>{e.exports={en:["week","weeks"],fa:["weeks"]}},11913:e=>{e.exports={en:["tick","ticks"],fa:["ticks"]}},58590:e=>{e.exports={en:["{count}m","{count}m"],fa:["{count}m"]}},47801:e=>{e.exports={en:["{count}d","{count}d"],fa:["{count}d"]}},46766:e=>{e.exports={en:["{count}y","{count}y"],fa:["{count}y"]}},56316:e=>{e.exports=Object.create(null),e.exports["#AAPL-symbol-description"]={en:["Apple Inc"],fa:["APPLE INC"]},e.exports["#AUDCAD-symbol-description"]={en:["Australian Dollar/Canadian Dollar"],fa:["Australian Dollar/Canadian Dollar"]},e.exports["#AUDCHF-symbol-description"]={en:["Australian Dollar / Swiss Franc"],fa:["Australian Dollar/Swiss Franc"]},e.exports["#AUDJPY-symbol-description"]={en:["Australian Dollar / Japanese Yen"],fa:["Australian Dollar/Japanese Yen"]},e.exports["#AUDNZD-symbol-description"]={en:["Australian Dollar / New Zealand Dollar"],fa:["Australian Dollar/New Zealand Dollar"]},e.exports["#AUDRUB-symbol-description"]={en:["Australian Dollar / Russian Ruble"],fa:["AUSTRALIAN DOLLAR / RUSSIAN RUBLE"]},e.exports["#AUDUSD-symbol-description"]={en:["Australian Dollar / U.S. Dollar"],fa:["Australian Dollar/U.S. Dollar"]},e.exports["#BRLJPY-symbol-description"]={en:["Brazilian Real / Japanese Yen"],fa:["Brazilian Real / Japanese Yen"]},e.exports["#BTCCAD-symbol-description"]={en:["Bitcoin / Canadian Dollar"],
fa:["Bitcoin / Canadian Dollar"]},e.exports["#BTCCNY-symbol-description"]={en:["Bitcoin / Chinese Yuan"],fa:["Bitcoin / Chinese Yuan"]},e.exports["#BTCEUR-symbol-description"]={en:["Bitcoin / Euro"],fa:["Bitcoin / Euro"]},e.exports["#BTCKRW-symbol-description"]={en:["Bitcoin / South Korean Won"],fa:["Bitcoin / South Korean Won"]},e.exports["#BTCRUR-symbol-description"]={en:["Bitcoin / Ruble"],fa:["Bitcoin / Ruble"]},e.exports["#BTCUSD-symbol-description"]={en:["Bitcoin / U.S. Dollar"],fa:["Bitcoin / Dollar"]},e.exports["#BVSP-symbol-description"]={en:["Brazil Bovespa Index"],fa:["Brazil Bovespa Index"]},e.exports["#CADJPY-symbol-description"]={en:["Canadian Dollar / Japanese Yen"],fa:["Canadian Dollar/Japanese Yen"]},e.exports["#CHFJPY-symbol-description"]={en:["Swiss Franc / Japanese Yen"],fa:["Swiss Franc/Japanese Yen"]},e.exports["#COPPER-symbol-description"]={en:["CFDs on Copper"],fa:["مس"]},e.exports["#ES1-symbol-description"]={en:["S&P 500 E-Mini Futures"],fa:["S&P 500 E-Mini Futures"]},e.exports["#ESP35-symbol-description"]={en:["IBEX 35 Index"],fa:["IBEX 35 Index"]},e.exports["#EUBUND-symbol-description"]={en:["Euro Bund"],fa:["Euro Bund"]},e.exports["#EURAUD-symbol-description"]={en:["Euro / Australian Dollar"],fa:["Euro Fx/Australian Dollar"]},e.exports["#EURBRL-symbol-description"]={en:["Euro / Brazilian Real"],fa:["Euro / Brazilian Real"]},e.exports["#EURCAD-symbol-description"]={en:["Euro / Canadian Dollar"],fa:["Euro Fx/Canadian Dollar"]},e.exports["#EURCHF-symbol-description"]={en:["Euro / Swiss Franc"],fa:["Euro Fx/Swiss Franc"]},e.exports["#EURGBP-symbol-description"]={en:["Euro / British Pound"],fa:["Euro Fx/British Pound"]},e.exports["#EURJPY-symbol-description"]={en:["Euro / Japanese Yen"],fa:["Euro Fx/Japanese Yen"]},e.exports["#EURNZD-symbol-description"]={en:["Euro / New Zealand Dollar"],fa:["Euro Fx/New Zealand Dollar"]},e.exports["#EURRUB-symbol-description"]={en:["Euro / Russian Ruble"],fa:["EURO / RUSSIAN RUBLE"]},e.exports["#EURRUB_TOM-symbol-description"]={en:["Euro / Russian Ruble TOM"],fa:["EUR/RUB TOM"]},e.exports["#EURSEK-symbol-description"]={en:["Euro / Swedish Krona"],fa:["Euro / Swedish Krona"]},e.exports["#EURTRY-symbol-description"]={en:["Euro / Turkish Lira"],fa:["Euro Fx/Turkish New Lira"]},e.exports["#EURUSD-symbol-description"]={en:["Euro / U.S. Dollar"],fa:["Euro Fx/U.S. Dollar"]},e.exports["#EUSTX50-symbol-description"]={en:["Euro Stoxx 50 Index"],fa:["Euro Stoxx 50 index of European listed shares"]},e.exports["#FRA40-symbol-description"]={en:["CAC 40 Index"],fa:["CAC 40 Index"]},e.exports["#GB10-symbol-description"]={en:["UK Government Bonds 10 yr"],fa:["UK Government Bonds 10 yr"]},e.exports["#GBPAUD-symbol-description"]={en:["British Pound / Australian Dollar"],fa:["British Pound/Australian Dollar"]},e.exports["#GBPCAD-symbol-description"]={en:["British Pound / Canadian Dollar"],fa:["British Pound/Canadian Dollar"]},e.exports["#GBPCHF-symbol-description"]={en:["British Pound / Swiss Franc"],fa:["British Pound/Swiss Franc"]},e.exports["#GBPEUR-symbol-description"]={
en:["British Pound / Euro"],fa:["POUND STERLING / EURO"]},e.exports["#GBPJPY-symbol-description"]={en:["British Pound / Japanese Yen"],fa:["British Pound/Japanese Yen"]},e.exports["#GBPNZD-symbol-description"]={en:["British Pound / New Zealand Dollar"],fa:["British Pound/New Zealand Dollar"]},e.exports["#GBPRUB-symbol-description"]={en:["British Pound / Russian Ruble"],fa:["Pound Sterling / Russian Ruble"]},e.exports["#GBPUSD-symbol-description"]={en:["British Pound / U.S. Dollar"],fa:["British Pound/U.S. Dollar"]},e.exports["#GER30-symbol-description"]={en:["DAX Index"],fa:["DAX index of German listed shares"]},e.exports["#GOOGL-symbol-description"]={en:["Alphabet Inc (Google) Class A"],fa:["GOOGLE INC"]},e.exports["#ITA40-symbol-description"]={en:["FTSE MIB Index"],fa:["FTSE MIB index of Italian listed shares"]},e.exports["#JPN225-symbol-description"]={en:["Nikkei 225 Index"],fa:["Nikkei 225 Index"]},e.exports["#JPYKRW-symbol-description"]={en:["Japanese Yen / South Korean Won"],fa:["YEN / WON"]},e.exports["#JPYRUB-symbol-description"]={en:["Japanese Yen / Russian Ruble"],fa:["YEN / RUSSIAN RUBLE"]},e.exports["#KA1-symbol-description"]={en:["Sugar #11 Futures"],fa:["Sugar #11 Futures"]},e.exports["#KG1-symbol-description"]={en:["Cotton Futures"],fa:["Cotton Futures"]},e.exports["#KT1-symbol-description"]={en:["Key Tronic Corр."],fa:["Key Tronic Corр."]},e.exports["#LKOH-symbol-description"]={en:["LUKOIL"],fa:["LUKOIL"]},e.exports["#LTCBTC-symbol-description"]={en:["Litecoin / Bitcoin"],fa:["Litecoin / Bitcoin"]},e.exports["#MGNT-symbol-description"]={en:["Magnit"],fa:["MAGNIT"]},e.exports["#MICEX-symbol-description"]={en:["MICEX Index"],fa:["MICEX INDEX"]},e.exports["#MNOD_ME.EQRP-symbol-description"]={en:["ADR GMK NORILSKIYNIKEL ORD SHS [REPO]"],fa:["ADR GMK NORILSKIYNIKEL ORD SHS [REPO]"]},e.exports["#MSFT-symbol-description"]={en:["Microsoft Corp."],fa:["MICROSOFT CORP"]},e.exports["#NAS100-symbol-description"]={en:["US 100 Cash CFD"],fa:["NASDAQ 100 index of US listed shares"]},e.exports["#NGAS-symbol-description"]={en:["Natural Gas (Henry Hub)"],fa:["Natural Gas (Henry Hub)"]},e.exports["#NKY-symbol-description"]={en:["Nikkei 225 Index"],fa:["Nikkei 225 Index"]},e.exports["#NZDJPY-symbol-description"]={en:["New Zealand Dollar / Japanese Yen"],fa:["New Zealand Dollar/Japanese Yen"]},e.exports["#NZDUSD-symbol-description"]={en:["New Zealand Dollar / U.S. Dollar"],fa:["New Zealand Dollar/U.S. Dollar"]},e.exports["#RB1-symbol-description"]={en:["RBOB Gasoline Futures"],fa:["RBOB Gasoline Futures"]},e.exports["#RTS-symbol-description"]={en:["Russian RTS Index"],fa:["Russian RTS Index"]},e.exports["#SBER-symbol-description"]={en:["SBERBANK"],fa:["SBERBANK"]},e.exports["#SPX500-symbol-description"]={en:["S&P 500 Index"],fa:["S&P 500 index of US listed shares"]},e.exports["#TWTR-symbol-description"]={en:["Twitter Inc"],fa:["TWITTER INC"]},e.exports["#UK100-symbol-description"]={en:["FTSE 100 Index"],fa:["FTSE 100 Index"]},e.exports["#USDBRL-symbol-description"]={en:["U.S. Dollar / Brazilian Real"],
fa:["U.S. Dollar / Brazilian Real"]},e.exports["#USDCAD-symbol-description"]={en:["U.S. Dollar / Canadian Dollar"],fa:["U.S. Dollar/Canadian Dollar"]},e.exports["#USDCHF-symbol-description"]={en:["U.S. Dollar / Swiss Franc"],fa:["U.S. Dollar/Swiss Franc"]},e.exports["#USDCNY-symbol-description"]={en:["U.S. Dollar / Chinese Yuan"],fa:["U.S. Dollar / Yuan Renminbi"]},e.exports["#USDDKK-symbol-description"]={en:["U.S. Dollar / Danish Krone"],fa:["US DOLLAR / DANISH KRONE"]},e.exports["#USDHKD-symbol-description"]={en:["U.S. Dollar / Hong Kong Dollar"],fa:["U.S. Dollar/Hong Kong Dollar"]},e.exports["#USDIDR-symbol-description"]={en:["U.S. Dollar / Rupiah"],fa:["U.S. Dollar / Rupiah"]},e.exports["#USDINR-symbol-description"]={en:["U.S. Dollar / Indian Rupee"],fa:["U.S. Dollar / Indian Rupee"]},e.exports["#USDJPY-symbol-description"]={en:["U.S. Dollar / Japanese Yen"],fa:["U.S. Dollar/Japanese Yen"]},e.exports["#USDKRW-symbol-description"]={en:["U.S. Dollar / South Korean"],fa:["US DOLLAR / WON"]},e.exports["#USDMXN-symbol-description"]={en:["U.S. Dollar / Mexican Peso"],fa:["U.S. Dollar / Mexican Peso"]},e.exports["#USDPHP-symbol-description"]={en:["U.S. Dollar / Philippine peso"],fa:["U.S. Dollar / Philippine peso"]},e.exports["#USDRUB-symbol-description"]={en:["U.S. Dollar / Russian Ruble"],fa:["US DOLLAR / RUSSIAN RUBLE"]},e.exports["#USDRUB_TOM-symbol-description"]={en:["U.S. Dollar / Russian Ruble TOM"],fa:["USD/RUB TOM"]},e.exports["#USDSEK-symbol-description"]={en:["U.S. Dollar / Swedish Krona"],fa:["U.S. Dollar/Swedish Krona"]},e.exports["#USDSGD-symbol-description"]={en:["U.S. Dollar / Singapore Dollar"],fa:["US DOLLAR / SINGAPORE DOLLAR"]},e.exports["#USDTRY-symbol-description"]={en:["U.S. Dollar / Turkish Lira"],fa:["U.S. Dollar/Turkish New Lira"]},e.exports["#VTBR-symbol-description"]={en:["VTB"],fa:["VTB"]},e.exports["#XAGUSD-symbol-description"]={en:["Silver / U.S. Dollar"],fa:["Silver/U.S. Dollar"]},e.exports["#XAUUSD-symbol-description"]={en:["Gold Spot / U.S. Dollar"],fa:["Gold / U.S. Dollar"]},e.exports["#XPDUSD-symbol-description"]={en:["CFDs on Palladium"],fa:["CFDs on Palladium"]},e.exports["#XPTUSD-symbol-description"]={en:["Platinum / U.S. Dollar"],fa:["Platinum/U.S. Dollar"]},e.exports["#ZS1-symbol-description"]={en:["Soybean Futures - ECBT"],fa:["Soybean Futures - ECBT"]},e.exports["#ZW1-symbol-description"]={en:["Wheat Futures - ECBT"],fa:["Wheat Futures - ECBT"]},e.exports["#BTCGBP-symbol-description"]={en:["Bitcoin / British Pound"],fa:["Bitcoin / British Pound"]},e.exports["#MICEXINDEXCF-symbol-description"]={en:["MOEX Russia Index"],fa:["MICEX Index"]},e.exports["#BTCAUD-symbol-description"]={en:["Bitcoin / Australian Dollar"],fa:["Bitcoin / Australian Dollar"]},e.exports["#BTCJPY-symbol-description"]={en:["Bitcoin / Japanese Yen"],fa:["Bitcoin / Japanese Yen"]},e.exports["#BTCBRL-symbol-description"]={en:["Bitcoin / Brazilian Real"],fa:["Bitcoin / Brazilian Real"]},e.exports["#PT10-symbol-description"]={en:["Portugal Government Bonds 10 yr"],fa:["Portugal Government Bonds 10 yr"]},
e.exports["#TXSX-symbol-description"]={en:["TSX 60 Index"],fa:["TSX 60 Index"]},e.exports["#VIXC-symbol-description"]={en:["TSX 60 VIX Index"],fa:["TSX 60 VIX"]},e.exports["#USDPLN-symbol-description"]={en:["U.S. Dollar / Polish Zloty"],fa:["USD/PLN"]},e.exports["#EURPLN-symbol-description"]={en:["Euro / Polish Zloty"],fa:["EUR/PLN"]},e.exports["#BTCPLN-symbol-description"]={en:["Bitcoin / Polish Zloty"],fa:["Bitcoin / Polish Zloty"]},e.exports["#CAC40-symbol-description"]={en:["CAC 40 Index"],fa:["CAC 40"]},e.exports["#XBTCAD-symbol-description"]={en:["Bitcoin / Canadian Dollar"],fa:["Bitcoin / Canadian Dollar"]},e.exports["#ITI2!-symbol-description"]={en:["Iron Ore Futures"],fa:["Iron Ore Futures"]},e.exports["#ITIF2018-symbol-description"]={en:["Iron Ore Futures"],fa:["Iron Ore Futures"]},e.exports["#ITIF2019-symbol-description"]={en:["Iron Ore Futures"],fa:["Iron Ore Futures"]},e.exports["#ITIF2020-symbol-description"]={en:["Iron Ore Futures"],fa:["Iron Ore Futures"]},e.exports["#ITIG2018-symbol-description"]={en:["Iron Ore Futures"],fa:["Iron Ore Futures"]},e.exports["#ITIG2019-symbol-description"]={en:["Iron Ore Futures"],fa:["Iron Ore Futures"]},e.exports["#ITIG2020-symbol-description"]={en:["Iron Ore Futures"],fa:["Iron Ore Futures"]},e.exports["#ITIH2018-symbol-description"]={en:["Iron Ore Futures"],fa:["Iron Ore Futures"]},e.exports["#ITIH2019-symbol-description"]={en:["Iron Ore Futures"],fa:["Iron Ore Futures"]},e.exports["#ITIH2020-symbol-description"]={en:["Iron Ore Futures"],fa:["Iron Ore Futures"]},e.exports["#ITIJ2018-symbol-description"]={en:["Iron Ore Futures"],fa:["Iron Ore Futures"]},e.exports["#ITIJ2019-symbol-description"]={en:["Iron Ore Futures"],fa:["Iron Ore Futures"]},e.exports["#ITIJ2020-symbol-description"]={en:["Iron Ore Futures"],fa:["Iron Ore Futures"]},e.exports["#ITIK2018-symbol-description"]={en:["Iron Ore Futures"],fa:["Iron Ore Futures"]},e.exports["#ITIK2019-symbol-description"]={en:["Iron Ore Futures"],fa:["Iron Ore Futures"]},e.exports["#ITIK2020-symbol-description"]={en:["Iron Ore Futures"],fa:["Iron Ore Futures"]},e.exports["#ITIM2017-symbol-description"]={en:["Iron Ore Futures"],fa:["Iron Ore Futures"]},e.exports["#ITIM2018-symbol-description"]={en:["Iron Ore Futures"],fa:["Iron Ore Futures"]},e.exports["#ITIM2019-symbol-description"]={en:["Iron Ore Futures"],fa:["Iron Ore Futures"]},e.exports["#ITIM2020-symbol-description"]={en:["Iron Ore Futures"],fa:["Iron Ore Futures"]},e.exports["#ITIN2017-symbol-description"]={en:["Iron Ore Futures"],fa:["Iron Ore Futures"]},e.exports["#ITIN2018-symbol-description"]={en:["Iron Ore Futures"],fa:["Iron Ore Futures"]},e.exports["#ITIN2019-symbol-description"]={en:["Iron Ore Futures"],fa:["Iron Ore Futures"]},e.exports["#ITIN2020-symbol-description"]={en:["Iron Ore Futures"],fa:["Iron Ore Futures"]},e.exports["#ITIQ2017-symbol-description"]={en:["Iron Ore Futures"],fa:["Iron Ore Futures"]},e.exports["#ITIQ2018-symbol-description"]={en:["Iron Ore Futures"],fa:["Iron Ore Futures"]},e.exports["#ITIQ2019-symbol-description"]={
en:["Iron Ore Futures"],fa:["Iron Ore Futures"]},e.exports["#ITIQ2020-symbol-description"]={en:["Iron Ore Futures"],fa:["Iron Ore Futures"]},e.exports["#ITIU2017-symbol-description"]={en:["Iron Ore Futures"],fa:["Iron Ore Futures"]},e.exports["#ITIU2018-symbol-description"]={en:["Iron Ore Futures"],fa:["Iron Ore Futures"]},e.exports["#ITIU2019-symbol-description"]={en:["Iron Ore Futures"],fa:["Iron Ore Futures"]},e.exports["#ITIU2020-symbol-description"]={en:["Iron Ore Futures"],fa:["Iron Ore Futures"]},e.exports["#ITIV2017-symbol-description"]={en:["Iron Ore Futures"],fa:["Iron Ore Futures"]},e.exports["#ITIV2018-symbol-description"]={en:["Iron Ore Futures"],fa:["Iron Ore Futures"]},e.exports["#ITIV2019-symbol-description"]={en:["Iron Ore Futures"],fa:["Iron Ore Futures"]},e.exports["#ITIV2020-symbol-description"]={en:["Iron Ore Futures"],fa:["Iron Ore Futures"]},e.exports["#ITIX2017-symbol-description"]={en:["Iron Ore Futures"],fa:["Iron Ore Futures"]},e.exports["#ITIX2018-symbol-description"]={en:["Iron Ore Futures"],fa:["Iron Ore Futures"]},e.exports["#ITIX2019-symbol-description"]={en:["Iron Ore Futures"],fa:["Iron Ore Futures"]},e.exports["#ITIX2020-symbol-description"]={en:["Iron Ore Futures"],fa:["Iron Ore Futures"]},e.exports["#ITIZ2017-symbol-description"]={en:["Iron Ore Futures"],fa:["Iron Ore Futures"]},e.exports["#ITIZ2018-symbol-description"]={en:["Iron Ore Futures"],fa:["Iron Ore Futures"]},e.exports["#ITIZ2019-symbol-description"]={en:["Iron Ore Futures"],fa:["Iron Ore Futures"]},e.exports["#ITIZ2020-symbol-description"]={en:["Iron Ore Futures"],fa:["Iron Ore Futures"]},e.exports["#AMEX:GXF-symbol-description"]={en:["Global x FTSE Nordic Region ETF"],fa:["Global x FTSE Nordic Region ETF"]},e.exports["#ASX:XAF-symbol-description"]={en:["S&P/ASX All Australian 50 Index"],fa:["S&P/ASX All Australian 50 Index"]},e.exports["#ASX:XAT-symbol-description"]={en:["S&P/ASX All Australian 200 Index"],fa:["S&P/ASX All Australian 200 Index"]},e.exports["#BIST:XU100-symbol-description"]={en:["BIST 100 Index"],fa:["BIST 100 Index"]},e.exports["#GPW:WIG20-symbol-description"]={en:["WIG20 Index"],fa:["WIG20 Index"]},e.exports["#INDEX:JKSE-symbol-description"]={en:["Jakarta Composite Index"],fa:["Jakarta Composite Index"]},e.exports["#INDEX:KLSE-symbol-description"]={en:["Bursa Malaysia KLCI Index"],fa:["Bursa Malaysia KLCI Index"]},e.exports["#INDEX:NZD-symbol-description"]={en:["NZX 50 Index"],fa:["NZX 50 Index"]},e.exports["#INDEX:STI-symbol-description"]={en:["STI Index"],fa:["STI Index"]},e.exports["#INDEX:XLY0-symbol-description"]={en:["Shanghai Composite Index"],fa:["Shanghai Composite Index"]},e.exports["#MOEX:MICEXINDEXCF-symbol-description"]={en:["MOEX Russia Index"],fa:["MOEX Russia Index"]},e.exports["#NYMEX:KT1!-symbol-description"]={en:["Coffee Futures"],fa:["Coffee Futures"]},e.exports["#OANDA:NATGASUSD-symbol-description"]={en:["CFDs on Natural Gas"],fa:["CFDs on Natural Gas"]},e.exports["#OANDA:USDPLN-symbol-description"]={en:["U.S. Dollar / Polish Zloty"],fa:["U.S. Dollar / Polish Zloty"]},
e.exports["#TSX:TX60-symbol-description"]={en:["S&P/TSX 60 Index"],fa:["S&P/TSX 60 Index"]},e.exports["#TSX:VBU-symbol-description"]={en:["Vanguard U.S. Aggregate Bond Index ETF (CAD-hedged) UN"],fa:["Vanguard U.S. Aggregate Bond Index ETF (CAD-hedged) UN"]},e.exports["#TSX:VIXC-symbol-description"]={en:["S&P/TSX 60 VIX Index"],fa:["S&P/TSX 60 VIX Index"]},e.exports["#TVC:CAC40-symbol-description"]={en:["CAC 40 Index"],fa:["CAC 40 Index"]},e.exports["#TVC:ES10-symbol-description"]={en:["Spain Government Bonds 10 YR"],fa:["Spain Government Bonds 10 YR"]},e.exports["#TVC:EUBUND-symbol-description"]={en:["Euro Bund"],fa:["Euro Bund"]},e.exports["#TVC:GB02-symbol-description"]={en:["UK Government Bonds 2 YR"],fa:["UK Government Bonds 2 YR"]},e.exports["#TVC:GB10-symbol-description"]={en:["UK Government Bonds 10 YR"],fa:["UK Government Bonds 10 YR"]},e.exports["#TVC:GOLD-symbol-description"]={en:["CFDs on Gold (US$ / OZ)"],fa:["CFDs on Gold (US$ / OZ)"]},e.exports["#TVC:ID03-symbol-description"]={en:["Indonesia Government Bonds 3 YR"],fa:["Indonesia Government Bonds 3 YR"]},e.exports["#TVC:ID10-symbol-description"]={en:["Indonesia Government Bonds 10 YR"],fa:["Indonesia Government Bonds 10 YR"]},e.exports["#TVC:PALLADIUM-symbol-description"]={en:["CFDs on Palladium (US$ / OZ)"],fa:["CFDs on Palladium (US$ / OZ)"]},e.exports["#TVC:PT10-symbol-description"]={en:["Portugal Government Bonds 10 YR"],fa:["Portugal Government Bonds 10 YR"]},e.exports["#TVC:SILVER-symbol-description"]={en:["CFDs on Silver (US$ / OZ)"],fa:["CFDs on Silver (US$ / OZ)"]},e.exports["#TSX:TSX-symbol-description"]={en:["S&P/TSX Composite Index"],fa:["S&P/TSX Composite Index"]},e.exports["#OANDA:CH20CHF-symbol-description"]={en:["Swiss 20 Index"],fa:["Swiss 20 Index"]},e.exports["#TVC:SHCOMP-symbol-description"]={en:["Shanghai Composite Index"],fa:["Shanghai Composite Index"]},e.exports["#NZX:ALLC-symbol-description"]={en:["S&P/NZX All Index (Capital Index)"],fa:["S&P/NZX All Index (Capital Index)"]},e.exports["#AMEX:SHYG-symbol-description"]={en:["Shares 0-5 YEAR High Yield Corporate Bond ETF"],fa:["Shares 0-5 YEAR High Yield Corporate Bond ETF"]},e.exports["#TVC:AU10-symbol-description"]={en:["Australia Government Bonds 10 YR"],fa:["Australia Government Bonds 10 YR"]},e.exports["#TVC:CN10-symbol-description"]={en:["China Government Bonds 10 YR"],fa:["China Government Bonds 10 YR"]},e.exports["#TVC:KR10-symbol-description"]={en:["Korea Government Bonds 10 YR"],fa:["Korea Government Bonds 10 YR"]},e.exports["#NYMEX:RB1!-symbol-description"]={en:["RBOB Gasoline Futures"],fa:["RBOB Gasoline Futures"]},e.exports["#NYMEX:HO1!-symbol-description"]={en:["NY Harbor ULSD Futures"],fa:["NY Harbor ULSD Futures"]},e.exports["#NYMEX:AEZ1!-symbol-description"]={en:["NY Ethanol Futures"],fa:["NY Ethanol Futures"]},e.exports["#OANDA:XCUUSD-symbol-description"]={en:["CFDs on Copper (US$ / lb)"],fa:["CFDs on Copper (US$ / lb)"]},e.exports["#COMEX:ZA1!-symbol-description"]={en:["Zinc Futures"],fa:["Zinc Futures"]},e.exports["#CBOT:ZW1!-symbol-description"]={
en:["Wheat Futures"],fa:["Wheat Futures"]},e.exports["#NYMEX:KA1!-symbol-description"]={en:["Sugar #11 Futures"],fa:["Sugar #11 Futures"]},e.exports["#CBOT:QBC1!-symbol-description"]={en:["Corn Futures"],fa:["Corn Futures"]},e.exports["#CME:E61!-symbol-description"]={en:["Euro Futures"],fa:["Euro Futures"]},e.exports["#CME:B61!-symbol-description"]={en:["British Pound Futures"],fa:["British Pound Futures"]},e.exports["#CME:QJY1!-symbol-description"]={en:["Japanese Yen Futures"],fa:["Japanese Yen Futures"]},e.exports["#CME:A61!-symbol-description"]={en:["Australian Dollar Futures"],fa:["Australian Dollar Futures"]},e.exports["#CME:D61!-symbol-description"]={en:["Canadian Dollar Futures"],fa:["Canadian Dollar Futures"]},e.exports["#CME:SP1!-symbol-description"]={en:["S&P 500 Futures"],fa:["S&P 500 Futures"]},e.exports["#CME_MINI:NQ1!-symbol-description"]={en:["NASDAQ 100 E-mini Futures"],fa:["NASDAQ 100 E-mini Futures"]},e.exports["#CBOT_MINI:YM1!-symbol-description"]={en:["E-mini Dow Jones ($5) Futures"],fa:["E-mini Dow Jones ($5) Futures"]},e.exports["#CME:NY1!-symbol-description"]={en:["NIKKEI 225 Futures"],fa:["NIKKEI 225 Futures"]},e.exports["#EUREX:DY1!-symbol-description"]={en:["DAX Index"],fa:["DAX Index"]},e.exports["#CME:IF1!-symbol-description"]={en:["IBOVESPA Index Futures-US$"],fa:["IBOVESPA Index Futures-US$"]},e.exports["#CBOT:TY1!-symbol-description"]={en:["10 Year T-Note Futures"],fa:["10 Year T-Note Futures"]},e.exports["#CBOT:FV1!-symbol-description"]={en:["5 Year T-Note Futures"],fa:["5 Year T-Note Futures"]},e.exports["#CBOT:ZE1!-symbol-description"]={en:["Treasury Notes - 3 Year Futures"],fa:["Treasury Notes - 3 Year Futures"]},e.exports["#CBOT:TU1!-symbol-description"]={en:["2 Year T-Note Futures"],fa:["2 Year T-Note Futures"]},e.exports["#CBOT:FF1!-symbol-description"]={en:["30-Day FED Funds Interest Rate Futures"],fa:["30-Day FED Funds Interest Rate Futures"]},e.exports["#CBOT:US1!-symbol-description"]={en:["T-Bond Futures"],fa:["T-Bond Futures"]},e.exports["#TVC:EXY-symbol-description"]={en:["Euro Currency Index"],fa:["Euro Currency Index"]},e.exports["#TVC:JXY-symbol-description"]={en:["Japanese Yen Currency Index"],fa:["Japanese Yen Currency Index"]},e.exports["#TVC:BXY-symbol-description"]={en:["British Pound Currency Index"],fa:["British Pound Currency Index"]},e.exports["#TVC:AXY-symbol-description"]={en:["Australian Dollar Currency Index"],fa:["Australian Dollar Currency Index"]},e.exports["#TVC:CXY-symbol-description"]={en:["Canadian Dollar Currency Index"],fa:["Canadian Dollar Currency Index"]},e.exports["#FRED:GDP-symbol-description"]={en:["Gross Domestic Product, 1 Decimal"],fa:["Gross Domestic Product, 1 Decimal"]},e.exports["#FRED:UNRATE-symbol-description"]={en:["Civilian Unemployment Rate"],fa:["Civilian Unemployment Rate"]},e.exports["#FRED:POP-symbol-description"]={en:["Total Population: All Ages Including Armed Forces Overseas"],fa:["Total Population: All Ages Including Armed Forces Overseas"]},e.exports["#ETHUSD-symbol-description"]={en:["Ethereum / U.S. Dollar"],
fa:["Ethereum / U.S. Dollar"]},e.exports["#BMFBOVESPA:IBOV-symbol-description"]={en:["Bovespa Index"],fa:["IBovespa Index"]},e.exports["#BMFBOVESPA:IBRA-symbol-description"]={en:["IBrasil Index"],fa:["IBrasil Index"]},e.exports["#BMFBOVESPA:IBXL-symbol-description"]={en:["Brazil 50 Index"],fa:["IBRX 50 Index"]},e.exports["#COMEX:HG1!-symbol-description"]={en:["Copper Futures"],fa:["Copper Futures"]},e.exports["#INDEX:HSCE-symbol-description"]={en:["Hang Seng China Enterprises Index"],fa:["Hang Seng China Enterprises Index"]},e.exports["#NYMEX:CL1!-symbol-description"]={en:["Light Crude Oil Futures"],fa:["Light Crude Oil Futures"]},e.exports["#OTC:IHRMF-symbol-description"]={en:["Ishares MSCI Japan SHS"],fa:["Ishares MSCI Japan SHS"]},e.exports["#TVC:DAX-symbol-description"]={en:["DAX Index"],fa:["DAX Index"]},e.exports["#TVC:DE10-symbol-description"]={en:["German Government Bonds 10 YR"],fa:["German Government Bonds 10 YR"]},e.exports["#TVC:DJI-symbol-description"]={en:["Dow Jones Industrial Average Index"],fa:["Dow Jones Industrial Average Index"]},e.exports["#TVC:DXY-symbol-description"]={en:["U.S. Dollar Index"],fa:["U.S. Dollar Currency Index"]},e.exports["#TVC:FR10-symbol-description"]={en:["France Government Bonds 10 YR"],fa:["France Government Bonds 10 YR"]},e.exports["#TVC:HSI-symbol-description"]={en:["Hang Seng Index"],fa:["Hang Seng Index"]},e.exports["#TVC:IBEX35-symbol-description"]={en:["IBEX 35 Index"],fa:["IBEX 35 Index"]},e.exports["#FX:AUS200-symbol-description"]={en:["S&P/ASX Index"],fa:["S&P/ASX Index"]},e.exports["#AMEX:SHY-symbol-description"]={en:["Ishares 1-3 Year Treasury Bond ETF"],fa:["Ishares 1-3 Year Treasury Bond ETF"]},e.exports["#ASX:XJO-symbol-description"]={en:["S&P/ASX 200 Index"],fa:["S&P/ASX 200 Index"]},e.exports["#BSE:SENSEX-symbol-description"]={en:["S&P BSE Sensex Index"],fa:["S&P BSE Sensex Index"]},e.exports["#INDEX:MIB-symbol-description"]={en:["MIB Index"],fa:["MIB Index"]},e.exports["#INDEX:MOY0-symbol-description"]={en:["Euro Stoxx 50 Index"],fa:["Euro Stoxx 50 Index"]},e.exports["#MOEX:RTSI-symbol-description"]={en:["RTS Index"],fa:["RTS Index"]},e.exports["#NSE:NIFTY-symbol-description"]={en:["Nifty 50 Index"],fa:["Nifty 50 Index"]},e.exports["#NYMEX:NG1!-symbol-description"]={en:["Natural Gas Futures"],fa:["Natural Gas Futures"]},e.exports["#NYMEX:ZC1!-symbol-description"]={en:["Corn Futures"],fa:["Corn Futures"]},e.exports["#TVC:IN10-symbol-description"]={en:["India Government Bonds 10 YR"],fa:["India Government Bonds 10 YR"]},e.exports["#TVC:IT10-symbol-description"]={en:["Italy Government Bonds 10 YR"],fa:["Italy Government Bonds 10 YR"]},e.exports["#TVC:JP10-symbol-description"]={en:["Japan Government Bonds 10 YR"],fa:["Japan Government Bonds 10 YR"]},e.exports["#TVC:NDX-symbol-description"]={en:["US 100 Index"],fa:["NASDAQ 100 Index"]},e.exports["#TVC:NI225-symbol-description"]={en:["Nikkei 225 Index"],fa:["Nikkei 225 Index"]},e.exports["#TVC:SPX-symbol-description"]={en:["S&P 500 Index"],fa:["S&P 500 Index"]},e.exports["#TVC:SX5E-symbol-description"]={
en:["Euro Stoxx 50 Index"],fa:["Euro Stoxx 50 Index"]},e.exports["#TVC:TR10-symbol-description"]={en:["Turkey Government Bonds 10 YR"],fa:["Turkey Government Bonds 10 YR"]},e.exports["#TVC:UKOIL-symbol-description"]={en:["CFDs on Brent Crude Oil"],fa:["CFDs on Brent Crude Oil"]},e.exports["#TVC:UKX-symbol-description"]={en:["UK 100 Index"],fa:["UK 100 Index"]},e.exports["#TVC:US02-symbol-description"]={en:["US Government Bonds 2 YR"],fa:["US Government Bonds 2 YR"]},e.exports["#TVC:US05-symbol-description"]={en:["US Government Bonds 5 YR"],fa:["US Government Bonds 5 YR"]},e.exports["#TVC:US10-symbol-description"]={en:["US Government Bonds 10 YR"],fa:["US Government Bonds 10 YR"]},e.exports["#TVC:USOIL-symbol-description"]={en:["CFDs on WTI Crude Oil"],fa:["CFDs on WTI Crude Oil"]},e.exports["#NYMEX:ITI1!-symbol-description"]={en:["Iron Ore Futures"],fa:["Iron Ore Futures"]},e.exports["#NASDAQ:SHY-symbol-description"]={en:["Ishares 1-3 Year Treasury Bond ETF"],fa:["Ishares 1-3 Year Treasury Bond ETF"]},e.exports["#AMEX:ALD-symbol-description"]={en:["WisdomTree Asia Local Debt ETF"],fa:["WisdomTree Asia Local Debt ETF"]},e.exports["#NASDAQ:AMD-symbol-description"]={en:["Advanced Micro Devices Inc"],fa:["Advanced Micro Devices Inc"]},e.exports["#NYSE:BABA-symbol-description"]={en:["Alibaba Group Holdings Ltd."],fa:["ALIBABA GROUP HLDG LTD"]},e.exports["#ICEEUR:CB-symbol-description"]={en:["Crude Oil Brent"],fa:["نفت خام برنت"]},e.exports["#ICEEUR:CB1!-symbol-description"]={en:["Brent Crude Oil"],fa:["نفت خام برنت"]},e.exports["#ICEUSA:CC-symbol-description"]={en:["Cocoa"],fa:["کاکائو"]},e.exports["#NYMEX:CL-symbol-description"]={en:["Crude Oil WTI"],fa:["Crude Oil WTI"]},e.exports["#ICEUSA:CT-symbol-description"]={en:["Cotton #2"],fa:["Cotton #2"]},e.exports["#NASDAQ:CTRV-symbol-description"]={en:["ContraVir Pharmaceuticals Inc"],fa:["CONTRAVIR PHARMACEUTICALS INC"]},e.exports["#CME:DL-symbol-description"]={en:["Class III Milk"],fa:["Class III Milk"]},e.exports["#NYSE:F-symbol-description"]={en:["FORD MTR CO DEL"],fa:["FORD MTR CO DEL"]},e.exports["#MOEX:GAZP-symbol-description"]={en:["GAZPROM"],fa:["GAZPROM"]},e.exports["#COMEX:GC-symbol-description"]={en:["Gold"],fa:["طلا"]},e.exports["#CME:GF-symbol-description"]={en:["Feeder Cattle"],fa:["Feeder Cattle"]},e.exports["#CME:HE-symbol-description"]={en:["Lean Hogs"],fa:["Lean Hogs"]},e.exports["#NASDAQ:IEF-symbol-description"]={en:["Ishares 7-10 Year Treasury Bond ETF"],fa:["Ishares 7-10 Year Treasury Bond ETF"]},e.exports["#NASDAQ:IEI-symbol-description"]={en:["Ishares 3-7 Year Treasury Bond ETF"],fa:["Ishares 3-7 Year Treasury Bond ETF"]},e.exports["#NYMEX:KA1-symbol-description"]={en:["Sugar #11 Futures"],fa:["Sugar #11 Futures"]},e.exports["#ICEUSA:KC-symbol-description"]={en:["Coffee"],fa:["Coffee"]},e.exports["#NYMEX:KG1-symbol-description"]={en:["Cotton Futures"],fa:["Cotton Futures"]},e.exports["#FWB:KT1-symbol-description"]={en:["Key Tronic Corр."],fa:["Key Tronic Corр."]},e.exports["#CME:LE-symbol-description"]={en:["Live Cattle"],fa:["Live Cattle"]},
e.exports["#ICEEUR:LO-symbol-description"]={en:["ICE Heating Oil"],fa:["ICE Heating Oil"]},e.exports["#CME:LS-symbol-description"]={en:["Lumber"],fa:["Lumber"]},e.exports["#MOEX:MGNT-symbol-description"]={en:["MAGNIT"],fa:["MAGNIT"]},e.exports["#LSIN:MNOD-symbol-description"]={en:["ADR GMK NORILSKIYNIKEL ORD SHS [REPO]"],fa:["ADR GMK NORILSKIYNIKEL ORD SHS [REPO]"]},e.exports["#NYMEX:NG-symbol-description"]={en:["Natural Gas"],fa:["گاز طبیعی"]},e.exports["#ICEUSA:OJ-symbol-description"]={en:["Orange Juice"],fa:["آب پرتقال"]},e.exports["#NYMEX:PA-symbol-description"]={en:["Palladium"],fa:["Palladium"]},e.exports["#NYSE:PBR-symbol-description"]={en:["PETROLEO BRASILEIRO SA PETROBR"],fa:["PETROLEO BRASILEIRO SA PETROBR"]},e.exports["#NYMEX:PL-symbol-description"]={en:["Platinum"],fa:["Platinum"]},e.exports["#COMEX_MINI:QC-symbol-description"]={en:["E-Mini Copper"],fa:["E-Mini Copper"]},e.exports["#NYMEX:RB-symbol-description"]={en:["Gasoline RBOB"],fa:["Gasoline RBOB"]},e.exports["#NYMEX:RB1-symbol-description"]={en:["RBOB Gasoline Futures"],fa:["RBOB Gasoline Futures"]},e.exports["#MOEX:SBER-symbol-description"]={en:["SBERBANK"],fa:["SBERBANK"]},e.exports["#AMEX:SCHO-symbol-description"]={en:["Schwab Short-Term U.S. Treasury ETF"],fa:["Schwab Short-Term U.S. Treasury ETF"]},e.exports["#COMEX:SI-symbol-description"]={en:["Silver"],fa:["نقره"]},e.exports["#NASDAQ:TLT-symbol-description"]={en:["Ishares 20+ Year Treasury Bond ETF"],fa:["Ishares 20+ Year Treasury Bond ETF"]},e.exports["#TVC:VIX-symbol-description"]={en:["Volatility S&P 500 Index"],fa:["Volatility S&P 500 Index"]},e.exports["#MOEX:VTBR-symbol-description"]={en:["VTB"],fa:["VTB"]},e.exports["#COMEX:ZA-symbol-description"]={en:["Zinc"],fa:["فلز روی"]},e.exports["#CBOT:ZC-symbol-description"]={en:["Corn"],fa:["دانه ذرت"]},e.exports["#CBOT:ZK-symbol-description"]={en:["Ethanol Futures"],fa:["Ethanol Futures"]},e.exports["#CBOT:ZL-symbol-description"]={en:["Soybean Oil"],fa:["روغن سویا"]},e.exports["#CBOT:ZO-symbol-description"]={en:["Oats"],fa:["جو"]},e.exports["#CBOT:ZR-symbol-description"]={en:["Rough Rice"],fa:["Rough Rice"]},e.exports["#CBOT:ZS-symbol-description"]={en:["Soybeans"],fa:["دانه سویا"]},e.exports["#CBOT:ZS1-symbol-description"]={en:["Soybean Futures"],fa:["Soybean Futures"]},e.exports["#CBOT:ZW-symbol-description"]={en:["Wheat"],fa:["گندم"]},e.exports["#CBOT:ZW1-symbol-description"]={en:["Wheat Futures - ECBT"],fa:["Wheat Futures - ECBT"]},e.exports["#NASDAQ:ITI-symbol-description"]={en:["Iteris Inc"],fa:["Iteris Inc."]},e.exports["#NYMEX:ITI2!-symbol-description"]={en:["Iron Ore Futures"],fa:["Iron Ore Futures"]},e.exports["#CADUSD-symbol-description"]={en:["Canadian Dollar / U.S. Dollar"],fa:["Canadian Dollar / U.S. Dollar"]},e.exports["#CHFUSD-symbol-description"]={en:["Swiss Franc / U.S. Dollar"],fa:["Swiss Franc / U.S. Dollar"]},e.exports["#GPW:ACG-symbol-description"]={en:["Acautogaz"],fa:["Acautogaz"]},e.exports["#JPYUSD-symbol-description"]={en:["Japanese Yen / U.S. Dollar"],fa:["Japanese Yen / U.S. Dollar"]},
e.exports["#USDAUD-symbol-description"]={en:["U.S. Dollar / Australian Dollar"],fa:["U.S. Dollar / Australian Dollar"]},e.exports["#USDEUR-symbol-description"]={en:["U.S. Dollar / Euro"],fa:["U.S. Dollar / Euro"]},e.exports["#USDGBP-symbol-description"]={en:["U.S. Dollar / Pound Sterling"],fa:["U.S. Dollar / Pound Sterling"]},e.exports["#USDNZD-symbol-description"]={en:["U.S. Dollar / New Zealand Dollar"],fa:["U.S. Dollar / New Zealand Dollar"]},e.exports["#UKOIL-symbol-description"]={en:["CFDs on Crude Oil (Brent)"],fa:["CFDs on Crude Oil (Brent)"]},e.exports["#USOIL-symbol-description"]={en:["CFDs on Crude Oil (WTI)"],fa:["CFDs on Crude Oil (WTI)"]},e.exports["#US30-symbol-description"]={en:["Dow Jones Industrial Average Index"],fa:["Dow Jones Industrial Average Index"]},e.exports["#BCHUSD-symbol-description"]={en:["Bitcoin Cash / U.S. Dollar"],fa:["Bitcoin Cash / U.S. Dollar"]},e.exports["#ETCUSD-symbol-description"]={en:["Ethereum Classic / U.S. Dollar"],fa:["Ethereum Classic / U.S. Dollar"]},e.exports["#GOOG-symbol-description"]={en:["Alphabet Inc (Google) Class C"],fa:["Alphabet Inc (Google) Class C"]},e.exports["#LTCUSD-symbol-description"]={en:["Litecoin / U.S. Dollar"],fa:["Litecoin / U.S. Dollar"]},e.exports["#XRPUSD-symbol-description"]={en:["XRP / U.S. Dollar"],fa:["Ripple / U.S. Dollar"]},e.exports["#SP:SPX-symbol-description"]={en:["S&P 500 Index"],fa:["S&P 500 Index"]},e.exports["#ETCBTC-symbol-description"]={en:["Ethereum Classic / Bitcoin"],fa:["Ethereum Classic / Bitcoin"]},e.exports["#ETHBTC-symbol-description"]={en:["Ethereum / Bitcoin"],fa:["Ethereum / Bitcoin"]},e.exports["#XRPBTC-symbol-description"]={en:["XRP / Bitcoin"],fa:["Ripple / Bitcoin"]},e.exports["#TVC:US30-symbol-description"]={en:["US Government Bonds 30 YR"],fa:["US Government Bonds 30 YR"]},e.exports["#COMEX:SI1!-symbol-description"]={en:["Silver Futures"],fa:["Silver Futures"]},e.exports["#BTGUSD-symbol-description"]={en:["Bitcoin Gold / U.S. Dollar"],fa:["Bitcoin Gold / U.S. Dollar"]},e.exports["#IOTUSD-symbol-description"]={en:["IOTA / U.S. Dollar"],fa:["IOTA / U.S. Dollar"]},e.exports["#CME:BTC1!-symbol-description"]={en:["Bitcoin CME Futures"],fa:["Bitcoin CME Futures"]},e.exports["#COMEX:GC1!-symbol-description"]={en:["Gold Futures"],fa:["Gold Futures"]},e.exports["#CORNUSD-symbol-description"]={en:["CFDs on Corn"],fa:["CFDs on Corn"]},e.exports["#COTUSD-symbol-description"]={en:["CFDs on Cotton"],fa:["CFDs on Cotton"]},e.exports["#DJ:DJA-symbol-description"]={en:["Dow Jones Composite Average Index"],fa:["Dow Jones Composite Average Index"]},e.exports["#DJ:DJI-symbol-description"]={en:["Dow Jones Industrial Average Index"],fa:["Dow Jones Industrial Average Index"]},e.exports["#ETHEUR-symbol-description"]={en:["Ethereum / Euro"],fa:["Ethereum / Euro"]},e.exports["#ETHGBP-symbol-description"]={en:["Ethereum / British Pound"],fa:["Ethereum / British Pound"]},e.exports["#ETHJPY-symbol-description"]={en:["Ethereum / Japanese Yen"],fa:["Ethereum / Japanese Yen"]},e.exports["#EURNOK-symbol-description"]={
en:["Euro / Norwegian Krone"],fa:["Euro / Norwegian Krone"]},e.exports["#GBPPLN-symbol-description"]={en:["British Pound / Polish Zloty"],fa:["British Pound / Polish Zloty"]},e.exports["#MOEX:BR1!-symbol-description"]={en:["Brent Oil Futures"],fa:["Brent Oil Futures"]},e.exports["#NYMEX:KG1!-symbol-description"]={en:["Cotton Futures"],fa:["Cotton Futures"]},e.exports["#NYMEX:PL1!-symbol-description"]={en:["Platinum Futures"],fa:["Platinum Futures"]},e.exports["#SOYBNUSD-symbol-description"]={en:["CFDs on Soybeans"],fa:["CFDs on Soybeans"]},e.exports["#SUGARUSD-symbol-description"]={en:["CFDs on Sugar"],fa:["CFDs on Sugar"]},e.exports["#TVC:IXIC-symbol-description"]={en:["US Composite Index"],fa:["NASDAQ Composite Index"]},e.exports["#TVC:RU-symbol-description"]={en:["Russell 1000 Index"],fa:["Russell 1000 Index"]},e.exports["#USDZAR-symbol-description"]={en:["U.S. Dollar / South African Rand"],fa:["U.S. Dollar / South African Rand"]},e.exports["#WHEATUSD-symbol-description"]={en:["CFDs on Wheat"],fa:["CFDs on Wheat"]},e.exports["#XRPEUR-symbol-description"]={en:["XRP / Euro"],fa:["Ripple / Euro"]},e.exports["#CBOT:S1!-symbol-description"]={en:["Soybean Futures"],fa:["Soybean Futures"]},e.exports["#SP:MID-symbol-description"]={en:["S&P 400 Index"],fa:["S&P 400 Index"]},e.exports["#TSX:XCUUSD-symbol-description"]={en:["CFDs on Copper"],fa:["CFDs on Copper"]},e.exports["#TVC:NYA-symbol-description"]={en:["NYSE Composite Index"],fa:["NYSE Composite Index"]},e.exports["#TVC:PLATINUM-symbol-description"]={en:["CFDs on Platinum (US$ / OZ)"],fa:["CFDs on Platinum (US$ / OZ)"]},e.exports["#TVC:SSMI-symbol-description"]={en:["Swiss Market Index"],fa:["Swiss Market Index"]},e.exports["#TVC:SXY-symbol-description"]={en:["Swiss Franc Currency Index"],fa:["Swiss Franc Currency Index"]},e.exports["#MOEX:RI1!-symbol-description"]={en:["RTS Index Futures"],fa:["RTS Index Futures"]},e.exports["#MOEX:MX1!-symbol-description"]={en:["MICEX Index Futures"],fa:["MICEX Index Futures"]},e.exports["#CBOE:BG1!-symbol-description"]={en:["Bitcoin CBOE Futures"],fa:["Bitcoin CBOE Futures"]},e.exports["#TVC:MY10-symbol-description"]={en:["Malaysia Government Bonds 10 YR"],fa:["Malaysia Government Bonds 10 YR"]},e.exports["#CME:S61!-symbol-description"]={en:["Swiss Franc Futures"],fa:["Swiss Franc Futures"]},e.exports["#TVC:DEU30-symbol-description"]={en:["DAX Index"],fa:["DAX Index"]},e.exports["#BCHEUR-symbol-description"]={en:["Bitcoin Cash / Euro"],fa:["Bitcoin Cash / Euro"]},e.exports["#TVC:ZXY-symbol-description"]={en:["New Zealand Dollar Currency Index"],fa:["New Zealand Dollar Currency Index"]},e.exports["#MIL:FTSEMIB-symbol-description"]={en:["FTSE MIB Index"],fa:["FTSE MIB Index"]},e.exports["#XETR:DAX-symbol-description"]={en:["DAX Index"],fa:["DAX Index"]},e.exports["#MOEX:IMOEX-symbol-description"]={en:["MOEX Russia Index"],fa:["MOEX Russia Index"]},e.exports["#FX:US30-symbol-description"]={en:["Dow Jones Industrial Average Index"],fa:["Dow Jones Industrial Average Index"]},e.exports["#MOEX:RUAL-symbol-description"]={
en:["United Company RUSAL PLC"],fa:["United Company RUSAL PLC"]},e.exports["#MOEX:MX2!-symbol-description"]={en:["MICEX Index Futures"],fa:["MICEX Index Futures"]},e.exports["#NEOUSD-symbol-description"]={en:["NEO / U.S. Dollar"],fa:["NEO / U.S. Dollar"]},e.exports["#XMRUSD-symbol-description"]={en:["Monero / U.S. Dollar"],fa:["Monero / U.S. Dollar"]},e.exports["#ZECUSD-symbol-description"]={en:["Zcash / U.S. Dollar"],fa:["Zcash / U.S. Dollar"]},e.exports["#TVC:CAC-symbol-description"]={en:["CAC 40 Index"],fa:["CAC 40 Index"]},e.exports["#NASDAQ:ZS-symbol-description"]={en:["Zscaler Inc"],fa:["Zscaler Inc"]},e.exports["#TVC:GB10Y-symbol-description"]={en:["UK Government Bonds 10 YR Yield"],fa:["UK Government Bonds 10 YR Yield"]},e.exports["#TVC:AU10Y-symbol-description"]={en:["Australia Government Bonds 10 YR Yield"],fa:["Australia Government Bonds 10 YR Yield"]},e.exports["#TVC:CN10Y-symbol-description"]={en:["China Government Bonds 10 YR Yield"],fa:["China Government Bonds 10 YR Yield"]},e.exports["#TVC:DE10Y-symbol-description"]={en:["German Government Bonds 10 YR Yield"],fa:["German Government Bonds 10 YR Yield"]},e.exports["#TVC:ES10Y-symbol-description"]={en:["Spain Government Bonds 10 YR Yield"],fa:["Spain Government Bonds 10 YR Yield"]},e.exports["#TVC:FR10Y-symbol-description"]={en:["France Government Bonds 10 YR Yield"],fa:["France Government Bonds 10 YR Yield"]},e.exports["#TVC:IN10Y-symbol-description"]={en:["India Government Bonds 10 YR Yield"],fa:["India Government Bonds 10 yr"]},e.exports["#TVC:IT10Y-symbol-description"]={en:["Italy Government Bonds 10 YR Yield"],fa:["Italy Government Bonds 10 yr"]},e.exports["#TVC:JP10Y-symbol-description"]={en:["Japan Government Bonds 10 YR Yield"],fa:["Japan Government Bonds 10 yr"]},e.exports["#TVC:KR10Y-symbol-description"]={en:["Korea Government Bonds 10 YR Yield"],fa:["Korea Government Bonds 10 YR Yield"]},e.exports["#TVC:MY10Y-symbol-description"]={en:["Malaysia Government Bonds 10 YR Yield"],fa:["Malaysia Government Bonds 10 YR Yield"]},e.exports["#TVC:PT10Y-symbol-description"]={en:["Portugal Government Bonds 10 YR Yield"],fa:["Portugal Government Bonds 10 YR Yield"]},e.exports["#TVC:TR10Y-symbol-description"]={en:["Turkey Government Bonds 10 YR Yield"],fa:["Turkey Government Bonds 10 YR"]},e.exports["#TVC:US02Y-symbol-description"]={en:["US Government Bonds 2 YR Yield"],fa:["US Government Bonds 2 yr"]},e.exports["#TVC:US05Y-symbol-description"]={en:["US Government Bonds 5 YR Yield"],fa:["US Government Bonds 5 yr"]},e.exports["#TVC:US10Y-symbol-description"]={en:["US Government Bonds 10 YR Yield"],fa:["US Government Bonds 10 yr"]},e.exports["#INDEX:TWII-symbol-description"]={en:["Taiwan Weighted Index"],fa:["Taiwan Weighted Index"]},e.exports["#CME:J61!-symbol-description"]={en:["Japanese Yen Futures"],fa:["Japanese Yen Futures"]},e.exports["#CME_MINI:J71!-symbol-description"]={en:["Japanese Yen E-mini Futures"],fa:["Japanese Yen E-mini Futures"]},e.exports["#CME_MINI:WM1!-symbol-description"]={en:["E-micro Japanese Yen / U.S. Dollar Futures"],
fa:["E-micro Japanese Yen / U.S. Dollar Futures"]},e.exports["#CME:M61!-symbol-description"]={en:["Mexican Peso Futures"],fa:["Mexican Peso Futures"]},e.exports["#CME:T61!-symbol-description"]={en:["South African Rand Futures"],fa:["South African Rand Futures"]},e.exports["#CME:SK1!-symbol-description"]={en:["Swedish Krona Futures"],fa:["Swedish Krona Futures"]},e.exports["#CME:QT1!-symbol-description"]={en:["Chinese Renminbi / U.S. Dollar Futures"],fa:["Chinese Renminbi / U.S. Dollar Futures"]},e.exports["#COMEX:AUP1!-symbol-description"]={en:["Aluminum MW U.S. Transaction Premium Platts (25MT) Futures"],fa:["Aluminum MW U.S. Transaction Premium Platts (25MT) Futures"]},e.exports["#CME:L61!-symbol-description"]={en:["Brazilian Real Futures"],fa:["Brazilian Real Futures"]},e.exports["#CME:WP1!-symbol-description"]={en:["Polish Zloty Futures"],fa:["Polish Zloty Futures"]},e.exports["#CME:N61!-symbol-description"]={en:["New Zealand Dollar Futures"],fa:["New Zealand Dollar Futures"]},e.exports["#CME_MINI:MG1!-symbol-description"]={en:["E-micro Australian Dollar / U.S. Dollar Futures"],fa:["E-micro Australian Dollar / U.S. Dollar Futures"]},e.exports["#CME_MINI:WN1!-symbol-description"]={en:["E-micro Swiss Franc / U.S. Dollar Futures"],fa:["E-micro Swiss Franc / U.S. Dollar Futures"]},e.exports["#CME_MINI:MF1!-symbol-description"]={en:["E-micro Euro / U.S. Dollar Futures"],fa:["E-micro Euro / U.S. Dollar Futures"]},e.exports["#CME_MINI:E71!-symbol-description"]={en:["Euro E-mini Futures"],fa:["Euro E-mini Futures"]},e.exports["#CBOT:ZK1!-symbol-description"]={en:["Denatured Fuel Ethanol Futures"],fa:["Denatured Fuel Ethanol Futures"]},e.exports["#CME_MINI:MB1!-symbol-description"]={en:["E-micro British Pound / U.S. Dollar Futures"],fa:["E-micro British Pound / U.S. Dollar Futures"]},e.exports["#NYMEX_MINI:QU1!-symbol-description"]={en:["E-mini Gasoline Futures"],fa:["E-mini Gasoline Futures"]},e.exports["#NYMEX_MINI:QX1!-symbol-description"]={en:["E-mini Heating Oil Futures"],fa:["E-mini Heating Oil Futures"]},e.exports["#COMEX_MINI:QC1!-symbol-description"]={en:["E-mini Copper Futures"],fa:["E-mini Copper Futures"]},e.exports["#NYMEX_MINI:QG1!-symbol-description"]={en:["E-mini Natural Gas Futures"],fa:["E-mini Natural Gas Futures"]},e.exports["#CME:E41!-symbol-description"]={en:["U.S. Dollar / Turkish Lira Futures"],fa:["U.S. Dollar / Turkish Lira Futures"]},e.exports["#COMEX_MINI:QI1!-symbol-description"]={en:["Silver (Mini) Futures"],fa:["Silver (Mini) Futures"]},e.exports["#CME:DL1!-symbol-description"]={en:["Milk, Class III Futures"],fa:["Milk, Class III Futures"]},e.exports["#NYMEX:UX1!-symbol-description"]={en:["Uranium Futures"],fa:["Uranium Futures"]},e.exports["#CBOT:BO1!-symbol-description"]={en:["Soybean Oil Futures"],fa:["Soybean Oil Futures"]},e.exports["#CME:HE1!-symbol-description"]={en:["Lean Hogs Futures"],fa:["Lean Hogs Futures"]},e.exports["#NYMEX:IAC1!-symbol-description"]={en:["Newcastle Coal Futures"],fa:["Newcastle Coal Futures"]},e.exports["#NYMEX_MINI:QM1!-symbol-description"]={
en:["E-mini Light Crude Oil Futures"],fa:["E-mini Light Crude Oil Futures"]},e.exports["#NYMEX:JMJ1!-symbol-description"]={en:["Mini Brent Financial Futures"],fa:["Mini Brent Financial Futures"]},e.exports["#COMEX:AEP1!-symbol-description"]={en:["Aluminium European Premium Futures"],fa:["Aluminium European Premium Futures"]},e.exports["#CBOT:ZQ1!-symbol-description"]={en:["30 Day Federal Funds Interest Rate Futures"],fa:["30 Day Federal Funds Interest Rate Futures"]},e.exports["#CME:LE1!-symbol-description"]={en:["Live Cattle Futures"],fa:["Live Cattle Futures"]},e.exports["#CME:UP1!-symbol-description"]={en:["Swiss Franc / Japanese Yen Futures"],fa:["Swiss Franc / Japanese Yen Futures"]},e.exports["#CBOT:ZN1!-symbol-description"]={en:["10 Year T-Note Futures"],fa:["10 Year T-Note Futures"]},e.exports["#CBOT:ZB1!-symbol-description"]={en:["T-Bond Futures"],fa:["T-Bond Futures"]},e.exports["#CME:GF1!-symbol-description"]={en:["Feeder Cattle Futures"],fa:["Feeder Cattle Futures"]},e.exports["#CBOT:UD1!-symbol-description"]={en:["Ultra T-Bond Futures"],fa:["Ultra T-Bond Futures"]},e.exports["#CME:I91!-symbol-description"]={en:["CME Housing Futures — Washington DC"],fa:["CME Housing Futures — Washington DC"]},e.exports["#CBOT:ZO1!-symbol-description"]={en:["Oat Futures"],fa:["Oat Futures"]},e.exports["#CBOT:ZM1!-symbol-description"]={en:["Soybean Meal Futures"],fa:["Soybean Meal Futures"]},e.exports["#CBOT_MINI:XN1!-symbol-description"]={en:["Corn Mini Futures"],fa:["Corn Mini Futures"]},e.exports["#CBOT:ZC1!-symbol-description"]={en:["Corn Futures"],fa:["Corn Futures"]},e.exports["#CME:LS1!-symbol-description"]={en:["Lumber Futures"],fa:["Lumber Futures"]},e.exports["#CBOT_MINI:XW1!-symbol-description"]={en:["Wheat Mini Futures"],fa:["Wheat Mini Futures"]},e.exports["#CBOT_MINI:XK1!-symbol-description"]={en:["Soybean Mini Futures"],fa:["Soybean Mini Futures"]},e.exports["#CBOT:ZS1!-symbol-description"]={en:["Soybean Futures"],fa:["Soybean Futures"]},e.exports["#NYMEX:PA1!-symbol-description"]={en:["Palladium Futures"],fa:["Palladium Futures"]},e.exports["#CME:FTU1!-symbol-description"]={en:["E-mini FTSE 100 Index USD Futures"],fa:["E-mini FTSE 100 Index USD Futures"]},e.exports["#CBOT:ZR1!-symbol-description"]={en:["Rice Futures"],fa:["Rice Futures"]},e.exports["#COMEX_MINI:GR1!-symbol-description"]={en:["Gold (E-micro) Futures"],fa:["Gold (E-micro) Futures"]},e.exports["#COMEX_MINI:QO1!-symbol-description"]={en:["Gold (Mini) Futures"],fa:["Gold (Mini) Futures"]},e.exports["#CME_MINI:RL1!-symbol-description"]={en:["E-mini Russell 1000 Futures"],fa:["E-mini Russell 1000 Futures"]},e.exports["#CME_MINI:EW1!-symbol-description"]={en:["S&P 400 Midcap E-mini Futures"],fa:["S&P 400 Midcap E-mini Futures"]},e.exports["#COMEX:LD1!-symbol-description"]={en:["Lead Futures"],fa:["Lead Futures"]},e.exports["#CME_MINI:ES1!-symbol-description"]={en:["S&P 500 E-mini Futures"],fa:["S&P 500 E-mini Futures"]},e.exports["#TVC:SA40-symbol-description"]={en:["South Africa Top 40 Index"],fa:["South Africa Top 40 Index"]},
e.exports["#BMV:ME-symbol-description"]={en:["S&P/BMV IPC Index"],fa:["IPC Mexico Index"]},e.exports["#BCBA:IMV-symbol-description"]={en:["S&P MERVAL Index"],fa:["MERVAL Index"]},e.exports["#HSI:HSI-symbol-description"]={en:["Hang Seng Index"],fa:["Hang Seng Index"]},e.exports["#BVL:SPBLPGPT-symbol-description"]={en:["S&P / BVL Peru General Index (PEN)"],fa:["S&P / BVL Peru General Index (PEN)"]},e.exports["#EGX:EGX30-symbol-description"]={en:["EGX 30 Index"],fa:["EGX 30 Price Return Index"]},e.exports["#BVC:IGBC-symbol-description"]={en:["Indice General de la Bolsa de Valores de Colombia"],fa:["Indice General de la Bolsa de Valores de Colombia"]},e.exports["#TWSE:TAIEX-symbol-description"]={en:["Taiwan Capitalization Weighted Stock Index"],fa:["Taiwan Capitalization Weighted Stock Index"]},e.exports["#QSE:GNRI-symbol-description"]={en:["QE Index"],fa:["QE Index"]},e.exports["#BME:IBC-symbol-description"]={en:["IBEX 35 Index"],fa:["IBEX 35 Index"]},e.exports["#NZX:NZ50G-symbol-description"]={en:["S&P / NZX 50 Index Gross"],fa:["S&P / NZX 50 Index Gross"]},e.exports["#SIX:SMI-symbol-description"]={en:["Swiss Market Index"],fa:["Swiss Market Index"]},e.exports["#SZSE:399001-symbol-description"]={en:["Shenzhen Component Index"],fa:["SZSE Component Index"]},e.exports["#TADAWUL:TASI-symbol-description"]={en:["Tadawul All Shares Index"],fa:["Tadawul All Shares Index"]},e.exports["#IDX:COMPOSITE-symbol-description"]={en:["IDX Composite Index"],fa:["IDX Composite Index"]},e.exports["#EURONEXT:PX1-symbol-description"]={en:["CAC 40 Index"],fa:["CAC 40 Index"]},e.exports["#OMXHEX:OMXH25-symbol-description"]={en:["OMX Helsinki 25 Index"],fa:["OMX Helsinki 25 Index"]},e.exports["#EURONEXT:BEL20-symbol-description"]={en:["BEL 20 Index"],fa:["BEL 20 Index"]},e.exports["#TVC:STI-symbol-description"]={en:["Straits Times Index"],fa:["Straits Times Index"]},e.exports["#DFM:DFMGI-symbol-description"]={en:["DFM Index"],fa:["DFM Index"]},e.exports["#TVC:KOSPI-symbol-description"]={en:["Korea Composite Stock Price Index"],fa:["Korea Composite Stock Price Index"]},e.exports["#FTSEMYX:FBMKLCI-symbol-description"]={en:["FTSE Bursa Malaysia KLCI Index"],fa:["FTSE Bursa Malaysia KLCI Index"]},e.exports["#TASE:TA35-symbol-description"]={en:["TA-35 Index"],fa:["TA-35 Index"]},e.exports["#OMXSTO:OMXS30-symbol-description"]={en:["OMX Stockholm 30 Index"],fa:["OMX Stockholm 30 Index"]},e.exports["#OMXICE:OMXI8-symbol-description"]={en:["OMX Iceland 8 Index"],fa:["OMX Iceland 8 Index"]},e.exports["#NSENG:NSE30-symbol-description"]={en:["NSE 30 Index"],fa:["NSE 30 Index"]},e.exports["#BAHRAIN:BSEX-symbol-description"]={en:["Bahrain All Share Index"],fa:["Bahrain All Share Index"]},e.exports["#OMXTSE:OMXTGI-symbol-description"]={en:["OMX Tallinn Gross Index"],fa:["OMX Tallinn GI"]},e.exports["#OMXCOP:OMXC25-symbol-description"]={en:["OMX Copenhagen 25 Index"],fa:["OMX Copenhagen 25 Index"]},e.exports["#OMXRSE:OMXRGI-symbol-description"]={en:["OMX Riga Gross Index"],fa:["OMX Riga GI"]},e.exports["#BELEX:BELEX15-symbol-description"]={
en:["BELEX 15 Index"],fa:["BELEX 15 Index"]},e.exports["#OMXVSE:OMXVGI-symbol-description"]={en:["OMX Vilnius Gross Index"],fa:["OMX Vilnius GI"]},e.exports["#EURONEXT:AEX-symbol-description"]={en:["AEX Index"],fa:["AEX Index"]},e.exports["#CBOE:VIX-symbol-description"]={en:["Volatility S&P 500 Index"],fa:["Volatility S&P 500 Index"]},e.exports["#NASDAQ:XAU-symbol-description"]={en:["PHLX Gold and Silver Sector Index"],fa:["PHLX Gold and Silver Sector Index"]},e.exports["#DJ:DJUSCL-symbol-description"]={en:["Dow Jones U.S. Coal Index"],fa:["Dow Jones U.S. Coal Index"]},e.exports["#DJ:DJCIKC-symbol-description"]={en:["Dow Jones Commodity Index Coffee"],fa:["Dow Jones Commodity Index Coffee"]},e.exports["#DJ:DJCIEN-symbol-description"]={en:["Dow Jones Commodity Index Energy"],fa:["Dow Jones Commodity Index Energy"]},e.exports["#NASDAQ:OSX-symbol-description"]={en:["PHLX Oil Service Sector Index"],fa:["PHLX Oil Service Sector Index"]},e.exports["#DJ:DJCISB-symbol-description"]={en:["Dow Jones Commodity Index Sugar"],fa:["Dow Jones Commodity Index Sugar"]},e.exports["#DJ:DJCICC-symbol-description"]={en:["Dow Jones Commodity Index Cocoa"],fa:["Dow Jones Commodity Index Cocoa"]},e.exports["#DJ:DJCIGR-symbol-description"]={en:["Dow Jones Commodity Index Grains"],fa:["Dow Jones Commodity Index Grains"]},e.exports["#DJ:DJCIAGC-symbol-description"]={en:["Dow Jones Commodity Index Agriculture Capped Component"],fa:["Dow Jones Commodity Index Agriculture Capped Component"]},e.exports["#DJ:DJCISI-symbol-description"]={en:["Dow Jones Commodity Index Silver"],fa:["Dow Jones Commodity Index Silver"]},e.exports["#DJ:DJCIIK-symbol-description"]={en:["Dow Jones Commodity Index Nickel"],fa:["Dow Jones Commodity Index Nickel"]},e.exports["#NASDAQ:HGX-symbol-description"]={en:["PHLX Housing Sector Index"],fa:["PHLX Housing Sector Index"]},e.exports["#DJ:DJCIGC-symbol-description"]={en:["Dow Jones Commodity Index Gold"],fa:["Dow Jones Commodity Index Gold"]},e.exports["#SP:SPGSCI-symbol-description"]={en:["S&P Goldman Sachs Commodity Index"],fa:["S&P Goldman Sachs Commodity Index"]},e.exports["#NASDAQ:UTY-symbol-description"]={en:["PHLX Utility Sector Index"],fa:["PHLX Utility Sector Index"]},e.exports["#DJ:DJU-symbol-description"]={en:["Dow Jones Utility Average Index"],fa:["Dow Jones Utility Average Index"]},e.exports["#SP:SVX-symbol-description"]={en:["S&P 500 Value Index"],fa:["S&P 500 Value Index"]},e.exports["#SP:OEX-symbol-description"]={en:["S&P 100 Index"],fa:["S&P 100 Index"]},e.exports["#CBOE:OEX-symbol-description"]={en:["S&P 100 Index"],fa:["S&P 100 Index"]},e.exports["#NASDAQ:SOX-symbol-description"]={en:["Philadelphia Semiconductor Index"],fa:["Philadelphia Semiconductor Index"]},e.exports["#RUSSELL:RUI-symbol-description"]={en:["Russell 1000 Index"],fa:["Russell 1000 Index"]},e.exports["#RUSSELL:RUA-symbol-description"]={en:["Russell 3000 Index"],fa:["Russell 3000 Index"]},e.exports["#RUSSELL:RUT-symbol-description"]={en:["Russell 2000 Index"],fa:["Russell 2000 Index"]},e.exports["#NYSE:XMI-symbol-description"]={
en:["NYSE ARCA Major Market Index"],fa:["NYSE ARCA Major Market Index"]},e.exports["#NYSE:XAX-symbol-description"]={en:["AMEX Composite Index"],fa:["AMEX Composite Index"]},e.exports["#NASDAQ:NDX-symbol-description"]={en:["Nasdaq 100 Index"],fa:["Nasdaq 100 Index"]},e.exports["#NASDAQ:IXIC-symbol-description"]={en:["Nasdaq Composite Index"],fa:["Nasdaq Composite Index"]},e.exports["#DJ:DJT-symbol-description"]={en:["Dow Jones Transportation Average Index"],fa:["Dow Jones Transportation Average Index"]},e.exports["#NYSE:NYA-symbol-description"]={en:["NYSE Composite Index"],fa:["NYSE Composite Index"]},e.exports["#NYMEX:CJ1!-symbol-description"]={en:["Cocoa Futures"],fa:["Cocoa Futures"]},e.exports["#USDILS-symbol-description"]={en:["U.S. Dollar / Israeli Shekel"],fa:["U.S. Dollar / Israeli Shekel"]},e.exports["#TSXV:F-symbol-description"]={en:["Fiore Gold Inc"],fa:["Fiore Gold Inc"]},e.exports["#SIX:F-symbol-description"]={en:["Ford Motor Company"],fa:["Ford Motor Company"]},e.exports["#BMV:F-symbol-description"]={en:["Ford Motor Company"],fa:["Ford Motor Company"]},e.exports["#TWII-symbol-description"]={en:["Taiwan Weighted Index"],fa:["Taiwan Weighted Index"]},e.exports["#TVC:PL10Y-symbol-description"]={en:["Poland Government Bonds 10 YR Yield"],fa:["Poland Government Bonds 10 YR Yield"]},e.exports["#TVC:PL05Y-symbol-description"]={en:["Poland Government Bonds 5 YR Yield"],fa:["Poland Government Bonds 5 YR Yield"]},e.exports["#SET:GC-symbol-description"]={en:["Global Connections Public Company"],fa:["Global Connections Public Company"]},e.exports["#TSX:GC-symbol-description"]={en:["Great Canadian Gaming Corporation"],fa:["Great Canadian Gaming Corporation"]},e.exports["#TVC:FTMIB-symbol-description"]={en:["Milano Italia Borsa Index"],fa:["Milano Italia Borsa Index"]},e.exports["#OANDA:SPX500USD-symbol-description"]={en:["S&P 500 Index"],fa:["S&P 500 Index"]},e.exports["#BMV:CT-symbol-description"]={en:["China SX20 RT"],fa:["China SX20 RT"]},e.exports["#TSXV:CT-symbol-description"]={en:["Centenera Mining Corporation"],fa:["Centenera Mining Corporation"]},e.exports["#BYBIT:ETHUSD-symbol-description"]={en:["ETHUSD Perpetual Contract"],fa:["ETHUSD Perpetual Contract"]},e.exports["#BYBIT:XRPUSD-symbol-description"]={en:["XRPUSD Perpetual Contract"],fa:["XRPUSD Perpetual Contract"]},e.exports["#BYBIT:BTCUSD-symbol-description"]={en:["BTCUSD Perpetual Contract"],fa:["BTCUSD Perpetual Contract"]},e.exports["#BITMEX:ETHUSD-symbol-description"]={en:["ETHUSD Perpetual Futures Contract"],fa:["ETHUSD Perpetual Futures Contract"]},e.exports["#DERIBIT:BTCUSD-symbol-description"]={en:["BTCUSD Perpetual Futures Contract"],fa:["BTCUSD Perpetual Futures Contract"]},e.exports["#DERIBIT:ETHUSD-symbol-description"]={en:["ETHUSD Perpetual Futures Contract"],fa:["ETHUSD Perpetual Futures Contract"]},e.exports["#USDHUF-symbol-description"]={en:["U.S. Dollar / Hungarian Forint"],fa:["U.S. Dollar / Hungarian Forint"]},e.exports["#USDTHB-symbol-description"]={en:["U.S. Dollar / Thai Baht"],fa:["U.S. Dollar / Thai Baht"]},
e.exports["#FOREXCOM:US2000-symbol-description"]={en:["US Small Cap 2000"],fa:["US Small Cap 2000"]},e.exports["#TSXV:PBR-symbol-description"]={en:["Para Resources Inc"],fa:["Para Resources Inc"]},e.exports["#NYSE:SI-symbol-description"]={en:["Silvergate Capital Corporation"],fa:["Silvergate Capital Corporation"]},e.exports["#NASDAQ:LE-symbol-description"]={en:["Lands' End Inc"],fa:["Lands' End Inc"]},e.exports["#CME:CB1!-symbol-description"]={en:["Butter Futures-Cash (Continuous: Current contract in front)"],fa:["Butter Futures-Cash (Continuous: Current contract in front)"]},e.exports["#LSE:SCHO-symbol-description"]={en:["Scholium Group Plc Ord 1P"],fa:["Scholium Group Plc Ord 1P"]},e.exports["#NEO:HE-symbol-description"]={en:["Hanwei Energy Services Corp."],fa:["Hanwei Energy Services Corp."]},e.exports["#NYSE:HE-symbol-description"]={en:["Hawaiian Electric Industries"],fa:["Hawaiian Electric Industries"]},e.exports["#OMXCOP:SCHO-symbol-description"]={en:["Schouw & Co A/S"],fa:["Schouw & Co A/S"]},e.exports["#TSX:HE-symbol-description"]={en:["Hanwei Energy Services Corp."],fa:["Hanwei Energy Services Corp."]},e.exports["#BSE:ITI-symbol-description"]={en:["ITI Ltd"],fa:["ITI Ltd"]},e.exports["#NSE:ITI-symbol-description"]={en:["Indian Telephone Industries Limited"],fa:["Indian Telephone Industries Limited"]},e.exports["#TSX:LS-symbol-description"]={en:["Middlefield Healthcare & Life Sciences Dividend Fund"],fa:["Middlefield Healthcare & Life Sciences Dividend Fund"]},e.exports["#BITMEX:XBT-symbol-description"]={en:["Bitcoin / U.S. Dollar Index"],fa:["Bitcoin / U.S. Dollar Index"]},e.exports["#CME_MINI:RTY1!-symbol-description"]={en:["E-Mini Russell 2000 Index Futures"],fa:["E-Mini Russell 2000 Index Futures"]},e.exports["#CRYPTOCAP:TOTAL-symbol-description"]={en:["Crypto Total Market Cap, $"],fa:["Crypto Total Market Cap, $"]},e.exports["#ICEUS:DX1!-symbol-description"]={en:["U.S. Dollar Index Futures"],fa:["U.S. Dollar Index Futures"]},e.exports["#NYMEX:TT1!-symbol-description"]={en:["Cotton Futures"],fa:["Cotton Futures"]},e.exports["#PHEMEX:BTCUSD-symbol-description"]={en:["BTC Perpetual Futures Contract"],fa:["BTC Perpetual Futures Contract"]},e.exports["#PHEMEX:ETHUSD-symbol-description"]={en:["ETH Perpetual Futures Contract"],fa:["ETH Perpetual Futures Contract"]},e.exports["#PHEMEX:XRPUSD-symbol-description"]={en:["XRP Perpetual Futures Contract"],fa:["XRP Perpetual Futures Contract"]},e.exports["#PHEMEX:LTCUSD-symbol-description"]={en:["LTC Perpetual Futures Contract"],fa:["LTC Perpetual Futures Contract"]},e.exports["#BITCOKE:BCHUSD-symbol-description"]={en:["BCH Quanto Swap"],fa:["BCH Quanto Swap"]},e.exports["#BITCOKE:BTCUSD-symbol-description"]={en:["BTC Quanto Swap"],fa:["BTC Quanto Swap"]},e.exports["#BITCOKE:ETHUSD-symbol-description"]={en:["ETH Quanto Swap"],fa:["ETH Quanto Swap"]},e.exports["#BITCOKE:LTCUSD-symbol-description"]={en:["LTC Quanto Swap"],fa:["LTC Quanto Swap"]},e.exports["#TVC:CA10-symbol-description"]={en:["Canadian Government Bonds, 10 YR"],fa:["Canadian Government Bonds, 10 YR"]},
e.exports["#TVC:CA10Y-symbol-description"]={en:["Canadian Government Bonds 10 YR Yield"],fa:["Canadian Government Bonds 10 YR Yield"]},e.exports["#TVC:ID10Y-symbol-description"]={en:["Indonesia Government Bonds 10 YR Yield"],fa:["Indonesia Government Bonds 10 YR Yield"]},e.exports["#TVC:NL10-symbol-description"]={en:["Netherlands Government Bonds, 10 YR"],fa:["Netherlands Government Bonds, 10 YR"]},e.exports["#TVC:NL10Y-symbol-description"]={en:["Netherlands Government Bonds 10 YR Yield"],fa:["Netherlands Government Bonds 10 YR Yield"]},e.exports["#TVC:NZ10-symbol-description"]={en:["New Zealand Government Bonds, 10 YR"],fa:["New Zealand Government Bonds, 10 YR"]},e.exports["#TVC:NZ10Y-symbol-description"]={en:["New Zealand Government Bonds 10 YR Yield"],fa:["New Zealand Government Bonds 10 YR Yield"]},e.exports["#SOLUSD-symbol-description"]={en:["Solana / U.S. Dollar"],fa:["Solana / U.S. Dollar"]},e.exports["#LUNAUSD-symbol-description"]={en:["Luna / U.S. Dollar"],fa:["Luna / U.S. Dollar"]},e.exports["#UNIUSD-symbol-description"]={en:["Uniswap / U.S. Dollar"],fa:["Uniswap / U.S. Dollar"]},e.exports["#LTCBRL-symbol-description"]={en:["Litecoin / Brazilian Real"],fa:["Litecoin / Brazilian Real"]},e.exports["#ETCEUR-symbol-description"]={en:["Ethereum Classic / Euro"],fa:["Ethereum Classic / Euro"]},e.exports["#ETHKRW-symbol-description"]={en:["Ethereum / South Korean Won"],fa:["Ethereum / South Korean Won"]},e.exports["#BTCRUB-symbol-description"]={en:["Bitcoin / Russian Ruble"],fa:["Bitcoin / Russian Ruble"]},e.exports["#BTCTHB-symbol-description"]={en:["Bitcoin / Thai Baht"],fa:["Bitcoin / Thai Baht"]},e.exports["#ETHTHB-symbol-description"]={en:["Ethereum / Thai Baht"],fa:["Ethereum / Thai Baht"]},e.exports["#TVC:EU10YY-symbol-description"]={en:["Euro Government Bonds 10 YR Yield"],fa:["Euro Government Bonds 10 YR Yield"]},e.exports["#NASDAQ:LCID-symbol-description"]={en:["Lucid Group, Inc."],fa:["Lucid Group, Inc."]},e.exports["#TADAWUL:2370-symbol-description"]={en:["Middle East Specialized Cables Co."],fa:["Middle East Specialized Cables Co."]},e.exports["#NASDAQ:GOOG-symbol-description"]={en:["#NASDAQ:GOOG-symbol-description"],fa:["#NASDAQ:GOOG-symbol-description"]},e.exports["#NASDAQ:GOOGL-symbol-description"]={en:["#NASDAQ:GOOGL-symbol-description"],fa:["#NASDAQ:GOOGL-symbol-description"]}}}]);