.underline-tab-cfYYXvwA {
  align-items: center;
  background: #0000;
  border: none;
  color: var(--themed-color-underline-tab-default-content, #131722);
  display: inline-grid;
  flex-shrink: 0;
  grid-auto-flow: column;
  justify-content: center;
  padding: 0;
  word-break: keep-all;
  -webkit-tap-highlight-color: transparent;
  cursor: default;
  --ui-lib-underline-tabs-tab-margin-bottom-xsmall: 10px;
  --ui-lib-underline-tabs-tab-margin-bottom-small: 8px;
  --ui-lib-underline-tabs-tab-margin-bottom-medium: 10px;
  --ui-lib-underline-tabs-tab-margin-bottom-large: 12px;
  --ui-lib-underline-tabs-tab-margin-bottom-xlarge: 16px;
}
html.theme-dark .underline-tab-cfYYXvwA {
  color: var(--themed-color-underline-tab-default-content, #d1d4dc);
}
.underline-tab-cfYYXvwA {
  outline: none;
  overflow: visible;
  position: relative;
}
.underline-tab-cfYYXvwA:focus {
  outline: none;
}
.underline-tab-cfYYXvwA:focus-visible {
  outline: none;
}
.underline-tab-cfYYXvwA:after {
  border-style: solid;
  border-width: 2px;
  box-sizing: border-box;
  content: "";
  display: none;
  height: calc(100% + 8px);
  pointer-events: none;
  position: absolute;
  right: -4px;
  top: -4px;
  width: calc(100% + 8px);
  z-index: 1;
}
.underline-tab-cfYYXvwA:focus:after {
  display: block;
}
.underline-tab-cfYYXvwA:focus-visible:after {
  display: block;
}
.underline-tab-cfYYXvwA:focus:not(:focus-visible):after {
  display: none;
}
.underline-tab-cfYYXvwA:after {
  border-color: #2962ff;
  border-radius: 8px;
}
.underline-tab-cfYYXvwA.disable-focus-outline-cfYYXvwA:after {
  display: none;
}
.underline-tab-cfYYXvwA.enable-cursor-pointer-cfYYXvwA:not(:disabled):not(
    [aria-disabled="true"]
  ) {
  cursor: pointer;
}
@media (any-hover: hover) {
  .underline-tab-cfYYXvwA:hover {
    color: var(--themed-color-default-gray, #6a6d78);
  }
  html.theme-dark .underline-tab-cfYYXvwA:hover {
    color: var(--themed-color-default-gray, #868993);
  }
}
.underline-tab-cfYYXvwA.selected-cfYYXvwA {
  color: var(--themed-color-underline-tab-selected-content, #131722);
}
html.theme-dark .underline-tab-cfYYXvwA.selected-cfYYXvwA {
  color: var(--themed-color-underline-tab-selected-content, #d1d4dc);
}
.underline-tab-cfYYXvwA:not(.disable-active-state-styles-cfYYXvwA):active:not(
    :disabled
  ):not([aria-disabled="true"]) {
  color: var(--themed-color-underline-tab-active-content, #131722);
}
html.theme-dark
  .underline-tab-cfYYXvwA:not(.disable-active-state-styles-cfYYXvwA):active:not(
    :disabled
  ):not([aria-disabled="true"]) {
  color: var(--themed-color-underline-tab-active-content, #d1d4dc);
}
.underline-tab-cfYYXvwA:disabled,
.underline-tab-cfYYXvwA[aria-disabled="true"] {
  background-color: var(--themed-color-tab-disabled, #0000);
  color: var(--themed-color-card-border-hover, #b2b5be);
}
html.theme-dark .underline-tab-cfYYXvwA:disabled,
html.theme-dark .underline-tab-cfYYXvwA[aria-disabled="true"] {
  background-color: var(--themed-color-tab-disabled, #0000);
  color: var(--themed-color-card-border-hover, #5d606b);
}
.underline-tab-cfYYXvwA.size-xsmall-cfYYXvwA {
  font-family:
    -apple-system,
    BlinkMacSystemFont,
    Trebuchet MS,
    Roboto,
    Ubuntu,
    sans-serif;
  font-feature-settings:
    "tnum" on,
    "lnum" on;
  font-style: normal;
  --ui-lib-typography-font-size: 14px;
  font-size: var(--ui-lib-typography-font-size);
  font-weight: 400;
  --ui-lib-typography-line-height: 18px;
  border-radius: 2px;
  border-radius: 4px;
  line-height: var(--ui-lib-typography-line-height);
  margin: 0 12px;
  padding-bottom: var(--ui-lib-underline-tabs-tab-margin-bottom-xsmall);
}
.underline-tab-cfYYXvwA.size-xsmall-cfYYXvwA:after {
  border-radius: 6px;
  height: calc(100% + 2px);
  right: -10px;
  top: -2px;
  width: calc(100% + 20px);
}
.underline-tab-cfYYXvwA.size-small-cfYYXvwA {
  font-family:
    -apple-system,
    BlinkMacSystemFont,
    Trebuchet MS,
    Roboto,
    Ubuntu,
    sans-serif;
  font-feature-settings:
    "tnum" on,
    "lnum" on;
  font-style: normal;
  --ui-lib-typography-font-size: 16px;
  font-size: var(--ui-lib-typography-font-size);
  font-weight: 600;
  --ui-lib-typography-line-height: 24px;
  border-radius: 4px;
  line-height: var(--ui-lib-typography-line-height);
  margin: 0 12px;
  padding-bottom: var(--ui-lib-underline-tabs-tab-margin-bottom-small);
}
.underline-tab-cfYYXvwA.size-small-cfYYXvwA:after {
  height: calc(100% + 2px);
  right: -10px;
  top: -2px;
  width: calc(100% + 20px);
}
.underline-tab-cfYYXvwA.size-medium-cfYYXvwA {
  font-family:
    -apple-system,
    BlinkMacSystemFont,
    Trebuchet MS,
    Roboto,
    Ubuntu,
    sans-serif;
  font-feature-settings:
    "tnum" on,
    "lnum" on;
  font-style: normal;
  --ui-lib-typography-font-size: 18px;
  font-size: var(--ui-lib-typography-font-size);
  font-weight: 600;
  --ui-lib-typography-line-height: 24px;
  border-radius: 4px;
  line-height: var(--ui-lib-typography-line-height);
  margin: 0 16px;
  padding-bottom: var(--ui-lib-underline-tabs-tab-margin-bottom-medium);
}
.underline-tab-cfYYXvwA.size-medium-cfYYXvwA:after {
  height: calc(100% + 2px);
  right: -12px;
  top: -2px;
  width: calc(100% + 24px);
}
.underline-tab-cfYYXvwA.size-large-cfYYXvwA {
  font-family:
    -apple-system,
    BlinkMacSystemFont,
    Trebuchet MS,
    Roboto,
    Ubuntu,
    sans-serif;
  font-feature-settings:
    "tnum" on,
    "lnum" on;
  font-style: normal;
  --ui-lib-typography-font-size: 20px;
  font-size: var(--ui-lib-typography-font-size);
  font-weight: 600;
  --ui-lib-typography-line-height: 24px;
  border-radius: 4px;
  line-height: var(--ui-lib-typography-line-height);
  margin: 0 20px;
  padding-bottom: var(--ui-lib-underline-tabs-tab-margin-bottom-large);
}
.underline-tab-cfYYXvwA.size-large-cfYYXvwA:after {
  height: calc(100% + 4px);
  right: -14px;
  top: -4px;
  width: calc(100% + 28px);
}
.underline-tab-cfYYXvwA.size-xlarge-cfYYXvwA {
  font-family:
    -apple-system,
    BlinkMacSystemFont,
    Trebuchet MS,
    Roboto,
    Ubuntu,
    sans-serif;
  font-feature-settings:
    "tnum" on,
    "lnum" on;
  --ui-lib-typography-line-height: 28px;
  line-height: var(--ui-lib-typography-line-height);
  --ui-lib-typography-font-size: 24px;
  border-radius: 4px;
  font-size: var(--ui-lib-typography-font-size);
  font-style: normal;
  font-weight: 600;
  margin: 0 20px;
  padding-bottom: var(--ui-lib-underline-tabs-tab-margin-bottom-xlarge);
}
.underline-tab-cfYYXvwA.size-xlarge-cfYYXvwA:after {
  height: calc(100% + 4px);
  right: -14px;
  top: -4px;
  width: calc(100% + 28px);
}
.underline-tab-cfYYXvwA.fake-cfYYXvwA {
  pointer-events: none;
  position: absolute;
  right: -999999px;
  z-index: -1;
}
.underline-tab-cfYYXvwA.margin-small-cfYYXvwA,
.underline-tab-cfYYXvwA.margin-xsmall-cfYYXvwA {
  margin: 0 3px;
}
.underline-tab-cfYYXvwA.margin-medium-cfYYXvwA {
  margin: 0 4px;
}
.underline-tab-cfYYXvwA.margin-large-cfYYXvwA,
.underline-tab-cfYYXvwA.margin-xlarge-cfYYXvwA {
  margin: 0 5px;
}
.underline-tab-cfYYXvwA:first-child {
  margin-right: 0;
}
.underline-tab-cfYYXvwA.collapse-cfYYXvwA:nth-last-child(-n + 3),
.underline-tab-cfYYXvwA:nth-last-child(-n + 2) {
  margin-left: 0;
}
.ellipsis-children-cfYYXvwA {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.ellipsis-children-cfYYXvwA:after {
  content: "";
  display: block;
}
.arrow-icon-NIrWNOPk {
  display: inline-flex;
  max-height: 28px;
  max-width: 28px;
  min-height: 18px;
  min-width: 18px;
  transition: transform 0.35s ease;
}
.arrow-icon-NIrWNOPk.dropped-NIrWNOPk {
  transform: rotate(-180deg);
}
.size-xsmall-NIrWNOPk {
  margin-inline-start: 2px;
}
.size-small-NIrWNOPk {
  margin-inline-start: 4px;
}
.size-large-NIrWNOPk,
.size-medium-NIrWNOPk,
.size-xlarge-NIrWNOPk {
  margin-inline-start: 8px;
}
.link-item-eIA09f0e {
  cursor: pointer;
}
.scroll-wrap-SmxgjhBJ {
  contain: content;
  height: 100%;
  position: relative;
  width: 100%;
}
.scroll-wrap-SmxgjhBJ.size-xlarge-SmxgjhBJ {
  margin: -6px
    calc(max(var(--ui-lib-underline-tabs-hor-padding, 0px), 14px) * -1) 0;
  padding: 6px max(var(--ui-lib-underline-tabs-hor-padding, 0px), 14px) 0;
}
.scroll-wrap-SmxgjhBJ.size-xlarge-SmxgjhBJ:before {
  left: max(var(--ui-lib-underline-tabs-hor-padding, 0px), 14px);
  right: max(var(--ui-lib-underline-tabs-hor-padding, 0px), 14px);
}
.scroll-wrap-SmxgjhBJ.size-xlarge-SmxgjhBJ.enable-scroll-SmxgjhBJ
  .underline-tabs-SmxgjhBJ {
  padding-right: 0;
}
.scroll-wrap-SmxgjhBJ.size-xlarge-SmxgjhBJ.enable-scroll-SmxgjhBJ
  .underline-tabs-SmxgjhBJ:after {
  content: "";
  flex-shrink: 0;
  width: max(var(--ui-lib-underline-tabs-hor-padding, 0px), 14px);
}
.scroll-wrap-SmxgjhBJ.size-large-SmxgjhBJ {
  margin: -6px
    calc(max(var(--ui-lib-underline-tabs-hor-padding, 0px), 14px) * -1) 0;
  padding: 6px max(var(--ui-lib-underline-tabs-hor-padding, 0px), 14px) 0;
}
.scroll-wrap-SmxgjhBJ.size-large-SmxgjhBJ:before {
  left: max(var(--ui-lib-underline-tabs-hor-padding, 0px), 14px);
  right: max(var(--ui-lib-underline-tabs-hor-padding, 0px), 14px);
}
.scroll-wrap-SmxgjhBJ.size-large-SmxgjhBJ.enable-scroll-SmxgjhBJ
  .underline-tabs-SmxgjhBJ {
  padding-right: 0;
}
.scroll-wrap-SmxgjhBJ.size-large-SmxgjhBJ.enable-scroll-SmxgjhBJ
  .underline-tabs-SmxgjhBJ:after {
  content: "";
  flex-shrink: 0;
  width: max(var(--ui-lib-underline-tabs-hor-padding, 0px), 14px);
}
.scroll-wrap-SmxgjhBJ.size-medium-SmxgjhBJ {
  margin: -4px
    calc(max(var(--ui-lib-underline-tabs-hor-padding, 0px), 12px) * -1) 0;
  padding: 4px max(var(--ui-lib-underline-tabs-hor-padding, 0px), 12px) 0;
}
.scroll-wrap-SmxgjhBJ.size-medium-SmxgjhBJ:before {
  left: max(var(--ui-lib-underline-tabs-hor-padding, 0px), 12px);
  right: max(var(--ui-lib-underline-tabs-hor-padding, 0px), 12px);
}
.scroll-wrap-SmxgjhBJ.size-medium-SmxgjhBJ.enable-scroll-SmxgjhBJ
  .underline-tabs-SmxgjhBJ {
  padding-right: 0;
}
.scroll-wrap-SmxgjhBJ.size-medium-SmxgjhBJ.enable-scroll-SmxgjhBJ
  .underline-tabs-SmxgjhBJ:after {
  content: "";
  flex-shrink: 0;
  width: max(var(--ui-lib-underline-tabs-hor-padding, 0px), 12px);
}
.scroll-wrap-SmxgjhBJ.size-small-SmxgjhBJ {
  margin: -2px
    calc(max(var(--ui-lib-underline-tabs-hor-padding, 0px), 10px) * -1) 0;
  padding: 2px max(var(--ui-lib-underline-tabs-hor-padding, 0px), 10px) 0;
}
.scroll-wrap-SmxgjhBJ.size-small-SmxgjhBJ:before {
  left: max(var(--ui-lib-underline-tabs-hor-padding, 0px), 10px);
  right: max(var(--ui-lib-underline-tabs-hor-padding, 0px), 10px);
}
.scroll-wrap-SmxgjhBJ.size-small-SmxgjhBJ.enable-scroll-SmxgjhBJ
  .underline-tabs-SmxgjhBJ {
  padding-right: 0;
}
.scroll-wrap-SmxgjhBJ.size-small-SmxgjhBJ.enable-scroll-SmxgjhBJ
  .underline-tabs-SmxgjhBJ:after {
  content: "";
  flex-shrink: 0;
  width: max(var(--ui-lib-underline-tabs-hor-padding, 0px), 10px);
}
.scroll-wrap-SmxgjhBJ.size-xsmall-SmxgjhBJ {
  margin: -4px
    calc(max(var(--ui-lib-underline-tabs-hor-padding, 0px), 10px) * -1) 0;
  padding: 4px max(var(--ui-lib-underline-tabs-hor-padding, 0px), 10px) 0;
}
.scroll-wrap-SmxgjhBJ.size-xsmall-SmxgjhBJ:before {
  left: max(var(--ui-lib-underline-tabs-hor-padding, 0px), 10px);
  right: max(var(--ui-lib-underline-tabs-hor-padding, 0px), 10px);
}
.scroll-wrap-SmxgjhBJ.size-xsmall-SmxgjhBJ.enable-scroll-SmxgjhBJ
  .underline-tabs-SmxgjhBJ {
  padding-right: 0;
}
.scroll-wrap-SmxgjhBJ.size-xsmall-SmxgjhBJ.enable-scroll-SmxgjhBJ
  .underline-tabs-SmxgjhBJ:after {
  content: "";
  flex-shrink: 0;
  width: max(var(--ui-lib-underline-tabs-hor-padding, 0px), 10px);
}
.scroll-wrap-SmxgjhBJ.enable-scroll-SmxgjhBJ {
  overflow-x: auto;
  overflow-y: hidden;
  -webkit-overflow-scrolling: touch;
}
@supports (-moz-appearance: none) {
  .scroll-wrap-SmxgjhBJ.enable-scroll-SmxgjhBJ {
    scrollbar-width: none;
  }
}
.scroll-wrap-SmxgjhBJ.enable-scroll-SmxgjhBJ.sb-scrollbar-wrap {
  display: none;
}
.scroll-wrap-SmxgjhBJ.enable-scroll-SmxgjhBJ::-webkit-scrollbar {
  display: none;
  height: 0;
  width: 0;
}
.scroll-wrap-SmxgjhBJ.enable-scroll-SmxgjhBJ::-webkit-scrollbar-thumb,
.scroll-wrap-SmxgjhBJ.enable-scroll-SmxgjhBJ::-webkit-scrollbar-track {
  display: none;
}
.scroll-wrap-SmxgjhBJ.enable-scroll-SmxgjhBJ::-webkit-scrollbar-corner {
  display: none;
}
.underline-tabs-SmxgjhBJ {
  display: inline-block;
  position: relative;
  white-space: nowrap;
}
.underline-tabs-SmxgjhBJ.make-grid-column-SmxgjhBJ {
  display: inline-grid;
  grid-auto-flow: column;
}
.underline-tabs-SmxgjhBJ.make-grid-column-SmxgjhBJ.stretch-tabs-SmxgjhBJ {
  width: 100%;
}
.underline-tabs-SmxgjhBJ.make-grid-column-SmxgjhBJ.equal-tab-size-SmxgjhBJ {
  grid-auto-columns: minmax(0, 1fr);
}
.scroll-wrap-SmxgjhBJ:before,
.underline-tabs-SmxgjhBJ:before {
  background-color: var(--themed-color-tab-switcher-border, #f0f3fa);
  border-radius: 2px;
  bottom: 0;
  content: "";
  height: 4px;
  left: 0;
  position: absolute;
  right: 0;
}
html.theme-dark .scroll-wrap-SmxgjhBJ:before,
html.theme-dark .underline-tabs-SmxgjhBJ:before {
  background-color: var(--themed-color-tab-switcher-border, #434651);
}
.center-Pun8HxCz,
.corner-Pun8HxCz,
.underline-Pun8HxCz {
  height: 4px;
  position: absolute;
  transition: transform 0.1s ease-in-out;
}
.underline-Pun8HxCz {
  bottom: 0;
  right: 0;
  transform-origin: right;
  width: 100px;
}
.center-Pun8HxCz {
  background: var(--themed-color-underline-tab-selected-content, #131722);
  left: 0;
  right: 0;
  transform: translateX(0);
  transform-origin: center;
}
html.theme-dark .center-Pun8HxCz {
  background: var(--themed-color-underline-tab-selected-content, #d1d4dc);
}
.corner-Pun8HxCz {
  background: var(--themed-color-underline-tab-selected-content, #131722);
  border-radius: 0 2px 2px 0;
  position: absolute;
  transform-origin: right;
  width: 20px;
}
html.theme-dark .corner-Pun8HxCz {
  background: var(--themed-color-underline-tab-selected-content, #d1d4dc);
}
.corner-Pun8HxCz:last-child {
  border-radius: 2px 0 0 2px;
  left: 0;
  transform-origin: left;
}
.disabled-Pun8HxCz .center-Pun8HxCz,
.disabled-Pun8HxCz .corner-Pun8HxCz {
  background-color: var(--themed-color-card-border-hover, #b2b5be);
}
html.theme-dark .disabled-Pun8HxCz .center-Pun8HxCz,
html.theme-dark .disabled-Pun8HxCz .corner-Pun8HxCz {
  background-color: var(--themed-color-card-border-hover, #5d606b);
}
