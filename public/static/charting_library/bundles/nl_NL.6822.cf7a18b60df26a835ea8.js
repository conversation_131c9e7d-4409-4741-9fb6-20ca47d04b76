(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[6822],{94920:e=>{e.exports={en:["ADJ"],nl_NL:["adj"]}},16755:e=>{e.exports={en:["B-ADJ"],nl_NL:["B-ADJ"]}},82631:e=>{e.exports={en:["SET"],nl_NL:["SET"]}},22233:e=>{e.exports={en:["auto"],nl_NL:["auto"]}},4161:e=>{e.exports={en:["log"],nl_NL:["log"]}},58221:e=>{e.exports={en:["All data"],nl_NL:["All data"]}},19273:e=>{e.exports={en:["Year to day"],nl_NL:["Year to day"]}},58426:e=>{e.exports={en:["{timePeriod} in {timeInterval}"],nl_NL:["{timePeriod} in {timeInterval}"]}},93020:e=>{e.exports={en:["Adjust data for dividends"],nl_NL:["Adjust data for dividends"]}},68921:e=>{e.exports={en:["Adjust for contract changes"],nl_NL:["Adjust for contract changes"]}},42432:e=>{e.exports={en:["Go to"],nl_NL:["Go to"]}},92966:e=>{e.exports={en:["Extended Hours is available only for intraday charts"],nl_NL:["Extended Hours is available only for intraday charts"]}},61206:e=>{e.exports={en:["Maximize chart"],nl_NL:["Maximize chart"]}},2031:e=>{e.exports={en:["Main symbol data is adjusted for dividends only"],nl_NL:["Main symbol data is adjusted for dividends only"]}},95739:e=>{e.exports={en:["Main symbol data is adjusted for splits only"],nl_NL:["Main symbol data is adjusted for splits only"]}},27665:e=>{e.exports={en:["Sessions"],nl_NL:["Sessions"]}},31142:e=>{e.exports={en:["Restore chart"],nl_NL:["Restore chart"]}},41888:e=>{e.exports={en:["Toggle Auto Scale"],nl_NL:["Schakel autoschaal"]}},1e4:e=>{e.exports={en:["Toggle Log Scale"],nl_NL:["Schakel log schaal"]}},81649:e=>{e.exports={en:["Toggle Percentage"],nl_NL:["Schakel percentage"]}},77073:e=>{e.exports={en:["Timezone"],nl_NL:["Timezone"]}},49545:e=>{e.exports={en:["Use settlement as close on daily interval"],nl_NL:["Use settlement as close on daily interval"]}},8586:e=>{e.exports={en:["ext"],nl_NL:["ext"]}},63808:e=>{e.exports={en:["{str} day","{str} days"],nl_NL:["{str} day","{str} days"]}},62368:e=>{e.exports={en:["{str} day","{str} days"],nl_NL:["{str} day","{str} days"]}},561:e=>{e.exports={en:["{str} day intervals","{str} days intervals"],nl_NL:["{str} day intervals","{str} days intervals"]}},72495:e=>{e.exports={en:["{str} hour","{str} hours"],nl_NL:["{str} hour","{str} hours"]}},64963:e=>{e.exports={en:["{str} hour","{str} hours"],nl_NL:["{str} hour","{str} hours"]}},14887:e=>{e.exports={en:["{str} hour intervals","{str} hours intervals"],nl_NL:["{str} hour intervals","{str} hours intervals"]}},12752:e=>{e.exports={en:["{str} month","{str} months"],nl_NL:["{str} month","{str} months"]}},20062:e=>{e.exports={en:["{str} month","{str} months"],nl_NL:["{str} month","{str} months"]}},48514:e=>{e.exports={en:["{str} month intervals","{str} months intervals"],nl_NL:["{str} month intervals","{str} months intervals"]}},95484:e=>{e.exports={en:["{str} minute","{str} minutes"],nl_NL:["{str} minute","{str} minutes"]}},5926:e=>{e.exports={en:["{str} minute","{str} minutes"],nl_NL:["{str} minute","{str} minutes"]}},15489:e=>{e.exports={en:["{str} minute intervals","{str} minutes intervals"],
nl_NL:["{str} minute intervals","{str} minutes intervals"]}},6088:e=>{e.exports={en:["{str} week","{str} weeks"],nl_NL:["{str} week","{str} weeks"]}},49306:e=>{e.exports={en:["{str} week","{str} weeks"],nl_NL:["{str} week","{str} weeks"]}},60316:e=>{e.exports={en:["{str} week intervals","{str} weeks intervals"],nl_NL:["{str} week intervals","{str} weeks intervals"]}},96325:e=>{e.exports={en:["{str} year","{str} years"],nl_NL:["{str} year","{str} years"]}},91549:e=>{e.exports={en:["{str} year","{str} years"],nl_NL:["{str} year","{str} years"]}},78971:e=>{e.exports={en:["{str} year intervals","{str} years intervals"],nl_NL:["{str} year intervals","{str} years intervals"]}}}]);