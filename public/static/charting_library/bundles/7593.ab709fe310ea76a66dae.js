"use strict";(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[7593],{57717:(e,t,i)=>{i.r(t),i.d(t,{createPropertyPage:()=>n});var r=i(64147);function n(e,t,i,n=null){var o;const s={id:t,title:i,definitions:new r.WatchedValue(e.definitions),visible:null!==(o=e.visible)&&void 0!==o?o:new r.WatchedValue(!0).readonly()};return null!==n&&(s.icon=n),s}},52305:(e,t,i)=>{i.d(t,{convertToInt:()=>o,floor:()=>n,limitedPrecision:()=>s});var r=i(73866);function n(e){return Math.floor(e)}function o(e){return parseInt(String(e))}function s(e){const t=new r.LimitedPrecisionNumericFormatter(e,!0);return e=>{if(null===e)return e;const i=t.parse(t.format(e));return i.res?i.value:null}}},23351:(e,t,i)=>{i.d(t,{convertToDefinitionProperty:()=>o,makeProxyDefinitionProperty:()=>n});var r=i(51768);function n(e,t,i){const r=new Map,n=void 0!==t?t[0]:e=>e,o=void 0!==t?void 0!==t[1]?t[1]:t[0]:e=>e,s={value:()=>n(e.value()),setValue:t=>{e.setValue(o(t))},subscribe:(t,i)=>{const n=e=>{i(s)};r.set(i,n),e.subscribe(t,n)},unsubscribe:(t,i)=>{const n=r.get(i);n&&(e.unsubscribe(t,n),r.delete(i))},unsubscribeAll:t=>{e.unsubscribeAll(t),r.clear()},destroy:()=>{e.release(),null==i||i()}};return s}function o(e,t,i,o,s,p,l){const c=n(t.weakReference(),o,p),a=void 0!==o?void 0!==o[1]?o[1]:o[0]:e=>e,u=null!=s?s:r=>e.setProperty(t,a(r),i);return c.setValue=e=>{var t;l&&(0,r.trackEvent)(l.category,l.event,null===(t=l.label)||void 0===t?void 0:t.call(l,e)),u(e)},c}},59411:(e,t,i)=>{i.d(t,{createLinePropertyDefinition:()=>l});var r=i(49857),n=i(51056);const o=[n.LINESTYLE_SOLID,n.LINESTYLE_DOTTED,n.LINESTYLE_DASHED],s=[1,2,3,4],p=[r.LineEnd.Normal,r.LineEnd.Arrow];function l(e,t){const i={propType:"line",properties:e,...t};return void 0!==i.properties.style&&(i.styleValues=o),void 0!==i.properties.width&&(i.widthValues=s),void 0===i.properties.leftEnd&&void 0===i.properties.rightEnd||void 0!==i.endsValues||(i.endsValues=p),void 0!==i.properties.value&&void 0===i.valueType&&(i.valueType=1),i}},14139:(e,t,i)=>{function r(e,t){return{propType:"options",properties:e,...t}}i.d(t,{createOptionsPropertyDefinition:()=>r})},72491:(e,t,i)=>{function r(e,t){return{propType:"checkable",properties:e,...t}}function n(e,t,i){return{propType:"checkableSet",properties:e,childrenDefinitions:i,...t}}function o(e,t){return{propType:"color",properties:e,noAlpha:!1,...t}}i.d(t,{convertFromReadonlyWVToDefinitionProperty:()=>U,convertFromWVToDefinitionProperty:()=>j,convertToDefinitionProperty:()=>F.convertToDefinitionProperty,createCheckablePropertyDefinition:()=>r,createCheckableSetPropertyDefinition:()=>n,createColorPropertyDefinition:()=>o,createCoordinatesPropertyDefinition:()=>I,createEmojiPropertyDefinition:()=>W,createImagePropertyDefinition:()=>z,createLeveledLinePropertyDefinition:()=>a,createLinePropertyDefinition:()=>s.createLinePropertyDefinition,createNumberPropertyDefinition:()=>f,createOptionalTwoColorsPropertyDefinition:()=>E,createOptionsPropertyDefinition:()=>d.createOptionsPropertyDefinition,
createPropertyDefinitionsCheckableListOptionsGroup:()=>_,createPropertyDefinitionsGeneralGroup:()=>H,createPropertyDefinitionsLeveledLinesGroup:()=>Y,createRangePropertyDefinition:()=>O,createSelectionCoordinatesPropertyDefinition:()=>C,createSessionPropertyDefinition:()=>x,createStudyInputsPropertyDefinition:()=>M,createSymbolPropertyDefinition:()=>A,createTextPropertyDefinition:()=>S,createTransparencyPropertyDefinition:()=>R,createTwoColorsPropertyDefinition:()=>V,createTwoOptionsPropertyDefinition:()=>y,destroyDefinitions:()=>le,getColorDefinitionProperty:()=>$,getLockPriceScaleDefinitionProperty:()=>q,getPriceScaleSelectionStrategyDefinitionProperty:()=>B,getScaleRatioDefinitionProperty:()=>J,getSymbolDefinitionProperty:()=>ee,isCheckableListOptionsDefinition:()=>pe,isColorDefinition:()=>ne,isLineDefinition:()=>re,isOptionsDefinition:()=>se,isPropertyDefinition:()=>te,isPropertyDefinitionsGroup:()=>ie,isTwoColorDefinition:()=>oe,makeProxyDefinitionProperty:()=>F.makeProxyDefinitionProperty});var s=i(59411),p=i(51056);const l=[p.LINESTYLE_SOLID,p.LINESTYLE_DOTTED,p.LINESTYLE_DASHED],c=[1,2,3,4];function a(e,t){const i={propType:"leveledLine",properties:e,...t};return void 0!==i.properties.style&&(i.styleValues=l),void 0!==i.properties.width&&(i.widthValues=c),i}var u;function f(e,t){return{propType:"number",properties:e,type:1,...t}}!function(e){e[e.Integer=0]="Integer",e[e.Float=1]="Float"}(u||(u={}));var d=i(14139);function y(e,t){return{propType:"twoOptions",properties:e,...t}}var v,b,P,D=i(11542);!function(e){e.Top="bottom",e.Middle="middle",e.Bottom="top"}(v||(v={})),function(e){e.Left="left",e.Center="center",e.Right="right"}(b||(b={})),function(e){e.Horizontal="horizontal",e.Vertical="vertical"}(P||(P={}));const T=[{id:"bottom",value:"bottom",title:D.t(null,void 0,i(97118))},{id:"middle",value:"middle",title:D.t(null,void 0,i(68833))},{id:"top",value:"top",title:D.t(null,void 0,i(27567))}],m=[{id:"left",value:"left",title:D.t(null,void 0,i(11626))},{id:"center",value:"center",title:D.t(null,void 0,i(24197))},{id:"right",value:"right",title:D.t(null,void 0,i(50421))}],g=[{id:"horizontal",value:"horizontal",title:D.t(null,void 0,i(95406))},{id:"vertical",value:"vertical",title:D.t(null,void 0,i(69526))}],h=[10,11,12,14,16,20,24,28,32,40].map((e=>({title:String(e),value:e}))),w=[1,2,3,4],L=D.t(null,void 0,i(25485)),k=D.t(null,void 0,i(67781));function S(e,t){const i={propType:"text",properties:e,...t,isEditable:t.isEditable||!1};return void 0!==i.properties.size&&void 0===i.sizeItems&&(i.sizeItems=h),void 0!==i.properties.alignmentVertical&&void 0===i.alignmentVerticalItems&&(i.alignmentVerticalItems=T),void 0!==i.properties.alignmentHorizontal&&void 0===i.alignmentHorizontalItems&&(i.alignmentHorizontalItems=m),(i.alignmentVerticalItems||i.alignmentHorizontalItems)&&void 0===i.alignmentTitle&&(i.alignmentTitle=L),void 0!==i.properties.orientation&&(void 0===i.orientationItems&&(i.orientationItems=g),void 0===i.orientationTitle&&(i.orientationTitle=k)),
void 0!==i.properties.borderWidth&&void 0===i.borderWidthItems&&(i.borderWidthItems=w),i}function V(e,t){return{propType:"twoColors",properties:e,noAlpha1:!1,noAlpha2:!1,...t}}function E(e,t){return{propType:"optionalTwoColors",properties:e,noAlpha1:!1,noAlpha2:!1,...t}}function I(e,t){return{propType:"coordinates",properties:e,...t}}function C(e,t){return{propType:"selectionCoordinates",properties:e,...t}}function O(e,t){return{propType:"range",properties:e,...t}}function R(e,t){return{propType:"transparency",properties:e,...t}}function A(e,t){return{propType:"symbol",properties:e,...t}}function x(e,t){return{propType:"session",properties:e,...t}}function z(e,t){return{propType:"image",properties:e,...t}}function W(e,t){return{propType:"emoji",properties:e,...t}}function M(e,t){return{propType:"studyInputs",properties:e,...t}}var N=i(64147);function H(e,t,i,r){return{id:t,title:i,visible:r,groupType:"general",definitions:new N.WatchedValue(e)}}function _(e,t,i,r,n){return{id:i,title:r,visible:n,groupType:"checkableListOptions",definitions:new N.WatchedValue(t),checkableListOptions:e}}function Y(e,t,i){return{id:t,title:i,groupType:"leveledLines",definitions:new N.WatchedValue(e)}}var F=i(23351);function G(e,t,i){const r=new Map,n=void 0!==t?t[0]:e=>e,o=void 0!==t?void 0!==t[1]?t[1]:t[0]:e=>e,s={value:()=>n(e.value()),setValue:t=>{var i;null===(i=e.setValue)||void 0===i||i.call(e,o(t))},subscribe:(t,i)=>{const n=()=>{i(s)};let o=r.get(t);void 0===o?(o=new Map,o.set(i,n),r.set(t,o)):o.set(i,n),e.subscribe(n)},unsubscribe:(t,i)=>{const n=r.get(t);if(void 0!==n){const t=n.get(i);void 0!==t&&(e.unsubscribe(t),n.delete(i))}},unsubscribeAll:t=>{const i=r.get(t);void 0!==i&&(i.forEach(((t,i)=>{e.unsubscribe(t)})),i.clear())}};return i&&(s.destroy=()=>i()),s}function j(e,t,i,r){const n=G(t,r),o=void 0!==r?void 0!==r[1]?r[1]:r[0]:e=>e;return n.setValue=r=>e.setWatchedValue(t,o(r),i),n}function U(e,t){return function(e,t,i,r){const n=new Map,o={subscribe:(i,r)=>{const o=e=>i(t(e));n.set(i,o),e.subscribe(o,r)},unsubscribe:t=>{if(t){const i=n.get(t);i&&(e.unsubscribe(i),n.delete(t))}else n.clear(),e.unsubscribe()},value:()=>t(e.value())};return G(o,i,r)}(e,(e=>e),t,(()=>e.release()))}function B(e,t){const i=(0,F.makeProxyDefinitionProperty)(t.weakReference());return i.setValue=t=>e.setPriceScaleSelectionStrategy(t),i}function q(e,t,i,r){const n=(0,F.makeProxyDefinitionProperty)(t.weakReference());return n.setValue=t=>{const n={lockScale:t};e.setPriceScaleMode(n,i,r)},n}function J(e,t,i,r){const n=(0,F.makeProxyDefinitionProperty)(t.weakReference(),r);return n.setValue=r=>{e.setScaleRatioProperty(t,r,i)},n}var K=i(24377),Q=i(19063),X=i(99531);function Z(e,t){if((0,Q.isHexColor)(e)){const i=(0,K.parseRgb)(e);return(0,K.rgbaToString)((0,K.rgba)(i,(100-t)/100))}return e}function $(e,t,i,r,n){let o;if(null!==i){const e=(0,X.combineProperty)(Z,t.weakReference(),i.weakReference());o=(0,F.makeProxyDefinitionProperty)(e.ownership())}else o=(0,F.makeProxyDefinitionProperty)(t.weakReference(),[()=>Z(t.value(),0),e=>e])
;return o.setValue=i=>{n&&e.beginUndoMacro(r),e.setProperty(t,i,r),n&&e.endUndoMacro()},o}function ee(e,t,i,r,n,o){const s=[(p=i,l=t,e=>{const t=p(l);if(e===l.value()&&null!==t){const e=t.ticker||t.full_name;if(e)return e}return e}),e=>e];var p,l;const c=(0,F.convertToDefinitionProperty)(e,t,n,s);o&&(c.setValue=o);const a=new Map;c.subscribe=(e,i)=>{const r=e=>{i(c)};a.set(i,r),t.subscribe(e,r)},c.unsubscribe=(e,i)=>{const r=a.get(i);r&&(t.unsubscribe(e,r),a.delete(i))};const u={};return r.subscribe(u,(()=>{a.forEach(((e,t)=>{t(c)}))})),c.destroy=()=>{r.unsubscribeAll(u),a.clear()},c}function te(e){return e.hasOwnProperty("propType")}function ie(e){return e.hasOwnProperty("groupType")}function re(e){return"line"===e.propType}function ne(e){return"color"===e.propType}function oe(e){return"twoColors"===e.propType}function se(e){return"options"===e.propType}function pe(e){return"checkableListOptions"===e.groupType}function le(e){e.forEach((e=>{var t;if(te(e)){Object.keys(e.properties).forEach((t=>{const i=e.properties[t];void 0!==i&&void 0!==i.destroy&&i.destroy()}))}else le(e.definitions.value()),null===(t=e.visible)||void 0===t||t.destroy()}))}}}]);