(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[2364],{48473:e=>{e.exports={en:["Real-time data for {symbolName}"],fa:["Real-time data for {symbolName}"]}},84455:e=>{e.exports={en:["is provided by {exchange} exchange."],fa:["is provided by {exchange} exchange."]}},97349:e=>{e.exports={en:["Fr"],fa:["Fr"]}},30961:e=>{e.exports={en:["Mo"],fa:["Mo"]}},94748:e=>{e.exports={en:["Sa"],fa:["Sa"]}},75005:e=>{e.exports={en:["Su"],fa:["Su"]}},92578:e=>{e.exports={en:["We"],fa:["We"]}},8765:e=>{e.exports={en:["Th"],fa:["Th"]}},9135:e=>{e.exports={en:["Tu"],fa:["Tu"]}},43206:e=>{e.exports={en:["Could not get Pine source code."],fa:["Could not get Pine source code."]}},65495:e=>{e.exports={en:["Collapse pane"],fa:["Collapse pane"]}},81605:e=>{e.exports={en:["Confirm Remove Study Tree"],fa:["Confirm Remove Study Tree"]}},40225:e=>{e.exports={en:["Continuous futures contracts"],fa:["Continuous futures contracts"]}},78162:e=>{e.exports={en:["Continuous futures contracts are synthetic instruments combining individual contracts. The 1! contract represents the front-month (nearest expiration) while the 2! represents the second nearest expiration."],fa:["Continuous futures contracts are synthetic instruments combining individual contracts. The 1! contract represents the front-month (nearest expiration) while the 2! represents the second nearest expiration."]}},58796:e=>{e.exports={en:["Cboe One"],fa:["Cboe One"]}},29151:e=>{e.exports={en:["Change description"],fa:["Change description"]}},63245:e=>{e.exports={en:["Change symbol"],fa:["تغییر نماد"]}},45639:e=>{e.exports={en:["Chart values"],fa:["Chart values"]}},28214:e=>{e.exports={en:["Create a free account"],fa:["Create a free account"]}},53357:e=>{e.exports={en:["All's well — Market is open."],fa:["All's well — Market is open."]}},28896:e=>{e.exports={en:["April"],fa:["آوریل"]}},11081:e=>{e.exports={en:["August"],fa:["آگوست"]}},10842:e=>{e.exports={en:["Bar change values"],fa:["Bar Change Values"]}},70032:e=>{e.exports={en:["Buy real-time data"],fa:["Buy real-time data"]}},54480:e=>{e.exports={en:["Go to Editor"],fa:["Go to Editor"]}},77174:e=>{e.exports={en:["Do you really want to delete study and all of it's children?"],fa:["Do you really want to delete study and all of it's children?"]}},13930:e=>{e.exports={en:["Double click"],fa:["Double click"]}},78992:e=>{e.exports={en:["Data error"],fa:["Data error"]}},32925:e=>{e.exports={en:["Data is updated once a day."],fa:["Data is updated once a day."]}},33039:e=>{e.exports={en:["Data is updated once per second, even if there are more updates on the market."],fa:["Data is updated once per second, even if there are more updates on the market."]}},43348:e=>{e.exports={en:["Data is delayed"],fa:["Data is delayed"]}},38368:e=>{e.exports={en:["Data on our Basic plan is updated once per second, even if there are more updates on the market."],fa:["Data on our Basic plan is updated once per second, even if there are more updates on the market."]}},90082:e=>{e.exports={en:["December"],fa:["دسامبر"]}},66260:e=>{e.exports={
en:["Delete pane"],fa:["Delete pane"]}},54602:e=>{e.exports={en:["Delisted"],fa:["Delisted"]}},31683:e=>{e.exports={en:["Delisted alert"],fa:["Delisted alert"]}},50035:e=>{e.exports={en:["Derived Data"],fa:["Derived Data"]}},45321:e=>{e.exports={en:["Derived Data refers to financial indicators which are created by combining and/or processing raw data supplied from various sources."],fa:["Derived Data refers to financial indicators which are created by combining and/or processing raw data supplied from various sources."]}},5805:e=>{e.exports={en:["End of day data"],fa:["End of day data"]}},57335:e=>{e.exports={en:["Error"],fa:["خطا"]}},23302:e=>{e.exports={en:["Evening. Market is open for post-market trading."],fa:["Evening. Market is open for post-market trading."]}},63538:e=>{e.exports={en:["Exchange timezone"],fa:["Exchange timezone"]}},81069:e=>{e.exports={en:["February"],fa:["February"]}},5447:e=>{e.exports={en:["Fill out Exchange Agreements"],fa:["Fill out Exchange Agreements"]}},44454:e=>{e.exports={en:["Flag Symbol"],fa:["Flag Symbol"]}},22928:e=>{e.exports={en:["Fri"],fa:["Fri"]}},3570:e=>{e.exports={en:["Friday"],fa:["Friday"]}},87845:e=>{e.exports={en:["Holiday"],fa:["Holiday"]}},75119:e=>{e.exports={en:["Halal symbol"],fa:["Halal symbol"]}},44036:e=>{e.exports={en:["Indicator arguments"],fa:["Indicator Arguments"]}},7511:e=>{e.exports={en:["Indicator titles"],fa:["عنوان اندیکاتور"]}},51353:e=>{e.exports={en:["Indicator values"],fa:["مقادیر اندیکاتور"]}},95400:e=>{e.exports={en:["If you'd like {listedExchange} real-time data you'll need to complete an Exchange Agreement. Don't worry, it only takes a few clicks"],fa:["If you'd like {listedExchange} real-time data you'll need to complete an Exchange Agreement. Don't worry, it only takes a few clicks"]}},81509:e=>{e.exports={en:["It'll go to post-market trading in {remainingTime}."],fa:["It'll go to post-market trading in {remainingTime}."]}},58470:e=>{e.exports={en:["It'll open for pre-market trading in {remainingTime}."],fa:["It'll open for pre-market trading in {remainingTime}."]}},200:e=>{e.exports={en:["January"],fa:["January"]}},6608:e=>{e.exports={en:["July"],fa:["July"]}},61487:e=>{e.exports={en:["June"],fa:["June"]}},91006:e=>{e.exports={en:["One update per second"],fa:["One update per second"]}},37997:e=>{e.exports={en:["October"],fa:["October"]}},25765:e=>{e.exports={en:["Open market status"],fa:["Open market status"]}},54316:e=>{e.exports={en:["Opened in Editor"],fa:["Opened in Editor"]}},28632:e=>{e.exports={en:["Opened in detached Editor"],fa:["Opened in detached Editor"]}},72423:e=>{e.exports={en:["Last day change values"],fa:["Last day change values"]}},27741:e=>{e.exports={en:["Learn more"],fa:["Learn more"]}},74079:e=>{e.exports={en:["Move pane down"],fa:["Move pane down"]}},7310:e=>{e.exports={en:["Move pane up"],fa:["Move pane up"]}},37150:e=>{e.exports={en:["Mon"],fa:["Mon"]}},19573:e=>{e.exports={en:["Monday"],fa:["Monday"]}},37117:e=>{e.exports={en:["More"],fa:["بیشتر"]}},65420:e=>{e.exports={
en:["Morning. Market is open for pre-market trading."],fa:["Morning. Market is open for pre-market trading."]}},61206:e=>{e.exports={en:["Maximize chart"],fa:["Maximize chart"]}},90165:e=>{e.exports={en:["Maximize pane"],fa:["Maximize pane"]}},25734:e=>{e.exports={en:["May"],fa:["می"]}},75018:e=>{e.exports={en:["Manage panes"],fa:["Manage panes"]}},93878:e=>{e.exports={en:["March"],fa:["March"]}},80086:e=>{e.exports={en:["Market open"],fa:["Market open"]}},5371:e=>{e.exports={en:["Market opens in {remainingTime}."],fa:["Market opens in {remainingTime}."]}},62464:e=>{e.exports={en:["Market closed"],fa:["Market closed"]}},18643:e=>{e.exports={en:["Market closes in {remainingTime}."],fa:["Market closes in {remainingTime}."]}},41392:e=>{e.exports={en:["Market is currently on holiday. Lucky them."],fa:["Market is currently on holiday. Lucky them."]}},4607:e=>{e.exports={en:["November"],fa:["November"]}},87142:e=>{e.exports={en:["Source code"],fa:["Source code"]}},32273:e=>{e.exports={en:["Sat"],fa:["Sat"]}},30348:e=>{e.exports={en:["Saturday"],fa:["Saturday"]}},90761:e=>{e.exports={en:["Scroll to the left"],fa:["Scroll to the left"]}},83040:e=>{e.exports={en:["Scroll to the most recent bar"],fa:["Scroll to the most recent bar"]}},25131:e=>{e.exports={en:["Scroll to the right"],fa:["Scroll to the right"]}},32179:e=>{e.exports={en:["September"],fa:["September"]}},85786:e=>{e.exports={en:["Show Object Tree"],fa:["Show Object Tree"]}},74759:e=>{e.exports={en:["Show interval settings"],fa:["Show interval settings"]}},86158:e=>{e.exports={en:["Study Error"],fa:["Study Error"]}},77493:e=>{e.exports={en:["Sun"],fa:["Sun"]}},61480:e=>{e.exports={en:["Sunday"],fa:["Sunday"]}},23079:e=>{e.exports={en:["Symbol price source"],fa:["Symbol price source"]}},14771:e=>{e.exports={en:["Symbol title"],fa:["Symbol title"]}},44138:e=>{e.exports={en:["Synthetic symbol"],fa:["Synthetic symbol"]}},73897:e=>{e.exports={en:["Post-market"],fa:["Post-market"]}},85996:e=>{e.exports={en:["Paid plans feature faster data updates."],fa:["Paid plans feature faster data updates."]}},36018:e=>{e.exports={en:["Pre-market"],fa:["Pre-market"]}},94972:e=>{e.exports={en:["Primary listing"],fa:["Primary listing"]}},20987:e=>{e.exports={en:["Real-time data for this symbol is not supported right now. We may support it in the future."],fa:["Real-time data for this symbol is not supported right now. We may support it in the future."]}},31539:e=>{e.exports={en:["Real-time data for {symbolName} is provided by {exchange} exchange."],fa:["Real-time data for {symbolName} is provided by {exchange} exchange."]}},31142:e=>{e.exports={en:["Restore chart"],fa:["Restore chart"]}},12486:e=>{e.exports={en:["Restore pane"],fa:["Restore pane"]}},11532:e=>{e.exports={en:["Wed"],fa:["Wed"]}},94226:e=>{e.exports={en:["Wednesday"],fa:["Wednesday"]}},7281:e=>{e.exports={en:["To get real-time data for {description}, please buy the real-time data package."],fa:["To get real-time data for {description}, please buy the real-time data package."]}},71388:e=>{e.exports={en:["Thu"],fa:["Thu"]}},
79137:e=>{e.exports={en:["Thursday"],fa:["Thursday"]}},95246:e=>{e.exports={en:["The main, or first, stock exchange where a company's stock is listed and traded."],fa:["The main, or first, stock exchange where a company's stock is listed and traded."]}},25608:e=>{e.exports={en:["The source code of this script version is open in the Pine Editor."],fa:["The source code of this script version is open in the Pine Editor."]}},33161:e=>{e.exports={en:["The source code of this script version is open in the detached Pine Editor."],fa:["The source code of this script version is open in the detached Pine Editor."]}},24669:e=>{e.exports={en:["This data is real-time, but it may be slightly different to its official counterpart coming from primary exchanges."],fa:["This data is real-time, but it may be slightly different to its official counterpart coming from primary exchanges."]}},52668:e=>{e.exports={en:["This data is real-time, but it may be slightly different to its official counterpart coming from {exchange}."],fa:["This data is real-time, but it may be slightly different to its official counterpart coming from {exchange}."]}},67607:e=>{e.exports={en:["This is a shariah-compliant stock, meaning that it follows Islamic law. This company does not charge or receive interest, and does not engage with certain sectors (gambling, alcohol, tobacco, pork products)."],fa:["This is a shariah-compliant stock, meaning that it follows Islamic law. This company does not charge or receive interest, and does not engage with certain sectors (gambling, alcohol, tobacco, pork products)."]}},83556:e=>{e.exports={en:["This is no longer publicly trading so no new data will be added. But you can explore the historicals here."],fa:["This is no longer publicly trading so no new data will be added. But you can explore the historicals here."]}},44492:e=>{e.exports={en:["This real-time data is provided by {originalExchange} exchange. It may be slightly different from the official data directly from {exchange}. If this difference is crucial for you, you'll need to purchase real-time data from the primary exchange, which we can help with."],fa:["This real-time data is provided by {originalExchange} exchange. It may be slightly different from the official data directly from {exchange}. If this difference is crucial for you, you'll need to purchase real-time data from the primary exchange, which we can help with."]}},53272:e=>{e.exports={en:["This symbol doesn't exist, please pick another one."],fa:["This symbol doesn't exist, please pick another one."]}},90589:e=>{e.exports={en:["This symbol is calculated by TradingView using the rate from other exchanges."],fa:["This symbol is calculated by TradingView using the rate from other exchanges."]}},52176:e=>{e.exports={en:["Time for a walk — this market is closed."],fa:["Time for a walk — this market is closed."]}},11916:e=>{e.exports={en:["Tue"],fa:["Tue"]}},82160:e=>{e.exports={en:["Tuesday"],fa:["Tuesday"]}},13865:e=>{e.exports={en:["Unflag Symbol"],fa:["Unflag Symbol"]}},37644:e=>{e.exports={en:["Volume"],
fa:["حجم"]}},97038:e=>{e.exports={en:["Zoom in"],fa:["بزرگ نمایی"]}},88710:e=>{e.exports={en:["Zoom out"],fa:["کوچک نمایی"]}},96227:e=>{e.exports={en:["change open market status visibility"],fa:["change open market status visibility"]}},27426:e=>{e.exports={en:["change bar change visibility"],fa:["change bar change visibility"]}},79637:e=>{e.exports={en:["change chart values visibility"],fa:["change chart values visibility"]}},63050:e=>{e.exports={en:["change indicator titles visibility"],fa:["change indicator titles visibility"]}},49583:e=>{e.exports={en:["change indicator values visibility"],fa:["change indicator values visibility"]}},78310:e=>{e.exports={en:["change indicator arguments visibility"],fa:["change indicator arguments visibility"]}},66307:e=>{e.exports={en:["change last day change visibility"],fa:["change last day change visibility"]}},88167:e=>{e.exports={en:["change symbol description visibility"],fa:["change symbol description visibility"]}},12050:e=>{e.exports={en:["change symbol field visibility"],fa:["change symbol field visibility"]}},96201:e=>{e.exports={en:["change volume values visibility"],fa:["change volume values visibility"]}},59938:e=>{e.exports={en:["less than 1 minute"],fa:["less than 1 minute"]}},51382:e=>{e.exports={en:["show {title}"],fa:["show {title}"]}},51320:e=>{e.exports={en:["{days} and {hours}"],fa:["{days} and {hours}"]}},55154:e=>{e.exports={en:["{exchange} by {originalExchange}"],fa:["{exchange} by {originalExchange}"]}},83187:e=>{e.exports={en:["{hours} and {minutes}"],fa:["{hours} and {minutes}"]}},51211:e=>{e.exports={en:["{listedExchange} real-time data is available for free to registered users."],fa:["{listedExchange} real-time data is available for free to registered users."]}},89142:e=>{e.exports={en:["{symbolName} data is delayed by {time} minutes because of exchange requirements."],fa:["{symbolName} data is delayed by {time} minutes because of exchange requirements."]}},51931:e=>{e.exports={en:["Data is updated once every {amount} second, even if there are more updates on the market.","Data is updated once every {amount} seconds, even if there are more updates on the market."],fa:["Data is updated once every {amount} seconds, even if there are more updates on the market."]}},83978:e=>{e.exports={en:["Data on our Basic plan is updated once every {amount} second, even if there are more updates on the market.","Data on our Basic plan is updated once every {amount} seconds, even if there are more updates on the market."],fa:["Data on our Basic plan is updated once every {amount} seconds, even if there are more updates on the market."]}},46960:e=>{e.exports={en:["Hide indicator legend","Hide indicators legend"],fa:["Hide indicators legend"]}},36050:e=>{e.exports={en:["One update every {amount} second","One update every {amount} seconds"],fa:["One update every {amount} seconds"]}},36553:e=>{e.exports={en:["Show indicator legend","Show indicators legend"],fa:["Show indicators legend"]}},39501:e=>{e.exports={en:["{number} day","{number} days"],fa:["{number} days"]}},
44646:e=>{e.exports={en:["{number} hour","{number} hours"],fa:["{number} hours"]}},32547:e=>{e.exports={en:["{number} minute","{number} minutes"],fa:["{number} minutes"]}}}]);