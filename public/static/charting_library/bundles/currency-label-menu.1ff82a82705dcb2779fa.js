(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[2704],{36136:e=>{e.exports={"nav-button":"nav-button-znwuaSC1",link:"link-znwuaSC1",background:"background-znwuaSC1",icon:"icon-znwuaSC1","flip-icon":"flip-icon-znwuaSC1","size-large":"size-large-znwuaSC1","preserve-paddings":"preserve-paddings-znwuaSC1","size-medium":"size-medium-znwuaSC1","size-small":"size-small-znwuaSC1","size-xsmall":"size-xsmall-znwuaSC1","size-xxsmall":"size-xxsmall-znwuaSC1","visually-hidden":"visually-hidden-znwuaSC1"}},3196:e=>{e.exports={"tv-circle-logo":"tv-circle-logo-PsAlMQQF","tv-circle-logo--xxxsmall":"tv-circle-logo--xxxsmall-PsAlMQQF","tv-circle-logo--xxsmall":"tv-circle-logo--xxsmall-PsAlMQQF","tv-circle-logo--xsmall":"tv-circle-logo--xsmall-PsAlMQQF","tv-circle-logo--small":"tv-circle-logo--small-PsAlMQQF","tv-circle-logo--medium":"tv-circle-logo--medium-PsAlMQQF","tv-circle-logo--large":"tv-circle-logo--large-PsAlMQQF","tv-circle-logo--xlarge":"tv-circle-logo--xlarge-PsAlMQQF","tv-circle-logo--xxlarge":"tv-circle-logo--xxlarge-PsAlMQQF","tv-circle-logo--xxxlarge":"tv-circle-logo--xxxlarge-PsAlMQQF","tv-circle-logo--visually-hidden":"tv-circle-logo--visually-hidden-PsAlMQQF"}},20121:e=>{e.exports={dialog:"dialog-aRAWUDhF",rounded:"rounded-aRAWUDhF",shadowed:"shadowed-aRAWUDhF",fullscreen:"fullscreen-aRAWUDhF",darker:"darker-aRAWUDhF",backdrop:"backdrop-aRAWUDhF"}},53330:e=>{e.exports={"tablet-normal-breakpoint":"screen and (max-width: 768px)","small-height-breakpoint":"screen and (max-height: 360px)","tablet-small-breakpoint":"screen and (max-width: 430px)"}},8473:e=>{e.exports={dialog:"dialog-b8SxMnzX",wrapper:"wrapper-b8SxMnzX",separator:"separator-b8SxMnzX",bounded:"bounded-b8SxMnzX"}},80822:e=>{e.exports={"small-height-breakpoint":"screen and (max-height: 360px)",container:"container-BZKENkhT",unsetAlign:"unsetAlign-BZKENkhT",title:"title-BZKENkhT",subtitle:"subtitle-BZKENkhT",textWrap:"textWrap-BZKENkhT",ellipsis:"ellipsis-BZKENkhT",close:"close-BZKENkhT",icon:"icon-BZKENkhT"}},59086:e=>{e.exports={container:"container-qm7Rg5MB",mobile:"mobile-qm7Rg5MB",inputContainer:"inputContainer-qm7Rg5MB",withCancel:"withCancel-qm7Rg5MB",input:"input-qm7Rg5MB",icon:"icon-qm7Rg5MB",cancel:"cancel-qm7Rg5MB"}},6163:e=>{e.exports={scrollWrap:"scrollWrap-a62DpCum"}},66716:e=>{e.exports={container:"container-c8Hkfy8e",separator:"separator-c8Hkfy8e",section:"section-c8Hkfy8e"}},28205:e=>{e.exports={action:"action-peI7w0K1",hovered:"hovered-peI7w0K1",active:"active-peI7w0K1",label:"label-peI7w0K1",description:"description-peI7w0K1",selected:"selected-peI7w0K1",small:"small-peI7w0K1",withDescription:"withDescription-peI7w0K1",action__favoriteIcon:"action__favoriteIcon-peI7w0K1",action__favoriteIcon_active:"action__favoriteIcon_active-peI7w0K1",labelAndDescription:"labelAndDescription-peI7w0K1",icon:"icon-peI7w0K1",fakeIcon:"fakeIcon-peI7w0K1",highlighted:"highlighted-peI7w0K1"}},6419:e=>{e.exports={menu:"menu-kJ5smAAE",withDescriptions:"withDescriptions-kJ5smAAE",header:"header-kJ5smAAE",title:"title-kJ5smAAE",
container:"container-kJ5smAAE",icon:"icon-kJ5smAAE",clear:"clear-kJ5smAAE",input:"input-kJ5smAAE",highlighted:"highlighted-kJ5smAAE",active:"active-kJ5smAAE",section:"section-kJ5smAAE"}},52036:e=>{e.exports={"tablet-normal-breakpoint":"screen and (max-width: 768px)","tooltip-offset":"20px",dialog:"dialog-qyCw0PaN",dragging:"dragging-qyCw0PaN",mobile:"mobile-qyCw0PaN",fullscreen:"fullscreen-qyCw0PaN",dialogAnimatedAppearance:"dialogAnimatedAppearance-qyCw0PaN",dialogAnimation:"dialogAnimation-qyCw0PaN",dialogTooltip:"dialogTooltip-qyCw0PaN"}},98992:e=>{e.exports={favorite:"favorite-_FRQhM5Y",hovered:"hovered-_FRQhM5Y",disabled:"disabled-_FRQhM5Y",active:"active-_FRQhM5Y",checked:"checked-_FRQhM5Y"}},32248:e=>{e.exports={highlighted:"highlighted-cwp8YRo6"}},47625:e=>{e.exports={separator:"separator-Pf4rIzEt"}},71150:e=>{e.exports={separator:"separator-QjUlCDId",small:"small-QjUlCDId",normal:"normal-QjUlCDId",large:"large-QjUlCDId"}},62794:e=>{e.exports={icon:"icon-WB2y0EnP",dropped:"dropped-WB2y0EnP"}},57340:(e,t,i)=>{"use strict";i.d(t,{CloseButton:()=>h});var n=i(50959),o=i(64388),s=i(17105),r=i(15130),a=i(38822),l=i(63346),c=i(34983);function d(e="large"){switch(e){case"large":return s;case"medium":default:return r;case"small":return a;case"xsmall":return l;case"xxsmall":return c}}const h=n.forwardRef(((e,t)=>n.createElement(o.NavButton,{...e,ref:t,icon:d(e.size)})))},64388:(e,t,i)=>{"use strict";i.d(t,{NavButton:()=>c});var n=i(50959),o=i(97754),s=i(9745),r=(i(78572),i(36136));function a(e){const{size:t="large",preservePaddings:i,isLink:n,flipIconOnRtl:s,className:a}=e;return o(r["nav-button"],r[`size-${t}`],i&&r["preserve-paddings"],s&&r["flip-icon"],n&&r.link,a)}function l(e){const{children:t,icon:i}=e;return n.createElement(n.Fragment,null,n.createElement("span",{className:r.background}),n.createElement(s.Icon,{icon:i,className:r.icon,"aria-hidden":!0}),t&&n.createElement("span",{className:r["visually-hidden"]},t))}const c=(0,n.forwardRef)(((e,t)=>{const{icon:i,type:o="button",preservePaddings:s,flipIconOnRtl:r,size:c,"aria-label":d,...h}=e;return n.createElement("button",{...h,className:a({...e,children:d}),ref:t,type:o},n.createElement(l,{icon:i},d))}));c.displayName="NavButton";var d=i(21593),h=i(53017);(0,n.forwardRef)(((e,t)=>{const{icon:i,renderComponent:o,"aria-label":s,...r}=e,c=null!=o?o:d.CustomComponentDefaultLink;return n.createElement(c,{...r,className:a({...e,children:s,isLink:!0}),reference:(0,h.isomorphicRef)(t)},n.createElement(l,{icon:i},s))})).displayName="NavAnchorButton"},78572:(e,t,i)=>{"use strict";var n,o,s,r;!function(e){e.Primary="primary",e.QuietPrimary="quiet-primary",e.Secondary="secondary",e.Ghost="ghost"}(n||(n={})),function(e){e.XXSmall="xxsmall",e.XSmall="xsmall",e.Small="small",e.Medium="medium",e.Large="large",e.XLarge="xlarge",e.XXLarge="xxlarge"}(o||(o={})),function(e){e.Brand="brand",e.Gray="gray",e.LightGray="light-gray",e.Green="green",e.Red="red",e.Black="black",e.Gradient="gradient",e.BlackFriday="black-friday",e.CyberMonday="cyber-monday"}(s||(s={})),function(e){
e.Semibold18px="semibold18px",e.Semibold16px="semibold16px",e.Semibold14px="semibold14px",e.Medium16px="medium16px",e.Regular16px="regular16px",e.Regular14px="regular14px"}(r||(r={}))},53885:(e,t,i)=>{"use strict";i.d(t,{getStyleClasses:()=>r,isCircleLogoWithUrlProps:()=>a});var n=i(97754),o=i(3196),s=i.n(o);function r(e,t){return n(s()["tv-circle-logo"],s()[`tv-circle-logo--${e}`],t)}function a(e){return"logoUrl"in e&&null!==e.logoUrl&&void 0!==e.logoUrl&&0!==e.logoUrl.length}},38952:(e,t,i)=>{"use strict";function n(e){const{reference:t,...i}=e;return{...i,ref:t}}i.d(t,{renameRef:()=>n})},21593:(e,t,i)=>{"use strict";i.d(t,{CustomComponentDefaultLink:()=>s});var n=i(50959),o=i(38952);function s(e){return n.createElement("a",{...(0,o.renameRef)(e)})}n.PureComponent},43010:(e,t,i)=>{"use strict";i.d(t,{useIsomorphicLayoutEffect:()=>o});var n=i(50959);function o(e,t){("undefined"==typeof window?n.useEffect:n.useLayoutEffect)(e,t)}},27267:(e,t,i)=>{"use strict";function n(e,t,i,n,o){function s(o){if(e>o.timeStamp)return;const s=o.target;void 0!==i&&null!==t&&null!==s&&s.ownerDocument===n&&(t.contains(s)||i(o))}return o.click&&n.addEventListener("click",s,!1),o.mouseDown&&n.addEventListener("mousedown",s,!1),o.touchEnd&&n.addEventListener("touchend",s,!1),o.touchStart&&n.addEventListener("touchstart",s,!1),()=>{n.removeEventListener("click",s,!1),n.removeEventListener("mousedown",s,!1),n.removeEventListener("touchend",s,!1),n.removeEventListener("touchstart",s,!1)}}i.d(t,{addOutsideEventListener:()=>n})},36383:(e,t,i)=>{"use strict";i.d(t,{useOutsideEvent:()=>r});var n=i(50959),o=i(43010),s=i(27267);function r(e){const{click:t,mouseDown:i,touchEnd:r,touchStart:a,handler:l,reference:c}=e,d=(0,n.useRef)(null),h=(0,n.useRef)("undefined"==typeof window?0:new window.CustomEvent("timestamp").timeStamp);return(0,o.useIsomorphicLayoutEffect)((()=>{const e={click:t,mouseDown:i,touchEnd:r,touchStart:a},n=c?c.current:d.current;return(0,s.addOutsideEventListener)(h.current,n,l,document,e)}),[t,i,r,a,l]),c||d}},9745:(e,t,i)=>{"use strict";i.d(t,{Icon:()=>o});var n=i(50959);const o=n.forwardRef(((e,t)=>{const{icon:i="",title:o,ariaLabel:s,ariaLabelledby:r,ariaHidden:a,...l}=e,c=!!(o||s||r);return n.createElement("span",{...l,ref:t,role:"img","aria-label":s,"aria-labelledby":r,"aria-hidden":a||!c,title:o,dangerouslySetInnerHTML:{__html:i}})}))},99663:(e,t,i)=>{"use strict";i.d(t,{Slot:()=>o,SlotContext:()=>s});var n=i(50959);class o extends n.Component{shouldComponentUpdate(){return!1}render(){return n.createElement("div",{style:{position:"fixed",zIndex:150,left:0,top:0},ref:this.props.reference})}}const s=n.createContext(null)},90186:(e,t,i)=>{"use strict";function n(e){return s(e,r)}function o(e){return s(e,a)}function s(e,t){const i=Object.entries(e).filter(t),n={};for(const[e,t]of i)n[e]=t;return n}function r(e){const[t,i]=e;return 0===t.indexOf("data-")&&"string"==typeof i}function a(e){return 0===e[0].indexOf("aria-")}i.d(t,{filterAriaProps:()=>o,filterDataProps:()=>n,filterProps:()=>s,isAriaAttribute:()=>a,isDataAttribute:()=>r
})},53017:(e,t,i)=>{"use strict";function n(e){return t=>{e.forEach((e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)}))}}function o(e){return n([e])}i.d(t,{isomorphicRef:()=>o,mergeRefs:()=>n})},52778:(e,t,i)=>{"use strict";i.d(t,{OutsideEvent:()=>o});var n=i(36383);function o(e){const{children:t,...i}=e;return t((0,n.useOutsideEvent)(i))}},67961:(e,t,i)=>{"use strict";i.d(t,{OverlapManager:()=>s,getRootOverlapManager:()=>a});var n=i(50151);class o{constructor(){this._storage=[]}add(e){this._storage.push(e)}remove(e){this._storage=this._storage.filter((t=>e!==t))}has(e){return this._storage.includes(e)}getItems(){return this._storage}}class s{constructor(e=document){this._storage=new o,this._windows=new Map,this._index=0,this._document=e,this._container=e.createDocumentFragment()}setContainer(e){const t=this._container,i=null===e?this._document.createDocumentFragment():e;!function(e,t){Array.from(e.childNodes).forEach((e=>{e.nodeType===Node.ELEMENT_NODE&&t.appendChild(e)}))}(t,i),this._container=i}registerWindow(e){this._storage.has(e)||this._storage.add(e)}ensureWindow(e,t={position:"fixed",direction:"normal"}){const i=this._windows.get(e);if(void 0!==i)return i;this.registerWindow(e);const n=this._document.createElement("div");if(n.style.position=t.position,n.style.zIndex=this._index.toString(),n.dataset.id=e,void 0!==t.index){const e=this._container.childNodes.length;if(t.index>=e)this._container.appendChild(n);else if(t.index<=0)this._container.insertBefore(n,this._container.firstChild);else{const e=this._container.childNodes[t.index];this._container.insertBefore(n,e)}}else"reverse"===t.direction?this._container.insertBefore(n,this._container.firstChild):this._container.appendChild(n);return this._windows.set(e,n),++this._index,n}unregisterWindow(e){this._storage.remove(e);const t=this._windows.get(e);void 0!==t&&(null!==t.parentElement&&t.parentElement.removeChild(t),this._windows.delete(e))}getZindex(e){const t=this.ensureWindow(e);return parseInt(t.style.zIndex||"0")}moveToTop(e){if(this.getZindex(e)!==this._index){const t=this.ensureWindow(e);this._windows.forEach(((e,i)=>{e.hasAttribute("data-focus-trap")&&e.setAttribute("data-focus-trap",e===t?"true":"false")})),t.style.zIndex=(++this._index).toString()}}removeWindow(e){this.unregisterWindow(e)}}const r=new WeakMap;function a(e=document){const t=e.getElementById("overlap-manager-root");if(null!==t)return(0,n.ensureDefined)(r.get(t));{const t=new s(e),i=function(e){const t=e.createElement("div");return t.style.position="absolute",t.style.zIndex=150..toString(),t.style.top="0px",t.style.left="0px",t.id="overlap-manager-root",t}(e);return r.set(i,t),t.setContainer(i),e.body.appendChild(i),t}}var l;!function(e){e[e.BaseZindex=150]="BaseZindex"}(l||(l={}))},99054:(e,t,i)=>{"use strict";i.d(t,{setFixedBodyState:()=>c});const n=(()=>{let e;return()=>{var t;if(void 0===e){const i=document.createElement("div"),n=i.style;n.visibility="hidden",n.width="100px",n.msOverflowStyle="scrollbar",document.body.appendChild(i);const o=i.offsetWidth
;i.style.overflow="scroll";const s=document.createElement("div");s.style.width="100%",i.appendChild(s);const r=s.offsetWidth;null===(t=i.parentNode)||void 0===t||t.removeChild(i),e=o-r}return e}})();function o(e,t,i){null!==e&&e.style.setProperty(t,i)}function s(e,t){return getComputedStyle(e,null).getPropertyValue(t)}function r(e,t){return parseInt(s(e,t))}let a=0,l=!1;function c(e){const{body:t}=document,i=t.querySelector(".widgetbar-wrap");if(e&&1==++a){const e=s(t,"overflow"),a=r(t,"padding-right");"hidden"!==e.toLowerCase()&&t.scrollHeight>t.offsetHeight&&(o(i,"right",`${n()}px`),t.style.paddingRight=`${a+n()}px`,l=!0),t.classList.add("i-no-scroll")}else if(!e&&a>0&&0==--a&&(t.classList.remove("i-no-scroll"),l)){o(i,"right","0px");let e=0;0,t.scrollHeight<=t.clientHeight&&(e-=n()),t.style.paddingRight=(e<0?0:e)+"px",l=!1}}},24437:(e,t,i)=>{"use strict";i.d(t,{DialogBreakpoints:()=>o});var n=i(53330);const o={SmallHeight:n["small-height-breakpoint"],TabletSmall:n["tablet-small-breakpoint"],TabletNormal:n["tablet-normal-breakpoint"]}},79418:(e,t,i)=>{"use strict";i.d(t,{AdaptivePopupDialog:()=>k});var n=i(50959),o=i(50151),s=i(97754),r=i.n(s),a=i(68335),l=i(63273),c=i(35749),d=i(82206),h=i(1109),u=i(24437),p=i(90692),m=i(95711);var g=i(52092),f=i(76422),v=i(11542),_=i(57340);const x=n.createContext({setHideClose:()=>{}});var w=i(80822);function y(e){const{title:t,titleTextWrap:o=!1,subtitle:s,showCloseIcon:a=!0,onClose:l,onCloseButtonKeyDown:c,renderBefore:d,renderAfter:h,draggable:u,className:p,unsetAlign:m,closeAriaLabel:g=v.t(null,void 0,i(47742)),closeButtonReference:f}=e,[y,E]=(0,n.useState)(!1);return n.createElement(x.Provider,{value:{setHideClose:E}},n.createElement("div",{className:r()(w.container,p,(s||m)&&w.unsetAlign)},d,n.createElement("div",{"data-dragg-area":u,className:w.title},n.createElement("div",{className:r()(o?w.textWrap:w.ellipsis)},t),s&&n.createElement("div",{className:r()(w.ellipsis,w.subtitle)},s)),h,a&&!y&&n.createElement(_.CloseButton,{className:w.close,"data-name":"close","aria-label":g,onClick:l,onKeyDown:c,ref:f,size:"medium",preservePaddings:!0})))}var E=i(53017),C=i(90186),b=i(56570),S=i(8473);const D={vertical:20},A={vertical:0};class k extends n.PureComponent{constructor(){super(...arguments),this._controller=null,this._reference=null,this._orientationMediaQuery=null,this._embedResizerOverridesEnabled=b.enabled("embed_resizer_overrides"),this._renderChildren=(e,t)=>(this._controller=e,this.props.render({requestResize:this._requestResize,centerAndFit:this._centerAndFit,isSmallWidth:t})),this._handleReference=e=>this._reference=e,this._handleCloseBtnClick=()=>{this.props.onKeyboardClose&&this.props.onKeyboardClose(),this._handleClose()},this._handleClose=()=>{this.props.onClose()},this._handleOpen=()=>{void 0!==this.props.onOpen&&this.props.isOpened&&this.props.onOpen(this.props.fullScreen||window.matchMedia(u.DialogBreakpoints.TabletSmall).matches)},this._handleKeyDown=e=>{if(!e.defaultPrevented){if(this.props.onKeyDown&&this.props.onKeyDown(e),27===(0,a.hashFromEvent)(e)){
if(e.defaultPrevented)return;if(this.props.forceCloseOnEsc&&this.props.forceCloseOnEsc())return this.props.onKeyboardClose&&this.props.onKeyboardClose(),void this._handleClose();const{activeElement:i}=document;if(null!==i){if(e.preventDefault(),"true"===(t=i).getAttribute("data-haspopup")&&"true"!==t.getAttribute("data-expanded"))return void this._handleClose();const n=this._reference;if(null!==n&&(0,c.isTextEditingField)(i))return void n.focus();if(null==n?void 0:n.contains(i))return this.props.onKeyboardClose&&this.props.onKeyboardClose(),void this._handleClose()}}var t,i;(function(e){if("function"==typeof e)return e();return Boolean(e)})(this.props.disableTabNavigationContainment)||(i=e,[9,a.Modifiers.Shift+9].includes((0,a.hashFromEvent)(i))&&i.stopPropagation())}},this._requestResize=()=>{null!==this._controller&&this._controller.recalculateBounds()},this._centerAndFit=()=>{null!==this._controller&&this._controller.centerAndFit()},this._calculatePositionWithOffsets=(e,t)=>{const i=(0,o.ensureDefined)(this.props.fullScreenViewOffsets).value();return{top:i.top,left:(0,l.isRtl)()?-i.right:i.left,width:t.clientWidth-i.left-i.right,height:t.clientHeight-i.top-i.bottom}}}componentDidMount(){this.props.ignoreClosePopupsAndDialog||f.subscribe(g.CLOSE_POPUPS_AND_DIALOGS_COMMAND,this._handleClose,null),this._handleOpen(),void 0!==this.props.onOpen&&(this._orientationMediaQuery=window.matchMedia("(orientation: portrait)"),this._orientationMediaQuery.addEventListener("change",this._handleOpen)),this.props.fullScreenViewOffsets&&this.props.fullScreen&&this.props.fullScreenViewOffsets.subscribe(this._requestResize)}componentWillUnmount(){this.props.ignoreClosePopupsAndDialog||f.unsubscribe(g.CLOSE_POPUPS_AND_DIALOGS_COMMAND,this._handleClose,null),null!==this._orientationMediaQuery&&this._orientationMediaQuery.removeEventListener("change",this._handleOpen),this.props.fullScreenViewOffsets&&this.props.fullScreen&&this.props.fullScreenViewOffsets.unsubscribe(this._requestResize)}focus(){(0,o.ensureNotNull)(this._reference).focus()}getElement(){return this._reference}contains(e){var t,i;return null!==(i=null===(t=this._reference)||void 0===t?void 0:t.contains(e))&&void 0!==i&&i}render(){const{className:e,wrapperClassName:t,headerClassName:i,isOpened:o,title:s,titleTextWrap:a,dataName:l,onClickOutside:c,additionalElementPos:g,additionalHeaderElement:f,backdrop:v,shouldForceFocus:_=!0,shouldReturnFocus:x,onForceFocus:w,showSeparator:b,subtitle:k,draggable:M=!0,fullScreen:N=!1,showCloseIcon:F=!0,rounded:T=!0,isAnimationEnabled:B,growPoint:P,dialogTooltip:L,unsetHeaderAlign:I,onDragStart:z,dataDialogName:R,closeAriaLabel:O,containerAriaLabel:H,reference:U,containerTabIndex:W,closeButtonReference:K,onCloseButtonKeyDown:Q,shadowed:q,fullScreenViewOffsets:$,fixedBody:G,onClick:V}=this.props,X="after"!==g?f:void 0,Y="after"===g?f:void 0,Z="string"==typeof s?s:R||"",J=(0,C.filterDataProps)(this.props),j=(0,E.mergeRefs)([this._handleReference,U]);return n.createElement(p.MatchMedia,{rule:u.DialogBreakpoints.SmallHeight
},(g=>n.createElement(p.MatchMedia,{rule:u.DialogBreakpoints.TabletSmall},(u=>n.createElement(d.PopupDialog,{rounded:!(u||N)&&T,className:r()(S.dialog,N&&$&&S.bounded,e),isOpened:o,reference:j,onKeyDown:this._handleKeyDown,onClickOutside:c,onClickBackdrop:c,fullscreen:u||N,guard:g?A:D,boundByScreen:u||N,shouldForceFocus:_,onForceFocus:w,shouldReturnFocus:x,backdrop:v,draggable:M,isAnimationEnabled:B,growPoint:P,name:this.props.dataName,dialogTooltip:L,onDragStart:z,containerAriaLabel:H,containerTabIndex:W,calculateDialogPosition:N&&$?this._calculatePositionWithOffsets:void 0,shadowed:q,fixedBody:G,onClick:V,...J},n.createElement("div",{className:r()(S.wrapper,t),"data-name":l,"data-dialog-name":Z},void 0!==s&&n.createElement(y,{draggable:M&&!(u||N),onClose:this._handleCloseBtnClick,renderAfter:Y,renderBefore:X,subtitle:k,title:s,titleTextWrap:a,showCloseIcon:F,className:i,unsetAlign:I,closeAriaLabel:O,closeButtonReference:K,onCloseButtonKeyDown:Q}),b&&n.createElement(h.Separator,{className:S.separator}),n.createElement(m.PopupContext.Consumer,null,(e=>this._renderChildren(e,u||N)))))))))}}},69654:(e,t,i)=>{"use strict";i.d(t,{DialogSearch:()=>h});var n=i(50959),o=i(97754),s=i.n(o),r=i(11542),a=i(9745),l=i(69859),c=i(54313),d=i(59086);function h(e){const{children:t,isMobile:o,renderInput:h,onCancel:p,containerClassName:m,inputContainerClassName:g,iconClassName:f,cancelTitle:v=r.t(null,void 0,i(4543)),..._}=e;return n.createElement("div",{className:s()(d.container,o&&d.mobile,m)},n.createElement("div",{className:s()(d.inputContainer,o&&d.mobile,g,p&&d.withCancel)},h||n.createElement(u,{isMobile:o,..._})),t,n.createElement(a.Icon,{className:s()(d.icon,o&&d.mobile,f),icon:o?c:l}),p&&(!o||""!==_.value)&&n.createElement("div",{className:s()(d.cancel,o&&d.mobile),onClick:p},v))}function u(e){const{className:t,reference:i,isMobile:o,value:r,onChange:a,onFocus:l,onBlur:c,onKeyDown:h,onSelect:u,placeholder:p,activeDescendant:m,...g}=e;return n.createElement("input",{...g,ref:i,type:"text",className:s()(t,d.input,o&&d.mobile),autoComplete:"off","data-role":"search",placeholder:p,value:r,onChange:a,onFocus:l,onBlur:c,onSelect:u,onKeyDown:h,"aria-activedescendant":m})}},41153:(e,t,i)=>{"use strict";i.r(t),i.d(t,{UnitConversionRenderer:()=>K});var n=i(50959),o=i(32227),s=i(27267),r=i(52092),a=i(76422),l=i(49992),c=i(90692),d=i(19785),h=i(63651),u=i(98715),p=i(32470),m=i(24437),g=i(78135),f=i(97754),v=i.n(f),_=i(11542),x=i(9745),w=i(20520),y=i(27317),E=i(40173),C=i(11684),b=i(39706),S=i(36189),D=i(59695),A=i(24637),k=i(28205);const M=n.memo((function(e){const{label:t,icon:i,rules:o,search:s,description:r,onClick:a,onClose:l,isActive:c,isSmallSize:d,isSelected:h,selectedRef:u,hasDescriptions:p,hasIcons:m,isFavorite:g,onFavoriteClick:f}=e,_=(0,n.useCallback)((()=>{a(),l&&l()}),[a,l]),x=d&&k.small;return n.createElement("div",{className:v()(k.action,c&&k.active,x,p&&k.withDescription,h&&k.selected),onClick:_,ref:u},m&&(void 0!==i?n.createElement(D.CircleLogo,{logoUrl:i,size:p?"xsmall":"xxxsmall",className:v()(k.icon,x)
}):n.createElement("span",{className:v()(k.fakeIcon,x)})),n.createElement("div",{className:v()(k.labelAndDescription,x)},n.createElement("span",{className:v()(k.label,x)},w(t)),p&&n.createElement("br",null),p&&n.createElement("span",{className:v()(k.description,x)},r?w(r):"")),void 0!==g&&n.createElement("div",{className:v()(k.action__favoriteIcon,g&&k.action__favoriteIcon_active)},n.createElement(S.FavoriteButton,{isActive:c,isFilled:g,onClick:function(e){e.stopPropagation(),null==f||f()}})));function w(e){return n.createElement(A.HighlightedText,{text:e,rules:o,queryString:s,className:v()(c&&k.highlighted,c&&k.active)})}}),((e,t)=>Object.keys(t).filter((e=>!["onClick","onClose","onFavoriteClick"].includes(e))).every((i=>t[i]===e[i]))));var N=i(51417),F=i(69311),T=i(6419),B=i(6163);const P=(0,E.mergeThemes)(y.DEFAULT_MENU_THEME,B);function L(e){const{title:t,sections:o,onClose:s,selectedId:r,selectedRef:a,search:l,setSearch:c,items:d,rules:h,searchRef:u,hasDescriptions:p,hasIcons:m,...g}=e,[f,y]=(0,n.useState)((()=>o.reduce(((e,t,i)=>(t.name&&(e[t.id]=!0),e)),{})));function E(e){const{id:t,...i}=e;return n.createElement(M,{key:t,rules:h,search:l,onClose:s,isSmallSize:!0,isSelected:t===r,selectedRef:t===r?a:void 0,hasDescriptions:p,hasIcons:m,...i})}return n.createElement(w.PopupMenu,{...g,onClose:s,className:v()(T.menu,p&&T.withDescriptions),theme:P,maxHeight:p?313:280,noMomentumBasedScroll:!0,isOpened:!0,onOpen:function(){var e;null===(e=u.current)||void 0===e||e.focus()}},n.createElement("div",{className:T.header},n.createElement("div",{className:T.title},t),n.createElement("div",{className:T.container},n.createElement(x.Icon,{icon:N,className:T.icon}),n.createElement("input",{size:1,type:"text",className:T.input,placeholder:_.t(null,void 0,i(8573)),autoComplete:"off","data-role":"search",onChange:function(e){c(e.target.value)},value:l,ref:u}),Boolean(l)&&n.createElement(x.Icon,{icon:F,className:T.clear,onClick:function(){c("")}}))),l?d.map(E):o.map(((e,t)=>n.createElement(n.Fragment,{key:e.id},Boolean(t)&&n.createElement(C.PopupMenuSeparator,null),e.name?n.createElement(b.CollapsibleSection,{summary:e.name,className:T.section,open:f[e.id],onStateChange:t=>y({...f,[e.id]:t})},e.actions.map(E)):e.actions.map(E)))))}var I=i(79418),z=i(69654),R=i(66716);function O(e){const{title:t,onClose:o,sections:s,selectedId:r,selectedRef:a,search:l,setSearch:c,items:d,rules:h,searchRef:u,hasIcons:p,hasDescriptions:m}=e;return n.createElement(I.AdaptivePopupDialog,{title:t,onClose:o,render:function(){return n.createElement(n.Fragment,null,n.createElement(z.DialogSearch,{placeholder:_.t(null,void 0,i(8573)),onChange:g,reference:u}),n.createElement("div",{className:R.container},l?d.map((e=>{const{id:t,isActive:i,...s}=e;return n.createElement(M,{key:t,isActive:i,onClose:o,rules:h,search:l,isSelected:t===r,selectedRef:t===r?a:void 0,hasIcons:p,hasDescriptions:m,...s})})):s.map(((e,t)=>n.createElement(n.Fragment,{key:e.id},e.name&&n.createElement("div",{className:R.section},e.name),e.actions.map(((i,c)=>{
const{id:d,...u}=i,g=c===e.actions.length-1,f=t===s.length-1;return n.createElement(n.Fragment,{key:d},n.createElement(M,{rules:h,search:l,onClose:o,isSelected:d===r,selectedRef:d===r?a:void 0,hasIcons:p,hasDescriptions:m,...u}),!f&&g&&n.createElement("div",{className:R.separator}))})))))))},dataName:"unit-conversion-dialog",draggable:!1,fullScreen:!0,isOpened:!0});function g(e){c(e.target.value)}}const H={horizontalAttachEdge:g.HorizontalAttachEdge.Right,horizontalDropDirection:g.HorizontalDropDirection.FromRightToLeft};function U(e){const{element:t,...i}=e,[o,s]=(0,n.useState)(y()),[r,a]=(0,n.useState)(""),l=(0,n.useRef)(null),f=(0,n.useRef)(null),v=(0,n.useMemo)((()=>(0,d.createRegExpList)(r)),[r]),{activeIdx:_,setActiveIdx:x}=(0,h.useKeyboardNavigation)(l.current,o,(function(e){e&&(e.onClick(),i.onClose())}));(0,p.useResetActiveIdx)(x,[o]),(0,u.useScrollToRef)(f,_),(0,n.useEffect)((()=>{s(r?function(e,t,i){const n=e.reduce(((e,t)=>[...e,...t.actions]),[]);return(0,d.rankedSearch)({data:n,rules:i,queryString:t,primaryKey:"label",secondaryKey:"description"})}(i.sections,r,v):y())}),[r,i.sections,v]);const w=(0,n.useMemo)((()=>({selectedId:Boolean(_>=0&&o[_])?o[_].id:"",selectedRef:f,search:r,setSearch:a,searchRef:l,items:o,rules:v,hasIcons:o.some((e=>void 0!==e.icon)),hasDescriptions:o.some((e=>void 0!==e.description))})),[_,f,r,a,l,o,v]);return n.createElement(c.MatchMedia,{rule:m.DialogBreakpoints.TabletSmall},(e=>e?n.createElement(O,{...i,...w}):n.createElement(L,{...i,...w,position:(0,g.getPopupPositioner)(t,H),doNotCloseOn:t})));function y(){return i.sections.reduce(((e,t)=>(e.push(...t.actions),e)),[])}}function W(e,t,i,n,o){a.emit(e,!t,i,n,o)}class K{constructor(e,t,i,n,c,d){this._rootElem=document.createElement("div"),this._isOpened=!1,this._handlerOutsideClickDestroy=null,this._disableShowMenu=!0,this.close=()=>{var e;if(this._isOpened=!1,this._disableShowMenu){const t=this._trackEventParamsGetter();W(t[0],this._isOpened,t[1],t[2],t[3]),null===(e=this._handlerOutsideClickDestroy)||void 0===e||e.call(this)}a.unsubscribe(r.CLOSE_POPUPS_AND_DIALOGS_COMMAND,this.close,this),null!==this._rootElem&&(o.unmountComponentAtNode(this._rootElem),l.favoriteCurrencyUnitConversionService.getOnChange().unsubscribe(this,this._render),this._rootElem=null,this._menuClosedCallback())},this.isOpened=()=>this._isOpened,this._handlerOutsideClick=()=>{var e;null===(e=this._handlerOutsideClickDestroy)||void 0===e||e.call(this),this._handlerOutsideClickDestroy=(0,s.addOutsideEventListener)(new CustomEvent("timestamp").timeStamp,this._element,(()=>{var e;null===(e=this._handlerOutsideClickDestroy)||void 0===e||e.call(this),this.close()}),window.document,{touchEnd:!0,click:!0})},this._title=e,this._element=t,this._disableShowMenu=n,this._sectionsGetter=i,this._menuClosedCallback=()=>{this._isOpened=!1,d()},this._trackEventParamsGetter=c,this._render(),l.favoriteCurrencyUnitConversionService.getOnChange().subscribe(this,this._render),a.subscribe(r.CLOSE_POPUPS_AND_DIALOGS_COMMAND,this.close,this)}_render(){if(this._isOpened=!0,
this._disableShowMenu){const e=this._trackEventParamsGetter();return W(e[0],this._isOpened,e[1],e[2],e[3]),void this._handlerOutsideClick()}const e={title:this._title,sections:this._sectionsGetter(),element:this._element,onClose:this.close};o.render(n.createElement(U,{...e}),this._rootElem)}}},63651:(e,t,i)=>{"use strict";i.d(t,{useKeyboardNavigation:()=>s});var n=i(50959),o=i(68335);function s(e,t,i,s="keydown"){const[r,a]=(0,n.useState)(-1);return(0,n.useEffect)((()=>{if(!e)return;const i=e=>{switch((0,o.hashFromEvent)(e)){case 40:if(r===t.length-1)break;e.preventDefault(),a(r+1);break;case 38:if(r<=0)break;e.preventDefault(),a(r-1);break}};return e.addEventListener("keydown",i),()=>{e.removeEventListener("keydown",i)}}),[e,r,t]),(0,n.useEffect)((()=>{if(!e||!i)return;const n=e=>{var n;e.repeat||13===(0,o.hashFromEvent)(e)&&i(null!==(n=t[r])&&void 0!==n?n:null,e)};return e.addEventListener(s,n),()=>{e.removeEventListener(s,n)}}),[e,r,t,i,s]),{activeIdx:r,setActiveIdx:a}}},32470:(e,t,i)=>{"use strict";i.d(t,{useResetActiveIdx:()=>o});var n=i(50959);function o(e,t=[]){(0,n.useEffect)((()=>{e(-1)}),[...t])}},98715:(e,t,i)=>{"use strict";i.d(t,{useScrollToRef:()=>o});var n=i(50959);function o(e,t){(0,n.useEffect)((()=>{var i;t>=0&&(null===(i=e.current)||void 0===i||i.scrollIntoView({block:"nearest"}))}),[t])}},59695:(e,t,i)=>{"use strict";i.d(t,{CircleLogo:()=>a,hiddenCircleLogoClass:()=>r});var n=i(50959),o=i(53885),s=i(3196);const r=i.n(s)()["tv-circle-logo--visually-hidden"];function a(e){var t,i;const s=(0,o.getStyleClasses)(e.size,e.className),r=null!==(i=null!==(t=e.alt)&&void 0!==t?t:e.title)&&void 0!==i?i:"";return(0,o.isCircleLogoWithUrlProps)(e)?n.createElement("img",{className:s,crossOrigin:"",src:e.logoUrl,alt:r,title:e.title,loading:e.loading,"aria-label":e["aria-label"],"aria-hidden":e["aria-hidden"]}):n.createElement("span",{className:s,title:e.title,"aria-label":e["aria-label"],"aria-hidden":e["aria-hidden"]},e.placeholderLetter)}},95711:(e,t,i)=>{"use strict";i.d(t,{PopupContext:()=>n});const n=i(50959).createContext(null)},82206:(e,t,i)=>{"use strict";i.d(t,{PopupDialog:()=>P});var n=i(50959),o=i(97754),s=i(50151),r=i(99663),a=i(67961),l=i(90186),c=i(20121);class d extends n.PureComponent{constructor(){super(...arguments),this._manager=new a.OverlapManager,this._handleSlot=e=>{this._manager.setContainer(e)}}render(){const{rounded:e=!0,shadowed:t=!0,fullscreen:i=!1,darker:s=!1,className:a,backdrop:d,containerTabIndex:h=-1}=this.props,u=o(a,c.dialog,e&&c.rounded,t&&c.shadowed,i&&c.fullscreen,s&&c.darker),p=(0,l.filterDataProps)(this.props),m=this.props.style?{...this._createStyles(),...this.props.style}:this._createStyles();return n.createElement(n.Fragment,null,n.createElement(r.SlotContext.Provider,{value:this._manager},d&&n.createElement("div",{onClick:this.props.onClickBackdrop,className:c.backdrop}),n.createElement("div",{...p,className:u,style:m,ref:this.props.reference,onFocus:this.props.onFocus,onMouseDown:this.props.onMouseDown,onMouseUp:this.props.onMouseUp,onClick:this.props.onClick,
onKeyDown:this.props.onKeyDown,tabIndex:h,"aria-label":this.props.containerAriaLabel},this.props.children)),n.createElement(r.Slot,{reference:this._handleSlot}))}_createStyles(){const{bottom:e,left:t,width:i,right:n,top:o,zIndex:s,height:r}=this.props;return{bottom:e,left:t,right:n,top:o,zIndex:s,maxWidth:i,height:r}}}var h,u=i(86431),p=i(52778),m=i(9859),g=i(19291);function f(e,t,i,n){return e+t>n&&(e=n-t),e<i&&(e=i),e}function v(e){return{x:(0,m.clamp)(e.x,20,document.documentElement.clientWidth-20),y:(0,m.clamp)(e.y,20,window.innerHeight-20)}}function _(e){return{x:e.clientX,y:e.clientY}}function x(e){return{x:e.touches[0].clientX,y:e.touches[0].clientY}}!function(e){e[e.MouseGuardZone=20]="MouseGuardZone"}(h||(h={}));class w{constructor(e,t,i={boundByScreen:!0}){this._drag=null,this._canBeTouchClick=!1,this._frame=null,this._onMouseDragStart=e=>{if(0!==e.button||this._isTargetNoDraggable(e))return;e.preventDefault(),document.addEventListener("mousemove",this._onMouseDragMove),document.addEventListener("mouseup",this._onMouseDragEnd);const t=v(_(e));this._dragStart(t)},this._onTouchDragStart=e=>{if(this._isTargetNoDraggable(e))return;this._canBeTouchClick=!0,e.preventDefault(),this._header.addEventListener("touchmove",this._onTouchDragMove,{passive:!1});const t=v(x(e));this._dragStart(t)},this._onMouseDragEnd=e=>{e.target instanceof Node&&this._header.contains(e.target)&&e.preventDefault(),document.removeEventListener("mousemove",this._onMouseDragMove),document.removeEventListener("mouseup",this._onMouseDragEnd),this._onDragStop()},this._onTouchDragEnd=e=>{this._header.removeEventListener("touchmove",this._onTouchDragMove),this._onDragStop(),this._canBeTouchClick&&(this._canBeTouchClick=!1,function(e){if(e instanceof SVGElement){const t=document.createEvent("SVGEvents");t.initEvent("click",!0,!0),e.dispatchEvent(t)}e instanceof HTMLElement&&e.click()}(e.target))},this._onMouseDragMove=e=>{const t=v(_(e));this._dragMove(t)},this._onTouchDragMove=e=>{this._canBeTouchClick=!1,e.preventDefault();const t=v(x(e));this._dragMove(t)},this._onDragStop=()=>{this._drag=null,this._header.classList.remove("dragging"),this._options.onDragEnd&&this._options.onDragEnd()},this._dialog=e,this._header=t,this._options=i,this._header.addEventListener("mousedown",this._onMouseDragStart),this._header.addEventListener("touchstart",this._onTouchDragStart),this._header.addEventListener("touchend",this._onTouchDragEnd)}destroy(){null!==this._frame&&cancelAnimationFrame(this._frame),this._header.removeEventListener("mousedown",this._onMouseDragStart),document.removeEventListener("mouseup",this._onMouseDragEnd),this._header.removeEventListener("touchstart",this._onTouchDragStart),this._header.removeEventListener("touchend",this._onTouchDragEnd),document.removeEventListener("mouseleave",this._onMouseDragEnd)}updateOptions(e){this._options=e}_dragStart(e){const t=this._dialog.getBoundingClientRect();this._drag={startX:e.x,startY:e.y,finishX:e.x,finishY:e.y,dialogX:t.left,dialogY:t.top};const i=Math.round(t.left),n=Math.round(t.top)
;this._dialog.style.transform=`translate(${i}px, ${n}px)`,this._header.classList.add("dragging"),this._options.onDragStart&&this._options.onDragStart()}_dragMove(e){if(this._drag){if(this._drag.finishX=e.x,this._drag.finishY=e.y,null!==this._frame)return;this._frame=requestAnimationFrame((()=>{if(this._drag){const t=e.x-this._drag.startX,i=e.y-this._drag.startY;this._moveDialog(this._drag.dialogX+t,this._drag.dialogY+i)}this._frame=null}))}}_moveDialog(e,t){const i=this._dialog.getBoundingClientRect(),{boundByScreen:n}=this._options,o=f(e,i.width,n?0:-1/0,n?window.innerWidth:1/0),s=f(t,i.height,n?0:-1/0,n?window.innerHeight:1/0);this._dialog.style.transform=`translate(${Math.round(o)}px, ${Math.round(s)}px)`}_isTargetNoDraggable(e){return e.target instanceof Element&&null!==e.target.closest("[data-disable-drag]")}}const y={vertical:0};var E,C=i(42842),b=i(95711),S=i(99054),D=i(31955),A=i(92184);!function(e){e.Open="dialog-open",e.Close="dialog-close",e.FullscreenOn="dialog-fullscreen-on",e.FullscreenOff="dialog-fullscreen-off"}(E||(E={}));const k=(0,D.getLogger)("DialogEventDispatcher");class M{constructor(){this._openSessionId=null}dispatch(e){if("dialog-open"===e){if(null!==this._openSessionId)return void k.logError("Multiple calls to open dialog");this._openSessionId=(0,A.randomHash)()}null!==this._openSessionId?(window.dispatchEvent(new CustomEvent(e,{bubbles:!0,detail:{dialogSessionId:this._openSessionId}})),"dialog-close"===e&&(this._openSessionId=null)):k.logError("Empty open dialog session id")}}var N=i(84015),F=(i(56570),i(52036));F["tooltip-offset"];const T=class{constructor(e,t){this._frame=null,this._isFullscreen=!1,this._handleResize=()=>{null===this._frame&&(this._frame=requestAnimationFrame((()=>{this.recalculateBounds(),this._frame=null})))},this._dialog=e,this._guard=t.guard||y,this._calculateDialogPosition=t.calculateDialogPosition,this._initialHeight=e.style.height,window.addEventListener("resize",this._handleResize)}updateOptions(e){this._guard=e.guard||y,this._calculateDialogPosition=e.calculateDialogPosition}setFullscreen(e){this._isFullscreen!==e&&(this._isFullscreen=e,this.recalculateBounds())}centerAndFit(){const{x:e,y:t}=this.getDialogsTopLeftCoordinates(),i=this._calcAvailableHeight(),n=this._calcDialogHeight();if(i===n)if(this._calculateDialogPosition){const{left:e,top:t}=this._calculateDialogPosition(this._dialog,document.documentElement,this._guard);this._dialog.style.transform=`translate(${Math.round(e)}px, ${Math.round(t)}px)`}else this._dialog.style.height=n+"px";this._dialog.style.top="0px",this._dialog.style.left="0px",this._dialog.style.transform=`translate(${e}px, ${t}px)`}getDialogsTopLeftCoordinates(){const{clientWidth:e,clientHeight:t}=this._getClientDimensions(),i=this._calcDialogHeight(),n=e/2-this._dialog.clientWidth/2,o=t/2-i/2+this._getTopOffset();return{x:Math.round(n),y:Math.round(o)}}recalculateBounds(){var e
;const{clientWidth:t,clientHeight:i}=this._getClientDimensions(),{vertical:n}=this._guard,o=null===(e=this._calculateDialogPosition)||void 0===e?void 0:e.call(this,this._dialog,{clientWidth:t,clientHeight:i},{vertical:n});if(this._isFullscreen){if(this._dialog.style.top="0px",this._dialog.style.left="0px",this._dialog.style.width="100%",this._dialog.style.height="100%",this._dialog.style.transform="none",o){const{left:e,top:t,width:i,height:n}=o;this._dialog.style.transform=`translate(${Math.round(e)}px, ${Math.round(t)}px)`,i&&(this._dialog.style.width=`${i}px`,this._dialog.style.minWidth="unset"),n&&(this._dialog.style.height=`${n}px`,this._dialog.style.minHeight="unset")}}else if(o){const{left:e,top:t}=o;this._dialog.style.transform=`translate(${Math.round(e)}px, ${Math.round(t)}px)`}else{this._dialog.style.width="",this._dialog.style.height="";const e=this._dialog.getBoundingClientRect(),o=i-2*n,s=f(e.left,e.width,0,t),r=f(e.top,e.height,n,i);this._dialog.style.top="0px",this._dialog.style.left="0px",this._dialog.style.transform=`translate(${Math.round(s)}px, ${Math.round(r)}px)`,this._dialog.style.height=o<e.height?o+"px":this._initialHeight}}destroy(){window.removeEventListener("resize",this._handleResize),null!==this._frame&&(cancelAnimationFrame(this._frame),this._frame=null)}_getClientDimensions(){return{clientHeight:document.documentElement.clientHeight,clientWidth:document.documentElement.clientWidth}}_getTopOffset(){return 0}_calcDialogHeight(){const e=this._calcAvailableHeight();return e<this._dialog.clientHeight?e:this._dialog.clientHeight}_calcAvailableHeight(){return this._getClientDimensions().clientHeight-2*this._guard.vertical}};class B extends n.PureComponent{constructor(e){super(e),this._dialog=null,this._cleanUpFunctions=[],this._prevActiveElement=null,this._eventDispatcher=new M,this._handleDialogRef=e=>{const{reference:t}=this.props;this._dialog=e,"function"==typeof t&&t(e)},this._handleFocus=()=>{this._moveToTop()},this._handleMouseDown=e=>{this._moveToTop()},this._handleTouchStart=e=>{this._moveToTop()},this.state={canFitTooltip:!1},this._prevActiveElement=document.activeElement}render(){return n.createElement(b.PopupContext.Provider,{value:this},n.createElement(p.OutsideEvent,{mouseDown:!0,touchStart:!0,handler:this.props.onClickOutside},(e=>n.createElement("div",{ref:e,"data-outside-boundary-for":this.props.name,onFocus:this._handleFocus,onMouseDown:this._handleMouseDown,onTouchStart:this._handleTouchStart,"data-dialog-name":this.props["data-dialog-name"]},n.createElement(d,{style:this._applyAnimationCSSVariables(),...this.props,reference:this._handleDialogRef,className:o(F.dialog,(0,N.isOnMobileAppPage)("any")&&!this.props.noMobileAppShadows&&F.mobile,this.props.fullscreen&&F.fullscreen,this.props.className)},!1,this.props.children)))))}componentDidMount(){const{draggable:e,boundByScreen:t,onDragStart:i}=this.props,n=(0,s.ensureNotNull)(this._dialog);if(this._eventDispatcher.dispatch("dialog-open"),e){const e=n.querySelector("[data-dragg-area]");if(e&&e instanceof HTMLElement){
const o=new w(n,e,{boundByScreen:Boolean(t),onDragStart:i});this._cleanUpFunctions.push((()=>o.destroy())),this._drag=o}}this.props.autofocus&&!n.contains(document.activeElement)&&n.focus(),(this._isFullScreen()||this.props.fixedBody)&&(0,S.setFixedBodyState)(!0);const{guard:o,calculateDialogPosition:r}=this.props;if(this.props.resizeHandler)this._resize=this.props.resizeHandler;else{const e=new T(n,{guard:o,calculateDialogPosition:r});this._cleanUpFunctions.push((()=>e.destroy())),this._resize=e}if(this._isFullScreen()&&this._eventDispatcher.dispatch("dialog-fullscreen-on"),this.props.isAnimationEnabled&&this.props.growPoint&&this._applyAppearanceAnimation(this.props.growPoint),this.props.centeredOnMount&&this._resize.centerAndFit(),this._resize.setFullscreen(this._isFullScreen()),this.props.shouldForceFocus){if(this.props.onForceFocus)return void this.props.onForceFocus(n);n.focus()}if(!n.contains(document.activeElement)){const e=function(e){const t=e.querySelector("[autofocus]:not([disabled])");if(t)return t;if(e.tabIndex>=0)return e;const i=(0,g.getActiveElementSelectors)(),n=Array.from(e.querySelectorAll(i)).filter((0,g.createScopedVisibleElementFilter)(e));let o=Number.NEGATIVE_INFINITY,s=null;for(let e=0;e<n.length;e++){const t=n[e],i=t.getAttribute("tabindex");if(null!==i){const e=parseInt(i,10);!isNaN(e)&&e>o&&(o=e,s=t)}}return s}(n);e instanceof HTMLElement&&e.focus()}}componentDidUpdate(e){const t=e.fullscreen;if(this._resize){const{guard:e,calculateDialogPosition:t}=this.props;this._resize.updateOptions({guard:e,calculateDialogPosition:t}),this._resize.setFullscreen(this._isFullScreen())}if(this._drag&&this._drag.updateOptions({boundByScreen:Boolean(this.props.boundByScreen),onDragStart:this.props.onDragStart}),e.fullscreen!==this.props.fullscreen){const e=this.props.fullscreen;e&&!t?this._eventDispatcher.dispatch("dialog-fullscreen-on"):!e&&t&&this._eventDispatcher.dispatch("dialog-fullscreen-off")}}componentWillUnmount(){var e;if(this.props.shouldReturnFocus&&this._prevActiveElement&&document.body.contains(this._prevActiveElement)&&(null===document.activeElement||document.activeElement===document.body||(null===(e=this._dialog)||void 0===e?void 0:e.contains(document.activeElement))))try{setTimeout((()=>{this._prevActiveElement.focus({preventScroll:!0})}))}catch(e){}for(const e of this._cleanUpFunctions)e();(this._isFullScreen()||this.props.fixedBody)&&(0,S.setFixedBodyState)(!1),this._isFullScreen()&&this._eventDispatcher.dispatch("dialog-fullscreen-off"),this._eventDispatcher.dispatch("dialog-close")}focus(){this._dialog&&this._dialog.focus()}centerAndFit(){this._resize&&this._resize.centerAndFit()}recalculateBounds(){this._resize&&this._resize.recalculateBounds()}_moveToTop(){null!==this.context&&this.context.moveToTop()}_applyAnimationCSSVariables(){return{"--animationTranslateStartX":null,"--animationTranslateStartY":null,"--animationTranslateEndX":null,"--animationTranslateEndY":null}}_applyAppearanceAnimation(e){if(this._resize&&this._dialog){
const{x:t,y:i}=e,{x:n,y:o}=this._resize.getDialogsTopLeftCoordinates();this._dialog.style.setProperty("--animationTranslateStartX",`${t}px`),this._dialog.style.setProperty("--animationTranslateStartY",`${i}px`),this._dialog.style.setProperty("--animationTranslateEndX",`${n}px`),this._dialog.style.setProperty("--animationTranslateEndY",`${o}px`),this._dialog.classList.add(F.dialogAnimatedAppearance)}}_handleTooltipFit(){0}_isFullScreen(){return Boolean(this.props.fullscreen)}}B.contextType=C.PortalContext,B.defaultProps={boundByScreen:!0,draggable:!0,centeredOnMount:!0,shouldReturnFocus:!0};const P=(0,u.makeOverlapable)(B,!0)},36189:(e,t,i)=>{"use strict";i.d(t,{FavoriteButton:()=>h});var n=i(11542),o=i(50959),s=i(97754),r=i(9745),a=i(39146),l=i(48010),c=i(98992);const d={add:n.t(null,void 0,i(69207)),remove:n.t(null,void 0,i(85106))};function h(e){const{className:t,isFilled:i,isActive:n,onClick:h,...u}=e;return o.createElement(r.Icon,{...u,className:s(c.favorite,"apply-common-tooltip",i&&c.checked,n&&c.active,t),icon:i?a:l,onClick:h,title:i?d.remove:d.add})}},19785:(e,t,i)=>{"use strict";i.d(t,{createRegExpList:()=>r,getHighlightedChars:()=>a,rankedSearch:()=>s});var n,o=i(37265);function s(e){const{data:t,rules:i,queryString:n,isPreventedFromFiltering:s,primaryKey:r,secondaryKey:a=r,optionalPrimaryKey:l,tertiaryKey:c}=e;return t.map((e=>{const t=l&&e[l]?e[l]:e[r],s=e[a],d=c&&e[c];let h,u=0;return i.forEach((e=>{var i,r,a,l,c;const{re:p,fullMatch:m}=e;if(p.lastIndex=0,(0,o.isString)(t)&&t&&t.toLowerCase()===n.toLowerCase())return u=4,void(h=null===(i=t.match(m))||void 0===i?void 0:i.index);if((0,o.isString)(t)&&m.test(t))return u=3,void(h=null===(r=t.match(m))||void 0===r?void 0:r.index);if((0,o.isString)(s)&&m.test(s))return u=2,void(h=null===(a=s.match(m))||void 0===a?void 0:a.index);if((0,o.isString)(s)&&p.test(s))return u=2,void(h=null===(l=s.match(p))||void 0===l?void 0:l.index);if(Array.isArray(d))for(const e of d)if(m.test(e))return u=1,void(h=null===(c=e.match(m))||void 0===c?void 0:c.index)})),{matchPriority:u,matchIndex:h,item:e}})).filter((e=>s||e.matchPriority)).sort(((e,t)=>{if(e.matchPriority<t.matchPriority)return 1;if(e.matchPriority>t.matchPriority)return-1;if(e.matchPriority===t.matchPriority){if(void 0===e.matchIndex||void 0===t.matchIndex)return 0;if(e.matchIndex>t.matchIndex)return 1;if(e.matchIndex<t.matchIndex)return-1}return 0})).map((({item:e})=>e))}function r(e,t){const i=[],n=e.toLowerCase(),o=e.split("").map(((e,t)=>`(${0!==t?`[/\\s-]${l(e)}`:l(e)})`)).join("(.*?)")+"(.*)";return i.push({fullMatch:new RegExp(`(${l(e)})`,"i"),re:new RegExp(`^${o}`,"i"),reserveRe:new RegExp(o,"i"),fuzzyHighlight:!0}),t&&t.hasOwnProperty(n)&&i.push({fullMatch:t[n],re:t[n],fuzzyHighlight:!1}),i}function a(e,t,i){const n=[];return e&&i?(i.forEach((e=>{const{fullMatch:i,re:o,reserveRe:s}=e;i.lastIndex=0,o.lastIndex=0;const r=i.exec(t),a=r||o.exec(t)||s&&s.exec(t);if(e.fuzzyHighlight=!r,a)if(e.fuzzyHighlight){let e=a.index;for(let t=1;t<a.length;t++){const i=a[t],o=a[t].length;if(t%2){
const t=i.startsWith(" ")||i.startsWith("/")||i.startsWith("-");n[t?e+1:e]=!0}e+=o}}else for(let e=0;e<a[0].length;e++)n[a.index+e]=!0})),n):n}function l(e){return e.replace(/[!-/[-^{-}?]/g,"\\$&")}!function(e){e[e.Low=0]="Low",e[e.MediumLow=1]="MediumLow",e[e.Medium=2]="Medium",e[e.High=3]="High",e[e.Highest=4]="Highest"}(n||(n={}))},24637:(e,t,i)=>{"use strict";i.d(t,{HighlightedText:()=>a});var n=i(50959),o=i(97754),s=i(19785),r=i(32248);function a(e){const{queryString:t,rules:i,text:a,className:l}=e,c=(0,n.useMemo)((()=>(0,s.getHighlightedChars)(t,a,i)),[t,i,a]);return n.createElement(n.Fragment,null,c.length?a.split("").map(((e,t)=>n.createElement(n.Fragment,{key:t},c[t]?n.createElement("span",{className:o(r.highlighted,l)},e):n.createElement("span",null,e)))):a)}},1109:(e,t,i)=>{"use strict";i.d(t,{Separator:()=>r});var n=i(50959),o=i(97754),s=i(47625);function r(e){return n.createElement("div",{className:o(s.separator,e.className)})}},86431:(e,t,i)=>{"use strict";i.d(t,{makeOverlapable:()=>s});var n=i(50959),o=i(42842);function s(e,t){return class extends n.PureComponent{render(){const{isOpened:i,root:s}=this.props;if(!i)return null;const r=n.createElement(e,{...this.props,zIndex:150});return"parent"===s?r:n.createElement(o.Portal,{shouldTrapFocus:t},r)}}}},11684:(e,t,i)=>{"use strict";i.d(t,{PopupMenuSeparator:()=>l});var n,o=i(50959),s=i(97754),r=i.n(s),a=i(71150);function l(e){const{size:t="normal",className:i,ariaHidden:n=!1}=e;return o.createElement("div",{className:r()(a.separator,"small"===t&&a.small,"normal"===t&&a.normal,"large"===t&&a.large,i),role:"separator","aria-hidden":n})}!function(e){e.Small="small",e.Large="large",e.Normal="normal"}(n||(n={}))},42842:(e,t,i)=>{"use strict";i.d(t,{Portal:()=>l,PortalContext:()=>c});var n=i(50959),o=i(32227),s=i(55698),r=i(67961),a=i(99663);class l extends n.PureComponent{constructor(){super(...arguments),this._uuid=(0,s.nanoid)()}componentWillUnmount(){this._manager().removeWindow(this._uuid)}render(){const e=this._manager().ensureWindow(this._uuid,this.props.layerOptions);e.style.top=this.props.top||"",e.style.bottom=this.props.bottom||"",e.style.left=this.props.left||"",e.style.right=this.props.right||"",e.style.pointerEvents=this.props.pointerEvents||"";const t=this.props.className;return t&&("string"==typeof t?e.classList.add(t):e.classList.add(...t)),this.props.shouldTrapFocus&&!e.hasAttribute("data-focus-trap")&&e.setAttribute("data-focus-trap","true"),this.props["aria-hidden"]&&e.setAttribute("aria-hidden","true"),o.createPortal(n.createElement(c.Provider,{value:this},this.props.children),e)}moveToTop(){this._manager().moveToTop(this._uuid)}_manager(){return null===this.context?(0,r.getRootOverlapManager)():this.context}}l.contextType=a.SlotContext;const c=n.createContext(null)},10381:(e,t,i)=>{"use strict";i.d(t,{ToolWidgetCaret:()=>l});var n=i(50959),o=i(97754),s=i(9745),r=i(62794),a=i(578);function l(e){const{dropped:t,className:i}=e;return n.createElement(s.Icon,{className:o(i,r.icon,{[r.dropped]:t}),icon:a})}},40173:(e,t,i)=>{"use strict"
;function n(e,t,i={}){return Object.assign({},e,function(e,t,i={}){const n=Object.assign({},t);for(const o of Object.keys(t)){const s=i[o]||o;s in e&&(n[o]=[e[s],t[o]].join(" "))}return n}(e,t,i))}i.d(t,{mergeThemes:()=>n})},48967:e=>{e.exports={summary:"summary-ynHBVe1n",hovered:"hovered-ynHBVe1n",caret:"caret-ynHBVe1n"}},39706:(e,t,i)=>{"use strict";i.d(t,{CollapsibleSection:()=>l});var n=i(50959),o=i(97754),s=i.n(o),r=i(10381),a=i(48967);const l=(0,n.forwardRef)((function(e,t){const{open:i,summary:o,children:l,onStateChange:c,tabIndex:d,className:h,...u}=e;return n.createElement(n.Fragment,null,n.createElement("div",{...u,className:s()(h,a.summary),onClick:function(){c&&c(!i)},"data-open":i,ref:t,tabIndex:d},o,n.createElement(r.ToolWidgetCaret,{className:a.caret,dropped:Boolean(i)})),i&&l)}))},78135:(e,t,i)=>{"use strict";i.d(t,{HorizontalAttachEdge:()=>o,HorizontalDropDirection:()=>r,VerticalAttachEdge:()=>n,VerticalDropDirection:()=>s,getPopupPositioner:()=>c});var n,o,s,r,a=i(50151);!function(e){e[e.Top=0]="Top",e[e.Bottom=1]="Bottom",e[e.AutoStrict=2]="AutoStrict"}(n||(n={})),function(e){e[e.Left=0]="Left",e[e.Right=1]="Right"}(o||(o={})),function(e){e[e.FromTopToBottom=0]="FromTopToBottom",e[e.FromBottomToTop=1]="FromBottomToTop"}(s||(s={})),function(e){e[e.FromLeftToRight=0]="FromLeftToRight",e[e.FromRightToLeft=1]="FromRightToLeft"}(r||(r={}));const l={verticalAttachEdge:n.Bottom,horizontalAttachEdge:o.Left,verticalDropDirection:s.FromTopToBottom,horizontalDropDirection:r.FromLeftToRight,verticalMargin:0,horizontalMargin:0,matchButtonAndListboxWidths:!1};function c(e,t){return i=>{var c,d;const{contentWidth:h,contentHeight:u,availableHeight:p}=i,m=(0,a.ensureNotNull)(e).getBoundingClientRect(),{horizontalAttachEdge:g=l.horizontalAttachEdge,horizontalDropDirection:f=l.horizontalDropDirection,horizontalMargin:v=l.horizontalMargin,verticalMargin:_=l.verticalMargin,matchButtonAndListboxWidths:x=l.matchButtonAndListboxWidths}=t;let w=null!==(c=t.verticalAttachEdge)&&void 0!==c?c:l.verticalAttachEdge,y=null!==(d=t.verticalDropDirection)&&void 0!==d?d:l.verticalDropDirection;w===n.AutoStrict&&(p<m.y+m.height+_+u?(w=n.Top,y=s.FromBottomToTop):(w=n.Bottom,y=s.FromTopToBottom));const E=w===n.Top?-1*_:_,C=g===o.Right?m.right:m.left,b=w===n.Top?m.top:m.bottom,S={x:C-(f===r.FromRightToLeft?h:0)+v,y:b-(y===s.FromBottomToTop?u:0)+E};return x&&(S.overrideWidth=m.width),S}}},75556:(e,t,i)=>{"use strict";i.r(t),i.d(t,{currencyActions:()=>a});var n=i(50151),o=i(11542),s=i(8481),r=i(49992);function a(e,t,a){if(null===t||t.readOnly)return[];const l=[],c=t=>{e.setPriceScaleCurrency(a,t)},d=t.selectedCurrency,h=t.originalCurrencies,u=t.baseCurrencies,p=t.displayedValues,m=r.favoriteCurrencyUnitConversionService.get().currencies,g={id:"first_section",actions:[]};if(h.size>1){const e=(0,s.createAction)("Mixed",o.t(null,void 0,i(54215)),void 0,void 0,null===t.selectedCurrency,(()=>c(null)));g.actions.push(e)}const f=e.model().availableCurrencies();if(null!==d){const e=(0,n.ensureNotNull)(f.item(d)),t=(0,s.createAction)(d,(0,
n.ensureDefined)(p.get(d)),e.logoUrl,e.description,!0,(()=>{}),m.has(d),(()=>r.favoriteCurrencyUnitConversionService.toggle("currencies",d)));g.actions.push(t)}const v=f.filterConvertible(u,(e=>e!==d&&h.has(e)));for(const e of v){const i=(0,n.ensureNotNull)(f.item(e.id));g.actions.push((0,s.createAction)(e.id,e.code,i.logoUrl,i.description,t.selectedCurrency===e.id,(()=>c(e.id)),m.has(e.id),(()=>r.favoriteCurrencyUnitConversionService.toggle("currencies",e.id))))}g.actions.length>0&&l.push(g);const _=f.filterConvertible(u,(e=>e!==d&&!h.has(e))),x=[],w=[];for(const e of _){const i=(0,n.ensureNotNull)(f.item(e.id)),o=m.has(e.id),a=(0,s.createAction)(e.id,e.code,i.logoUrl,i.description,t.selectedCurrency===e.id,(()=>c(e.id)),o,(()=>r.favoriteCurrencyUnitConversionService.toggle("currencies",e.id)));o?x.push(a):w.push(a)}return(w.length>0||x.length>0)&&l.push({id:"second_section",actions:x.concat(w)}),l}},49992:(e,t,i)=>{"use strict";i.d(t,{favoriteCurrencyUnitConversionService:()=>a});var n=i(56840),o=i(21097),s=i(59063);class r extends s.AbstractJsonStoreService{constructor(e,t){super(e,t,"FAVORITE_CURRENCY_UNIT_CONVERSION_CHANGED","currencyUnitConversion.favorites",{currencies:new Set,units:new Set})}add(e,t){const i=this.get();i[e].add(t),this.set(i)}remove(e,t){const i=this.get();i[e].delete(t)&&this.set(i)}toggle(e,t){this.get()[e].has(t)?this.remove(e,t):this.add(e,t)}_serialize(e){return[[...e.currencies],[...e.units]]}_deserialize(e){return{currencies:new Set(e[0]),units:new Set(e[1])}}}const a=new r(o.TVXWindowEvents,n)},59363:(e,t,i)=>{"use strict";i.r(t),i.d(t,{unitActions:()=>a});var n=i(50151),o=i(11542),s=i(8481),r=i(49992);function a(e,t,a){if(null===t||0===t.availableGroups.size)return[];const l=[],c=t=>{e.setPriceScaleUnit(a,t)},d=t.selectedUnit,h=t.originalUnits,u=t.names,p=t.descriptions,m=r.favoriteCurrencyUnitConversionService.get().units,g={actions:[],id:"first_section"};if(h.size>1){const e=(0,s.createAction)("Mixed",o.t(null,void 0,i(54215)),void 0,void 0,null===t.selectedUnit,(()=>c(null)));g.actions.push(e)}const f=e.model().availableUnits();if(null!==d){const e=(0,s.createAction)(d,(0,n.ensureDefined)(u.get(d)),void 0,(0,n.ensureDefined)(p.get(d)),!0,(()=>{}),m.has(d),(()=>r.favoriteCurrencyUnitConversionService.toggle("units",d)));g.actions.push(e)}const v=f.unitsByGroups(t.availableGroups),_=[],x=[];for(const e of v)for(const t of e.units){const e=m.has(t.id);if(t.id===d||!e&&!h.has(t.id))continue;const i=(0,s.createAction)(t.id,t.name,void 0,t.description,!1,(()=>c(t.id)),e,(()=>r.favoriteCurrencyUnitConversionService.toggle("units",t.id)));e?x.push(i):_.push(i)}(_.length>0||x.length>0)&&g.actions.push(...x.sort(((e,t)=>e.label.toLowerCase().localeCompare(t.label.toLowerCase()))),..._),g.actions.length>0&&l.push(g);const w=d&&f.unitGroupById(d);if(null!==w)for(const e of v){if(e.name!==w)continue;const t=[];for(const i of e.units)i.id===d||h.has(i.id)||m.has(i.id)||t.push((0,
s.createAction)(i.id,i.name,void 0,i.description,!1,(()=>c(i.id)),!1,(()=>r.favoriteCurrencyUnitConversionService.toggle("units",i.id))));t.length>0&&l.push({id:e.name,name:e.name,actions:t})}for(const e of v){if(e.name===w)continue;const t=[];for(const i of e.units)i.id===d||h.has(i.id)||m.has(i.id)||t.push((0,s.createAction)(i.id,i.name,void 0,i.description,!1,(()=>c(i.id)),!1,(()=>r.favoriteCurrencyUnitConversionService.toggle("units",i.id))));t.length>0&&l.push({id:e.name,name:e.name,actions:t})}return l}},8481:(e,t,i)=>{"use strict";function n(e,t,i,n,o,s,r,a){return{id:e,label:t,icon:i,description:n,isActive:o,onClick:s,isFavorite:r,onFavoriteClick:a}}i.d(t,{createAction:()=>n})},51417:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18" fill="none"><path stroke="currentColor" d="M11.85 11.93A5.48 5.48 0 0 0 8 2.5a5.5 5.5 0 1 0 3.85 9.43zm0 0L16 16"/></svg>'},17105:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path stroke="currentColor" stroke-width="1.2" d="m1.5 1.5 21 21m0-21-21 21"/></svg>'},15130:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path stroke="currentColor" stroke-width="1.2" d="m1.5 1.5 15 15m0-15-15 15"/></svg>'},38822:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 14 14" width="14" height="14"><path stroke="currentColor" stroke-width="1.2" d="m1.5 1.5 11 11m0-11-11 11"/></svg>'},63346:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" width="12" height="12"><path stroke="currentColor" stroke-width="1.2" d="m1.5 1.5 9 9m0-9-9 9"/></svg>'},34983:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 10 10" width="10" height="10"><path stroke="currentColor" stroke-width="1.2" d="m1.5 1.5 7 7m0-7-7 7"/></svg>'},578:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 8" width="16" height="8"><path fill="currentColor" d="M0 1.475l7.396 6.04.596.485.593-.49L16 1.39 14.807 0 7.393 6.122 8.58 6.12 1.186.08z"/></svg>'},69311:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18" fill="none"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M9.7 9l4.65-4.65-.7-.7L9 8.29 4.35 3.65l-.7.7L8.29 9l-4.64 4.65.7.7L9 9.71l4.65 4.64.7-.7L9.71 9z"/></svg>'},69859:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none"><path stroke="currentColor" d="M12.4 12.5a7 7 0 1 0-4.9 2 7 7 0 0 0 4.9-2zm0 0l5.101 5"/></svg>'},39146:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18" fill="none"><path fill="currentColor" d="M9 1l2.35 4.76 5.26.77-3.8 3.7.9 5.24L9 13l-4.7 2.47.9-5.23-3.8-3.71 5.25-.77L9 1z"/></svg>'},48010:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18" fill="none"><path stroke="currentColor" d="M9 2.13l1.903 3.855.************** 4.255.618-3.079 3.001-.188.184.044.259.727 4.237-3.805-2L9 12.434l-.233.122-3.805 2.001.727-4.237.044-.26-.188-.183-3.079-3.001 4.255-.618.26-.038.116-.236L9 2.13z"/></svg>'},54313:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="M18.5 12.5a6 6 0 1 1-12 0 6 6 0 0 1 12 0Zm-1.25 5.8a7.5 7.5 0 1 1 1.06-1.06l4.22 4.23.53.53L22 23.06l-.53-.53-4.22-4.22Z"/></svg>'},55698:(e,t,i)=>{"use strict";i.d(t,{nanoid:()=>n});let n=(e=21)=>crypto.getRandomValues(new Uint8Array(e)).reduce(((e,t)=>e+=(t&=63)<36?t.toString(36):t<62?(t-26).toString(36).toUpperCase():t>62?"-":"_"),"")}}]);