(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[6780],{87075:e=>{e.exports={defaultsButtonText:"defaultsButtonText-aJgjxj2V",defaultsButtonItem:"defaultsButtonItem-aJgjxj2V",defaultsButtonIcon:"defaultsButtonIcon-aJgjxj2V"}},87509:e=>{e.exports={themesButtonText:"themesButtonText-AeBgp7zz",themesButtonIcon:"themesButtonIcon-AeBgp7zz",defaultsButtonText:"defaultsButtonText-AeBgp7zz",defaultsButtonItem:"defaultsButtonItem-AeBgp7zz"}},78746:e=>{e.exports={scrollable:"scrollable-Ycj0dUGE",tabs:"tabs-Ycj0dUGE"}},52088:e=>{e.exports={titleWrap:"titleWrap-e3jFxbHm",ellipsis:"ellipsis-e3jFxbHm",hideInput:"hideInput-e3jFxbHm",hideText:"hideText-e3jFxbHm",empty:"empty-e3jFxbHm",hideEmpty:"hideEmpty-e3jFxbHm",editIcon:"editIcon-e3jFxbHm"}},65119:e=>{e.exports={tabs:"tabs-xNPrJ8dY"}},456:e=>{e.exports={"link-item":"link-item-eIA09f0e"}},10399:e=>{e.exports={"arrow-icon":"arrow-icon-NIrWNOPk",dropped:"dropped-NIrWNOPk","size-xsmall":"size-xsmall-NIrWNOPk","size-small":"size-small-NIrWNOPk","size-medium":"size-medium-NIrWNOPk","size-large":"size-large-NIrWNOPk","size-xlarge":"size-xlarge-NIrWNOPk"}},24554:e=>{e.exports={"underline-tab":"underline-tab-cfYYXvwA","disable-focus-outline":"disable-focus-outline-cfYYXvwA","enable-cursor-pointer":"enable-cursor-pointer-cfYYXvwA",selected:"selected-cfYYXvwA","disable-active-state-styles":"disable-active-state-styles-cfYYXvwA","size-xsmall":"size-xsmall-cfYYXvwA","size-small":"size-small-cfYYXvwA","size-medium":"size-medium-cfYYXvwA","size-large":"size-large-cfYYXvwA","size-xlarge":"size-xlarge-cfYYXvwA",fake:"fake-cfYYXvwA","margin-xsmall":"margin-xsmall-cfYYXvwA","margin-small":"margin-small-cfYYXvwA","margin-medium":"margin-medium-cfYYXvwA","margin-large":"margin-large-cfYYXvwA","margin-xlarge":"margin-xlarge-cfYYXvwA",collapse:"collapse-cfYYXvwA","ellipsis-children":"ellipsis-children-cfYYXvwA"}},7633:e=>{e.exports={"scroll-wrap":"scroll-wrap-SmxgjhBJ","size-xlarge":"size-xlarge-SmxgjhBJ","enable-scroll":"enable-scroll-SmxgjhBJ","underline-tabs":"underline-tabs-SmxgjhBJ","size-large":"size-large-SmxgjhBJ","size-medium":"size-medium-SmxgjhBJ","size-small":"size-small-SmxgjhBJ","size-xsmall":"size-xsmall-SmxgjhBJ","make-grid-column":"make-grid-column-SmxgjhBJ","stretch-tabs":"stretch-tabs-SmxgjhBJ","equal-tab-size":"equal-tab-size-SmxgjhBJ"}},29662:e=>{e.exports={underline:"underline-Pun8HxCz",center:"center-Pun8HxCz",corner:"corner-Pun8HxCz",disabled:"disabled-Pun8HxCz"}},38546:(e,t,i)=>{"use strict";i.d(t,{DialogTabs:()=>a});var s=i(50959),n=i(93081);const a=s.forwardRef((function(e,t){const{id:i,tabs:a,activeTab:l,onChange:o,className:r}=e;return s.createElement("div",{className:r,ref:t},s.createElement(n.UnderlineButtonTabs,{id:i,items:a,isActive:function(e){return e.id===l},onActivate:function(e){o(e.id)},overflowBehaviour:"scroll"}))}))},78890:(e,t,i)=>{"use strict";i.d(t,{PropertyActions:()=>p});var s=i(50959),n=i(97754),a=i.n(n),l=i(9745),o=i(11542),r=i(95276),c=i(16396),u=i(44996),d=i(87075);const m={reset:o.t(null,void 0,i(33533)),
saveAsDefault:o.t(null,void 0,i(99687)),defaults:o.t(null,void 0,i(48572))};var h;!function(e){e.Normal="normal",e.Compact="compact"}(h||(h={}));class p extends s.PureComponent{render(){const{mode:e,saveAsDefaults:t,resetToDefaults:i}=this.props;return s.createElement(r.ControlDisclosure,{id:"property-actions",className:a()("normal"===e&&d.defaultsButtonText),hideArrowButton:"compact"===e,buttonChildren:this._getPlaceHolderItem("compact"===e)},s.createElement(c.PopupMenuItem,{className:d.defaultsButtonItem,isActive:!1,label:m.reset,onClick:i}),s.createElement(c.PopupMenuItem,{className:d.defaultsButtonItem,isActive:!1,label:m.saveAsDefault,onClick:t}))}_getPlaceHolderItem(e){return e?s.createElement(l.Icon,{className:d.defaultsButtonIcon,icon:u}):m.defaults}}},48531:(e,t,i)=>{"use strict";i.d(t,{FooterMenu:()=>C});var s=i(50959),n=i(11542),a=i(9745),l=i(95276),o=i(90692),r=i(87509),c=i(44996);function u(e){return e.isTabletWidth?s.createElement(a.Icon,{className:r.themesButtonIcon,icon:c}):s.createElement(s.Fragment,null,n.t(null,void 0,i(93553)))}function d(e){return s.createElement(o.MatchMedia,{rule:"screen and (max-width: 768px)"},(t=>s.createElement(l.ControlDisclosure,{className:!t&&r.themesButtonText,hideArrowButton:t,buttonChildren:s.createElement(u,{isTabletWidth:t})},e.children)))}var m=i(16396),h=i(96040),p=i(70412),g=i(32563),b=i(60925);function v(e){const{name:t,onRemove:i,onClick:n}=e,[a,l]=(0,p.useHover)(),o=s.useCallback((()=>n(t)),[n,t]),c=s.useCallback((()=>{i&&i(t)}),[i,t]);return s.createElement("div",{...l},s.createElement(m.PopupMenuItem,{className:r.defaultsButtonItem,isActive:!1,label:t,onClick:o,toolbox:i&&s.createElement(h.RemoveButton,{hidden:!g.mobiletouch&&!a,onClick:c,icon:b})}))}function f(e){return s.createElement(d,null,s.createElement(v,{onClick:function(){const{sources:t,chartUndoModel:i}=e;i.restoreLineToolsFactoryDefaults(t)},name:n.t(null,void 0,i(62511))}))}function C(e){return s.createElement(f,{...e})}},37289:(e,t,i)=>{"use strict";i.d(t,{PropertiesEditorTab:()=>c});var s=i(50959),n=i(66849);const a={"Elliott Impulse Wave (12345)Degree":"normal","Elliott Triangle Wave (ABCDE)Degree":"normal","Elliott Triple Combo Wave (WXYXZ)Degree":"normal","Elliott Correction Wave (ABC)Degree":"normal","Elliott Double Combo Wave (WXY)Degree":"normal",BarsPatternMode:"normal",StudyInputSource:"normal"},l={TextText:"big",AnchoredTextText:"big",NoteText:"big",AnchoredNoteText:"big",CalloutText:"big",BalloonText:"big"};var o=i(71891),r=i(62709);function c(e){const{page:t,pageRef:i,tableKey:c}=e;return s.createElement(n.ControlCustomHeightContext.Provider,{value:l},s.createElement(n.ControlCustomWidthContext.Provider,{value:a},t&&s.createElement(o.PropertyTable,{reference:i,key:c},t.definitions.value().map((e=>s.createElement(r.Section,{key:e.id,definition:e}))))))}},75892:(e,t,i)=>{"use strict";i.r(t),i.d(t,{SourcePropertiesEditorRenderer:()=>R})
;var s=i(50959),n=i(76422),a=i(50151),l=i(49483),o=i(56840),r=i.n(o),c=i(11542),u=i(50182),d=i(59064),m=i(48531),h=i(37289),p=i(86656),g=i(19466),b=i(630),v=i(72708),f=i(32549),C=i(29875),_=i(67317),x=i(85719),S=i(90692),T=i(45126),w=i(38546),y=i(78890),E=i(68495),I=i(56570),A=i(78746);const P=new T.TranslatedString("change {sourceTitle} title to {newSourceTitle}",c.t(null,void 0,i(23687)));class k extends s.PureComponent{constructor(e){super(e),this._activePageRef=s.createRef(),this._handleChangeMode=e=>{this.setState({isRenaming:e})},this._getTranslatedStringForSource=e=>new T.TranslatedString(e.name(),e.title(g.TitleDisplayTarget.StatusLine)),this._setTitle=e=>{const{source:t,model:i}=this.props,s=P.format({sourceTitle:t.properties().title.value()||this._getTranslatedStringForSource(t),newSourceTitle:e});i.setProperty(t.properties().title,e,s,x.lineToolsDoNotAffectChartInvalidation&&(0,b.isLineTool)(t))},this._getActionPageById=e=>{if(!e)return;const{pages:t}=this.props;return t.find((t=>t.id.toLowerCase()===e.toLowerCase()))},this._onChangeActivePageDefinitions=()=>{this.setState({tableKey:Date.now()},(()=>{this._requestResize&&this._requestResize()}))},this._handleResetToDefaults=()=>{const{source:e,model:t}=this.props;(0,v.isStudy)(e)&&t.restorePropertiesForSource(e)},this._handleSaveAsDefaults=()=>{const{source:e}=this.props;(0,v.isStudy)(e)&&e.properties().saveDefaults()},this._renderFooterLeft=()=>{const{source:e,model:t}=this.props;return(0,b.isLineTool)(e)?s.createElement(m.FooterMenu,{sources:[e],chartUndoModel:t}):s.createElement(S.MatchMedia,{rule:"screen and (max-width: 430px)"},(t=>(0,v.isStudy)(e)&&s.createElement(y.PropertyActions,{saveAsDefaults:this._handleSaveAsDefaults,resetToDefaults:this._handleResetToDefaults,mode:t?"compact":"normal"})))},this._subscribe=e=>{e&&e.definitions.subscribe(this._onChangeActivePageDefinitions)},this._unsubscribe=e=>{e&&e.definitions.unsubscribe(this._onChangeActivePageDefinitions)},this._getActiveTabSettingsName=()=>{const{source:e}=this.props;return e instanceof f.Series?"properties_dialog.active_tab.chart":e instanceof C.LineDataSource?"properties_dialog.active_tab.drawing":e instanceof _.Study?"properties_dialog.active_tab.study":""},this._handleSelectPage=e=>{const{activePageId:t}=this.state,i=this._getActionPageById(t),s=this._getActionPageById(e),n=this._getActiveTabSettingsName();t!==e&&(this._unsubscribe(i),n&&r().setValue(n,e),this._subscribe(s),this.setState({activePageId:e,tableKey:Date.now()},(()=>{this._requestResize&&this._requestResize(),this._focusActivePageFirstTextInput()})))},this._handleScroll=()=>{d.globalCloseDelegate.fire()},this._handleSubmit=()=>{this.props.onSubmit(),this._closePopupDialog()},this._closePopupDialog=()=>{window.lineToolPropertiesToolbar&&window.lineToolPropertiesToolbar.refresh(),this.props.onClose()};const{pages:t}=this.props;let i;if(this._getActionPageById(this.props.activePageId))i=(0,a.ensureDefined)(this.props.activePageId);else{const e=r().getValue(this._getActiveTabSettingsName(),""),s=this._getActionPageById(e)
;i=s?s.id:t[0].id}this.state={activePageId:i,tableKey:Date.now(),isRenaming:!1},window.lineToolPropertiesToolbar&&window.lineToolPropertiesToolbar.hide()}componentDidMount(){const{activePageId:e}=this.state,t=this._getActionPageById(e);this._focusActivePageFirstTextInput(),this._subscribe(t)}componentWillUnmount(){const{activePageId:e}=this.props,t=this._getActionPageById(e);clearTimeout(this._timeout),this._unsubscribe(t)}render(){var e;const{onCancel:t,source:i}=this.props,{activePageId:n,isRenaming:a}=this.state,l=(null===(e=i.properties().title)||void 0===e?void 0:e.value())||i.title(g.TitleDisplayTarget.StatusLine),o=s.createElement(E.Title,{isRenaming:a,onChangeMode:this._handleChangeMode,setTitle:this._setTitle,defaultTitle:l,canBeRenamed:(0,b.isLineTool)(i)&&!I.enabled("widget")});return s.createElement(u.AdaptiveConfirmDialog,{dataName:(0,v.isStudy)(i)?"indicator-properties-dialog":"source-properties-editor",dataDialogName:l,title:o,isOpened:!0,onSubmit:this._handleSubmit,onCancel:t,onClickOutside:this._handleSubmit,onClose:this._closePopupDialog,footerLeftRenderer:this._renderFooterLeft,render:this._renderChildren(n),submitOnEnterKey:!1,showCloseIcon:!a})}_renderChildren(e){return({requestResize:t})=>{this._requestResize=t;const{pages:i,source:n}=this.props,a=i.find((t=>t.id===e)),l=(0,v.isStudy)(n)?"indicator-properties-dialog-tabs":"source-properties-editor-tabs",o=i.map((({title:e,id:t})=>({label:e,id:t,dataId:`${l}-${t}`})));return s.createElement(s.Fragment,null,s.createElement(w.DialogTabs,{className:A.tabs,id:l,activeTab:e,onChange:this._handleSelectPage,tabs:o}),s.createElement(p.TouchScrollContainer,{className:A.scrollable,onScroll:this._handleScroll},s.createElement(h.PropertiesEditorTab,{page:a,pageRef:this._activePageRef,tableKey:this.state.tableKey})))}}_focusActivePageFirstTextInput(){if(!l.CheckMobile.any()&&this._activePageRef.current){const e=this._activePageRef.current.querySelector("input[type=text],textarea");e&&(this._timeout=setTimeout((()=>{e.focus()}),0))}}}var B=i(29280),z=i(28124);class R extends B.DialogRenderer{constructor(e){super(),this._timeout=null,this._handleClose=()=>{var e;null===(e=this._rootInstance)||void 0===e||e.unmount(),this._setVisibility(!1),this._onClose&&this._onClose(),this._subscription.unsubscribe(this,this._handleCollectionChanged)},this._handleSubmit=()=>{const e=this._source;(0,b.isLineTool)(e)&&e.hasAlert().value()&&setTimeout((()=>{e.areLocalAndServerAlertsMismatch()&&e.synchronizeAlert(!0)}))},this._handleCancel=()=>{this._model.undoToCheckpoint(this._checkpoint)},this._propertyPages=e.propertyPages,this._model=e.model,this._activePageId=e.activePageId,this._onClose=e.onClose,this._source=e.source,this._checkpoint=this._ensureCheckpoint(e.undoCheckPoint),this._subscription=this._model.model().dataSourceCollectionChanged(),this._subscription.subscribe(this,this._handleCollectionChanged)}hide(e){e?this._handleCancel():this._handleSubmit(),this._handleClose()}isVisible(){return this.visible().value()}show(e){this.isVisible()||(this._rootInstance=(0,
z.createReactRoot)(s.createElement(k,{source:this._source,onSubmit:this._handleSubmit,onClose:this._handleClose,onCancel:this._handleCancel,pages:this._propertyPages,model:this._model,activePageId:this._activePageId,shouldReturnFocus:null==e?void 0:e.shouldReturnFocus}),this._container),this._setVisibility(!0),n.emit("drawings_settings_dialog",{objectType:"drawing",scriptTitle:this._source.title(g.TitleDisplayTarget.StatusLine)}))}_handleCollectionChanged(){null===this._timeout&&(this._timeout=setTimeout((()=>{this._closeDialogIfSourceIsDeleted(),this._timeout=null})))}_closeDialogIfSourceIsDeleted(){null===this._model.model().dataSourceForId(this._source.id())&&this._handleClose()}_ensureCheckpoint(e){return void 0===e&&(e=this._model.createUndoCheckpoint()),e}}},68495:(e,t,i)=>{"use strict";i.d(t,{Title:()=>m});var s=i(50959),n=i(97754),a=i(31261),l=i(9745),o=i(14729),r=i(68335),c=i(51768),u=i(48040),d=i(52088);function m(e){const{isRenaming:t,onChangeMode:i,setTitle:m,defaultTitle:h,canBeRenamed:p}=e,g=(0,s.useRef)(null),[b,v]=(0,s.useState)(h),[f,C]=(0,s.useState)(h);return(0,s.useEffect)((()=>{t&&g.current&&(g.current.focus(),g.current.setSelectionRange(0,b.length))}),[t]),s.createElement(s.Fragment,null,s.createElement("div",{className:n(d.titleWrap,t&&d.hideText)},s.createElement("span",{className:d.ellipsis}," ",f),p&&s.createElement(s.Fragment,null,s.createElement(l.Icon,{className:n(d.editIcon),icon:u,onClick:function(){(0,c.trackEvent)("GUI","Rename","Drawing settings"),v(f),i(!0)},"data-name":"edit","data-role":"button"}),s.createElement("div",{className:n(d.empty,!t&&d.hideEmpty)}))),p&&s.createElement("div",{className:n(!t&&d.hideInput),"data-disable-drag":!0},s.createElement(a.InputControl,{value:b,onChange:function(e){v(e.currentTarget.value)},onBlur:_,reference:g,onClick:o.preventDefault,onKeyDown:function(e){27===(0,r.hashFromEvent)(e)&&(e.preventDefault(),v(h),i(!1));13===(0,r.hashFromEvent)(e)&&(e.preventDefault(),_())},"data-disable-drag":!0,stretch:!0})));function _(){""!==b&&(m(b),C(b)),i(!1)}}},66512:(e,t,i)=>{"use strict";i.r(t),i.d(t,{SourcesPropertiesEditorRenderer:()=>v});var s=i(50959),n=i(29280),a=i(28124),l=i(11542),o=i(86656),r=i(50182),c=i(48531),u=i(37289),d=i(68495),m=i(56570),h=i(38546),p=i(65119);const g=l.t(null,void 0,i(8954));function b(e){const{propertyPages:t,onSubmit:i,onCancel:n,onClose:a,title:l,activeTabId:b,sources:v,undoModel:f,renamable:C}=e,_=b&&t.filter((e=>e.id===b)).length>0?b:t[0].id,x=f.model().lineToolsGroupModel().groupForLineTool(v[0]),S=!!(C&&x&&l&&v.every((e=>{var t;return(null==x?void 0:x.id)===(null===(t=f.model().lineToolsGroupModel().groupForLineTool(e))||void 0===t?void 0:t.id)}))),[T,w]=(0,s.useState)(_),[y,E]=(0,s.useState)(!1),[I,A]=(0,s.useState)(l||g),P=(0,s.useMemo)((()=>t.map((({title:e,id:t})=>({label:e,id:t,dataId:`sources-properties-editor-tabs-${t}`})))),[t]);const k=s.createElement(d.Title,{isRenaming:y,onChangeMode:function(e){E(e)},setTitle:function(e){x&&(x.setName(e),A(e))},defaultTitle:I,canBeRenamed:S&&!m.enabled("widget")})
;return s.createElement(r.AdaptiveConfirmDialog,{dataName:"sources-properties-editor",dataDialogName:I,title:k,isOpened:!0,onSubmit:i,onCancel:n,onClickOutside:a,onClose:a,footerLeftRenderer:function(){return s.createElement(c.FooterMenu,{sources:v,chartUndoModel:f})},render:function(){const e=t.find((e=>e.id===T));return s.createElement(s.Fragment,null,s.createElement(h.DialogTabs,{className:p.tabs,id:"sources-properties-editor-tabs",activeTab:T,onChange:w,tabs:P}),s.createElement(o.TouchScrollContainer,null,s.createElement(u.PropertiesEditorTab,{page:e,tableKey:T})))},submitOnEnterKey:!1,showCloseIcon:!y})}class v extends n.DialogRenderer{constructor(e){super(),this._dataSourceChangedPromise=null,this._submitHandler=()=>{Promise.resolve().then((()=>{this._sources.map((e=>{e.areLocalAndServerAlertsMismatch()&&e.synchronizeAlert(!0)}))})),this._close()},this._cancelHandler=()=>{this._undoModel.undoToCheckpoint(this._undoCheckpoint)},this._closeHandler=()=>{this._close()},this._dataSourceCollectionChangedHandler=()=>{null===this._dataSourceChangedPromise&&(this._dataSourceChangedPromise=Promise.resolve().then((()=>{const e=this._undoModel.model();this._sources.find((t=>null===e.dataSourceForId(t.id())))&&this._close(),this._dataSourceChangedPromise=null})))},this._sources=e.sources,this._propertyPages=e.propertyPages,this._undoModel=e.undoModel,this._title=e.title,this._activeTabId=e.activeTabId,this._renamable=e.renamable,this._undoCheckpoint=this._undoModel.createUndoCheckpoint(),this._undoModel.model().dataSourceCollectionChanged().subscribe(this,this._dataSourceCollectionChangedHandler)}destroy(){this._close()}show(){this._isVisible()||(this._mount(),this._setVisibility(!0))}hide(){this._isVisible()&&(this._unmount(),this._setVisibility(!1))}_mount(){this._rootInstance=(0,a.createReactRoot)(s.createElement(b,{propertyPages:this._propertyPages,sources:this._sources,undoModel:this._undoModel,onSubmit:this._submitHandler,onCancel:this._cancelHandler,onClose:this._closeHandler,title:this._title,activeTabId:this._activeTabId,renamable:this._renamable}),this._container)}_unmount(){var e;null===(e=this._rootInstance)||void 0===e||e.unmount()}_isVisible(){return this.visible().value()}_close(){this.hide(),this._undoModel.model().dataSourceCollectionChanged().unsubscribe(this,this._dataSourceCollectionChangedHandler)}}},93081:(e,t,i)=>{"use strict";i.d(t,{UnderlineButtonTabs:()=>q});var s,n=i(50959),a=i(97754),l=i.n(a),o=i(11542),r=i(95854),c=i(38528),u=i(47201),d=i(73775),m=i(16212),h=i(26597);!function(e){e.XSmall="xsmall",e.Small="small",e.Medium="medium",e.Large="large",e.XLarge="xlarge"}(s||(s={}));const p=(0,n.createContext)({size:"small",overflowBehaviour:void 0});var g=i(17946),b=i(24554);function v(e){const{size:t="xsmall",active:i,fake:s,enableActiveStateStyles:n,anchor:l=!1,hideFocusOutline:o=!1,equalTabSize:r,className:c,overflowBehaviour:u}=e
;return a(b["underline-tab"],b[`size-${t}`],i&&b.selected,!n&&b["disable-active-state-styles"],o&&b["disable-focus-outline"],s&&b.fake,l&&b["enable-cursor-pointer"],r&&b[`margin-${t}`],"collapse"===u&&b.collapse,c)}const f=(0,n.forwardRef)(((e,t)=>{const{size:i,overflowBehaviour:s}=(0,n.useContext)(p),a=(0,n.useContext)(g.CustomBehaviourContext),{active:o,fake:r,className:c,enableActiveStateStyles:u=a.enableActiveStateStyles,hideFocusOutline:d=!1,equalTabSize:m,children:h,...f}=e;return n.createElement("button",{...f,ref:t,className:v({size:i,active:o,fake:r,enableActiveStateStyles:u,hideFocusOutline:d,equalTabSize:m,className:c,overflowBehaviour:s})},m&&"string"==typeof h?n.createElement("span",{className:l()(b["ellipsis-children"],"apply-overflow-tooltip")},h):h)}));f.displayName="UnderlineTabsBaseButton";const C=(0,n.forwardRef)(((e,t)=>{const{item:i,highlighted:s,handleItemRef:a,onClick:l,"aria-disabled":o,...r}=e,c=(0,n.useCallback)((()=>{l&&l(i)}),[l,i]),u=(0,n.useCallback)((e=>{a&&a(i,e),t&&"object"==typeof t?t.current=e:"function"==typeof t&&t(e)}),[i,a,t]);return n.createElement(f,{...r,id:i.id,onClick:c,ref:u},i.label)}));C.displayName="UnderlineButtonTab";var _=i(50151),x=i(16396),S=i(4523),T=i(9745),w=i(47531),y=i(2948),E=i(63509),I=i(68874),A=i(10399);function P(e){switch(e){case"xsmall":return w;case"small":return y;case"medium":case"large":return E;case"xlarge":return I}}function k(e){const{size:t,isDropped:i=!1}=e;return n.createElement(T.Icon,{icon:P(t),className:a(A["arrow-icon"],A[`size-${t}`],i&&A.dropped)})}var B=i(456);function z(e){const{size:t,disabled:i,isOpened:s,enableActiveStateStyles:a,hideFocusOutline:l,fake:o,items:r,buttonContent:u,buttonRef:d,isAnchorTabs:m,isHighlighted:h,onButtonClick:p,onItemClick:g,onClose:b}=e,v=(0,n.useRef)(null),C=(0,c.useMergedRefs)([d,v]),T=function(e,t){const i=(0,n.useRef)(N);return(0,n.useEffect)((()=>{const e=getComputedStyle((0,_.ensureNotNull)(t.current));i.current={xsmall:R(e,"xsmall"),small:R(e,"small"),medium:R(e,"medium"),large:R(e,"large"),xlarge:R(e,"xlarge")}}),[t]),(0,n.useCallback)((()=>{const s=(0,_.ensureNotNull)(t.current).getBoundingClientRect(),n=i.current[e];return{x:s.left,y:s.top+s.height+n+4,indentFromWindow:{top:4,bottom:4,left:4,right:4}}}),[t,e])}(t,v);return n.createElement(S.PopupMenuDisclosureView,{buttonRef:v,listboxTabIndex:-1,isOpened:s,onClose:b,listboxAria:{"aria-hidden":!0},popupPosition:T,button:n.createElement(f,{"aria-hidden":!0,disabled:i,active:s,onClick:p,ref:C,tabIndex:-1,enableActiveStateStyles:a,hideFocusOutline:l,fake:o},u,n.createElement(k,{size:t,isDropped:s})),popupChildren:r.map((e=>n.createElement(x.PopupMenuItem,{key:e.id,className:m?B["link-item"]:void 0,onClick:g,onClickArg:e,isActive:h(e),label:e.label,isDisabled:e.disabled,link:"href"in e?e.href:void 0,rel:"rel"in e?e.rel:void 0,target:"target"in e?e.target:void 0,renderComponent:"renderComponent"in e?e.renderComponent:void 0,dontClosePopup:!0})))})}function R(e,t){return parseInt(e.getPropertyValue(`--ui-lib-underline-tabs-tab-margin-bottom-${t}`),10)}
const N={xsmall:0,small:0,medium:0,large:0,xlarge:0};var D=i(5325),M=i(42707),F=i(86240),Y=i(7633);function H(e){const{size:t,overflowBehaviour:i,className:s}=e;return a(Y["scroll-wrap"],Y[`size-${t}`],"scroll"===i&&Y["enable-scroll"],s)}function L(){const[e,t]=(0,n.useState)(!1);return(0,n.useEffect)((()=>{t(D.mobiletouch)}),[]),e}var X=i(12481),j=i(63273),O=i(29662),W=i.n(O);function V(e){const{disabled:t,translateX:i,transitionDuration:s}=e,l=e.scale/100;return n.createElement("div",{className:a(W().underline,t&&W().disabled),style:{transform:`translateX(${i}px) scaleX(${l})`,transitionDuration:`${s}ms`}},n.createElement("div",{className:W().corner,style:{transform:`scaleX(${1/l})`}}),n.createElement("div",{className:W().center,style:{transform:`scaleX(${1-30/e.scale})`}}),n.createElement("div",{className:W().corner,style:{transform:`scaleX(${1/l})`}}))}function q(e){const{id:t,items:s,activationType:a,orientation:g,disabled:b,moreButtonContent:v=o.t(null,void 0,i(37117)),size:f="small",onActivate:_,isActive:x,className:S,style:T,overflowBehaviour:w,enableActiveStateStyles:y,tablistLabelId:E,tablistLabel:I,"data-name":A="underline-tabs-buttons",stretchTabs:P,equalTabSize:k}=e,B=L(),R=function(e){const t=(0,M.useSafeMatchMedia)(F["media-mf-phone-landscape"],!0),i=L();return null!=e?e:i||!t?"scroll":"collapse"}(w),N=(0,n.useRef)(!1),D=(0,n.useCallback)((e=>e.id),[]),O="none"===R&&P,W="none"===R&&k,q=null!=y?y:!B,{visibleItems:J,hiddenItems:K,containerRefCallback:U,innerContainerRefCallback:$,moreButtonRef:Z,setItemRef:G}=(0,r.useCollapsible)(s,D,x),Q="collapse"===R?J:s,ee="collapse"===R?K:[],te=(0,n.useCallback)((e=>ee.includes(e)),[ee]),ie=(0,n.useRef)(new Map),{isOpened:se,open:ne,close:ae,onButtonClick:le}=(0,d.useDisclosure)({id:t,disabled:b}),oe=function(e="xsmall"){switch(e){case"xsmall":case"small":return 12;case"medium":return 16;case"large":case"xlarge":return 20}}(f),{tabsBindings:re,tablistBinding:ce,scrollWrapBinding:ue,onActivate:de,onHighlight:me,isHighlighted:he}=(0,m.useTabs)({id:t,items:[...Q,...ee],activationType:a,orientation:g,disabled:b,tablistLabelId:E,tablistLabel:I,onActivate:_,isActive:x,isCollapsed:te,isRtl:j.isRtl,itemsRefs:ie,isDisclosureOpened:se,scrollIntoViewOptions:{additionalScroll:oe}}),pe=s.find(x),ge=ee.find(he),be=(0,n.useCallback)((()=>{pe&&me(pe)}),[me,pe]),ve=(0,n.useCallback)((e=>{var t;return null!==(t=re.find((t=>t.id===e.id)))&&void 0!==t?t:{}}),[re]),fe=(0,n.useCallback)((()=>{ae(),be(),N.current=!0}),[ae,be]),Ce=(0,n.useCallback)((()=>{ge&&(de(ge),me(ge,200))}),[de,me,ge]);ue.ref=(0,c.useMergedRefs)([ue.ref,U]),ce.ref=(0,c.useMergedRefs)([ce.ref,$]),ce.onKeyDown=(0,u.createSafeMulticastEventHandler)((0,h.useKeyboardEventHandler)([(0,h.useKeyboardClose)(se,fe),(0,h.useKeyboardActionHandler)([13,32],Ce,(0,n.useCallback)((()=>Boolean(ge)),[ge]))]),ce.onKeyDown);const _e=(0,n.useCallback)((e=>{N.current=!0,le(e)}),[N,le]),xe=(0,n.useCallback)((e=>{e&&de(e)}),[de]);(0,n.useEffect)((()=>{N.current?N.current=!1:(ge&&!se&&ne(),!ge&&se&&ae())}),[ge,se,ne,ae])
;const Se=function(e,t,i=[]){const[s,a]=(0,n.useState)(),l=(0,n.useRef)(),o=(0,n.useRef)(),r=e=>{var t;const i=null!==(t=e.parentElement)&&void 0!==t?t:void 0;if(void 0===i)return;const s=void 0===o.current||o.current===e?0:100;o.current=e;const{left:n,right:l,width:r}=e.getBoundingClientRect(),{left:c,right:u}=i.getBoundingClientRect(),d=(0,j.isRtl)()?l-u:n-c;a({translateX:d,scale:r,transitionDuration:s})};return(0,n.useEffect)((()=>{const e=(0,X.default)((e=>{const t=e[0].target;void 0!==t&&r(t)}),50);l.current=new ResizeObserver(e)}),[]),(0,n.useEffect)((()=>{var i;if(void 0===t)return;const s=e.get(t);return void 0!==s?(r(s),null===(i=l.current)||void 0===i||i.observe(s),()=>{var e;return null===(e=l.current)||void 0===e?void 0:e.disconnect()}):void 0}),i),s}(ie.current,null!=pe?pe:ge,[null!=pe?pe:ge,Q,f,O,R]);return n.createElement(p.Provider,{value:{size:f,overflowBehaviour:R}},n.createElement("div",{...ue,className:H({size:f,overflowBehaviour:R,className:S}),style:T,"data-name":A},n.createElement("div",{...ce,className:l()(Y["underline-tabs"],{[Y["make-grid-column"]]:O||W,[Y["stretch-tabs"]]:O,[Y["equal-tab-size"]]:W})},Q.map((e=>n.createElement(C,{...ve(e),key:e.id,item:e,onClick:de,enableActiveStateStyles:q,hideFocusOutline:B,ref:G(D(e)),...e.dataId&&{"data-id":e.dataId},equalTabSize:W}))),ee.map((e=>n.createElement(C,{...ve(e),ref:G(D(e)),key:e.id,item:e,fake:!0}))),"collapse"===R&&n.createElement(z,{size:f,disabled:b,isOpened:se,items:ee,buttonContent:v,buttonRef:Z,isHighlighted:he,onButtonClick:_e,onItemClick:xe,onClose:ae,enableActiveStateStyles:q,hideFocusOutline:B,fake:0===ee.length}),Se?n.createElement(V,{...Se,disabled:b}):n.createElement("div",null))))}var J=i(38952);function K(e){return n.createElement("a",{...(0,J.renameRef)(e)})}(0,n.forwardRef)(((e,t)=>{var i;const{size:s,overflowBehaviour:a}=(0,n.useContext)(p),l=(0,n.useContext)(g.CustomBehaviourContext),{item:o,highlighted:r,handleItemRef:c,onClick:u,active:d,fake:m,className:h,enableActiveStateStyles:b=l.enableActiveStateStyles,hideFocusOutline:f=!1,disabled:C,"aria-disabled":_,...x}=e,S=(0,n.useCallback)((e=>{_?e.preventDefault():u&&u(o)}),[u,_,o]),T=(0,n.useCallback)((e=>{c&&c(o,e),t&&"object"==typeof t?t.current=e:"function"==typeof t&&t(e)}),[o,c,t]),w=null!==(i=o.renderComponent)&&void 0!==i?i:K;return n.createElement(w,{...x,id:o.id,"aria-disabled":_,onClick:S,reference:T,href:o.href,rel:o.rel,target:o.target,className:v({size:s,active:d,fake:m,enableActiveStateStyles:b,anchor:!0,hideFocusOutline:f,className:h,overflowBehaviour:a})},o.label)})).displayName="UnderlineAnchorTab"},47531:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="m4.67 7.38.66-.76L9 9.84l3.67-3.22.66.76L9 11.16 4.67 7.38Z"/></svg>'},63509:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M3.57 7.85 9 12.62l5.43-4.77-1.32-1.5L9 9.95l-4.11-3.6-1.32 1.5Z"/></svg>'},48040:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M16.73 6.56a2.5 2.5 0 0 1 3.54 0l1.17 1.17a2.5 2.5 0 0 1 0 3.54l-.59.58-9 9-1 1-.14.15H6v-4.7l.15-.15 1-1 9-9 .58-.59Zm2.83.7a1.5 1.5 0 0 0-2.12 0l-.23.24 3.29 3.3.23-.24a1.5 1.5 0 0 0 0-2.12l-1.17-1.17Zm.23 4.24L16.5 8.2l-8.3 8.3 3.3 3.3 8.3-8.3Zm-9 9L7.5 17.2l-.5.5V21h3.3l.5-.5Z"/></svg>'},68874:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="m14 18.41-6.7-6.7 1.4-1.42 5.3 5.3 5.3-5.3 1.4 1.41-6.7 6.71Z"/></svg>'}}]);