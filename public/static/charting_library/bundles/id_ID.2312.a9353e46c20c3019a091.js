(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[2312],{53310:e=>{e.exports={en:["Re"],id_ID:["ulangi"]}},94073:e=>{e.exports={en:["A"],id_ID:["A"]}},66384:e=>{e.exports={en:["L"],id_ID:["L"]}},85119:e=>{e.exports={en:["Dark"],id_ID:["Gelap"]}},96870:e=>{e.exports={en:["Light"],id_ID:["Terang"]}},85886:e=>{e.exports={en:["d"],id_ID:["d"]}},44634:e=>{e.exports={en:["h"],id_ID:["h"]}},5977:e=>{e.exports={en:["m"],id_ID:["m"]}},21492:e=>{e.exports={en:["s"],id_ID:["s"]}},97559:e=>{e.exports={en:["{title} copy"],id_ID:["Salinan {title}"]}},38691:e=>{e.exports={en:["D"],id_ID:["D"]}},77995:e=>{e.exports={en:["M"],id_ID:["M"]}},93934:e=>{e.exports={en:["R"],id_ID:["R"]}},82901:e=>{e.exports={en:["T"],id_ID:["T"]}},7408:e=>{e.exports={en:["W"],id_ID:["W"]}},38048:e=>{e.exports={en:["h"],id_ID:["h"]}},68430:e=>{e.exports={en:["m"],id_ID:["m"]}},68823:e=>{e.exports={en:["s"],id_ID:["s"]}},2696:e=>{e.exports={en:["C"],id_ID:["C"]}},43253:e=>{e.exports={en:["H"],id_ID:["H"]}},61372:e=>{e.exports={en:["HL2"],id_ID:["HL2"]}},55096:e=>{e.exports={en:["HLC3"],id_ID:["HLC3"]}},94174:e=>{e.exports={en:["OHLC4"],id_ID:["OHLC4"]}},89923:e=>{e.exports={en:["L"],id_ID:["L"]}},46728:e=>{e.exports={en:["O"],id_ID:["O"]}},32856:e=>{e.exports=Object.create(null),e.exports.Close_input={en:["Close"],id_ID:["Tutup"]},e.exports.Back_input={en:["Back"],id_ID:["Kembali"]},e.exports.Minimize_input={en:["Minimize"],id_ID:["Perkecil"]},e.exports["Hull MA_input"]={en:["Hull MA"],id_ID:["MA Hull"]},e.exports.from_input={en:["from"],id_ID:["dari"]},e.exports.to_input={en:["to"],id_ID:["ke"]},e.exports["{number} item_combobox_input"]={en:["{number} item","{number} items"],id_ID:["{number} item"]},e.exports.Length_input={en:["Length"],id_ID:["Panjang"]},e.exports.Plot_input={en:["Plot"],id_ID:["Plot"]},e.exports.Zero_input={en:["Zero"],id_ID:["Nol"]},e.exports.Signal_input={en:["Signal"],id_ID:["Sinyal"]},e.exports.Long_input={en:["Long"],id_ID:["Pembelian"]},e.exports.Short_input={en:["Short"],id_ID:["Penjualan"]},e.exports.UpperLimit_input={en:["UpperLimit"],id_ID:["LimitAtas"]},e.exports.LowerLimit_input={en:["LowerLimit"],id_ID:["LimitBawah"]},e.exports.Offset_input={en:["Offset"],id_ID:["Offset"]},e.exports.length_input={en:["length"],id_ID:["panjang"]},e.exports.mult_input={en:["mult"],id_ID:["mult"]},e.exports.short_input={en:["short"],id_ID:["penjualan"]},e.exports.long_input={en:["long"],id_ID:["pembelian"]},e.exports.Limit_input={en:["Limit"],id_ID:["Limit"]},e.exports.Move_input={en:["Move"],id_ID:["Pindah"]},e.exports.Value_input={en:["Value"],id_ID:["Nilai"]},e.exports.Method_input={en:["Method"],id_ID:["Metode"]},e.exports["Values in status line_input"]={en:["Values in status line"],id_ID:["Nilai dalam baris status"]},e.exports["Labels on price scale_input"]={en:["Labels on price scale"],id_ID:["Label pada skala harga"]},e.exports["Accumulation/Distribution_input"]={en:["Accumulation/Distribution"],id_ID:["Akumulasi/Distribusi"]},e.exports.ADR_B_input={en:["ADR_B"],id_ID:["ADR_B"]},e.exports["Equality Line_input"]={
en:["Equality Line"],id_ID:["Garis Kesetaraan"]},e.exports["Window Size_input"]={en:["Window Size"],id_ID:["Besar Jendela"]},e.exports.Sigma_input={en:["Sigma"],id_ID:["Sigma"]},e.exports["Aroon Up_input"]={en:["Aroon Up"],id_ID:["Aroon Naik"]},e.exports["Aroon Down_input"]={en:["Aroon Down"],id_ID:["Aroon Turun"]},e.exports.Upper_input={en:["Upper"],id_ID:["Atas"]},e.exports.Lower_input={en:["Lower"],id_ID:["Bawah"]},e.exports.Deviation_input={en:["Deviation"],id_ID:["Deviasi"]},e.exports["Levels Format_input"]={en:["Levels Format"],id_ID:["Format Level"]},e.exports["Labels Position_input"]={en:["Labels Position"],id_ID:["Posisi Label"]},e.exports["0 Level Color_input"]={en:["0 Level Color"],id_ID:["Warna Level 0"]},e.exports["0.236 Level Color_input"]={en:["0.236 Level Color"],id_ID:["Warna Level 0.236"]},e.exports["0.382 Level Color_input"]={en:["0.382 Level Color"],id_ID:["Warna Level 0.382"]},e.exports["0.5 Level Color_input"]={en:["0.5 Level Color"],id_ID:["Warna Level 0.5"]},e.exports["0.618 Level Color_input"]={en:["0.618 Level Color"],id_ID:["Warna Level 0.618"]},e.exports["0.65 Level Color_input"]={en:["0.65 Level Color"],id_ID:["Warna Level 0.65"]},e.exports["0.786 Level Color_input"]={en:["0.786 Level Color"],id_ID:["Warna Level 0.786"]},e.exports["1 Level Color_input"]={en:["1 Level Color"],id_ID:["Warna Level 1"]},e.exports["1.272 Level Color_input"]={en:["1.272 Level Color"],id_ID:["Warna Level 1.272"]},e.exports["1.414 Level Color_input"]={en:["1.414 Level Color"],id_ID:["Warna Level 1.414"]},e.exports["1.618 Level Color_input"]={en:["1.618 Level Color"],id_ID:["Warna Level 1.618"]},e.exports["1.65 Level Color_input"]={en:["1.65 Level Color"],id_ID:["Warna Level 1.65"]},e.exports["2.618 Level Color_input"]={en:["2.618 Level Color"],id_ID:["Warna Level 2.618"]},e.exports["2.65 Level Color_input"]={en:["2.65 Level Color"],id_ID:["Warna Level 2.65"]},e.exports["3.618 Level Color_input"]={en:["3.618 Level Color"],id_ID:["Warna Level 3.618"]},e.exports["3.65 Level Color_input"]={en:["3.65 Level Color"],id_ID:["Warna Level 3.65"]},e.exports["4.236 Level Color_input"]={en:["4.236 Level Color"],id_ID:["Warna Level 4.236"]},e.exports["-0.236 Level Color_input"]={en:["-0.236 Level Color"],id_ID:["Warna Level -0.236"]},e.exports["-0.382 Level Color_input"]={en:["-0.382 Level Color"],id_ID:["Warna Level -0.382"]},e.exports["-0.618 Level Color_input"]={en:["-0.618 Level Color"],id_ID:["Warna Level -0.618"]},e.exports["-0.65 Level Color_input"]={en:["-0.65 Level Color"],id_ID:["Warna Level -0.65"]},e.exports.ADX_input={en:["ADX"],id_ID:["ADX"]},e.exports["ADX Smoothing_input"]={en:["ADX Smoothing"],id_ID:["Penghalusan ADX"]},e.exports["DI Length_input"]={en:["DI Length"],id_ID:["Panjang DI"]},e.exports.Smoothing_input={en:["Smoothing"],id_ID:["Penghalusan"]},e.exports.ATR_input={en:["ATR"],id_ID:["ATR"]},e.exports.Growing_input={en:["Growing"],id_ID:["Berkembang"]},e.exports.Falling_input={en:["Falling"],id_ID:["Jatuh"]},e.exports["Color 0_input"]={en:["Color 0"],id_ID:["Warna 0"]},e.exports["Color 1_input"]={
en:["Color 1"],id_ID:["Warna 1"]},e.exports.Source_input={en:["Source"],id_ID:["Sumber"]},e.exports.StdDev_input={en:["StdDev"],id_ID:["StdDev"]},e.exports.Basis_input={en:["Basis"],id_ID:["Basis"]},e.exports.Median_input={en:["Median"],id_ID:["Median"]},e.exports["Bollinger Bands %B_input"]={en:["Bollinger Bands %B"],id_ID:["%B Ikat Bollinger"]},e.exports.Overbought_input={en:["Overbought"],id_ID:["Overbought"]},e.exports.Oversold_input={en:["Oversold"],id_ID:["Oversold"]},e.exports["Bollinger Bands Width_input"]={en:["Bollinger Bands Width"],id_ID:["Lebar Ikat Bollinger"]},e.exports["RSI Length_input"]={en:["RSI Length"],id_ID:["Panjang RSI"]},e.exports["UpDown Length_input"]={en:["UpDown Length"],id_ID:["Panjang UpDown"]},e.exports["ROC Length_input"]={en:["ROC Length"],id_ID:["Panjang ROC"]},e.exports.MF_input={en:["MF"],id_ID:["MF"]},e.exports.resolution_input={en:["resolution"],id_ID:["resolusi"]},e.exports["Fast Length_input"]={en:["Fast Length"],id_ID:["Panjang Cepat"]},e.exports["Slow Length_input"]={en:["Slow Length"],id_ID:["Panjang Lambat"]},e.exports["Chaikin Oscillator_input"]={en:["Chaikin Oscillator"],id_ID:["Osilator Chaikin"]},e.exports.P_input={en:["P"],id_ID:["P"]},e.exports.X_input={en:["X"],id_ID:["X"]},e.exports.Q_input={en:["Q"],id_ID:["Q"]},e.exports.p_input={en:["p"],id_ID:["p"]},e.exports.x_input={en:["x"],id_ID:["x"]},e.exports.q_input={en:["q"],id_ID:["q"]},e.exports.Price_input={en:["Price"],id_ID:["Harga"]},e.exports["Chande MO_input"]={en:["Chande MO"],id_ID:["MO Chande"]},e.exports["Zero Line_input"]={en:["Zero Line"],id_ID:["Garis Nol"]},e.exports["Color 2_input"]={en:["Color 2"],id_ID:["Warna 2"]},e.exports["Color 3_input"]={en:["Color 3"],id_ID:["Warna 3"]},e.exports["Color 4_input"]={en:["Color 4"],id_ID:["Warna 4"]},e.exports["Color 5_input"]={en:["Color 5"],id_ID:["Warna 5"]},e.exports["Color 6_input"]={en:["Color 6"],id_ID:["Warna 6"]},e.exports["Color 7_input"]={en:["Color 7"],id_ID:["Warna 7"]},e.exports["Color 8_input"]={en:["Color 8"],id_ID:["Warna 8"]},e.exports.CHOP_input={en:["CHOP"],id_ID:["CHOP"]},e.exports["Upper Band_input"]={en:["Upper Band"],id_ID:["Ikat Atas"]},e.exports["Lower Band_input"]={en:["Lower Band"],id_ID:["Ikat Bawah"]},e.exports.CCI_input={en:["CCI"],id_ID:["CCI"]},e.exports["Smoothing Line_input"]={en:["Smoothing Line"],id_ID:["Garis Diperhalus"]},e.exports["Smoothing Length_input"]={en:["Smoothing Length"],id_ID:["Panjang Diperhalus"]},e.exports["WMA Length_input"]={en:["WMA Length"],id_ID:["Panjang WMA"]},e.exports["Long RoC Length_input"]={en:["Long RoC Length"],id_ID:["Panjang RoC Pembelian"]},e.exports["Short RoC Length_input"]={en:["Short RoC Length"],id_ID:["Panjang RoC Penjualan"]},e.exports.sym_input={en:["sym"],id_ID:["sim"]},e.exports.Symbol_input={en:["Symbol"],id_ID:["Simbol"]},e.exports.Correlation_input={en:["Correlation"],id_ID:["Korelasi"]},e.exports.Period_input={en:["Period"],id_ID:["Periode"]},e.exports.Centered_input={en:["Centered"],id_ID:["Dipusatkan"]},e.exports["Detrended Price Oscillator_input"]={
en:["Detrended Price Oscillator"],id_ID:["Osilator Harga Detrended / Detrended Price Oscillator"]},e.exports.isCentered_input={en:["isCentered"],id_ID:["Ditengahkan"]},e.exports.DPO_input={en:["DPO"],id_ID:["DPO"]},e.exports["ADX smoothing_input"]={en:["ADX smoothing"],id_ID:["Penghalusan ADX"]},e.exports["+DI_input"]={en:["+DI"],id_ID:["+DI"]},e.exports["-DI_input"]={en:["-DI"],id_ID:["-DI"]},e.exports.DEMA_input={en:["DEMA"],id_ID:["DEMA"]},e.exports["Multi timeframe_input"]={en:["Multi timeframe"],id_ID:["Beberapa kerangka waktu"]},e.exports.Timeframe_input={en:["Timeframe"],id_ID:["Kerangka waktu"]},e.exports["Wait for timeframe closes_input"]={en:["Wait for timeframe closes"],id_ID:["Tunggu kerangka waktu ditutup"]},e.exports.Divisor_input={en:["Divisor"],id_ID:["Pembagi"]},e.exports.EOM_input={en:["EOM"],id_ID:["EOM"]},e.exports["Elder's Force Index_input"]={en:["Elder's Force Index"],id_ID:["Indeks Kekuatan Elder / Elder's Force Index"]},e.exports.Percent_input={en:["Percent"],id_ID:["Persen"]},e.exports.Exponential_input={en:["Exponential"],id_ID:["Eksponensial"]},e.exports.Average_input={en:["Average"],id_ID:["Rata-Rata"]},e.exports["Upper Percentage_input"]={en:["Upper Percentage"],id_ID:["Persentase Atas"]},e.exports["Lower Percentage_input"]={en:["Lower Percentage"],id_ID:["Persentase Bawah"]},e.exports.Fisher_input={en:["Fisher"],id_ID:["Fisher"]},e.exports.Trigger_input={en:["Trigger"],id_ID:["Pemicu"]},e.exports.Level_input={en:["Level"],id_ID:["Level"]},e.exports["Trader EMA 1 length_input"]={en:["Trader EMA 1 length"],id_ID:["EMA Trader panjang 1"]},e.exports["Trader EMA 2 length_input"]={en:["Trader EMA 2 length"],id_ID:["EMA Trader panjang 2"]},e.exports["Trader EMA 3 length_input"]={en:["Trader EMA 3 length"],id_ID:["EMA Trader panjang 3"]},e.exports["Trader EMA 4 length_input"]={en:["Trader EMA 4 length"],id_ID:["EMA Trader panjang 4"]},e.exports["Trader EMA 5 length_input"]={en:["Trader EMA 5 length"],id_ID:["EMA Trader panjang 5"]},e.exports["Trader EMA 6 length_input"]={en:["Trader EMA 6 length"],id_ID:["EMA Trader panjang 6"]},e.exports["Investor EMA 1 length_input"]={en:["Investor EMA 1 length"],id_ID:["EMA Investor panjang 1"]},e.exports["Investor EMA 2 length_input"]={en:["Investor EMA 2 length"],id_ID:["EMA Investor panjang 2"]},e.exports["Investor EMA 3 length_input"]={en:["Investor EMA 3 length"],id_ID:["EMA Investor panjang 3"]},e.exports["Investor EMA 4 length_input"]={en:["Investor EMA 4 length"],id_ID:["EMA Investor panjang 4"]},e.exports["Investor EMA 5 length_input"]={en:["Investor EMA 5 length"],id_ID:["EMA Investor panjang 5"]},e.exports["Investor EMA 6 length_input"]={en:["Investor EMA 6 length"],id_ID:["EMA Investor panjang 6"]},e.exports.HV_input={en:["HV"],id_ID:["HV"]},e.exports["Conversion Line Periods_input"]={en:["Conversion Line Periods"],id_ID:["Periode Garis Konversi"]},e.exports["Base Line Periods_input"]={en:["Base Line Periods"],id_ID:["Periode Garis Dasar"]},e.exports["Lagging Span_input"]={en:["Lagging Span"],id_ID:["Lagging Span"]},
e.exports["Conversion Line_input"]={en:["Conversion Line"],id_ID:["Garis Konversi"]},e.exports["Base Line_input"]={en:["Base Line"],id_ID:["Garis Dasar"]},e.exports["Leading Span A_input"]={en:["Leading Span A"],id_ID:["Leading Span A"]},e.exports["Leading Span B_input"]={en:["Leading Span B"],id_ID:["Leading Span B"]},e.exports["Plots Background_input"]={en:["Plots Background"],id_ID:["Latar Belakang Plot"]},e.exports["yay Color 0_input"]={en:["yay Color 0"],id_ID:["Warna yay 0"]},e.exports["yay Color 1_input"]={en:["yay Color 1"],id_ID:["Warna yay 1"]},e.exports.Multiplier_input={en:["Multiplier"],id_ID:["Pengali"]},e.exports["Bands style_input"]={en:["Bands style"],id_ID:["Corak Pita"]},e.exports.Middle_input={en:["Middle"],id_ID:["Tengah"]},e.exports.useTrueRange_input={en:["useTrueRange"],id_ID:["gunakanRentangSebenarnya"]},e.exports.ROCLen1_input={en:["ROCLen1"],id_ID:["PjgROC1"]},e.exports.ROCLen2_input={en:["ROCLen2"],id_ID:["PjgROC2"]},e.exports.ROCLen3_input={en:["ROCLen3"],id_ID:["PjgROC3"]},e.exports.ROCLen4_input={en:["ROCLen4"],id_ID:["PjgROC4"]},e.exports.SMALen1_input={en:["SMALen1"],id_ID:["PjgSMA1"]},e.exports.SMALen2_input={en:["SMALen2"],id_ID:["PjgSMA2"]},e.exports.SMALen3_input={en:["SMALen3"],id_ID:["PjgSMA3"]},e.exports.SMALen4_input={en:["SMALen4"],id_ID:["PjgSMA4"]},e.exports.SigLen_input={en:["SigLen"],id_ID:["PjgSig"]},e.exports.KST_input={en:["KST"],id_ID:["KST"]},e.exports.Sig_input={en:["Sig"],id_ID:["Sig"]},e.exports.roclen1_input={en:["roclen1"],id_ID:["pjgroc1"]},e.exports.roclen2_input={en:["roclen2"],id_ID:["pjgroc2"]},e.exports.roclen3_input={en:["roclen3"],id_ID:["pjgroc3"]},e.exports.roclen4_input={en:["roclen4"],id_ID:["pjgroc4"]},e.exports.smalen1_input={en:["smalen1"],id_ID:["pjgsma1"]},e.exports.smalen2_input={en:["smalen2"],id_ID:["pjgsma2"]},e.exports.smalen3_input={en:["smalen3"],id_ID:["pjgsma3"]},e.exports.smalen4_input={en:["smalen4"],id_ID:["pjgsma4"]},e.exports.siglen_input={en:["siglen"],id_ID:["pjgsin"]},e.exports["Upper Deviation_input"]={en:["Upper Deviation"],id_ID:["Deviasi Atas"]},e.exports["Lower Deviation_input"]={en:["Lower Deviation"],id_ID:["Deviasi Bawah"]},e.exports["Use Upper Deviation_input"]={en:["Use Upper Deviation"],id_ID:["Gunakan Deviasi Atas"]},e.exports["Use Lower Deviation_input"]={en:["Use Lower Deviation"],id_ID:["Gunakan Deviasi Bawah"]},e.exports.Count_input={en:["Count"],id_ID:["Hitung"]},e.exports.Crosses_input={en:["Crosses"],id_ID:["Persilangan"]},e.exports.MOM_input={en:["MOM"],id_ID:["MOM"]},e.exports.MA_input={en:["MA"],id_ID:["MA"]},e.exports["Length EMA_input"]={en:["Length EMA"],id_ID:["Panjang EMA"]},e.exports["Length MA_input"]={en:["Length MA"],id_ID:["Panjang MA"]},e.exports["Fast length_input"]={en:["Fast length"],id_ID:["Panjang Cepat"]},e.exports["Slow length_input"]={en:["Slow length"],id_ID:["Panjang lambat"]},e.exports["Signal smoothing_input"]={en:["Signal smoothing"],id_ID:["Penghalusan sinyal"]},e.exports["Simple ma(oscillator)_input"]={en:["Simple ma(oscillator)"],id_ID:["MA sederhana(osilator)"]},
e.exports["Simple ma(signal line)_input"]={en:["Simple ma(signal line)"],id_ID:["MA sederhana (garis sinyal)"]},e.exports.Histogram_input={en:["Histogram"],id_ID:["Histogram"]},e.exports.MACD_input={en:["MACD"],id_ID:["MACD"]},e.exports.fastLength_input={en:["fastLength"],id_ID:["Panjangcepat"]},e.exports.slowLength_input={en:["slowLength"],id_ID:["Panjanglambat"]},e.exports.signalLength_input={en:["signalLength"],id_ID:["Panjangsinyal"]},e.exports.NV_input={en:["NV"],id_ID:["NV"]},e.exports.OnBalanceVolume_input={en:["OnBalanceVolume"],id_ID:["VolumeKeseimbangan"]},e.exports.Start_input={en:["Start"],id_ID:["Start"]},e.exports.Increment_input={en:["Increment"],id_ID:["Kenaikan"]},e.exports["Max value_input"]={en:["Max value"],id_ID:["Nilai Max"]},e.exports.ParabolicSAR_input={en:["ParabolicSAR"],id_ID:["SAR Parabolis"]},e.exports.start_input={en:["start"],id_ID:["mulai"]},e.exports.increment_input={en:["increment"],id_ID:["kenaikan"]},e.exports.maximum_input={en:["maximum"],id_ID:["maksimum"]},e.exports["Short length_input"]={en:["Short length"],id_ID:["Panjang penjualan"]},e.exports["Long length_input"]={en:["Long length"],id_ID:["Panjang pembelian"]},e.exports.OSC_input={en:["OSC"],id_ID:["OSC"]},e.exports.shortlen_input={en:["shortlen"],id_ID:["pjgpenjualan"]},e.exports.longlen_input={en:["longlen"],id_ID:["pjgpembelian"]},e.exports.PVT_input={en:["PVT"],id_ID:["PVT"]},e.exports.ROC_input={en:["ROC"],id_ID:["ROC"]},e.exports.RSI_input={en:["RSI"],id_ID:["RSI"]},e.exports.RVGI_input={en:["RVGI"],id_ID:["RVGI"]},e.exports.RVI_input={en:["RVI"],id_ID:["RVI"]},e.exports["Long period_input"]={en:["Long period"],id_ID:["Periode pembelian"]},e.exports["Short period_input"]={en:["Short period"],id_ID:["Periode penjualan"]},e.exports["Signal line period_input"]={en:["Signal line period"],id_ID:["Periode garis sinyal"]},e.exports.SMI_input={en:["SMI"],id_ID:["SMI"]},e.exports["SMI Ergodic Oscillator_input"]={en:["SMI Ergodic Oscillator"],id_ID:["Osilator SMI Ergodic / SMI Ergodic Oscillator"]},e.exports.Indicator_input={en:["Indicator"],id_ID:["Indikator"]},e.exports.Oscillator_input={en:["Oscillator"],id_ID:["Osilator"]},e.exports.K_input={en:["K"],id_ID:["K"]},e.exports.D_input={en:["D"],id_ID:["D"]},e.exports.smoothK_input={en:["smoothK"],id_ID:["Khalus"]},e.exports.smoothD_input={en:["smoothD"],id_ID:["Dhalus"]},e.exports["%K_input"]={en:["%K"],id_ID:["%K"]},e.exports["%D_input"]={en:["%D"],id_ID:["%D"]},e.exports["Stochastic Length_input"]={en:["Stochastic Length"],id_ID:["Panjang Stochastic"]},e.exports["RSI Source_input"]={en:["RSI Source"],id_ID:["Sumber RSI"]},e.exports.lengthRSI_input={en:["lengthRSI"],id_ID:["panjangRSI"]},e.exports.lengthStoch_input={en:["lengthStoch"],id_ID:["panjangStoch"]},e.exports.TRIX_input={en:["TRIX"],id_ID:["TRIX"]},e.exports.TEMA_input={en:["TEMA"],id_ID:["TEMA"]},e.exports["Long Length_input"]={en:["Long Length"],id_ID:["Panjang Pembelian"]},e.exports["Short Length_input"]={en:["Short Length"],id_ID:["Panjang Penjualan"]},e.exports["Signal Length_input"]={en:["Signal Length"],
id_ID:["Panjang Sinyal"]},e.exports.Length1_input={en:["Length1"],id_ID:["Panjang1"]},e.exports.Length2_input={en:["Length2"],id_ID:["Panjang2"]},e.exports.Length3_input={en:["Length3"],id_ID:["Panjang3"]},e.exports.length7_input={en:["length7"],id_ID:["panjang7"]},e.exports.length14_input={en:["length14"],id_ID:["panjang14"]},e.exports.length28_input={en:["length28"],id_ID:["panjang28"]},e.exports.UO_input={en:["UO"],id_ID:["UO"]},e.exports.VWMA_input={en:["VWMA"],id_ID:["VWMA"]},e.exports.len_input={en:["len"],id_ID:["pjg"]},e.exports["VI +_input"]={en:["VI +"],id_ID:["VI +"]},e.exports["VI -_input"]={en:["VI -"],id_ID:["VI -"]},e.exports["%R_input"]={en:["%R"],id_ID:["%R"]},e.exports["Jaw Length_input"]={en:["Jaw Length"],id_ID:["Panjang Rahang"]},e.exports["Teeth Length_input"]={en:["Teeth Length"],id_ID:["Panjang Gigi"]},e.exports["Lips Length_input"]={en:["Lips Length"],id_ID:["Panjang Bibir"]},e.exports.Jaw_input={en:["Jaw"],id_ID:["Rahang"]},e.exports.Teeth_input={en:["Teeth"],id_ID:["Gigi"]},e.exports.Lips_input={en:["Lips"],id_ID:["Bibir"]},e.exports["Jaw Offset_input"]={en:["Jaw Offset"],id_ID:["Offset Jaw"]},e.exports["Teeth Offset_input"]={en:["Teeth Offset"],id_ID:["Offset Teeth"]},e.exports["Lips Offset_input"]={en:["Lips Offset"],id_ID:["Offset Lips"]},e.exports["Down fractals_input"]={en:["Down fractals"],id_ID:["Fraktal Turun"]},e.exports["Up fractals_input"]={en:["Up fractals"],id_ID:["Fraktal naik"]},e.exports.Periods_input={en:["Periods"],id_ID:["Periode"]},e.exports.Shapes_input={en:["Shapes"],id_ID:["Bentuk"]},e.exports["show MA_input"]={en:["show MA"],id_ID:["tampilkan MA"]},e.exports["MA Length_input"]={en:["MA Length"],id_ID:["Panjang MA"]},e.exports["Color based on previous close_input"]={en:["Color based on previous close"],id_ID:["Warna dengan basis penutupan sebelumnya"]},e.exports["Rows Layout_input"]={en:["Rows Layout"],id_ID:["Layout Baris"]},e.exports["Row Size_input"]={en:["Row Size"],id_ID:["Besar Baris"]},e.exports.Volume_input={en:["Volume"],id_ID:["Volume"]},e.exports["Value Area volume_input"]={en:["Value Area volume"],id_ID:["Volume Area Nilai"]},e.exports["Extend Right_input"]={en:["Extend Right"],id_ID:["Perpanjang Kanan"]},e.exports["Extend POC Right_input"]={en:["Extend POC Right"],id_ID:["Perpanjang POC Kekanan"]},e.exports["Extend VAH Right_input"]={en:["Extend VAH Right"],id_ID:["Perpanjangan VAH ke Kanan"]},e.exports["Extend VAL Right_input"]={en:["Extend VAL Right"],id_ID:["Perpanjangan VAL ke Kanan"]},e.exports["Value Area Volume_input"]={en:["Value Area Volume"],id_ID:["Volume Area Nilai"]},e.exports.Placement_input={en:["Placement"],id_ID:["Penempatan"]},e.exports.POC_input={en:["POC"],id_ID:["POC"]},e.exports["Developing Poc_input"]={en:["Developing Poc"],id_ID:["POC Berjalan"]},e.exports["Up Volume_input"]={en:["Up Volume"],id_ID:["Volume Naik"]},e.exports["Down Volume_input"]={en:["Down Volume"],id_ID:["Volume Turun"]},e.exports["Value Area_input"]={en:["Value Area"],id_ID:["Area Nilai"]},e.exports["Histogram Box_input"]={en:["Histogram Box"],
id_ID:["Kotak Histogram"]},e.exports["Value Area Up_input"]={en:["Value Area Up"],id_ID:["Area Nilai Naik"]},e.exports["Value Area Down_input"]={en:["Value Area Down"],id_ID:["Area Nilai Turun"]},e.exports["Number Of Rows_input"]={en:["Number Of Rows"],id_ID:["Jumlah Baris"]},e.exports["Ticks Per Row_input"]={en:["Ticks Per Row"],id_ID:["Tick Per Baris"]},e.exports["Up/Down_input"]={en:["Up/Down"],id_ID:["Naik/Turun"]},e.exports.Total_input={en:["Total"],id_ID:["Total"]},e.exports.Delta_input={en:["Delta"],id_ID:["Delta"]},e.exports.Bar_input={en:["Bar"],id_ID:["Bar"]},e.exports.Day_input={en:["Day"],id_ID:["Hari"]},e.exports["Deviation (%)_input"]={en:["Deviation (%)"],id_ID:["Deviasi (%)"]},e.exports.Depth_input={en:["Depth"],id_ID:["Kedalaman"]},e.exports["Extend to last bar_input"]={en:["Extend to last bar"],id_ID:["Perpanjang hingga bar terakhir"]},e.exports.Simple_input={en:["Simple"],id_ID:["Sederhana"]},e.exports.Weighted_input={en:["Weighted"],id_ID:["Terbebani"]},e.exports["Wilder's Smoothing_input"]={en:["Wilder's Smoothing"],id_ID:["Wilder's Smoothing"]},e.exports["1st Period_input"]={en:["1st Period"],id_ID:["Periode ke-1"]},e.exports["2nd Period_input"]={en:["2nd Period"],id_ID:["Periode ke-2"]},e.exports["3rd Period_input"]={en:["3rd Period"],id_ID:["Periode ke-3"]},e.exports["4th Period_input"]={en:["4th Period"],id_ID:["Periode ke-4"]},e.exports["5th Period_input"]={en:["5th Period"],id_ID:["Periode ke-5"]},e.exports["6th Period_input"]={en:["6th Period"],id_ID:["Periode ke-6"]},e.exports["Rate of Change Lookback_input"]={en:["Rate of Change Lookback"],id_ID:["Kilas balik Kecepatan Perubahan"]},e.exports["Instrument 1_input"]={en:["Instrument 1"],id_ID:["Instrumen 1"]},e.exports["Instrument 2_input"]={en:["Instrument 2"],id_ID:["Instrumen 2"]},e.exports["Rolling Period_input"]={en:["Rolling Period"],id_ID:["Periode Bergulir"]},e.exports["Standard Errors_input"]={en:["Standard Errors"],id_ID:["Standar Error"]},e.exports["Averaging Periods_input"]={en:["Averaging Periods"],id_ID:["Periode Perata-Rataan"]},e.exports["Days Per Year_input"]={en:["Days Per Year"],id_ID:["Hari Per Tahun"]},e.exports["Market Closed Percentage_input"]={en:["Market Closed Percentage"],id_ID:["Persentasi Penutupan Pasar"]},e.exports["ATR Mult_input"]={en:["ATR Mult"],id_ID:["ATR Mult"]},e.exports.VWAP_input={en:["VWAP"],id_ID:["VWAP"]},e.exports["Anchor Period_input"]={en:["Anchor Period"],id_ID:["Periode Jangkar"]},e.exports.Session_input={en:["Session"],id_ID:["Sesi"]},e.exports.Week_input={en:["Week"],id_ID:["Minggu"]},e.exports.Month_input={en:["Month"],id_ID:["Bulan"]},e.exports.Year_input={en:["Year"],id_ID:["Tahun"]},e.exports.Decade_input={en:["Decade"],id_ID:["Dekade"]},e.exports.Century_input={en:["Century"],id_ID:["Abad"]},e.exports.Sessions_input={en:["Sessions"],id_ID:["Sesi"]},e.exports["Each (pre-market, market, post-market)_input"]={en:["Each (pre-market, market, post-market)"],id_ID:["Masing-masing (pra-pasar, pasar, pasca-pasar)"]},e.exports["Pre-market only_input"]={en:["Pre-market only"],
id_ID:["Hanya Pra-pasar"]},e.exports["Market only_input"]={en:["Market only"],id_ID:["Hanya jam pasar"]},e.exports["Post-market only_input"]={en:["Post-market only"],id_ID:["Hanya Pasca-pasar"]},e.exports["Main chart symbol_input"]={en:["Main chart symbol"],id_ID:["Simbol chart utama"]},e.exports["Another symbol_input"]={en:["Another symbol"],id_ID:["Simbol lainnya"]},e.exports.Line_input={en:["Line"],id_ID:["Garis"]},e.exports["Nothing selected_combobox_input"]={en:["Nothing selected"],id_ID:["Tidak ada yang dipilih"]},e.exports["All items_combobox_input"]={en:["All items"],id_ID:["Seluruh item"]},e.exports.Cancel_input={en:["Cancel"],id_ID:["Batalkan"]},e.exports.Open_input={en:["Open"],id_ID:["Buka"]},e.exports.MM_month_input={en:["MM"],id_ID:["MM"]},e.exports.YY_year_input={en:["YY"],id_ID:["YY"]},e.exports.Style_input={en:["Style"],id_ID:["Corak"]},e.exports["Box size assignment method_input"]={en:["Box size assignment method"],id_ID:["Metode penempatan ukuran kotak"]},e.exports["Color bars based on previous close_input"]={en:["Color bars based on previous close"],id_ID:["Warnai bar berdasarkan penutupan sebelumnya"]},e.exports.Candles_input={en:["Candles"],id_ID:["Candle"]},e.exports.Borders_input={en:["Borders"],id_ID:["Batas-Batas"]},e.exports.Wick_input={en:["Wick"],id_ID:["Sumbu"]},e.exports["HLC bars_input"]={en:["HLC bars"],id_ID:["Bar HLC"]},e.exports["Price source_input"]={en:["Price source"],id_ID:["Sumber harga"]},e.exports.Type_input={en:["Type"],id_ID:["Tipe"]},e.exports["Show real prices on price scale (instead of Heikin-Ashi price)_input"]={en:["Show real prices on price scale (instead of Heikin-Ashi price)"],id_ID:["Tampilkan harga sebenarnya pada skala harga (bukan harga Heikin-Ashi)"]},e.exports["Up bars_input"]={en:["Up bars"],id_ID:["Bar naik"]},e.exports["Down bars_input"]={en:["Down bars"],id_ID:["Bar turun"]},e.exports["Projection up bars_input"]={en:["Projection up bars"],id_ID:["Proyeksi bar naik"]},e.exports["Projection down bars_input"]={en:["Projection down bars"],id_ID:["Proyeksi bar turun"]},e.exports["Projection up color_input"]={en:["Projection up color"],id_ID:["Warna proyeksi ke atas"]},e.exports["Projection down color_input"]={en:["Projection down color"],id_ID:["Warna proyeksi ke bawah"]},e.exports.Fill_input={en:["Fill"],id_ID:["Isian"]},e.exports["Up color_input"]={en:["Up color"],id_ID:["Warna Naik"]},e.exports["Down color_input"]={en:["Down color"],id_ID:["Warna Turun"]},e.exports.Traditional_input={en:["Traditional"],id_ID:["Tradisional"]},e.exports.PercentageLTP_input={en:["PercentageLTP"],id_ID:["PesentaseLTP"]},e.exports["Box size_input"]={en:["Box size"],id_ID:["Ukuran kotak"]},e.exports["Number of line_input"]={en:["Number of line"],id_ID:["Jumlah garis"]},e.exports["ATR length_input"]={en:["ATR length"],id_ID:["Panjang ATR"]},e.exports.Percentage_input={en:["Percentage"],id_ID:["Persentase"]},e.exports["Reversal amount_input"]={en:["Reversal amount"],id_ID:["Jumlah Pembalikan"]},e.exports["Phantom bars_input"]={en:["Phantom bars"],id_ID:["Bar bayangan"]},
e.exports["One step back building_input"]={en:["One step back building"],id_ID:["Pembuatan One Step Back"]},e.exports.Wicks_input={en:["Wicks"],id_ID:["Sumbu"]},e.exports.Range_input={en:["Range"],id_ID:["Rentang"]},e.exports.All_input={en:["All"],id_ID:["Semua"]},e.exports.Custom_input={en:["Custom"],id_ID:["Kustom"]},e.exports["Leading Span Periods_input"]={en:["Leading Span Periods"],id_ID:["Periode Leading Span"]},e.exports["Lagging Span Periods_input"]={en:["Lagging Span Periods"],id_ID:["Lagging Span Periods"]},e.exports["Leading Shift Periods_input"]={en:["Leading Shift Periods"],id_ID:["Periode Shift Leading"]},e.exports["ATR({atrValue})_input"]={en:["ATR({atrValue})"],id_ID:["ATR({atrValue})"]},e.exports["PercentageLTP({percentageLTPValue}%)_input"]={en:["PercentageLTP({percentageLTPValue}%)"],id_ID:["PersentaseLTP({percentageLTPValue}%)"]}},50873:e=>{e.exports={en:["ATR({atrValue})"],id_ID:["ATR({atrValue})"]}},28037:e=>{e.exports={en:["PercentageLTP({percentageLTPValue}%)"],id_ID:["PersentaseLTP({percentageLTPValue}%)"]}},40566:e=>{e.exports={en:["Traditional"],id_ID:["Tradisional"]}},75163:e=>{e.exports={en:["Invert scale"],id_ID:["Inversikan Skala"]}},35210:e=>{e.exports={en:["Indexed to 100"],id_ID:["Diindeks ke 100"]}},31340:e=>{e.exports={en:["Logarithmic"],id_ID:["Logaritmik"]}},19405:e=>{e.exports={en:["No overlapping labels"],id_ID:["Tidak ada label yang tertumpuk"]}},34954:e=>{e.exports={en:["Percent"],id_ID:["Persen"]}},55300:e=>{e.exports={en:["Regular"],id_ID:["Reguler"]}},8029:e=>{e.exports={en:["ETH"],id_ID:["ETH"]}},34647:e=>{e.exports={en:["Electronic trading hours"],id_ID:["Jam Trading Elektronik"]}},36862:e=>{e.exports={en:["Extended trading hours"],id_ID:["Jam trading perpanjangan"]}},7807:e=>{e.exports={en:["POST"],id_ID:["pasca"]}},46273:e=>{e.exports={en:["PRE"],id_ID:["pra"]}},50434:e=>{e.exports={en:["Postmarket"],id_ID:["Pasca pasar"]}},59330:e=>{e.exports={en:["Premarket"],id_ID:["Pra pasar"]}},35342:e=>{e.exports={en:["RTH"],id_ID:["RTH"]}},84246:e=>{e.exports={en:["Regular trading hours"],id_ID:["Jam Trading Reguler"]}},13132:e=>{e.exports={en:["May"],id_ID:["Mei"]}},83477:e=>{e.exports=Object.create(null),e.exports.Technicals_study={en:["Technicals"],id_ID:["Teknikal"]},e.exports["Average Day Range_study"]={en:["Average Day Range"],id_ID:["Rata-Rata Rentang Harian"]},e.exports["Bull Bear Power_study"]={en:["Bull Bear Power"],id_ID:["Kekuatan Bull Bear"]},e.exports["Capital expenditures_study"]={en:["Capital expenditures"],id_ID:["Pengeluaran modal / Capital expenditures"]},e.exports["Cash to debt ratio_study"]={en:["Cash to debt ratio"],id_ID:["Cash to debt ratio"]},e.exports["Debt to EBITDA ratio_study"]={en:["Debt to EBITDA ratio"],id_ID:["Debt to EBITDA ratio"]},e.exports["Directional Movement Index_study"]={en:["Directional Movement Index"],id_ID:["Indeks Arah Pergerakan"]},e.exports.DMI_study={en:["DMI"],id_ID:["DMI"]},e.exports["Dividend payout ratio %_study"]={en:["Dividend payout ratio %"],id_ID:["Dividend payout ratio %"]},e.exports["Equity to assets ratio_study"]={
en:["Equity to assets ratio"],id_ID:["Equity to assets ratio"]},e.exports["Enterprise value to EBIT ratio_study"]={en:["Enterprise value to EBIT ratio"],id_ID:["Enterprise value to EBIT ratio"]},e.exports["Enterprise value to EBITDA ratio_study"]={en:["Enterprise value to EBITDA ratio"],id_ID:["Enterprise value to EBITDA ratio"]},e.exports["Enterprise value to revenue ratio_study"]={en:["Enterprise value to revenue ratio"],id_ID:["Enterprise value to revenue ratio"]},e.exports["Goodwill, net_study"]={en:["Goodwill, net"],id_ID:["Goodwill, net"]},e.exports["Ichimoku Cloud_study"]={en:["Ichimoku Cloud"],id_ID:["Awan Ichimoku"]},e.exports.Ichimoku_study={en:["Ichimoku"],id_ID:["Ichimoku"]},e.exports["Moving Average Convergence Divergence_study"]={en:["Moving Average Convergence Divergence"],id_ID:["Rata-Rata Pergerakan Konvergen Divergen / Moving Average Convergence Divergence"]},e.exports["Operating income_study"]={en:["Operating income"],id_ID:["Pemasukan operasional"]},e.exports["Price to book ratio_study"]={en:["Price to book ratio"],id_ID:["Price to book ratio"]},e.exports["Price to cash flow ratio_study"]={en:["Price to cash flow ratio"],id_ID:["Price to cash flow ratio"]},e.exports["Price to earnings ratio_study"]={en:["Price to earnings ratio"],id_ID:["Price to earnings ratio"]},e.exports["Price to free cash flow ratio_study"]={en:["Price to free cash flow ratio"],id_ID:["Price to free cash flow ratio"]},e.exports["Price to sales ratio_study"]={en:["Price to sales ratio"],id_ID:["Price to sales ratio"]},e.exports["Float shares outstanding_study"]={en:["Float shares outstanding"],id_ID:["Float shares outstanding"]},e.exports["Total common shares outstanding_study"]={en:["Total common shares outstanding"],id_ID:["Total saham umum beredar / Total common shares outstanding"]},e.exports["Volume Weighted Average Price_study"]={en:["Volume Weighted Average Price"],id_ID:["Harga Rata-Rata Terbebani Volume"]},e.exports["Volume Weighted Moving Average_study"]={en:["Volume Weighted Moving Average"],id_ID:["Rata-Rata Pergerakan Terbebani Volume"]},e.exports["Williams Percent Range_study"]={en:["Williams Percent Range"],id_ID:["Rentang Persentase Williams / Williams Percent Range"]},e.exports.Doji_study={en:["Doji"],id_ID:["Doji"]},e.exports["Spinning Top Black_study"]={en:["Spinning Top Black"],id_ID:["Spinning Top Hitam"]},e.exports["Spinning Top White_study"]={en:["Spinning Top White"],id_ID:["Spinning Top Putih"]},e.exports["Accounts payable_study"]={en:["Accounts payable"],id_ID:["Accounts payable"]},e.exports["Accounts receivables, gross_study"]={en:["Accounts receivables, gross"],id_ID:["Piutang, bruto"]},e.exports["Accounts receivable - trade, net_study"]={en:["Accounts receivable - trade, net"],id_ID:["Accounts receivable - trade, net"]},e.exports.Accruals_study={en:["Accruals"],id_ID:["Accruals"]},e.exports["Accrued payroll_study"]={en:["Accrued payroll"],id_ID:["Accrued payroll"]},e.exports["Accumulated depreciation, total_study"]={en:["Accumulated depreciation, total"],id_ID:["Accumulated depreciation, total"]},
e.exports["Additional paid-in capital/Capital surplus_study"]={en:["Additional paid-in capital/Capital surplus"],id_ID:["Additional paid-in capital/Capital surplus"]},e.exports["After tax other income/expense_study"]={en:["After tax other income/expense"],id_ID:["After tax other income/expense"]},e.exports["Altman Z-score_study"]={en:["Altman Z-score"],id_ID:["Altman Z-score"]},e.exports.Amortization_study={en:["Amortization"],id_ID:["Amortisasi / Amortization"]},e.exports["Amortization of intangibles_study"]={en:["Amortization of intangibles"],id_ID:["Amortisasi barang tak berwujud"]},e.exports["Amortization of deferred charges_study"]={en:["Amortization of deferred charges"],id_ID:["Amortisasi biaya tertangguh"]},e.exports["Asset turnover_study"]={en:["Asset turnover"],id_ID:["Asset turnover"]},e.exports["Average basic shares outstanding_study"]={en:["Average basic shares outstanding"],id_ID:["Average basic shares outstanding"]},e.exports["Bad debt / Doubtful accounts_study"]={en:["Bad debt / Doubtful accounts"],id_ID:["Piutang macet / Piutang ragu-ragu"]},e.exports["Basic EPS_study"]={en:["Basic EPS"],id_ID:["Basic EPS"]},e.exports["Basic earnings per share (Basic EPS)_study"]={en:["Basic earnings per share (Basic EPS)"],id_ID:["Perolehan dasar per saham / basic Earnings per share (Basic EPS)"]},e.exports["Beneish M-score_study"]={en:["Beneish M-score"],id_ID:["Beneish M-score"]},e.exports["Book value per share_study"]={en:["Book value per share"],id_ID:["Book value per share"]},e.exports["Buyback yield %_study"]={en:["Buyback yield %"],id_ID:["Buyback yield %"]},e.exports["Capital and operating lease obligations_study"]={en:["Capital and operating lease obligations"],id_ID:["Capital and operating lease obligations"]},e.exports["Capital expenditures - fixed assets_study"]={en:["Capital expenditures - fixed assets"],id_ID:["Capital expenditures - fixed assets"]},e.exports["Capital expenditures - other assets_study"]={en:["Capital expenditures - other assets"],id_ID:["Capital expenditures - other assets"]},e.exports["Capitalized lease obligations_study"]={en:["Capitalized lease obligations"],id_ID:["Capitalized lease obligations"]},e.exports["Cash and short term investments_study"]={en:["Cash and short term investments"],id_ID:["Cash and short term investments"]},e.exports["Cash conversion cycle_study"]={en:["Cash conversion cycle"],id_ID:["Cash conversion cycle"]},e.exports["Cash & equivalents_study"]={en:["Cash & equivalents"],id_ID:["Cash & equivalents"]},e.exports["Cash from financing activities_study"]={en:["Cash from financing activities"],id_ID:["Kas dari aktivitas pembiayaan"]},e.exports["Cash from investing activities_study"]={en:["Cash from investing activities"],id_ID:["Kas dari aktivitas investasi"]},e.exports["Cash from operating activities_study"]={en:["Cash from operating activities"],id_ID:["Kas dari aktivitas operasional"]},e.exports["Change in accounts payable_study"]={en:["Change in accounts payable"],id_ID:["Change in accounts payable"]},e.exports["Change in accounts receivable_study"]={
en:["Change in accounts receivable"],id_ID:["Change in accounts receivable"]},e.exports["Change in accrued expenses_study"]={en:["Change in accrued expenses"],id_ID:["Change in accrued expenses"]},e.exports["Change in inventories_study"]={en:["Change in inventories"],id_ID:["Change in inventories"]},e.exports["Change in other assets/liabilities_study"]={en:["Change in other assets/liabilities"],id_ID:["Change in other assets/liabilities"]},e.exports["Change in taxes payable_study"]={en:["Change in taxes payable"],id_ID:["Change in taxes payable"]},e.exports["Changes in working capital_study"]={en:["Changes in working capital"],id_ID:["Perubahan pada kapital berjalan"]},e.exports["COGS to revenue ratio_study"]={en:["COGS to revenue ratio"],id_ID:["COGS to revenue ratio"]},e.exports["Common dividends paid_study"]={en:["Common dividends paid"],id_ID:["Common dividends paid"]},e.exports["Common equity, total_study"]={en:["Common equity, total"],id_ID:["Common equity, total"]},e.exports["Common stock par/Carrying value_study"]={en:["Common stock par/Carrying value"],id_ID:["Common stock par/Carrying value"]},e.exports["Cost of goods_study"]={en:["Cost of goods"],id_ID:["Biaya barang"]},e.exports["Cost of goods sold_study"]={en:["Cost of goods sold"],id_ID:["Harga pokok penjualan"]},e.exports["Current portion of LT debt and capital leases_study"]={en:["Current portion of LT debt and capital leases"],id_ID:["Current portion of LT debt and capital leases"]},e.exports["Current ratio_study"]={en:["Current ratio"],id_ID:["Current ratio"]},e.exports["Days inventory_study"]={en:["Days inventory"],id_ID:["Days inventory"]},e.exports["Days payable_study"]={en:["Days payable"],id_ID:["Days payable"]},e.exports["Days sales outstanding_study"]={en:["Days sales outstanding"],id_ID:["Days sales outstanding"]},e.exports["Debt to assets ratio_study"]={en:["Debt to assets ratio"],id_ID:["Debt to assets ratio"]},e.exports["Debt to equity ratio_study"]={en:["Debt to equity ratio"],id_ID:["Debt to equity ratio"]},e.exports["Debt to revenue ratio_study"]={en:["Debt to revenue ratio"],id_ID:["Debt to revenue ratio"]},e.exports["Deferred income, current_study"]={en:["Deferred income, current"],id_ID:["Deferred income, current"]},e.exports["Deferred income, non-current_study"]={en:["Deferred income, non-current"],id_ID:["Deferred income, non-current"]},e.exports["Deferred tax assets_study"]={en:["Deferred tax assets"],id_ID:["Deferred tax assets"]},e.exports["Deferred taxes (cash flow)_study"]={en:["Deferred taxes (cash flow)"],id_ID:["Deferred taxes (cash flow)"]},e.exports["Deferred tax liabilities_study"]={en:["Deferred tax liabilities"],id_ID:["Deferred tax liabilities"]},e.exports.Depreciation_study={en:["Depreciation"],id_ID:["Depresiasi"]},e.exports["Deprecation and amortization_study"]={en:["Deprecation and amortization"],id_ID:["Deprecation and amortization"]},e.exports["Depreciation & amortization (cash flow)_study"]={en:["Depreciation & amortization (cash flow)"],id_ID:["Depreciation & amortization (cash flow)"]},
e.exports["Depreciation/depletion_study"]={en:["Depreciation/depletion"],id_ID:["Depreciation/depletion"]},e.exports["Diluted EPS_study"]={en:["Diluted EPS"],id_ID:["EPS Terdilusi"]},e.exports["Diluted earnings per share (Diluted EPS)_study"]={en:["Diluted earnings per share (Diluted EPS)"],id_ID:["Laba Dilusian per Saham (Diluted EPS)"]},e.exports["Diluted net income available to common stockholders_study"]={en:["Diluted net income available to common stockholders"],id_ID:["Diluted net income available to common stockholders"]},e.exports["Diluted shares outstanding_study"]={en:["Diluted shares outstanding"],id_ID:["Diluted shares outstanding"]},e.exports["Dilution adjustment_study"]={en:["Dilution adjustment"],id_ID:["Dilution adjustment"]},e.exports["Discontinued operations_study"]={en:["Discontinued operations"],id_ID:["Discontinued operations"]},e.exports["Dividends payable_study"]={en:["Dividends payable"],id_ID:["Dividends payable"]},e.exports["Dividends per share - common stock primary issue_study"]={en:["Dividends per share - common stock primary issue"],id_ID:["Dividends per share - common stock primary issue"]},e.exports["Dividend yield %_study"]={en:["Dividend yield %"],id_ID:["Dividend yield %"]},e.exports["Earnings yield_study"]={en:["Earnings yield"],id_ID:["Hasil perolehan / Earnings yield"]},e.exports.EBIT_study={en:["EBIT"],id_ID:["EBIT"]},e.exports.EBITDA_study={en:["EBITDA"],id_ID:["EBITDA"]},e.exports["EBITDA margin %_study"]={en:["EBITDA margin %"],id_ID:["EBITDA margin %"]},e.exports["Effective interest rate on debt %_study"]={en:["Effective interest rate on debt %"],id_ID:["Effective interest rate on debt %"]},e.exports["Enterprise value_study"]={en:["Enterprise value"],id_ID:["Enterprise value"]},e.exports["EPS basic one year growth_study"]={en:["EPS basic one year growth"],id_ID:["EPS basic one year growth"]},e.exports["EPS diluted one year growth_study"]={en:["EPS diluted one year growth"],id_ID:["EPS diluted one year growth"]},e.exports["EPS estimates_study"]={en:["EPS estimates"],id_ID:["EPS estimates"]},e.exports["Equity in earnings_study"]={en:["Equity in earnings"],id_ID:["Equity in earnings"]},e.exports["Financing activities – other sources_study"]={en:["Financing activities – other sources"],id_ID:["Financing activities – other sources"]},e.exports["Financing activities – other uses_study"]={en:["Financing activities – other uses"],id_ID:["Financing activities – other uses"]},e.exports["Free cash flow_study"]={en:["Free cash flow"],id_ID:["Arus kas bebas"]},e.exports["Free cash flow margin %_study"]={en:["Free cash flow margin %"],id_ID:["Free cash flow margin %"]},e.exports["Fulmer H factor_study"]={en:["Fulmer H factor"],id_ID:["Fulmer H factor"]},e.exports["Funds from operations_study"]={en:["Funds from operations"],id_ID:["Dana dari operasi"]},e.exports["Goodwill to assets ratio_study"]={en:["Goodwill to assets ratio"],id_ID:["Goodwill to assets ratio"]},e.exports["Graham's number_study"]={en:["Graham's number"],id_ID:["Graham's number"]},e.exports["Gross margin %_study"]={
en:["Gross margin %"],id_ID:["Gross margin %"]},e.exports["Gross profit_study"]={en:["Gross profit"],id_ID:["Profit bruto / Gross profit"]},e.exports["Gross profit to assets ratio_study"]={en:["Gross profit to assets ratio"],id_ID:["Gross profit to assets ratio"]},e.exports["Gross property/plant/equipment_study"]={en:["Gross property/plant/equipment"],id_ID:["Gross property/plant/equipment"]},e.exports.Impairments_study={en:["Impairments"],id_ID:["Penurunan"]},e.exports["Income Tax Credits_study"]={en:["Income Tax Credits"],id_ID:["Kredit Pajak Penghasilan"]},e.exports["Income tax, current_study"]={en:["Income tax, current"],id_ID:["Pajak penghasilan, saat ini"]},e.exports["Income tax, current - domestic_study"]={en:["Income tax, current - domestic"],id_ID:["Pajak penghasilan, saat ini - domestik"]},e.exports["Income Tax, current - foreign_study"]={en:["Income Tax, current - foreign"],id_ID:["Pajak penghasilan, saat ini - luar negeri"]},e.exports["Income tax, deferred_study"]={en:["Income tax, deferred"],id_ID:["Pajak penghasilan, ditangguhkan"]},e.exports["Income tax, deferred - domestic_study"]={en:["Income tax, deferred - domestic"],id_ID:["Pajak penghasilan, ditangguhkan - domestik"]},e.exports["Income tax, deferred - foreign_study"]={en:["Income tax, deferred - foreign"],id_ID:["Pajak penghasilan, ditangguhkan - luar negeri"]},e.exports["Income tax payable_study"]={en:["Income tax payable"],id_ID:["Income tax payable"]},e.exports["Interest capitalized_study"]={en:["Interest capitalized"],id_ID:["Interest capitalized"]},e.exports["Interest coverage_study"]={en:["Interest coverage"],id_ID:["Interest coverage"]},e.exports["Interest expense, net of interest capitalized_study"]={en:["Interest expense, net of interest capitalized"],id_ID:["Interest expense, net of interest capitalized"]},e.exports["Interest expense on debt_study"]={en:["Interest expense on debt"],id_ID:["Interest expense on debt"]},e.exports["Inventories - finished goods_study"]={en:["Inventories - finished goods"],id_ID:["Inventories - finished goods"]},e.exports["Inventories - progress payments & other_study"]={en:["Inventories - progress payments & other"],id_ID:["Inventories - progress payments & other"]},e.exports["Inventories - raw materials_study"]={en:["Inventories - raw materials"],id_ID:["Inventories - raw materials"]},e.exports["Inventories - work in progress_study"]={en:["Inventories - work in progress"],id_ID:["Inventories - work in progress"]},e.exports["Inventory to revenue ratio_study"]={en:["Inventory to revenue ratio"],id_ID:["Inventory to revenue ratio"]},e.exports["Inventory turnover_study"]={en:["Inventory turnover"],id_ID:["Inventory turnover"]},e.exports["Investing activities – other sources_study"]={en:["Investing activities – other sources"],id_ID:["Investing activities – other sources"]},e.exports["Investing activities – other uses_study"]={en:["Investing activities – other uses"],id_ID:["Investing activities – other uses"]},e.exports["Investments in unconsolidated subsidiaries_study"]={
en:["Investments in unconsolidated subsidiaries"],id_ID:["Investments in unconsolidated subsidiaries"]},e.exports["Issuance of long term debt_study"]={en:["Issuance of long term debt"],id_ID:["Issuance of long term debt"]},e.exports["Issuance/retirement of debt, net_study"]={en:["Issuance/retirement of debt, net"],id_ID:["Issuance/retirement of debt, net"]},e.exports["Issuance/retirement of long term debt_study"]={en:["Issuance/retirement of long term debt"],id_ID:["Issuance/retirement of long term debt"]},e.exports["Issuance/retirement of other debt_study"]={en:["Issuance/retirement of other debt"],id_ID:["Issuance/retirement of other debt"]},e.exports["Issuance/retirement of short term debt_study"]={en:["Issuance/retirement of short term debt"],id_ID:["Issuance/retirement of short term debt"]},e.exports["Issuance/retirement of stock, net_study"]={en:["Issuance/retirement of stock, net"],id_ID:["Issuance/retirement of stock, net"]},e.exports["KZ index_study"]={en:["KZ index"],id_ID:["KZ index"]},e.exports["Legal claim expense_study"]={en:["Legal claim expense"],id_ID:["Biaya klaim hukum"]},e.exports["Long term debt_study"]={en:["Long term debt"],id_ID:["Hutang jangka panjang"]},e.exports["Long term debt excl. lease liabilities_study"]={en:["Long term debt excl. lease liabilities"],id_ID:["Long term debt excl. lease liabilities"]},e.exports["Long term debt to total assets ratio_study"]={en:["Long term debt to total assets ratio"],id_ID:["Long term debt to total assets ratio"]},e.exports["Long term debt to total equity ratio_study"]={en:["Long term debt to total equity ratio"],id_ID:["Rasio utang jangka panjang terhadap total ekuitas"]},e.exports["Long term investments_study"]={en:["Long term investments"],id_ID:["Long term investments"]},e.exports["Market capitalization_study"]={en:["Market capitalization"],id_ID:["Kapitalisasi pasar"]},e.exports["Minority interest_study"]={en:["Minority interest"],id_ID:["Suku bunga minoritas / Minority interest"]},e.exports["Miscellaneous non-operating expense_study"]={en:["Miscellaneous non-operating expense"],id_ID:["Miscellaneous non-operating expense"]},e.exports["Net current asset value per share_study"]={en:["Net current asset value per share"],id_ID:["Net current asset value per share"]},e.exports["Net debt_study"]={en:["Net debt"],id_ID:["Hutang Bersih"]},e.exports["Net income_study"]={en:["Net income"],id_ID:["Pendapatan netto"]},e.exports["Net income before discontinued operations_study"]={en:["Net income before discontinued operations"],id_ID:["Net income before discontinued operations"]},e.exports["Net income (cash flow)_study"]={en:["Net income (cash flow)"],id_ID:["Net income (cash flow)"]},e.exports["Net income per employee_study"]={en:["Net income per employee"],id_ID:["Net income per employee"]},e.exports["Net intangible assets_study"]={en:["Net intangible assets"],id_ID:["Net intangible assets"]},e.exports["Net margin %_study"]={en:["Net margin %"],id_ID:["Net margin %"]},e.exports["Net property/plant/equipment_study"]={en:["Net property/plant/equipment"],
id_ID:["Net property/plant/equipment"]},e.exports["Non-cash items_study"]={en:["Non-cash items"],id_ID:["Non-cash items"]},e.exports["Non-controlling/minority interest_study"]={en:["Non-controlling/minority interest"],id_ID:["Non-controlling/minority interest"]},e.exports["Non-operating income, excl. interest expenses_study"]={en:["Non-operating income, excl. interest expenses"],id_ID:["Non-operating income, excl. interest expenses"]},e.exports["Non-operating income, total_study"]={en:["Non-operating income, total"],id_ID:["Non-operating income, total"]},e.exports["Non-operating interest income_study"]={en:["Non-operating interest income"],id_ID:["Non-operating interest income"]},e.exports["Note receivable - long term_study"]={en:["Note receivable - long term"],id_ID:["Note receivable - long term"]},e.exports["Notes payable_study"]={en:["Notes payable"],id_ID:["Notes payable"]},e.exports["Number of employees_study"]={en:["Number of employees"],id_ID:["Number of employees"]},e.exports["Number of shareholders_study"]={en:["Number of shareholders"],id_ID:["Jumlah pemegang saham"]},e.exports["Operating earnings yield %_study"]={en:["Operating earnings yield %"],id_ID:["Operating earnings yield %"]},e.exports["Operating expenses (excl. COGS)_study"]={en:["Operating expenses (excl. COGS)"],id_ID:["Operating expenses (excl. COGS)"]},e.exports["Operating lease liabilities_study"]={en:["Operating lease liabilities"],id_ID:["Operating lease liabilities"]},e.exports["Operating margin %_study"]={en:["Operating margin %"],id_ID:["Operating margin %"]},e.exports["Other COGS_study"]={en:["Other COGS"],id_ID:["Other COGS"]},e.exports["Other common equity_study"]={en:["Other common equity"],id_ID:["Other common equity"]},e.exports["Other current assets, total_study"]={en:["Other current assets, total"],id_ID:["Other current assets, total"]},e.exports["Other current liabilities_study"]={en:["Other current liabilities"],id_ID:["Other current liabilities"]},e.exports["Other cost of goods sold_study"]={en:["Other cost of goods sold"],id_ID:["Harga pokok penjualan lainnya"]},e.exports["Other exceptional charges_study"]={en:["Other exceptional charges"],id_ID:["Biaya eksepsional lainnya"]},e.exports["Other financing cash flow items, total_study"]={en:["Other financing cash flow items, total"],id_ID:["Barang arus kas pembiayaan lainnya, total"]},e.exports["Other intangibles, net_study"]={en:["Other intangibles, net"],id_ID:["Other intangibles, net"]},e.exports["Other investing cash flow items, total_study"]={en:["Other investing cash flow items, total"],id_ID:["Barang arus kas investai lainnya, total"]},e.exports["Other investments_study"]={en:["Other investments"],id_ID:["Other investments"]},e.exports["Other liabilities, total_study"]={en:["Other liabilities, total"],id_ID:["Other liabilities, total"]},e.exports["Other long term assets, total_study"]={en:["Other long term assets, total"],id_ID:["Other long term assets, total"]},e.exports["Other non-current liabilities, total_study"]={en:["Other non-current liabilities, total"],
id_ID:["Kewajiban tidak lancar lainnya, total"]},e.exports["Other operating expenses, total_study"]={en:["Other operating expenses, total"],id_ID:["Other operating expenses, total"]},e.exports["Other receivables_study"]={en:["Other receivables"],id_ID:["Other receivables"]},e.exports["Other short term debt_study"]={en:["Other short term debt"],id_ID:["Other short term debt"]},e.exports["Paid in capital_study"]={en:["Paid in capital"],id_ID:["Paid in capital"]},e.exports["PEG ratio_study"]={en:["PEG ratio"],id_ID:["PEG ratio"]},e.exports["Piotroski F-score_study"]={en:["Piotroski F-score"],id_ID:["Piotroski F-score"]},e.exports["Preferred dividends_study"]={en:["Preferred dividends"],id_ID:["Preferred dividends"]},e.exports["Preferred dividends paid_study"]={en:["Preferred dividends paid"],id_ID:["Preferred dividends paid"]},e.exports["Preferred stock, carrying value_study"]={en:["Preferred stock, carrying value"],id_ID:["Preferred stock, carrying value"]},e.exports["Prepaid expenses_study"]={en:["Prepaid expenses"],id_ID:["Prepaid expenses"]},e.exports["Pretax equity in earnings_study"]={en:["Pretax equity in earnings"],id_ID:["Pretax equity in earnings"]},e.exports["Pretax income_study"]={en:["Pretax income"],id_ID:["Pretax income"]},e.exports["Price earnings ratio forward_study"]={en:["Price earnings ratio forward"],id_ID:["Price earnings ratio forward"]},e.exports["Price sales ratio forward_study"]={en:["Price sales ratio forward"],id_ID:["Price sales ratio forward"]},e.exports["Price to tangible book ratio_study"]={en:["Price to tangible book ratio"],id_ID:["Price to tangible book ratio"]},e.exports["Provision for risks & charge_study"]={en:["Provision for risks & charge"],id_ID:["Provision for risks & charge"]},e.exports["Purchase/acquisition of business_study"]={en:["Purchase/acquisition of business"],id_ID:["Purchase/acquisition of business"]},e.exports["Purchase of investments_study"]={en:["Purchase of investments"],id_ID:["Purchase of investments"]},e.exports["Purchase/sale of business, net_study"]={en:["Purchase/sale of business, net"],id_ID:["Pembelian/penjualan dari bisnis, netto"]},e.exports["Purchase/sale of investments, net_study"]={en:["Purchase/sale of investments, net"],id_ID:["Pembelian/Penjualan dari investasi, netto"]},e.exports["Quality ratio_study"]={en:["Quality ratio"],id_ID:["Quality ratio"]},e.exports["Quick ratio_study"]={en:["Quick ratio"],id_ID:["Quick ratio"]},e.exports["Reduction of long term debt_study"]={en:["Reduction of long term debt"],id_ID:["Reduction of long term debt"]},e.exports["Repurchase of common & preferred stock_study"]={en:["Repurchase of common & preferred stock"],id_ID:["Repurchase of common & preferred stock"]},e.exports["Research & development_study"]={en:["Research & development"],id_ID:["Riset & pengembangan"]},e.exports["Research & development to revenue ratio_study"]={en:["Research & development to revenue ratio"],id_ID:["Research & development to revenue ratio"]},e.exports["Restructuring charge_study"]={en:["Restructuring charge"],
id_ID:["Biaya restrukturisasi"]},e.exports["Retained earnings_study"]={en:["Retained earnings"],id_ID:["Retained earnings"]},e.exports["Return on assets %_study"]={en:["Return on assets %"],id_ID:["Return on assets %"]},e.exports["Return on equity %_study"]={en:["Return on equity %"],id_ID:["Return on equity %"]},e.exports["Return on equity adjusted to book value %_study"]={en:["Return on equity adjusted to book value %"],id_ID:["Return on equity adjusted to book value %"]},e.exports["Return on invested capital %_study"]={en:["Return on invested capital %"],id_ID:["Return on invested capital %"]},e.exports["Return on tangible assets %_study"]={en:["Return on tangible assets %"],id_ID:["Return on tangible assets %"]},e.exports["Return on tangible equity %_study"]={en:["Return on tangible equity %"],id_ID:["Return on tangible equity %"]},e.exports["Revenue estimates_study"]={en:["Revenue estimates"],id_ID:["Revenue estimates"]},e.exports["Revenue one year growth_study"]={en:["Revenue one year growth"],id_ID:["Revenue one year growth"]},e.exports["Revenue per employee_study"]={en:["Revenue per employee"],id_ID:["Revenue per employee"]},e.exports["Sale/maturity of investments_study"]={en:["Sale/maturity of investments"],id_ID:["Sale/maturity of investments"]},e.exports["Sale of common & preferred stock_study"]={en:["Sale of common & preferred stock"],id_ID:["Sale of common & preferred stock"]},e.exports["Sale of fixed assets & businesses_study"]={en:["Sale of fixed assets & businesses"],id_ID:["Sale of fixed assets & businesses"]},e.exports["Selling/general/admin expenses, other_study"]={en:["Selling/general/admin expenses, other"],id_ID:["Pengeluaran penjualan/umum/admin, lainnya"]},e.exports["Selling/general/admin expenses, total_study"]={en:["Selling/general/admin expenses, total"],id_ID:["Pengeluaran penjualan/umum/admin, total"]},e.exports["Shareholders' equity_study"]={en:["Shareholders' equity"],id_ID:["Shareholders' equity"]},e.exports["Shares buyback ratio %_study"]={en:["Shares buyback ratio %"],id_ID:["Shares buyback ratio %"]},e.exports["Short term debt_study"]={en:["Short term debt"],id_ID:["Short term debt"]},e.exports["Short term debt excl. current portion of LT debt_study"]={en:["Short term debt excl. current portion of LT debt"],id_ID:["Short term debt excl. current portion of LT debt"]},e.exports["Short term investments_study"]={en:["Short term investments"],id_ID:["Short term investments"]},e.exports["Sloan ratio %_study"]={en:["Sloan ratio %"],id_ID:["Sloan ratio %"]},e.exports["Springate score_study"]={en:["Springate score"],id_ID:["Springate score"]},e.exports["Sustainable growth rate_study"]={en:["Sustainable growth rate"],id_ID:["Sustainable growth rate"]},e.exports["Tangible book value per share_study"]={en:["Tangible book value per share"],id_ID:["Tangible book value per share"]},e.exports["Tangible common equity ratio_study"]={en:["Tangible common equity ratio"],id_ID:["Tangible common equity ratio"]},e.exports.Taxes_study={en:["Taxes"],id_ID:["Taxes"]},
e.exports["Tobin's Q (approximate)_study"]={en:["Tobin's Q (approximate)"],id_ID:["Tobin's Q (approximate)"]},e.exports["Total assets_study"]={en:["Total assets"],id_ID:["Total aset"]},e.exports["Total cash dividends paid_study"]={en:["Total cash dividends paid"],id_ID:["Total dividen tunai dibayarkan / Total cash dividends paid"]},e.exports["Total current assets_study"]={en:["Total current assets"],id_ID:["Total aset saat ini"]},e.exports["Total current liabilities_study"]={en:["Total current liabilities"],id_ID:["Total liabilitas saat ini"]},e.exports["Total debt_study"]={en:["Total debt"],id_ID:["Total hutang"]},e.exports["Total equity_study"]={en:["Total equity"],id_ID:["Total ekuitas"]},e.exports["Total inventory_study"]={en:["Total inventory"],id_ID:["Total inventory"]},e.exports["Total liabilities_study"]={en:["Total liabilities"],id_ID:["Total liabilitas"]},e.exports["Total liabilities & shareholders' equities_study"]={en:["Total liabilities & shareholders' equities"],id_ID:["Total liabilities & shareholders' equities"]},e.exports["Total non-current assets_study"]={en:["Total non-current assets"],id_ID:["Total aset tidak lancar"]},e.exports["Total non-current liabilities_study"]={en:["Total non-current liabilities"],id_ID:["Total liabilitas tidak lancar"]},e.exports["Total operating expenses_study"]={en:["Total operating expenses"],id_ID:["Total pengeluaran operasi"]},e.exports["Total receivables, net_study"]={en:["Total receivables, net"],id_ID:["Total receivables, net"]},e.exports["Total revenue_study"]={en:["Total revenue"],id_ID:["Total pendapatan"]},e.exports["Treasury stock - common_study"]={en:["Treasury stock - common"],id_ID:["Treasury stock - common"]},e.exports["Unrealized gain/loss_study"]={en:["Unrealized gain/loss"],id_ID:["Keuntungan/kerugian yang belum terealisasi"]},e.exports["Unusual income/expense_study"]={en:["Unusual income/expense"],id_ID:["Unusual income/expense / Pemasukan/pengeluaran tidak biasa"]},e.exports["Zmijewski score_study"]={en:["Zmijewski score"],id_ID:["Zmijewski score / Skor Zmijewski"]},e.exports["Valuation ratios_study"]={en:["Valuation ratios"],id_ID:["Rasio valuasi"]},e.exports["Profitability ratios_study"]={en:["Profitability ratios"],id_ID:["Rasio profitabilitas"]},e.exports["Liquidity ratios_study"]={en:["Liquidity ratios"],id_ID:["Rasio likuiditas"]},e.exports["Solvency ratios_study"]={en:["Solvency ratios"],id_ID:["Rasio solvabilitas"]},e.exports["Key stats_study"]={en:["Key stats"],id_ID:["Statistik kunci"]},e.exports["Accumulation/Distribution_study"]={en:["Accumulation/Distribution"],id_ID:["Akumulasi/Distribusi"]},e.exports["Accumulative Swing Index_study"]={en:["Accumulative Swing Index"],id_ID:["Indeks Swing Akumulatif / Accumulative Swing Index"]},e.exports["Advance/Decline_study"]={en:["Advance/Decline"],id_ID:["Kemajuan/Kemunduran / Advance/Decline"]},e.exports["All Chart Patterns_study"]={en:["All Chart Patterns"],id_ID:["Seluruh Motif Chart"]},e.exports["Arnaud Legoux Moving Average_study"]={en:["Arnaud Legoux Moving Average"],
id_ID:["Rata-Rata Pergerakan Arnaud Legoux / Arnaud Legoux Moving Average"]},e.exports.Aroon_study={en:["Aroon"],id_ID:["Aroon"]},e.exports.ASI_study={en:["ASI"],id_ID:["ASI"]},e.exports["Average Directional Index_study"]={en:["Average Directional Index"],id_ID:["Indeks Arah Rata-Rata / Average Directional Index"]},e.exports["Average True Range_study"]={en:["Average True Range"],id_ID:["Rata-Rata Rentang Sebenarnya / Average True Range"]},e.exports["Awesome Oscillator_study"]={en:["Awesome Oscillator"],id_ID:["Osilator Awesome / Awesome Oscillator"]},e.exports["Balance of Power_study"]={en:["Balance of Power"],id_ID:["Keseimbangan dari Kekuatan / Balance of Power"]},e.exports["Bollinger Bands %B_study"]={en:["Bollinger Bands %B"],id_ID:["%B Ikat Bollinger"]},e.exports["Bollinger Bands Width_study"]={en:["Bollinger Bands Width"],id_ID:["Lebar Ikat Bollinger"]},e.exports["Bollinger Bands_study"]={en:["Bollinger Bands"],id_ID:["Ikat Bollinger / Bollinger Bands"]},e.exports["Chaikin Money Flow_study"]={en:["Chaikin Money Flow"],id_ID:["Arus Uang Chaikin / Chaikin Money Flow"]},e.exports["Chaikin Oscillator_study"]={en:["Chaikin Oscillator"],id_ID:["Osilator Chaikin"]},e.exports["Chande Kroll Stop_study"]={en:["Chande Kroll Stop"],id_ID:["Chande Kroll Stop"]},e.exports["Chande Momentum Oscillator_study"]={en:["Chande Momentum Oscillator"],id_ID:["Osilator Momentum Chande"]},e.exports["Chop Zone_study"]={en:["Chop Zone"],id_ID:["Zona Chop"]},e.exports["Choppiness Index_study"]={en:["Choppiness Index"],id_ID:["Indeks Choppiness / Choppiness Index"]},e.exports["Commodity Channel Index_study"]={en:["Commodity Channel Index"],id_ID:["Indeks Kanal Komoditas / Commodity Channel Index"]},e.exports["Connors RSI_study"]={en:["Connors RSI"],id_ID:["RSI Connors"]},e.exports["Coppock Curve_study"]={en:["Coppock Curve"],id_ID:["Kurva Coppock"]},e.exports["Correlation Coefficient_study"]={en:["Correlation Coefficient"],id_ID:["Koefisien Korelasi"]},e.exports.CRSI_study={en:["CRSI"],id_ID:["CRSI"]},e.exports["Detrended Price Oscillator_study"]={en:["Detrended Price Oscillator"],id_ID:["Osilator Harga Detrended / Detrended Price Oscillator"]},e.exports["Directional Movement_study"]={en:["Directional Movement"],id_ID:["Pergerakan Terarah/ Directional Movement"]},e.exports["Donchian Channels_study"]={en:["Donchian Channels"],id_ID:["Kanal Donchian"]},e.exports["Double EMA_study"]={en:["Double EMA"],id_ID:["Double EMA"]},e.exports["Ease Of Movement_study"]={en:["Ease Of Movement"],id_ID:["Ease Of Movement"]},e.exports["Elder Force Index_study"]={en:["Elder Force Index"],id_ID:["Indeks Kekuatan Elder / Elder Force Index"]},e.exports["EMA Cross_study"]={en:["EMA Cross"],id_ID:["Persilangan EMA"]},e.exports.Envelopes_study={en:["Envelopes"],id_ID:["Envelope"]},e.exports["Fisher Transform_study"]={en:["Fisher Transform"],id_ID:["Transformasi Fisher / Fisher Transform"]},e.exports["Fixed Range_study"]={en:["Fixed Range"],id_ID:["Rentang Tetap"]},e.exports["Fixed Range Volume Profile_study"]={en:["Fixed Range Volume Profile"],
id_ID:["Profil Volume Rentang Tetap"]},e.exports["Guppy Multiple Moving Average_study"]={en:["Guppy Multiple Moving Average"],id_ID:["Rata-Rata Pergerakan Berganda Guppy / Guppy Multiple Moving Average"]},e.exports["Historical Volatility_study"]={en:["Historical Volatility"],id_ID:["Volatilitas Historis"]},e.exports["Hull Moving Average_study"]={en:["Hull Moving Average"],id_ID:["Rata-Rata Pergerakan Hull / Hull Moving Average"]},e.exports["Keltner Channels_study"]={en:["Keltner Channels"],id_ID:["Kanal Keltner"]},e.exports["Klinger Oscillator_study"]={en:["Klinger Oscillator"],id_ID:["Osilator Klinger"]},e.exports["Know Sure Thing_study"]={en:["Know Sure Thing"],id_ID:["Know Sure Thing"]},e.exports["Least Squares Moving Average_study"]={en:["Least Squares Moving Average"],id_ID:["Rata-Rata Pergerakan Kuadrat Terkecil / Least Squares Moving Average"]},e.exports["Linear Regression Curve_study"]={en:["Linear Regression Curve"],id_ID:["Kurva Regresi Linear"]},e.exports["MA Cross_study"]={en:["MA Cross"],id_ID:["Persilangan MA / MA Cross"]},e.exports["MA with EMA Cross_study"]={en:["MA with EMA Cross"],id_ID:["Persilangan MA dengan EMA"]},e.exports["MA/EMA Cross_study"]={en:["MA/EMA Cross"],id_ID:["Persilangan MA/EMA"]},e.exports.MACD_study={en:["MACD"],id_ID:["MACD"]},e.exports["Mass Index_study"]={en:["Mass Index"],id_ID:["Indeks Massa / Mass Index"]},e.exports["McGinley Dynamic_study"]={en:["McGinley Dynamic"],id_ID:["Dinamik McGinley / McGinley Dynamic"]},e.exports.Median_study={en:["Median"],id_ID:["Median"]},e.exports.Momentum_study={en:["Momentum"],id_ID:["Momentum"]},e.exports["Money Flow_study"]={en:["Money Flow"],id_ID:["Arus Uang / Money Flow"]},e.exports["Moving Average Channel_study"]={en:["Moving Average Channel"],id_ID:["Kanal Rata-Rata Pergerakan / Moving Average Channel"]},e.exports["Moving Average Exponential_study"]={en:["Moving Average Exponential"],id_ID:["Rata-Rata Pergerakan Eksponensial / Moving Average Exponential"]},e.exports["Moving Average Weighted_study"]={en:["Moving Average Weighted"],id_ID:["Rata-Rata Pergerakan Terbebani / Moving Average Weighted"]},e.exports["Moving Average Simple_study"]={en:["Moving Average Simple"],id_ID:["Simple Moving Average"]},e.exports["Net Volume_study"]={en:["Net Volume"],id_ID:["Volume Bersih"]},e.exports["On Balance Volume_study"]={en:["On Balance Volume"],id_ID:["Volume Keseimbangan / On Balance Volume"]},e.exports["Parabolic SAR_study"]={en:["Parabolic SAR"],id_ID:["SAR Parabolis"]},e.exports["Pivot Points Standard_study"]={en:["Pivot Points Standard"],id_ID:["Poin Pivot Standar"]},e.exports["Periodic Volume Profile_study"]={en:["Periodic Volume Profile"],id_ID:["Profil Volume Periodik"]},e.exports["Price Channel_study"]={en:["Price Channel"],id_ID:["Kanal Harga"]},e.exports["Price Oscillator_study"]={en:["Price Oscillator"],id_ID:["Osilator Harga"]},e.exports["Price Volume Trend_study"]={en:["Price Volume Trend"],id_ID:["Tren Volume Harga / Price Volume Trend"]},e.exports["Rate Of Change_study"]={en:["Rate Of Change"],id_ID:["Kecepatan Perubahan"]},
e.exports["Relative Strength Index_study"]={en:["Relative Strength Index"],id_ID:["Indeks Kekuatan Relatif / Relative Strength Index"]},e.exports["Relative Vigor Index_study"]={en:["Relative Vigor Index"],id_ID:["Indeks Vigor Relatif / Relative Vigor Index"]},e.exports["Relative Volatility Index_study"]={en:["Relative Volatility Index"],id_ID:["Indeks Volatilitas Relatif / Relative Volatility Index"]},e.exports["Relative Volume at Time_study"]={en:["Relative Volume at Time"],id_ID:["Volume Relatif pada Waktu"]},e.exports["Session Volume_study"]={en:["Session Volume"],id_ID:["Volume Sesi"]},e.exports["Session Volume HD_study"]={en:["Session Volume HD"],id_ID:["Volume Sesi HD"]},e.exports["Session Volume Profile_study"]={en:["Session Volume Profile"],id_ID:["Profil Volume Sesi"]},e.exports["Session Volume Profile HD_study"]={en:["Session Volume Profile HD"],id_ID:["Profil Volume Sesi HD"]},e.exports["SMI Ergodic Indicator/Oscillator_study"]={en:["SMI Ergodic Indicator/Oscillator"],id_ID:["Indikator/Osilator SMI Ergodic"]},e.exports["Smoothed Moving Average_study"]={en:["Smoothed Moving Average"],id_ID:["Rata-Rata Pergerakan Terhaluskan / Smoothed Moving Average"]},e.exports["Stochastic Momentum Index_study"]={en:["Stochastic Momentum Index"],id_ID:["Indeks Momentum Stochastic"]},e.exports.Stoch_study={en:["Stoch"],id_ID:["Stoch"]},e.exports["Stochastic RSI_study"]={en:["Stochastic RSI"],id_ID:["Stochastic RSI"]},e.exports.Stochastic_study={en:["Stochastic"],id_ID:["Stochastic"]},e.exports["Time Weighted Average Price_study"]={en:["Time Weighted Average Price"],id_ID:["Harga Rata-rata Terbebani Waktu"]},e.exports["Triple EMA_study"]={en:["Triple EMA"],id_ID:["Triple EMA"]},e.exports.TRIX_study={en:["TRIX"],id_ID:["TRIX"]},e.exports["True Strength Indicator_study"]={en:["True Strength Indicator"],id_ID:["Indikator Kekuatan Sebenarnya / True Strength Indicator"]},e.exports["Ultimate Oscillator_study"]={en:["Ultimate Oscillator"],id_ID:["Osilator Ultimate"]},e.exports["Visible Range_study"]={en:["Visible Range"],id_ID:["Rentang Terlihat"]},e.exports["Visible Range Volume Profile_study"]={en:["Visible Range Volume Profile"],id_ID:["Profil Volume Rentang Terlihat"]},e.exports["Volume Oscillator_study"]={en:["Volume Oscillator"],id_ID:["Osilator Volume"]},e.exports.Volume_study={en:["Volume"],id_ID:["Volume"]},e.exports.Vol_study={en:["Vol"],id_ID:["Vol"]},e.exports["Vortex Indicator_study"]={en:["Vortex Indicator"],id_ID:["Indikator Vortex"]},e.exports.VWAP_study={en:["VWAP"],id_ID:["VWAP"]},e.exports.VWMA_study={en:["VWMA"],id_ID:["VWMA"]},e.exports["Williams %R_study"]={en:["Williams %R"],id_ID:["%R Williams / Williams %R"]},e.exports["Williams Alligator_study"]={en:["Williams Alligator"],id_ID:["Aligator Williams / Williams Alligator"]},e.exports["Williams Fractal_study"]={en:["Williams Fractal"],id_ID:["Fraktal Williams / Williams Fractal"]},e.exports["Zig Zag_study"]={en:["Zig Zag"],id_ID:["Zig Zag"]},e.exports["24-hour Volume_study"]={en:["24-hour Volume"],id_ID:["Volume 24 Jam"]},e.exports["Ease of Movement_study"]={
en:["Ease of Movement"],id_ID:["Ease Of Movement"]},e.exports["Elders Force Index_study"]={en:["Elders Force Index"],id_ID:["Indeks Kekuatan Elder"]},e.exports.Envelope_study={en:["Envelope"],id_ID:["Envelope"]},e.exports.Gaps_study={en:["Gaps"],id_ID:["Gap"]},e.exports["Linear Regression Channel_study"]={en:["Linear Regression Channel"],id_ID:["Kanal Regresi Linier"]},e.exports["Moving Average Ribbon_study"]={en:["Moving Average Ribbon"],id_ID:["Pita Pergerakan Rata-rata"]},e.exports["Multi-Time Period Charts_study"]={en:["Multi-Time Period Charts"],id_ID:["Chart Periode Waktu Berganda"]},e.exports["Open Interest_study"]={en:["Open Interest"],id_ID:["Minat Terbuka/Open Interest"]},e.exports["Rob Booker - Intraday Pivot Points_study"]={en:["Rob Booker - Intraday Pivot Points"],id_ID:["Rob Booker - Poin Pivot Intrahari"]},e.exports["Rob Booker - Knoxville Divergence_study"]={en:["Rob Booker - Knoxville Divergence"],id_ID:["Rob Booker - Divergen Knoxville"]},e.exports["Rob Booker - Missed Pivot Points_study"]={en:["Rob Booker - Missed Pivot Points"],id_ID:["Rob Booker - Poin Pivot Terlewati"]},e.exports["Rob Booker - Reversal_study"]={en:["Rob Booker - Reversal"],id_ID:["Rob Booker - Reversal"]},e.exports["Rob Booker - Ziv Ghost Pivots_study"]={en:["Rob Booker - Ziv Ghost Pivots"],id_ID:["Rob Booker - Pivot Bayangan / Ziv Ghost Pivots"]},e.exports.Supertrend_study={en:["Supertrend"],id_ID:["Supertrend"]},e.exports["Technical Ratings_study"]={en:["Technical Ratings"],id_ID:["Peringkat Teknikal"]},e.exports["True Strength Index_study"]={en:["True Strength Index"],id_ID:["Indeks Kekuatan Sebenarnya / True Strength Index"]},e.exports["Up/Down Volume_study"]={en:["Up/Down Volume"],id_ID:["Volume Naik/Turun"]},e.exports["Visible Average Price_study"]={en:["Visible Average Price"],id_ID:["Rata-rata Harga Terlihat"]},e.exports["Williams Fractals_study"]={en:["Williams Fractals"],id_ID:["Fraktal Williams"]},e.exports["Keltner Channels Strategy_study"]={en:["Keltner Channels Strategy"],id_ID:["Strategi Kanal Keltner"]},e.exports["Rob Booker - ADX Breakout_study"]={en:["Rob Booker - ADX Breakout"],id_ID:["Rob Booker - Breakout ADX"]},e.exports["Supertrend Strategy_study"]={en:["Supertrend Strategy"],id_ID:["Strategi Supertrend"]},e.exports["Technical Ratings Strategy_study"]={en:["Technical Ratings Strategy"],id_ID:["Strategi Peringkat Teknikal"]},e.exports["Auto Anchored Volume Profile_study"]={en:["Auto Anchored Volume Profile"],id_ID:["Profil Volume Terjangkar Otomatis"]},e.exports["Auto Fib Extension_study"]={en:["Auto Fib Extension"],id_ID:["Fibonacci Ekstension Otomatis"]},e.exports["Auto Fib Retracement_study"]={en:["Auto Fib Retracement"],id_ID:["Fib Retracement Otomatis"]},e.exports["Auto Pitchfork_study"]={en:["Auto Pitchfork"],id_ID:["Pitchfork Otomatis"]},e.exports["Bearish Flag Chart Pattern_study"]={en:["Bearish Flag Chart Pattern"],id_ID:["Motif Chart Bendera / Flag Bearish"]},e.exports["Bullish Flag Chart Pattern_study"]={en:["Bullish Flag Chart Pattern"],id_ID:["Motif Chart Bendera Bullish"]},
e.exports["Bearish Pennant Chart Pattern_study"]={en:["Bearish Pennant Chart Pattern"],id_ID:["Motif Chart Pennant/Panji Bearish"]},e.exports["Bullish Pennant Chart Pattern_study"]={en:["Bullish Pennant Chart Pattern"],id_ID:["Motif Chart Bendera / Flag Bullish"]},e.exports["Double Bottom Chart Pattern_study"]={en:["Double Bottom Chart Pattern"],id_ID:["Motif Chart Double Bottom"]},e.exports["Double Top Chart Pattern_study"]={en:["Double Top Chart Pattern"],id_ID:["Motif Chart Double Top"]},e.exports["Elliott Wave Chart Pattern_study"]={en:["Elliott Wave Chart Pattern"],id_ID:["Motif Chart Gelombang Elliott"]},e.exports["Falling Wedge Chart Pattern_study"]={en:["Falling Wedge Chart Pattern"],id_ID:["Motif Chart Baji / Wedge Menurun"]},e.exports["Head And Shoulders Chart Pattern_study"]={},e.exports["Inverse Head And Shoulders Chart Pattern_study"]={},e.exports["Rectangle Chart Pattern_study"]={en:["Rectangle Chart Pattern"],id_ID:["Motif Chart Persegi"]},e.exports["Rising Wedge Chart Pattern_study"]={en:["Rising Wedge Chart Pattern"],id_ID:["Motif Chart Baji / Wedge Menaik"]},e.exports["Triangle Chart Pattern_study"]={en:["Triangle Chart Pattern"],id_ID:["Motif Chart Segitiga"]},e.exports["Triple Bottom Chart Pattern_study"]={en:["Triple Bottom Chart Pattern"],id_ID:["Motif Chart Triple Bottom"]},e.exports["Triple Top Chart Pattern_study"]={en:["Triple Top Chart Pattern"],id_ID:["Motif Chart Triple Top"]},e.exports["VWAP Auto Anchored_study"]={en:["VWAP Auto Anchored"],id_ID:["VWAP Terjangkar Otomatis"]},e.exports["*All Candlestick Patterns*_study"]={en:["*All Candlestick Patterns*"],id_ID:["*Seluruh Motif Candlestick*"]},e.exports["Abandoned Baby - Bearish_study"]={en:["Abandoned Baby - Bearish"],id_ID:["Abandoned Baby - Bearish"]},e.exports["Abandoned Baby - Bullish_study"]={en:["Abandoned Baby - Bullish"],id_ID:["Abandoned Baby - Bullish"]},e.exports["Dark Cloud Cover - Bearish_study"]={en:["Dark Cloud Cover - Bearish"],id_ID:["Dark Cloud Cover - Bearish"]},e.exports["Doji Star - Bearish_study"]={en:["Doji Star - Bearish"],id_ID:["Doji Star - Bearish"]},e.exports["Doji Star - Bullish_study"]={en:["Doji Star - Bullish"],id_ID:["Doji Star - Bullish"]},e.exports["Downside Tasuki Gap - Bearish_study"]={en:["Downside Tasuki Gap - Bearish"],id_ID:["Downside Tasuki Gap - Bearish"]},e.exports["Dragonfly Doji - Bullish_study"]={en:["Dragonfly Doji - Bullish"],id_ID:["Dragonfly Doji - Bullish"]},e.exports["Engulfing - Bearish_study"]={en:["Engulfing - Bearish"],id_ID:["Engulfing - Bearish"]},e.exports["Engulfing - Bullish_study"]={en:["Engulfing - Bullish"],id_ID:["Engulfing - Bullish"]},e.exports["Evening Doji Star - Bearish_study"]={en:["Evening Doji Star - Bearish"],id_ID:["Evening Doji Star - Bearish"]},e.exports["Evening Star - Bearish_study"]={en:["Evening Star - Bearish"],id_ID:["Evening Star - Bearish"]},e.exports["Falling Three Methods - Bearish_study"]={en:["Falling Three Methods - Bearish"],id_ID:["Falling Three Methods - Bearish"]},e.exports["Falling Window - Bearish_study"]={en:["Falling Window - Bearish"],
id_ID:["Falling Window - Bearish"]},e.exports["Gravestone Doji - Bearish_study"]={en:["Gravestone Doji - Bearish"],id_ID:["Gravestone Doji - Bearish"]},e.exports["Hammer - Bullish_study"]={en:["Hammer - Bullish"],id_ID:["Hammer - Bullish"]},e.exports["Hanging Man - Bearish_study"]={en:["Hanging Man - Bearish"],id_ID:["Hanging Man - Bearish"]},e.exports["Harami - Bearish_study"]={en:["Harami - Bearish"],id_ID:["Harami - Bearish"]},e.exports["Harami - Bullish_study"]={en:["Harami - Bullish"],id_ID:["Harami - Bullish"]},e.exports["Harami Cross - Bearish_study"]={en:["Harami Cross - Bearish"],id_ID:["Harami Cross - Bearish"]},e.exports["Harami Cross - Bullish_study"]={en:["Harami Cross - Bullish"],id_ID:["Harami Cross - Bullish"]},e.exports["Inverted Hammer - Bullish_study"]={en:["Inverted Hammer - Bullish"],id_ID:["Inverted Hammer - Bullish"]},e.exports["Kicking - Bearish_study"]={en:["Kicking - Bearish"],id_ID:["Kicking - Bearish"]},e.exports["Kicking - Bullish_study"]={en:["Kicking - Bullish"],id_ID:["Kicking - Bullish"]},e.exports["Long Lower Shadow - Bullish_study"]={en:["Long Lower Shadow - Bullish"],id_ID:["Long Lower Shadow - Bullish"]},e.exports["Long Upper Shadow - Bearish_study"]={en:["Long Upper Shadow - Bearish"],id_ID:["Long Upper Shadow - Bearish"]},e.exports["Marubozu Black - Bearish_study"]={en:["Marubozu Black - Bearish"],id_ID:["Marubozu Black - Bearish"]},e.exports["Marubozu White - Bullish_study"]={en:["Marubozu White - Bullish"],id_ID:["Marubozu White - Bullish"]},e.exports["Morning Doji Star - Bullish_study"]={en:["Morning Doji Star - Bullish"],id_ID:["Morning Doji Star - Bullish"]},e.exports["Morning Star - Bullish_study"]={en:["Morning Star - Bullish"],id_ID:["Morning Star - Bullish"]},e.exports["On Neck - Bearish_study"]={en:["On Neck - Bearish"],id_ID:["On Neck - Bearish"]},e.exports["Piercing - Bullish_study"]={en:["Piercing - Bullish"],id_ID:["Piercing - Bullish"]},e.exports["Rising Three Methods - Bullish_study"]={en:["Rising Three Methods - Bullish"],id_ID:["Rising Three Methods - Bullish"]},e.exports["Rising Window - Bullish_study"]={en:["Rising Window - Bullish"],id_ID:["Rising Window - Bullish"]},e.exports["Shooting Star - Bearish_study"]={en:["Shooting Star - Bearish"],id_ID:["Shooting Star - Bearish"]},e.exports["Three Black Crows - Bearish_study"]={en:["Three Black Crows - Bearish"],id_ID:["Three Black Crows - Bearish"]},e.exports["Three White Soldiers - Bullish_study"]={en:["Three White Soldiers - Bullish"],id_ID:["Three White Soldiers - Bullish"]},e.exports["Tri-Star - Bearish_study"]={en:["Tri-Star - Bearish"],id_ID:["Tri-Star - Bearish"]},e.exports["Tri-Star - Bullish_study"]={en:["Tri-Star - Bullish"],id_ID:["Tri-Star - Bullish"]},e.exports["Tweezer Top - Bearish_study"]={en:["Tweezer Top - Bearish"],id_ID:["Tweezer Top - Bearish"]},e.exports["Upside Tasuki Gap - Bullish_study"]={en:["Upside Tasuki Gap - Bullish"],id_ID:["Upside Tasuki Gap - Bullish"]},e.exports.SuperTrend_study={en:["SuperTrend"],id_ID:["SuperTrend"]},e.exports["Average Price_study"]={en:["Average Price"],
id_ID:["Harga Rata-Rata"]},e.exports["Typical Price_study"]={en:["Typical Price"],id_ID:["Harga Tipikal"]},e.exports["Median Price_study"]={en:["Median Price"],id_ID:["Harga Median"]},e.exports["Money Flow Index_study"]={en:["Money Flow Index"],id_ID:["Indeks Arus Uang"]},e.exports["Moving Average Double_study"]={en:["Moving Average Double"],id_ID:["Rata-Rata Pergerakan Ganda"]},e.exports["Moving Average Triple_study"]={en:["Moving Average Triple"],id_ID:["Rata-Rata Pergerakan Tripel"]},e.exports["Moving Average Adaptive_study"]={en:["Moving Average Adaptive"],id_ID:["Rata-Rata Pergerakan Adaptif"]},e.exports["Moving Average Hamming_study"]={en:["Moving Average Hamming"],id_ID:["Rata-Rata Pergerakan Hamming"]},e.exports["Moving Average Modified_study"]={en:["Moving Average Modified"],id_ID:["Rata-Rata Pergerakan Termodifikasi"]},e.exports["Moving Average Multiple_study"]={en:["Moving Average Multiple"],id_ID:["Rata-Rata Pergerakan Berganda"]},e.exports["Linear Regression Slope_study"]={en:["Linear Regression Slope"],id_ID:["Kemiringan Regresi Linear"]},e.exports["Standard Error_study"]={en:["Standard Error"],id_ID:["Standar Error"]},e.exports["Standard Error Bands_study"]={en:["Standard Error Bands"],id_ID:["Ikat Standar Error"]},e.exports["Correlation - Log_study"]={en:["Correlation - Log"],id_ID:["Korelasi - Log"]},e.exports["Standard Deviation_study"]={en:["Standard Deviation"],id_ID:["Standar Deviasi"]},e.exports["Chaikin Volatility_study"]={en:["Chaikin Volatility"],id_ID:["Volatilitas Chaikin"]},e.exports["Volatility Close-to-Close_study"]={en:["Volatility Close-to-Close"],id_ID:["Volatilitas Penutupan-ke-Penutupan"]},e.exports["Volatility Zero Trend Close-to-Close_study"]={en:["Volatility Zero Trend Close-to-Close"],id_ID:["Volatilitas Zero Trend Penutupan-ke-Penutupan"]},e.exports["Volatility O-H-L-C_study"]={en:["Volatility O-H-L-C"],id_ID:["Volatilitas O-H-L-C"]},e.exports["Volatility Index_study"]={en:["Volatility Index"],id_ID:["Indeks Volatilitas"]},e.exports["Trend Strength Index_study"]={en:["Trend Strength Index"],id_ID:["Indeks Kekuatan Tren"]},e.exports["Majority Rule_study"]={en:["Majority Rule"],id_ID:["Aturan Mayoritas"]},e.exports["Advance Decline Line_study"]={en:["Advance Decline Line"],id_ID:["Garis Kemajuan Kemunduran / Advance Decline Line"]},e.exports["Advance Decline Ratio_study"]={en:["Advance Decline Ratio"],id_ID:["Rasio Kemajuan Kemunduran / Advance Decline Ratio"]},e.exports["Advance/Decline Ratio (Bars)_study"]={en:["Advance/Decline Ratio (Bars)"],id_ID:["Rasio Kemajuan/Kemunduran / Advance/Decline Ratio (Bars)"]},e.exports["BarUpDn Strategy_study"]={en:["BarUpDn Strategy"],id_ID:["Strategi BarUpDn"]},e.exports["Bollinger Bands Strategy directed_study"]={en:["Bollinger Bands Strategy directed"],id_ID:["Strategi Ikat Bollinger terarahkan"]},e.exports["Bollinger Bands Strategy_study"]={en:["Bollinger Bands Strategy"],id_ID:["Strategi Ikat Bollinger"]},e.exports.ChannelBreakOutStrategy_study={en:["ChannelBreakOutStrategy"],id_ID:["StrategiBreakOutKanal"]},e.exports.Compare_study={
en:["Compare"],id_ID:["Bandingkan"]},e.exports["Conditional Expressions_study"]={en:["Conditional Expressions"],id_ID:["Ekspresi Bersyarat"]},e.exports.ConnorsRSI_study={en:["ConnorsRSI"],id_ID:["ConnorsRSI"]},e.exports["Consecutive Up/Down Strategy_study"]={en:["Consecutive Up/Down Strategy"],id_ID:["Strategi Naik/Turun Berurutan"]},e.exports["Cumulative Volume Index_study"]={en:["Cumulative Volume Index"],id_ID:["Indeks Volume Kumulatif"]},e.exports["Divergence Indicator_study"]={en:["Divergence Indicator"],id_ID:["Indikator Divergen"]},e.exports["Greedy Strategy_study"]={en:["Greedy Strategy"],id_ID:["Strategi Greedy"]},e.exports["InSide Bar Strategy_study"]={en:["InSide Bar Strategy"],id_ID:["Strategi InSide Bar"]},e.exports["Keltner Channel Strategy_study"]={en:["Keltner Channel Strategy"],id_ID:["Strategi Kanal Keltner"]},e.exports["Linear Regression_study"]={en:["Linear Regression"],id_ID:["Regresi Linier"]},e.exports["MACD Strategy_study"]={en:["MACD Strategy"],id_ID:["Strategi MACD"]},e.exports["Momentum Strategy_study"]={en:["Momentum Strategy"],id_ID:["Strategi Momentum"]},e.exports["Moon Phases_study"]={en:["Moon Phases"],id_ID:["Fase Bulan"]},e.exports["Moving Average Convergence/Divergence_study"]={en:["Moving Average Convergence/Divergence"],id_ID:["Rata-Rata Pergerakan Konvergen/Divergen / Moving Average Convergence/Divergence"]},e.exports["MovingAvg Cross_study"]={en:["MovingAvg Cross"],id_ID:["Persilangan Rata-RataPerg"]},e.exports["MovingAvg2Line Cross_study"]={en:["MovingAvg2Line Cross"],id_ID:["Persilangan 2GarisRata-RataPerg"]},e.exports["OutSide Bar Strategy_study"]={en:["OutSide Bar Strategy"],id_ID:["Strategi OutSide Bar"]},e.exports.Overlay_study={en:["Overlay"],id_ID:["Overlay"]},e.exports["Parabolic SAR Strategy_study"]={en:["Parabolic SAR Strategy"],id_ID:["Strategi SAR Parabolis"]},e.exports["Pivot Extension Strategy_study"]={en:["Pivot Extension Strategy"],id_ID:["Strategi Ekstensi Pivot"]},e.exports["Pivot Points High Low_study"]={en:["Pivot Points High Low"],id_ID:["Poin Pivot High Low"]},e.exports["Pivot Reversal Strategy_study"]={en:["Pivot Reversal Strategy"],id_ID:["Strategi Pembalikan Pivot"]},e.exports["Price Channel Strategy_study"]={en:["Price Channel Strategy"],id_ID:["Strategi Kanal Harga"]},e.exports["RSI Strategy_study"]={en:["RSI Strategy"],id_ID:["Strategi RSI"]},e.exports["SMI Ergodic Indicator_study"]={en:["SMI Ergodic Indicator"],id_ID:["Indikator SMI Ergodic"]},e.exports["SMI Ergodic Oscillator_study"]={en:["SMI Ergodic Oscillator"],id_ID:["Osilator SMI Ergodic"]},e.exports["Stochastic Slow Strategy_study"]={en:["Stochastic Slow Strategy"],id_ID:["Strategi Stochastic Lambat"]},e.exports["Volatility Stop_study"]={en:["Volatility Stop"],id_ID:["Stop Volatilitas"]},e.exports["Volty Expan Close Strategy_study"]={en:["Volty Expan Close Strategy"],id_ID:["Strategi Volty Expan Close"]},e.exports["Woodies CCI_study"]={en:["Woodies CCI"],id_ID:["CCI Woodies"]},e.exports["Anchored Volume Profile_study"]={en:["Anchored Volume Profile"],id_ID:["Profil Volume Terjangkar"]},
e.exports["Trading Sessions_study"]={en:["Trading Sessions"],id_ID:["Sesi trading"]},e.exports["Cup and Handle Chart Pattern_study"]={en:["Cup and Handle Chart Pattern"],id_ID:["Motif Chart Cup and Handle"]},e.exports["Inverted Cup and Handle Chart Pattern_study"]={en:["Inverted Cup and Handle Chart Pattern"],id_ID:["Inverted Cup and Handle Chart Pattern"]},e.exports["Head and Shoulders Chart Pattern_study"]={en:["Head and Shoulders Chart Pattern"],id_ID:["Head and Shoulders Chart Pattern"]},e.exports["Inverted Head and Shoulders Chart Pattern_study"]={en:["Inverted Head and Shoulders Chart Pattern"],id_ID:["Inverted Head and Shoulders Chart Pattern"]}},89633:e=>{e.exports={en:["Anchored Volume Profile"],id_ID:["Profil Volume Terjangkar"]}},25705:e=>{e.exports={en:["Fixed Range Volume Profile"],id_ID:["Profil Volume Rentang Tetap"]}},24261:e=>{e.exports={en:["Vol"],id_ID:["Vol"]}},51077:e=>{e.exports={en:["Minor"],id_ID:["Minor"]}},922:e=>{e.exports={en:["Minute"],id_ID:["Menit"]}},91405:e=>{e.exports={en:["Text"],id_ID:["Teks"]}},78972:e=>{e.exports={en:["Couldn't copy"],id_ID:["Tidak dapat menyalin"]}},10615:e=>{e.exports={en:["Couldn't cut"],id_ID:["Tidak dapat memotong"]}},81518:e=>{e.exports={en:["Couldn't paste"],id_ID:["Tidak dapat menempel"]}},83140:e=>{e.exports={en:["Countdown to bar close"],id_ID:["Hitung Mundur Ke Penutupan Bar"]}},10871:e=>{e.exports={en:["Colombo"],id_ID:["Colombo"]}},55761:e=>{e.exports={en:["Columns"],id_ID:["Kolom-Kolom"]}},9818:e=>{e.exports={en:["Comment"],id_ID:["Komentar"]}},53942:e=>{e.exports={en:["Compare or Add Symbol"],id_ID:["Bandingkan atau Tambahkan Simbol"]}},12086:e=>{e.exports={en:["Compilation error"],id_ID:["Error kompilasi"]}},48141:e=>{e.exports={en:["Confirm Inputs"],id_ID:["Konfirmasi Input"]}},38917:e=>{e.exports={en:["Copenhagen"],id_ID:["Copenhagen"]}},49680:e=>{e.exports={en:["Copy"],id_ID:["Salin"]}},66134:e=>{e.exports={en:["Copy Chart Layout"],id_ID:["Salin Layout Chart"]}},63553:e=>{e.exports={en:["Copy price"],id_ID:["Salin harga"]}},65736:e=>{e.exports={en:["Cairo"],id_ID:["Kairo"]}},25381:e=>{e.exports={en:["Callout"],id_ID:["Label"]}},45054:e=>{e.exports={en:["Candles"],id_ID:["Candle"]}},30948:e=>{e.exports={en:["Caracas"],id_ID:["Caracas"]}},70409:e=>{e.exports={en:["Casablanca"],id_ID:["Casablanca"]}},37276:e=>{e.exports={en:["Change"],id_ID:["Perubahan"]}},85124:e=>{e.exports={en:["Change Symbol"],id_ID:["Ubah Simbol"]}},2569:e=>{e.exports={en:["Change interval"],id_ID:["Ubah interval"]}},9687:e=>{e.exports={en:["Change interval. Press number or comma"],id_ID:["Ubah interval. Tekan angka atau koma"]}},36332:e=>{e.exports={en:["Change symbol. Start typing symbol name"],id_ID:["Ubah simbol. Mulai mengetikkan nama simbol"]}},48566:e=>{e.exports={en:["Change scale currency"],id_ID:["Ubah mata uang skala"]}},85110:e=>{e.exports={en:["Change scale unit"],id_ID:["Ubah unit skala"]}},56275:e=>{e.exports={en:["Chart #{index}"],id_ID:["Chart #{index}"]}},39950:e=>{e.exports={en:["Chart Properties"],id_ID:["Properti Chart"]}},98856:e=>{e.exports={
en:["Chart by TradingView"],id_ID:["Chart oleh TradingView"]}},1136:e=>{e.exports={en:["Chart for {symbol}, {interval}"],id_ID:["Chart untuk {symbol}, {interval}"]}},69804:e=>{e.exports={en:["Chart image copied to clipboard {emoji}"],id_ID:["Gambar chart yang disalin ke clipboard {emoji}"]}},6655:e=>{e.exports={en:["Chart image embed code copied to clipboard {emoji}"],id_ID:["Kode embed gambar chart disalin ke clipboard {emoji}"]}},36549:e=>{e.exports={en:["Chatham Islands"],id_ID:["Kepulauan Chatham"]}},72452:e=>{e.exports={en:["Chicago"],id_ID:["Chicago"]}},50349:e=>{e.exports={en:["Chongqing"],id_ID:["Chongqing"]}},91944:e=>{e.exports={en:["Circle"],id_ID:["Lingkaran"]}},14985:e=>{e.exports={en:["Click to set a point"],id_ID:["Klik untuk menentukan sebuah poin"]}},12537:e=>{e.exports={en:["Clone"],id_ID:["Gandakan"]}},62578:e=>{e.exports={en:["Close"],id_ID:["Penutupan"]}},264:e=>{e.exports={en:["Create limit order"],id_ID:["Buat order baru"]}},6969:e=>{e.exports={en:["Cross"],id_ID:["Cross"]}},74334:e=>{e.exports={en:["Cross Line"],id_ID:["Garis Perpotongan"]}},59396:e=>{e.exports={en:["Currencies"],id_ID:["Mata Uang"]}},20177:e=>{e.exports={en:["Current interval and above"],id_ID:["Interval saat ini dan di atasnya"]}},494:e=>{e.exports={en:["Current interval and below"],id_ID:["Interval saat ini dan di bawahnya"]}},60668:e=>{e.exports={en:["Current interval only"],id_ID:["Hanya interval saat ini"]}},78609:e=>{e.exports={en:["Curve"],id_ID:["Kurva"]}},87380:e=>{e.exports={en:["Cycle"],id_ID:["Siklus"]}},84031:e=>{e.exports={en:["Cyclic Lines"],id_ID:["Garis Siklus"]}},93191:e=>{e.exports={en:["Cypher Pattern"],id_ID:["Motif Cypher"]}},7219:e=>{e.exports={en:["A layout with that name already exists"],id_ID:["Layout dengan nama tersebut sudah ada"]}},67635:e=>{e.exports={en:["A layout with that name already exists. Do you want to overwrite it?"],id_ID:["Layout dengan nama tersebut sudah ada. Apakah anda ingin menimpanya?"]}},46712:e=>{e.exports={en:["ABCD Pattern"],id_ID:["Motif ABCD"]}},36485:e=>{e.exports={en:["Amsterdam"],id_ID:["Amsterdam"]}},42630:e=>{e.exports={en:["Anchorage"],id_ID:["Penjangkaran"]}},63209:e=>{e.exports={en:["Anchored Note"],id_ID:["Catatan Terjangkar"]}},42669:e=>{e.exports={en:["Anchored Text"],id_ID:["Teks Terjangkar"]}},84541:e=>{e.exports={en:["Anchored VWAP"],id_ID:["VWAP Terjangkar"]}},77401:e=>{e.exports={en:["Access error"],id_ID:["Error akses"]}},46501:e=>{e.exports={en:["Add Symbol"],id_ID:["Tambah Simbol"]}},69709:e=>{e.exports={en:["Add alert on {title}"],id_ID:["Tambah Peringatan untuk {title}"]}},89295:e=>{e.exports={en:["Add alert on {title} at {price}"],id_ID:["Tambahkan peringatan untuk {title} pada {price}"]}},2439:e=>{e.exports={en:["Add financial metric for {instrumentName}"],id_ID:["Tambah metrik Finansial untuk {instrumentName}"]}},35088:e=>{e.exports={en:["Add indicator/strategy on {studyTitle}"],id_ID:["Tambah Indikator/Strategi pada {studyTitle}"]}},39873:e=>{e.exports={en:["Add text note for {symbol}"],id_ID:["Tambahkan Catatan Teks untuk {symbol}"]}},35679:e=>{
e.exports={en:["Add this financial metric to entire layout"],id_ID:["Tambah Metrik Finansial ini ke Seluruh Layout"]}},10996:e=>{e.exports={en:["Add this financial metric to favorites"],id_ID:["Tambahkan metrik keuangan ini ke favorit"]}},26090:e=>{e.exports={en:["Add this indicator to entire layout"],id_ID:["Tambah Indikator ini ke Seluruh Layout"]}},92957:e=>{e.exports={en:["Add this indicator to favorites"],id_ID:["Tambahkan indikator ini ke favorit"]}},95754:e=>{e.exports={en:["Add this strategy to entire layout"],id_ID:["Tambah Strategi ini ke Seluruh Layout"]}},39010:e=>{e.exports={en:["Add this symbol to entire layout"],id_ID:["Tambah Simbol ini ke Seluruh Layout"]}},426:e=>{e.exports={en:["Adelaide"],id_ID:["Adelaide"]}},40452:e=>{e.exports={en:["Always invisible"],id_ID:["Selalu Tidak Terlihat"]}},36299:e=>{e.exports={en:["Always visible"],id_ID:["Selalu Terlihat"]}},81442:e=>{e.exports={en:["All indicators and drawing tools"],id_ID:["Seluruh Indikator dan Alat Gambar"]}},58026:e=>{e.exports={en:["All intervals"],id_ID:["Seluruh interval"]}},78358:e=>{e.exports={en:["Apply default"],id_ID:["Terapkan Bawaan"]}},22437:e=>{e.exports={en:["Apply these indicators to entire layout"],id_ID:["Terapkan Indikator berikut ini ke Seluruh Layout"]}},27072:e=>{e.exports={en:["Apr"],id_ID:["Apr"]}},59324:e=>{e.exports={en:["Arc"],id_ID:["Busur"]}},34456:e=>{e.exports={en:["Area"],id_ID:["Area"]}},11858:e=>{e.exports={en:["Arrow"],id_ID:["Panah"]}},34247:e=>{e.exports={en:["Arrow Down"],id_ID:["Panah Turun"]}},36352:e=>{e.exports={en:["Arrow Marker"],id_ID:["Penanda panah"]}},73193:e=>{e.exports={en:["Arrow Mark Down"],id_ID:["Tanda Panah Turun"]}},1949:e=>{e.exports={en:["Arrow Mark Left"],id_ID:["Tanda Panah Kiri"]}},86275:e=>{e.exports={en:["Arrow Mark Right"],id_ID:["Tanda Panah Kanan"]}},62453:e=>{e.exports={en:["Arrow Mark Up"],id_ID:["Tanda Panah Naik"]}},77231:e=>{e.exports={en:["Arrow Up"],id_ID:["Panah Naik"]}},98128:e=>{e.exports={en:["Astana"],id_ID:["Astana"]}},63627:e=>{e.exports={en:["Ashgabat"],id_ID:["Ashkhabad"]}},72445:e=>{e.exports={en:["At close"],id_ID:["Pada penutupan"]}},73702:e=>{e.exports={en:["Athens"],id_ID:["Athena"]}},21469:e=>{e.exports={en:["Auto"],id_ID:["Auto"]}},24157:e=>{e.exports={en:["Auto (fits data to screen)"],id_ID:["Auto (Mengepaskan Data Pada Layar)"]}},46450:e=>{e.exports={en:["Aug"],id_ID:["Agst"]}},21841:e=>{e.exports={en:["Average close price label"],id_ID:["Label rata-rata harga penutupan"]}},16138:e=>{e.exports={en:["Average close price line"],id_ID:["Garis harga rata-rata penutupan"]}},73025:e=>{e.exports={en:["Avg"],id_ID:["Rata-rata"]}},87580:e=>{e.exports={en:["Azores"]}},73905:e=>{e.exports={en:["Bogota"],id_ID:["Bogota"]}},90594:e=>{e.exports={en:["Bahrain"],id_ID:["Bahrain"]}},70540:e=>{e.exports={en:["Balloon"],id_ID:["Balon"]}},47045:e=>{e.exports={en:["Bangkok"],id_ID:["Bangkok"]}},76651:e=>{e.exports={en:["Bar Replay isn't available for this chart type. Do you want to exit Bar Replay?"],
id_ID:["Putar Ulang Bar tidak tersedia untuk tipe chart ini. Apakah anda ingin keluar dari Putar Ulang Bar?"]}},68054:e=>{e.exports={en:["Bar Replay isn't available for this time interval. Do you want to exit Bar Replay?"],id_ID:["Putar Ulang Bar tidak tersedia untuk interval waktu ini. Apakah anda ingin keluar dari Putar Ulang Bar?"]}},27377:e=>{e.exports={en:["Bars"],id_ID:["Bar"]}},81994:e=>{e.exports={en:["Bars Pattern"],id_ID:["Motif Bar"]}},59213:e=>{e.exports={en:["Baseline"],id_ID:["Garis dasar"]}},71797:e=>{e.exports={en:["Belgrade"],id_ID:["Belgrade"]}},64313:e=>{e.exports={en:["Berlin"],id_ID:["Berlin"]}},43539:e=>{e.exports={en:["Brush"],id_ID:["Kuas"]}},91499:e=>{e.exports={en:["Brussels"],id_ID:["Brussels"]}},70876:e=>{e.exports={en:["Bratislava"],id_ID:["Bratislava"]}},55481:e=>{e.exports={en:["Bring forward"],id_ID:["Bawa Maju"]}},17293:e=>{e.exports={en:["Bring to front"],id_ID:["Bawa ke Depan"]}},79336:e=>{e.exports={en:["Brisbane"],id_ID:["Brisbane"]}},33672:e=>{e.exports={en:["Bucharest"],id_ID:["Bucharest"]}},20313:e=>{e.exports={en:["Budapest"],id_ID:["Budapest"]}},25282:e=>{e.exports={en:["Buenos Aires"],id_ID:["Buenos Aires"]}},46768:e=>{e.exports={en:["By TradingView"],id_ID:["Oleh TradingView"]}},54280:e=>{e.exports={en:["Go to date"],id_ID:["Menuju ke tanggal"]}},74975:e=>{e.exports={en:["Go to {lineToolName}"],id_ID:["Menuju ke {lineToolName}"]}},15462:e=>{e.exports={en:["Got it"],id_ID:["Mengerti"]}},47460:e=>{e.exports={en:["Gann Box"],id_ID:["Kotak Gann"]}},48683:e=>{e.exports={en:["Gann Fan"],id_ID:["Kipas Gann"]}},44763:e=>{e.exports={en:["Gann Square"],id_ID:["Persegi Gann"]}},60707:e=>{e.exports={en:["Gann Square Fixed"],id_ID:["Kotak Gann Paten"]}},46808:e=>{e.exports={en:["Ghost Feed"],id_ID:["Ghost Feed"]}},57726:e=>{e.exports={en:["Grand supercycle"],id_ID:["Supercycle Besar"]}},34914:e=>{e.exports={en:["Do you really want to delete Study Template '{name}' ?"],id_ID:["Apakah benar anda ingin menghapus Template Studi '{name}' ?"]}},77125:e=>{e.exports={en:["Double Curve"],id_ID:["Kurva Ganda"]}},9430:e=>{e.exports={en:["Double-click any edge to reset layout grid"],id_ID:["Dobel-klik di tepi mana pun untuk mengatur ulang kisi layout"]}},75296:e=>{e.exports={en:["Double-click to finish Path"],id_ID:["Klik dua kali untuk menyelesaikan Jalur"]}},17409:e=>{e.exports={en:["Double-click to finish Polyline"],id_ID:["Klik dua kali untuk menyelesaikan Polyline"]}},57131:e=>{e.exports={en:["Data Provided by"],id_ID:["Data Disediakan oleh"]}},62154:e=>{e.exports={en:["Date"],id_ID:["Tanggal"]}},85444:e=>{e.exports={en:["Date Range"],id_ID:["Rentang Tanggal"]}},47017:e=>{e.exports={en:["Date and Price Range"],id_ID:["Rentang Tanggal dan Harga"]}},32084:e=>{e.exports={en:["Dec"],id_ID:["Des"]}},23403:e=>{e.exports={en:["Degree"],id_ID:["Derajat"]}},27358:e=>{e.exports={en:["Denver"],id_ID:["Denver"]}},24959:e=>{e.exports={en:["Dhaka"],id_ID:["Dhaka"]}},15179:e=>{e.exports={en:["Diamond"],id_ID:["Berlian"]}},91544:e=>{e.exports={en:["Disjoint Channel"],id_ID:["Kanal Disjoint"]}},70132:e=>{
e.exports={en:["Displacement"],id_ID:["Pemindahan"]}},93864:e=>{e.exports={en:["Drawings toolbar"],id_ID:["Toolbar Alat Gambar"]}},96890:e=>{e.exports={en:["Draw Horizontal Line at"],id_ID:["Menggambar Garis Horizontal menyala"]}},23650:e=>{e.exports={en:["Dubai"],id_ID:["Dubai"]}},79716:e=>{e.exports={en:["Dublin"],id_ID:["Dublin"]}},73456:e=>{e.exports={en:["Emoji"],id_ID:["Emoji"]}},9541:e=>{e.exports={en:["Enter a new chart layout name"],id_ID:["Masukkan nama layout chart yang baru"]}},80943:e=>{e.exports={en:["Elliott Correction Wave (ABC)"],id_ID:["Gelombang Koreksi Elliott (ABC)"]}},75112:e=>{e.exports={en:["Elliott Double Combo Wave (WXY)"],id_ID:["Gelombang Kombinasi Dobel Elliott (WXY)"]}},61114:e=>{e.exports={en:["Elliott Impulse Wave (12345)"],id_ID:["Gelombang Impulse Elliott (12345)"]}},72359:e=>{e.exports={en:["Elliott Triangle Wave (ABCDE)"],id_ID:["Gelombang Segitiga Elliott / Elliott Triangle Wave (ABCDE)"]}},76129:e=>{e.exports={en:["Elliott Triple Combo Wave (WXYXZ)"],id_ID:["Gelombang Kombinasi Tripel Elliott (WXYXZ)"]}},78996:e=>{e.exports={en:["Ellipse"],id_ID:["Elips"]}},52788:e=>{e.exports={en:["Extended Line"],id_ID:["Garis Perpanjangan"]}},86905:e=>{e.exports={en:["Exchange"],id_ID:["Bursa"]}},19271:e=>{e.exports={en:["Existing pane above"],id_ID:["Pane Yang Telah Ada Diatas"]}},46545:e=>{e.exports={en:["Existing pane below"],id_ID:["Pane Yang Telah Ada Dibawah"]}},20138:e=>{e.exports={en:["Forecast"],id_ID:["Prakiraan"]}},2507:e=>{e.exports={en:["Feb"],id_ID:["Feb"]}},59005:e=>{e.exports={en:["Fib Channel"],id_ID:["Kanal Fib"]}},82330:e=>{e.exports={en:["Fib Circles"],id_ID:["Lingkaran Fib"]}},55986:e=>{e.exports={en:["Fib Retracement"],id_ID:["Retracemen Fib"]}},33880:e=>{e.exports={en:["Fib Speed Resistance Arcs"],id_ID:["Busur Resisten Kecepatan Fib"]}},2395:e=>{e.exports={en:["Fib Speed Resistance Fan"],id_ID:["Kipas Resisten Kecepatan Fib"]}},39014:e=>{e.exports={en:["Fib Spiral"],id_ID:["Spiral Fib"]}},30622:e=>{e.exports={en:["Fib Time Zone"],id_ID:["Zona Waktu Fib"]}},85042:e=>{e.exports={en:["Fib Wedge"],id_ID:["Baji Fib"]}},33885:e=>{e.exports={en:["Flag"],id_ID:["Bendera"]}},14600:e=>{e.exports={en:["Flag Mark"],id_ID:["Tanda Bendera"]}},45051:e=>{e.exports={en:["Flat Top/Bottom"],id_ID:["Puncak/Dasar Datar"]}},63271:e=>{e.exports={en:["Flipped"],id_ID:["Membalik"]}},39643:e=>{e.exports={en:["Fraction part is invalid."],id_ID:["Bagian fraksi tidak valid."]}},24077:e=>{e.exports={en:["Fundamental studies are no longer available on charts"],id_ID:["Studi-Studi Fundamental tidak lagi tersedia pada chart"]}},31561:e=>{e.exports={en:["Kolkata"],id_ID:["Kolkata"]}},54533:e=>{e.exports={en:["Kathmandu"],id_ID:["Kathmandu"]}},83490:e=>{e.exports={en:["Kagi"],id_ID:["Kagi"]}},70913:e=>{e.exports={en:["Karachi"],id_ID:["Karachi"]}},76614:e=>{e.exports={en:["Kuwait"],id_ID:["Kuwait"]}},38561:e=>{e.exports={en:["Kuala Lumpur"],id_ID:["Kuala Lumpur"]}},99906:e=>{e.exports={en:["HLC area"],id_ID:["Area HLC"]}},34491:e=>{e.exports={en:["Ho Chi Minh"],id_ID:["Ho Chi Minh"]}},13459:e=>{
e.exports={en:["Hollow candles"],id_ID:["Candle Kosong"]}},48861:e=>{e.exports={en:["Hong Kong"],id_ID:["Hong Kong"]}},79668:e=>{e.exports={en:["Honolulu"],id_ID:["Honolulu"]}},21795:e=>{e.exports={en:["Horizontal Line"],id_ID:["Garis Horisontal"]}},25487:e=>{e.exports={en:["Horizontal Ray"],id_ID:["Sinar Horisontal"]}},21928:e=>{e.exports={en:["Head and Shoulders"],id_ID:["Head dan Shoulders"]}},63876:e=>{e.exports={en:["Heikin Ashi"],id_ID:["Heikin Ashi"]}},48203:e=>{e.exports={en:["Helsinki"],id_ID:["Helsinki"]}},27298:e=>{e.exports={en:["Hide"],id_ID:["Sembunyikan"]}},47074:e=>{e.exports={en:["Hide all"],id_ID:["Sembunyikan seluruhnya"]}},52563:e=>{e.exports={en:["Hide all drawings"],id_ID:["Sembunyikan seluruh gambar"]}},90763:e=>{e.exports={en:["Hide all drawings and indicators"],id_ID:["Sembunyikan seluruh gambar dan indikator"]}},18216:e=>{e.exports={en:["Hide all drawings, indicators, positions & orders"],id_ID:["Sembunyikan seluruh gambar, indikator, posisi & order"]}},78525:e=>{e.exports={en:["Hide all indicators"],id_ID:["Sembunyikan seluruh indikator"]}},42164:e=>{e.exports={en:["Hide all positions & orders"],id_ID:["Sembunyikan seluruh posisi & order"]}},3217:e=>{e.exports={en:["Hide drawings"],id_ID:["Sembunyikan gambar"]}},97878:e=>{e.exports={en:["Hide events on chart"],id_ID:["Sembunyikan Peristiwa di Chart"]}},72351:e=>{e.exports={en:["Hide indicators"],id_ID:["Sembunyikan indikator"]}},28345:e=>{e.exports={en:["Hide marks on bars"],id_ID:["Sembunyikan Tanda-Tanda pada Bar"]}},92226:e=>{e.exports={en:["Hide positions & orders"],id_ID:["Sembunyikan posisi & order"]}},78254:e=>{e.exports={en:["High"],id_ID:["Tertinggi"]}},98236:e=>{e.exports={en:["High-low"],id_ID:["Tertinggi-Terendah"]}},99479:e=>{e.exports={en:["High and low price labels"],id_ID:["Label harga tertinggi dan terendah"]}},33766:e=>{e.exports={en:["High and low price lines"],id_ID:["Garis harga tertinggi dan terendah"]}},69476:e=>{e.exports={en:["Highlighter"],id_ID:["Penanda"]}},72819:e=>{e.exports={en:["Histogram is too large, please increase {boldHighlightStart}Row Size{boldHighlightEnd} in the settings window."],id_ID:["Histogram terlalu besar, harap tambah {boldHighlightStart}Ukuran Baris{boldHighlightEnd} di jendela pengaturan."]}},94966:e=>{e.exports={en:["Histogram is too large, please increase {boldHighlightStart}Ticks Per Row{boldHighlightEnd} in the settings window."],id_ID:["Histogram terlalu besar, harap tambah {boldHighlightStart}Tick Per Baris{boldHighlightEnd} di jendela pengaturan."]}},66751:e=>{e.exports={en:["Histogram is too large, please reduce {boldHighlightStart}Row Size{boldHighlightEnd} in the settings window."],id_ID:["Histogram terlalu besar, harap kurangi {boldHighlightStart}Ukuran Baris{boldHighlightEnd} di jendela pengaturan."]}},68065:e=>{e.exports={en:["Image"],id_ID:["Gambar"]}},80185:e=>{e.exports={en:["Intervals less than {resolution} are not supported for {ticker}."],id_ID:["Interval kurang dari {resolution} tidak didukung untuk {ticker}."]}},10268:e=>{e.exports={en:["Intermediate"],
id_ID:["Menengah"]}},14285:e=>{e.exports={en:["Invalid Symbol"],id_ID:["Simbol Tidak Valid"]}},52969:e=>{e.exports={en:["Invalid symbol"],id_ID:["Simbol tidak valid"]}},37189:e=>{e.exports={en:["Invert scale"],id_ID:["Inversikan Skala"]}},89999:e=>{e.exports={en:["Indexed to 100"],id_ID:["Diindeks ke 100"]}},46850:e=>{e.exports={en:["Indicators value labels"],id_ID:["Label nilai indikator"]}},54418:e=>{e.exports={en:["Indicators name labels"],id_ID:["Label nama indikator"]}},40490:e=>{e.exports={en:["Indicators, Metrics and Strategies. Press slash"],id_ID:["Indikator, Metrik dan Strategi. Tekan garis miring"]}},15992:e=>{e.exports={en:["Info Line"],id_ID:["Garis Info"]}},87829:e=>{e.exports={en:["Insert indicator"],id_ID:["Masukkan Indikator"]}},91612:e=>{e.exports={en:["Inside"],id_ID:["Di Dalam"]}},41686:e=>{e.exports={en:["Inside Pitchfork"],id_ID:["Pitchfork Bagian Dalam"]}},37913:e=>{e.exports={en:["Icon"],id_ID:["Ikon"]}},78326:e=>{e.exports={en:["Istanbul"],id_ID:["Istanbul"]}},39585:e=>{e.exports={en:["Johannesburg"],id_ID:["Johannesburg"]}},14995:e=>{e.exports={en:["Jakarta"],id_ID:["Jakarta"]}},62310:e=>{e.exports={en:["Jan"],id_ID:["Jan"]}},36057:e=>{e.exports={en:["Jerusalem"],id_ID:["Yerusalem"]}},53786:e=>{e.exports={en:["Jul"],id_ID:["Jul"]}},429:e=>{e.exports={en:["Jun"],id_ID:["Jun"]}},67560:e=>{e.exports={en:["Juneau"],id_ID:["Juneau"]}},62329:e=>{e.exports={en:["On the left"],id_ID:["Di Sebelah Kiri"]}},55813:e=>{e.exports={en:["On the right"],id_ID:["Di Sebelah Kanan"]}},64818:e=>{e.exports={en:["Only {availableResolutions} intervals are supported for {ticker}."],id_ID:["Hanya interval {availableResolutions} yang didukung oleh {ticker}."]}},21064:e=>{e.exports={en:["Oops!"],id_ID:["Oops!"]}},51221:e=>{e.exports={en:["Object Tree"],id_ID:["Pohon Objek"]}},12179:e=>{e.exports={en:["Oct"],id_ID:["Okt"]}},16610:e=>{e.exports={en:["Open"],id_ID:["Pembukaan"]}},46005:e=>{e.exports={en:["Original"],id_ID:["Original"]}},75722:e=>{e.exports={en:["Oslo"],id_ID:["Oslo"]}},65318:e=>{e.exports={en:["Low"],id_ID:["Terendah"]}},55382:e=>{e.exports={en:["Load layout. Press period"],id_ID:["Memuat layout. Tekan titik"]}},5837:e=>{e.exports={en:["Lock"],id_ID:["Kunci"]}},79777:e=>{e.exports={en:["Lock/unlock"],id_ID:["Kunci/Buka Kunci"]}},66005:e=>{e.exports={en:["Lock vertical cursor line by time"],id_ID:["Kunci garis kursor vertikal berdasarkan waktu"]}},14017:e=>{e.exports={en:["Lock price to bar ratio"],id_ID:["Kunci Harga Ke Rasio Bar"]}},16170:e=>{e.exports={en:["Logarithmic"],id_ID:["Logaritma"]}},19439:e=>{e.exports={en:["London"],id_ID:["London"]}},74832:e=>{e.exports={en:["Long Position"],id_ID:["Posisi Pembelian"]}},28733:e=>{e.exports={en:["Los Angeles"],id_ID:["Los Angeles"]}},85924:e=>{e.exports={en:["Label Down"],id_ID:["Label Turun"]}},52402:e=>{e.exports={en:["Label Up"],id_ID:["Label Naik"]}},5119:e=>{e.exports={en:["Labels"],id_ID:["Label"]}},19931:e=>{e.exports={en:["Lagos"],id_ID:["Lagos"]}},63815:e=>{e.exports={en:["Last day change"],id_ID:["Perubahan hari terakhir"]}},59444:e=>{e.exports={
en:["Lima"],id_ID:["Lima"]}},3554:e=>{e.exports={en:["Line"],id_ID:["Garis"]}},9394:e=>{e.exports={en:["Line with markers"],id_ID:["Garis dengan penanda"]}},43588:e=>{e.exports={en:["Line break"],id_ID:["Garis Jeda"]}},56982:e=>{e.exports={en:["Lines"],id_ID:["Garis"]}},37367:e=>{e.exports={en:["Link to the chart image copied to clipboard {emoji}"],id_ID:["Tautan ke gambar chart yang disalin ke clipboard {emoji}"]}},53375:e=>{e.exports={en:["Lisbon"],id_ID:["Lisbon"]}},81038:e=>{e.exports={en:["Luxembourg"],id_ID:["Luxembourg"]}},60663:e=>{e.exports={en:["Move the point to position the anchor then tap to place"],id_ID:["Pindahkan titiknya untuk memposisikan jangkar lalu tap untuk meletakkan"]}},35049:e=>{e.exports={en:["Move to"],id_ID:["Pindah Ke"]}},26493:e=>{e.exports={en:["Move scale to left"],id_ID:["Pindahkan Skala ke Kiri"]}},40789:e=>{e.exports={en:["Move scale to right"],id_ID:["Pindahkan Skala ke Kanan"]}},70382:e=>{e.exports={en:["Modified Schiff"],id_ID:["Schiff Termodifikasi"]}},57681:e=>{e.exports={en:["Modified Schiff Pitchfork"],id_ID:["Pitchfork Schiff Termodifikasi"]}},93907:e=>{e.exports={en:["More settings"]}},64039:e=>{e.exports={en:["Moscow"],id_ID:["Moscow"]}},52066:e=>{e.exports={en:["Madrid"],id_ID:["Madrid"]}},38365:e=>{e.exports={en:["Malta"],id_ID:["Malta"]}},48991:e=>{e.exports={en:["Manila"],id_ID:["Manila"]}},92767:e=>{e.exports={en:["Mar"],id_ID:["Mar"]}},73332:e=>{e.exports={en:["Mexico City"],id_ID:["Kota Meksiko"]}},88314:e=>{e.exports={en:["Merge all scales into one"],id_ID:["Gabungkan Seluruh Skala Menjadi Satu"]}},54215:e=>{e.exports={en:["Mixed"],id_ID:["Campuran"]}},24866:e=>{e.exports={en:["Micro"],id_ID:["Mikro"]}},87957:e=>{e.exports={en:["Millennium"],id_ID:["Milenium"]}},14724:e=>{e.exports={en:["Minuette"],id_ID:["Minuette"]}},78273:e=>{e.exports={en:["Minuscule"],id_ID:["Amat kecil"]}},28941:e=>{e.exports={en:["Mirrored"],id_ID:["Dicerminkan"]}},9865:e=>{e.exports={en:["Muscat"],id_ID:["Muscat"]}},96935:e=>{e.exports={en:["N/A"],id_ID:["Tidak Tersedia"]}},36252:e=>{e.exports={en:["No data here"],id_ID:["Belum ada data disini"]}},11254:e=>{e.exports={en:["No scale (fullscreen)"],id_ID:["Tanpa Skala (Layar Penuh)"]}},9140:e=>{e.exports={en:["No sync"],id_ID:["Tidak ada sinkronisasi"]}},50910:e=>{e.exports={en:["No volume data"],id_ID:["Tidak ada data volume"]}},94389:e=>{e.exports={en:["Note"],id_ID:["Catatan"]}},26899:e=>{e.exports={en:["Nov"],id_ID:["Nov"]}},67891:e=>{e.exports={en:["Norfolk Island"],id_ID:["Pulai Norfolk"]}},40977:e=>{e.exports={en:["Nairobi"],id_ID:["Nairobi"]}},40544:e=>{e.exports={en:["New York"],id_ID:["New York"]}},66103:e=>{e.exports={en:["New Zealand"],id_ID:["Selandia Baru"]}},15512:e=>{e.exports={en:["New pane above"],id_ID:["Panel baru diatas"]}},52160:e=>{e.exports={en:["New pane below"],id_ID:["Panel baru dibawah"]}},15402:e=>{e.exports={en:["Next time you can use {shortcut} for quick paste"],id_ID:["Ke depannya anda dapat menggunakan {shortcut} untuk menempel dengan cepat"]}},94600:e=>{e.exports={en:["Nicosia"],id_ID:["Nikosia"]}},
73013:e=>{e.exports={en:["Something went wrong"],id_ID:["Terjadi masalah"]}},4509:e=>{e.exports={en:["Something went wrong. Please try again later."],id_ID:["Terjadi kesalahan. Harap coba kembali nanti."]}},43047:e=>{e.exports={en:["Save New Chart Layout"],id_ID:["Simpan Layout Chart Baru"]}},76266:e=>{e.exports={en:["Save as"],id_ID:["Simpan Sebagai"]}},55502:e=>{e.exports={en:["San Salvador"],id_ID:["San Salvador"]}},30231:e=>{e.exports={en:["Santiago"],id_ID:["Santiago"]}},91912:e=>{e.exports={en:["Sao Paulo"],id_ID:["Sao Paulo"]}},43931:e=>{e.exports={en:["Scale currency"],id_ID:["Mata uang skala"]}},43758:e=>{e.exports={en:["Scale price chart only"],id_ID:["Skalakan Chart Harga Saja"]}},40012:e=>{e.exports={en:["Scale unit"],id_ID:["Unit skala"]}},69904:e=>{e.exports={en:["Schiff"],id_ID:["Schiff"]}},42608:e=>{e.exports={en:["Schiff Pitchfork"],id_ID:["Pitchfork Schiff"]}},76078:e=>{e.exports={en:["Script may be not updated if you leave the page."],id_ID:["Skrip mungkin tidak diperbaharui apabila anda menginggalkan halaman ini."]}},32514:e=>{e.exports={en:["Settings"],id_ID:["Pengaturan"]}},70784:e=>{e.exports={en:["Second fraction part is invalid."],id_ID:["Bagian pecahan kedua tidak valid."]}},75594:e=>{e.exports={en:["Security info"],id_ID:["Informasi keamanan"]}},21973:e=>{e.exports={en:["Send to back"],id_ID:["Kirim ke Belakang"]}},71179:e=>{e.exports={en:["Send backward"],id_ID:["Kirim Mundur"]}},26820:e=>{e.exports={en:["Seoul"],id_ID:["Seoul"]}},6816:e=>{e.exports={en:["Sep"],id_ID:["Sep"]}},94031:e=>{e.exports={en:["Session"],id_ID:["Sesi"]}},83298:e=>{e.exports={en:["Session volume profile"],id_ID:["Profil volume sesi"]}},66707:e=>{e.exports={en:["Session breaks"],id_ID:["Jeda Sesi"]}},1852:e=>{e.exports={en:["Shanghai"],id_ID:["Shanghai"]}},8075:e=>{e.exports={en:["Short Position"],id_ID:["Posisi Penjualan"]}},98334:e=>{e.exports={en:["Show"],id_ID:["Perlihatkan"]}},85891:e=>{e.exports={en:["Show all drawings"],id_ID:["Tampilkan seluruh gambar"]}},25881:e=>{e.exports={en:["Show all drawings and indicators"],id_ID:["Tampilkan seluruh gambar dan indikator"]}},86738:e=>{e.exports={en:["Show all drawings, indicators, positions & orders"],id_ID:["Tampilkan seluruh gambar, indikator, posisi & order"]}},98753:e=>{e.exports={en:["Show all indicators"],id_ID:["Tampilkan seluruh indikator"]}},55418:e=>{e.exports={en:["Show all ideas"],id_ID:["Tampilkan seluruh ide"]}},20506:e=>{e.exports={en:["Show all positions & orders"],id_ID:["Tampilkan seluruh posisi & order"]}},33158:e=>{e.exports={en:["Show continuous contract switch"],id_ID:["Tampilkan perubahan kontrak berlanjut"]}},81465:e=>{e.exports={en:["Show contract expiration"],id_ID:["Tampilkan kedaluwarsa kontrak"]}},29449:e=>{e.exports={en:["Show dividends"],id_ID:["Perlihatkan dividen"]}},37113:e=>{e.exports={en:["Show earnings"],id_ID:["Perlihatkan perolehan"]}},10261:e=>{e.exports={en:["Show ideas of followed users"],id_ID:["Tampilkan ide dari pengguna yang diikuti"]}},68112:e=>{e.exports={en:["Show latest news and Minds"],
id_ID:["Tampilkan update terkini"]}},44020:e=>{e.exports={en:["Show my ideas only"],id_ID:["Tampilkan ide saya saja"]}},50849:e=>{e.exports={en:["Show splits"],id_ID:["Perlihatkan pemecahan"]}},67751:e=>{e.exports={en:["Signpost"],id_ID:["Signpost"]}},77377:e=>{e.exports={en:["Singapore"],id_ID:["Singapura"]}},39090:e=>{e.exports={en:["Sine Line"],id_ID:["Garis Sinus"]}},66205:e=>{e.exports={en:["Square"],id_ID:["Persegi"]}},86146:e=>{e.exports={en:["Studies limit exceeded: {number} studies per layout.\nPlease, remove some studies."],id_ID:["Batas studi terlewati. {number} studi per layout.\nHarap menghilangkan beberapa studi."]}},92516:e=>{e.exports={en:["Style"],id_ID:["Corak"]}},61507:e=>{e.exports={en:["Stack on the left"],id_ID:["Susun di Kiri"]}},97800:e=>{e.exports={en:["Stack on the right"],id_ID:["Susun di Kanan"]}},85166:e=>{e.exports={en:["Start using keyboard navigation mode. Press {shortcut}"],id_ID:["Mulai gunakan mode navigasi keyboard. Tekan {shortcut}"]}},4035:e=>{e.exports={en:["Stay in drawing mode"],id_ID:["Tetap Dalam Mode Menggambar"]}},69217:e=>{e.exports={en:["Step line"],id_ID:["Garis tahap"]}},43114:e=>{e.exports={en:["Sticker"],id_ID:["Stiker"]}},86716:e=>{e.exports={en:["Stockholm"],id_ID:["Stockholm"]}},1145:e=>{e.exports={en:["Submicro"],id_ID:["Submikro"]}},63375:e=>{e.exports={en:["Submillennium"],id_ID:["Submilenium"]}},30585:e=>{e.exports={en:["Subminuette"],id_ID:["Subminuette"]}},67948:e=>{e.exports={en:["Supercycle"],id_ID:["Supercycle"]}},3348:e=>{e.exports={en:["Supermillennium"],id_ID:["Supermilenium"]}},18905:e=>{e.exports={en:["Switch to {resolution}"],id_ID:["Beralih ke {resolution}"]}},31622:e=>{e.exports={en:["Sydney"],id_ID:["Sydney"]}},70963:e=>{e.exports={en:["Symbol Error"],id_ID:["Simbol Error"]}},32390:e=>{e.exports={en:["Symbol name label"],id_ID:["Label Nama Simbol"]}},10127:e=>{e.exports={en:["Symbol last price label"],id_ID:["Label Nilai Terakhir Simbol"]}},39079:e=>{e.exports={en:["Sync globally"],id_ID:["Sinkronisasikan secara global"]}},46607:e=>{e.exports={en:["Sync in layout"],id_ID:["Sinkronisasi pada layout"]}},76519:e=>{e.exports={en:["Point & figure"],id_ID:["Poin & Figur"]}},39949:e=>{e.exports={en:["Polyline"],id_ID:["Polyline"]}},371:e=>{e.exports={en:["Path"],id_ID:["Jalur"]}},59256:e=>{e.exports={en:["Parallel Channel"],id_ID:["Kanal Paralel"]}},61879:e=>{e.exports={en:["Paris"],id_ID:["Paris"]}},35140:e=>{e.exports={en:["Paste"],id_ID:["Paste"]}},6919:e=>{e.exports={en:["Percent"],id_ID:["Persen"]}},24436:e=>{e.exports={en:["Perth"],id_ID:["Perth"]}},14055:e=>{e.exports={en:["Phoenix"],id_ID:["Phoenix"]}},34156:e=>{e.exports={en:["Pitchfan"],id_ID:["Pitchfan"]}},19634:e=>{e.exports={en:["Pitchfork"],id_ID:["Pitchfork"]}},33110:e=>{e.exports={en:["Pin to new left scale"],id_ID:["Pin ke Skala Kiri Baru"]}},28280:e=>{e.exports={en:["Pin to new right scale"],id_ID:["Pin ke Skala Kanan Baru"]}},14115:e=>{e.exports={en:["Pin to left scale"],id_ID:["Pin ke Skala Kiri"]}},72046:e=>{e.exports={en:["Pin to left scale (hidden)"],
id_ID:["Pin ke Skala Kiri (Tersembunyi)"]}},81054:e=>{e.exports={en:["Pin to right scale"],id_ID:["Pin ke skala kanan"]}},16986:e=>{e.exports={en:["Pin to right scale (hidden)"],id_ID:["Pin ke Skala Kanan (Tersembunyi)"]}},60035:e=>{e.exports={en:["Pin to scale (now left)"],id_ID:["Pin ke Skala (Saat ini Kiri)"]}},94210:e=>{e.exports={en:["Pin to scale (now no scale)"],id_ID:["Pin ke Skala (Saat ini Tanpa Skala)"]}},10761:e=>{e.exports={en:["Pin to scale (now right)"],id_ID:["Pin ke Skala (Saat ini Kanan)"]}},76150:e=>{e.exports={en:["Pin to scale (now {label})"],id_ID:["Pin ke Skala ({label} Saat ini)"]}},29436:e=>{e.exports={en:["Pin to scale {label}"],id_ID:["Pin ke Skala {label}"]}},2165:e=>{e.exports={en:["Pin to scale {label} (hidden)"],id_ID:["Pin ke Skala {label} (Tersembunyi)"]}},90095:e=>{e.exports={en:["Pinned to left scale"],id_ID:["Di Pin ke Skala Kiri"]}},32538:e=>{e.exports={en:["Pinned to left scale (hidden)"],id_ID:["Di Pin ke Skala Kiri (Tersembunyi)"]}},44579:e=>{e.exports={en:["Pinned to right scale"],id_ID:["Di Pin ke Skala Kanan"]}},94559:e=>{e.exports={en:["Pinned to right scale (hidden)"],id_ID:["Pin ke Skala Kanan (Tersembunyi)"]}},12645:e=>{e.exports={en:["Pinned to scale {label}"],id_ID:["Di Pin ke Skala {label}"]}},3564:e=>{e.exports={en:["Pinned to scale {label} (hidden)"],id_ID:["Di Pin ke Skala {label} (Tersembunyi)"]}},71566:e=>{e.exports={en:["Plus button"],id_ID:["Tanda tambah"]}},28298:e=>{e.exports={en:["Please give us a clipboard writing permission in your browser or press {keystroke}"],id_ID:["Harap beri kami izin menulis clipboard di browser anda atau tekan {keystroke}"]}},81248:e=>{e.exports={en:["Prague"],id_ID:["Praha"]}},81712:e=>{e.exports={en:["Press and hold {key} while zooming to maintain the chart position"],id_ID:["Tekan dan tahan {key} saat melakukan zoom untuk mempertahankan posisi chart"]}},91282:e=>{e.exports={en:["Price Label"],id_ID:["Label Harga"]}},97512:e=>{e.exports={en:["Price Note"],id_ID:["Catatan Harga"]}},68941:e=>{e.exports={en:["Price Range"],id_ID:["Rentang Harga"]}},66123:e=>{e.exports={en:["Price format is invalid."],id_ID:["Format harga tidak valid."]}},72926:e=>{e.exports={en:["Price line"],id_ID:["Garis Harga"]}},59189:e=>{e.exports={en:["Primary"],id_ID:["Primer"]}},75747:e=>{e.exports={en:["Projection"],id_ID:["Proyeksi"]}},55801:e=>{e.exports={en:["Published on {customer}, {date}"],id_ID:["Dipublikasikan pada {customer}, {date}"]}},28756:e=>{e.exports={en:["Qatar"],id_ID:["Qatar"]}},57959:e=>{e.exports={en:["Quick search. Press {shortcut}"],id_ID:["Pencarian cepat. Tekan {shortcut}"]}},56820:e=>{e.exports={en:["Rotated Rectangle"],id_ID:["Persegi Terputar"]}},52961:e=>{e.exports={en:["Rome"],id_ID:["Roma"]}},50318:e=>{e.exports={en:["Ray"],id_ID:["Sinar"]}},55169:e=>{e.exports={en:["Range"],id_ID:["Rentang"]}},13386:e=>{e.exports={en:["Reykjavik"],id_ID:["Reykjavik"]}},26001:e=>{e.exports={en:["Rectangle"],id_ID:["Persegi"]}},48236:e=>{e.exports={en:["Redo"],id_ID:["Ulangi"]}},2460:e=>{e.exports={en:["Regression Trend"],id_ID:["Tren Regresi"]}
},67410:e=>{e.exports={en:["Remove"],id_ID:["Hilangkan"]}},96374:e=>{e.exports={en:["Remove drawings"],id_ID:["Hilangkan Gambar"]}},99984:e=>{e.exports={en:["Remove indicators"],id_ID:["Hilangkan Indikator"]}},3061:e=>{e.exports={en:["Remove this financial metric from favorites"],id_ID:["Hapus metrik keuangan ini dari favorit"]}},58764:e=>{e.exports={en:["Remove this indicator from favorites"],id_ID:["Hapus indikator ini dari favorit"]}},22584:e=>{e.exports={en:["Rename Chart Layout"],id_ID:["Ganti Nama Layout Chart"]}},88130:e=>{e.exports={en:["Renko"],id_ID:["Renko"]}},75246:e=>{e.exports={en:["Reset chart view"],id_ID:["Atur ulang tampilan chart"]}},88853:e=>{e.exports={en:["Reset points"],id_ID:["Reset poin"]}},15332:e=>{e.exports={en:["Reset price scale"],id_ID:["Atur ulang skala harga"]}},54170:e=>{e.exports={en:["Reset time scale"],id_ID:["Reset Skala Waktu"]}},37974:e=>{e.exports={en:["Riyadh"],id_ID:["Riyadh"]}},94022:e=>{e.exports={en:["Riga"],id_ID:["Riga"]}},60630:e=>{e.exports={en:["Runtime error"],id_ID:["Error runtime"]}},66719:e=>{e.exports={en:["Warning"],id_ID:["Peringatan"]}},5959:e=>{e.exports={en:["Warsaw"],id_ID:["Warsawa"]}},94465:e=>{e.exports={en:["Toggle auto scale"],id_ID:["Toggle skala otomatis"]}},46992:e=>{e.exports={en:["Toggle log scale"],id_ID:["Toggle skala log"]}},98549:e=>{e.exports={en:["Tokelau"],id_ID:["Tokelau"]}},69122:e=>{e.exports={en:["Tokyo"],id_ID:["Tokyo"]}},10095:e=>{e.exports={en:["Toronto"],id_ID:["Toronto"]}},11034:e=>{e.exports={en:["Taipei"],id_ID:["Taipei"]}},79995:e=>{e.exports={en:["Tallinn"],id_ID:["Tallinn"]}},6686:e=>{e.exports={en:["Tehran"],id_ID:["Teheran"]}},93553:e=>{e.exports={en:["Template"],id_ID:["Template"]}},81657:e=>{e.exports={en:["The data vendor doesn't provide volume data for this symbol."],id_ID:["Vendor data tidak menyediakan data volume untuk simbol ini."]}},49947:e=>{e.exports={en:["The publication preview could not be loaded. Please disable your browser extensions and try again."],id_ID:["Kilasan publikasi tidak dapat dimuat. Harap matikan ekstensi browser anda lalu coba kembali."]}},93738:e=>{e.exports={en:["This file is too big. Max size is {value}."],id_ID:["File terlalu besar. Ukuran maksimum adalah {value}."]}},59519:e=>{e.exports={en:["This indicator cannot be applied to another indicator."],id_ID:["Indikator ini tidak dapat diterapkan pada indikator lain"]}},18260:e=>{e.exports={en:["This script contains an error. Please contact its author."],id_ID:["Skrip ini mengandung error. Silahkan hubungi penulisnya."]}},76989:e=>{e.exports={en:["This script is invite-only. To request access, please contact its author."],id_ID:["Skrip ini hanya-undangan. Untuk meminta akses, silakan hubungi penulisnya."]}},47773:e=>{e.exports={en:["This symbol is only available on {linkStart}TradingView{linkEnd}."],id_ID:["Simbol tersebut hanya tersedia di {linkStart}TradingView{linkEnd}."]}},46982:e=>{e.exports={en:["Three Drives Pattern"],id_ID:["Three Drives Pattern"]}},24821:e=>{e.exports={en:["Ticks"],id_ID:["Tick"]}},80254:e=>{e.exports={
en:["Tick-based intervals are not available for {ticker}."],id_ID:["Interval berbasis tick tidak tersedia untuk {ticker}."]}},12806:e=>{e.exports={en:["Time"],id_ID:["Waktu"]}},20909:e=>{e.exports={en:["Time zone"],id_ID:["Zona Waktu"]}},46852:e=>{e.exports={en:["Time Cycles"],id_ID:["Siklus Waktu"]}},17809:e=>{e.exports={en:["Time Price Opportunity"],id_ID:["Peluang Harga Waktu"]}},66823:e=>{e.exports={en:["Trade"],id_ID:["Trade"]}},7697:e=>{e.exports={en:["TradingView is interactive and has commands to use with a screen reader. The following is a list of keyboard commands available to interact on the platform"],id_ID:["Tradingview bersifat interaktif dan memiliki perintah untuk digunakan dengan pembaca layar. Berikut ini adalah daftar perintah keyboard yang tersedia untuk berinteraksi di platform"]}},35757:e=>{e.exports={en:["Trend Angle"],id_ID:["Sudut Tren"]}},97339:e=>{e.exports={en:["Trend Line"],id_ID:["Garis Tren"]}},80583:e=>{e.exports={en:["Trend-Based Fib Extension"],id_ID:["Ekstensi Fib Berbasis Tren"]}},72159:e=>{e.exports={en:["Trend-Based Fib Time"],id_ID:["Waktu Fib Berbasis Tren"]}},1671:e=>{e.exports={en:["Triangle"],id_ID:["Segitiga"]}},76152:e=>{e.exports={en:["Triangle Down"],id_ID:["Segitiga Turun"]}},90148:e=>{e.exports={en:["Triangle Pattern"],id_ID:["Motif Segitiga"]}},21236:e=>{e.exports={en:["Triangle Up"],id_ID:["Segitiga Naik"]}},21007:e=>{e.exports={en:["Tunis"],id_ID:["Tunis"]}},1833:e=>{e.exports={en:["UTC"],id_ID:["UTC"]}},14804:e=>{e.exports={en:["Undo"],id_ID:["Kembalikan"]}},15432:e=>{e.exports={en:["Units"],id_ID:["Unit"]}},11768:e=>{e.exports={en:["Unknown error"],id_ID:["Kesalahan tidak diketahui"]}},99894:e=>{e.exports={en:["Unlock"],id_ID:["Buka Kunci"]}},75546:e=>{e.exports={en:["Unsupported interval"],id_ID:["Interval tidak didukung"]}},8580:e=>{e.exports={en:["User-defined error"],id_ID:["Error yang ditentukan pengguna"]}},40693:e=>{e.exports={en:["Volume Profile Fixed Range"],id_ID:["Profil Volume Rentang Tetap"]}},39903:e=>{e.exports={en:["Volume Profile indicator available only on our upgraded plans."],id_ID:["Indikator Profil Volume hanya tersedia pada skema terupgrade kami."]}},93722:e=>{e.exports={en:["Volume candles"],id_ID:["Candle volume"]}},69156:e=>{e.exports={en:["Volume data is not provided in BIST MIXED data plan."],id_ID:["Data volume tidak disediakan dalam paket data BIST MIXED."]}},92763:e=>{e.exports={en:["Volume footprint"],id_ID:["Jejak volume"]}},32838:e=>{e.exports={en:["Vancouver"],id_ID:["Vancouver"]}},29535:e=>{e.exports={en:["Vertical Line"],id_ID:["Garis Vertikal"]}},23160:e=>{e.exports={en:["Vienna"],id_ID:["Wina"]}},60534:e=>{e.exports={en:["Vilnius"],id_ID:["Vilnius"]}},40091:e=>{e.exports={en:["Visibility"],id_ID:["Visibilitas"]}},54853:e=>{e.exports={en:["Visibility on intervals"],id_ID:["Visibilitas interval"]}},10309:e=>{e.exports={en:["Visible on mouse over"],id_ID:["Terlihat saat Mouse Diatas"]}},4077:e=>{e.exports={en:["Visual order"],id_ID:["Urutan visual"]}},11316:e=>{e.exports={en:["X Cross"],id_ID:["Persilangan X"]}},42231:e=>{
e.exports={en:["XABCD Pattern"],id_ID:["Motif XABCD"]}},25059:e=>{e.exports={en:["You cannot see this pivot timeframe on this resolution"],id_ID:["Anda tidak dapat melihat kerangka waktu pivot pada resolusi ini"]}},53168:e=>{e.exports={en:["Yangon"],id_ID:["Yangon"]}},62859:e=>{e.exports={en:["Zurich"],id_ID:["Zurich"]}},47977:e=>{e.exports={en:["change Elliott degree"],id_ID:["ubah derajat Elliott"]}},61557:e=>{e.exports={en:["change no overlapping labels"],id_ID:["ubah label tidak tumpang tindih"]}},76852:e=>{e.exports={en:["change average close price label visibility"],id_ID:["Ubah visibilitas label rata-rata harga penutupan"]}},1022:e=>{e.exports={en:["change average close price line visibility"],id_ID:["Ubah visibilitas garis rata-rata harga penutupan"]}},69362:e=>{e.exports={en:["change bid and ask labels visibility"],id_ID:["Ubah visibilitas label penawaran dan permintaan"]}},52919:e=>{e.exports={en:["change bid and ask lines visibility"],id_ID:["Ubah visibilitas garis penawaran dan permintaan"]}},32302:e=>{e.exports={en:["change currency"],id_ID:["ubah mata uang"]}},68846:e=>{e.exports={en:["change chart layout to {title}"],id_ID:["ubah layout chart ke {title}"]}},18867:e=>{e.exports={en:["change continuous contract switch visibility"],id_ID:["ubah visibilitas perubahan kontrak berlanjut"]}},39383:e=>{e.exports={en:["change countdown to bar close visibility"],id_ID:["Ubah visibilitas perhitungan mundur ke penutupan bar"]}},16979:e=>{e.exports={en:["change date range"],id_ID:["ubah rentang tanggal"]}},53929:e=>{e.exports={en:["change dividends visibility"],id_ID:["Ubah visibilitas dividen"]}},6119:e=>{e.exports={en:["change events visibility on chart"],id_ID:["Ubah visibilitas peristiwa pada chart"]}},6819:e=>{e.exports={en:["change earnings visibility"],id_ID:["Ubah visibilitas laba"]}},85532:e=>{e.exports={en:["change futures contract expiration visibility"],id_ID:["ubah visibilitas kedaluwarsa kontrak berjangka"]}},24226:e=>{e.exports={en:["change high and low price labels visibility"],id_ID:["Ubah visibilitas label harga tertinggi dan terendah"]}},80692:e=>{e.exports={en:["change high and low price lines visibility"],id_ID:["Ubah visibilitas garis harga tertinggi dan terendah"]}},24893:e=>{e.exports={en:["change indicators name labels visibility"],id_ID:["Ubah visibilitas label nama indikator"]}},64729:e=>{e.exports={en:["change indicators value labels visibility"],id_ID:["Ubah visibilitas label nilai indikator"]}},50243:e=>{e.exports={en:["change latest news and Minds visibility"],id_ID:["Ubah visibilitas update terkini"]}},88849:e=>{e.exports={en:["change linking group"],id_ID:["Ubah penghubungan grup"]}},14691:e=>{e.exports={en:["change pane height"],id_ID:["ubah ketinggian panel"]}},96379:e=>{e.exports={en:["change plus button visibility"],id_ID:["Ubah visibilitas tombol plus"]}},30870:e=>{e.exports={en:["change pre/post market price label visibility"],id_ID:["Ubah visibilitas label harga pra/pasca pasar"]}},11718:e=>{e.exports={en:["change pre/post market price line visibility"],
id_ID:["Ubah visibilitas garis harga pra/pasca pasar"]}},58419:e=>{e.exports={en:["change previous close price line visibility"],id_ID:["Ubah visibilitas garis penutupan harga sebelumnya"]}},8662:e=>{e.exports={en:["change price line visibility"],id_ID:["Ubah Garis Harga"]}},2509:e=>{e.exports={en:["change price to bar ratio"],id_ID:["Ubah harga ke rasio bar"]}},32829:e=>{e.exports={en:["change resolution"],id_ID:["Ubah Resolusi"]}},35400:e=>{e.exports={en:["change symbol"],id_ID:["Ubah simbol"]}},73357:e=>{e.exports={en:["change symbol labels visibility"],id_ID:["Ubah visibilitas label simbol"]}},67453:e=>{e.exports={en:["change symbol last value visibility"],id_ID:["Ubah visibilitas nilai terakhir simbol"]}},4729:e=>{e.exports={en:["change symbol previous close value visibility"],id_ID:["Ubah visibilitas nilai penutupan simbol sebelumnya"]}},87041:e=>{e.exports={en:["change session"],id_ID:["Ubah sesi"]}},38413:e=>{e.exports={en:["change session breaks visibility"],id_ID:["ubah visibilitas jeda sesi"]}},49965:e=>{e.exports={en:["change series style"],id_ID:["ubah corak seri"]}},47474:e=>{e.exports={en:["change splits visibility"],id_ID:["Ubah visibilitas pemecahan"]}},20137:e=>{e.exports={en:["change timezone"],id_ID:["Ubah zona waktu"]}},85975:e=>{e.exports={en:["change unit"],id_ID:["ubah unit"]}},1924:e=>{e.exports={en:["change visibility"],id_ID:["Ubah Visibilitas"]}},84331:e=>{e.exports={en:["change visibility at current interval"],id_ID:["Ubah visibilitas pada interval saat ini"]}},45800:e=>{e.exports={en:["change visibility at current interval and above"],id_ID:["Ubah visibilitas pada interval saat ini dan di atasnya"]}},75645:e=>{e.exports={en:["change visibility at current interval and below"],id_ID:["Ubah visibilitas pada interval saat ini dan di bawahnya"]}},57916:e=>{e.exports={en:["change visibility at all intervals"],id_ID:["Ubah visibilitas pada semua interval"]}},98463:e=>{e.exports={en:["change {title} style"],id_ID:["ubah corak {title}"]}},57122:e=>{e.exports={en:["change {title} text"],id_ID:["Ubah teks {title}"]}},63058:e=>{e.exports={en:["change {pointIndex} point"],id_ID:["ubah poin {pointIndex}"]}},94566:e=>{e.exports={en:["charts by TradingView"],id_ID:["chart oleh TradingView"]}},32943:e=>{e.exports={en:["clone line tools"],id_ID:["Klon peralatan garis"]}},46219:e=>{e.exports={en:["create line tools group"],id_ID:["Buat kelompok alat garis"]}},95394:e=>{e.exports={en:["create line tools group from selection"],id_ID:["Buat kelompok alat garis dari pilihan"]}},12898:e=>{e.exports={en:["create {tool}"],id_ID:["buat {tool}"]}},94227:e=>{e.exports={en:["cut sources"],id_ID:["cut sumber"]}},11500:e=>{e.exports={en:["cut {title}"],id_ID:["cut {title}"]}},12570:e=>{e.exports={en:["add line tool {lineTool} to group {name}"],id_ID:["Tambahkan alat garis {lineTool} ke kelompok {name}"]}},21162:e=>{e.exports={en:["add line tool(s) to group {group}"],id_ID:["tambahkan alat garis ke kelompok {group}"]}},67608:e=>{e.exports={en:["add this financial metric to entire layout"],
id_ID:["Tambah Metrik Finansial ini ke Seluruh Layout"]}},96677:e=>{e.exports={en:["add this indicator to entire layout"],id_ID:["Tambah Indikator ini ke Seluruh Layout"]}},58156:e=>{e.exports={en:["add this strategy to entire layout"],id_ID:["Tambah Strategi ini ke Seluruh Layout"]}},79290:e=>{e.exports={en:["add this symbol to entire layout"],id_ID:["Tambah Simbol ini ke Seluruh Layout"]}},68231:e=>{e.exports={en:["apply chart theme"],id_ID:["terapkan tema chart"]}},99551:e=>{e.exports={en:["apply all chart properties"],id_ID:["terapkan semua properti chart"]}},89720:e=>{e.exports={en:["apply drawing template"],id_ID:["Terapkan Template Gambar"]}},27851:e=>{e.exports={en:["apply factory defaults to selected sources"],id_ID:["terapkan setelan pabrik ke sumber yang dipilih"]}},70507:e=>{e.exports={en:["apply indicators to entire layout"],id_ID:["Terapkan indikator ke seluruh layout"]}},69604:e=>{e.exports={en:["apply study template {template}"],id_ID:["Terapkan template studi {template}"]}},86708:e=>{e.exports={en:["apply toolbars theme"],id_ID:["terapkan tema toolbar"]}},1979:e=>{e.exports={en:["bring group {title} forward"],id_ID:["bawa ke depan {title} grup"]}},53159:e=>{e.exports={en:["bring {title} to front"],id_ID:["bawa {title} ke depan"]}},41966:e=>{e.exports={en:["bring {title} forward"],id_ID:["Bawa {title} ke depan"]}},44676:e=>{e.exports={en:["by TradingView"],id_ID:["oleh TradingView"]}},58850:e=>{e.exports={en:["date range lock"],id_ID:["kunci rentang tanggal"]}},35111:e=>{e.exports={en:["erase level line"],id_ID:["hapus haris level"]}},99395:e=>{e.exports={en:["exclude line tools from group {group}"],id_ID:["Keluarkan alat garis dari kelompok {group}"]}},82349:e=>{e.exports={en:["flip bars pattern"],id_ID:["balik motif bar"]}},13017:e=>{e.exports={en:["hide {title}"],id_ID:["sembunyikan {title}"]}},62249:e=>{e.exports={en:["hide marks on bars"],id_ID:["Sembunyikan Tanda-Tanda pada Bar"]}},56558:e=>{e.exports={en:["interval lock"],id_ID:["pengunci interval"]}},6830:e=>{e.exports={en:["invert scale"],id_ID:["Inversikan Skala"]}},48818:e=>{e.exports={en:["insert {title}"],id_ID:["masukkan {title}"]}},56307:e=>{e.exports={en:["insert {title} after {targetTitle}"],id_ID:["masukkan {title} setelah {targetTitle}"]}},32960:e=>{e.exports={en:["insert {title} after {target}"],id_ID:["Masukkan {title} setelah {target}"]}},57106:e=>{e.exports={en:["insert {title} before {target}"],id_ID:["Masukkan {title} sebelum {target}"]}},46229:e=>{e.exports={en:["insert {title} before {targetTitle}"],id_ID:["Masukkan {title} sebelum {targetTitle}"]}},43364:e=>{e.exports={en:["load default drawing template"],id_ID:["memuat template gambar default"]}},62011:e=>{e.exports={en:["loading..."],id_ID:["memuat..."]}},76104:e=>{e.exports={en:["lock {title}"],id_ID:["Kunci {title}"]}},20453:e=>{e.exports={en:["lock group {group}"],id_ID:["Kunci kelompok {group}"]}},18942:e=>{e.exports={en:["lock objects"],id_ID:["kunci objek"]}},98277:e=>{e.exports={en:["move"],id_ID:["pindah"]}},58228:e=>{e.exports={
en:["move {title} to new left scale"],id_ID:["Pindahkan {title} ke Skala Kiri Baru"]}},77482:e=>{e.exports={en:["move {title} to new right scale"],id_ID:["pindahkan {title} ke skala kanan yang baru"]}},64077:e=>{e.exports={en:["move all scales to left"],id_ID:["Pindahkan Seluruh Skala ke Kiri"]}},19013:e=>{e.exports={en:["move all scales to right"],id_ID:["Pindahkan Seluruh Skala ke Kanan"]}},52510:e=>{e.exports={en:["move drawing(s)"],id_ID:["Pindahkan Gambar"]}},79209:e=>{e.exports={en:["move left"],id_ID:["Pindahkan ke kiri"]}},60114:e=>{e.exports={en:["move right"],id_ID:["Pindahkan ke kanan"]}},44854:e=>{e.exports={en:["move scale"],id_ID:["Pindahkan skala"]}},10625:e=>{e.exports={en:["make {title} no scale (Full screen)"],id_ID:["Jadikan {title} tanpa skala (Layar Penuh)"]}},76709:e=>{e.exports={en:["make group {group} invisible"],id_ID:["Jadikan kelompok {group} tidak terlihat"]}},45987:e=>{e.exports={en:["make group {group} visible"],id_ID:["Jadikan kelompok {group} terlihat"]}},78055:e=>{e.exports={en:["merge down"],id_ID:["gabungkan ke bawah"]}},41866:e=>{e.exports={en:["merge to pane"],id_ID:["gabungkan ke panel"]}},52458:e=>{e.exports={en:["merge up"],id_ID:["gabungkan ke atas"]}},20965:e=>{e.exports={en:["mirror bars pattern"],id_ID:["cerminkan motif bar"]}},90091:e=>{e.exports={en:["n/a"],id_ID:["Tidak Tersedia"]}},94981:e=>{e.exports={en:["scale price"],id_ID:["skala harga"]}},63796:e=>{e.exports={en:["scale price chart only"],id_ID:["Skalakan Chart Harga Saja"]}},70771:e=>{e.exports={en:["scale time"],id_ID:["skala waktu"]}},42070:e=>{e.exports={en:["scroll"],id_ID:["gulir"]}},87840:e=>{e.exports={en:["scroll time"],id_ID:["gulirkan waktu"]}},82241:e=>{e.exports={en:["set price scale selection strategy to {title}"],id_ID:["atur skala harga strategi yang dipilih ke {title}"]}},40962:e=>{e.exports={en:["send {title} backward"],id_ID:["Kirim {title} kebelakang"]}},5005:e=>{e.exports={en:["send {title} to back"],id_ID:["kirim {title} ke belakang"]}},69546:e=>{e.exports={en:["send group {title} backward"],id_ID:["bawa mundur {title} grup"]}},63934:e=>{e.exports={en:["share line tools globally"],id_ID:["Bagikan peralatan garis secara global"]}},90221:e=>{e.exports={en:["share line tools in layout"],id_ID:["Bagikan peralatan garis pada layout"]}},13336:e=>{e.exports={en:["show all ideas"],id_ID:["tampilkan seluruh ide"]}},91395:e=>{e.exports={en:["show ideas of followed users"],id_ID:["tampilkan ide dari pengguna yang diikuti"]}},57460:e=>{e.exports={en:["show my ideas only"],id_ID:["tampilkan ide saya saja"]}},4114:e=>{e.exports={en:["stay in drawing mode"],id_ID:["Tetap dalam mode menggambar"]}},3350:e=>{e.exports={en:["stop syncing drawing"],id_ID:["hentikan sinkronisasi gambar"]}},49183:e=>{e.exports={en:["stop syncing line tool(s)"],id_ID:["hentikan sinkronisasi peralatan menggaris"]}},53278:e=>{e.exports={en:["symbol lock"],id_ID:["pengunci simbol"]}},91677:e=>{e.exports={en:["sync time"],id_ID:["waktu sinkronisasi"]}},3140:e=>{e.exports={en:["powered by"],id_ID:["diberdayakan oleh"]}},92800:e=>{
e.exports={en:["powered by TradingView"],id_ID:["diberdayakan oleh TradingView"]}},62192:e=>{e.exports={en:["paste drawing"],id_ID:["paste gambar"]}},1064:e=>{e.exports={en:["paste indicator"],id_ID:["paste indikator"]}},57010:e=>{e.exports={en:["paste {title}"],id_ID:["paste {title}"]}},78690:e=>{e.exports={en:["pin to left scale"],id_ID:["Pin ke skala kiri"]}},7495:e=>{e.exports={en:["pin to right scale"],id_ID:["Pin ke Skala Kanan"]}},81566:e=>{e.exports={en:["pin to scale {label}"],id_ID:["Pin ke Skala {label}"]}},2618:e=>{e.exports={en:["rearrange panes"],id_ID:["atur ulang pane"]}},43172:e=>{e.exports={en:["remove all studies"],id_ID:["Hilangkan seluruh studi"]}},56253:e=>{e.exports={en:["remove all studies and drawing tools"],id_ID:["Hilangkan seluruh studi dan peralatan gambar"]}},27171:e=>{e.exports={en:["remove deselected empty line tools"],id_ID:["hapus alat garis kosong yang tidak dipilih"]}},30538:e=>{e.exports={en:["remove drawings"],id_ID:["Hilangkan Gambar"]}},1193:e=>{e.exports={en:["remove drawings group"],id_ID:["lepaskan kelompok gambar"]}},38199:e=>{e.exports={en:["remove line data sources"],id_ID:["hapus garis sumber data"]}},93333:e=>{e.exports={en:["remove pane"],id_ID:["lepaskan panel"]}},94543:e=>{e.exports={en:["remove {title}"],id_ID:["lepaskan {title}"]}},41430:e=>{e.exports={en:["removing line tools group {name}"],id_ID:["Hilangkan kelompok alat garis {name}"]}},80491:e=>{e.exports={en:["rename group {group} to {newName}"],id_ID:["Ubah nama kelompok {group} menjadi {newName}"]}},85366:e=>{e.exports={en:["reset layout sizes"],id_ID:["atur ulang ukuran layout"]}},3323:e=>{e.exports={en:["reset scales"],id_ID:["atur ulang skala"]}},17336:e=>{e.exports={en:["reset time scale"],id_ID:["Reset Skala Waktu"]}},47418:e=>{e.exports={en:["resize layout"],id_ID:["ubah ukuran layout"]}},85815:e=>{e.exports={en:["restore defaults"],id_ID:["kembali ke bawaan"]}},96881:e=>{e.exports={en:["restore study defaults"],id_ID:["Pulihkan studi ke bawaan"]}},42240:e=>{e.exports={en:["toggle auto scale"],id_ID:["toggle skala otomatis"]}},46054:e=>{e.exports={en:["toggle collapsed pane state"],id_ID:["ubah status panel yang tertutup"]}},24736:e=>{e.exports={en:["toggle indexed to 100 scale"],id_ID:["toggle skala diindeks ke 100"]}},49695:e=>{e.exports={en:["toggle lock scale"],id_ID:["toggle pengunci skala"]}},49403:e=>{e.exports={en:["toggle log scale"],id_ID:["toggle skala Log"]}},98994:e=>{e.exports={en:["toggle percentage scale"],id_ID:["toggle skala persentase"]}},80688:e=>{e.exports={en:["toggle regular scale"],id_ID:["toggle skala reguler"]}},46807:e=>{e.exports={en:["track time"],id_ID:["waktu pelacakan"]}},8040:e=>{e.exports={en:["turn line tools sharing off"],id_ID:["matikan pembagian peralatan garis"]}},23230:e=>{e.exports={en:["unlock objects"],id_ID:["buka kunci objek"]}},74590:e=>{e.exports={en:["unlock group {group}"],id_ID:["Buka kunci kelompok {group}"]}},12525:e=>{e.exports={en:["unlock {title}"],id_ID:["Buka kunci {title}"]}},81576:e=>{e.exports={en:["unmerge to new bottom pane"],
id_ID:["pisahkan ke panel bawah yang baru"]}},79443:e=>{e.exports={en:["unmerge up"],id_ID:["pisahkan ke atas"]}},46453:e=>{e.exports={en:["unmerge down"],id_ID:["pisahkan ke bawah"]}},94656:e=>{e.exports={en:["{chartStyle} chart type isn't currently available for tick-based intervals."],id_ID:["Oh tidak! Jenis chart {chartStyle} saat ini tidak tersedia untuk interval berbasis tick."]}},41643:e=>{e.exports={en:["{count} bars"],id_ID:["{count} bar"]}},83470:e=>{e.exports={en:["{symbol} financials by TradingView"],id_ID:["{symbol} finansial oleh TradingView"]}},40947:e=>{e.exports={en:["{userName} published on {customer}, {date}"],id_ID:["{userName} dipublikasikan pada {customer}, {date}"]}},91084:e=>{e.exports={en:["zoom"],id_ID:["zoom"]}},49856:e=>{e.exports={en:["zoom in"],id_ID:["perbesar"]}},73638:e=>{e.exports={en:["zoom out"],id_ID:["perkecil"]}},41807:e=>{e.exports={en:["day","days"],id_ID:["hari"]}},42328:e=>{e.exports={en:["hour","hours"],id_ID:["jam"]}},98393:e=>{e.exports={en:["month","months"],id_ID:["bulan"]}},78318:e=>{e.exports={en:["minute","minutes"],id_ID:["menit"]}},33232:e=>{e.exports={en:["second","seconds"],id_ID:["detik"]}},89937:e=>{e.exports={en:["range","ranges"],id_ID:["rentang"]}},48898:e=>{e.exports={en:["week","weeks"],id_ID:["minggu"]}},11913:e=>{e.exports={en:["tick","ticks"],id_ID:["tick"]}},58590:e=>{e.exports={en:["{count}m","{count}m"],id_ID:["{count} bulan"]}},47801:e=>{e.exports={en:["{count}d","{count}d"],id_ID:["{count} hari"]}},46766:e=>{e.exports={en:["{count}y","{count}y"],id_ID:["{count} tahun"]}},56316:e=>{e.exports=Object.create(null),e.exports["#AAPL-symbol-description"]={en:["Apple Inc"],id_ID:["Apple Inc"]},e.exports["#AUDCAD-symbol-description"]={en:["Australian Dollar/Canadian Dollar"],id_ID:["Dollar Australia/Dollar Kanada"]},e.exports["#AUDCHF-symbol-description"]={en:["Australian Dollar / Swiss Franc"],id_ID:["Dollar Australia / Franc Swiss"]},e.exports["#AUDJPY-symbol-description"]={en:["Australian Dollar / Japanese Yen"],id_ID:["Dollar Australia / Yen Jepang"]},e.exports["#AUDNZD-symbol-description"]={en:["Australian Dollar / New Zealand Dollar"],id_ID:["Dollar Australia / Dollar New Zealand"]},e.exports["#AUDRUB-symbol-description"]={en:["Australian Dollar / Russian Ruble"],id_ID:["Dollar Australia / Ruble Rusia"]},e.exports["#AUDUSD-symbol-description"]={en:["Australian Dollar / U.S. Dollar"],id_ID:["Dollar Australia / Dollar AS"]},e.exports["#BRLJPY-symbol-description"]={en:["Brazilian Real / Japanese Yen"],id_ID:["Real Brazil / Yen Jepang"]},e.exports["#BTCCAD-symbol-description"]={en:["Bitcoin / Canadian Dollar"],id_ID:["Bitcoin / Dollar Kanada"]},e.exports["#BTCCNY-symbol-description"]={en:["Bitcoin / Chinese Yuan"],id_ID:["Bitcoin / Yuan Cina"]},e.exports["#BTCEUR-symbol-description"]={en:["Bitcoin / Euro"],id_ID:["Bitcoin / Euro"]},e.exports["#BTCKRW-symbol-description"]={en:["Bitcoin / South Korean Won"],id_ID:["Bitcoin / Won Korea Selatan"]},e.exports["#BTCRUR-symbol-description"]={en:["Bitcoin / Ruble"],id_ID:["Bitcoin / Ruble"]},
e.exports["#BTCUSD-symbol-description"]={en:["Bitcoin / U.S. Dollar"],id_ID:["Bitcoin / Dollar AS"]},e.exports["#BVSP-symbol-description"]={en:["Brazil Bovespa Index"],id_ID:["Index Bovespa Brazil"]},e.exports["#CADJPY-symbol-description"]={en:["Canadian Dollar / Japanese Yen"],id_ID:["Dollar Kanada / Yen Jepang"]},e.exports["#CHFJPY-symbol-description"]={en:["Swiss Franc / Japanese Yen"],id_ID:["Franc Swiss / Yen Jepang"]},e.exports["#COPPER-symbol-description"]={en:["CFDs on Copper"],id_ID:["CFD pada Tembaga"]},e.exports["#ES1-symbol-description"]={en:["S&P 500 E-Mini Futures"],id_ID:["Kontrak Berjangka S&P 500 E-Mini"]},e.exports["#ESP35-symbol-description"]={en:["IBEX 35 Index"],id_ID:["Indeks IBEX 35"]},e.exports["#EUBUND-symbol-description"]={en:["Euro Bund"],id_ID:["Bund Euro"]},e.exports["#EURAUD-symbol-description"]={en:["Euro / Australian Dollar"],id_ID:["Euro / Dollar Australia"]},e.exports["#EURBRL-symbol-description"]={en:["Euro / Brazilian Real"],id_ID:["Euro / Real Brazil"]},e.exports["#EURCAD-symbol-description"]={en:["Euro / Canadian Dollar"],id_ID:["Euro / Dollar Kanada"]},e.exports["#EURCHF-symbol-description"]={en:["Euro / Swiss Franc"],id_ID:["Euro / Franc Swiss"]},e.exports["#EURGBP-symbol-description"]={en:["Euro / British Pound"],id_ID:["Euro / Pound Inggris"]},e.exports["#EURJPY-symbol-description"]={en:["Euro / Japanese Yen"],id_ID:["Euro / Yen Jepang"]},e.exports["#EURNZD-symbol-description"]={en:["Euro / New Zealand Dollar"],id_ID:["Euro / Dollar New Zealand"]},e.exports["#EURRUB-symbol-description"]={en:["Euro / Russian Ruble"],id_ID:["Euro / Ruble Rusia"]},e.exports["#EURRUB_TOM-symbol-description"]={en:["Euro / Russian Ruble TOM"],id_ID:["Euro /Ruble TOM Rusia"]},e.exports["#EURSEK-symbol-description"]={en:["Euro / Swedish Krona"],id_ID:["Euro / Krona Swedia"]},e.exports["#EURTRY-symbol-description"]={en:["Euro / Turkish Lira"],id_ID:["Euro / Lira Turki"]},e.exports["#EURUSD-symbol-description"]={en:["Euro / U.S. Dollar"],id_ID:["Euro / Dollar AS"]},e.exports["#EUSTX50-symbol-description"]={en:["Euro Stoxx 50 Index"],id_ID:["Indeks Euro Stoxx 50"]},e.exports["#FRA40-symbol-description"]={en:["CAC 40 Index"],id_ID:["Indeks CAC 40"]},e.exports["#GB10-symbol-description"]={en:["UK Government Bonds 10 yr"],id_ID:["Obligasi Pemerintah Inggris 10th"]},e.exports["#GBPAUD-symbol-description"]={en:["British Pound / Australian Dollar"],id_ID:["Pound Inggris / Dollar Australia"]},e.exports["#GBPCAD-symbol-description"]={en:["British Pound / Canadian Dollar"],id_ID:["Pound Inggris / Dollar Kanada"]},e.exports["#GBPCHF-symbol-description"]={en:["British Pound / Swiss Franc"],id_ID:["Pound Inggris / Franc Swiss"]},e.exports["#GBPEUR-symbol-description"]={en:["British Pound / Euro"],id_ID:["Pound Inggris / Euro"]},e.exports["#GBPJPY-symbol-description"]={en:["British Pound / Japanese Yen"],id_ID:["Pound Inggris / Yen Jepang"]},e.exports["#GBPNZD-symbol-description"]={en:["British Pound / New Zealand Dollar"],id_ID:["Pound Inggris / Dollar New Zealand"]},e.exports["#GBPRUB-symbol-description"]={
en:["British Pound / Russian Ruble"],id_ID:["Pound Inggris / Ruble Rusia"]},e.exports["#GBPUSD-symbol-description"]={en:["British Pound / U.S. Dollar"],id_ID:["Pound Inggris / Dollar AS"]},e.exports["#GER30-symbol-description"]={en:["DAX Index"],id_ID:["Indeks DAX"]},e.exports["#GOOGL-symbol-description"]={en:["Alphabet Inc (Google) Class A"],id_ID:["Alphabet Inc (Google) Kelas A"]},e.exports["#ITA40-symbol-description"]={en:["FTSE MIB Index"],id_ID:["Indeks FTSE MIB"]},e.exports["#JPN225-symbol-description"]={en:["Nikkei 225 Index"],id_ID:["Indeks Nikkei 225"]},e.exports["#JPYKRW-symbol-description"]={en:["Japanese Yen / South Korean Won"],id_ID:["Yen Jepang / Won Korea Selatan"]},e.exports["#JPYRUB-symbol-description"]={en:["Japanese Yen / Russian Ruble"],id_ID:["Yen Jepang / Ruble Rusia"]},e.exports["#KA1-symbol-description"]={en:["Sugar #11 Futures"],id_ID:["Kontrak Berjangka Gula #11"]},e.exports["#KG1-symbol-description"]={en:["Cotton Futures"],id_ID:["Kontrak Berjangka Kapas"]},e.exports["#KT1-symbol-description"]={en:["Key Tronic Corр."],id_ID:["Key Tronic Corр."]},e.exports["#LKOH-symbol-description"]={en:["LUKOIL"],id_ID:["LUKOIL"]},e.exports["#LTCBTC-symbol-description"]={en:["Litecoin / Bitcoin"],id_ID:["Litecoin / Bitcoin"]},e.exports["#MGNT-symbol-description"]={en:["Magnit"],id_ID:["Magnit"]},e.exports["#MICEX-symbol-description"]={en:["MICEX Index"],id_ID:["Indeks MICEX"]},e.exports["#MNOD_ME.EQRP-symbol-description"]={en:["ADR GMK NORILSKIYNIKEL ORD SHS [REPO]"],id_ID:["ADR GMK NORILSKIYNIKEL ORD SHS [REPO]"]},e.exports["#MSFT-symbol-description"]={en:["Microsoft Corp."],id_ID:["Microsoft Corp."]},e.exports["#NAS100-symbol-description"]={en:["US 100 Cash CFD"],id_ID:["CFD Cash US 100"]},e.exports["#NGAS-symbol-description"]={en:["Natural Gas (Henry Hub)"],id_ID:["Gas Alam (Henry Hub)"]},e.exports["#NKY-symbol-description"]={en:["Nikkei 225 Index"],id_ID:["Indeks Nikkei 225"]},e.exports["#NZDJPY-symbol-description"]={en:["New Zealand Dollar / Japanese Yen"],id_ID:["Dollar New Zealand / Yen Jepang"]},e.exports["#NZDUSD-symbol-description"]={en:["New Zealand Dollar / U.S. Dollar"],id_ID:["Dollar New Zealand / Dollar AS"]},e.exports["#RB1-symbol-description"]={en:["RBOB Gasoline Futures"],id_ID:["Kontrak Berjangka Bensin RBOB"]},e.exports["#RTS-symbol-description"]={en:["Russian RTS Index"],id_ID:["Indeks RTS Rusia"]},e.exports["#SBER-symbol-description"]={en:["SBERBANK"],id_ID:["SBERBANK"]},e.exports["#SPX500-symbol-description"]={en:["S&P 500 Index"],id_ID:["Indeks S&P 500"]},e.exports["#TWTR-symbol-description"]={en:["Twitter Inc"],id_ID:["Twitter Inc"]},e.exports["#UK100-symbol-description"]={en:["FTSE 100 Index"],id_ID:["Indeks FTSE 100"]},e.exports["#USDBRL-symbol-description"]={en:["U.S. Dollar / Brazilian Real"],id_ID:["Dollar A.S. / Real Brazil"]},e.exports["#USDCAD-symbol-description"]={en:["U.S. Dollar / Canadian Dollar"],id_ID:["Dollar A.S. / Dollar Kanada"]},e.exports["#USDCHF-symbol-description"]={en:["U.S. Dollar / Swiss Franc"],id_ID:["Dollar A.S. / Franc Swiss"]},
e.exports["#USDCNY-symbol-description"]={en:["U.S. Dollar / Chinese Yuan"],id_ID:["Dollar A.S. / Yuan Cina"]},e.exports["#USDDKK-symbol-description"]={en:["U.S. Dollar / Danish Krone"],id_ID:["Dollar A.S. / Krona Denmark"]},e.exports["#USDHKD-symbol-description"]={en:["U.S. Dollar / Hong Kong Dollar"],id_ID:["Dollar A.S. / Dollar Hong Kong"]},e.exports["#USDIDR-symbol-description"]={en:["U.S. Dollar / Rupiah"],id_ID:["Dollar A.S. / Rupiah"]},e.exports["#USDINR-symbol-description"]={en:["U.S. Dollar / Indian Rupee"],id_ID:["Dollar A.S. / Rupee India"]},e.exports["#USDJPY-symbol-description"]={en:["U.S. Dollar / Japanese Yen"],id_ID:["Dollar A.S. / Yen Jepang"]},e.exports["#USDKRW-symbol-description"]={en:["U.S. Dollar / South Korean"],id_ID:["Dollar A.S. / Won Korea Selatan"]},e.exports["#USDMXN-symbol-description"]={en:["U.S. Dollar / Mexican Peso"],id_ID:["Dollar A.S. / Peso Meksiko"]},e.exports["#USDPHP-symbol-description"]={en:["U.S. Dollar / Philippine peso"],id_ID:["Dollar A.S. / Peso Filipina"]},e.exports["#USDRUB-symbol-description"]={en:["U.S. Dollar / Russian Ruble"],id_ID:["Dollar A.S. / Ruble Rusia"]},e.exports["#USDRUB_TOM-symbol-description"]={en:["U.S. Dollar / Russian Ruble TOM"],id_ID:["Dollar A.S. / Ruble TOM Rusia"]},e.exports["#USDSEK-symbol-description"]={en:["U.S. Dollar / Swedish Krona"],id_ID:["Dollar A.S. / Krona Swedia"]},e.exports["#USDSGD-symbol-description"]={en:["U.S. Dollar / Singapore Dollar"],id_ID:["Dollar A.S. / Dollar Singapura"]},e.exports["#USDTRY-symbol-description"]={en:["U.S. Dollar / Turkish Lira"],id_ID:["Dollar A.S. / Lira Turki"]},e.exports["#VTBR-symbol-description"]={en:["VTB"],id_ID:["VTB"]},e.exports["#XAGUSD-symbol-description"]={en:["Silver / U.S. Dollar"],id_ID:["Perak / Dollar A.S."]},e.exports["#XAUUSD-symbol-description"]={en:["Gold Spot / U.S. Dollar"],id_ID:["Emas / Dollar A.S."]},e.exports["#XPDUSD-symbol-description"]={en:["CFDs on Palladium"],id_ID:["CFD pada Palladium"]},e.exports["#XPTUSD-symbol-description"]={en:["Platinum / U.S. Dollar"],id_ID:["Platinum / Dollar A.S."]},e.exports["#ZS1-symbol-description"]={en:["Soybean Futures - ECBT"],id_ID:["Kontrak Berjangka Kacang Kedelai - ECBT"]},e.exports["#ZW1-symbol-description"]={en:["Wheat Futures - ECBT"],id_ID:["Kontrak Berjangka Gandum - ECBT"]},e.exports["#BTCGBP-symbol-description"]={en:["Bitcoin / British Pound"],id_ID:["Bitcoin/Pound Inggris"]},e.exports["#MICEXINDEXCF-symbol-description"]={en:["MOEX Russia Index"],id_ID:["Indeks MOEX Rusia"]},e.exports["#BTCAUD-symbol-description"]={en:["Bitcoin / Australian Dollar"],id_ID:["Bitcoin/Dollar Australia"]},e.exports["#BTCJPY-symbol-description"]={en:["Bitcoin / Japanese Yen"],id_ID:["Bitcoin/Yen Jepang"]},e.exports["#BTCBRL-symbol-description"]={en:["Bitcoin / Brazilian Real"],id_ID:["Bitcoin/Real Brazil"]},e.exports["#PT10-symbol-description"]={en:["Portugal Government Bonds 10 yr"],id_ID:["Obligasi Pemerintah Portugal 10 th"]},e.exports["#TXSX-symbol-description"]={en:["TSX 60 Index"],id_ID:["Indeks TSX 60"]},e.exports["#VIXC-symbol-description"]={
en:["TSX 60 VIX Index"],id_ID:["Indeks TSX 60 VIX"]},e.exports["#USDPLN-symbol-description"]={en:["U.S. Dollar / Polish Zloty"],id_ID:["USD/PLN"]},e.exports["#EURPLN-symbol-description"]={en:["Euro / Polish Zloty"],id_ID:["EUR/PLN"]},e.exports["#BTCPLN-symbol-description"]={en:["Bitcoin / Polish Zloty"],id_ID:["Bitcoin/Zloty Polandia"]},e.exports["#CAC40-symbol-description"]={en:["CAC 40 Index"],id_ID:["Indeks CAC 40"]},e.exports["#XBTCAD-symbol-description"]={en:["Bitcoin / Canadian Dollar"],id_ID:["Bitcoin / Dollar Kanada"]},e.exports["#ITI2!-symbol-description"]={en:["Iron Ore Futures"],id_ID:["Kontrak Berjangka Bijih Besi"]},e.exports["#ITIF2018-symbol-description"]={en:["Iron Ore Futures"],id_ID:["Kontrak Berjangka Bijih Besi"]},e.exports["#ITIF2019-symbol-description"]={en:["Iron Ore Futures"],id_ID:["Kontrak Berjangka Bijih Besi"]},e.exports["#ITIF2020-symbol-description"]={en:["Iron Ore Futures"],id_ID:["Kontrak Berjangka Bijih Besi"]},e.exports["#ITIG2018-symbol-description"]={en:["Iron Ore Futures"],id_ID:["Kontrak Berjangka Bijih Besi"]},e.exports["#ITIG2019-symbol-description"]={en:["Iron Ore Futures"],id_ID:["Kontrak Berjangka Bijih Besi"]},e.exports["#ITIG2020-symbol-description"]={en:["Iron Ore Futures"],id_ID:["Kontrak Berjangka Bijih Besi"]},e.exports["#ITIH2018-symbol-description"]={en:["Iron Ore Futures"],id_ID:["Kontrak Berjangka Bijih Besi"]},e.exports["#ITIH2019-symbol-description"]={en:["Iron Ore Futures"],id_ID:["Kontrak Berjangka Bijih Besi"]},e.exports["#ITIH2020-symbol-description"]={en:["Iron Ore Futures"],id_ID:["Kontrak Berjangka Bijih Besi"]},e.exports["#ITIJ2018-symbol-description"]={en:["Iron Ore Futures"],id_ID:["Kontrak Berjangka Bijih Besi"]},e.exports["#ITIJ2019-symbol-description"]={en:["Iron Ore Futures"],id_ID:["Kontrak Berjangka Bijih Besi"]},e.exports["#ITIJ2020-symbol-description"]={en:["Iron Ore Futures"],id_ID:["Kontrak Berjangka Bijih Besi"]},e.exports["#ITIK2018-symbol-description"]={en:["Iron Ore Futures"],id_ID:["Kontrak Berjangka Bijih Besi"]},e.exports["#ITIK2019-symbol-description"]={en:["Iron Ore Futures"],id_ID:["Kontrak Berjangka Bijih Besi"]},e.exports["#ITIK2020-symbol-description"]={en:["Iron Ore Futures"],id_ID:["Kontrak Berjangka Bijih Besi"]},e.exports["#ITIM2017-symbol-description"]={en:["Iron Ore Futures"],id_ID:["Kontrak Berjangka Bijih Besi"]},e.exports["#ITIM2018-symbol-description"]={en:["Iron Ore Futures"],id_ID:["Kontrak Berjangka Bijih Besi"]},e.exports["#ITIM2019-symbol-description"]={en:["Iron Ore Futures"],id_ID:["Kontrak Berjangka Bijih Besi"]},e.exports["#ITIM2020-symbol-description"]={en:["Iron Ore Futures"],id_ID:["Kontrak Berjangka Bijih Besi"]},e.exports["#ITIN2017-symbol-description"]={en:["Iron Ore Futures"],id_ID:["Kontrak Berjangka Bijih Besi"]},e.exports["#ITIN2018-symbol-description"]={en:["Iron Ore Futures"],id_ID:["Kontrak Berjangka Bijih Besi"]},e.exports["#ITIN2019-symbol-description"]={en:["Iron Ore Futures"],id_ID:["Kontrak Berjangka Bijih Besi"]},e.exports["#ITIN2020-symbol-description"]={en:["Iron Ore Futures"],
id_ID:["Kontrak Berjangka Bijih Besi"]},e.exports["#ITIQ2017-symbol-description"]={en:["Iron Ore Futures"],id_ID:["Kontrak Berjangka Bijih Besi"]},e.exports["#ITIQ2018-symbol-description"]={en:["Iron Ore Futures"],id_ID:["Kontrak Berjangka Bijih Besi"]},e.exports["#ITIQ2019-symbol-description"]={en:["Iron Ore Futures"],id_ID:["Kontrak Berjangka Bijih Besi"]},e.exports["#ITIQ2020-symbol-description"]={en:["Iron Ore Futures"],id_ID:["Kontrak Berjangka Bijih Besi"]},e.exports["#ITIU2017-symbol-description"]={en:["Iron Ore Futures"],id_ID:["Kontrak Berjangka Bijih Besi"]},e.exports["#ITIU2018-symbol-description"]={en:["Iron Ore Futures"],id_ID:["Kontrak Berjangka Bijih Besi"]},e.exports["#ITIU2019-symbol-description"]={en:["Iron Ore Futures"],id_ID:["Kontrak Berjangka Bijih Besi"]},e.exports["#ITIU2020-symbol-description"]={en:["Iron Ore Futures"],id_ID:["Kontrak Berjangka Bijih Besi"]},e.exports["#ITIV2017-symbol-description"]={en:["Iron Ore Futures"],id_ID:["Kontrak Berjangka Bijih Besi"]},e.exports["#ITIV2018-symbol-description"]={en:["Iron Ore Futures"],id_ID:["Kontrak Berjangka Bijih Besi"]},e.exports["#ITIV2019-symbol-description"]={en:["Iron Ore Futures"],id_ID:["Kontrak Berjangka Bijih Besi"]},e.exports["#ITIV2020-symbol-description"]={en:["Iron Ore Futures"],id_ID:["Kontrak Berjangka Bijih Besi"]},e.exports["#ITIX2017-symbol-description"]={en:["Iron Ore Futures"],id_ID:["Kontrak Berjangka Bijih Besi"]},e.exports["#ITIX2018-symbol-description"]={en:["Iron Ore Futures"],id_ID:["Kontrak Berjangka Bijih Besi"]},e.exports["#ITIX2019-symbol-description"]={en:["Iron Ore Futures"],id_ID:["Kontrak Berjangka Bijih Besi"]},e.exports["#ITIX2020-symbol-description"]={en:["Iron Ore Futures"],id_ID:["Kontrak Berjangka Bijih Besi"]},e.exports["#ITIZ2017-symbol-description"]={en:["Iron Ore Futures"],id_ID:["Kontrak Berjangka Bijih Besi"]},e.exports["#ITIZ2018-symbol-description"]={en:["Iron Ore Futures"],id_ID:["Kontrak Berjangka Bijih Besi"]},e.exports["#ITIZ2019-symbol-description"]={en:["Iron Ore Futures"],id_ID:["Kontrak Berjangka Bijih Besi"]},e.exports["#ITIZ2020-symbol-description"]={en:["Iron Ore Futures"],id_ID:["Kontrak Berjangka Bijih Besi"]},e.exports["#AMEX:GXF-symbol-description"]={en:["Global x FTSE Nordic Region ETF"],id_ID:["ETF Global x FTSE Wilayah Nordik"]},e.exports["#ASX:XAF-symbol-description"]={en:["S&P/ASX All Australian 50 Index"],id_ID:["Indeks S&P/ASX Seluruh Australia 50"]},e.exports["#ASX:XAT-symbol-description"]={en:["S&P/ASX All Australian 200 Index"],id_ID:["Indeks S&P/ASX Seluruh Australia 200"]},e.exports["#BIST:XU100-symbol-description"]={en:["BIST 100 Index"],id_ID:["Indeks BIST 100"]},e.exports["#GPW:WIG20-symbol-description"]={en:["WIG20 Index"],id_ID:["Indeks WIG20"]},e.exports["#INDEX:JKSE-symbol-description"]={en:["Jakarta Composite Index"],id_ID:["Indeks Komposit Jakarta"]},e.exports["#INDEX:KLSE-symbol-description"]={en:["Bursa Malaysia KLCI Index"],id_ID:["Indeks KLCI Bursa Malaysia"]},e.exports["#INDEX:NZD-symbol-description"]={en:["NZX 50 Index"],id_ID:["Indeks NZX 50"]},
e.exports["#INDEX:STI-symbol-description"]={en:["STI Index"],id_ID:["Indeks STI"]},e.exports["#INDEX:XLY0-symbol-description"]={en:["Shanghai Composite Index"],id_ID:["Indeks Komposit Shanghai"]},e.exports["#MOEX:MICEXINDEXCF-symbol-description"]={en:["MOEX Russia Index"],id_ID:["Indeks MOEX Rusia"]},e.exports["#NYMEX:KT1!-symbol-description"]={en:["Coffee Futures"],id_ID:["Kontrak Berjangka Kopi"]},e.exports["#OANDA:NATGASUSD-symbol-description"]={en:["CFDs on Natural Gas"],id_ID:["CFD pada Gas Alam"]},e.exports["#OANDA:USDPLN-symbol-description"]={en:["U.S. Dollar / Polish Zloty"],id_ID:["USD/PLN"]},e.exports["#TSX:TX60-symbol-description"]={en:["S&P/TSX 60 Index"],id_ID:["Indeks S&P/TSX"]},e.exports["#TSX:VBU-symbol-description"]={en:["Vanguard U.S. Aggregate Bond Index ETF (CAD-hedged) UN"],id_ID:["Indeks Agregat Obligasi ETF Vanguard US (CAD-ter-hedge) UN"]},e.exports["#TSX:VIXC-symbol-description"]={en:["S&P/TSX 60 VIX Index"],id_ID:["Indeks S&P/TSX 60 VIX"]},e.exports["#TVC:CAC40-symbol-description"]={en:["CAC 40 Index"],id_ID:["Indeks CAC 40"]},e.exports["#TVC:ES10-symbol-description"]={en:["Spain Government Bonds 10 YR"],id_ID:["Obligasi Pemerintah Spanyol 10 TH"]},e.exports["#TVC:EUBUND-symbol-description"]={en:["Euro Bund"],id_ID:["Bund Euro"]},e.exports["#TVC:GB02-symbol-description"]={en:["UK Government Bonds 2 YR"],id_ID:["Obligasi Pemerintah Inggris 2 TH"]},e.exports["#TVC:GB10-symbol-description"]={en:["UK Government Bonds 10 YR"],id_ID:["Obligasi Pemerintah Inggris 10 TH"]},e.exports["#TVC:GOLD-symbol-description"]={en:["CFDs on Gold (US$ / OZ)"],id_ID:["CFD pada Emas ($AS/OZ)"]},e.exports["#TVC:ID03-symbol-description"]={en:["Indonesia Government Bonds 3 YR"],id_ID:["Obligasi Pemerintah Indonesia 3 TH"]},e.exports["#TVC:ID10-symbol-description"]={en:["Indonesia Government Bonds 10 YR"],id_ID:["Obligasi Pemerintah Indonesia 10 TH"]},e.exports["#TVC:PALLADIUM-symbol-description"]={en:["CFDs on Palladium (US$ / OZ)"],id_ID:["CFD pada Paladium ($AS/OZ)"]},e.exports["#TVC:PT10-symbol-description"]={en:["Portugal Government Bonds 10 YR"],id_ID:["Obligasi Pemerintah Portugal 10 TH"]},e.exports["#TVC:SILVER-symbol-description"]={en:["CFDs on Silver (US$ / OZ)"],id_ID:["CFD pada Perak ($AS/OZ)"]},e.exports["#TSX:TSX-symbol-description"]={en:["S&P/TSX Composite Index"],id_ID:["Indeks Komposit S&P/TSX"]},e.exports["#OANDA:CH20CHF-symbol-description"]={en:["Swiss 20 Index"],id_ID:["Indeks Swiss 20"]},e.exports["#TVC:SHCOMP-symbol-description"]={en:["Shanghai Composite Index"],id_ID:["Indeks Komposit Shanghai"]},e.exports["#NZX:ALLC-symbol-description"]={en:["S&P/NZX All Index (Capital Index)"],id_ID:["SELURUH Indeks S&P/NZX (Indeks Kapital)"]},e.exports["#AMEX:SHYG-symbol-description"]={en:["Shares 0-5 YEAR High Yield Corporate Bond ETF"],id_ID:["Saham 0-5 TAHUN ETF Obligasi Perusahan Dengan Hasil Tinggi"]},e.exports["#TVC:AU10-symbol-description"]={en:["Australia Government Bonds 10 YR"],id_ID:["Obligasi Pemerintah Australia 10 TH"]},e.exports["#TVC:CN10-symbol-description"]={
en:["China Government Bonds 10 YR"],id_ID:["Obligasi Pemerintah Cina 10 TH"]},e.exports["#TVC:KR10-symbol-description"]={en:["Korea Government Bonds 10 YR"],id_ID:["Obligasi Pemerintah Korea 10 TH"]},e.exports["#NYMEX:RB1!-symbol-description"]={en:["RBOB Gasoline Futures"],id_ID:["Kontrak Berjangka Bensin RBOB"]},e.exports["#NYMEX:HO1!-symbol-description"]={en:["NY Harbor ULSD Futures"],id_ID:["Kontrak Berjangka Pelabuhan NY ULSD"]},e.exports["#NYMEX:AEZ1!-symbol-description"]={en:["NY Ethanol Futures"],id_ID:["Kontrak Berjangka Ethanol NY"]},e.exports["#OANDA:XCUUSD-symbol-description"]={en:["CFDs on Copper (US$ / lb)"],id_ID:["CFD pada Tembaga (US / lb)"]},e.exports["#COMEX:ZA1!-symbol-description"]={en:["Zinc Futures"],id_ID:["Kontrak Berjangka Seng"]},e.exports["#CBOT:ZW1!-symbol-description"]={en:["Wheat Futures"],id_ID:["Kontrak Berjangka Gandum"]},e.exports["#NYMEX:KA1!-symbol-description"]={en:["Sugar #11 Futures"],id_ID:["Kontrak Berjangka Gula #11"]},e.exports["#CBOT:QBC1!-symbol-description"]={en:["Corn Futures"],id_ID:["Kontrak Berjangka Jagung"]},e.exports["#CME:E61!-symbol-description"]={en:["Euro Futures"],id_ID:["Kontrak Berjangka Euro"]},e.exports["#CME:B61!-symbol-description"]={en:["British Pound Futures"],id_ID:["Kontrak Berjangka Pound Inggris"]},e.exports["#CME:QJY1!-symbol-description"]={en:["Japanese Yen Futures"],id_ID:["Kontrak Berjangka Yen Jepang"]},e.exports["#CME:A61!-symbol-description"]={en:["Australian Dollar Futures"],id_ID:["Kontrak Berjangka Dollar Australia"]},e.exports["#CME:D61!-symbol-description"]={en:["Canadian Dollar Futures"],id_ID:["Kontrak Berjangka Dollar Kanada"]},e.exports["#CME:SP1!-symbol-description"]={en:["S&P 500 Futures"],id_ID:["Kontrak Berjangka S&P 500"]},e.exports["#CME_MINI:NQ1!-symbol-description"]={en:["NASDAQ 100 E-mini Futures"],id_ID:["Kontrak Berjangka NASDAQ 100 E-MINI"]},e.exports["#CBOT_MINI:YM1!-symbol-description"]={en:["E-mini Dow Jones ($5) Futures"],id_ID:["Kontrak Berjangka E-MINI DOW JONES ($5)"]},e.exports["#CME:NY1!-symbol-description"]={en:["NIKKEI 225 Futures"],id_ID:["Kontrak Berjangka NIKKEI 225"]},e.exports["#EUREX:DY1!-symbol-description"]={en:["DAX Index"],id_ID:["Indeks DAX"]},e.exports["#CME:IF1!-symbol-description"]={en:["IBOVESPA Index Futures-US$"],id_ID:["Kontrak Berjangka Indeks IBOVESPA-$AS"]},e.exports["#CBOT:TY1!-symbol-description"]={en:["10 Year T-Note Futures"],id_ID:["Kontrak Berjangka T-Note 10 Tahun"]},e.exports["#CBOT:FV1!-symbol-description"]={en:["5 Year T-Note Futures"],id_ID:["Kontrak Berjangka T-Note 5 Tahun"]},e.exports["#CBOT:ZE1!-symbol-description"]={en:["Treasury Notes - 3 Year Futures"],id_ID:["Catatan Departemen Keuangan - Kontrak Berjangka 3 Tahun"]},e.exports["#CBOT:TU1!-symbol-description"]={en:["2 Year T-Note Futures"],id_ID:["Kontrak Berjangka T-Note 2 Tahun"]},e.exports["#CBOT:FF1!-symbol-description"]={en:["30-Day FED Funds Interest Rate Futures"],id_ID:["Kontrak Berjangka Suku Bunga Dana FED 30-Hari"]},e.exports["#CBOT:US1!-symbol-description"]={en:["T-Bond Futures"],
id_ID:["Kontrak Berjangka T-Bond"]},e.exports["#TVC:EXY-symbol-description"]={en:["Euro Currency Index"],id_ID:["Indeks Mata Uang Euro"]},e.exports["#TVC:JXY-symbol-description"]={en:["Japanese Yen Currency Index"],id_ID:["Indeks Mata Uang Yen Jepang"]},e.exports["#TVC:BXY-symbol-description"]={en:["British Pound Currency Index"],id_ID:["Indeks Mata Uang Pound Inggris"]},e.exports["#TVC:AXY-symbol-description"]={en:["Australian Dollar Currency Index"],id_ID:["Indeks Mata Uang Dollar Australia"]},e.exports["#TVC:CXY-symbol-description"]={en:["Canadian Dollar Currency Index"],id_ID:["Indeks Mata Uang Dollar Kanada"]},e.exports["#FRED:GDP-symbol-description"]={en:["Gross Domestic Product, 1 Decimal"],id_ID:["Produk Domestik Bruto, 1 Desimal"]},e.exports["#FRED:UNRATE-symbol-description"]={en:["Civilian Unemployment Rate"],id_ID:["Tingkat Pengangguran Warga Sipil"]},e.exports["#FRED:POP-symbol-description"]={en:["Total Population: All Ages Including Armed Forces Overseas"],id_ID:["Total Populasi. Seluruh Usia Termasuk Pasukan Bersenjata Di Luar Negeri"]},e.exports["#ETHUSD-symbol-description"]={en:["Ethereum / U.S. Dollar"],id_ID:["Ethereum / Dollar"]},e.exports["#BMFBOVESPA:IBOV-symbol-description"]={en:["Bovespa Index"],id_ID:["Indeks Bovespa"]},e.exports["#BMFBOVESPA:IBRA-symbol-description"]={en:["IBrasil Index"],id_ID:["Indeks IBrasil"]},e.exports["#BMFBOVESPA:IBXL-symbol-description"]={en:["Brazil 50 Index"],id_ID:["Indeks Brazil 50"]},e.exports["#COMEX:HG1!-symbol-description"]={en:["Copper Futures"],id_ID:["Kontrak Berjangka Tembaga"]},e.exports["#INDEX:HSCE-symbol-description"]={en:["Hang Seng China Enterprises Index"],id_ID:["Indeks Perusahaan Cina Hang Seng"]},e.exports["#NYMEX:CL1!-symbol-description"]={en:["Light Crude Oil Futures"],id_ID:["Kontrak Berjangka Minyak Mentah Ringan"]},e.exports["#OTC:IHRMF-symbol-description"]={en:["Ishares MSCI Japan SHS"],id_ID:["Ishares MSCI Jepang SHS"]},e.exports["#TVC:DAX-symbol-description"]={en:["DAX Index"],id_ID:["Indeks dari 30 Perusahaan Mayor Jerman"]},e.exports["#TVC:DE10-symbol-description"]={en:["German Government Bonds 10 YR"],id_ID:["Obligasi Pemerintah Jerman 10 TH"]},e.exports["#TVC:DJI-symbol-description"]={en:["Dow Jones Industrial Average Index"],id_ID:["Indeks Rata-rata Industri Dow Jones"]},e.exports["#TVC:DXY-symbol-description"]={en:["U.S. Dollar Index"],id_ID:["Indeks Dollar A.S."]},e.exports["#TVC:FR10-symbol-description"]={en:["France Government Bonds 10 YR"],id_ID:["Obligasi Pemerintah Perancis 10 TH"]},e.exports["#TVC:HSI-symbol-description"]={en:["Hang Seng Index"],id_ID:["Indeks Hang Seng"]},e.exports["#TVC:IBEX35-symbol-description"]={en:["IBEX 35 Index"],id_ID:["Indeks IBEX 35"]},e.exports["#FX:AUS200-symbol-description"]={en:["S&P/ASX Index"],id_ID:["Indeks S&P/ASX"]},e.exports["#AMEX:SHY-symbol-description"]={en:["Ishares 1-3 Year Treasury Bond ETF"],id_ID:["ETF Obligasi Departemen Keuangan 1-3 Tahun Ishares"]},e.exports["#ASX:XJO-symbol-description"]={en:["S&P/ASX 200 Index"],id_ID:["Indeks S&P/ASX 200"]},
e.exports["#BSE:SENSEX-symbol-description"]={en:["S&P BSE Sensex Index"],id_ID:["Indeks S&P BSE Sensex"]},e.exports["#INDEX:MIB-symbol-description"]={en:["MIB Index"],id_ID:["Indeks MIB"]},e.exports["#INDEX:MOY0-symbol-description"]={en:["Euro Stoxx 50 Index"],id_ID:["Indeks Euro STOXX 50"]},e.exports["#MOEX:RTSI-symbol-description"]={en:["RTS Index"],id_ID:["Indeks RTS"]},e.exports["#NSE:NIFTY-symbol-description"]={en:["Nifty 50 Index"],id_ID:["Indeks Nifty 50"]},e.exports["#NYMEX:NG1!-symbol-description"]={en:["Natural Gas Futures"],id_ID:["Kontrak Berjangka Gas Alam"]},e.exports["#NYMEX:ZC1!-symbol-description"]={en:["Corn Futures"],id_ID:["Kontrak Berjangka Jagung"]},e.exports["#TVC:IN10-symbol-description"]={en:["India Government Bonds 10 YR"],id_ID:["Obligasi Pemerintah India 10 TH"]},e.exports["#TVC:IT10-symbol-description"]={en:["Italy Government Bonds 10 YR"],id_ID:["Obligasi Pemerintah Italia 10 TH"]},e.exports["#TVC:JP10-symbol-description"]={en:["Japan Government Bonds 10 YR"],id_ID:["Obligasi Pemerintah Jepang 10 TH"]},e.exports["#TVC:NDX-symbol-description"]={en:["US 100 Index"],id_ID:["Indeks US 100"]},e.exports["#TVC:NI225-symbol-description"]={en:["Nikkei 225 Index"],id_ID:["NIKKEI 225"]},e.exports["#TVC:SPX-symbol-description"]={en:["S&P 500 Index"],id_ID:["S&P 500"]},e.exports["#TVC:SX5E-symbol-description"]={en:["Euro Stoxx 50 Index"],id_ID:["Indeks STOXX 50"]},e.exports["#TVC:TR10-symbol-description"]={en:["Turkey Government Bonds 10 YR"],id_ID:["Obligasi Pemerintah Turki 10 TH"]},e.exports["#TVC:UKOIL-symbol-description"]={en:["CFDs on Brent Crude Oil"],id_ID:["CFD pada Minyak Mentah Brent"]},e.exports["#TVC:UKX-symbol-description"]={en:["UK 100 Index"],id_ID:["Indeks UK 100"]},e.exports["#TVC:US02-symbol-description"]={en:["US Government Bonds 2 YR"],id_ID:["Obligasi Pemerintah AS 2 TH"]},e.exports["#TVC:US05-symbol-description"]={en:["US Government Bonds 5 YR"],id_ID:["Obligasi Pemerintah AS 5 TH"]},e.exports["#TVC:US10-symbol-description"]={en:["US Government Bonds 10 YR"],id_ID:["Obligasi Pemerintah AS 10 TH"]},e.exports["#TVC:USOIL-symbol-description"]={en:["CFDs on WTI Crude Oil"],id_ID:["CFD pada Minyak Mentah WTI"]},e.exports["#NYMEX:ITI1!-symbol-description"]={en:["Iron Ore Futures"],id_ID:["Kontrak Berjangka Bijih Besi"]},e.exports["#NASDAQ:SHY-symbol-description"]={en:["Ishares 1-3 Year Treasury Bond ETF"],id_ID:["ETF Obligasi Departemen Keuangan 1-3 Tahun Ishares"]},e.exports["#AMEX:ALD-symbol-description"]={en:["WisdomTree Asia Local Debt ETF"],id_ID:["ETF Hutang Lokal Asia WisdomTree"]},e.exports["#NASDAQ:AMD-symbol-description"]={en:["Advanced Micro Devices Inc"],id_ID:["Advanced Micro Devices Inc"]},e.exports["#NYSE:BABA-symbol-description"]={en:["Alibaba Group Holdings Ltd."],id_ID:["Alibaba Group Holdings Ltd."]},e.exports["#ICEEUR:CB-symbol-description"]={en:["Crude Oil Brent"],id_ID:["Brent Minyak Mentah"]},e.exports["#ICEEUR:CB1!-symbol-description"]={en:["Brent Crude Oil"],id_ID:["Minyak Mentah Brent"]},e.exports["#ICEUSA:CC-symbol-description"]={en:["Cocoa"],
id_ID:["Kakao"]},e.exports["#NYMEX:CL-symbol-description"]={en:["Crude Oil WTI"],id_ID:["WTI Minyak Mentah"]},e.exports["#ICEUSA:CT-symbol-description"]={en:["Cotton #2"],id_ID:["Kapas #2"]},e.exports["#NASDAQ:CTRV-symbol-description"]={en:["ContraVir Pharmaceuticals Inc"],id_ID:["ContraVir Pharmaceuticals Inc"]},e.exports["#CME:DL-symbol-description"]={en:["Class III Milk"],id_ID:["Susu Kelas III"]},e.exports["#NYSE:F-symbol-description"]={en:["FORD MTR CO DEL"],id_ID:["FORD MTR CO DEL"]},e.exports["#MOEX:GAZP-symbol-description"]={en:["GAZPROM"],id_ID:["GAZPROM"]},e.exports["#COMEX:GC-symbol-description"]={en:["Gold"],id_ID:["Emas"]},e.exports["#CME:GF-symbol-description"]={en:["Feeder Cattle"],id_ID:["Ternak Pengumpan"]},e.exports["#CME:HE-symbol-description"]={en:["Lean Hogs"],id_ID:["Lean Hogs"]},e.exports["#NASDAQ:IEF-symbol-description"]={en:["Ishares 7-10 Year Treasury Bond ETF"],id_ID:["ETF Obligasi Departemen Keuangan Ishares 7-10 Tahun"]},e.exports["#NASDAQ:IEI-symbol-description"]={en:["Ishares 3-7 Year Treasury Bond ETF"],id_ID:["ETF Obligasi Departemen Keuangan Ishares 3-7 Tahun"]},e.exports["#NYMEX:KA1-symbol-description"]={en:["Sugar #11 Futures"],id_ID:["Kontrak Berjangka Gula #11"]},e.exports["#ICEUSA:KC-symbol-description"]={en:["Coffee"],id_ID:["Kopi"]},e.exports["#NYMEX:KG1-symbol-description"]={en:["Cotton Futures"],id_ID:["Kontrak Berjangka Kapas"]},e.exports["#FWB:KT1-symbol-description"]={en:["Key Tronic Corр."],id_ID:["Key Tronic Corр"]},e.exports["#CME:LE-symbol-description"]={en:["Live Cattle"],id_ID:["Ternak Hidup"]},e.exports["#ICEEUR:LO-symbol-description"]={en:["ICE Heating Oil"],id_ID:["Minyak Pemanas ICE"]},e.exports["#CME:LS-symbol-description"]={en:["Lumber"],id_ID:["Kayu"]},e.exports["#MOEX:MGNT-symbol-description"]={en:["MAGNIT"],id_ID:["MAGNIT"]},e.exports["#LSIN:MNOD-symbol-description"]={en:["ADR GMK NORILSKIYNIKEL ORD SHS [REPO]"],id_ID:["ADR GMK NORILSKIYNIKEL ORD SHS [REPO]"]},e.exports["#NYMEX:NG-symbol-description"]={en:["Natural Gas"],id_ID:["Gas Alam"]},e.exports["#ICEUSA:OJ-symbol-description"]={en:["Orange Juice"],id_ID:["Jus Jeruk"]},e.exports["#NYMEX:PA-symbol-description"]={en:["Palladium"],id_ID:["Palladium"]},e.exports["#NYSE:PBR-symbol-description"]={en:["PETROLEO BRASILEIRO SA PETROBR"],id_ID:["PETROLEO BRASILEIRO SA PETROBR"]},e.exports["#NYMEX:PL-symbol-description"]={en:["Platinum"],id_ID:["Platinum"]},e.exports["#COMEX_MINI:QC-symbol-description"]={en:["E-Mini Copper"],id_ID:["Tembaga E-Mini"]},e.exports["#NYMEX:RB-symbol-description"]={en:["Gasoline RBOB"],id_ID:["Bensin RBOB"]},e.exports["#NYMEX:RB1-symbol-description"]={en:["RBOB Gasoline Futures"],id_ID:["Kontrak Berjangka Bensin RBOB"]},e.exports["#MOEX:SBER-symbol-description"]={en:["SBERBANK"],id_ID:["SBERBANK"]},e.exports["#AMEX:SCHO-symbol-description"]={en:["Schwab Short-Term U.S. Treasury ETF"],id_ID:["ETF Departemen Keuangan AS Jangka Pendek Schwab"]},e.exports["#COMEX:SI-symbol-description"]={en:["Silver"],id_ID:["Perak"]},e.exports["#NASDAQ:TLT-symbol-description"]={
en:["Ishares 20+ Year Treasury Bond ETF"],id_ID:["ETF Obligasi Departemen Keuangan 20+ Tahun Ishares"]},e.exports["#TVC:VIX-symbol-description"]={en:["Volatility S&P 500 Index"],id_ID:["Indeks Volatilitas S&P 500"]},e.exports["#MOEX:VTBR-symbol-description"]={en:["VTB"],id_ID:["VTB"]},e.exports["#COMEX:ZA-symbol-description"]={en:["Zinc"],id_ID:["Seng"]},e.exports["#CBOT:ZC-symbol-description"]={en:["Corn"],id_ID:["Jagung"]},e.exports["#CBOT:ZK-symbol-description"]={en:["Ethanol Futures"],id_ID:["Kontrak Berjangka Ethanol"]},e.exports["#CBOT:ZL-symbol-description"]={en:["Soybean Oil"],id_ID:["Minyak Kedelai"]},e.exports["#CBOT:ZO-symbol-description"]={en:["Oats"],id_ID:["Oats"]},e.exports["#CBOT:ZR-symbol-description"]={en:["Rough Rice"],id_ID:["Beras Gabah"]},e.exports["#CBOT:ZS-symbol-description"]={en:["Soybeans"],id_ID:["Kacang Kedelai"]},e.exports["#CBOT:ZS1-symbol-description"]={en:["Soybean Futures"],id_ID:["Kontrak Berjangka Kacang Kedelai"]},e.exports["#CBOT:ZW-symbol-description"]={en:["Wheat"],id_ID:["Gandum"]},e.exports["#CBOT:ZW1-symbol-description"]={en:["Wheat Futures - ECBT"],id_ID:["Kontrak Berjangka Gandum - ECBT"]},e.exports["#NASDAQ:ITI-symbol-description"]={en:["Iteris Inc"],id_ID:["Iteris Inc"]},e.exports["#NYMEX:ITI2!-symbol-description"]={en:["Iron Ore Futures"],id_ID:["Kontrak Berjangka Bijih Besi"]},e.exports["#CADUSD-symbol-description"]={en:["Canadian Dollar / U.S. Dollar"],id_ID:["Dollar Kanada / Dollar AS"]},e.exports["#CHFUSD-symbol-description"]={en:["Swiss Franc / U.S. Dollar"],id_ID:["Franc Swiss / Dollar AS"]},e.exports["#GPW:ACG-symbol-description"]={en:["Acautogaz"],id_ID:["Acautogaz"]},e.exports["#JPYUSD-symbol-description"]={en:["Japanese Yen / U.S. Dollar"],id_ID:["Yen Jepang / Dollar AS"]},e.exports["#USDAUD-symbol-description"]={en:["U.S. Dollar / Australian Dollar"],id_ID:["Dollar AS / Dollar Australia"]},e.exports["#USDEUR-symbol-description"]={en:["U.S. Dollar / Euro"],id_ID:["Dollar AS / Euro"]},e.exports["#USDGBP-symbol-description"]={en:["U.S. Dollar / Pound Sterling"],id_ID:["Dollar AS / Pound Sterling"]},e.exports["#USDNZD-symbol-description"]={en:["U.S. Dollar / New Zealand Dollar"],id_ID:["Dollar AS / Dollar New Zealand"]},e.exports["#UKOIL-symbol-description"]={en:["CFDs on Crude Oil (Brent)"],id_ID:["CFD pada Minyak Mentah (Brent)"]},e.exports["#USOIL-symbol-description"]={en:["CFDs on Crude Oil (WTI)"],id_ID:["CFD pada Minyak Mentah (WTI)"]},e.exports["#US30-symbol-description"]={en:["Dow Jones Industrial Average Index"],id_ID:["Indeks Rata-Rata Industri Dow Jones"]},e.exports["#BCHUSD-symbol-description"]={en:["Bitcoin Cash / U.S. Dollar"],id_ID:["Bitcoin Cash / Dollar"]},e.exports["#ETCUSD-symbol-description"]={en:["Ethereum Classic / U.S. Dollar"],id_ID:["Ethereum Klasik / Dollar"]},e.exports["#GOOG-symbol-description"]={en:["Alphabet Inc (Google) Class C"],id_ID:["Alphabet Inc (Google) Kelas C"]},e.exports["#LTCUSD-symbol-description"]={en:["Litecoin / U.S. Dollar"],id_ID:["Litecoin / Dollar"]},e.exports["#XRPUSD-symbol-description"]={
en:["XRP / U.S. Dollar"],id_ID:["XRP / Dollar A.S."]},e.exports["#SP:SPX-symbol-description"]={en:["S&P 500 Index"],id_ID:["Indeks S&P500"]},e.exports["#ETCBTC-symbol-description"]={en:["Ethereum Classic / Bitcoin"],id_ID:["Ethereum Klasik / Bitcoin"]},e.exports["#ETHBTC-symbol-description"]={en:["Ethereum / Bitcoin"],id_ID:["Ethereum / Bitcoin"]},e.exports["#XRPBTC-symbol-description"]={en:["XRP / Bitcoin"],id_ID:["XRP / Bitcoin"]},e.exports["#TVC:US30-symbol-description"]={en:["US Government Bonds 30 YR"],id_ID:["Obligasi Pemerintah AS 30 Th"]},e.exports["#COMEX:SI1!-symbol-description"]={en:["Silver Futures"],id_ID:["Kontrak Berjangka Perak"]},e.exports["#BTGUSD-symbol-description"]={en:["Bitcoin Gold / U.S. Dollar"],id_ID:["Emas Bitcoin / Dollar A.S."]},e.exports["#IOTUSD-symbol-description"]={en:["IOTA / U.S. Dollar"],id_ID:["IOTA / Dollar A.S."]},e.exports["#CME:BTC1!-symbol-description"]={en:["Bitcoin CME Futures"],id_ID:["Kontrak Berjangka Bitcoin CME"]},e.exports["#COMEX:GC1!-symbol-description"]={en:["Gold Futures"],id_ID:["Kontrak Berjangka Emas"]},e.exports["#CORNUSD-symbol-description"]={en:["CFDs on Corn"],id_ID:["CFD pada Jagung"]},e.exports["#COTUSD-symbol-description"]={en:["CFDs on Cotton"],id_ID:["CFD pada Kapas"]},e.exports["#DJ:DJA-symbol-description"]={en:["Dow Jones Composite Average Index"],id_ID:["Indeks Rata-Rata Komposit Dow Jones"]},e.exports["#DJ:DJI-symbol-description"]={en:["Dow Jones Industrial Average Index"],id_ID:["Indeks Rata-Rata Industri Dow Jones"]},e.exports["#ETHEUR-symbol-description"]={en:["Ethereum / Euro"],id_ID:["Ethereum / Euro"]},e.exports["#ETHGBP-symbol-description"]={en:["Ethereum / British Pound"],id_ID:["Ethereum / Pound Inggris"]},e.exports["#ETHJPY-symbol-description"]={en:["Ethereum / Japanese Yen"],id_ID:["Ethereum / Yen Jepang"]},e.exports["#EURNOK-symbol-description"]={en:["Euro / Norwegian Krone"],id_ID:["Euro / Krone Norwegia"]},e.exports["#GBPPLN-symbol-description"]={en:["British Pound / Polish Zloty"],id_ID:["Pound Inggris / Zloty Polandia"]},e.exports["#MOEX:BR1!-symbol-description"]={en:["Brent Oil Futures"],id_ID:["Kontrak Berjangka Brent Oil"]},e.exports["#NYMEX:KG1!-symbol-description"]={en:["Cotton Futures"],id_ID:["Kontrak Berjangka Kapas"]},e.exports["#NYMEX:PL1!-symbol-description"]={en:["Platinum Futures"],id_ID:["Kontrak Berjangka Platinum"]},e.exports["#SOYBNUSD-symbol-description"]={en:["CFDs on Soybeans"],id_ID:["CFD pada Kacang Kedelai"]},e.exports["#SUGARUSD-symbol-description"]={en:["CFDs on Sugar"],id_ID:["CFD pada Gula"]},e.exports["#TVC:IXIC-symbol-description"]={en:["US Composite Index"],id_ID:["Indeks Komposit US"]},e.exports["#TVC:RU-symbol-description"]={en:["Russell 1000 Index"],id_ID:["Indeks Russell 1000"]},e.exports["#USDZAR-symbol-description"]={en:["U.S. Dollar / South African Rand"],id_ID:["Dollar A.S / Rand Afrika Selatan"]},e.exports["#WHEATUSD-symbol-description"]={en:["CFDs on Wheat"],id_ID:["CFD pada Gandum"]},e.exports["#XRPEUR-symbol-description"]={en:["XRP / Euro"],id_ID:["XRP / Euro"]},
e.exports["#CBOT:S1!-symbol-description"]={en:["Soybean Futures"],id_ID:["Kontrak Berjangka Kedelai"]},e.exports["#SP:MID-symbol-description"]={en:["S&P 400 Index"],id_ID:["Indeks S&P 400"]},e.exports["#TSX:XCUUSD-symbol-description"]={en:["CFDs on Copper"],id_ID:["CFD pada Tembaga"]},e.exports["#TVC:NYA-symbol-description"]={en:["NYSE Composite Index"],id_ID:["Indeks Komposit NYSE"]},e.exports["#TVC:PLATINUM-symbol-description"]={en:["CFDs on Platinum (US$ / OZ)"],id_ID:["CFD pada Platimun ($AS / OZ)"]},e.exports["#TVC:SSMI-symbol-description"]={en:["Swiss Market Index"],id_ID:["Indeks Pasar Swiss"]},e.exports["#TVC:SXY-symbol-description"]={en:["Swiss Franc Currency Index"],id_ID:["Indeks Mata Uang Franc Swiss"]},e.exports["#MOEX:RI1!-symbol-description"]={en:["RTS Index Futures"],id_ID:["Kontrak Berjangka Indeks RTS"]},e.exports["#MOEX:MX1!-symbol-description"]={en:["MICEX Index Futures"],id_ID:["Kontrak Berjangka Indeks MICEX"]},e.exports["#CBOE:BG1!-symbol-description"]={en:["Bitcoin CBOE Futures"],id_ID:["Kontrak Berjangka CBOE Bitcoin"]},e.exports["#TVC:MY10-symbol-description"]={en:["Malaysia Government Bonds 10 YR"],id_ID:["Obligasi Pemerintah Malaysia 10 TH"]},e.exports["#CME:S61!-symbol-description"]={en:["Swiss Franc Futures"],id_ID:["Kontrak Berjangka Franc Swiss"]},e.exports["#TVC:DEU30-symbol-description"]={en:["DAX Index"],id_ID:["Indeks DAX"]},e.exports["#BCHEUR-symbol-description"]={en:["Bitcoin Cash / Euro"],id_ID:["Bitcoin Cash / Euro"]},e.exports["#TVC:ZXY-symbol-description"]={en:["New Zealand Dollar Currency Index"],id_ID:["Indeks Mata Uang Dollar New Zealand"]},e.exports["#MIL:FTSEMIB-symbol-description"]={en:["FTSE MIB Index"],id_ID:["Indeks FTSE MIB"]},e.exports["#XETR:DAX-symbol-description"]={en:["DAX Index"],id_ID:["Indeks DAX"]},e.exports["#MOEX:IMOEX-symbol-description"]={en:["MOEX Russia Index"],id_ID:["Indeks MOEX Rusia"]},e.exports["#FX:US30-symbol-description"]={en:["Dow Jones Industrial Average Index"],id_ID:["Indeks Rata-Rata Industri Dow Jones"]},e.exports["#MOEX:RUAL-symbol-description"]={en:["United Company RUSAL PLC"],id_ID:["United Company RUSAL PLC"]},e.exports["#MOEX:MX2!-symbol-description"]={en:["MICEX Index Futures"],id_ID:["Kontrak Berjangka Indeks MICEX"]},e.exports["#NEOUSD-symbol-description"]={en:["NEO / U.S. Dollar"],id_ID:["NEO / Dollar A.S."]},e.exports["#XMRUSD-symbol-description"]={en:["Monero / U.S. Dollar"],id_ID:["Monero  / Dollar A.S."]},e.exports["#ZECUSD-symbol-description"]={en:["Zcash / U.S. Dollar"],id_ID:["Zcash / Dollar A.S."]},e.exports["#TVC:CAC-symbol-description"]={en:["CAC 40 Index"],id_ID:["Indeks CAC 40"]},e.exports["#NASDAQ:ZS-symbol-description"]={en:["Zscaler Inc"],id_ID:["Zscaler Inc"]},e.exports["#TVC:GB10Y-symbol-description"]={en:["UK Government Bonds 10 YR Yield"],id_ID:["Obligasi Pemerintah Inggris Yield 10 TH"]},e.exports["#TVC:AU10Y-symbol-description"]={en:["Australia Government Bonds 10 YR Yield"],id_ID:["Obligasi Pemerintah Australia Yield 10 TH"]},e.exports["#TVC:CN10Y-symbol-description"]={
en:["China Government Bonds 10 YR Yield"],id_ID:["Obligasi Pemerintah Cina Yield 10 TH"]},e.exports["#TVC:DE10Y-symbol-description"]={en:["German Government Bonds 10 YR Yield"],id_ID:["Obligasi Pemerintah Jerman Yield 10 TH"]},e.exports["#TVC:ES10Y-symbol-description"]={en:["Spain Government Bonds 10 YR Yield"],id_ID:["Obligasi Pemerintah Spanyol Yield 10 TH"]},e.exports["#TVC:FR10Y-symbol-description"]={en:["France Government Bonds 10 YR Yield"],id_ID:["Obligasi Pemerintah Perancis Yield 10 TH"]},e.exports["#TVC:IN10Y-symbol-description"]={en:["India Government Bonds 10 YR Yield"],id_ID:["Obligasi Pemerintah India Yield 10 TH"]},e.exports["#TVC:IT10Y-symbol-description"]={en:["Italy Government Bonds 10 YR Yield"],id_ID:["Obligasi Pemerintah Italia Yield 10 TH"]},e.exports["#TVC:JP10Y-symbol-description"]={en:["Japan Government Bonds 10 YR Yield"],id_ID:["Obligasi Pemerintah Jepang Yield 10 TH"]},e.exports["#TVC:KR10Y-symbol-description"]={en:["Korea Government Bonds 10 YR Yield"],id_ID:["Obligasi Pemerintah Korea Yield 10 TH"]},e.exports["#TVC:MY10Y-symbol-description"]={en:["Malaysia Government Bonds 10 YR Yield"],id_ID:["Obligasi Pemerintah Malaysia Yield 10 TH"]},e.exports["#TVC:PT10Y-symbol-description"]={en:["Portugal Government Bonds 10 YR Yield"],id_ID:["Obligasi Pemerintah Portugis Yield 10 TH"]},e.exports["#TVC:TR10Y-symbol-description"]={en:["Turkey Government Bonds 10 YR Yield"],id_ID:["Obligasi Pemerintah Turki Yield 10 TH"]},e.exports["#TVC:US02Y-symbol-description"]={en:["US Government Bonds 2 YR Yield"],id_ID:["Obligasi Pemerintah A.S Yield 2 TH"]},e.exports["#TVC:US05Y-symbol-description"]={en:["US Government Bonds 5 YR Yield"],id_ID:["Obligasi Pemerintah A.S Yield 5 TH"]},e.exports["#TVC:US10Y-symbol-description"]={en:["US Government Bonds 10 YR Yield"],id_ID:["Obligasi Pemerintah A.S Yield 10 TH"]},e.exports["#INDEX:TWII-symbol-description"]={en:["Taiwan Weighted Index"],id_ID:["Indeks Terbebani Taiwan"]},e.exports["#CME:J61!-symbol-description"]={en:["Japanese Yen Futures"],id_ID:["Kontrak Berjangka Yen Jepang"]},e.exports["#CME_MINI:J71!-symbol-description"]={en:["Japanese Yen E-mini Futures"],id_ID:["Kontrak Berjangka E-mini Yen Jepang"]},e.exports["#CME_MINI:WM1!-symbol-description"]={en:["E-micro Japanese Yen / U.S. Dollar Futures"],id_ID:["Kontrak Berjangka E-micro Yen Jepang / Dollar A.S."]},e.exports["#CME:M61!-symbol-description"]={en:["Mexican Peso Futures"],id_ID:["Kontrak Berjangka Peso Meksiko"]},e.exports["#CME:T61!-symbol-description"]={en:["South African Rand Futures"],id_ID:["Kontrak Berjangka Rand Afrika Selatan"]},e.exports["#CME:SK1!-symbol-description"]={en:["Swedish Krona Futures"],id_ID:["Kontrak Berjangka Krona Swedia"]},e.exports["#CME:QT1!-symbol-description"]={en:["Chinese Renminbi / U.S. Dollar Futures"],id_ID:["Kontrak Berjangka Renminbi Cina / Dollar A.S"]},e.exports["#COMEX:AUP1!-symbol-description"]={en:["Aluminum MW U.S. Transaction Premium Platts (25MT) Futures"],id_ID:["Kontrak Berjangka Aluminum MW U.S Transaction Premium Platts (25MT)"]},
e.exports["#CME:L61!-symbol-description"]={en:["Brazilian Real Futures"],id_ID:["Kontrak Berjangka Real Brazil"]},e.exports["#CME:WP1!-symbol-description"]={en:["Polish Zloty Futures"],id_ID:["Kontrak Berjangka Zloty Polandia"]},e.exports["#CME:N61!-symbol-description"]={en:["New Zealand Dollar Futures"],id_ID:["Kontrak Berjangka Dollar New Zealand"]},e.exports["#CME_MINI:MG1!-symbol-description"]={en:["E-micro Australian Dollar / U.S. Dollar Futures"],id_ID:["Kontrak Berjangka E-micro Australian Dollar / Dollar A.S"]},e.exports["#CME_MINI:WN1!-symbol-description"]={en:["E-micro Swiss Franc / U.S. Dollar Futures"],id_ID:["Kontrak Berjangka E-micro Franc Swiss / Dollar A.S"]},e.exports["#CME_MINI:MF1!-symbol-description"]={en:["E-micro Euro / U.S. Dollar Futures"],id_ID:["Kontrak Berjangka E-micro Euro / Dollar A.S"]},e.exports["#CME_MINI:E71!-symbol-description"]={en:["Euro E-mini Futures"],id_ID:["Kontrak Berjangka E-mini Euro"]},e.exports["#CBOT:ZK1!-symbol-description"]={en:["Denatured Fuel Ethanol Futures"],id_ID:["Kontrak Berjangka Bahan Bakar Etanol Denaturisasi"]},e.exports["#CME_MINI:MB1!-symbol-description"]={en:["E-micro British Pound / U.S. Dollar Futures"],id_ID:["Kontrak Berjangka E-micro Pound Inggris / Dollar A.S"]},e.exports["#NYMEX_MINI:QU1!-symbol-description"]={en:["E-mini Gasoline Futures"],id_ID:["Kontrak Berjangka Bensin E-mini"]},e.exports["#NYMEX_MINI:QX1!-symbol-description"]={en:["E-mini Heating Oil Futures"],id_ID:["Kontrak Berjangka Minyak Pemanas E-mini"]},e.exports["#COMEX_MINI:QC1!-symbol-description"]={en:["E-mini Copper Futures"],id_ID:["Kontrak Berjangka Tembaga E-mini"]},e.exports["#NYMEX_MINI:QG1!-symbol-description"]={en:["E-mini Natural Gas Futures"],id_ID:["Kontrak Berjangka Gas Alam E-mini"]},e.exports["#CME:E41!-symbol-description"]={en:["U.S. Dollar / Turkish Lira Futures"],id_ID:["Kontrak Berjangka Dollar A.S / Lira Turki"]},e.exports["#COMEX_MINI:QI1!-symbol-description"]={en:["Silver (Mini) Futures"],id_ID:["Kontrak Berjangka Perak (Mini)"]},e.exports["#CME:DL1!-symbol-description"]={en:["Milk, Class III Futures"],id_ID:["Kontrak Berjangka Susu, Kelas III"]},e.exports["#NYMEX:UX1!-symbol-description"]={en:["Uranium Futures"],id_ID:["Kontrak Berjangka Uranium"]},e.exports["#CBOT:BO1!-symbol-description"]={en:["Soybean Oil Futures"],id_ID:["Kontrak Berjangka Minyak Kedelai"]},e.exports["#CME:HE1!-symbol-description"]={en:["Lean Hogs Futures"],id_ID:["Kontrak Berjangka Daging Babi"]},e.exports["#NYMEX:IAC1!-symbol-description"]={en:["Newcastle Coal Futures"],id_ID:["Kontrak Berjangka Batu Bara New Castle"]},e.exports["#NYMEX_MINI:QM1!-symbol-description"]={en:["E-mini Light Crude Oil Futures"],id_ID:["Kontrak Berjangka Minyak Mentah Ringan E-mini"]},e.exports["#NYMEX:JMJ1!-symbol-description"]={en:["Mini Brent Financial Futures"],id_ID:["Kontrak Berjangka Finansial Brent Mini"]},e.exports["#COMEX:AEP1!-symbol-description"]={en:["Aluminium European Premium Futures"],id_ID:["Kontrak Berjangka Aluminium Premium Eropa"]},e.exports["#CBOT:ZQ1!-symbol-description"]={
en:["30 Day Federal Funds Interest Rate Futures"],id_ID:["Kontrak Berjangka Suku Bunga Dana Federal 30 Hari"]},e.exports["#CME:LE1!-symbol-description"]={en:["Live Cattle Futures"],id_ID:["Kontrak Berjangka Ternak Hidup"]},e.exports["#CME:UP1!-symbol-description"]={en:["Swiss Franc / Japanese Yen Futures"],id_ID:["Kontrak Berjangka Franc Swiss / Yen Jepang"]},e.exports["#CBOT:ZN1!-symbol-description"]={en:["10 Year T-Note Futures"],id_ID:["Kontrak Berjangka T-Note 10 Tahun"]},e.exports["#CBOT:ZB1!-symbol-description"]={en:["T-Bond Futures"],id_ID:["Kontrak Berjangka T-Bond"]},e.exports["#CME:GF1!-symbol-description"]={en:["Feeder Cattle Futures"],id_ID:["Kontrak Berjangka Pengumpan Ternak"]},e.exports["#CBOT:UD1!-symbol-description"]={en:["Ultra T-Bond Futures"],id_ID:["Kontrak Berjangka Ultra T-Bond"]},e.exports["#CME:I91!-symbol-description"]={en:["CME Housing Futures — Washington DC"],id_ID:["Kontrak Berjangka Perumahan CME - Washington DC"]},e.exports["#CBOT:ZO1!-symbol-description"]={en:["Oat Futures"],id_ID:["Kontrak Berjangka Oat"]},e.exports["#CBOT:ZM1!-symbol-description"]={en:["Soybean Meal Futures"],id_ID:["Kontrak Berjangka Makanan Kedelai"]},e.exports["#CBOT_MINI:XN1!-symbol-description"]={en:["Corn Mini Futures"],id_ID:["Kontrak Berjangka Mini Jagung"]},e.exports["#CBOT:ZC1!-symbol-description"]={en:["Corn Futures"],id_ID:["Kontrak Berjangka Jagung"]},e.exports["#CME:LS1!-symbol-description"]={en:["Lumber Futures"],id_ID:["Kontrak Berjangka Kayu"]},e.exports["#CBOT_MINI:XW1!-symbol-description"]={en:["Wheat Mini Futures"],id_ID:["Kontrak Berjangka Gandum"]},e.exports["#CBOT_MINI:XK1!-symbol-description"]={en:["Soybean Mini Futures"],id_ID:["Kontrak Berjangka Mini Kedelai"]},e.exports["#CBOT:ZS1!-symbol-description"]={en:["Soybean Futures"],id_ID:["Kontrak Berjangka Kedelai"]},e.exports["#NYMEX:PA1!-symbol-description"]={en:["Palladium Futures"],id_ID:["Kontrak Berjangka Palladium"]},e.exports["#CME:FTU1!-symbol-description"]={en:["E-mini FTSE 100 Index USD Futures"],id_ID:["Kontrak Berjangka Indeks E-mini FTSE 100 USD"]},e.exports["#CBOT:ZR1!-symbol-description"]={en:["Rice Futures"],id_ID:["Kontrak Berjangka Beras"]},e.exports["#COMEX_MINI:GR1!-symbol-description"]={en:["Gold (E-micro) Futures"],id_ID:["Kontrak Berjangka Emas (E-micro)"]},e.exports["#COMEX_MINI:QO1!-symbol-description"]={en:["Gold (Mini) Futures"],id_ID:["Kontrak Berjangka Emas (mini)"]},e.exports["#CME_MINI:RL1!-symbol-description"]={en:["E-mini Russell 1000 Futures"],id_ID:["E-mini Russell 1000"]},e.exports["#CME_MINI:EW1!-symbol-description"]={en:["S&P 400 Midcap E-mini Futures"],id_ID:["Kontrak Berjangka E-mini S&P 400"]},e.exports["#COMEX:LD1!-symbol-description"]={en:["Lead Futures"],id_ID:["Kontrak Berjangka Timah"]},e.exports["#CME_MINI:ES1!-symbol-description"]={en:["S&P 500 E-mini Futures"],id_ID:["Kontrak Berjangka E-mini S&P 500"]},e.exports["#TVC:SA40-symbol-description"]={en:["South Africa Top 40 Index"],id_ID:["Indeks 40 Teratas Afrika Selatan"]},e.exports["#BMV:ME-symbol-description"]={en:["S&P/BMV IPC Index"],
id_ID:["Indeks S&P/BMV IPC"]},e.exports["#BCBA:IMV-symbol-description"]={en:["S&P MERVAL Index"],id_ID:["Indeks S&P MERVAL"]},e.exports["#HSI:HSI-symbol-description"]={en:["Hang Seng Index"],id_ID:["Indeks Hang Seng"]},e.exports["#BVL:SPBLPGPT-symbol-description"]={en:["S&P / BVL Peru General Index (PEN)"],id_ID:["Indeks Umum S&P / BVL Peru (PEN)"]},e.exports["#EGX:EGX30-symbol-description"]={en:["EGX 30 Index"],id_ID:["Indeks EGX 30"]},e.exports["#BVC:IGBC-symbol-description"]={en:["Indice General de la Bolsa de Valores de Colombia"],id_ID:["Indeks Umum dari Bursa Saham Kolombia"]},e.exports["#TWSE:TAIEX-symbol-description"]={en:["Taiwan Capitalization Weighted Stock Index"],id_ID:["Indeks Saham Terbebani Kapitalisasi Taiwan"]},e.exports["#QSE:GNRI-symbol-description"]={en:["QE Index"],id_ID:["Indeks QE"]},e.exports["#BME:IBC-symbol-description"]={en:["IBEX 35 Index"],id_ID:["Indeks IBEX 35"]},e.exports["#NZX:NZ50G-symbol-description"]={en:["S&P / NZX 50 Index Gross"],id_ID:["Indeks S&P / NZX 50 Bruto"]},e.exports["#SIX:SMI-symbol-description"]={en:["Swiss Market Index"],id_ID:["Indeks Pasar Swiss"]},e.exports["#SZSE:399001-symbol-description"]={en:["Shenzhen Component Index"],id_ID:["Indeks Komponen Shenzhen"]},e.exports["#TADAWUL:TASI-symbol-description"]={en:["Tadawul All Shares Index"],id_ID:["Indeks Seluruh Saham Tadawul"]},e.exports["#IDX:COMPOSITE-symbol-description"]={en:["IDX Composite Index"],id_ID:["Indeks Harga Saham Gabungan IDX"]},e.exports["#EURONEXT:PX1-symbol-description"]={en:["CAC 40 Index"],id_ID:["Indeks CAC 40"]},e.exports["#OMXHEX:OMXH25-symbol-description"]={en:["OMX Helsinki 25 Index"],id_ID:["Indeks OMX Helsinki 25"]},e.exports["#EURONEXT:BEL20-symbol-description"]={en:["BEL 20 Index"],id_ID:["Indeks BEL 20"]},e.exports["#TVC:STI-symbol-description"]={en:["Straits Times Index"],id_ID:["Indeks Straits Times"]},e.exports["#DFM:DFMGI-symbol-description"]={en:["DFM Index"],id_ID:["Indeks DFM"]},e.exports["#TVC:KOSPI-symbol-description"]={en:["Korea Composite Stock Price Index"],id_ID:["Indeks Harga Saham Komposit Korea"]},e.exports["#FTSEMYX:FBMKLCI-symbol-description"]={en:["FTSE Bursa Malaysia KLCI Index"],id_ID:["Indeks KLCI FTSE Bursa Malaysia"]},e.exports["#TASE:TA35-symbol-description"]={en:["TA-35 Index"],id_ID:["Indeks TA-35"]},e.exports["#OMXSTO:OMXS30-symbol-description"]={en:["OMX Stockholm 30 Index"],id_ID:["Indeks OMX Stockholm 30"]},e.exports["#OMXICE:OMXI8-symbol-description"]={en:["OMX Iceland 8 Index"],id_ID:["Indeks OMX Islandia 8"]},e.exports["#NSENG:NSE30-symbol-description"]={en:["NSE 30 Index"],id_ID:["Indeks NSE 30"]},e.exports["#BAHRAIN:BSEX-symbol-description"]={en:["Bahrain All Share Index"],id_ID:["Indeks Seluruh Saham Bahrain"]},e.exports["#OMXTSE:OMXTGI-symbol-description"]={en:["OMX Tallinn Gross Index"],id_ID:["Indeks Gross OMX Tallinn"]},e.exports["#OMXCOP:OMXC25-symbol-description"]={en:["OMX Copenhagen 25 Index"],id_ID:["Indeks OMX Copenhagen 25"]},e.exports["#OMXRSE:OMXRGI-symbol-description"]={en:["OMX Riga Gross Index"],id_ID:["Indeks Gross OMX Riga"]},
e.exports["#BELEX:BELEX15-symbol-description"]={en:["BELEX 15 Index"],id_ID:["Indeks BELEX 15"]},e.exports["#OMXVSE:OMXVGI-symbol-description"]={en:["OMX Vilnius Gross Index"],id_ID:["Indeks Gross OMX Vilnius"]},e.exports["#EURONEXT:AEX-symbol-description"]={en:["AEX Index"],id_ID:["Indeks AEX"]},e.exports["#CBOE:VIX-symbol-description"]={en:["Volatility S&P 500 Index"],id_ID:["Indeks Volatilitas S&P 500"]},e.exports["#NASDAQ:XAU-symbol-description"]={en:["PHLX Gold and Silver Sector Index"],id_ID:["Indeks Sektor Emas dan Perak PHLX"]},e.exports["#DJ:DJUSCL-symbol-description"]={en:["Dow Jones U.S. Coal Index"],id_ID:["Indeks Batu Bara Dow Jones A.S"]},e.exports["#DJ:DJCIKC-symbol-description"]={en:["Dow Jones Commodity Index Coffee"],id_ID:["Indeks Komoditas Dow Jones Kopi"]},e.exports["#DJ:DJCIEN-symbol-description"]={en:["Dow Jones Commodity Index Energy"],id_ID:["Indeks Komoditas Dow Jones Energi"]},e.exports["#NASDAQ:OSX-symbol-description"]={en:["PHLX Oil Service Sector Index"],id_ID:["Indeks Sektor Layanan Minyak PHLX"]},e.exports["#DJ:DJCISB-symbol-description"]={en:["Dow Jones Commodity Index Sugar"],id_ID:["Indeks Komoditas Dow Jones Gula"]},e.exports["#DJ:DJCICC-symbol-description"]={en:["Dow Jones Commodity Index Cocoa"],id_ID:["Indeks Komoditas Dow Jones Kokoa"]},e.exports["#DJ:DJCIGR-symbol-description"]={en:["Dow Jones Commodity Index Grains"],id_ID:["Indeks Komoditas Dow Jones Gandum"]},e.exports["#DJ:DJCIAGC-symbol-description"]={en:["Dow Jones Commodity Index Agriculture Capped Component"],id_ID:["Indeks Komoditas Dow Jones Komponen Terbatas Pertanian"]},e.exports["#DJ:DJCISI-symbol-description"]={en:["Dow Jones Commodity Index Silver"],id_ID:["Indeks Komoditas Dow Jones Perak"]},e.exports["#DJ:DJCIIK-symbol-description"]={en:["Dow Jones Commodity Index Nickel"],id_ID:["Indeks Komoditas Dow Jones Nikel"]},e.exports["#NASDAQ:HGX-symbol-description"]={en:["PHLX Housing Sector Index"],id_ID:["Indeks Sektor Perumahan PHLX"]},e.exports["#DJ:DJCIGC-symbol-description"]={en:["Dow Jones Commodity Index Gold"],id_ID:["Indeks Komoditas Dow Jones Emas"]},e.exports["#SP:SPGSCI-symbol-description"]={en:["S&P Goldman Sachs Commodity Index"],id_ID:["Indeks Komoditas S&P Goldman Sachs"]},e.exports["#NASDAQ:UTY-symbol-description"]={en:["PHLX Utility Sector Index"],id_ID:["Indeks Sektor Utilitas PHLX"]},e.exports["#DJ:DJU-symbol-description"]={en:["Dow Jones Utility Average Index"],id_ID:["Indeks Utilitas Rata-RataDow Jones"]},e.exports["#SP:SVX-symbol-description"]={en:["S&P 500 Value Index"],id_ID:["Indeks Nilai S&P 500"]},e.exports["#SP:OEX-symbol-description"]={en:["S&P 100 Index"],id_ID:["Indeks S&P 100"]},e.exports["#CBOE:OEX-symbol-description"]={en:["S&P 100 Index"],id_ID:["Indeks S&P 100"]},e.exports["#NASDAQ:SOX-symbol-description"]={en:["Philadelphia Semiconductor Index"],id_ID:["Indeks Semikonduktor Philadelphia"]},e.exports["#RUSSELL:RUI-symbol-description"]={en:["Russell 1000 Index"],id_ID:["Indeks Russell 1000"]},e.exports["#RUSSELL:RUA-symbol-description"]={en:["Russell 3000 Index"],
id_ID:["Indeks Russell 3000"]},e.exports["#RUSSELL:RUT-symbol-description"]={en:["Russell 2000 Index"],id_ID:["Indeks Russell 2000"]},e.exports["#NYSE:XMI-symbol-description"]={en:["NYSE ARCA Major Market Index"],id_ID:["Indeks Pasar Mayor NYSE ARCA"]},e.exports["#NYSE:XAX-symbol-description"]={en:["AMEX Composite Index"],id_ID:["Indeks Komposit AMEX"]},e.exports["#NASDAQ:NDX-symbol-description"]={en:["Nasdaq 100 Index"],id_ID:["Indeks 100 Nasdaq"]},e.exports["#NASDAQ:IXIC-symbol-description"]={en:["Nasdaq Composite Index"],id_ID:["Indeks Komposit Nasdaq"]},e.exports["#DJ:DJT-symbol-description"]={en:["Dow Jones Transportation Average Index"],id_ID:["Indeks Rata-Rata Transportasi Dow Jones"]},e.exports["#NYSE:NYA-symbol-description"]={en:["NYSE Composite Index"],id_ID:["Indeks Komposit NYSE"]},e.exports["#NYMEX:CJ1!-symbol-description"]={en:["Cocoa Futures"],id_ID:["Kontrak Berjangka Kokoa"]},e.exports["#USDILS-symbol-description"]={en:["U.S. Dollar / Israeli Shekel"],id_ID:["Dollar A.S. / Shekel Israel"]},e.exports["#TSXV:F-symbol-description"]={en:["Fiore Gold Inc"],id_ID:["Fiore Gold Inc"]},e.exports["#SIX:F-symbol-description"]={en:["Ford Motor Company"],id_ID:["Perusahaan Ford Motor"]},e.exports["#BMV:F-symbol-description"]={en:["Ford Motor Company"],id_ID:["Perusahaan Ford Motor"]},e.exports["#TWII-symbol-description"]={en:["Taiwan Weighted Index"],id_ID:["Indeks Terbebani Taiwan"]},e.exports["#TVC:PL10Y-symbol-description"]={en:["Poland Government Bonds 10 YR Yield"],id_ID:["Obligasi Pemerintah Polandia Yield 10 TH"]},e.exports["#TVC:PL05Y-symbol-description"]={en:["Poland Government Bonds 5 YR Yield"],id_ID:["Obligasi Pemerintah Polandia Yield 5 TH"]},e.exports["#SET:GC-symbol-description"]={en:["Global Connections Public Company"],id_ID:["Perusahaan Publik Global Connections"]},e.exports["#TSX:GC-symbol-description"]={en:["Great Canadian Gaming Corporation"],id_ID:["Great Canadian Gaming Corporation"]},e.exports["#TVC:FTMIB-symbol-description"]={en:["Milano Italia Borsa Index"],id_ID:["Indeks Milano Italia Borsa"]},e.exports["#OANDA:SPX500USD-symbol-description"]={en:["S&P 500 Index"],id_ID:["Indeks S&P 500"]},e.exports["#BMV:CT-symbol-description"]={en:["China SX20 RT"],id_ID:["China SX20 RT"]},e.exports["#TSXV:CT-symbol-description"]={en:["Centenera Mining Corporation"],id_ID:["Perusahaan Tambang Centenera"]},e.exports["#BYBIT:ETHUSD-symbol-description"]={en:["ETHUSD Perpetual Contract"],id_ID:["Kontrak Berlanjut ETHUSD"]},e.exports["#BYBIT:XRPUSD-symbol-description"]={en:["XRPUSD Perpetual Contract"],id_ID:["Kontrak Berlanjut XRPUSD"]},e.exports["#BYBIT:BTCUSD-symbol-description"]={en:["BTCUSD Perpetual Contract"],id_ID:["Kontrak Berlanjut BTCUSD"]},e.exports["#BITMEX:ETHUSD-symbol-description"]={en:["ETHUSD Perpetual Futures Contract"],id_ID:["Kontrak Berjangka Berlanjut ETHUSD"]},e.exports["#DERIBIT:BTCUSD-symbol-description"]={en:["BTCUSD Perpetual Futures Contract"],id_ID:["Kontrak Berjangka Berlanjut BTCUSD"]},e.exports["#DERIBIT:ETHUSD-symbol-description"]={en:["ETHUSD Perpetual Futures Contract"],
id_ID:["Kontrak Berjangka Berlanjut ETHUSD"]},e.exports["#USDHUF-symbol-description"]={en:["U.S. Dollar / Hungarian Forint"],id_ID:["Dollar A.S / Forint Hungaria"]},e.exports["#USDTHB-symbol-description"]={en:["U.S. Dollar / Thai Baht"],id_ID:["Dollar A.S / Baht Thailand"]},e.exports["#FOREXCOM:US2000-symbol-description"]={en:["US Small Cap 2000"],id_ID:["Cap Kecil AS 2000"]},e.exports["#TSXV:PBR-symbol-description"]={en:["Para Resources Inc"],id_ID:["Para Resources Inc"]},e.exports["#NYSE:SI-symbol-description"]={en:["Silvergate Capital Corporation"],id_ID:["Silvergate Capital Corporation"]},e.exports["#NASDAQ:LE-symbol-description"]={en:["Lands' End Inc"],id_ID:["Lands' End Inc"]},e.exports["#CME:CB1!-symbol-description"]={en:["Butter Futures-Cash (Continuous: Current contract in front)"],id_ID:["Kontrak Berjangka Mentega-Kas (Berlanjut: Kontrak saat ini didepan)"]},e.exports["#LSE:SCHO-symbol-description"]={en:["Scholium Group Plc Ord 1P"],id_ID:["Scholium Group Plc Ord 1P"]},e.exports["#NEO:HE-symbol-description"]={en:["Hanwei Energy Services Corp."],id_ID:["Hanwei Energy Services Corp."]},e.exports["#NYSE:HE-symbol-description"]={en:["Hawaiian Electric Industries"],id_ID:["Industri-Industri Elektrik Hawaii"]},e.exports["#OMXCOP:SCHO-symbol-description"]={en:["Schouw & Co A/S"],id_ID:["Schouw & Co A/S"]},e.exports["#TSX:HE-symbol-description"]={en:["Hanwei Energy Services Corp."],id_ID:["Hanwei Energy Services Corp."]},e.exports["#BSE:ITI-symbol-description"]={en:["ITI Ltd"],id_ID:["ITI Ltd"]},e.exports["#NSE:ITI-symbol-description"]={en:["Indian Telephone Industries Limited"],id_ID:["Indian Telephone Industries Limited"]},e.exports["#TSX:LS-symbol-description"]={en:["Middlefield Healthcare & Life Sciences Dividend Fund"],id_ID:["Dana Dividen Middlefield Healthcare & Life Sciences"]},e.exports["#BITMEX:XBT-symbol-description"]={en:["Bitcoin / U.S. Dollar Index"],id_ID:["Indeks Bitcoin / Dollar A.S."]},e.exports["#CME_MINI:RTY1!-symbol-description"]={en:["E-Mini Russell 2000 Index Futures"],id_ID:["Indeks Kontrak Berjangka E-Mini Russell 2000"]},e.exports["#CRYPTOCAP:TOTAL-symbol-description"]={en:["Crypto Total Market Cap, $"],id_ID:["Total Cap Pasar Crypto, $"]},e.exports["#ICEUS:DX1!-symbol-description"]={en:["U.S. Dollar Index Futures"],id_ID:["Kontrak Berjangka Indeks Dollar A.S."]},e.exports["#NYMEX:TT1!-symbol-description"]={en:["Cotton Futures"],id_ID:["Kontrak Berjangka Kapas"]},e.exports["#PHEMEX:BTCUSD-symbol-description"]={en:["BTC Perpetual Futures Contract"],id_ID:["Kortrak Berjangka Menerus BTC"]},e.exports["#PHEMEX:ETHUSD-symbol-description"]={en:["ETH Perpetual Futures Contract"],id_ID:["Kortrak Berjangka Menerus ETH"]},e.exports["#PHEMEX:XRPUSD-symbol-description"]={en:["XRP Perpetual Futures Contract"],id_ID:["Kortrak Berjangka Menerus XRP"]},e.exports["#PHEMEX:LTCUSD-symbol-description"]={en:["LTC Perpetual Futures Contract"],id_ID:["Kortrak Berjangka Menerus LTC"]},e.exports["#BITCOKE:BCHUSD-symbol-description"]={en:["BCH Quanto Swap"],id_ID:["BCH Quanto Swap"]},
e.exports["#BITCOKE:BTCUSD-symbol-description"]={en:["BTC Quanto Swap"],id_ID:["BTC Quanto Swap"]},e.exports["#BITCOKE:ETHUSD-symbol-description"]={en:["ETH Quanto Swap"],id_ID:["ETH Quanto Swap"]},e.exports["#BITCOKE:LTCUSD-symbol-description"]={en:["LTC Quanto Swap"],id_ID:["LTC Quanto Swap"]},e.exports["#TVC:CA10-symbol-description"]={en:["Canadian Government Bonds, 10 YR"],id_ID:["Kanada - Obligasi Pemerintah 10 TH"]},e.exports["#TVC:CA10Y-symbol-description"]={en:["Canadian Government Bonds 10 YR Yield"],id_ID:["Kanada - Obligasi Pemerintah Yield 10 TH"]},e.exports["#TVC:ID10Y-symbol-description"]={en:["Indonesia Government Bonds 10 YR Yield"],id_ID:["Indonesia - Obligasi Pemerintah Yield 10 TH"]},e.exports["#TVC:NL10-symbol-description"]={en:["Netherlands Government Bonds, 10 YR"],id_ID:["Belanda  - Obligasi Pemerintah 10 TH"]},e.exports["#TVC:NL10Y-symbol-description"]={en:["Netherlands Government Bonds 10 YR Yield"],id_ID:["Belanda - Obligasi Pemerintah Yield 10 TH"]},e.exports["#TVC:NZ10-symbol-description"]={en:["New Zealand Government Bonds, 10 YR"],id_ID:["New Zealand - Obligasi Pemerintah 10 TH"]},e.exports["#TVC:NZ10Y-symbol-description"]={en:["New Zealand Government Bonds 10 YR Yield"],id_ID:["New Zealand - Obligasi Pemerintah Yield 10 TH"]},e.exports["#SOLUSD-symbol-description"]={en:["Solana / U.S. Dollar"],id_ID:["Solana / Dollar A.S."]},e.exports["#LUNAUSD-symbol-description"]={en:["Luna / U.S. Dollar"],id_ID:["Luna / Dollar A.S."]},e.exports["#UNIUSD-symbol-description"]={en:["Uniswap / U.S. Dollar"],id_ID:["Uniswap / Dollar A.S."]},e.exports["#LTCBRL-symbol-description"]={en:["Litecoin / Brazilian Real"],id_ID:["Litecoin / Real Brazil"]},e.exports["#ETCEUR-symbol-description"]={en:["Ethereum Classic / Euro"],id_ID:["Ethereum Classic / Euro"]},e.exports["#ETHKRW-symbol-description"]={en:["Ethereum / South Korean Won"],id_ID:["Ethereum / Won Korea Selatan"]},e.exports["#BTCRUB-symbol-description"]={en:["Bitcoin / Russian Ruble"],id_ID:["Bitcoin / Ruble Rusia"]},e.exports["#BTCTHB-symbol-description"]={en:["Bitcoin / Thai Baht"],id_ID:["Bitcoin / Baht Thailand"]},e.exports["#ETHTHB-symbol-description"]={en:["Ethereum / Thai Baht"],id_ID:["Ethereum / Baht Thailand"]},e.exports["#TVC:EU10YY-symbol-description"]={en:["Euro Government Bonds 10 YR Yield"],id_ID:["Yield 10 TH Obligasi Pemerintah Euro"]},e.exports["#NASDAQ:LCID-symbol-description"]={en:["Lucid Group, Inc."],id_ID:["Lucid Group, Inc."]},e.exports["#TADAWUL:2370-symbol-description"]={en:["Middle East Specialized Cables Co."],id_ID:["Middle East Specialized Cables Co."]},e.exports["#NASDAQ:GOOG-symbol-description"]={en:["#NASDAQ:GOOG-symbol-description"],id_ID:["#NASDAQ:GOOG-symbol-description"]},e.exports["#NASDAQ:GOOGL-symbol-description"]={en:["#NASDAQ:GOOGL-symbol-description"],id_ID:["#NASDAQ:GOOGL-symbol-description"]}}}]);