"use strict";(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[9498],{99992:(e,l,t)=>{t.r(l),t.d(l,{exportData:()=>m});var s=t(11542),i=t(50151),n=t(19466),o=t(72708),u=t(91988),a=t(8025),d=t(68805),r=t(94113),c=t(41991),f=t(9859),h=t(17730);const p={includeTime:!0,includeUserTime:!1,includeSeries:!0,includeDisplayedValues:!1,includedStudies:"all",includeOffsetStudyValues:!1,includeOHLCValuesForSingleValuePlots:!1};function m(e,l={}){const t=Object.assign({},p,l),s={schema:[],data:[],displayedData:[],indexes:[]},n=e.timeScale().points(),r=e.mainSeries(),m=r.style(),S=!l.includeOHLCValuesForSingleValuePlots&&(0,d.isSingleValueBasedStyle)(m),x=(0,i.ensureNotNull)(r.symbolInfo()),N=(0,h.getChartWidgetApiTimeConverter)(r.interval(),x,e),V=!l.includeOHLCValuesForSingleValuePlots&&(0,d.isCloseBasedSymbol)(x),I=function(e,l){const t=e.allStudies().filter((e=>e.showInObjectTree()&&e.isVisible()));if("all"===l)return t;return t.filter((e=>l.includes(e.id())))}(e,t.includedStudies),O=[];for(const e of I){const t=(0,o.isOverlayStudy)(e)?T(e,l):y(e);O.push(t)}const b=I.map((e=>e.data()));(t.includeSeries||0===b.length)&&b.push(r.bars());const w=function(e,l,t,s,n){const o=(0,i.ensureNotNull)(e.range().value(),"time scale points range"),d=function(e,l,t,s){var n,o;const d=s.from,r=s.to,c=e.range().value(),h=(0,i.ensureNotNull)(void 0!==d?e.indexOf(d,!0):(0,i.ensureNotNull)(c).firstIndex),p=(0,i.ensureNotNull)(void 0!==r?e.indexOf(r,!0):(0,i.ensureNotNull)(c).lastIndex);let m=p,g=h;for(let e=0;e<l.length;e++){const i=l[e],u=s.includeOffsetStudyValues?(0,f.max)(null!==(o=null===(n=t[e])||void 0===n?void 0:n.fieldPlotOffsets)&&void 0!==o?o:[0]):0,d=i.search(h,a.PlotRowSearchMode.NearestRight);null!==d&&d.index<m&&(m=d.index);const r=i.search(p,a.PlotRowSearchMode.NearestLeft);null!==r&&r.index+u>g&&(g=r.index+u)}return(0,i.assert)(m<=g,"Range must contain at least 1 time point"),new u.BarsRange(m,g)}(e,l,t,n),r=d.firstBar(),c=d.lastBar(),h=[];for(let e=r;e<=c;e++){const l={index:e,time:(0,i.ensureNotNull)(s.convertTimePointIndexToInternalTime(e)),publicTime:(0,i.ensureNotNull)(s.convertTimePointIndexToPublicTime(e))};if(!(void 0!==n.from&&l.time<n.from)){if(void 0!==n.to&&l.time>n.to)break;if(!n.includeOffsetStudyValues&&e>o.lastIndex)break;h.push(l)}}return h.length>0?new g(h):null}(n,b,O,N,t);if(null===w)return s;const D=w.firstBar(),F=w.lastBar();t.includeTime&&s.schema.push({type:"time"});const C=s.schema.length;t.includeUserTime&&s.schema.push({type:"userTime"});const _=s.schema.length;if(t.includeSeries){const e=r.statusProvider({hideResolution:!0}).getSplitTitle(),l=Object.values(e).filter((e=>""!==e)).join(", ");s.schema.push(...function(e,l,t,s,n){const o=[];t?o.push(P("close",e)):s?o.push(P((0,i.ensureNotNull)(n),e)):12===l?o.push(P("high",e),P("low",e)):16===l?o.push(P("high",e),P("low",e),P("close",e)):o.push(P("open",e),P("high",e),P("low",e),P("close",e));return o}(l,m,V,S,r.priceSource()))}let B=s.schema.length;for(const e of O)s.schema.push(...e.fields);const L=s.schema.length;if(0===L)return s
;for(let e=D;e<=F;++e){const l=new Float64Array(L);l.fill(NaN),s.data.push(l),s.indexes.push(e),t.includeDisplayedValues&&s.displayedData.push(new Array(L).fill(""))}if(t.includeTime||t.includeUserTime){const l=e.dateTimeFormatter();for(let e=D;e<=F;++e){const n=w.item(e),o=n.time,u=n.publicTime,a=new Date(1e3*(0,i.ensureNotNull)(u));if(t.includeTime&&(s.data[e-D][0]=(0,i.ensureNotNull)(o)),t.includeUserTime&&(s.data[e-D][C]=a.getTime()/1e3),t.includeDisplayedValues){const i=l.format(a);t.includeTime&&(s.displayedData[e-D][0]=i),t.includeUserTime&&(s.displayedData[e-D][C]=i)}}}if(t.includeSeries){const e=r.bars().range(D,F),l=(0,c.getPriceValueFormatterForSource)(r),i=e=>l(e,{ignoreLocaleNumberFormat:!0}),n=r.barFunction();e.each(((e,l)=>{const o=s.data[e-D],u=v(l[4]);if(V){if(o[_]=u,t.includeDisplayedValues){s.displayedData[e-D][_]=i(u)}}else if(S){const u=n(l);if(o[_]=u,t.includeDisplayedValues){s.displayedData[e-D][_]=i(u)}}else{const n=v(l[1]),a=v(l[2]),d=v(l[3]);if(12===m?(o[_]=a,o[_+1]=d):16===m?(o[_]=a,o[_+1]=d,o[_+2]=u):(o[_]=n,o[_+1]=a,o[_+2]=d,o[_+3]=u),t.includeDisplayedValues){const l=s.displayedData[e-D];12===m?(l[_]=i(a),l[_+1]=i(d)):16===m?(l[_]=i(a),l[_+1]=i(d),l[_+2]=i(u)):(l[_]=i(n),l[_+1]=i(a),l[_+2]=i(d),l[_+3]=i(u))}}return!1}))}for(let e=0;e<I.length;++e){const n=I[e],u=O[e];let a,r=!1,f=!1;(0,o.isOverlayStudy)(n)&&(a=n.barFunction(),r=!l.includeOHLCValuesForSingleValuePlots&&(0,d.isCloseBasedSymbol)(n.symbolInfo()),f=!l.includeOHLCValuesForSingleValuePlots&&(0,d.isSingleValueBasedStyle)(n.style()));for(let e=0;e<u.fields.length;++e){const l=(0,c.getPriceValueFormatterForStudy)(n,u.fields[e].plotId),o=e=>l(e,{ignoreLocaleNumberFormat:!0}),d=u.fieldPlotOffsets[e],h=u.fieldToPlotIndex[e],p=D-d,m=F-d,g=B+e;n.data().range(p,m).each(((e,l)=>{const n=s.data[e-p];let u;return u=r?v(l[4]):f?(0,i.ensureDefined)(a)(l):v(l[h]),n[g]=u,t.includeDisplayedValues&&(s.displayedData[e-p][g]=o(u)),!1}))}B+=u.fields.length}return s}class g{constructor(e){this._items=e,this._firstIndex=this._items[0].index,this._lastIndex=this._items[this._items.length-1].index}firstBar(){return this._firstIndex}lastBar(){return this._lastIndex}item(e){return this._items[e-this._firstIndex]}}function y(e){const l=e.metaInfo(),o={fieldToPlotIndex:[],fieldPlotOffsets:[],fields:[]},u=e.id(),a=e.properties().childs(),d=e.title(n.TitleDisplayTarget.StatusLine,!1,void 0,!1);for(let n=0;n<l.plots.length;++n){const c=l.plots[n];let f,h="";if((0,r.isPlotSupportDisplay)(c)){const e=a.styles.childs()[c.id];if(void 0!==e&&0===e.childs().display.value())continue;f=(0,i.ensureDefined)(l.styles)[c.id]}else if((0,r.isOhlcPlot)(c)){const e=a.ohlcPlots.childs()[c.target];if(void 0!==e&&0===e.childs().display.value())continue;switch(f=l.ohlcPlots&&l.ohlcPlots[c.target],c.type){case"ohlc_open":h=` (${s.t(null,void 0,t(16610))})`;break;case"ohlc_high":h=` (${s.t(null,void 0,t(78254))}`;break;case"ohlc_low":h=` (${s.t(null,void 0,t(65318))})`;break;case"ohlc_close":h=` (${s.t(null,void 0,t(62578))})`}}if(void 0===f||void 0===f.title)continue
;const p=`${f.title}${h}`;o.fields.push(S(u,d,p,c.id)),o.fieldToPlotIndex.push(n+1),o.fieldPlotOffsets.push(e.offset(c.id))}return o}function T(e,l){const t={fieldToPlotIndex:[],fieldPlotOffsets:[],fields:[]},s=e.id(),o=e.title(n.TitleDisplayTarget.StatusLine,!1,void 0,!1),u=e.style(),a=!l.includeOHLCValuesForSingleValuePlots&&(0,d.isCloseBasedSymbol)(e.symbolInfo()),r=!l.includeOHLCValuesForSingleValuePlots&&(0,d.isSingleValueBasedStyle)(u);if(a)t.fields.push(S(s,o,"close","close")),t.fieldToPlotIndex.push(4),t.fieldPlotOffsets.push(0);else if(r){const l=(0,i.ensureNotNull)(e.priceSource());t.fields.push(S(s,o,l,l)),t.fieldToPlotIndex.push(1),t.fieldPlotOffsets.push(0)}else 12===u?(t.fields.push(S(s,o,"high","high"),S(s,o,"low","low")),t.fieldToPlotIndex.push(3,2),t.fieldPlotOffsets.push(0,0)):16===u?(t.fields.push(S(s,o,"high","high"),S(s,o,"low","low"),S(s,o,"close","close")),t.fieldToPlotIndex.push(2,3,4),t.fieldPlotOffsets.push(0,0,0)):(t.fields.push(S(s,o,"open","open"),S(s,o,"high","high"),S(s,o,"low","low"),S(s,o,"close","close")),t.fieldToPlotIndex.push(1,2,3,4),t.fieldPlotOffsets.push(0,0,0,0));return t}function S(e,l,t,s){return{type:"value",sourceType:"study",sourceId:e,sourceTitle:l,plotTitle:t,plotId:s}}function P(e,l){return{type:"value",sourceType:"series",plotTitle:e,sourceTitle:l}}function v(e){return null!=e?e:NaN}}}]);