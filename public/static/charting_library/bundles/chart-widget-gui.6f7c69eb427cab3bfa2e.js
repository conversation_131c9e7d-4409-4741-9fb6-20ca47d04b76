(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[5093],{44576:e=>{e.exports={"tv-circle-logo-pair":"tv-circle-logo-pair-ocURKVwI","tv-circle-logo-pair--xxxxsmall":"tv-circle-logo-pair--xxxxsmall-ocURKVwI","tv-circle-logo-pair--xxxsmall":"tv-circle-logo-pair--xxxsmall-ocURKVwI","tv-circle-logo-pair--xxsmall":"tv-circle-logo-pair--xxsmall-ocURKVwI","tv-circle-logo-pair--xsmall":"tv-circle-logo-pair--xsmall-ocURKVwI","tv-circle-logo-pair--small":"tv-circle-logo-pair--small-ocURKVwI","tv-circle-logo-pair--medium":"tv-circle-logo-pair--medium-ocURKVwI","tv-circle-logo-pair--large":"tv-circle-logo-pair--large-ocURKVwI","tv-circle-logo-pair--xlarge":"tv-circle-logo-pair--xlarge-ocURKVwI","tv-circle-logo-pair--xxlarge":"tv-circle-logo-pair--xxlarge-ocURKVwI","tv-circle-logo-pair--xxxlarge":"tv-circle-logo-pair--xxxlarge-ocURKVwI","tv-circle-logo-pair__logo":"tv-circle-logo-pair__logo-ocURKVwI","tv-circle-logo-pair__logo--xxxxsmall":"tv-circle-logo-pair__logo--xxxxsmall-ocURKVwI","tv-circle-logo-pair__logo--xxxsmall":"tv-circle-logo-pair__logo--xxxsmall-ocURKVwI","tv-circle-logo-pair__logo--xxsmall":"tv-circle-logo-pair__logo--xxsmall-ocURKVwI","tv-circle-logo-pair__logo--xsmall":"tv-circle-logo-pair__logo--xsmall-ocURKVwI","tv-circle-logo-pair__logo--small":"tv-circle-logo-pair__logo--small-ocURKVwI","tv-circle-logo-pair__logo--medium":"tv-circle-logo-pair__logo--medium-ocURKVwI","tv-circle-logo-pair__logo--large":"tv-circle-logo-pair__logo--large-ocURKVwI","tv-circle-logo-pair__logo--xlarge":"tv-circle-logo-pair__logo--xlarge-ocURKVwI","tv-circle-logo-pair__logo--xxlarge":"tv-circle-logo-pair__logo--xxlarge-ocURKVwI","tv-circle-logo-pair__logo--xxxlarge":"tv-circle-logo-pair__logo--xxxlarge-ocURKVwI","tv-circle-logo-pair__logo-empty":"tv-circle-logo-pair__logo-empty-ocURKVwI"}},3196:e=>{e.exports={"tv-circle-logo":"tv-circle-logo-PsAlMQQF","tv-circle-logo--xxxsmall":"tv-circle-logo--xxxsmall-PsAlMQQF","tv-circle-logo--xxsmall":"tv-circle-logo--xxsmall-PsAlMQQF","tv-circle-logo--xsmall":"tv-circle-logo--xsmall-PsAlMQQF","tv-circle-logo--small":"tv-circle-logo--small-PsAlMQQF","tv-circle-logo--medium":"tv-circle-logo--medium-PsAlMQQF","tv-circle-logo--large":"tv-circle-logo--large-PsAlMQQF","tv-circle-logo--xlarge":"tv-circle-logo--xlarge-PsAlMQQF","tv-circle-logo--xxlarge":"tv-circle-logo--xxlarge-PsAlMQQF","tv-circle-logo--xxxlarge":"tv-circle-logo--xxxlarge-PsAlMQQF","tv-circle-logo--visually-hidden":"tv-circle-logo--visually-hidden-PsAlMQQF"}},49844:e=>{e.exports={loader:"loader-UL6iwcBa",static:"static-UL6iwcBa",item:"item-UL6iwcBa","tv-button-loader":"tv-button-loader-UL6iwcBa",medium:"medium-UL6iwcBa",small:"small-UL6iwcBa",black:"black-UL6iwcBa",white:"white-UL6iwcBa",gray:"gray-UL6iwcBa",primary:"primary-UL6iwcBa"}},53885:(e,t,i)=>{"use strict";i.d(t,{getStyleClasses:()=>a,isCircleLogoWithUrlProps:()=>n});var s=i(97754),l=i(3196),o=i.n(l);function a(e,t){return s(o()["tv-circle-logo"],o()[`tv-circle-logo--${e}`],t)}function n(e){
return"logoUrl"in e&&null!==e.logoUrl&&void 0!==e.logoUrl&&0!==e.logoUrl.length}},27267:(e,t,i)=>{"use strict";function s(e,t,i,s,l){function o(l){if(e>l.timeStamp)return;const o=l.target;void 0!==i&&null!==t&&null!==o&&o.ownerDocument===s&&(t.contains(o)||i(l))}return l.click&&s.addEventListener("click",o,!1),l.mouseDown&&s.addEventListener("mousedown",o,!1),l.touchEnd&&s.addEventListener("touchend",o,!1),l.touchStart&&s.addEventListener("touchstart",o,!1),()=>{s.removeEventListener("click",o,!1),s.removeEventListener("mousedown",o,!1),s.removeEventListener("touchend",o,!1),s.removeEventListener("touchstart",o,!1)}}i.d(t,{addOutsideEventListener:()=>s})},26996:(e,t,i)=>{"use strict";i.d(t,{Loader:()=>r});var s,l=i(50959),o=i(97754),a=i(49844),n=i.n(a);function r(e){const{className:t,size:i="medium",staticPosition:s,color:a="black"}=e,r=o(n().item,n()[a],n()[i]);return l.createElement("span",{className:o(n().loader,s&&n().static,t)},l.createElement("span",{className:r}),l.createElement("span",{className:r}),l.createElement("span",{className:r}))}!function(e){e.Medium="medium",e.Small="small"}(s||(s={}))},82708:(e,t,i)=>{"use strict";i.d(t,{safeShortName:()=>l});var s=i(13665);function l(e){try{return(0,s.shortName)(e)}catch(t){return e}}},618:(e,t,i)=>{"use strict";i.d(t,{removeUsdFromCryptoPairLogos:()=>a,resolveLogoUrls:()=>o});var s=i(36279);const l=(0,s.getLogoUrlResolver)();function o(e,t=s.LogoSize.Medium){const i=e.logoid,o=e["base-currency-logoid"],a=e["currency-logoid"],n=i&&l.getSymbolLogoUrl(i,t);if(n)return[n];const r=o&&l.getSymbolLogoUrl(o,t),d=a&&l.getSymbolLogoUrl(a,t);return r&&d?[r,d]:r?[r]:d?[d]:[]}function a(e){return 2!==e.length?e:function(e){return e.some((e=>n(e)))}(e)&&!function(e){return e.some((e=>e.includes("country")&&!n(e)))}(e)?e.filter((e=>!n(e))):e}function n(e){return!1}},39330:(e,t,i)=>{"use strict";i.d(t,{getBlockStyleClasses:()=>a,getLogoStyleClasses:()=>n});var s=i(97754),l=i(44576),o=i.n(l);function a(e,t){return s(o()["tv-circle-logo-pair"],o()[`tv-circle-logo-pair--${e}`],t)}function n(e,t=!0){return s(o()["tv-circle-logo-pair__logo"],o()[`tv-circle-logo-pair__logo--${e}`],!t&&o()["tv-circle-logo-pair__logo-empty"])}},59695:(e,t,i)=>{"use strict";i.d(t,{CircleLogo:()=>n,hiddenCircleLogoClass:()=>a});var s=i(50959),l=i(53885),o=i(3196);const a=i.n(o)()["tv-circle-logo--visually-hidden"];function n(e){var t,i;const o=(0,l.getStyleClasses)(e.size,e.className),a=null!==(i=null!==(t=e.alt)&&void 0!==t?t:e.title)&&void 0!==i?i:"";return(0,l.isCircleLogoWithUrlProps)(e)?s.createElement("img",{className:o,crossOrigin:"",src:e.logoUrl,alt:a,title:e.title,loading:e.loading,"aria-label":e["aria-label"],"aria-hidden":e["aria-hidden"]}):s.createElement("span",{className:o,title:e.title,"aria-label":e["aria-label"],"aria-hidden":e["aria-hidden"]},e.placeholderLetter)}},58492:(e,t,i)=>{"use strict";i.d(t,{getStyleClasses:()=>s.getStyleClasses});var s=i(53885)},3041:e=>{e.exports={}},30595:e=>{e.exports={marginlegendhoriz:"4px",legend:"legend-l31H9iuA",item:"item-l31H9iuA",
withAction:"withAction-l31H9iuA",selected:"selected-l31H9iuA",last:"last-l31H9iuA",text:"text-l31H9iuA",noWrapWrapper:"noWrapWrapper-l31H9iuA",noWrap:"noWrap-l31H9iuA",series:"series-l31H9iuA",valuesAdditionalWrapper:"valuesAdditionalWrapper-l31H9iuA",valueItem:"valueItem-l31H9iuA",valueTitle:"valueTitle-l31H9iuA",valueValue:"valueValue-l31H9iuA",hideUniportantValueItems:"hideUniportantValueItems-l31H9iuA",unimportant:"unimportant-l31H9iuA",valuesWrapper:"valuesWrapper-l31H9iuA",wrappable:"wrappable-l31H9iuA",directionColumn:"directionColumn-l31H9iuA",titlesWrapper:"titlesWrapper-l31H9iuA",button:"button-l31H9iuA",statusesWrapper:"statusesWrapper-l31H9iuA",logoWrapper:"logoWrapper-l31H9iuA",buttonsWrapper:"buttonsWrapper-l31H9iuA",buttons:"buttons-l31H9iuA",statusesWrapper__statuses:"statusesWrapper__statuses-l31H9iuA",pairContainer:"pairContainer-l31H9iuA",logo:"logo-l31H9iuA",hidden:"hidden-l31H9iuA",noActions:"noActions-l31H9iuA",titleWrapper:"titleWrapper-l31H9iuA",title:"title-l31H9iuA",intervalTitle:"intervalTitle-l31H9iuA",withDot:"withDot-l31H9iuA",accessible:"accessible-l31H9iuA",disabled:"disabled-l31H9iuA",disabledOnInterval:"disabledOnInterval-l31H9iuA",withCustomTextColor:"withCustomTextColor-l31H9iuA",study:"study-l31H9iuA",mainTitle:"mainTitle-l31H9iuA",descTitle:"descTitle-l31H9iuA",hideValues:"hideValues-l31H9iuA",has5Buttons:"has5Buttons-l31H9iuA",stayInHoveredMode:"stayInHoveredMode-l31H9iuA",withTail:"withTail-l31H9iuA",loader:"loader-l31H9iuA",providerTitle:"providerTitle-l31H9iuA",exchangeTitle:"exchangeTitle-l31H9iuA",styleTitle:"styleTitle-l31H9iuA",priceSourceTitle:"priceSourceTitle-l31H9iuA",flagged:"flagged-l31H9iuA",medium:"medium-l31H9iuA",minimized:"minimized-l31H9iuA",micro:"micro-l31H9iuA",linked:"linked-l31H9iuA",onlyOneButtonCanBeStick:"onlyOneButtonCanBeStick-l31H9iuA",touchMode:"touchMode-l31H9iuA",buttonIcon:"buttonIcon-l31H9iuA",flag:"flag-l31H9iuA",invisibleHover:"invisibleHover-l31H9iuA",eye:"eye-l31H9iuA",eyeLoading:"eyeLoading-l31H9iuA","eye-animation":"eye-animation-l31H9iuA",linking:"linking-l31H9iuA",intervalEye:"intervalEye-l31H9iuA",markerContainer:"markerContainer-l31H9iuA",flagWrapper:"flagWrapper-l31H9iuA",sourcesWrapper:"sourcesWrapper-l31H9iuA",legendMainSourceWrapper:"legendMainSourceWrapper-l31H9iuA",sources:"sources-l31H9iuA",toggler:"toggler-l31H9iuA pane-button-e6PF69Df",onlyOneSourceShown:"onlyOneSourceShown-l31H9iuA",counter:"counter-l31H9iuA",iconArrow:"iconArrow-l31H9iuA",objectTree:"objectTree-l31H9iuA",closed:"closed-l31H9iuA",objectsTreeCanBeShown:"objectsTreeCanBeShown-l31H9iuA"}},19317:e=>{e.exports={"css-value-pane-controls-padding-left":"1px","css-value-pane-controls-padding-right":"4px",css_value_pane_controls_margin_top:"4",css_value_pane_controls_button_size:"22",css_value_pane_controls_button_touch_size:"22",paneControls:"paneControls-JQv8nO8e",hidden:"hidden-JQv8nO8e",forceHidden:"forceHidden-JQv8nO8e",button:"button-JQv8nO8e pane-button-e6PF69Df",buttonIcon:"buttonIcon-JQv8nO8e",minimize:"minimize-JQv8nO8e",restore:"restore-JQv8nO8e",
newButton:"newButton-JQv8nO8e",touchMode:"touchMode-JQv8nO8e",maximize:"maximize-JQv8nO8e",collapse:"collapse-JQv8nO8e","maximize-animation-up-bracket":"maximize-animation-up-bracket-JQv8nO8e","maximize-animation-down-bracket":"maximize-animation-down-bracket-JQv8nO8e","minimize-animation-up-bracket":"minimize-animation-up-bracket-JQv8nO8e","minimize-animation-down-bracket":"minimize-animation-down-bracket-JQv8nO8e",up:"up-JQv8nO8e","up-animation":"up-animation-JQv8nO8e",down:"down-JQv8nO8e","down-animation":"down-animation-JQv8nO8e",buttonsWrapper:"buttonsWrapper-JQv8nO8e"}},76920:e=>{e.exports={blockHidden:"blockHidden-e6PF69Df","pane-button":"pane-button-e6PF69Df"}},10492:e=>{e.exports={"css-value-small-size":"18px","css-value-medium-size":"22px","css-value-large-size":"28px","css-value-border-radius-small-size":"9px","css-value-border-radius-medium-size":"11px","css-value-border-radius-large-size":"8px",statuses:"statuses-Lgtz1OtS",statusItem:"statusItem-Lgtz1OtS",statuses_hidden:"statuses_hidden-Lgtz1OtS",small:"small-Lgtz1OtS",medium:"medium-Lgtz1OtS",large:"large-Lgtz1OtS",blinking:"blinking-Lgtz1OtS","blinking-animation":"blinking-animation-Lgtz1OtS",marketStatusOpen:"marketStatusOpen-Lgtz1OtS",marketStatusClose:"marketStatusClose-Lgtz1OtS",marketStatusPre:"marketStatusPre-Lgtz1OtS",marketStatusPost:"marketStatusPost-Lgtz1OtS",marketStatusHoliday:"marketStatusHoliday-Lgtz1OtS",marketStatusDelisted:"marketStatusDelisted-Lgtz1OtS",marketStatusExpired:"marketStatusExpired-Lgtz1OtS",marketStatusCustom:"marketStatusCustom-Lgtz1OtS",invalidSymbol:"invalidSymbol-Lgtz1OtS",replayModeAutoPlay:"replayModeAutoPlay-Lgtz1OtS",replayModePause:"replayModePause-Lgtz1OtS",replayModePointSelect:"replayModePointSelect-Lgtz1OtS","blinking-animation-custom":"blinking-animation-custom-Lgtz1OtS",notAccurate:"notAccurate-Lgtz1OtS",openedInPineEditor:"openedInPineEditor-Lgtz1OtS",openedInDetachedPineEditor:"openedInDetachedPineEditor-Lgtz1OtS",delay:"delay-Lgtz1OtS",eod:"eod-Lgtz1OtS",tvCalculatedPair:"tvCalculatedPair-Lgtz1OtS",dataProblemHigh:"dataProblemHigh-Lgtz1OtS",dataProblemLow:"dataProblemLow-Lgtz1OtS"}},57177:(e,t,i)=>{"use strict";var s;function l(e){e.dispatchEvent(new CustomEvent("roving-tabindex:main-element"))}function o(e){e.dispatchEvent(new CustomEvent("roving-tabindex:secondary-element"))}i.d(t,{becomeMainElement:()=>l,becomeSecondaryElement:()=>o}),function(e){e.MainElement="roving-tabindex:main-element",e.SecondaryElement="roving-tabindex:secondary-element"}(s||(s={}))},22136:(e,t,i)=>{"use strict";i.r(t),i.d(t,{ControlBarNavigation:()=>J});var s=i(50151),l=i(11542),o=i(32563),a=i(56570),n=i(88960),r=i(60859),d=i(63273),u=i(49481),h=i(42752),c=i(61814),_=i(49483),p=i(68335),g=i(68805),m=(i(51768),i(23317)),v=i(89612),b=i(77576),w=i(93724),S=i(91986),y=i(76996),M=i(78529),C=i(50119),f=i(62884),E=i(50662),V=i(42205);i(3041);const L=(0,p.humanReadableModifiers)(p.Modifiers.Alt,!1),x=(0,p.humanReadableModifiers)(p.Modifiers.Shift,!1),W=(0,p.humanReadableModifiers)(p.Modifiers.Mod,!1),A=(0,c.hotKeySerialize)({
keys:[L,"R"],text:"{0} + {1}"}),k=(0,c.hotKeySerialize)({keys:[L,"Click",L,"Enter"],text:"{0} + {1}, {2} + {3}"}),T=(0,c.hotKeySerialize)({keys:[C],text:"{0}"}),H=(0,c.hotKeySerialize)({keys:[f],text:"{0}"}),B=(0,c.hotKeySerialize)({keys:[W,E],text:"{0} + {1}"}),I=(0,c.hotKeySerialize)({keys:[W,V],text:"{0} + {1}"}),D=(0,c.hotKeySerialize)({keys:[L,x,f],text:"{0} + {1} + {2}"}),P=l.t(null,void 0,i(88710)),z=l.t(null,void 0,i(97038)),N=l.t(null,void 0,i(61206)),R=l.t(null,void 0,i(31142)),O=l.t(null,void 0,i(90761)),F=l.t(null,void 0,i(25131)),U=l.t(null,void 0,i(75246)),G=l.t(null,void 0,i(83040));var j,K;!function(e){e[e.BarVisibleDistance=100]="BarVisibleDistance",e[e.BackButtonRightMargin=14]="BackButtonRightMargin",e[e.SingleButtonWidth=36]="SingleButtonWidth",e[e.GroupMargins=14]="GroupMargins",e[e.GoToRealtimeButtonWidth=50]="GoToRealtimeButtonWidth",e[e.LeftMargin=50]="LeftMargin",e[e.TimeAxisMainPaneMargin=27]="TimeAxisMainPaneMargin",e[e.PaneControlsHeight=28]="PaneControlsHeight"}(j||(j={})),function(e){e.ScrollLeftRight="js-btn-group-scroll",e.ZoomInZoomOut="js-btn-group-zoom",e.Maximize="js-btn-group-maximize",e.ResetScale="js-btn-group-reset-scale"}(K||(K={}));const $=`<div class="control-bar-wrapper">\n\t<div class="control-bar control-bar--hidden">\n\t\t<div class="control-bar__group js-btn-group js-btn-group-zoom">\n\t\t\t<div class="control-bar__btn control-bar__btn--zoom-out apply-common-tooltip" title="${P}" data-tooltip-hotkey="${I}">\n\t\t\t\t${m}\n\t\t\t</div>\n\t\t\t<div class="control-bar__btn control-bar__btn--zoom-in apply-common-tooltip" title="${z}" data-tooltip-hotkey="${B}">\n\t\t\t\t${b}\n\t\t\t</div>\n\t\t</div>\n\t\t<div class="control-bar__group js-btn-group js-btn-group-maximize">\n\t\t\t<div class="control-bar__btn control-bar__btn--maximize apply-common-tooltip" title="${N}" data-tooltip-hotkey="${k}">\n\t\t\t\t${S}\n\t\t\t</div>\n\t\t\t<div class="control-bar__btn control-bar__btn--minimize js-hidden apply-common-tooltip" title="${R}" data-tooltip-hotkey="${k}">\n\t\t\t\t${y}\n\t\t\t</div>\n\t\t</div>\n\t\t<div class="control-bar__group js-btn-group js-btn-group-scroll">\n\t\t\t<div class="control-bar__btn control-bar__btn--move-left apply-common-tooltip" title="${O}" data-tooltip-hotkey="${T}">\n\t\t\t\t${v}\n\t\t\t</div>\n\t\t\t<div class="control-bar__btn control-bar__btn--move-right apply-common-tooltip" title="${F}" data-tooltip-hotkey="${H}">\n\t\t\t\t${v}\n\t\t\t</div>\n\t\t</div>\n\t\t<div class="control-bar__group js-btn-group js-btn-group-reset-scale">\n\t\t\t<div class="control-bar__btn control-bar__btn--turn-button control-bar__btn--btn-hidden apply-common-tooltip js-btn-reset" title="${U}" data-tooltip-hotkey="${A}">\n\t\t\t\t${w}\n\t\t\t</div>\n\t\t</div>\n\t</div>\n</div>`,Z=`<div class="control-bar-wrapper control-bar-wrapper--back-present">\n\t<div class="control-bar control-bar__btn control-bar__btn--btn-hidden apply-common-tooltip" title="${G}" data-tooltip-hotkey="${D}">\n\t\t${M}\n\t</div>\n</div>`,Q=_.CheckMobile.any(),X="control-bar__btn--btn-hidden",q={
zoomInOut:!0,maximize:!0,scrollLeftRight:!0,resetScale:!0,goToRealtime:!0};class J{constructor(e,t,i){this._widget=(0,s.ensureNotNull)((0,u.parseHtml)($).querySelector(".control-bar-wrapper")),this._controlBar=(0,s.ensureNotNull)(this._widget.querySelector(".control-bar")),this._back=(0,s.ensureNotNull)((0,u.parseHtml)(Z).querySelector(".control-bar-wrapper")),this._btnGroups=Array.from(this._controlBar.querySelectorAll(".js-btn-group")),this._targetPaneWidget=null,this._backButtonVisible=!1,this._boundMouseHandler=null,this._chartModel=null,this._checkIntervalId=0,this._controlBarVisible=!1,this._priceAxisChanged=null,this._resetScalesAvailable=null,this._priceAxisName="right",this._rafId=0,this._visibilityTypeProperty=null,this._boundUpdateMaximizeButtonsVisibility=this._updateMaximizeButtonsVisibility.bind(this),this._boundToggleFullscreenButtons=this._toggleFullscreenButtons.bind(this),this._boundOnVisibilityTypeChanged=this._onVisibilityTypeChange.bind(this),this._paneWidth=0,this._leftPriceScaleWidth=0,this._rightPriceScaleWidth=0,this._chart=e,this._parent=t,this._options=Object.assign({},q,i),this._visibilityPrioritizedGroups=this._initGroupDescriptions(),this._init(),this._initHandlers(),this.updatePosition()}destroy(){var e;null!==this._visibilityTypeProperty&&(this._visibilityTypeProperty.destroy(),this._visibilityTypeProperty=null),null!==this._boundMouseHandler&&(this._parent.removeEventListener("mousemove",this._boundMouseHandler,!1),this._parent.removeEventListener("mouseleave",this._boundMouseHandler,!1),this._boundMouseHandler=null),null!==this._priceAxisChanged&&(this._priceAxisChanged.unsubscribe(this,this._updateBackBtnPosition),this._priceAxisChanged=null),clearInterval(this._checkIntervalId),null===(e=this._resetScalesAvailable)||void 0===e||e.destroy();const t=this._chart.getResizerDetacher();t.fullscreenable.unsubscribe(this._boundUpdateMaximizeButtonsVisibility),t.fullscreen.unsubscribe(this._boundToggleFullscreenButtons),this._chart=null}updatePosition(){const e=this._targetPaneWidget=this._getTargetPaneWidget();if(null===e)return;const t=e.getElement().querySelector(".chart-markup-table .pane");if(null===t)return;this._paneWidth=e.width(),this._leftPriceScaleWidth=this._chart.getPriceAxisMaxWidthByName("left"),this._rightPriceScaleWidth=this._chart.getPriceAxisMaxWidthByName("right");const i=this._parent.getBoundingClientRect().bottom-t.getBoundingClientRect().bottom+this._bottomMargin(e);this._widget.style.bottom=`${i}px`,this._back.style.bottom=`${i}px`,this._updateBtnGroupVisibility()}_getTargetPaneWidget(){const e=this._chart.maximizedPaneWidget();if(e)return e;const t=[...this._chart.paneWidgets()].reverse();for(const e of t)if(e.height()>=this._widget.clientHeight+28+this._bottomMargin(e))return e;return null}_bottomMargin(e){return e.containsMainSeries()?27:0}_init(){if(_.CheckMobile.any())for(const e of this._btnGroups)e.classList.add("js-hidden");this._buttons={zoomIn:this._widget.querySelector(".control-bar__btn--zoom-in"),
zoomOut:this._widget.querySelector(".control-bar__btn--zoom-out"),moveLeft:this._widget.querySelector(".control-bar__btn--move-left"),moveRight:this._widget.querySelector(".control-bar__btn--move-right"),turn:this._widget.querySelector(".control-bar__btn--turn-button"),maximize:this._widget.querySelector(".control-bar__btn--maximize"),minimize:this._widget.querySelector(".control-bar__btn--minimize")},this._parent.appendChild(this._widget),this._parent.appendChild(this._back),this._backButtonVisible=!1,this._priceAxisName=(0,d.isRtl)()?"left":"right",this._chart.withModel(this,(()=>{var e;this._chartModel=this._chart.model(),this._initVisibility(),this._priceAxisChanged=this._chart.getPriceAxisWidthChangedByName(this._priceAxisName),this._priceAxisChanged.subscribe(this,this._updateBackBtnPosition),this._resetScalesAvailable=this._chartModel.model().resetScalesAvailable().spawn(),this._resetScalesAvailable.subscribe(this._updateResetScalesButtonVisibility.bind(this),{callWithLast:!0});const t=this._chart.getResizerDetacher();t.fullscreenable.subscribe(this._boundUpdateMaximizeButtonsVisibility),t.fullscreen.subscribe(this._boundToggleFullscreenButtons),this._updateMaximizeButtonsVisibility(),this._updateBackBtnPosition(),null===(e=this._back.querySelector(".control-bar__btn"))||void 0===e||e.addEventListener("click",(()=>{null!==this._chartModel&&this._chartModel.timeScale().scrollToRealtime(!0)})),this._checkIntervalId=setInterval((()=>this._check()),1e3)}))}_initHandlers(){const e=o.mobiletouch?"touchstart":"mousedown",t=o.mobiletouch?["touchend"]:["mouseup","mouseout"];this._buttons.moveLeft.addEventListener(e,(e=>{e.preventDefault(),this._chart.scrollHelper().moveByBar(1),this._trackEvent("Move Left")})),this._buttons.moveRight.addEventListener(e,(e=>{e.preventDefault(),this._chart.scrollHelper().moveByBar(-1),this._trackEvent("Move Right")}));for(const e of t)this._buttons.moveLeft.addEventListener(e,(()=>this._chart.scrollHelper().stopMove())),this._buttons.moveRight.addEventListener(e,(()=>this._chart.scrollHelper().stopMove()));this._buttons.turn.addEventListener("click",(e=>{e.preventDefault(),this._chart.GUIResetScales(),this._trackEvent("Reset to Default Settings")})),this._buttons.zoomOut.addEventListener("click",(e=>{e.preventDefault(),null!==this._chartModel&&this._chartModel.zoomOut(),this._trackEvent("Zoom Out")})),this._buttons.zoomIn.addEventListener("click",(e=>{e.preventDefault(),null!==this._chartModel&&this._chartModel.zoomIn(),this._trackEvent("Zoom In")})),this._buttons.maximize.addEventListener("click",(e=>{e.preventDefault(),this._chart.setActive(!0),this._chart.getResizerDetacher().requestFullscreen(),this._trackEvent(" Maximize Chart")})),this._buttons.minimize.addEventListener("click",(e=>{e.preventDefault(),this._chart.getResizerDetacher().exitFullscreen(),this._trackEvent(" Restore Chart")}));const i=e=>e.addEventListener("contextmenu",(e=>e.preventDefault()));i(this._buttons.moveLeft),i(this._buttons.moveRight),i(this._buttons.turn),i(this._buttons.zoomOut),i(this._buttons.zoomIn),
i(this._buttons.minimize),i(this._buttons.maximize)}_initGroupDescriptions(){return[{shouldBeHiddenOnMobile:!1,available:this._isMaximizeButtonAvailable.bind(this),className:"js-btn-group-maximize",element:this._getBtnGroup("js-btn-group-maximize"),totalWidth:50},{shouldBeHiddenOnMobile:!1,available:()=>this._options.resetScale,className:"js-btn-group-reset-scale",element:this._getBtnGroup("js-btn-group-reset-scale"),totalWidth:50},{shouldBeHiddenOnMobile:!a.enabled("show_zoom_and_move_buttons_on_touch"),available:()=>this._options.zoomInOut,className:"js-btn-group-zoom",element:this._getBtnGroup("js-btn-group-zoom"),totalWidth:86},{shouldBeHiddenOnMobile:!a.enabled("show_zoom_and_move_buttons_on_touch"),available:()=>this._options.scrollLeftRight,className:"js-btn-group-scroll",element:this._getBtnGroup("js-btn-group-scroll"),totalWidth:86}]}_check(){var e;if(null===this._chartModel||!this._options.goToRealtime)return;const t=this._chartModel.timeScale().rightOffset()<0;t!==this._backButtonVisible&&(this._backButtonVisible=t,null===(e=this._back.querySelector(".control-bar__btn"))||void 0===e||e.classList.toggle(X,!this._backButtonVisible))}_initVisibility(){this._visibilityTypeProperty=(0,n.combine)(((e,t)=>null!==t?"alwaysOff":e),(0,h.convertPropertyToWatchedValue)((0,r.actualBehavior)()).ownership(),(0,g.getSeriesDisplayErrorWV)((0,s.ensureNotNull)(this._chartModel).mainSeries()).ownership()),this._visibilityTypeProperty.subscribe(this._boundOnVisibilityTypeChanged),this._onVisibilityTypeChange()}_onVisibilityTypeChange(){if(null===this._visibilityTypeProperty)return;const e=this._visibilityTypeProperty.value();"alwaysOn"===e||"alwaysOff"===e?(this._controlBarVisible="alwaysOn"===e,null!==this._boundMouseHandler&&(this._parent.removeEventListener("mousemove",this._boundMouseHandler,!1),this._parent.removeEventListener("mouseleave",this._boundMouseHandler,!1),this._boundMouseHandler=null)):(this._controlBarVisible=!1,this._boundMouseHandler||(this._boundMouseHandler=this._visibilityMouseHandler.bind(this),this._parent.addEventListener("mousemove",this._boundMouseHandler),this._parent.addEventListener("mouseleave",this._boundMouseHandler))),this._updateControlBarVisibility()}_visibilityMouseHandler(e){if(e.buttons)return;if(null!==this._chartModel&&this._chartModel.lineBeingCreated())return;let t="mouseleave"!==e.type;if("mousemove"===e.type){const i=this._widget.getBoundingClientRect(),s=100-(this._targetPaneWidget?this._bottomMargin(this._targetPaneWidget):0);t=e.clientX>=i.left-100&&e.clientX<=i.right+100&&e.clientY>=i.top-s&&e.clientY<=i.bottom+100}this._controlBarVisible!==t&&(this._controlBarVisible=t,null===this._rafId&&(this._rafId=this._controlBar.ownerDocument.defaultView.requestAnimationFrame(this._updateControlBarVisibility.bind(this))))}_updateControlBarVisibility(){this._rafId=null,this._controlBar.classList.toggle("control-bar--hidden",!this._controlBarVisible)}_updateBackBtnPosition(){if("left"===this._priceAxisName||"right"===this._priceAxisName){
const e=this._chart.getPriceAxisMaxWidthByName(this._priceAxisName)+14;e&&(this._back.style.marginRight=`${e}px`)}}_updateBtnGroupVisibility(){const e=this._leftPriceScaleWidth+this._paneWidth,t=(e+this._rightPriceScaleWidth)/2;let i=2*Math.min(e-t,t-this._leftPriceScaleWidth)-50-50,s=!1;for(const e of this._visibilityPrioritizedGroups){e.enoughSpaceForGroup=!1;e.available()&&(!Q||!e.shouldBeHiddenOnMobile)&&(i-=e.totalWidth,e.enoughSpaceForGroup=i>=0&&!s,s=s||!e.enoughSpaceForGroup),!e.enoughSpaceForGroup!==e.element.classList.contains("js-hidden")&&e.element.classList.toggle("js-hidden",!e.enoughSpaceForGroup)}this._updateControlBarPosition()}_getBtnGroup(e){return(0,s.ensureDefined)(this._btnGroups.find((t=>t.classList.contains(e))))}_updateControlBarPosition(){const e=this._visibilityPrioritizedGroups.reduce(((e,t)=>e+(t.enoughSpaceForGroup?t.totalWidth:0)),0),t=(this._paneWidth+this._leftPriceScaleWidth+this._rightPriceScaleWidth)/2-Math.ceil(e/2);this._widget.style.left=`${t}px`}_updateResetScalesButtonVisibility(){if(null===this._chartModel)return;const e=this._chartModel.model().resetScalesAvailable().value();this._buttons.turn.classList.toggle(X,!e)}_updateMaximizeButtonsVisibility(){this._updateBtnGroupVisibility()}_toggleFullscreenButtons(){const e=this._chart.inFullscreen();this._buttons.maximize.classList.toggle("js-hidden",e),this._buttons.minimize.classList.toggle("js-hidden",!e)}_isMaximizeButtonAvailable(){return this._options.maximize,!1}_trackEvent(e){0}}},8855:(e,t,i)=>{"use strict";i.r(t),i.d(t,{LegendWidget:()=>Fl});var s=i(27714),l=i(43370),o=i(50151),a=i(52033),n=i(64147),r=i(37265),d=i(24377),u=i(3343),h=i(27267),c=i(56570),_=i(49483);function p(e,t){null===e.firstChild?e.textContent=t:e.firstChild.nodeValue=t}var g=i(57177),m=i(19291),v=i(40281),b=i(38780),w=i(35236);const S=v.trackingModeIsAvailable?44:28;var y=i(63276),M=i(80007),C=i(26996),f=i(4237),E=i(50959),V=i(76920);const L=(x=C.Loader,W={staticPosition:!0,size:"small"},(e,t)=>function(e,t,i,s){const l=document.createElement("span"),o=(0,f.createRoot)(l);function a(e){l.classList.toggle(V.blockHidden,!e)}a(!1);const{className:n}=null!=s?s:{};return n&&l.classList.add(n),o.render((0,E.createElement)(t,i)),e.appendChild(l),{toggleVisibility:a,destroy:function(){o.unmount()}}}(e,x,W,t));var x,W,A=i(30595);const k=_.CheckMobile.any();var T,H;!function(e){e[e.Tiny=1]="Tiny",e[e.Small=2]="Small",e[e.Medium=3]="Medium",e[e.Large=4]="Large"}(T||(T={})),function(e){e[e.NoIntervalForMultiChart=1]="NoIntervalForMultiChart",e[e.NoExchangeProviderAndInterval=2]="NoExchangeProviderAndInterval",e[e.AllVisible=3]="AllVisible"}(H||(H={}));function B(e,t){e.dataset.status=t?"loading":void 0}class I{constructor(e,t,i){this._el=null,this._firstBlockWrapper=null,this._titlesWrapperEl=null,this._titleContainers=[],this._titleElements=[],this._valuesElements=[],this._actionsParentEl=null,this._actionAdditionalWrapperEl=null,this._actionElements=[],this._accessibleButtons=[],this._stayInHoveredMode=!1,this._rowMode=4,this._titlesMode=3,
this._statusesWrapper=null,this._loader=null,this._valuesParentEl=null,this._valuesAdditionalWrapperEl=null,this._resizeObserver=null,this._hideInvisibleHover=null,this._hideValues=null,this._allButtonsWidth=null,this._lastStatusesWrapperWidth=null,this._lastActionsWrapperWidth=null,this._showActionsHandler=null,this._hideActionsHandler=null,this._selectedSourceHandler=null,this._mouseEventHandlers=[],this._disableTimeout=null,this._updateDisabledState=(e=this._disabled.value())=>{null!==this._el&&(this._el.classList.toggle(A.disabled,e),this._updateLoadingState(),this._updateStatusWidgetVisibility(e),this._updateTitleMaxWidth())},this._updateLoadingState=(e=this._loading.value())=>{if(null!==this._el){this._el.classList.toggle(A.eyeLoading,e&&!this._disabled.value()),B(this._el,e)}null!==this._loader&&this._loader.toggleVisibility(e),this._updateShowValues()},this._model=e,this._parentEl=t,this._disabled=this._model.disabled().spawn(),this._disabled.subscribe(this._updateDisabledState),this._disabledOnInterval=this._model.disabledOnInterval().spawn(),this._disabledOnInterval.subscribe(this._updateDisabledOnIntervalState.bind(this)),this._selected=this._model.selected().spawn(),this._selected.subscribe(this._updateSelectedState.bind(this)),this._loading=this._model.loading().spawn(),this._loading.subscribe(function(e,t){let i=0;return s=>{clearTimeout(i),s?e():i=setTimeout(e,t)}}(this._updateLoadingState,700)),this._isTitleHidden=this._model.isTitleHidden().spawn(),this._isRowHidden=this._model.isRowHidden().spawn(),this._isTitleHidden.subscribe(this._updateShowTitles.bind(this)),this._isRowHidden.subscribe(this._updateShowLine.bind(this)),this._createTitlesSpawns();for(let e=0;e<this._titlesSpawns.length;e++)this._titlesSpawns[e].title.subscribe(this._updateTitlesHandler.bind(this,e));this._values=this._model.values().spawn(),this._values.subscribe(this._updateValues.bind(this)),this._createValuesSpawns(),this._addValuesSpawnsSubscriptions(),this._actionsSpawnArray=this._model.actions().map((e=>({visible:e.visible.spawn(),title:void 0===e.title?null:e.title.spawn()})));for(let e=0;e<this._actionsSpawnArray.length;e++){this._actionsSpawnArray[e].visible.subscribe(this._updateActionVisibilities.bind(this,e));const t=this._actionsSpawnArray[e].title;null!==t&&t.subscribe(this._updateActionTitle.bind(this,e))}this._withActions=i.withActions,this._isMultipleLayout=i.isMultipleLayout.spawn(),this._render(),this._updateStates(),this._updateShowTitles(),this._updateShowValues(),this._updateShowLine(),this._loader=L((0,o.ensureNotNull)(this._valuesParentEl),{className:A.loader}),this._customTextColor=i.customTextColor.spawn(),this._customTextColor.subscribe(this._updateCustomTextColor.bind(this)),this._updateCustomTextColor(),this._withActions&&(this._showActionsHandler=(0,M.wrapHandlerWithPreventEvent)(this._showActions.bind(this)),this._hideActionsHandler=(0,M.wrapHandlerWithPreventEvent)(this._hideActions.bind(this)),this._selectedSourceHandler=(0,
w.defaultPreventedHandler)(this._model.setSourceSelected.bind(this._model)),null!==this._titlesWrapperEl&&(k||(this._titlesWrapperEl.addEventListener("mouseenter",this._showActionsHandler),this._titlesWrapperEl.addEventListener("mouseleave",this._hideActionsHandler)),this._mouseEventHandlers.push(new w.MouseEventHandler(this._titlesWrapperEl,{mouseDoubleClickEvent:this._model.onShowSettings.bind(this._model),doubleTapEvent:this._model.onShowSettings.bind(this._model),mouseClickEvent:this._selectedSourceHandler,tapEvent:this._selectedSourceHandler}))),null===this._actionAdditionalWrapperEl||null===this._actionsParentEl||k||(this._actionAdditionalWrapperEl.addEventListener("mouseenter",this._showActionsHandler),this._actionAdditionalWrapperEl.addEventListener("mouseleave",this._hideActionsHandler),this._actionsParentEl.addEventListener("contextmenu",(e=>{e.preventDefault(),e.stopPropagation()}))))}destroy(){var e,t,i;this._disabled.destroy(),this._disabledOnInterval.destroy(),this._selected.destroy(),this._loading.destroy(),this._isTitleHidden.destroy(),this._isRowHidden.destroy(),this._customTextColor.destroy(),null===(e=this._loader)||void 0===e||e.destroy(),this._isMultipleLayout.destroy(),null!==this._disableTimeout&&clearTimeout(this._disableTimeout);for(const e of this._titlesSpawns)e.title.destroy();if(null!==this._titlesWrapperEl){for(const e of this._mouseEventHandlers)e.destroy();this._titleContainers=[],this._titleElements=[],this._withActions&&null!==this._selectedSourceHandler&&null!==this._showActionsHandler&&null!==this._hideActionsHandler&&(k||(this._titlesWrapperEl.removeEventListener("mouseenter",this._showActionsHandler),this._titlesWrapperEl.removeEventListener("mouseleave",this._hideActionsHandler))),this._titlesWrapperEl=null}for(const e of this._actionsSpawnArray){e.visible.destroy();const t=e.title;null!==t&&t.destroy()}if(this._actionElements=[],null!==this._actionAdditionalWrapperEl&&(this._withActions&&null!==this._showActionsHandler&&null!==this._hideActionsHandler&&!k&&(this._actionAdditionalWrapperEl.removeEventListener("mouseenter",this._showActionsHandler),this._actionAdditionalWrapperEl.removeEventListener("mouseleave",this._hideActionsHandler)),this._actionAdditionalWrapperEl=null),this._actionsParentEl=null,this._removeValuesSpawnsSubscriptions(),this._values.destroy(),null!==this._valuesParentEl&&(this._valuesElements=[],this._valuesParentEl=null),null===(t=this._hideInvisibleHover)||void 0===t||t.destroy(),null===(i=this._hideValues)||void 0===i||i.destroy(),null!==this._resizeObserver&&(this._resizeObserver.disconnect(),this._resizeObserver=null),null!==this._el){const e=this._el.parentNode;null==e||e.removeChild(this._el),this._el=null}}getElement(){return this._el}getHeight(){return null===this._el?null:24}updateMode(e,t){this._rowMode===e&&this._titlesMode===t&&null!==this._allButtonsWidth||(this._rowMode!==e&&(this._rowMode=e,this._updateActionsVisibilitiesByMode()),this._titlesMode!==t&&(this._titlesMode=t,this._updateTitlesVisibilitiesByMode()),this._updateAllButtonsWidth())}
accessibleButtons(){return this._isRowHidden.value()?[]:this._accessibleButtons.filter((e=>!e.classList.contains(V.blockHidden)))}showActions(){null===this._el||this._el.classList.contains(A.withAction)||this._showActions()}hideActions(){null!==this._el&&this._el.classList.contains(A.withAction)&&this._hideActions()}_updateActionsVisibilitiesByMode(){}_updateTitlesVisibilitiesByMode(){}_render(){this._renderTitles(),this._renderActions(),this._renderValues(),this._el=document.createElement("div"),B(this._el,this._loading.value()),this._firstBlockWrapper=document.createElement("div"),this._firstBlockWrapper.classList.add(A.noWrapWrapper),this._firstBlockWrapper.appendChild((0,o.ensureNotNull)(this._titlesWrapperEl)),null!==this._actionsParentEl&&this._firstBlockWrapper.appendChild(this._actionsParentEl),this._el.appendChild(this._firstBlockWrapper),this._el.appendChild((0,o.ensureNotNull)(this._valuesParentEl)),this._parentEl.append(this._el)}_renderTitles(){null===this._titlesWrapperEl&&(this._titlesWrapperEl=document.createElement("div"),this._titlesWrapperEl.classList.add(A.titlesWrapper));for(let e=0;e<this._titlesSpawns.length;e++){const{wrapper:t,title:i}=this._renderTitle(this._titlesSpawns[e]);this._titlesWrapperEl.appendChild(t),this._titleContainers.push(t),this._titleElements.push(i)}}_renderTitle(e){var t;const{class:i,title:s,titleId:l,tooltip:o,onClick:a}=e,n=a,r=null!==(t=s.value())&&void 0!==t?t:"",d=document.createElement("div");d.classList.add(A.titleWrapper,i,"apply-overflow-tooltip",A.withDot),d.dataset.name=l,void 0!==o&&(d.classList.add("apply-common-tooltip"),d.setAttribute("title",o));const u=document.createElement(n?"button":"div");if(u.classList.add(A.title),void 0!==a){d.classList.add(A.withAction);const e=e=>{var t;null===(t=this._selectedSourceHandler)||void 0===t||t.call(this,e),a(),n&&u.blur()};this._mouseEventHandlers.push(new w.MouseEventHandler(d,{mouseClickEvent:e,tapEvent:_.CheckMobile.any()?void 0:e},{ignoreClickAndTapOnDblClickOrDblTap:!0}))}if(n){const e=u;this._makeItemAccessible(e,a,null!=o?o:"")}return r.length>0?u.appendChild(document.createTextNode(r)):(d.classList.add(V.blockHidden),u.classList.add(V.blockHidden)),d.appendChild(u),{wrapper:d,title:u}}_makeItemAccessible(e,t,i){""!==i&&e.setAttribute("aria-label",i),e.type="button",e.classList.add(A.accessible),e.tabIndex=-1;const s=t=>{switch(t.type){case"roving-tabindex:main-element":e.tabIndex=0;break;case"roving-tabindex:secondary-element":e.tabIndex=-1}};e.addEventListener("roving-tabindex:main-element",s),e.addEventListener("roving-tabindex:secondary-element",s),e.addEventListener("keydown",(e=>{const i=(0,u.hashFromEvent)(e);13!==i&&32!==i||(e.preventDefault(),t(e))})),this._accessibleButtons.push(e)}_renderActions(){if(!this._withActions)return;null===this._actionsParentEl&&(this._actionsParentEl=document.createElement("div"),this._actionsParentEl.classList.add(A.buttonsWrapper),this._parentEl.append(this._actionsParentEl),this._actionAdditionalWrapperEl=document.createElement("div"),
this._actionAdditionalWrapperEl.classList.add(A.buttons),this._actionsParentEl.appendChild(this._actionAdditionalWrapperEl));const e=(0,o.ensureNotNull)(this._actionAdditionalWrapperEl);this._model.actions().forEach((t=>{var i,s;const l=void 0===t.disableAccessibility,o={iconSize:v.trackingModeIsAvailable?"large":"small",tag:l?"button":"div",buttonClassName:A.button,wrapIconClassName:A.buttonIcon,hiddenClassName:V.blockHidden,blurOnClick:!!l||void 0},a=(0,y.createActionElement)(t,o);l&&this._makeItemAccessible(a,(e=>{t.action(e)}),null!==(s=null===(i=t.title)||void 0===i?void 0:i.value())&&void 0!==s?s:""),this._actionElements.push(a),e.appendChild(a)}))}_initWrappersIfNotInitialized(){return null===this._valuesParentEl&&(this._valuesParentEl=document.createElement("div"),this._valuesParentEl.classList.add(A.valuesWrapper),this._valuesAdditionalWrapperEl=document.createElement("div"),this._valuesAdditionalWrapperEl.classList.add(A.valuesAdditionalWrapper),this._valuesParentEl.appendChild(this._valuesAdditionalWrapperEl)),(0,o.ensureNotNull)(this._valuesAdditionalWrapperEl)}_isWidthButtonsMode(){return null!==this._el&&(this._el.classList.contains(A.withAction)||this._disabled.value()||this._selected.value()||this._stayInHoveredMode)}_updateTitlesHandler(e,t){const i=(0,o.ensureNotNull)(this._titleContainers[e]),s=(0,o.ensureNotNull)(this._titleElements[e]),l=0===t.length||this._isTitleHiddenByMode(this._titlesSpawns[e]);s.classList.toggle(V.blockHidden,l),i.classList.toggle(V.blockHidden,l),p((0,o.ensureNotNull)(this._titleElements[e]),t)}_isTitleHiddenByMode(e){return!1}_updateStates(e){this._updateDisabledState(),this._updateDisabledOnIntervalState(),this._updateSelectedState(),this._updateLoadingState(),e&&this._clearDisableState()}_updateValuesHTMLElHandler(e,t){p((0,o.ensure)(this._valuesElements[e].value),t),this._updateShowValues()}_updateValueColorHandler(e,t=""){(0,o.ensure)(this._valuesElements[e].value).style.color=t}_updateValueVisibleHandler(e,t){const i=(0,o.ensure)(this._valuesElements[e].value).closest(`.${A.valueItem}`);null!==i&&i.classList.toggle(V.blockHidden,!t),this._updateShowValues()}_updateShowLine(){null!==this._el&&this._el.classList.toggle(V.blockHidden,this._isRowHidden.value())}_createValuesSpawns(){this._valuesSpawnArray=this._values.value().map((e=>({value:e.value.spawn(),color:e.color.spawn(),visible:e.visible.spawn(),title:e.title.spawn()})))}_removeValuesSpawnsSubscriptions(){for(const e of this._valuesSpawnArray)e.value.destroy(),e.color.destroy(),e.visible.destroy(),e.title.destroy();this._valuesSpawnArray=[]}_addValuesSpawnsSubscriptions(){for(let e=0;e<this._valuesSpawnArray.length;e++){const t=this._valuesSpawnArray[e];t.value.subscribe(this._updateValuesHTMLElHandler.bind(this,e)),t.color.subscribe(this._updateValueColorHandler.bind(this,e)),t.visible.subscribe(this._updateValueVisibleHandler.bind(this,e)),t.title.subscribe(this._updateValuesTitleHTMLElHandler.bind(this,e))}}_updateShowValues(){function e(e){if(!e)return
;const t=Array.from(e.children).every((e=>e.classList.contains(V.blockHidden)));e.classList.toggle(V.blockHidden,t)}e(this._valuesAdditionalWrapperEl),e(this._valuesParentEl)}_addStatusesWidget(e,t,i){this._statusesWrapper=document.createElement("div"),this._statusesWrapper.classList.add(A.statusesWrapper),e.classList.add(A.statusesWrapper__statuses),this._statusesWrapper.appendChild(e),(0,o.ensureNotNull)(this._firstBlockWrapper).appendChild(this._statusesWrapper),this._hideInvisibleHover=t.spawn(),this._hideInvisibleHover.subscribe(this._updateInvisibleHoverMode.bind(this),{callWithLast:!0}),this._hideValues=i.spawn(),this._hideValues.subscribe(this._updateHideValuesMode.bind(this),{callWithLast:!0}),this._updateStatusWidgetVisibility(this._disabled.value()),this._resizeObserver=new ResizeObserver(this._handlerRestrictTitleWidth.bind(this)),null!==this._actionsParentEl&&this._resizeObserver.observe(this._actionsParentEl),this._resizeObserver.observe(this._statusesWrapper)}_updateTitleMaxWidth(){if(null===this._firstBlockWrapper)return;const e=this._allButtonsWidth||0,t=(this._lastActionsWrapperWidth||0)+(this._lastStatusesWrapperWidth||0);this._isWidthButtonsMode()?this._firstBlockWrapper.style.maxWidth=`calc(100% - ${Math.max(e,t)}px)`:this._firstBlockWrapper.style.maxWidth=t>0?`calc(100% - ${t}px)`:""}_updateAllButtonsWidth(){this._allButtonsWidth=this._getButtonsCount()*S+1,this._updateTitleMaxWidth()}_updateInvisibleHoverMode(e){null!==this._el&&this._el.classList.toggle(A.invisibleHover,!e)}_updateHideValuesMode(e){null!==this._el&&this._el.classList.toggle(A.hideValues,e)}_showActions(){if(null===this._el||!this._withActions)return;this._el.classList.add(A.withAction);const e=null!==this._valuesParentEl&&null!==this._titlesWrapperEl&&this._valuesParentEl.offsetTop===this._titlesWrapperEl.offsetTop;this._el.classList.toggle(A.withTail,e),this._updateTitleMaxWidth()}_hideActions(){null!==this._el&&this._withActions&&!this._stayInHoveredMode&&(this._el.classList.remove(A.withAction),null!==this._valuesParentEl&&this._valuesParentEl.classList.remove(A.withTail),this._updateTitleMaxWidth())}_handlerRestrictTitleWidth(e){if(null===this._actionsParentEl||null===this._firstBlockWrapper)return;let t=null,i=null;for(const s of e)s.target===this._statusesWrapper&&(t=s.contentRect.width),s.target===this._actionsParentEl&&(i=s.contentRect.width);t===this._lastStatusesWrapperWidth&&i===this._lastActionsWrapperWidth||(null!==t&&(this._lastStatusesWrapperWidth=t),null!==i&&(this._lastActionsWrapperWidth=i),this._updateTitleMaxWidth())}_clearDisableState(){null!==this._el&&(this._el.classList.remove(A.eyeLoading),this._el.classList.remove(A.disabled),this._updateStatusWidgetVisibility(this._disabled.value()),this._updateTitleMaxWidth())}_updateDisabledOnIntervalState(){var e;null===(e=this._el)||void 0===e||e.classList.toggle(A.disabledOnInterval,this._disabledOnInterval.value())}_updateSelectedState(){null!==this._el&&this._withActions&&this._el.classList.toggle(A.selected,this._selected.value())}_updateShowTitles(){
null!==this._titlesWrapperEl&&(this._titlesWrapperEl.classList.toggle(V.blockHidden,this._isTitleHidden.value()),null!==this._actionsParentEl&&this._actionsParentEl.classList.toggle(V.blockHidden,this._isTitleHidden.value()))}_updateValues(){this._removeValuesSpawnsSubscriptions(),this._createValuesSpawns(),null!==this._valuesParentEl&&null!==this._valuesAdditionalWrapperEl&&(this._valuesElements=[],this._valuesAdditionalWrapperEl.innerHTML=""),this._renderValues(),this._addValuesSpawnsSubscriptions(),this._updateShowValues()}_updateActionVisibilities(e){null!==this._actionsParentEl&&this._actionsParentEl.querySelectorAll(`.${A.button}`)[e].classList.toggle(V.blockHidden,!this._actionsSpawnArray[e].visible.value())}_updateActionTitle(e){const t=this._actionsSpawnArray[e].title;if(null===this._actionsParentEl||null===t)return;const i=this._actionsParentEl.querySelectorAll(`.${A.button}`)[e];i.setAttribute("title",t.value()),i.hasAttribute("aria-label")&&i.setAttribute("aria-label",t.value())}_updateCustomTextColor(){const e=this._customTextColor.value()||"";for(const t of this._titleContainers)null!==t&&(t.style.color=e);const t=(0,o.ensureNotNull)(this._valuesParentEl).querySelectorAll(`.${A.valueTitle}`);for(let i=0;i<t.length;i++)t[i].style.color=e;(0,o.ensureNotNull)(this._el).classList.toggle(A.withCustomTextColor,Boolean(e))}_updateStatusWidgetVisibility(e){null!==this._statusesWrapper&&this._statusesWrapper.classList.toggle(V.blockHidden,e)}}var D=i(16230),P=i(97754),z=i.n(P),N=i(39330),R=i(58492),O=i(82708),F=i(19938),U=i(59695);class G{constructor(e,t){this._lastDrawnLogos=[],this._logoWrapper=null,this._pairContainer=null,this._primaryLogo=null,this._secondaryLogo=null,this._logoContainer=null,this._symbolLetterContainer=null,this._updateLogoVisibility=e=>{this._logoWrapper&&this._logoWrapper.classList.toggle(A.hidden,!e)},this._updateSymbolLogo=async e=>{var t,i,s,l,o,a,n,r,d;if(null===this._logoWrapper&&this._renderSymbolLogo(),0!==this._lastDrawnLogos.length&&0!==e.length&&(0,D.default)(this._lastDrawnLogos,e))return;const u=await(h=e,Promise.all(h.map((e=>(0,F.getImage)(`symbol_logo_${e}`,e,K).then((e=>e.cloneNode())))))).catch((()=>[]));var h;switch(u.length){case 0:null===(t=this._pairContainer)||void 0===t||t.classList.add(A.hidden),null===(i=this._logoContainer)||void 0===i||i.classList.add(U.hiddenCircleLogoClass),this._updateSymbolLetter(),null===(s=this._symbolLetterContainer)||void 0===s||s.classList.remove(U.hiddenCircleLogoClass),this._lastDrawnLogos=e;break;case 1:j(this._logoContainer,u[0]),null===(l=this._pairContainer)||void 0===l||l.classList.add(A.hidden),null===(o=this._logoContainer)||void 0===o||o.classList.remove(U.hiddenCircleLogoClass),null===(a=this._symbolLetterContainer)||void 0===a||a.classList.add(U.hiddenCircleLogoClass),this._lastDrawnLogos=e;break;case 2:j(this._primaryLogo,u[0]),j(this._secondaryLogo,u[1]),null===(n=this._pairContainer)||void 0===n||n.classList.remove(A.hidden),null===(r=this._logoContainer)||void 0===r||r.classList.add(U.hiddenCircleLogoClass),
null===(d=this._symbolLetterContainer)||void 0===d||d.classList.add(U.hiddenCircleLogoClass),this._lastDrawnLogos=e}},this._model=e,this._parentElement=t,this._renderSymbolLogo(),this._logoUrls=e.symbolLogoUrls().spawn(),this._logoUrls.subscribe(this._updateSymbolLogo,{callWithLast:!0}),this._isLogoVisible=e.isSymbolLogoVisible().spawn(),this._isLogoVisible.subscribe(this._updateLogoVisibility,{callWithLast:!0})}destroy(){var e;null===(e=this._logoWrapper)||void 0===e||e.remove(),this._logoWrapper=null,this._pairContainer=null,this._primaryLogo=null,this._secondaryLogo=null,this._logoContainer=null,this._symbolLetterContainer=null,this._logoUrls.destroy(),this._isLogoVisible.destroy()}_renderSymbolLogo(){if(!this._logoWrapper){const e=this._logoWrapper=document.createElement("div");e.classList.add(A.logoWrapper);const t=this._pairContainer=e.appendChild(document.createElement("span"));t.classList.add(A.pairContainer,A.hidden);const i=t.appendChild(document.createElement("span"));i.className=(0,N.getBlockStyleClasses)("xxxsmall");(this._secondaryLogo=i.appendChild(document.createElement("span"))).className=z()((0,N.getLogoStyleClasses)("xxxsmall"),A.logo);(this._primaryLogo=i.appendChild(document.createElement("span"))).className=z()((0,N.getLogoStyleClasses)("xxxsmall"),A.logo);(this._logoContainer=e.appendChild(document.createElement("span"))).className=z()((0,R.getStyleClasses)("xxxsmall"),A.logo,U.hiddenCircleLogoClass);(this._symbolLetterContainer=e.appendChild(document.createElement("span"))).className=z()((0,R.getStyleClasses)("xxxsmall"),A.logo),this._updateSymbolLetter()}this._parentElement.insertBefore(this._logoWrapper,this._parentElement.firstChild)}_updateSymbolLetter(){var e;if(this._symbolLetterContainer){const t=(0,O.safeShortName)(null!==(e=this._model.symbol())&&void 0!==e?e:"")[0];p(this._symbolLetterContainer,t)}}}function j(e,t){e&&e.replaceChildren(t)}function K(e){e.crossOrigin="",e.decoding="async"}const $=_.isSafari?"click":"auxclick";class Z extends I{constructor(e,t,i){super(e,t,i),this._wheelClickHandler=null,this._symbolLogoRenderer=null,this._updateSymbolLogoRenderer=e=>{var t;c.enabled("show_symbol_logo_for_compare_studies")&&(null===(t=this._symbolLogoRenderer)||void 0===t||t.destroy(),this._symbolLogoRenderer=e?new G(e,(0,o.ensureNotNull)(this._titleContainers[0])):null)},this._canUpdateRowVisibility=!0,this._globalRowVisibility=this._model.globalVisibility().spawn(),this._globalRowVisibility.subscribe(this._updateShowLine.bind(this),{callWithLast:!0}),this._has5Buttons=this._model.isAbleShowSourceCode().spawn(),this._has5Buttons.subscribe(this._update5ButtonsStyles.bind(this),{callWithLast:!0}),this._updateStates(!this._disabled.value()),i.statusWidgetEl&&this._addStatusesWidget(i.statusWidgetEl,i.hideInvisibleHover,i.hideValues),this._selected.subscribe(this._updateTitleMaxWidth.bind(this)),i.withActions&&(this._wheelClickHandler=this._onWheelClicked.bind(this),null!==this._titlesWrapperEl&&this._titlesWrapperEl.addEventListener($,this._wheelClickHandler))
;const s=e.symbolLogoViewModel().value();this._updateSymbolLogoRenderer(s),e.symbolLogoViewModel().subscribe(this._updateSymbolLogoRenderer)}destroy(){var e;this._model.symbolLogoViewModel().unsubscribe(this._updateSymbolLogoRenderer),null===(e=this._symbolLogoRenderer)||void 0===e||e.destroy(),this._has5Buttons.destroy(),this._globalRowVisibility&&this._globalRowVisibility.destroy(),null!==this._wheelClickHandler&&null!==this._titlesWrapperEl&&this._titlesWrapperEl.removeEventListener($,this._wheelClickHandler),super.destroy()}accessibleButtons(){return this._globalRowVisibility.value()?super.accessibleButtons():[]}_updateActionsVisibilitiesByMode(){const e=this._model.actions(),t=[];let i=[];if(2===this._rowMode||1===this._rowMode)e[0].visible.value()&&t.push(this._actionElements[0]),e[this._actionElements.length-1].visible.value()&&t.push(this._actionElements[this._actionElements.length-1]),i=this._actionElements.slice(1,this._actionElements.length-1);else for(let i=0;i<this._actionElements.length-1;i++)e[i].visible.value()&&t.push(this._actionElements[i]);i.forEach((e=>e.classList.toggle(V.blockHidden,!0))),t.forEach((e=>e.classList.toggle(V.blockHidden,!1)))}_updateShowLine(){if(null===this._el||!this._canUpdateRowVisibility)return;const e=!this._globalRowVisibility.value();e?this._el.classList.toggle(V.blockHidden,e):super._updateShowLine()}_getButtonsCount(){switch(this._rowMode){case 4:return this._has5Buttons.value()?5:4;case 3:return 3;default:return 2}}_render(){super._render();const e=(0,o.ensureNotNull)(this._el);e.classList.add(A.item,A.study),e.dataset.name="legend-source-item",e.dataset.entityId=this._model.getSource().id(),e.setAttribute("role","toolbar")}_createTitlesSpawns(){const e=this._model.titles(),t=this._model.titleActions();this._titlesSpawns=[{...t.title,title:e.title.spawn(),class:A.mainTitle,titleId:"legend-source-title"},{...t.args,title:e.args.spawn(),class:A.descTitle,titleId:"legend-source-description"}]}_renderValues(){const e=this._initWrappersIfNotInitialized(),t=this._values.value();for(const i of t){const t=document.createElement("div");t.classList.add(A.valueItem),t.classList.toggle(V.blockHidden,!i.visible.value());const s=document.createElement("div");s.classList.add(A.valueValue),s.style.color=i.color.value()||"",s.appendChild(document.createTextNode(i.value.value()));const l=i.title.value();void 0!==l&&(s.classList.add("apply-common-tooltip"),s.title=l),t.appendChild(s),this._valuesElements.push({value:s}),e.appendChild(t)}}_updateValuesTitleHTMLElHandler(e,t=""){const i=(0,o.ensure)(this._valuesElements[e].value);i.classList.toggle("apply-common-tooltip",0!==t.length),i.title=t}_update5ButtonsStyles(e){null!==this._el&&(this._el.classList.toggle(A.has5Buttons,e),this._updateAllButtonsWidth())}_onWheelClicked(e){1===e.button&&this._model.onRemoveSource()}}class Q extends I{constructor(e,t,i){super(e,t,i),this._symbolLogoRenderer=null,this._clientHeight=null,this._updateLinkedState=void 0,this._flagged=this._model.flagged().spawn(),
this._flagged.subscribe(this._updateFlaggedState.bind(this)),this._updateStates(),i.statusWidgetEl&&this._addStatusesWidget(i.statusWidgetEl,i.hideInvisibleHover,i.hideValues),this._selected.subscribe(this._updateTitleMaxWidth.bind(this));const s=e.symbolLogoViewModel().value();s&&(this._symbolLogoRenderer=new G(s,(0,o.ensureNotNull)(this._titleContainers[0])))}destroy(){var e,t;super.destroy(),null===(e=this._flagged)||void 0===e||e.destroy(),null===(t=this._symbolLogoRenderer)||void 0===t||t.destroy()}getHeight(){return null===this._el?null:(null===this._clientHeight&&(this._clientHeight=this._el.clientHeight,0===this._clientHeight&&(this._clientHeight=null)),this._clientHeight)}_updateActionsVisibilitiesByMode(){const e=[];let t=[];const i=this._model.actions();if(1===this._rowMode)i[this._actionElements.length-1].visible.value()&&e.push(this._actionElements[this._actionElements.length-1]),t=this._actionElements.slice(0,this._actionElements.length-1);else for(let t=0;t<this._actionElements.length;t++)i[t].visible.value()&&e.push(this._actionElements[t]);t.forEach((e=>e.classList.toggle(V.blockHidden,!0))),e.forEach((e=>e.classList.toggle(V.blockHidden,!1)))}_updateTitlesVisibilitiesByMode(){}_isTitleHiddenByMode(e){const t=e.titleId;return"legend-source-interval"!==t&&"legend-source-provider"!==t&&"legend-source-exchange"!==t?super._isTitleHiddenByMode(e):"legend-source-interval"===t&&this._isMultipleLayout.value()?1===this._titlesMode:3!==this._titlesMode}_hideTitleAndItsContainerIfRequired(e){const t=this._getTitleIndexByDataName(e);if(-1===t)return;const i=this._titlesSpawns[t],s=this._titleContainers[t],l=this._titleElements[t],o=0===i.title.value().length||this._isTitleHiddenByMode(i);null==s||s.classList.toggle(V.blockHidden,o),null==l||l.classList.toggle(V.blockHidden,o)}_getButtonsCount(){return 1===this._rowMode?1:3}_render(){super._render();const e=(0,o.ensureNotNull)(this._el);e.classList.add(A.item,A.series),e.classList.toggle(A.onlyOneButtonCanBeStick,this._model.isOneButtonCanBeStick()),e.dataset.name="legend-series-item",e.setAttribute("role","toolbar")}_updateStates(){super._updateStates(),this._updateFlaggedState()}_renderValues(){const e=this._initWrappersIfNotInitialized(),t=this._values.value();for(const i of t){const t=document.createElement("div");t.classList.add(A.valueItem),t.classList.toggle(V.blockHidden,!i.visible.value()),t.classList.toggle(A.unimportant,i.unimportant.value());const s=document.createElement("div"),l=i.title.value()||"";s.classList.add(A.valueTitle),s.classList.toggle(V.blockHidden,0===l.length),s.appendChild(document.createTextNode(l)),t.appendChild(s);const o=document.createElement("div");o.classList.add(A.valueValue),o.style.color=i.color.value()||"",o.appendChild(document.createTextNode(i.value.value())),t.appendChild(o),this._valuesElements.push({title:s,value:o}),e.appendChild(t)}}_createTitlesSpawns(){const e=this._model.titles(),t=this._model.titleActions();this._titlesSpawns=[{...t.title,title:e.title.spawn(),class:A.mainTitle,titleId:"legend-source-title"},{
...t.description,title:e.description.spawn(),class:A.descTitle,titleId:"legend-source-description"},{...t.interval,title:e.interval.spawn(),class:A.intervalTitle,titleId:"legend-source-interval"},{...t.provider,title:e.provider.spawn(),class:A.providerTitle,titleId:"legend-source-provider"},{...t.exchange,title:e.exchange.spawn(),class:A.exchangeTitle,titleId:"legend-source-exchange"},{...t.chartStyle,title:e.chartStyle.spawn(),class:A.styleTitle,titleId:"legend-source-style"},{...t.priceSource,title:e.priceSource.spawn(),class:A.priceSourceTitle,titleId:"legend-source-price-source"}]}_updateValuesTitleHTMLElHandler(e,t=""){const i=(0,o.ensure)(this._valuesElements[e].title);p(i,t),i.classList.toggle(V.blockHidden,0===t.length),this._updateShowValues()}_isWidthButtonsMode(){var e;return null!==this._el&&(void 0!==this._flagged&&Boolean(this._flagged.value())||(null===(e=this._linked)||void 0===e?void 0:e.value())||super._isWidthButtonsMode())}_updateFlaggedState(){if(void 0===this._flagged)return;(0,o.ensureNotNull)(this._el).classList.toggle(A.flagged,Boolean(this._flagged.value())),this._updateTitleMaxWidth()}_getTitleIndexByDataName(e){return this._titlesSpawns.findIndex((t=>t.titleId===e))}}var X=i(11542),q=i(51768);function J(e){(0,q.trackEvent)("GUI","Legend action",e)}var Y,ee,te=i(47036),ie=i(62920),se=i(65300),le=i(36885);!function(e){e[e.Default=0]="Default",e[e.Micro=1]="Micro"}(Y||(Y={}));class oe{constructor(e,t,i){this._el=null,this._counterEl=null,this._arrowIconEL=null,this._objectTreeEl=null,this._mode=0,this._accessibleButtons=[],this._parentEl=e,this._themedColor=t.spawn(),this._themedColor.subscribe(this._updateThemedColor.bind(this)),this._showCollapserWithOneIndicator=i.showCollapserWithOneIndicator.spawn(),this._showCollapserWithOneIndicator.subscribe(this._updateSourceCount.bind(this)),this._sourceCount=i.visibleDataSourceCount.spawn(),this._sourceCount.subscribe(this._updateSourceCount.bind(this)),this._isStateOpen=i.isDataSourcesCollapsed.spawn(),this._isStateOpen.subscribe(this._updateState.bind(this)),this._showObjectsTree=i.showObjectsTree.spawn(),this._showObjectsTree.subscribe(this._updateObjectTreeVisibility.bind(this)),this._render(),this._updateState(),this._updateThemedColor(this._themedColor.value()),this._updateObjectTreeVisibility(this._showObjectsTree.value()),this._toggleStateHandler=e=>{var t;null!==this._el&&(e.cancelable&&e.preventDefault(),e instanceof KeyboardEvent||null===(t=this._el)||void 0===t||t.blur(),1!==this._mode?i.onCollapseDataSources():i.onShowObjectsTreeDialog())},null!==this._el&&(this._el.addEventListener("touchend",this._toggleStateHandler),this._el.addEventListener("click",this._toggleStateHandler),this._el.addEventListener("contextmenu",(e=>{e.preventDefault(),e.stopPropagation()}))),this._updateSourceCount()}destroy(){this._sourceCount.destroy(),this._isStateOpen.destroy(),this._showCollapserWithOneIndicator.destroy(),null!==this._objectTreeEl&&(this._objectTreeEl=null),this._arrowIconEL=null,this._counterEl=null,
null!==this._el&&(this._el.removeEventListener("touchend",this._toggleStateHandler),this._el.removeEventListener("click",this._toggleStateHandler),this._el.innerHTML="",this._el=null)}accessibleButtons(){return null===this._el||this._el.classList.contains(A.onlyOneSourceShown)&&1!==this._mode?[]:this._accessibleButtons}setMode(e){this._mode=e?1:0,this._updateTooltip()}_render(){this._el=document.createElement("button"),this._el.className=`${A.toggler} apply-common-tooltip`;const e=this._el;e.type="button",e.classList.add(A.accessible),e.tabIndex=-1;const t=t=>{if(null!==e)switch(t.type){case"roving-tabindex:main-element":e.tabIndex=0;break;case"roving-tabindex:secondary-element":e.tabIndex=-1}};e.addEventListener("roving-tabindex:main-element",t),e.addEventListener("roving-tabindex:secondary-element",t),e.addEventListener("keydown",(e=>{const t=(0,u.hashFromEvent)(e);13!==t&&32!==t||(e.preventDefault(),this._toggleStateHandler(e))})),this._accessibleButtons.push(e),this._arrowIconEL=document.createElement("div"),this._arrowIconEL.classList.add(A.iconArrow),this._arrowIconEL.innerHTML=v.trackingModeIsAvailable?ie:te,this._el.appendChild(this._arrowIconEL),this._objectTreeEl=document.createElement("div"),this._objectTreeEl.classList.add(A.objectTree),this._objectTreeEl.innerHTML=v.trackingModeIsAvailable?le:se,this._el.appendChild(this._objectTreeEl),this._counterEl=document.createElement("div"),this._counterEl.classList.add(A.counter),this._counterEl.appendChild(document.createTextNode(String(this._sourceCount.value()))),this._el.appendChild(this._counterEl),this._parentEl.appendChild(this._el)}_updateThemedColor(e){if(null!==this._el)if(e.length>0){const[t,i,s]=(0,d.parseRgb)(e);this._el.style.backgroundColor=(0,d.rgbaToString)([t,i,s,(0,d.normalizeAlphaComponent)(.8)])}else this._el.style.removeProperty("background-color")}_updateSourceCount(){const e=this._sourceCount.value();p((0,o.ensureNotNull)(this._counterEl),String(e));const t=(0,o.ensureNotNull)(this._el),i=e<1;t.classList.toggle(V.blockHidden,i);const s=1===e;t.classList.toggle(A.onlyOneSourceShown,s&&!this._showCollapserWithOneIndicator.value())}_updateState(){const e=!this._isStateOpen.value();this._parentEl.classList.toggle(A.closed,e),this._updateTooltip(),J((e?"Hide":"Show")+" not main sources")}_tooltip(){if(1===this._mode)return X.t(null,void 0,i(85786));const e=this._sourceCount.value(),t=X.t(null,{plural:"Hide indicators legend",count:e},i(46960)),s=X.t(null,{plural:"Show indicators legend",count:e},i(36553));return this._isStateOpen.value()?t:s}_updateTooltip(){if(null!==this._el){const e=this._tooltip();this._el.setAttribute("title",e),this._el.setAttribute("aria-label",e)}}_updateObjectTreeVisibility(e){(0,o.ensureNotNull)(this._el).classList.toggle(A.objectsTreeCanBeShown,e)}}!function(e){e[e.Medium=222]="Medium",e[e.Small=205]="Small",e[e.Tiny=133]="Tiny",e[e.HideUnimportantValues=272]="HideUnimportantValues",e[e.SeriesDirectionColumn=542]="SeriesDirectionColumn",e[e.HideExchangeIntevalProvider=442]="HideExchangeIntevalProvider",
e[e.HideIntervalMultiChart=192]="HideIntervalMultiChart"}(ee||(ee={}));const ae=c.enabled("object_tree_legend_mode"),ne=[27,9,37,39,38,40];class re{constructor(e,t){this._mode=4,this._renderToggler=null,this._mainDataSourceRenderer=null,this._dataSourceRenderers=[],this._parentEl=document.createElement("div"),this._mainDataSourceEl=null,this._dataSourcesEl=null,this._dataSourcesAdditionalWrapperEl=null,this._collapsedDataSourcesWrapperEl=null,this._collapsedDataSourcesEl=null,this._outsideEventForCollapsedTooltip=null,this._onKeyboardNavigationActivationBound=this._onKeyboardNavigationActivation.bind(this),this._onIsDataSourcesCollapsedBound=this._onIsDataSourcesCollapsed.bind(this),this._focusEventAbortController=null,this._options=e,this._togglerOptions=t,this._isStudiesLegendHidden=e.isStudiesLegendHidden.spawn(),this._isStudiesLegendHidden.subscribe(this._updateLegendVisibility.bind(this)),this._isAllLegendHidden=e.isAllLegendHidden.spawn(),this._isAllLegendHidden.subscribe(this._updateLegendVisibility.bind(this)),this._updateLegendVisibility(),this._hideAllExceptFirstLine=e.hideAllExceptFirstLine.spawn(),this._hideAllExceptFirstLine.subscribe(this._updateAllHiddenExeptFirstLine.bind(this)),this._themedColor=e.themedColor.spawn(),this._themedColor.subscribe(this._setCustomBg.bind(this)),this._showBackground=e.showBackground.spawn(),this._showBackground.subscribe(this._setCustomBg.bind(this)),this._backgroundTransparency=e.backgroundTransparency.spawn(),this._backgroundTransparency.subscribe(this._setCustomBg.bind(this)),this._collapsedDataSourcesCountSpawn=e.collapsedDataSourcesCount.spawn(),this._collapsedDataSourcesCountSpawn.subscribe(this._updateCollapsedSourcesCount.bind(this)),this._showCollapsedDataSourcesTooltipHandler=this._showCollapsedDataSourcesTooltip.bind(this),this._parentEl.classList.add(A.legend),this._parentEl.classList.toggle(A.noWrap,!v.trackingModeIsAvailable),this._parentEl.classList.toggle(A.noActions,!this._options.withActions),this._parentEl.classList.toggle(A.touchMode,v.trackingModeIsAvailable),this._parentEl.classList.toggle(A.wrappable,!this._hideAllExceptFirstLine.value()),this._parentEl.dataset.name="legend",this._parentEl.style.setProperty("--legend-source-item-button-width",`${S}px`);const i=t=>{t.preventDefault(),e.showLegendWidgetContextMenu(t)};this._mouseEventHandler=new w.MouseEventHandler(this._parentEl,{contextMenuEvent:i,touchContextMenuEvent:i}),window.addEventListener("keyboard-navigation-activation",this._onKeyboardNavigationActivationBound),this._parentEl.addEventListener("keydown",this._handleKeyDown.bind(this)),this._options.onLayoutChanged.subscribe(this,this._onLayoutChanged)}destroy(){var e;null===(e=this._focusEventAbortController)||void 0===e||e.abort();const t=document.activeElement;let i=-1;if(re._wasKeyboardNavigationActivated&&t instanceof HTMLButtonElement){null!==this._getRowRendererByChild(t)&&(i=(0,m.queryTabbableElements)(document.body).indexOf(t))}if(this._isStudiesLegendHidden.destroy(),this._isAllLegendHidden.destroy(),
this._hideAllExceptFirstLine.destroy(),this._themedColor.destroy(),this._showBackground.destroy(),this._backgroundTransparency.destroy(),this._collapsedDataSourcesCountSpawn.destroy(),v.trackingModeIsAvailable&&null!==this._collapsedDataSourcesWrapperEl&&this._collapsedDataSourcesWrapperEl.removeEventListener("touchend",this._showCollapsedDataSourcesTooltipHandler),this._outsideEventForCollapsedTooltip&&this._outsideEventForCollapsedTooltip(),null!==this._dataSourcesAdditionalWrapperEl&&(this._dataSourcesAdditionalWrapperEl.innerHTML="",this._dataSourcesAdditionalWrapperEl=null),null!==this._dataSourcesEl&&(this._dataSourcesEl.innerHTML="",this._dataSourcesEl=null),this._togglerOptions.isDataSourcesCollapsed.unsubscribe(this._onIsDataSourcesCollapsedBound),null!==this._renderToggler&&(this._renderToggler.destroy(),this._renderToggler=null),null!==this._mainDataSourceRenderer&&(this._mainDataSourceRenderer.destroy(),this._mainDataSourceRenderer=null),0!==this._dataSourceRenderers.length){for(const e of this._dataSourceRenderers)e.destroy();this._dataSourceRenderers=[]}if(this._mouseEventHandler.destroy(),this._parentEl.innerHTML="",delete this._parentEl,window.removeEventListener("keyboard-navigation-activation",this._onKeyboardNavigationActivationBound),-1!==i){let e;window.dispatchEvent(new CustomEvent("keyboard-navigation-activation",{bubbles:!0}));const t=(0,m.queryTabbableElements)(document.body);e=i===t.length?t[0]:t[i],e&&e.focus()}}addCustomWidget(e,t){if(0===t.block){this._renderMainDataSourceEl();const i=(0,o.ensureNotNull)(this._mainDataSourceEl);1===t.position&&e.renderTo(i,i.firstChild),0===t.position&&e.renderTo(i)}if(1===t.block){this._renderDataSourcesEl();const i=(0,o.ensureNotNull)(this._dataSourcesAdditionalWrapperEl);1===t.position&&e.renderTo(i,i.firstChild),0===t.position&&e.renderTo(i)}}firstTitle(){return this._parentEl.firstElementChild}getElement(){return this._parentEl}updateMode(e){const t=ae&&e<133?1:e<205?2:e<222?3:4,i=e<192?1:e<442?2:3;this._mode=t,null!==this._mainDataSourceRenderer&&this._mainDataSourceRenderer.updateMode(t,i);for(const e of this._dataSourceRenderers)e.updateMode(t,i);this._parentEl.classList.toggle(A.medium,3===t),this._parentEl.classList.toggle(A.minimized,2===t),this._parentEl.classList.toggle(A.micro,1===t),null!==this._renderToggler&&this._renderToggler.setMode(1===t);const s=!this._hideAllExceptFirstLine.value()&&(v.trackingModeIsAvailable||e<542);this._parentEl.classList.toggle(A.directionColumn,s),this._parentEl.classList.toggle(A.hideUniportantValueItems,!_.CheckMobile.any()&&e<=272)}getMainSourceHeight(){return null===this._mainDataSourceRenderer?0:this._mainDataSourceRenderer.getHeight()}getDataSourceHeight(){return 0===this._dataSourceRenderers.length?0:this._dataSourceRenderers[0].getHeight()}_renderMainDataSourceEl(){null===this._mainDataSourceEl&&(this._mainDataSourceEl=document.createElement("div"),this._mainDataSourceEl.classList.add(A.legendMainSourceWrapper),this._parentEl.insertBefore(this._mainDataSourceEl,this._dataSourcesEl))}
_renderDataSourcesEl(){null===this._dataSourcesEl&&(this._dataSourcesEl=document.createElement("div"),this._dataSourcesEl.classList.add(A.sourcesWrapper),this._renderToggle(this._dataSourcesEl),this._dataSourcesAdditionalWrapperEl=document.createElement("div"),this._dataSourcesAdditionalWrapperEl.classList.add(A.sources),this._dataSourcesEl.appendChild(this._dataSourcesAdditionalWrapperEl),this._renderCollapsedCounter(this._dataSourcesAdditionalWrapperEl),this._parentEl.appendChild(this._dataSourcesEl))}_renderToggle(e){this._options.showToggleButton&&(this._renderToggler=new oe(e,this._options.themedColor,this._togglerOptions))}_onIsDataSourcesCollapsed(e){var t;if(e){const e=this._getAllAccessibleButtons();if(0===e.filter((e=>e.tabIndex>=0)).length){const t=this._getRowRendererByChild(e[0]);t&&this._makeRowElementTheMainOne(e[0],t)}return}if(0!==((null===(t=this._mainDataSourceRenderer)||void 0===t?void 0:t.accessibleButtons())||[]).filter((e=>e.tabIndex>=0)).length)return;const i=[];this._dataSourceRenderers.forEach((e=>i.push(...e.accessibleButtons())));const s=i.filter((e=>e.tabIndex>=0));if(0!==s.length){this._focusEventAbortController&&this._focusEventAbortController.abort(),s.forEach((e=>(0,g.becomeSecondaryElement)(e)));let e=null,t=null;if(this._mainDataSourceRenderer){const i=this._mainDataSourceRenderer.accessibleButtons();i.length>0&&(e=i[0],t=this._mainDataSourceRenderer)}null===e&&this._renderToggler&&(e=this._renderToggler.accessibleButtons()[0],t=this._renderToggler),e&&t&&this._makeRowElementTheMainOne(e,t)}}_renderCollapsedCounter(e){this._collapsedDataSourcesWrapperEl=document.createElement("div"),this._collapsedDataSourcesWrapperEl.className=`${A.item} ${A.last}`,this._collapsedDataSourcesEl=document.createElement("span"),this._collapsedDataSourcesEl.className=`${A.text} apply-common-tooltip`,this._collapsedDataSourcesWrapperEl.append(this._collapsedDataSourcesEl),e.append(this._collapsedDataSourcesWrapperEl),v.trackingModeIsAvailable&&this._collapsedDataSourcesWrapperEl.addEventListener("touchend",this._showCollapsedDataSourcesTooltipHandler),this._updateCollapsedSourcesCount(this._collapsedDataSourcesCountSpawn.value())}_showCollapsedDataSourcesTooltip(){(0,b.showOnElement)(this._collapsedDataSourcesEl,{text:this._options.collapsedDataSourcesTitle.value()}),this._addOutsideEventForHideTooltip()}_addOutsideEventForHideTooltip(){null!==this._outsideEventForCollapsedTooltip&&this._outsideEventForCollapsedTooltip(),this._outsideEventForCollapsedTooltip=(0,h.addOutsideEventListener)(new CustomEvent("timestamp").timeStamp,this._collapsedDataSourcesWrapperEl,(()=>{null!==this._outsideEventForCollapsedTooltip&&this._outsideEventForCollapsedTooltip(),(0,b.hide)()}),window.document,{touchEnd:!0})}_updateCollapsedSourcesCount(e){if(null===this._collapsedDataSourcesWrapperEl||null===this._collapsedDataSourcesEl)return;const t=0===e;this._collapsedDataSourcesWrapperEl.classList.toggle(V.blockHidden,t),t||(p(this._collapsedDataSourcesEl,`+${e}`),
this._collapsedDataSourcesEl.setAttribute("title",this._options.collapsedDataSourcesTitle.value()))}_updateLegendVisibility(){let e;const t=re._wasKeyboardNavigationActivated&&(this._isAllLegendHidden.value()||this._isStudiesLegendHidden.value());if(t){e=this._getAllAccessibleButtons().filter((e=>e.tabIndex>=0))[0]}if(this._parentEl.classList.toggle(V.blockHidden,this._isAllLegendHidden.value()),null!==this._dataSourcesEl&&this._dataSourcesEl.classList.toggle(V.blockHidden,this._isStudiesLegendHidden.value()),t){const t=this._getAllAccessibleButtons();if(e&&t.includes(e))return;if(e&&(0,g.becomeSecondaryElement)(e),0!==t.length){const e=this._getRowRendererByChild(t[0]);e&&this._makeRowElementTheMainOne(t[0],e)}}}_updateAllHiddenExeptFirstLine(){this._parentEl.classList.toggle(A.wrappable,!this._hideAllExceptFirstLine.value())}_setCustomBg(){const e=this._showBackground.value(),t=this._themedColor.value(),i=this._backgroundTransparency.value();let s="";if(e){const[e,l,o]=(0,d.parseRgb)(t);s=(0,d.rgbaToString)([e,l,o,(0,d.normalizeAlphaComponent)(1-i/100)])}this._parentEl.style.color=s}_onLayoutChanged(e){var t;const{newMainSource:i,removedDataSources:s,addedDataSources:l,movedDataSources:o}=e,a=document.activeElement;let n=-1,r=-1;const d=null===this._mainDataSourceRenderer&&0===this._dataSourceRenderers.length;if(re._wasKeyboardNavigationActivated&&a instanceof HTMLButtonElement){const e=this._getRowRenderers(),t=e.find((e=>e.accessibleButtons().includes(a)));if(void 0!==t){const o=t===this._mainDataSourceRenderer&&null===i,d=t instanceof Z&&s.includes(this._dataSourceRenderers.indexOf(t));if(o||d){n=e.indexOf(t);const o=(0,m.queryTabbableElements)(document.body).indexOf(a);r=0!==l.length||(void 0!==i||null!==this._mainDataSourceRenderer)&&null!==i||s.length!==this._dataSourceRenderers.length&&0!==this._dataSourceRenderers.length?o+1:o}}}void 0!==i&&(null!==i?this._addMainDataSource(i.model,i.statusWidget):(null===(t=this._mainDataSourceRenderer)||void 0===t||t.destroy(),this._mainDataSourceRenderer=null));const u=[];if(o.forEach((e=>{u.push({renderer:this._dataSourceRenderers[e.oldIndex],newIndex:e.newIndex})})),u.sort(((e,t)=>e.newIndex-t.newIndex)),s.forEach((e=>{this._dataSourceRenderers.splice(e,1)[0].destroy()})),0!==l.length&&(this._renderDataSourcesEl(),l.forEach((e=>{this._addDataSource(e.model,e.statusWidget,e.index)}))),0!==u.length&&(this._renderDataSourcesEl(),u.forEach((e=>{this._moveDataSource(e.renderer,e.newIndex)}))),re._wasKeyboardNavigationActivated&&d&&(null!==this._mainDataSourceRenderer||this._dataSourceRenderers.length)&&window.dispatchEvent(new CustomEvent("keyboard-navigation-activation",{bubbles:!0})),-1!==n){let e,t;const i=this._getRowRenderers();for(;n<i.length;){const s=i[n],l=s.accessibleButtons();if(l.length>0){e=l[0],t=s;break}n++}if(e&&t)this._changeFocusElement(e,t);else if(-1!==r){window.dispatchEvent(new CustomEvent("keyboard-navigation-activation",{bubbles:!0}));const t=(0,m.queryTabbableElements)(document.body);e=r===t.length?t[0]:t[r],e&&(a&&(0,g.becomeSecondaryElement)(a),
e.focus())}}}_addMainDataSource(e,t){this._renderMainDataSourceEl(),this._mainDataSourceRenderer=new Q(e,(0,o.ensureNotNull)(this._mainDataSourceEl),{withActions:this._options.withActions,customTextColor:this._options.customTextColor,statusWidgetEl:t.getElement(),hideInvisibleHover:t.visibility(),hideValues:t.errorWidgetIsShown,isMultipleLayout:this._options.isMultipleLayout}),this._updateLegendVisibility()}_addDataSource(e,t,i){const s=(0,o.ensureNotNull)(this._dataSourcesAdditionalWrapperEl),l=new Z(e,s,{withActions:this._options.withActions,customTextColor:this._options.customTextColor,statusWidgetEl:t.getElement(),hideInvisibleHover:t.visibility(),hideValues:t.errorWidgetIsShown,isMultipleLayout:this._options.isMultipleLayout}),a=(0,o.ensureNotNull)(l.getElement());if(null!==a){let e=this._dataSourceRenderers[i];void 0!==e?s.insertBefore(a,e.getElement()):(e=this._dataSourceRenderers[i-1],void 0!==e&&s.insertBefore(a,(0,o.ensureNotNull)(e.getElement()).nextSibling))}this._dataSourceRenderers.splice(i,0,l),this._updateLegendVisibility()}_moveDataSource(e,t){const i=this._dataSourceRenderers.indexOf(e);if(-1===i||t===i)return;const s=(0,o.ensureNotNull)(this._dataSourcesAdditionalWrapperEl),l=(0,o.ensureNotNull)(e.getElement());let a=this._dataSourceRenderers[t];void 0!==a?s.insertBefore(l,a.getElement()):(a=this._dataSourceRenderers[t-1],void 0!==a&&s.insertBefore(l,(0,o.ensureNotNull)(a.getElement()).nextSibling)),this._dataSourceRenderers.splice(i,1),this._dataSourceRenderers.splice(t,0,e)}_onKeyboardNavigationActivation(){re._wasKeyboardNavigationActivated=!0,this._togglerOptions.isDataSourcesCollapsed.unsubscribe(this._onIsDataSourcesCollapsedBound),this._togglerOptions.isDataSourcesCollapsed.subscribe(this._onIsDataSourcesCollapsedBound);const e=this._getAllAccessibleButtons(),t=e.filter((e=>e.tabIndex>=0));if(0===t.length){const[t]=e.filter((e=>e.hasAttribute("tabIndex")));if(void 0===t)return;const i=this._getRowRendererByChild(t);if(null===i)return;this._makeRowElementTheMainOne(t,i)}if(t.length>1){const[,...e]=t;for(const t of e)(0,g.becomeSecondaryElement)(t)}}_handleKeyDown(e){if(e.defaultPrevented)return;const t=(0,u.hashFromEvent)(e);if(!ne.includes(t))return;const i=document.activeElement;if(!(i instanceof HTMLButtonElement))return;const s=this._getRowRendererByChild(i);if(null===s)return;if(27===t||9===t)return void(27===t?i.blur():window.dispatchEvent(new CustomEvent("keyboard-navigation-activation",{bubbles:!0})));const l=s.accessibleButtons(),o=l.indexOf(i);if(0===l.length||-1===o)return;const a=t=>{e.preventDefault();const i=this._getRowRenderers(),l=i.indexOf(s);let o,a,n="blockNext"===t?l+1:l-1;for(;n>=0&&n<i.length;){const e=i[n],s=e.accessibleButtons();if(0!==s.length){o=e,a=s;break}n="blockNext"===t?n+1:n-1}o&&a&&this._changeFocusElement(a[0],o)},n=(0,m.mapKeyCodeToDirection)(t);switch(n){case"inlinePrev":e.preventDefault(),this._changeFocusElement(0!==o?l[o-1]:l[l.length-1],s);break;case"inlineNext":e.preventDefault(),this._changeFocusElement(o!==l.length-1?l[o+1]:l[0],s);break
;case"blockPrev":case"blockNext":a(n)}}_subscribeElementToFocusEvents(e,t){this._focusEventAbortController=new AbortController,e.addEventListener("focusin",(()=>{this._focusEventAbortController=new AbortController,e.addEventListener("focusout",(i=>{const s=i.relatedTarget;null!==s&&s instanceof HTMLButtonElement&&t.accessibleButtons().includes(s)||(this._subscribeElementToFocusEvents(e,t),t instanceof oe||t.hideActions())}),{once:!0,signal:this._focusEventAbortController.signal}),this._options.onLegendRowFocused(),t instanceof oe||t.showActions()}),{once:!0,signal:this._focusEventAbortController.signal})}_makeRowElementTheMainOne(e,t){this._subscribeElementToFocusEvents(e,t),(0,g.becomeMainElement)(e)}_changeFocusElement(e,t){document.activeElement&&(0,g.becomeSecondaryElement)(document.activeElement),this._makeRowElementTheMainOne(e,t),e.focus()}_getRowRenderers(){let e=[];return this._mainDataSourceRenderer&&e.push(this._mainDataSourceRenderer),this._dataSourcesEl&&!this._dataSourcesEl.classList.contains(V.blockHidden)&&(this._togglerOptions.isDataSourcesCollapsed.value()&&1!==this._mode&&(e=e.concat(this._dataSourceRenderers)),this._renderToggler&&0!==this._dataSourceRenderers.length&&e.push(this._renderToggler)),e}_getAllAccessibleButtons(){const e=[];return this._getRowRenderers().forEach((t=>{e.push(...t.accessibleButtons())})),e}_getRowRendererByChild(e){var t;return null!==(t=this._getRowRenderers().find((t=>t.accessibleButtons().includes(e))))&&void 0!==t?t:null}}re._wasKeyboardNavigationActivated=!1;var de,ue=i(86129),he=i(99531),ce=i(42752),_e=i(86235),pe=i(58473),ge=i(76460),me=i(19063),ve=i(45126),be=i(63273),we=i(88960),Se=i(19466);function ye(e){return void 0!==e?me.resetTransparency(e):e}function Me(e){return e.hasOwnProperty("touches")}!function(e){e.Small="small",e.Large="large"}(de||(de={}));const Ce=new ve.TranslatedString("show {title}",X.t(null,void 0,i(51382))),fe=new ve.TranslatedString("hide {title}",X.t(null,void 0,i(13017))),Ee=X.t(null,void 0,i(98334)),Ve=X.t(null,void 0,i(27298));class Le{constructor(e,t,i,s,l){this._values=new n.WatchedValue([]),this._actions=[],this._loading=new n.WatchedValue(!1),this._symbolLogoViewModel=new n.WatchedValue(null),this._destroyed=!1,this._moreActionCM=null,this._updateLoadingStatus=()=>{this._loading.setValue(this._source.isLoading())},this._model=e,this._source=t,this._options=i,this._callbacks=s,this._contextMenuOptions=l,this._disabled=new n.WatchedValue(this._getDisabledState()),this._disabledOnInterval=new n.WatchedValue(this._getDisabledOnIntervalState()),this._selected=new n.WatchedValue(!1),this._isTitleHidden=new n.WatchedValue(this._getTitleHiddenValue()),this._isValuesHidden=new n.WatchedValue(this._getValuesHiddenValue()),this._isRowHidden=new n.WatchedValue(this._getRowHiddenValue()),this._isEditable=new n.WatchedValue(this._getIsEditable()),(0,we.combine)((()=>({})),this._isTitleHidden.weakReference(),this._isValuesHidden.weakReference(),this._disabled.weakReference()).subscribe(this._updateRowVisibilities.bind(this)),
this._values.subscribe((()=>{this._isValuesHidden.setValue(this._getValuesHiddenValue())}))}destroy(){var e;null===(e=this._symbolLogoViewModel.value())||void 0===e||e.destroy(),this._destroyed=!0}titles(){return this._titles}titleActions(){return this._titleActions}values(){return this._values.readonly()}actions(){return this._actions}disabled(){return this._disabled.readonly()}disabledOnInterval(){return this._disabledOnInterval.readonly()}selected(){return this._selected.readonly()}loading(){return this._loading.readonly()}isTitleHidden(){return this._isTitleHidden.readonly()}isValuesHidden(){return this._isValuesHidden.readonly()}isRowHidden(){return this._isRowHidden.readonly()}isEditable(){return this._isEditable.readonly()}symbolLogoViewModel(){return this._symbolLogoViewModel}update(){var e,t;this._updateTitles(),this._updateValues(),this._updateStates(),null===(t=null===(e=this.symbolLogoViewModel())||void 0===e?void 0:e.value())||void 0===t||t.update()}onToggleDisabled(){const e=this._source.properties().childs().visible,t=!e.value();this._model.setProperty(e,t,(t?Ce:fe).format({title:new ve.TranslatedString(this._source.name(),this._source.title(Se.TitleDisplayTarget.StatusLine))})),J((t?"Show":"Hide")+" source")}onShowSettings(e){this._source.userEditEnabled()&&(this.setSourceSelected(),this._callbacks.showChartPropertiesForSource(this._source,e),J("Settings for source"))}onShowMoreActions(e){return this._options.readOnlyMode?Promise.resolve(null):(this._callbacks.updateActions(),J("Show source context menu"),this._callbacks.showContextMenuForSources([this._source],this._calcNewPosition(e),{...this._contextMenuOptions,isKeyboardEvent:!Me(e)&&(0,ge.isKeyboardClick)(e)},{origin:"LegendPropertiesContextMenu"}))}setSourceSelected(){this._model.selectionMacro((e=>{e.clearSelection(),e.addSourceToSelection(this._source)}))}_moreActionHandler(e){e.preventDefault(),null!==this._moreActionCM&&this._moreActionCM.isShown()?this._moreActionCM=null:(this.setSourceSelected(),this.onShowMoreActions(e).then((e=>{this._moreActionCM=e})))}_updateStates(){this._disabled.setValue(this._getDisabledState()),this._disabledOnInterval.setValue(this._getDisabledOnIntervalState()),this._selected.setValue(this._model.selection().isSelected(this._source)),this._isEditable.setValue(this._getIsEditable()),this._updateLoadingStatus()}_hasValues(){return this._values.value().length>0}_getEyeTitle(){return this._disabled.value()?Ee:Ve}_getIsEditable(){return this._source.userEditEnabled()}_getDisabledState(){return!this._source.properties().visible.value()}_updateRowVisibilities(){this._isRowHidden.setValue(this._getRowHiddenValue())}_getRowHiddenValue(){return this._options.readOnlyMode&&this._disabled.value()||this._isTitleHidden.value()&&(this._isValuesHidden.value()||this._disabled.value())}_calcNewPosition(e){let t={};if(Me(e)&&e.touches.length>0)t={clientX:e.touches[0].clientX,clientY:e.touches[0].clientY};else if(null!==e.target){const i=e.target.getBoundingClientRect();t={clientX:(0,be.isRtl)()?i.right:i.left,
clientY:i.top+i.height+3}}else{const i=e;t={clientX:i.clientX,clientY:i.clientY}}return t}}var xe=i(618),We=i(36279),Ae=i(12184),ke=i(5201);const Te=c.enabled("show_symbol_logos")&&c.enabled("show_symbol_logo_in_legend"),He=new ke.CircularCacheBuffer(100);class Be{constructor(e,t){this._symbolLogoUrls=new n.WatchedValue([]),this._quoteDataForLogos=null,this._logoDataUpdated=()=>{var e;const t=this.symbol(),i=this._logoData.value();i&&t&&He.set(t,i);const s=He.get(t);this._quoteDataForLogos=(0,r.merge)(null!=s?s:{},null!==(e=this._logoData.value())&&void 0!==e?e:{}),this._symbolLogoUrls.setValue((0,xe.removeUsdFromCryptoPairLogos)((0,xe.resolveLogoUrls)(this._quoteDataForLogos,We.LogoSize.Medium)))},this._logoModel=e,this._isLogoVisible=(0,Ae.createWVFromGetterAndSubscription)((()=>t.showLogo.value()),t.showLogo),this._logoData=this._logoModel.logoData().spawn(),this._logoData.subscribe(this._logoDataUpdated,{callWithLast:!0})}destroy(){this._isLogoVisible.destroy(),this._logoModel.destroy()}isSymbolLogoVisible(){return this._isLogoVisible}symbolLogoUrls(){return this._symbolLogoUrls}symbol(){return this._logoModel.symbol()}update(){this._logoModel.update()}}function Ie(e,t){return Te?new Be(e,t):null}var De=i(26023);const Pe=(0,i(12168).getLogger)("Chart.LegendWidget");function ze(e){const t={};for(const i of["logoid","currency-logoid","base-currency-logoid"])if(i in e){const s=i;t[s]=e[s]}return t}class Ne{constructor(e){this._logoData=new n.WatchedValue(null),this._prevSymbol="",this._source=e,this._source.symbolResolved().subscribe(this,this._onSourceSymbolResolved),this.update()}destroy(){this._source.symbolResolved().unsubscribeAll(this)}symbol(){return this._source.symbol()}logoData(){return this._logoData}update(){const e=this.symbol();this._prevSymbol!==e&&(this._prevSymbol=e,this._onSourceSymbolResolved())}_onQuoteSymbolDataUpdated(e,t){const i=t.values;!this._source.symbolSameAsCurrent(e.values.pro_name)||void 0===i.logoid&&void 0===i["currency-logoid"]&&void 0===i["base-currency-logoid"]||this._logoData.setValue(ze(i))}async _onSourceSymbolChange(){const e=this.symbol();let t=null;try{this._logoData.setValue(null),t=await this._source.quotesProvider().quotesSnapshot(e)}catch(e){Pe.logError(`Quote snapshot error: ${e}`)}finally{this.symbol()===e&&(null===t?this._logoData.setValue(null,!0):this._logoData.setValue(ze(t)))}}_onSourceSymbolResolved(){{const e=this._source.symbolInfo(),t=(null==e?void 0:e.logo_urls)||[];if(1===t.length)return void this._logoData.setValue({logoid:t[0]});if(2===t.length)return void this._logoData.setValue({"base-currency-logoid":t[0],"currency-logoid":t[1]});this._logoData.setValue(null,!0)}}}var Re=i(54336),Oe=i(41674),Fe=i(87258),Ue=i(45534);const Ge=X.t(null,void 0,i(37117)),je=X.t(null,void 0,i(44454)),Ke=X.t(null,void 0,i(13865)),$e=X.t(null,void 0,i(63245)),Ze=X.t(null,void 0,i(29151)),Qe=X.t(null,void 0,i(2569)),Xe=c.enabled("legend_inplace_edit"),qe=c.enabled("show_hide_button_in_legend"),Je=c.enabled("hide_resolution_in_legend");class Ye extends Le{constructor(e,t,i,s,l){
super(e,t,i,s,l),this._titles={title:new n.WatchedValue(""),description:new n.WatchedValue(""),interval:new n.WatchedValue(""),provider:new n.WatchedValue(""),exchange:new n.WatchedValue(""),chartStyle:new n.WatchedValue(""),priceSource:new n.WatchedValue("")},this._titleActions={title:void 0,description:void 0,interval:void 0,provider:void 0,exchange:void 0,chartStyle:void 0,priceSource:void 0},this._symbolMarker=null,this._symbolMarkerIcon=null,this._flagged=new n.WatchedValue(null),this._symbolAction=null,this._symbolForMarker=null,this._isOneButtonCanBeStick=!1,this._layoutChartSyncLegendRenderer=null,this._isChartLinked=new n.WatchedValue(!1).readonly().spawn(),this._initializeTitleActions(),this._createActions(),this._updateSymbolMarker();const o=this._model.model().properties().childs().paneProperties.childs().legendProperties.childs();this._symbolLogoViewModel.setValue(Ie(new Ne(t),o)),o.showSeriesTitle.subscribe(this,(()=>{this._isTitleHidden.setValue(this._getTitleHiddenValue())})),this._isPriceSourceHidden=(0,he.createWVFromGetterAndSubscription)((()=>!o.showPriceSource.value()),o.showPriceSource),this._valuesVisibleProperty=(0,he.combineProperty)(((e,t,i,s)=>e||t||i||s),o.showSeriesOHLC.weakReference(),o.showBarChange.weakReference(),o.showVolume.weakReference(),o.showLastDayChange.weakReference()),this._valuesVisibleProperty.subscribe(null,(()=>{this._isValuesHidden.setValue(this._getValuesHiddenValue())})),this.update(),this._source.statusWV().subscribe(this._updateLoadingStatus)}destroy(){super.destroy(),this._model.model().properties().childs().paneProperties.childs().legendProperties.childs().showSeriesTitle.unsubscribeAll(this),this._source.statusWV().unsubscribe(this._updateLoadingStatus),this._valuesVisibleProperty.destroy(),this._isPriceSourceHidden.destroy()}flagged(){return this._flagged}linked(){return this._isChartLinked}onShowSettings(){this._source.userEditEnabled()&&this._callbacks.showGeneralChartProperties(De.TabNames.symbol)}isOneButtonCanBeStick(){return this._isOneButtonCanBeStick}_updateTitles(){const e=(0,o.ensureNotNull)(this._source.statusView()).getSplitTitle();this._titles.title.setValue((0,ue.clean)(e.title,!0)),this._titles.description.setValue((0,ue.clean)(e.description,!0)),Je||this._titles.interval.setValue((0,ue.clean)(e.interval,!0)),this._titles.provider.setValue((0,ue.clean)(e.provider,!0)),this._titles.exchange.setValue((0,ue.clean)(e.exchange,!0)),this._titles.chartStyle.setValue((0,ue.clean)(e.chartStyle,!0)),this._titles.priceSource.setValue((0,ue.clean)(this._isPriceSourceHidden.value()?"":e.priceSource,!0))}_updateValues(){const e=this._source.legendView(),t=this._values.value(),i=e.marketTitle(),s=e.marketTitle().length>0,l=e.items();if(0===t.length||t.length!==l.length+1){const e={value:new n.WatchedValue(""),color:new n.WatchedValue(""),visible:new n.WatchedValue(s),title:new n.WatchedValue(i),unimportant:new n.WatchedValue(!1)},t=l.map((e=>({value:new n.WatchedValue(e.value()),color:new n.WatchedValue(ye(e.color())),visible:new n.WatchedValue(e.visible()),
title:new n.WatchedValue(e.title()),unimportant:new n.WatchedValue(e.unimportant())})));this._values.setValue([e].concat(t))}else{t[0].title.setValue(i),t[0].visible.setValue(s);for(let e=0;e<l.length;e++){const i=l[e];t[e+1].value.setValue(i.value()),t[e+1].color.setValue(ye(i.color())),t[e+1].visible.setValue(i.visible()),t[e+1].title.setValue(i.title())}}}_updateStates(){super._updateStates(),this._updateSymbolMarker()}_getDisabledOnIntervalState(){return!1}_getTitleHiddenValue(){return!this._model.model().properties().childs().paneProperties.childs().legendProperties.childs().showSeriesTitle.value()}_getValuesHiddenValue(){return!this._hasValues()||!this._valuesVisibleProperty.value()}_initializeTitleActions(){if(!Xe)return;const e=()=>{const e=this._model.mainSeries(),t=e.symbol(),i=e.properties().childs().shortName.value();(0,pe.showDialog)({defaultValue:e.isSpread()?t:i||t||""})};c.enabled("disable_legend_inplace_symbol_change")||(this._titleActions.title={onClick:e,tooltip:$e},this._titleActions.description={onClick:e,tooltip:Ze}),this._titleActions.interval={onClick:()=>{(0,_e.showChangeIntervalDialogAsync)({initVal:this._model.mainSeries().interval(),selectOnInit:!0})},tooltip:Qe}}_createActions(){if(qe){const e=(0,ce.convertPropertyToWatchedValue)((0,he.combineProperty)((e=>!e),this._source.properties().childs().visible.weakReference())),t={iconMap:new Map([["large",Oe],["small",Re]]),action:(0,M.wrapHandlerWithPreventEvent)(this.onToggleDisabled.bind(this)),visible:e,className:A.eye,title:new n.WatchedValue(this._getEyeTitle()),dataset:{name:"legend-show-hide-action"}};this._actions.push(t),this._disabled.subscribe((()=>{var e;null===(e=t.title)||void 0===e||e.setValue(this._getEyeTitle())}))}this._actions.push({iconMap:new Map([["large",Ue],["small",Fe]]),action:this._moreActionHandler.bind(this),visible:new n.WatchedValue(!0),title:new n.WatchedValue(Ge),dataset:{name:"legend-more-action"}})}_getMarkerTitle(){return null!==this._symbolMarker?this._symbolMarker.isMarked()?Ke:je:""}_symbolActionHandler(e){(0,M.preventDefault)(e),null!==this._symbolMarker&&(this._updateSymbolMarker(),e instanceof KeyboardEvent&&this._symbolMarker.toggle(e),J("Change flag state"))}_updateSymbolMarker(){this._isOneButtonCanBeStick=!0}}var et=i(72708),tt=i(3615);var it=i(28388),st=i(60074),lt=i(50340),ot=i(94664),at=i(31955),nt=i(83637),rt=i(34882),dt=i(30556),ut=i(91104);(0,at.getLogger)("Chart.LegendWidget");const ht=X.t(null,void 0,i(32514)),ct=X.t(null,void 0,i(87142)),_t=X.t(null,void 0,i(67410)),pt=X.t(null,void 0,i(37117)),gt=(X.t(null,void 0,i(57335)),X.t(null,void 0,i(43206)),X.t(null,void 0,i(74759))),mt=X.t(null,void 0,i(63245)),vt=(c.enabled("study_buttons_in_legend"),c.enabled("show_hide_button_in_legend")),bt=c.enabled("property_pages"),wt=c.enabled("format_button_in_legend"),St=c.enabled("delete_button_in_legend"),yt=c.enabled("legend_inplace_edit");class Mt extends Le{constructor(e,t,i,s,l){super(e,t,i,s,l),this._titles={title:new n.WatchedValue(""),args:new n.WatchedValue("")},this._titleActions={
title:void 0,args:void 0},this._error=new n.WatchedValue(!1),this._isAbleShowSourceCode=new n.WatchedValue(!1),this._isAbleShowSourceCodeInner=new n.WatchedValue(!1).spawn(),this._pineAction=null,this._globalVisibility=new n.WatchedValue(!0),this._updateSymbolLogoModel(),this._initializeTitleActions(),this._createActions();const o=this._model.model().properties().childs().paneProperties.childs().legendProperties.childs(),a=[o.showSeriesTitle,o.showStudyTitles];for(const e of a)e.subscribe(this,(()=>{this._isTitleHidden.setValue(this._getTitleHiddenValue())}));const r=[o.showSeriesOHLC,o.showBarChange,o.showStudyValues,o.showLastDayChange];for(const e of r)e.subscribe(this,(()=>{this._isValuesHidden.setValue(this._getValuesHiddenValue())}));this.update()}destroy(){super.destroy();const e=this._model.model().properties().childs().paneProperties.childs().legendProperties.childs();e.showSeriesTitle.unsubscribeAll(this),e.showStudyTitles.unsubscribeAll(this),e.showSeriesOHLC.unsubscribeAll(this),e.showBarChange.unsubscribeAll(this),e.showStudyValues.unsubscribeAll(this),e.showLastDayChange.unsubscribeAll(this),this._isAbleShowSourceCodeInner.destroy()}error(){return this._error.readonly()}isAbleShowSourceCode(){return this._isAbleShowSourceCode}onRemoveSource(){var e;this._source.isUserDeletable()&&(this._source.hasChildren()?(e=this._model.removeSource.bind(this._model,this._source,!1),(0,tt.showConfirm)({title:X.t(null,void 0,i(81605)),text:X.t(null,void 0,i(77174)),onConfirm:({dialogClose:t})=>{e(),t()}})):this._model.removeSource(this._source,!1),J("Remove sources"))}async onShowSourceCode(){}setGlobalVisibility(e){this._globalVisibility.setValue(e)}globalVisibility(){return this._globalVisibility.readonly()}getFullTitle(){return[this._titles.title,this._titles.args].map((e=>e.value())).join(" ")}getSource(){return this._source}_updateTitles(){const e=(0,o.ensureNotNull)(this._source.statusView()).getSplitTitle();this._titles.title.setValue((0,ue.clean)(e[0],!0));const t=Array.isArray(e[1])?e[1].join(" "):e[1]||"";this._titles.args.setValue((0,ue.clean)(t,!0))}_updateValues(){const e=this._source.legendView();if(null===e)return;if(0===e.items().length)return;const t=this._values.value();if(0===t.length){const t=e.items().map((e=>({value:new n.WatchedValue(e.value()),color:new n.WatchedValue(ye(e.color())),visible:new n.WatchedValue(e.visible()),unimportant:new n.WatchedValue(e.unimportant()),title:new n.WatchedValue(e.title())})));this._values.setValue(t)}else{const i=e.items();for(let e=0;e<i.length;e++){const s=t[e],l=i[e];s.value.setValue(l.value()),s.color.setValue(ye(l.color())),s.visible.setValue(l.visible()),s.title.setValue(l.title())}}}_updateStates(){super._updateStates(),void 0!==this._error&&this._error.setValue(Boolean(this._source.isFailed()))}_getTitleHiddenValue(){const e=this._model.model().properties().childs().paneProperties.childs().legendProperties.childs();return this._isSymbolLikeStudy()?!e.showSeriesTitle.value():!e.showStudyTitles.value()}_getDisabledOnIntervalState(){return!(!(0,
et.isStudy)(this._source)&&!(0,et.isStudyStub)(this._source))&&!this._source.isActualInterval()}_getValuesHiddenValue(){if(!this._hasValues())return!0;const e=this._model.model().properties().childs().paneProperties.childs().legendProperties.childs();return this._isSymbolLikeStudy()?!e.showSeriesOHLC.value()&&!e.showBarChange.value()&&!e.showLastDayChange.value():!e.showStudyValues.value()}_initializeTitleActions(){const e=this._source;if(!yt||!(0,st.isSymbolSourceWithQuotesProvider)(e))return;this._titleActions.title={onClick:()=>{let t=null;const i=t=(0,lt.loadNewSymbolSearch)().then((s=>{var l;if(i!==t)return;const o=e.symbol(),a=(0,O.safeShortName)(o),n="spread"===(null===(l=e.symbolInfo())||void 0===l?void 0:l.type)?o:a||o||"",r=(0,ot.getSymbolSearchCompleteOverrideFunction)();s.showSymbolSearchItemsDialog({onSearchComplete:t=>{r(t[0].symbol,t[0].result).then((t=>{this._model.setSymbol(e,t.symbol)}))},dialogTitle:mt,defaultValue:n,showSpreadActions:c.enabled("show_spread_operators")&&c.enabled("studies_symbol_search_spread_operators")})}))},tooltip:mt}}_isSymbolLikeStudy(){return(0,et.isCompareOrOverlayStudy)(this._source)}async _updateAbleShowSourceCode(){}_updateVisibilityPineAction(e){null!==this._pineAction&&(this._pineAction.visible.setValue(e),this._isAbleShowSourceCode.setValue(e))}_createActions(){if(!this._options.readOnlyMode){if(this._pineAction={iconMap:new Map([["large",rt],["small",rt]]),action:(0,M.wrapHandlerWithPreventEvent)(this.onShowSourceCode.bind(this)),disableAccessibility:!0,visible:new n.WatchedValue(!1),title:new n.WatchedValue(ct),dataset:{name:"legend-pine-action"}},vt){const e={iconMap:new Map([["large",Oe],["small",Re]]),action:(0,M.wrapHandlerWithPreventEvent)(this.onToggleDisabled.bind(this)),visible:new n.WatchedValue(!this._getDisabledOnIntervalState()),className:A.eye,title:new n.WatchedValue(this._getEyeTitle()),dataset:{name:"legend-show-hide-action"}};this._actions.push(e),this._disabled.subscribe((()=>{var t;null===(t=e.title)||void 0===t||t.setValue(this._getEyeTitle())}));const t={iconMap:new Map([["large",Oe],["small",Re]]),action:(0,M.wrapHandlerWithPreventEvent)(this.onShowSettings.bind(this,De.TabNames.visibility)),visible:new n.WatchedValue(this._getDisabledOnIntervalState()),className:A.intervalEye,title:new n.WatchedValue(gt),dataset:{name:"legend-interval-show-hide-action"}};this._actions.push(t),this._disabledOnInterval.subscribe((i=>{t.visible.setValue(i),e.visible.setValue(!i)}))}if(bt&&wt&&(!(0,et.isStudy)(this._source)||new it.MetaInfoHelper(this._source.metaInfo()).hasUserEditableOptions())){const e={iconMap:new Map([["large",nt],["small",nt]]),action:(0,M.wrapHandlerWithPreventEvent)(this.onShowSettings.bind(this)),visible:new n.WatchedValue(this._getIsEditable()),title:new n.WatchedValue(ht),dataset:{name:"legend-settings-action"}};this._actions.push(e),this._isEditable.subscribe((t=>{e.visible.setValue(t)}))}if(St){const e={iconMap:new Map([["large",ut],["small",dt]]),action:(0,M.wrapHandlerWithPreventEvent)(this.onRemoveSource.bind(this)),
visible:new n.WatchedValue(this._getIsEditable()),title:new n.WatchedValue(_t),dataset:{name:"legend-delete-action"}};this._actions.push(e),this._isEditable.subscribe((t=>{e.visible.setValue(t)}))}this._actions.push({iconMap:new Map([["large",Ue],["small",Fe]]),action:this._moreActionHandler.bind(this),visible:this._isEditable.spawn(),title:new n.WatchedValue(pt),dataset:{name:"legend-more-action"}})}}_updateSymbolLogoModel(){var e;if(null===(e=this._symbolLogoViewModel.value())||void 0===e||e.destroy(),(0,st.isSymbolSourceWithQuotesProvider)(this._source)){const e=this._model.model().properties().childs().paneProperties.childs().legendProperties.childs();this._symbolLogoViewModel.setValue(Ie(new Ne(this._source),e))}else this._symbolLogoViewModel.setValue(null)}}var Ct=i(97702),ft=i(40443),Et=i(68805),Vt=i(30141),Lt=i(34585),xt=i(23486),Wt=i(81199);function At(e,t,i){e.setProperty(t,!t.value(),i)}const kt=new ve.TranslatedString("change symbol description visibility",X.t(null,void 0,i(88167))),Tt=new ve.TranslatedString("change open market status visibility",X.t(null,void 0,i(96227))),Ht=new ve.TranslatedString("change chart values visibility",X.t(null,void 0,i(79637))),Bt=new ve.TranslatedString("change last day change visibility",X.t(null,void 0,i(66307))),It=new ve.TranslatedString("change bar change visibility",X.t(null,void 0,i(27426))),Dt=new ve.TranslatedString("change indicator titles visibility",X.t(null,void 0,i(63050))),Pt=new ve.TranslatedString("change indicator arguments visibility",X.t(null,void 0,i(78310))),zt=new ve.TranslatedString("change indicator values visibility",X.t(null,void 0,i(49583))),Nt=new ve.TranslatedString("change volume values visibility",X.t(null,void 0,i(96201))),Rt=new ve.TranslatedString("change symbol field visibility",X.t(null,void 0,i(12050))),Ot=X.t(null,void 0,i(14771)),Ft=X.t(null,void 0,i(25765)),Ut=X.t(null,void 0,i(45639)),Gt=X.t(null,void 0,i(72423)),jt=X.t(null,void 0,i(10842)),Kt=X.t(null,void 0,i(37644)),$t=X.t(null,void 0,i(7511)),Zt=X.t(null,void 0,i(44036)),Qt=X.t(null,void 0,i(51353)),Xt=X.t(null,void 0,i(23079)),qt=(0,Lt.appendEllipsis)(X.t(null,void 0,i(32514))),Jt=c.enabled("symbol_info_price_source"),Yt=(e,t)=>e?e.dataset[t]?e.dataset[t]:e.parentElement?Yt(e.parentElement,t):null:null;var ei=i(60697),ti=i(75725);class ii{constructor(e){this._source=e,this._fullSessionScheduleViewModel=new ei.FullSessionScheduleViewModel(e)}destroy(){this._fullSessionScheduleViewModel.destroy()}renderer(e,t,i){var s;const l=null===(s=this._source.marketStatusModel())||void 0===s?void 0:s.status().value();return"expired"===l||"delisted"===l?null:(0,E.createElement)(ti.FullSessionScheduleRenderer,{key:e,className:t,showAllDays:i,sessionDays:this._fullSessionScheduleViewModel.sessionsDays,now:this._fullSessionScheduleViewModel.currentTimeValue(),timezone:this._fullSessionScheduleViewModel.timezone()})}updateSource(e){this._source=e,this._fullSessionScheduleViewModel.destroy(),this._fullSessionScheduleViewModel=new ei.FullSessionScheduleViewModel(e)}}
var si=i(19625),li=i(49406),oi=i(82236),ai=i(10492);class ni{constructor(e){this.isBlinkingMode=new n.WatchedValue(!1),this._status=new n.WatchedValue(null),this._fullTooltip=new n.WatchedValue(null),this._iconClassNames=new n.WatchedValue(null),this._visible=new n.WatchedValue(!1),this._tooltip=new n.WatchedValue(null),this._icon=new n.WatchedValue(null),this._className=new n.WatchedValue(null),this._customColor=new n.WatchedValue(null),this._infoMaps=e,this._size=e.size||"small",this._status.subscribe(this._updateByStatus.bind(this),{callWithLast:!0})}turnOffBlinkingMode(){}status(){return this._status}tooltip(){return this._tooltip}icon(){return this._icon}className(){return this._className}visible(){return this._visible}size(){return this._size}fullInfo(){return this._fullTooltip}customColor(){return this._customColor}_getTooltip(e){var t,i;return null!==(i=null===(t=this._infoMaps.tooltipMap)||void 0===t?void 0:t.get(e))&&void 0!==i?i:null}_getIcon(e){let t;const i=this._infoMaps.iconMap.get(e);return void 0!==i&&(t=i.get(this._size)),t||null}_getClassName(e){return this._infoMaps.classNameMap.get(e)||null}_getFullTooltipIconClassNames(e){const t=this._getClassName(e);return t?[ai.statusItem,t]:[]}_getTitle(e){var t,i;return null!==(i=null===(t=this._infoMaps.titleMap)||void 0===t?void 0:t.get(e))&&void 0!==i?i:null}_getTitleColor(e){var t,i;return null!==(i=null===(t=this._infoMaps.titleColorMap)||void 0===t?void 0:t.get(e))&&void 0!==i?i:null}_getAction(e){var t,i;return null!==(i=null===(t=this._infoMaps.actionMap)||void 0===t?void 0:t.get(e))&&void 0!==i?i:null}_getHTML(e){var t,i,s;return null!==(s=null===(i=null===(t=this._infoMaps.htmlMap)||void 0===t?void 0:t.get(e))||void 0===i?void 0:i.map(li.htmlEscape))&&void 0!==s?s:[]}async _updateFullTooltip(){const e=this._status.value();null!==e?this._fullTooltip.setValue([{icon:this._getIcon(e),iconClassName:this._getFullTooltipIconClassNames(e),title:this._getTitle(e),titleColor:this._getTitleColor(e),html:this._getHTML(e),size:this._size,action:this._getAction(e)}]):this._fullTooltip.setValue(null)}_updateByStatus(e){if(null===e||this._shouldBeHiddenByStatus(e))return this._icon.setValue(null),this._tooltip.setValue(null),void this._visible.setValue(!1);this._icon.setValue(this._getIcon(e)),this._className.setValue(this._getClassName(e)),this._tooltip.setValue(this._getTooltip(e)),this._visible.setValue(!0),this._updateFullTooltip()}_shouldBeHiddenByStatus(e){return!1}}var ri=i(12646),di=i(31233),ui=i(55593),hi=i(69410),ci=i(52828),_i=i(91665),pi=i(39379),gi=i(72844),mi=i(23683),vi=i(38373),bi=i(79304),wi=i(21672),Si=i(92315);const yi=new Map([["small",ri],["medium",di],["large",di]]),Mi=new Map([["small",ui],["medium",hi],["large",hi]]),Ci=new Map([["small",ci],["medium",_i],["large",_i]]),fi=(new Map([["small",pi],["medium",pi],["large",pi]]),new Map([["small",gi],["medium",mi],["large",mi]]),new Map([["small",vi],["medium",bi],["large",bi]]),new Map([["small",""],["medium",""],["large",""]]),new Map([["small",wi],["medium",Si],["large",Si]]),
new Map([["small",""],["medium",""],["large",""]])),Ei=si.colorsPalette["color-delay-mode"],Vi=si.colorsPalette["color-eod-mode"],Li=si.colorsPalette["color-notaccurate-mode"],xi=(si.colorsPalette["color-primary-symbol"],si.colorsPalette["color-halal"],si.colorsPalette["color-continuous"],si.colorsPalette["color-data-problem"],si.colorsPalette["color-data-problem"],si.colorsPalette["color-grapes-purple-400"]),Wi=X.t(null,void 0,i(43348)),Ai=X.t(null,void 0,i(5805)),ki=X.t(null,void 0,i(91006)),Ti=X.t(null,void 0,i(58796)),Hi=X.t(null,void 0,i(55154)),Bi=X.t(null,void 0,i(44138)),Ii=(X.t(null,void 0,i(94972)),X.t(null,void 0,i(95246)),X.t(null,void 0,i(50035)),X.t(null,void 0,i(45321)),X.t(null,void 0,i(75119)),X.t(null,void 0,i(67607)),(0,li.htmlEscape)(X.t(null,void 0,i(89142)))),Di=(0,li.htmlEscape)(X.t(null,void 0,i(51211))),Pi=(0,li.htmlEscape)(X.t(null,void 0,i(7281))),zi=(0,li.htmlEscape)(X.t(null,void 0,i(20987))),Ni=(0,li.htmlEscape)(X.t(null,void 0,i(32925))),Ri=(0,li.htmlEscape)(X.t(null,void 0,i(38368))),Oi=(0,li.htmlEscape)(X.t(null,void 0,i(33039))),Fi=(0,li.htmlEscape)(X.t(null,void 0,i(85996))),Ui=(0,li.htmlEscape)(X.t(null,void 0,i(95400))),Gi=X.t(null,void 0,i(31539)),ji=(X.t(null,{context:'Part of: "Real-time data for {symbolName} is provided by {exchange} exchange."'},i(48473)),X.t(null,{context:'Part of: "Real-time data for {symbolName} is provided by {exchange} exchange."'},i(84455)),X.t(null,void 0,i(24669))),Ki=X.t(null,void 0,i(52668)),$i=X.t(null,void 0,i(44492)),Zi=(X.t(null,void 0,i(40225)),X.t(null,void 0,i(78162)),X.t(null,void 0,i(54316)),X.t(null,void 0,i(28632)),X.t(null,void 0,i(25608)),X.t(null,void 0,i(33161)),X.t(null,void 0,i(54480)),X.t(null,void 0,i(44138)),X.t(null,void 0,i(90589)));var Qi=i(22118),Xi=i(56840);const qi="tv.alreadyBlinkedStatuses",Ji=[];function Yi(){return Xi.getJSON(qi,Ji)}const es=new n.WatchedValue(Yi());function ts(e){const t=Xi.getJSON(qi,Ji);t.includes(e)||(t.push(e),Xi.setJSON(qi,t),es.setValue(Yi()))}Xi.onSync.subscribe(null,(()=>es.setValue(Yi())));const is=es;var ss=i(14712);const ls=(0,
at.getLogger)("Chart.LegendWidget"),os=["TFEXDelayForGuest","MOEXDelayForGuest","CHIXAuDelayForGuest","MILDelayForGuest","NGMDelayForGuest","DEForGuest","ICESGDelayForGuest","TAIFEXDelayForGuest","TURQUOISEDelayForGuest","ADXDelayForGuest","TRADEGATEDelayForGuest","LUXSEDelayForGuest","NSENGDelayForGuest","FINRADelayForGuest"],as=new Map([["DelayToRealtime",yi],["DelayNoRealtime",yi],["TFEXDelayForGuest",yi],["MOEXDelayForGuest",yi],["CHIXAuDelayForGuest",yi],["MILDelayForGuest",yi],["NGMDelayForGuest",yi],["ICESGDelayForGuest",yi],["TAIFEXDelayForGuest",yi],["TURQUOISEDelayForGuest",yi],["ADXDelayForGuest",yi],["TRADEGATEDelayForGuest",yi],["LUXSEDelayForGuest",yi],["NSENGDelayForGuest",yi],["FINRADelayForGuest",yi],["DEForGuest",yi],["EOD",Mi],["TickByTick",Ci],["BATSToRealtime",Ci],["DelayWithoutMarketAgreement",yi],["TVCalculatedPair",fi]]),ns=new Map([["DelayToRealtime",ai.delay],["DelayNoRealtime",ai.delay],["TFEXDelayForGuest",ai.delay],["MOEXDelayForGuest",ai.delay],["CHIXAuDelayForGuest",ai.delay],["MILDelayForGuest",ai.delay],["NGMDelayForGuest",ai.delay],["ICESGDelayForGuest",ai.delay],["TAIFEXDelayForGuest",ai.delay],["TURQUOISEDelayForGuest",ai.delay],["ADXDelayForGuest",ai.delay],["TRADEGATEDelayForGuest",ai.delay],["LUXSEDelayForGuest",ai.delay],["NSENGDelayForGuest",ai.delay],["FINRADelayForGuest",ai.delay],["DEForGuest",ai.delay],["EOD",ai.eod],["TickByTick",ai.notAccurate],["BATSToRealtime",ai.notAccurate],["DelayWithoutMarketAgreement",ai.delay],["TVCalculatedPair",ai.tvCalculatedPair]]),rs=new Map([["DelayToRealtime",Ei],["DelayNoRealtime",Ei],["TFEXDelayForGuest",Ei],["MOEXDelayForGuest",Ei],["CHIXAuDelayForGuest",Ei],["MILDelayForGuest",Ei],["NGMDelayForGuest",Ei],["ICESGDelayForGuest",Ei],["TAIFEXDelayForGuest",Ei],["TURQUOISEDelayForGuest",Ei],["ADXDelayForGuest",Ei],["TRADEGATEDelayForGuest",Ei],["LUXSEDelayForGuest",Ei],["NSENGDelayForGuest",Ei],["FINRADelayForGuest",Ei],["DEForGuest",Ei],["EOD",Vi],["TickByTick",Li],["BATSToRealtime",Li],["DelayWithoutMarketAgreement",Ei],["TVCalculatedPair",xi]]),ds=(X.t(null,void 0,i(28214)),X.t(null,void 0,i(27741)));(0,li.htmlEscape)(X.t(null,void 0,i(5447)));class us extends ni{constructor(e,t,i){var s;super({iconMap:as,classNameMap:ns,titleColorMap:rs,size:t}),this._dataUpdatedInfo=new n.WatchedValue(null).spawn(),this._onMarketStatusChanged=()=>{this._updateByStatus(this.status().value())},this._options=i,this._model=e,this._dataModeBlinkingStatuses=is.spawn(),this._dataModeBlinkingStatuses.subscribe(this._updateBlinkingMode.bind(this)),null===(s=this._options.marketStatus)||void 0===s||s.subscribe(this._onMarketStatusChanged),this.turnOffBlinkingMode=this._turnOffBlinking.bind(this),this.setModel(e)}destroy(){var e,t;this._dataUpdatedInfo.destroy(),this._dataModeBlinkingStatuses.destroy(),null===(e=this._options.marketStatus)||void 0===e||e.unsubscribe(this._onMarketStatusChanged),null===(t=this._options.marketStatus)||void 0===t||t.release()}setModel(e){if(this._dataUpdatedInfo.destroy(),null===e)return this._model=e,
void(this._dataUpdatedInfo=new n.WatchedValue(null).spawn());this._dataUpdatedInfo=e.status().spawn(),this._dataUpdatedInfo.subscribe(this._updateStatus.bind(this),{callWithLast:!0})}_shouldBeHiddenByStatus(e){var t,i;const s=null===(t=this._options.marketStatus)||void 0===t?void 0:t.value();return"expired"===s||"delisted"===s||(!("BATSToRealtime"!==e||!(null===(i=this._model)||void 0===i?void 0:i.isSpread()))||super._shouldBeHiddenByStatus(e))}_getTooltip(){const e=this._getShortTexts();return null===e?null:Object.values(e).join(" · ")}async _updateFullTooltip(){var e;const t=this._dataUpdatedInfo.value(),i=this._status.value();if(null===t||null===i)return void this._fullTooltip.setValue(null);const s=this._getShortTexts(),[l,o]=await Promise.all([this._getHtmls(),this._getActions()]);if(t!==this._dataUpdatedInfo.value())return;const a=[];for(const n of t){const t=n.mode;"BATSToRealtime"===t&&(null===(e=this._model)||void 0===e?void 0:e.isSpread())||a.push({icon:this._getIcon(t),iconClassName:this._getFullTooltipIconClassNames(i),title:s&&s[t],titleColor:this._getTitleColor(t),html:l&&l[t],size:this._size,action:o&&o[t]})}this._fullTooltip.setValue(a)}_updateStatus(e){var t;const i=null!==e?e[0]:null;this._status.setValue(null!==(t=null==i?void 0:i.mode)&&void 0!==t?t:null,!0),this._updateBlinkingMode()}async _getHtmls(){var e,t;const s=this._dataUpdatedInfo.value();if(null===s||null===this._model)return Promise.resolve(null);const l={},o=this._model.symbolName();let a=null,n=null;try{a=await this._model.description(),n=this._model.exchange()}catch(e){ls.logError(`Can't get exchange description, reason: ${(0,ss.errorToString)(e)}`)}for(const r of s){const s=r.mode;if(l[s]=[],["DelayToRealtime","DelayNoRealtime","DelayWithoutMarketAgreement",...os].includes(s)&&(l[s].push(Ii.format({symbolName:o,time:this._model.time().toString()})),this._options.subscriptionFullInfo&&null!==a&&"DelayToRealtime"===s&&l[s].push(Pi.format({description:`<b>${a}</b>`})),"DelayNoRealtime"===s&&l[s].push(zi),"DelayWithoutMarketAgreement"===s&&l[s].push(Ui.format({listedExchange:this._model.listedExchange()})),this._options.subscriptionFullInfo&&os.includes(s)&&l[s].push(Di.format({listedExchange:this._model.listedExchange()}))),"EOD"===s&&(l[s]=[Ni]),"TickByTick"===s){const o=void 0===r.updatePeriod?Ri:(0,li.htmlEscape)(X.t(null,{count:r.updatePeriod,replace:{amount:(null!==(e=r.updatePeriod)&&void 0!==e?e:1).toString()},plural:"Data on our Basic plan is updated once every {amount} seconds, even if there are more updates on the market."},i(83978))),a=void 0===r.updatePeriod?Oi:(0,li.htmlEscape)(X.t(null,{count:r.updatePeriod,replace:{amount:(null!==(t=r.updatePeriod)&&void 0!==t?t:1).toString()},plural:"Data is updated once every {amount} seconds, even if there are more updates on the market."},i(51931)));l[s].push(this._options.subscriptionFullInfo?o:a),this._options.subscriptionFullInfo&&l[s].push(Fi)}if("TVCalculatedPair"===s&&(l[s]=[Zi]),null!==n&&"BATSToRealtime"===s){let e=this._model.listedExchange();0,
Qi.CRUCIAL_REALTIME_BATS.includes(this._model.listedExchange())?l[s].push($i.format({exchange:e,originalExchange:Ti})):l[s].push(Gi.format({symbolName:o,exchange:n}),""!==e?(0,li.htmlEscape)(Ki).format({exchange:e}):(0,li.htmlEscape)(ji))}}return Object.keys(l).length>0?l:null}async _getActions(){if(null===this._dataUpdatedInfo.value()||null===this._model)return null;const e={};return Object.keys(e).length>0?e:null}_showSupportDialogForUpdateMode(e){}_getShortTexts(){var e,t;const s=this._dataUpdatedInfo.value();if(null===s||null===this._model)return null;const l={};for(const o of s){const s=o.mode;if(["DelayToRealtime","DelayNoRealtime",...os,"DelayWithoutMarketAgreement"].includes(s)&&(l[s]=Wi),"EOD"===s&&(l[s]=Ai),"TickByTick"===s){const t=void 0===o.updatePeriod?ki:(0,li.htmlEscape)(X.t(null,{plural:"One update every {amount} seconds",count:o.updatePeriod,replace:{amount:(null!==(e=o.updatePeriod)&&void 0!==e?e:1).toString()}},i(36050)));l[s]=t}if("BATSToRealtime"===s){let e=null!==(t=this._model.firstReplacedByBatsExchange())&&void 0!==t?t:"";0,l[s]=""!==e?Hi.format({exchange:e,originalExchange:Ti}):Ti}"TVCalculatedPair"===s&&(l[s]=Bi)}return Object.keys(l).length>0?l:null}_updateBlinkingMode(){const e=this._dataUpdatedInfo.value();if(null===e)return;const t=this._dataModeBlinkingStatuses.value();for(const i of e)if(!t.includes(i.mode))return void this.isBlinkingMode.setValue(!0);this.isBlinkingMode.setValue(!1)}_turnOffBlinking(){const e=this._dataUpdatedInfo.value();if(null!==e)for(const t of e)ts(t.mode)}_goProDialogAction(e,t={}){return{text:ds,onClick:()=>{null!==this._model&&createGoProDialog({forceUpgradeBtn:!0,goOnMarkets:!0,customParams:t,upgradeMessage:X.t(null,void 0,i(70032)),feature:e}).then((()=>{trackEvent("Data Warning","Full description visible",e)}))}}}}const hs=X.t(null,void 0,i(86158)),cs=new Map([[!0,new Map([["small",vi],["medium",bi],["large",bi]])],[!1,new Map([["small",""],["medium",""],["large",""]])]]),_s=new Map([[!0,ai.dataProblemLow],[!1,null]]),ps=new Map([[!0,hs],[!1,null]]),gs=new Map([[!0,hs],[!1,null]]),ms=new Map([[!0,si.colorsPalette["color-data-problem"]],[!1,null]]);class vs extends ni{constructor(e,t,i){super({iconMap:cs,classNameMap:_s,tooltipMap:ps,titleMap:gs,titleColorMap:ms,size:t}),this._dataSourceErrorStatus=new n.WatchedValue(null).spawn(),this._lastError=null,this._options=i,this.setSource(e)}destroy(){this._dataSourceErrorStatus.destroy()}setSource(e){this._dataSourceErrorStatus.destroy(),this._dataSourceErrorStatus=e.errorStatus().spawn(),this._dataSourceErrorStatus.subscribe(this._updateStatus.bind(this),{callWithLast:!0})}_getTooltip(e){var t;return null!==(t=e?this._getDataSourceErrorStatusCustomTitle():null)&&void 0!==t?t:super._getTooltip(e)}_getTitle(e){var t;return null!==(t=e?this._getDataSourceErrorStatusCustomTitle():null)&&void 0!==t?t:super._getTitle(e)}async _updateFullTooltip(){var e;const t=this._status.value(),i=this._dataSourceErrorStatus.value();null!==t&&null!==i?this._fullTooltip.setValue([{icon:this._getIcon(t),
iconClassName:this._getFullTooltipIconClassNames(t),title:this._getTitle(t),titleColor:this._getTitleColor(t),html:void 0===i.stackTrace?[(0,oi.formatStudyError)((0,li.htmlEscape)(i.error))]:null,size:this._size,action:this._getAction(t),solutionId:this._options.errorSolution?null===(e=this._dataSourceErrorStatus.value())||void 0===e?void 0:e.solutionId:void 0}]):this._fullTooltip.setValue(null)}_getAction(e){return null}_updateStatus(e){const t=this._status.value();null!==e?(this._status.setValue(!0),t&&this._lastError!==e.error&&this._updateByStatus(!0),this._lastError=e.error):(this._status.setValue(null),this._lastError=null)}_getDataSourceErrorStatusCustomTitle(){var e;return(null===(e=this._dataSourceErrorStatus.value())||void 0===e?void 0:e.title)||null}}const bs=X.t(null,void 0,i(78992)),ws=new Map([["high",new Map([["small",vi],["medium",bi],["large",bi]])],["low",new Map([["small",vi],["medium",bi],["large",bi]])]]),Ss=new Map([["high",ai.dataProblemHigh],["low",ai.dataProblemLow]]),ys=new Map([["high",bs],["low",bs]]),Ms=new Map([["high",si.colorsPalette["color-data-problem"]],["low",si.colorsPalette["color-data-problem"]]]);class Cs extends ni{constructor(e,t){super({tooltipMap:ys,iconMap:ws,classNameMap:Ss,titleMap:ys,titleColorMap:Ms,size:t}),this._dataProblems=new n.WatchedValue([]).spawn(),this._isDataProblemCritical=new n.WatchedValue(!1),this.setModel(e)}destroy(){this._dataProblems.destroy()}isDataProblemCritical(){return this._isDataProblemCritical}setModel(e){this._dataProblems.destroy(),null!==e?(this._dataProblems=e.dataProblems().spawn(),this._dataProblems.subscribe(this._updateStatus.bind(this),{callWithLast:!0})):this._dataProblems=new n.WatchedValue([]).spawn()}async _updateFullTooltip(){const e=this.status().value(),t=this._dataProblems.value();if(null===e||0===t.length)return void this._fullTooltip.setValue(null);const i=t.map(((t,i)=>{var s;return{icon:this._getIcon(e),iconClassName:this._getFullTooltipIconClassNames(e),title:null!==(s=t.title)&&void 0!==s?s:0===i?this._getTitle(e):null,titleColor:this._getTitleColor(e),html:[(0,li.htmlEscape)(t.text)],size:this._size,action:this._getAction(e)}}));this._fullTooltip.setValue(i)}_getTooltip(e){var t;return null!==(t=this._getDataProblemCustomTitle())&&void 0!==t?t:super._getTooltip(e)}_getTitle(e){var t;return null!==(t=this._getDataProblemCustomTitle())&&void 0!==t?t:super._getTitle(e)}_updateStatus(e){var t,i;const s=null!==(i=null===(t=e[0])||void 0===t?void 0:t.severity)&&void 0!==i?i:null,l=this._status.value()!==s;this._status.setValue(s),this._isDataProblemCritical.setValue(function(e){return"high"===e}(s)),l||this._updateFullTooltip()}_getDataProblemCustomTitle(){var e,t;return(null===(t=null===(e=this._dataProblems.value())||void 0===e?void 0:e[0])||void 0===t?void 0:t.title)||null}}class fs extends ni{constructor(e,t){super(t),this._booleanStatus=new n.WatchedValue(!1).spawn(),this.updateStatus(e)}destroy(){this._booleanStatus.destroy()}updateStatus(e){this._booleanStatus.destroy(),this._booleanStatus=e.spawn(),
this._booleanStatus.subscribe(this._updateStatus.bind(this),{callWithLast:!0})}_updateStatus(e){e?this._status.setValue(!0):this._status.setValue(null)}}const Es=X.t(null,void 0,i(14285)),Vs=X.t(null,void 0,i(53272)),Ls=new Map([[!0,new Map([["small",vi],["medium",bi],["large",bi]])],[!1,new Map([["small",""],["medium",""],["large",""]])]]),xs=new Map([[!0,ai.invalidSymbol],[!1,null]]),Ws=new Map([[!0,Es],[!1,null]]),As=new Map([[!0,Es],[!1,null]]),ks=new Map([[!0,si.colorsPalette["color-invalid-symbol"]],[!1,null]]),Ts=new Map([[!0,[Vs]],[!1,null]]),Hs=new Map([[!0,null],[!1,null]]);var Bs;!function(e){e.Small="small",e.Medium="medium",e.Large="large"}(Bs||(Bs={}));class Is{constructor(e){this._el=document.createElement("div"),this._prevCustomClass=null,this._prevCustomColor=null,this._customColor=null,this._size=e.size||"small",this._icon=e.icon.spawn(),this._className=e.className.spawn(),this._visible=e.visible.spawn(),this._el.classList.add(ai.statusItem,ai[this._size]),this._icon.subscribe(this._updateIcon.bind(this),{callWithLast:!0}),this._className.subscribe(this._updateClassName.bind(this),{callWithLast:!0}),this._customColor=e.customColor.spawn(),this._customColor.subscribe(this._updateCustomColor.bind(this),{callWithLast:!0}),e.isBlinking&&(this._isBlinking=e.isBlinking.spawn(),this._isBlinking.subscribe(this._updateBlinkingMode.bind(this),{callWithLast:!0}),this._turnOffBlinking=e.turnOffBlinking)}destroy(){this._visible.destroy(),this._icon.destroy(),this._isBlinking&&this._isBlinking.destroy()}onClick(){this._turnOffBlinking&&this._turnOffBlinking()}visible(){return this._visible}element(){return this._el}_updateIcon(e){this._el.innerHTML=e||""}_updateClassName(e){this._prevCustomClass!==e&&(null!==this._prevCustomClass&&this._el.classList.remove(this._prevCustomClass),null!==e&&this._el.classList.add(e),this._prevCustomClass=e)}_updateCustomColor(e){this._prevCustomColor!==e&&(this._el.style.setProperty("--custom-status-color",e),this._prevCustomColor=e)}_updateBlinkingMode(e){this._el.classList.toggle(ai.blinking,e)}}var Ds;function Ps(e,t,i){for(const s of t)for(const t of s.split(" "))e.classList.toggle(t,i)}!function(e){e.TooltipSeparator=" · "}(Ds||(Ds={}));class zs{constructor(e,t,i,s=ai){this.element=document.createElement("div"),this._blinkingSpawns=[],this._iconsRenderers=[],this._updateIcons=()=>{const[e,t]=this._iconsRenderers.reduce(((e,t)=>{const i=t.element();return t.visible().value()&&e[0].length<3?e[0].push(i):e[1].push(i),e}),[[],[]]);t.forEach((e=>{this.element.contains(e)&&this.element.removeChild(e)})),e.forEach(((e,t)=>{this.element.contains(e)||(t>=this.element.childElementCount?this.element.appendChild(e):this.element.insertBefore(e,this.element.childNodes[t]))}))},this._theme=s;const l=[this._theme.statuses,"apply-common-tooltip","common-tooltip-wide",this._theme[e],this._theme.statuses_hidden];Ps(this.element,l,!0),this._tooltips=t.spawn(),this._tooltips.subscribe(this._updateTooltip.bind(this)),this._onClickCallback=i.onClick,this._onClickHandler=this._onClick.bind(this),
this.element.addEventListener("click",this._onClickHandler)}destroy(){for(const e of this._iconsRenderers)e.destroy();for(const e of this._blinkingSpawns)e.destroy();this._tooltips.destroy(),this.element.removeEventListener("click",this._onClickHandler),this.element.remove()}setVisibility(e){Ps(this.element,[this._theme.statuses_hidden],e)}addStatusModel(e){const t=new Is({visible:e.visible,icon:e.model.icon(),className:e.model.className(),size:e.model.size(),isBlinking:e.model.isBlinkingMode,turnOffBlinking:e.model.turnOffBlinkingMode,customColor:e.model.customColor()});this._iconsRenderers.push(t);const i=e.model.isBlinkingMode.spawn();i.subscribe(this._updateBlinkingMode.bind(this)),t.visible().subscribe(this._updateIcons,{callWithLast:!0}),this._blinkingSpawns.push(i),this._updateBlinkingMode()}_onClick(e){e.preventDefault();const t=this._iconsRenderers.filter((e=>e.visible().value()));for(const e of t)e.onClick();let i=14;t.length>1&&(i-=2);const s=this.element.getBoundingClientRect(),l={x:s.left-i,y:s.bottom+4};this._onClickCallback(l)}_updateTooltip(){this.element.setAttribute("title",this._tooltips.value().join(" · "))}_updateBlinkingMode(){const e=this._blinkingSpawns.some((e=>e.value()));Ps(this.element,[this._theme.blinking],e)}}class Ns{constructor(e,t){this.isBlinkingMode=new n.WatchedValue(!1),this._status=new n.WatchedValue(null),this._size="small",this._fullInfo=new n.WatchedValue(null),this._className=new n.WatchedValue(ai.marketStatusCustom),this._symbolModel=null,this._symbol=null,this._tooltip=new n.WatchedValue(null),this._icon=new n.WatchedValue(null),this._visible=new n.WatchedValue(!1),this._color=new n.WatchedValue(null),this._updateVisibleCallback=this._updateVisible.bind(this),this._updateColorCallback=this._updateColor.bind(this),this._updateIconCallback=this._updateIcon.bind(this),this._updateTooltipCallback=this._updateTooltip.bind(this),this._updateFullInfoCallback=this._updateFullInfo.bind(this),this._model=e,this._size=t}turnOffBlinkingMode(){}destroy(){}setModel(e){this._model=e}setSymbol(e){e!==this._symbol&&(this._unSyncModel(),this._symbol=e,e?(this._symbolModel=this._model.getSymbolCustomStatus(e),this._syncModel()):this._symbolModel=null)}status(){return this._status.spawn()}tooltip(){return this._tooltip.spawn()}icon(){return this._icon.spawn()}className(){return this._className.spawn()}visible(){return this._visible.spawn()}size(){return this._size}fullInfo(){return this._fullInfo.spawn()}customColor(){return this._color.spawn()}_updateFullInfo(e){if(null===e)return void this._fullInfo.setValue(null);const t=e.map((e=>{var t,i;return{icon:null!==(t=e.icon)&&void 0!==t?t:this.icon().value(),iconClassName:[ai.marketStatusCustom],title:e.title,titleColor:null!==(i=e.color)&&void 0!==i?i:this.customColor().value(),html:e.content,size:this.size(),action:this._buildAction(e.action)}}));this._fullInfo.setValue(t)}_buildAction(e){var t;return e&&e.onClick?{text:null!==(t=e.text)&&void 0!==t?t:"",tooltip:e.tooltip,onClick:e.onClick}:null}_unSyncModel(){
this._symbolModel&&(this._symbolModel.visible().unsubscribe(this._updateVisibleCallback),this._symbolModel.color().unsubscribe(this._updateColorCallback),this._symbolModel.icon().unsubscribe(this._updateIconCallback),this._symbolModel.tooltip().unsubscribe(this._updateTooltipCallback),this._symbolModel.tooltipContent().unsubscribe(this._updateFullInfoCallback))}_syncModel(){var e,t,i,s,l,o,a,n,r,d;this._visible.setValue(null!==(t=null===(e=this._symbolModel)||void 0===e?void 0:e.visible().value())&&void 0!==t&&t),this._color.setValue(null!==(s=null===(i=this._symbolModel)||void 0===i?void 0:i.color().value())&&void 0!==s?s:null),this._icon.setValue(null!==(o=null===(l=this._symbolModel)||void 0===l?void 0:l.icon().value())&&void 0!==o?o:null),this._tooltip.setValue(null!==(n=null===(a=this._symbolModel)||void 0===a?void 0:a.tooltip().value())&&void 0!==n?n:null),this._updateFullInfo(null!==(d=null===(r=this._symbolModel)||void 0===r?void 0:r.tooltipContent().value())&&void 0!==d?d:null),this._symbolModel&&(this._symbolModel.visible().subscribe(this._updateVisibleCallback),this._symbolModel.color().subscribe(this._updateColorCallback),this._symbolModel.icon().subscribe(this._updateIconCallback),this._symbolModel.tooltip().subscribe(this._updateTooltipCallback),this._symbolModel.tooltipContent().subscribe(this._updateFullInfoCallback))}_updateVisible(e){this._visible.setValue(e)}_updateColor(e){this._color.setValue(e)}_updateIcon(e){this._icon.setValue(e)}_updateTooltip(e){this._tooltip.setValue(e)}}var Rs=i(38119),Os=i(53218),Fs=i(32140),Us=i(62998),Gs=i(25230),js=i(15507),Ks=i(43401),$s=i(85290),Zs=i(12462)
;const Qs=X.t(null,void 0,i(80086)),Xs=X.t(null,void 0,i(36018)),qs=X.t(null,void 0,i(73897)),Js=X.t(null,void 0,i(62464)),Ys=X.t(null,void 0,i(87845)),el=X.t(null,void 0,i(53357)),tl=X.t(null,void 0,i(65420)),il=X.t(null,void 0,i(23302)),sl=X.t(null,void 0,i(52176)),ll=X.t(null,void 0,i(41392)),ol=X.t(null,void 0,i(59938)),al=X.t(null,void 0,i(51320)),nl=X.t(null,void 0,i(83187)),rl=X.t(null,void 0,i(5371)),dl=X.t(null,void 0,i(18643)),ul=X.t(null,void 0,i(81509)),hl=X.t(null,void 0,i(58470)),cl=new Map([["market",new Map([["small",Fs],["medium",Us],["large",Us]])],["pre_market",new Map([["small",$s],["medium",Zs],["large",Zs]])],["post_market",new Map([["small",js],["medium",Ks],["large",Ks]])],["out_of_session",new Map([["small",Os],["medium",Os],["large",Os]])],["holiday",new Map([["small",Gs],["medium",Gs],["large",Gs]])]]),_l=new Map([["market",ai.marketStatusOpen],["pre_market",ai.marketStatusPre],["post_market",ai.marketStatusPost],["out_of_session",ai.marketStatusClose],["holiday",ai.marketStatusHoliday]]),pl=new Map([["market",Qs],["pre_market",Xs],["post_market",qs],["out_of_session",Js],["holiday",Ys]]),gl=new Map([["market",Qs],["pre_market",Xs],["post_market",qs],["out_of_session",Js],["holiday",Ys]]),ml=new Map([["market",si.colorsPalette["color-market-open"]],["pre_market",si.colorsPalette["color-pre-market"]],["post_market",si.colorsPalette["color-post-market"]],["out_of_session",si.colorsPalette["color-market-closed"]],["holiday",si.colorsPalette["color-market-holiday"]]]),vl=new Map([["market",el],["pre_market",tl],["post_market",il],["out_of_session",sl],["holiday",ll]]);const bl=cl,wl=_l,Sl=pl,yl=gl,Ml=ml,Cl=vl;function fl(e){return X.t(null,{plural:"{number} minutes",count:e},i(32547)).format({number:e.toString()})}function El(e){return X.t(null,{plural:"{number} hours",count:e},i(44646)).format({number:e.toString()})}function Vl(e){const t=Math.floor(e/86400),s=Math.floor((e-86400*t)/3600),l=Math.floor((e-86400*t-3600*s)/60);return 0===t&&0===s&&0===l?ol:t>0?al.format({days:(o=t,X.t(null,{plural:"{number} days",count:o},i(39501)).format({number:o.toString()})),hours:El(s)}):s>0?nl.format({hours:El(s),minutes:fl(l)}):fl(l);var o}const Ll={market:e=>("post_market"===e.status?ul:dl).format({remainingTime:Vl(e.remainingSeconds)}),pre_market:e=>rl.format({remainingTime:Vl(e.remainingSeconds)}),post_market:e=>dl.format({remainingTime:Vl(e.remainingSeconds)}),out_of_session:e=>("pre_market"===e.status?hl:rl).format({remainingTime:Vl(e.remainingSeconds)}),holiday:e=>("pre_market"===e.status?hl:rl).format({remainingTime:Vl(e.remainingSeconds)}),delisted:e=>"",expired:e=>""},xl=new Map([["market",null],["pre_market",null],["post_market",null],["out_of_session",null],["holiday",null],["delisted",null]]);class Wl extends ni{constructor(e,t,i=!1){super({tooltipMap:Sl,iconMap:bl,classNameMap:wl,titleMap:yl,titleColorMap:Ml,actionMap:xl,size:t}),this._model=null,this._expiredStatus=null,this._marketStatus=new n.WatchedValue(null).spawn(),this._sessionEdge=new n.WatchedValue(null).spawn(),
this._ignoreHideStatusSettings=i,this.setModel(e),Vt.showMarketOpenStatusProperty.subscribe(this,this._showMarketOpenStatusPropertyChanged)}destroy(){this._marketStatus.destroy(),this._sessionEdge.destroy(),this._model=null,Vt.showMarketOpenStatusProperty.unsubscribeAll(this)}setModel(e){var t;if(this._marketStatus.destroy(),this._sessionEdge.destroy(),null===(t=this._expiredStatus)||void 0===t||t.destroy(),null===e)return this._marketStatus=new n.WatchedValue(null).spawn(),this._sessionEdge=new n.WatchedValue(null).spawn(),void(this._expiredStatus=null);this._model=e;const i=e.futuresContractExpirationTime();i&&(this._expiredStatus=i.expired().spawn(),this._expiredStatus.subscribe((e=>{e&&this._updateByStatus(this._marketStatus.value())}))),this._marketStatus=e.status().spawn(),this._marketStatus.subscribe(this._updateStatus.bind(this),{callWithLast:!0}),this._sessionEdge=e.nextSessionEdge().spawn(),this._sessionEdge.subscribe(this._updateTooltip.bind(this)),this._updateTooltip()}async _updateFullTooltip(){const e=this.status().value();if(null===e)return void this._fullTooltip.setValue(null);const t=[],i=Cl.get(e);i&&t.push((0,li.htmlEscape)(i));const s=this._marketStatus.value();if(null!==this._model&&null!==s&&"expired"!==s&&"delisted"!==s){const i=this._model.nextSessionEdge().value();null!==i&&t.push({text:Ll[e](i),bold:!0})}this._fullTooltip.setValue([{icon:this._getIcon(e),iconClassName:this._getFullTooltipIconClassNames(e),title:this._getTitle(e),titleColor:this._getTitleColor(e),html:t,size:this._size,action:this._getAction(e)}])}_shouldBeHiddenByStatus(e){return!this._ignoreHideStatusSettings&&!Vt.showMarketOpenStatusProperty.value()&&"market"===e}_updateStatus(e){this._status.setValue(e)}_updateTooltip(){this._updateFullTooltip()}_showMarketOpenStatusPropertyChanged(){this._updateByStatus(this._status.value())}}class Al{constructor(e,t){var i,s;this.errorWidgetIsShown=new n.WatchedValue(!1),this._size=v.trackingModeIsAvailable?"medium":"small",this._tooltips=new n.WatchedValue([]),this._visibilitySpawns=[],this._tooltipSpawns=[],this._statusWidgetInfos=[],this._visibility=new n.WatchedValue(!1),this._renderer=new zs(this._size,this._tooltips,{onClick:this._handleToggleDropdown.bind(this)}),this._symbolInvalidViewModel=null,this._dataSourceErrorStatusViewModel=null,this._marketStatusViewModel=null,this._dataUpdatedModeViewModel=null,this._dataProblemViewModel=null,this._pineEditorStateViewModel=null,this._customStatusViewModel=null,this._sessionWidget=null,this._errorWidget=null,this._dataSourceHasErrorVisible=null,this._dataSourceErrorCanBeShown=new n.WatchedValue(!1),this._marketStatusCanBeShown=new n.WatchedValue(!1),this._dataUpdatedModeCanBeShown=new n.WatchedValue(!1),this._dataProblemCanBeShown=new n.WatchedValue(!1),this._isDataProblemCritical=null,this._container=document.createElement("div"),this._menuOpened=!1,this._menuPosition=null,this._handleDropdownMenuClose=()=>{var e;this._menuOpened=!1,null===(e=this._source.symbol())||void 0===e||e.unsubscribe(this._handleDropdownMenuClose),
this._updateDropdownMenu()},this._updateVisibility=e=>{this._visibility.setValue(!e),this._renderer.setVisibility(e)},this._source=e,this._symbol=null!==(s=null===(i=e.symbol())||void 0===i?void 0:i.spawn())&&void 0!==s?s:null,this._options=t,this._statusProviderHidden=e.hidden().spawn(),this._statusProviderHidden.subscribe(this._updateVisibility,{callWithLast:!0}),this._recreateWidgets(),this._symbol&&this._symbol.subscribe(this._recreateAndUpdateWidgetState.bind(this)),this._addSubscriptionForSymbolInvalid(),null!==this._dataSourceHasErrorVisible&&(this._dataSourceHasErrorVisible.subscribe(this._updateStatusWidgetsVisibilities.bind(this)),this._dataSourceHasErrorVisible.subscribe(this._updateErrorWidgetIsShown.bind(this))),this._options.dataProblemEnabled&&null!==this._isDataProblemCritical&&this._isDataProblemCritical.subscribe(this._updateStatusWidgetsVisibilities.bind(this));for(const e of this._tooltipSpawns)e.subscribe(this._updateTooltips.bind(this));for(const e of this._visibilitySpawns)e.subscribe(this._updateTooltips.bind(this));this._updateErrorWidgetIsShown(),this._updateStatusWidgetsVisibilities(),this._updateTooltips()}destroy(){var e,t;this._statusProviderHidden.destroy(),this._visibility.unsubscribe(),null===(e=this._symbol)||void 0===e||e.destroy(),null===(t=this._isDataProblemCritical)||void 0===t||t.destroy();for(const e of this._tooltipSpawns)e.destroy();for(const e of this._visibilitySpawns)e.destroy();for(const e of this._statusWidgetInfos){if(e.additionalWidgets)for(const t of e.additionalWidgets)t.destroy();e.model.destroy()}this._renderer.destroy()}visibility(){return this._visibility.readonly()}getElement(){return this._renderer.element}_updateStatusWidgetsVisibilities(){const e=this._isForceStatusActive();this._dataSourceErrorCanBeShown.setValue(!e),this._marketStatusCanBeShown.setValue(!e),this._dataUpdatedModeCanBeShown.setValue(!e),this._dataProblemCanBeShown.setValue(!this._isPrimaryWidgetShown())}_isPrimaryWidgetShown(){var e,t;return null!==(t=null===(e=this._source.isSymbolInvalid())||void 0===e?void 0:e.value())&&void 0!==t&&t}_isForceStatusActive(){var e,t;return this._isPrimaryWidgetShown()||null!==(t=null===(e=this._isDataProblemCritical)||void 0===e?void 0:e.value())&&void 0!==t&&t}_recreateWidgets(){var e,t,i,s,l,a,n,r;if(this._options.sourceStatusesEnabled){const t=this._source.isSymbolInvalid();if(null!==t)if(null===this._symbolInvalidViewModel){this._symbolInvalidViewModel=new fs(t,{tooltipMap:Ws,iconMap:Ls,classNameMap:xs,titleMap:As,titleColorMap:ks,htmlMap:Ts,actionMap:Hs,size:this._size});const e=this._symbolInvalidViewModel.visible().spawn();this._visibilitySpawns.push(e),this._tooltipSpawns.push(this._symbolInvalidViewModel.tooltip().spawn());const i={visible:e,model:this._symbolInvalidViewModel};this._statusWidgetInfos.push(i),this._renderer.addStatusModel(i)}else this._symbolInvalidViewModel.updateStatus(t),this._addSubscriptionForSymbolInvalid();if(null===this._dataSourceErrorStatusViewModel){
this._dataSourceErrorStatusViewModel=new vs(this._source,this._size,this._options.sourceStatuses),this._dataSourceHasErrorVisible=(0,we.combine)((()=>this._dataSourceErrorCanBeShown.value()&&(0,o.ensureNotNull)(this._dataSourceErrorStatusViewModel).visible().value()),this._dataSourceErrorCanBeShown.weakReference(),this._dataSourceErrorStatusViewModel.visible().weakReference()),this._visibilitySpawns.push(this._dataSourceHasErrorVisible),this._tooltipSpawns.push(this._dataSourceErrorStatusViewModel.tooltip().spawn());const e=[];0;const t={visible:this._dataSourceHasErrorVisible,model:this._dataSourceErrorStatusViewModel,additionalWidgets:e};this._statusWidgetInfos.push(t),this._renderer.addStatusModel(t)}else this._dataSourceErrorStatusViewModel.setSource(this._source),null===(e=this._errorWidget)||void 0===e||e.updateSource(this._source)}if(this._options.marketStatusEnabled){const e=this._source.marketStatusModel();if(null===this._marketStatusViewModel){this._marketStatusViewModel=new Wl(e,this._size);const t=(0,we.combine)((()=>this._marketStatusCanBeShown.value()&&(0,o.ensureNotNull)(this._marketStatusViewModel).visible().value()&&!(0,Et.isEconomicSymbol)(this._source.symbolInfo().value())),this._marketStatusCanBeShown.weakReference(),this._marketStatusViewModel.visible().weakReference(),this._source.symbolInfo().weakReference());this._visibilitySpawns.push(t),this._tooltipSpawns.push(this._marketStatusViewModel.tooltip().spawn());const i={visible:t,model:this._marketStatusViewModel};null!==e&&(this._sessionWidget=new ii(this._source),i.additionalWidgets=[this._sessionWidget]),this._statusWidgetInfos.push(i),this._renderer.addStatusModel(i)}else this._marketStatusViewModel.setModel(e),null===(t=this._sessionWidget)||void 0===t||t.updateSource(this._source)}if(this._options.dataUpdateModeEnabled){const e=this._source.dataUpdatedModeModel();if(null===this._dataUpdatedModeViewModel){const t={...this._options.dataUpdateMode,marketStatus:null===(i=this._source.marketStatusModel())||void 0===i?void 0:i.status().spawnOwnership()};this._dataUpdatedModeViewModel=new us(e,this._size,t);const s=(0,we.combine)((()=>this._dataUpdatedModeCanBeShown.value()&&(0,o.ensureNotNull)(this._dataUpdatedModeViewModel).visible().value()&&!(0,Et.isEconomicSymbol)(this._source.symbolInfo().value())),this._dataUpdatedModeCanBeShown.weakReference(),this._dataUpdatedModeViewModel.visible().weakReference(),this._source.symbolInfo().weakReference());this._visibilitySpawns.push(s),this._tooltipSpawns.push(this._dataUpdatedModeViewModel.tooltip().spawn());const l={visible:s,model:this._dataUpdatedModeViewModel};this._statusWidgetInfos.push(l),this._renderer.addStatusModel(l)}else this._dataUpdatedModeViewModel.setModel(e)}if(this._options.dataProblemEnabled){const e=this._source.dataProblemModel();if(null===this._dataProblemViewModel){this._dataProblemViewModel=new Cs(e,this._size),this._isDataProblemCritical=this._dataProblemViewModel.isDataProblemCritical().spawn();const t=(0,we.combine)((()=>this._dataProblemCanBeShown.value()&&(0,
o.ensureNotNull)(this._dataProblemViewModel).visible().value()),this._dataProblemCanBeShown.weakReference(),this._dataProblemViewModel.visible().weakReference());this._visibilitySpawns.push(t),this._tooltipSpawns.push(this._dataProblemViewModel.tooltip().spawn());const i={visible:t,model:this._dataProblemViewModel};this._statusWidgetInfos.push(i),this._renderer.addStatusModel(i)}else this._dataProblemViewModel.setModel(e)}if(null!==(a=null===(l=(s=this._source).isMainSeries)||void 0===l?void 0:l.call(s))&&void 0!==a&&a){const e=Rs.CustomStatusModel.getInstance(),t=null!==(r=null===(n=this._symbol)||void 0===n?void 0:n.value())&&void 0!==r?r:null;if(null===this._customStatusViewModel){this._customStatusViewModel=new Ns(e,this._size),this._customStatusViewModel.setSymbol(t);const i=this._customStatusViewModel.visible().spawn(),s={visible:i,model:this._customStatusViewModel};this._visibilitySpawns.push(i),this._tooltipSpawns.push(this._customStatusViewModel.tooltip().spawn()),this._statusWidgetInfos.push(s),this._renderer.addStatusModel(s)}else this._customStatusViewModel.setModel(e),this._customStatusViewModel.setSymbol(t)}}_updateTooltips(){const e=[];for(let t=0;t<this._tooltipSpawns.length;t++){if(!this._visibilitySpawns[t].value())continue;const i=this._tooltipSpawns[t].value();null!==i&&i.length>0&&e.push(i)}this._tooltips.setValue(e)}_addTooltipSpawn(e){e.subscribe(this._updateTooltips.bind(this)),this._tooltipSpawns.push(e)}_addVisibilitySpawn(e){e.subscribe(this._updateTooltips.bind(this)),this._visibilitySpawns.push(e)}_recreateAndUpdateWidgetState(){this._recreateWidgets(),this._updateStatusWidgetsVisibilities(),this._updateErrorWidgetIsShown(),this._updateTooltips()}_addSubscriptionForSymbolInvalid(){const e=this._source.isSymbolInvalid();this._options.sourceStatusesEnabled&&null!==e&&(e.subscribe(this._updateStatusWidgetsVisibilities.bind(this)),e.subscribe(this._updateErrorWidgetIsShown.bind(this),{callWithLast:!0}))}_updateErrorWidgetIsShown(){var e,t,i,s;const l=null!==(t=null===(e=this._source.isSymbolInvalid())||void 0===e?void 0:e.value())&&void 0!==t&&t,o=null!==(s=null===(i=this._dataSourceHasErrorVisible)||void 0===i?void 0:i.value())&&void 0!==s&&s;this.errorWidgetIsShown.setValue(l||o)}_handleToggleDropdown(e){var t,i;this._menuPosition=e,this._menuOpened=!this._menuOpened,this._menuOpened&&(null===(t=this._source.symbol())||void 0===t||t.subscribe(this._handleDropdownMenuClose),i=`Open full tooltip for statuses: ${this._tooltips.value().join(", ")}`,(0,q.trackEvent)("GUI","Statuses widget's action",i)),this._updateDropdownMenu()}_updateDropdownMenu(){Promise.all([i.e(2603),i.e(7769),i.e(8077),i.e(2106),i.e(361),i.e(8643)]).then(i.bind(i,12048)).then((e=>{e.render({opened:this._menuOpened,container:this._container,rendererButton:this._renderer.element,statusWidgetInfos:this._statusWidgetInfos,onClose:this._handleDropdownMenuClose,position:(0,o.ensureNotNull)(this._menuPosition)})}))}}class kl extends Al{constructor(e,t,i){super(e,i),this._isInReplay=new n.WatchedValue(!1).readonly().spawn(),
this._isInReplayCanBeShown=null,this._inited=!1,this._halalCanBeShown=new n.WatchedValue(!1)}destroy(){super.destroy()}_updateStatusWidgetsVisibilities(){super._updateStatusWidgetsVisibilities()}_isPrimaryWidgetShown(){var e,t;return super._isPrimaryWidgetShown()||null!==(t=null===(e=this._isInReplay)||void 0===e?void 0:e.value())&&void 0!==t&&t}_crateHalalStatus(){}_getHalalVisibilitySpawn(){return new n.WatchedValue(!1).readonly().spawn()}}var Tl,Hl=i(630);!function(e){e[e.ForceDisableHiddenStateTimeoutMs=3500]="ForceDisableHiddenStateTimeoutMs"}(Tl||(Tl={}));class Bl{constructor(e,t){this._hidden=new n.WatchedValue(!1),this._symbol=null,this._isSymbolInvalid=null,this._symbolInfo=new n.WatchedValue(null).spawn(),this._source=e,e.properties().hasChild("symbol")&&(this._symbol=(0,he.createWVFromGetterAndSubscription)((()=>e.properties().symbol.value()),e.properties().symbol.listeners()));const i=[];if((0,Hl.isStudyLineTool)(e))i.push(e.onStatusChanged());else if((0,et.isStudy)(e)||(0,et.isStudyStub)(e))i.push(e.onStatusChanged(),e.onIsActualIntervalChange()),this._isSymbolInvalid=(0,he.createWVFromGetterAndSubscriptions)((()=>e.isSymbolInvalid()&&e.isActualInterval()),i);else{(0,o.assert)(e===t.mainSeries());const s=t.mainSeries();i.push(s.onStatusChanged()),this._isSymbolInvalid=(0,he.createWVFromGetterAndSubscriptions)((()=>s.isSymbolInvalid()),i),this._symbolInfo=(0,he.createWVFromGetterAndSubscription)(s.symbolInfo.bind(s),s.dataEvents().symbolResolved())}this._dataSourceErrorStatus=(0,he.createWVFromGetterAndSubscriptions)((()=>this._source.statusProvider({}).errorStatus()),i)}destroy(){var e,t;null===(e=this._symbol)||void 0===e||e.destroy(),null===(t=this._isSymbolInvalid)||void 0===t||t.destroy(),this._dataSourceErrorStatus.destroy(),this._symbolInfo.destroy()}entityId(){return this._source.id()}symbol(){return this._symbol}isSymbolInvalid(){return this._isSymbolInvalid}errorStatus(){return this._dataSourceErrorStatus}symbolInfo(){return this._symbolInfo}hidden(){return this._hidden.readonly()}marketStatusModel(){return this._source.marketStatusModel()}dataProblemModel(){return this._source.dataProblemModel()}dataUpdatedModeModel(){return this._source.dataUpdatedModeModel()}async pineSourceCodeModel(){return null}isMainSeries(){var e,t,i;return null!==(i=null===(t=(e=this._source).isMainSeries)||void 0===t?void 0:t.call(e))&&void 0!==i&&i}}class Il extends Bl{constructor(e,t){super(e,t),this._quotesData=new n.WatchedValue(null),this._forceDisableHiddenState=new n.WatchedValue(!0),this._forceDisableHiddenStateTimeout=null,this._series=e,this._marketStatus=e.marketStatusModel().status().spawn(),this._marketStatus.subscribe((e=>{null===e&&(null!==this._forceDisableHiddenStateTimeout&&clearTimeout(this._forceDisableHiddenStateTimeout),this._forceDisableHiddenState.setValue(!1),this._forceDisableHiddenStateTimeout=setTimeout((()=>{this._forceDisableHiddenStateTimeout=null,this._forceDisableHiddenState.setValue(!0)}),3500))}),{callWithLast:!0}),
this._dataProblems=this._series.dataProblemModel().dataProblems().spawn(),this._marketStatus.subscribe(this._updateHiddenValue.bind(this)),this._dataProblems.subscribe(this._updateHiddenValue.bind(this)),this._forceDisableHiddenState.subscribe(this._updateHiddenValue.bind(this)),e.onStatusChanged().subscribe(this,this._updateHiddenValue),this._updateHiddenValue()}quotesData(){return this._quotesData.readonly().spawn()}destroy(){this._marketStatus.destroy(),this._dataProblems.destroy(),this._series.onStatusChanged().unsubscribeAll(this),null!==this._forceDisableHiddenStateTimeout&&clearTimeout(this._forceDisableHiddenStateTimeout),super.destroy()}_updateHiddenValue(){const e=this._series.status(),t=this._forceDisableHiddenState.value()||12===e||4===e||null!==this._marketStatus.value()&&2!==e&&1!==e||this._dataProblems.value().some((e=>"high"===e.severity));this._hidden.setValue(!t)}}var Dl=i(85662);const Pl={readOnlyMode:!1,contextMenu:{settings:!0,mainSeries:!0,studies:!0,showOpenMarketStatus:!1},symbolMarkerEnabled:!1,showToggleButton:!0,canShowSourceCode:!1,statusesWidgets:{sourceStatusesEnabled:!1,sourceStatuses:{errorSolution:!0},marketStatusEnabled:!1,marketStatus:{preMarketSolution:!0,postMarketSolution:!0},dataUpdateModeEnabled:!1,dataUpdateMode:{subscriptionFullInfo:!0},dataProblemEnabled:!1}};var zl;!function(e){e.TogglerStateSettingsKey="legend.isVisibilityToggled",e[e.TextSourceIsAlwaysTickerMaxSize=132]="TextSourceIsAlwaysTickerMaxSize"}(zl||(zl={}));c.enabled("hide_legend_by_default");const Nl=c.enabled("fundamental_widget"),Rl=c.enabled("legend_context_menu"),Ol=2*parseInt(A.marginlegendhoriz);class Fl{constructor(e,t,i,s,o,d,u,h){this._mainSeriesViewModel=null,this._dataSourceViewModels=[],this._sourcesIds=[],this._visibleDataSourceCount=new n.WatchedValue(0),this._themedColor=new n.WatchedValue(""),this._mainSeriesRowHidden=null,this._dataSourceRowsHidden=[],this._customWidgetsVisibilities=[],this._allLegendHidden=new n.WatchedValue(!1),this._studiesLegendHidden=new n.WatchedValue(!1),this._showCollapserWithOneIndicator=new n.WatchedValue(!1),this._customWidgetsHeights=[],this._onLegendVisibilityToggled=null,this._availableHeight=0,this._collapsedDataSourcesCount=new n.WatchedValue(0),this._collapsedDataSourcesTitle=new n.WatchedValue(""),this._mainSeriesStatusWidget=null,this._dataSourcesStatusesWidgets=[],this._statusProviders=new Map,this._size=null,this._customLegendWidgetsFactoriesMap=new Map,this._customLegendWidgetsMap=new Map,this._margin=0,this._layoutChanged=new a.Delegate,this._model=e,this._paneWidget=t,this._options=(0,r.merge)((0,r.clone)(Pl),u),this._callbacks=h,this._mainSeriesViewModelsOptions={readOnlyMode:this._options.readOnlyMode,symbolMarkerEnabled:this._options.symbolMarkerEnabled},this._dataSourceViewModelsOptions={...this._mainSeriesViewModelsOptions,canShowSourceCode:this._options.canShowSourceCode},this._backgroundThemeName=i;const c=this._showLegendCalculatedProperty();this._isDataSourcesCollapsed=new n.WatchedValue(c.value()),c.subscribe(this,(()=>{
this._isDataSourcesCollapsed.setValue(c.value())}));const _=new n.WatchedValue(this._getCustomTextColorValue()),p=this._model.model().properties().childs();p.scalesProperties.childs().textColor.subscribe(this,(()=>{_.setValue(this._getCustomTextColorValue())}));const g=p.paneProperties.childs().legendProperties.childs().showBackground,m=new n.WatchedValue(g.value());g.subscribe(this,(()=>{m.setValue(g.value())}));const v=p.paneProperties.childs().legendProperties.childs().backgroundTransparency,b=new n.WatchedValue(v.value());v.subscribe(this,(()=>{b.setValue(v.value())})),this._hideNotMainSources=s,this._hideNotMainSources.subscribe(this._updateLegendVisibilities.bind(this)),this._hideAllExceptFirstLine=o,this._hideAllExceptFirstLine.subscribe(this._updateCollapsedSourcesMode.bind(this)),this._hideWholeLegend=d,this._hideWholeLegend.subscribe(this._updateLegendVisibilities.bind(this)),this._isPaneMain=new n.WatchedValue(this._getIsPaneMainValue()),this._updateCollapsedSourcesModeThrottle=(0,l.default)(this._updateCollapsedSourcesMode.bind(this),100),this._isPaneMain.subscribe((e=>this._showCollapserWithOneIndicator.setValue(e)),{callWithLast:!0}),this._renderer=new re({isMultipleLayout:this._model.isMultipleLayout(),withActions:!this._options.readOnlyMode,showToggleButton:this._options.showToggleButton,isStudiesLegendHidden:this._studiesLegendHidden.readonly(),isAllLegendHidden:this._allLegendHidden.readonly(),customTextColor:_.readonly(),themedColor:this._themedColor.readonly(),showBackground:m.readonly(),backgroundTransparency:b.readonly(),collapsedDataSourcesCount:this._collapsedDataSourcesCount.readonly(),collapsedDataSourcesTitle:this._collapsedDataSourcesTitle.readonly(),showLegendWidgetContextMenu:this.onShowLegendWidgetContextMenu.bind(this),hideAllExceptFirstLine:this._hideAllExceptFirstLine,onLegendRowFocused:this._callbacks.onLegendRowFocused,onLayoutChanged:this._layoutChanged},{showCollapserWithOneIndicator:this._showCollapserWithOneIndicator.readonly(),visibleDataSourceCount:this._visibleDataSourceCount.readonly(),isDataSourcesCollapsed:this._isDataSourcesCollapsed.readonly(),showObjectsTree:this._isPaneMain.readonly(),onCollapseDataSources:this.onCollapseDataSources.bind(this),onShowObjectsTreeDialog:this._callbacks.showObjectsTreeDialog})}destroy(){this._backgroundThemeName.release(),this._hideNotMainSources.release(),this._hideAllExceptFirstLine.release(),this._hideWholeLegend.release(),this._sourcesIds=[],null!==this._mainSeriesViewModel&&this._destroyMainDataSource();for(const[,e]of this._statusProviders)e.destroy();for(const e of this._dataSourceViewModels)e.destroy();this._dataSourceViewModels=[];for(const e of this._dataSourcesStatusesWidgets)e.destroy();this._dataSourcesStatusesWidgets=[],this._clearSubscriptions();for(const e of Array.from(this._customLegendWidgetsMap.keys()))this._destroyCustomWidgetFromLayerBlock(e);this._customLegendWidgetsMap.clear(),this._renderer.destroy(),delete this._renderer,this._showLegendCalculatedProperty().unsubscribeAll(this),
this._showLegendOriginalProperty().unsubscribeAll(this);const e=this._model.model().properties().childs();e.scalesProperties.childs().textColor.unsubscribeAll(this),e.paneProperties.childs().legendProperties.childs().showBackground.unsubscribeAll(this),e.paneProperties.childs().legendProperties.childs().backgroundTransparency.unsubscribeAll(this)}addCustomWidgetToLegend(e,t){const i=this._customLegendWidgetsFactoriesMap.get(t.block)||new Map,s=i.get(t.position)||[];s.push(e),i.set(t.position,s),this._customLegendWidgetsFactoriesMap.set(t.block,i),this.updateLayout(),this._updateCustomWidgetModeBySize()}onShowLegendWidgetContextMenu(e,t){if(this._options.readOnlyMode||!Rl)return Promise.resolve();J("Show legend context menu");const i=new Map;for(const e of Array.from(this._customLegendWidgetsMap.keys())){const t=(0,o.ensureDefined)(this._customLegendWidgetsMap.get(e)),s=new Map;for(const e of Array.from(t.keys())){const i=(0,o.ensureDefined)(t.get(e)),l=s.get(e)||[];for(const e of i)l.push(...e.contextMenuActions());s.set(e,l)}i.set(e,s)}return function(e,t,i,s,l,o){const a=[],n=s.get(0);if(void 0!==n){const e=n.get(1);void 0!==e&&e.length>0&&(a.push(...e),a.push(new Ct.Separator))}const r=e.model().properties().childs().paneProperties.childs().legendProperties.childs(),d=Jt&&e.model().symbolSources().some((e=>{var t;return void 0!==(null===(t=e.symbolInfo())||void 0===t?void 0:t.price_source_id)}));a.push(new Ct.Action({actionId:"Chart.Legend.ToggleSymbolVisibility",options:{checkable:!0,checked:r.showSeriesTitle.value(),label:Ot,statName:"Show Symbol",onExecute:()=>At(e,r.showSeriesTitle,kt)}})),t.showOpenMarketStatus&&"market"===e.mainSeries().marketStatusModel().status().value()&&!(0,Et.isEconomicSymbol)(e.mainSeries().symbolInfo())&&a.push(new Ct.Action({actionId:"Chart.Legend.ToggleOpenMarketStatusVisibility",options:{checkable:!0,checked:Vt.showMarketOpenStatusProperty.value(),label:Ft,statName:"Show Open market status",onExecute:()=>At(e,Vt.showMarketOpenStatusProperty,Tt)}})),a.push(new Ct.Action({actionId:"Chart.Legend.ToggleOhlcValuesVisibility",options:{checkable:!0,checked:r.showSeriesOHLC.value(),label:Ut,statName:"Show chart values",onExecute:()=>At(e,r.showSeriesOHLC,Ht)}}));const u=e.mainSeries().style(),h=12!==u&&20!==u;if(h&&!xt.alwaysShowLastPriceAndLastDayChange&&a.push(new Ct.Action({actionId:"Chart.Legend.ToggleBarChangeValuesVisibility",options:{checkable:!0,checked:r.showBarChange.value(),label:jt,statName:"Show Bar Change Values",onExecute:()=>At(e,r.showBarChange,It)}})),20!==u&&a.push(new Ct.Action({actionId:"Chart.Legend.ToggleVolumeVisibility",options:{checkable:!0,checked:r.showVolume.value(),label:Kt,statName:"Show Volume",onExecute:()=>At(e,r.showVolume,Nt)}})),h&&(xt.lastDayChangeAvailable||xt.alwaysShowLastPriceAndLastDayChange)){const t=xt.alwaysShowLastPriceAndLastDayChange?r.showBarChange:r.showLastDayChange;a.push(new Ct.Action({actionId:"Chart.Legend.ToggleLastDayChangeValuesVisibility",options:{checkable:!0,checked:t.value(),label:Gt,statName:"Last day change values",
onExecute:()=>At(e,t,Bt)}}))}if(d&&a.push(new Ct.Action({actionId:"Chart.Legend.TogglePriceSourceVisibility",options:{checkable:!0,checked:r.showPriceSource.value(),label:Xt,statName:"Show Price Source",onExecute:()=>At(e,r.showPriceSource,Rt)}})),a.push(new Ct.Separator),void 0!==n){const e=n.get(0);void 0!==e&&e.length>0&&(a.push(...e),a.push(new Ct.Separator))}const c=s.get(1);if(void 0!==c){const e=c.get(1);void 0!==e&&e.length>0&&(a.push(...e),a.push(new Ct.Separator))}if(e.model().priceDataSources().some((e=>!(0,st.isActingAsSymbolSource)(e)&&e.showInObjectTree()))&&(a.push(new Ct.Action({actionId:"Chart.Legend.ToggleIndicatorTitlesVisibility",options:{checkable:!0,checked:r.showStudyTitles.value(),label:$t,statName:"Show Indicator Titles",onExecute:()=>At(e,r.showStudyTitles,Dt)}})),a.push(new Ct.Action({actionId:"Chart.Legend.ToggleIndicatorArgumentsVisibility",options:{checkable:!0,checked:r.showStudyArguments.value(),label:Zt,statName:"Show Indicator Arguments",onExecute:()=>At(e,r.showStudyArguments,Pt)}})),a.push(new Ct.Action({actionId:"Chart.Legend.ToggleIndicatorValuesVisibility",options:{checkable:!0,checked:r.showStudyValues.value(),label:Qt,statName:"Show Indicator Values",onExecute:()=>At(e,r.showStudyValues,zt)}}))),void 0!==c){const e=c.get(0);void 0!==e&&e.length>0&&(a.push(...e),a.push(new Ct.Separator))}t.settings&&(a[a.length-1]instanceof Ct.Separator||a.push(new Ct.Separator),a.push(new Wt.ActionWithStandardIcon({actionId:"Chart.Dialogs.ShowGeneralSettings.LegendTab",options:{label:qt,iconId:"Settings",statName:"Settings...",onExecute:()=>i(De.TabNames.legend)}})));const _=Yt(l.target,"entityId"),p={menuName:"LegendPropertiesContextMenu",detail:{type:_?"study":"series",id:null!=_?_:"_seriesId"}};return ft.ContextMenuManager.showMenu(a,l,void 0,p,o)}(this._model,this._options.contextMenu,this._callbacks.showGeneralChartProperties,i,e,t)}onCollapseDataSources(){const e=this._showLegendOriginalProperty();e.setValue(!e.value())}updateLayout(){const e=this._paneWidget.state().sourcesByGroup().legendViewSources().filter((e=>null!==e.statusView()&&e.isDisplayedInLegend())).reverse(),t=this._sourcesIds,i=this._model.mainSeries(),s=e.indexOf(i);let l;if(-1!==s){if(e.splice(s,1),!Nl&&null===this._mainSeriesViewModel){const e=new Ye(this._model,i,this._mainSeriesViewModelsOptions,this._callbacks,this._options.contextMenu);this._mainSeriesViewModel=e;const t=new kl(this._statusSourceAdapter(i),this._model.model(),this._options.statusesWidgets);this._mainSeriesStatusWidget=t,l={model:e,statusWidget:t}}this._addCustomWidgetForLayerBlock(0)}else null!==this._mainSeriesViewModel&&(l=null,this._destroyMainDataSource(),this._destroyCustomWidgetFromLayerBlock(0));const a=e.map((e=>e.instanceId()));this._sourcesIds=a;const n=[],r=new Map,d=[],u=[],h=this._dataSourceViewModels,c=this._dataSourcesStatusesWidgets;let _=t.length-1;for(;_>=0;){const e=t[_],i=a.indexOf(e);if(-1===i){n.push(_),h[_].destroy(),c[_].destroy();const t=this._statusProviders.get(e);t&&(t.destroy(),this._statusProviders.delete(e))
}else _!==i&&(r.set(e,{model:h[_],statusWidget:c[_]}),d.push({oldIndex:_,newIndex:i}));_--}const p=[],g=[];for(let i=0;i<a.length;i++){const s=a[i];let l,n;if(r.has(s)){const e=(0,o.ensureDefined)(r.get(s));l=e.model,n=e.statusWidget}else if(t[i]===s)l=h[i],n=c[i];else{const t=e[i];l=new Mt(this._model,t,this._dataSourceViewModelsOptions,this._callbacks,this._options.contextMenu),n=new Al(this._statusSourceAdapter(t),this._options.statusesWidgets),u.push({model:l,statusWidget:n,index:i})}p.push(l),g.push(n)}this._dataSourceViewModels=p,this._dataSourcesStatusesWidgets=g,this._dataSourceViewModels.length>0?this._addCustomWidgetForLayerBlock(1):this._destroyCustomWidgetFromLayerBlock(1),this._recreateSubscriptions(),this._isPaneMain.setValue(this._getIsPaneMainValue()),this.update(),this._updateWidgetModeByWidth();const m=n.length||u.length||d.length;(void 0!==l||m)&&this._layoutChanged.fire({newMainSource:l,removedDataSources:n,addedDataSources:u,movedDataSources:d}),m&&this._updateCollapsedSourcesMode()}update(){null!==this._mainSeriesViewModel&&this._mainSeriesViewModel.update();for(const e of this._dataSourceViewModels)e.update()}updateThemedColors(e){null===e&&(e=(0,Dl.getStdThemedValue)("chartProperties.paneProperties.background",this._backgroundThemeName.value())),this._themedColor.setValue(e||"")}firstTitle(){return this._renderer.firstTitle()}getElement(){return this._renderer.getElement()}addMargin(e){if(this._margin===e)return;this._margin=e;this._renderer.getElement().style.maxWidth=0===this._margin?"":`calc(100% - ${this._margin+Ol}px)`,this._updateWidgetModeBySize()}updateWidgetModeBySize(e){this._size=e,this._updateWidgetModeBySize()}_statusSourceAdapter(e){const t=e.instanceId();let i=this._statusProviders.get(t);return void 0===i&&(i=e!==this._model.mainSeries()?new Bl(e,this._model.model()):new Il(this._model.mainSeries(),this._model.model()),this._statusProviders.set(t,i)),i}_updateWidgetModeBySize(){this._updateWidgetModeByWidth(),this._updateWidgetModeByHeight(),this._updateCustomWidgetModeBySize()}_updateWidgetModeByWidth(){if(null===this._size)return;const e=this._availableWidth();this._renderer.updateMode(e),this._paneWidget.hasState()&&this._paneWidget.state().containsMainSeries()&&this._model.mainSeries().setTextSourceIsAlwaysTickerRestrictionEnabled(e<=132)}_updateWidgetModeByHeight(){null!==this._size&&(this._availableHeight=.8*this._size.height,this._updateCollapsedSourcesModeThrottle())}_updateCustomWidgetModeBySize(){if(null===this._size)return;const e=(0,s.size)({width:this._availableWidth(),height:this._size.height});for(const t of Array.from(this._customLegendWidgetsMap.values()))for(const i of Array.from(t.values()))for(const t of i)t.updateWidgetModeBySize(e)}_destroyMainDataSource(){(0,o.ensureNotNull)(this._mainSeriesStatusWidget).destroy(),this._mainSeriesStatusWidget=null;(0,o.ensureNotNull)(this._mainSeriesViewModel).destroy(),this._mainSeriesViewModel=null}_updateCollapsedSourcesMode(){const e=this._dataSourceViewModels.length,t=this._hideAllExceptFirstLine.value()
;if(this._availableHeight>0&&e>2){const i=Number(this._renderer.getMainSourceHeight()),s=this._renderer.getDataSourceHeight(),l=this._getCustomWidgetsHeight();if(null!==s){const o=Math.floor((this._availableHeight-i-l)/s),a=Math.max(o,2)-1;if(e>a+1){let i="";for(let s=0;s<e;s++){const e=s<a;this._dataSourceViewModels[s].setGlobalVisibility(e&&(!t||0===s)),e||(i+=`${0===i.length?"":", "}${this._dataSourceViewModels[s].getFullTitle()}`)}return this._collapsedDataSourcesTitle.setValue(i),void this._collapsedDataSourcesCount.setValue(e-a)}}}for(let e=0;e<this._dataSourceViewModels.length;++e)this._dataSourceViewModels[e].setGlobalVisibility(!t||0===e);this._collapsedDataSourcesCount.setValue(0),this._collapsedDataSourcesTitle.setValue("")}_getCustomWidgetsHeight(){let e=0;for(const t of Array.from(this._customLegendWidgetsMap.values()))for(const i of Array.from(t.values()))for(const t of i)e+=t.height().value();return e}_getCustomTextColorValue(){const e=this._model.model().properties().childs().scalesProperties.childs().textColor.value();return(0,Dl.isStdThemedDefaultValue)("chartProperties.scalesProperties.textColor",e,(0,Dl.getCurrentTheme)().name)?null:e}_clearSubscriptions(){null!==this._mainSeriesRowHidden&&(this._mainSeriesRowHidden.destroy(),this._mainSeriesRowHidden=null);for(const e of this._dataSourceRowsHidden)e.destroy();this._dataSourceRowsHidden=[];for(const e of this._customWidgetsVisibilities)e.destroy();this._customWidgetsVisibilities=[];for(const e of this._customWidgetsHeights)e.destroy();this._customWidgetsHeights=[]}_recreateSubscriptions(){this._clearSubscriptions(),null!==this._mainSeriesViewModel&&(this._mainSeriesRowHidden=this._mainSeriesViewModel.isRowHidden().spawn(),this._mainSeriesRowHidden.subscribe(this._updateLegendVisibilities.bind(this)));for(const e of this._dataSourceViewModels){const t=e.isRowHidden().spawn();this._dataSourceRowsHidden.push(t),t.subscribe(this._updateVisibleDataSourceCount.bind(this)),t.subscribe(this._updateLegendVisibilities.bind(this))}for(const e of Array.from(this._customLegendWidgetsMap.values()))for(const t of Array.from(e.values()))for(const e of t){const t=e.visibility().spawn();this._customWidgetsVisibilities.push(t),t.subscribe(this._updateLegendVisibilities.bind(this));const i=e.height().spawn();this._customWidgetsHeights.push(i),i.subscribe(this._updateCollapsedSourcesMode.bind(this))}this._updateVisibleDataSourceCount(),this._updateLegendVisibilities()}_updateLegendVisibilities(){if(this._hideWholeLegend.value())return void this._allLegendHidden.setValue(!0);const e=0!==this._dataSourceRowsHidden.length&&this._dataSourceRowsHidden.every((e=>e.value())),t=this._hideNotMainSources.value()||e;this._studiesLegendHidden.setValue(t);const i=null===this._mainSeriesRowHidden||this._mainSeriesRowHidden.value(),s=this._customWidgetsVisibilities.some((e=>e.value()));this._allLegendHidden.setValue(e&&i&&!s)}_updateVisibleDataSourceCount(){const e=this._dataSourceRowsHidden.filter((e=>!e.value())).length;this._visibleDataSourceCount.setValue(e)}
_setLegendVisibilityToggled(){0}_getIsPaneMainValue(){return this._paneWidget.containsMainSeries()}_showLegendCalculatedProperty(){return this._model.model().showLegend()}_showLegendOriginalProperty(){return this._model.model().properties().childs().paneProperties.childs().legendProperties.childs().showLegend}_addCustomWidgetForLayerBlock(e){const t=this._customLegendWidgetsFactoriesMap.get(e);if(void 0===t)return;const i=this._customLegendWidgetsMap.get(e)||new Map;let s=!1;for(const l of Array.from(t.keys())){const o=i.get(l)||[],a=t.get(l)||[];for(let t=o.length;t<a.length;t++){const i=a[t](this._model.model(),this._backgroundThemeName.spawnOwnership());0===e&&0===l&&i.setGlobalVisibility((0,we.combine)(((e,t)=>!e&&!t),this._hideNotMainSources.weakReference(),this._hideAllExceptFirstLine.weakReference()).ownership()),o.push(i),this._renderer.addCustomWidget(i,{block:e,position:l}),s=!0}s&&i.set(l,o)}s&&this._customLegendWidgetsMap.set(e,i)}_destroyCustomWidgetFromLayerBlock(e){const t=this._customLegendWidgetsMap.get(e);if(void 0!==t){for(const e of Array.from(t.values()))for(const t of e)t.destroy();t.clear(),this._customLegendWidgetsMap.delete(e)}}_availableWidth(){return null===this._size?0:Math.max(0,this._size.width-this._margin-Ol)}}},61560:(e,t,i)=>{"use strict";i.r(t),i.d(t,{PaneControlsWidget:()=>J});var s,l=i(32563),o=i(51768),a=i(11542),n=i(68335),r=i(51613),d=i(72708),u=i(50151),h=i(24377),c=i(40281),_=i(63276),p=i(19317),g=i(76920);!function(e){e[e.ContextMenuModeWidthPt=666.65]="ContextMenuModeWidthPt",e[e.VisibleModeMinWidth=356]="VisibleModeMinWidth"}(s||(s={}));class m{constructor(e,t,i){this._parentEl=document.createElement("div"),this._listActionsWrapperEl=null,this._listActionsElements={},this._actionsSpawns={},this._onMouseEnterLeaveEventHandler=null,this._mouseOverWidget=!1,this._wrapEl=e,this._onMouseEnterLeaveEventHandler=this._onMouseEnterLeaveEvent.bind(this),this._wrapEl.addEventListener("mouseenter",this._onMouseEnterLeaveEventHandler),this._wrapEl.addEventListener("mouseleave",this._onMouseEnterLeaveEventHandler),this._actions=t,this._globalVisibility=i.globalVisibility.spawn(),this._globalVisibility.subscribe(this._updatePaneControlsWidgetVisibility.bind(this)),this._visibilityType=i.visibilityType.spawn(),this._visibilityType.subscribe(this._updatePaneControlsWidgetVisibility.bind(this)),this._doNotSwitchToContextMenuMode=i.doNotSwitchToContextMenuMode,this._themedColor=i.themedColor.spawn(),this._themedColor.subscribe(this._updateThemedColor.bind(this));for(const[e,t]of Object.entries(this._actions)){const i=e;this._actionsSpawns[i]={visible:t.visible.spawn(),title:void 0===t.title?null:t.title.spawn()},this._actionsSpawns[i].visible.subscribe(this._updateActionVisibilities.bind(this,i));const s=this._actionsSpawns[i].title;null!==s&&s.subscribe(this._updateActionTitle.bind(this,i))}this._render(),this._updatePaneControlsWidgetVisibility(),this._updateThemedColor(this._themedColor.value()),this._parentEl.classList.toggle(p.touchMode,c.trackingModeIsAvailable),
this._parentEl.addEventListener("contextmenu",(e=>e.preventDefault())),this._parentEl.setAttribute("aria-hidden","true")}destroy(){this._visibilityType.destroy(),this._themedColor.destroy();for(const e of Object.keys(this._actionsSpawns)){const t=e;this._actionsSpawns[t].visible.destroy();const i=this._actionsSpawns[t].title;null!==i&&i.destroy()}null!==this._onMouseEnterLeaveEventHandler&&(this._wrapEl.removeEventListener("mouseenter",this._onMouseEnterLeaveEventHandler),this._wrapEl.removeEventListener("mouseleave",this._onMouseEnterLeaveEventHandler),this._onMouseEnterLeaveEventHandler=null),this._parentEl.innerHTML="",delete this._parentEl}getElement(){return this._parentEl}bottomWithMargin(){const e=this._parentEl.classList.contains(p.touchMode)?Number(p.css_value_pane_controls_button_touch_size):Number(p.css_value_pane_controls_button_size);return 2*Number(p.css_value_pane_controls_margin_top)+e}updateWidgetModeByWidth(e){const t=!this._doNotSwitchToContextMenuMode.value()&&e<356,i=!this._doNotSwitchToContextMenuMode.value()&&e<666.65,s=(0,u.ensureNotNull)(this._listActionsWrapperEl),l=(0,u.ensureNotNull)(this._listActionsElements.more);s.classList.toggle(g.blockHidden,t||i),l.classList.toggle(g.blockHidden,t||!i||!this._actions.more.visible.value())}_render(){this._renderActions(),this._parentEl.classList.add(p.paneControls),this._wrapEl.append(this._parentEl)}_renderActions(){null===this._listActionsWrapperEl&&(this._listActionsWrapperEl=document.createElement("div"),this._listActionsWrapperEl.classList.add(p.buttonsWrapper),this._parentEl.append(this._listActionsWrapperEl));const e={iconSize:c.trackingModeIsAvailable?"large":"small",tag:"div",buttonClassName:p.button,wrapIconClassName:p.buttonIcon,hiddenClassName:g.blockHidden};this._listActionsElements.up=(0,_.createActionElement)(this._actions.up,e),this._listActionsElements.down=(0,_.createActionElement)(this._actions.down,e),this._listActionsElements.collapse=(0,_.createActionElement)(this._actions.collapse,e),this._listActionsElements.restore=(0,_.createActionElement)(this._actions.restore,e),this._listActionsElements.close=(0,_.createActionElement)(this._actions.close,e),this._listActionsElements.maximize=(0,_.createActionElement)(this._actions.maximize,e),this._listActionsElements.minimize=(0,_.createActionElement)(this._actions.minimize,e),this._listActionsWrapperEl.append(this._listActionsElements.up,this._listActionsElements.down,this._listActionsElements.close,this._listActionsElements.collapse,this._listActionsElements.restore,this._listActionsElements.maximize,this._listActionsElements.minimize),this._listActionsElements.more=(0,_.createActionElement)(this._actions.more,e);for(const e of Object.keys(this._listActionsElements))(0,u.ensureNotNull)(this._listActionsElements[e]).classList.add(p.newButton);this._parentEl.append(this._listActionsElements.more)}_updateActionVisibilities(e,t){(0,u.ensureNotNull)(this._listActionsElements[e]).classList.toggle(g.blockHidden,!t)}_updateActionTitle(e,t){(0,
u.ensureNotNull)(this._listActionsElements[e]).setAttribute("title",t)}_onMouseEnterLeaveEvent(e){this._mouseOverWidget="mouseenter"===e.type,"visibleOnMouseOver"===this._visibilityType.value()&&this._updatePaneControlsWidgetVisibility()}_updatePaneControlsWidgetVisibility(){let e,t=!1;switch(this._visibilityType.value()){case"alwaysOff":e=!1,t=!0;break;case"alwaysOn":e=this._globalVisibility.value();break;case"visibleOnMouseOver":e=this._globalVisibility.value()&&this._mouseOverWidget}this._parentEl.classList.toggle(p.hidden,!e),this._parentEl.classList.toggle(p.forceHidden,!this._globalVisibility.value()||t)}_updateThemedColor(e){if(e.length>0){const[t,i,s]=(0,h.parseRgb)(e);this._parentEl.style.color=(0,h.rgbaToString)([t,i,s,(0,h.normalizeAlphaComponent)(.8)])}else this._parentEl.style.removeProperty("color")}}var v=i(97702),b=i(40443),w=i(30426),S=i(72899),y=i(48344),M=i(99539),C=i(20465),f=i(34763);const E=a.t(null,void 0,i(13930)),V=E,L=(0,n.humanReadableModifiers)(n.Modifiers.Mod)+E;var x=i(64147),W=i(85662),A=i(61814),k=i(37896),T=i(81020),H=i(3515),B=i(79526),I=i(82847),D=i(7859),P=i(70471),z=i(71402),N=i(42930);const R=l.mobiletouch,O=a.t(null,void 0,i(66260)),F=a.t(null,void 0,i(7310)),U=a.t(null,void 0,i(74079)),G=a.t(null,void 0,i(90165)),j=a.t(null,void 0,i(12486)),K=a.t(null,void 0,i(65495)),$=a.t(null,void 0,i(75018)),Z=a.t(null,void 0,i(13930));var Q;!function(e){e.PaneCloseButton="pane-button-close",e.PaneUpButton="pane-button-up",e.PaneDownButton="pane-button-down",e.PaneMaximizeButton="pane-button-maximize",e.PaneMinimizeButton="pane-button-minimize",e.PaneCollapseButton="pane-button-collapse",e.PaneRestoreButton="pane-button-restore",e.PaneMoreButton="pane-button-more"}(Q||(Q={}));const X=(0,A.hotKeySerialize)({keys:[""],text:Z}),q=(0,A.hotKeySerialize)({keys:[(0,n.humanReadableModifiers)(n.Modifiers.Mod,!1)],text:`{0} + ${Z}`});class J{constructor(e,t,i,s,l){this._actions={},this._moreCMShown=!1,this._themedColor=new x.WatchedValue(""),this._model=e,this._paneWidget=t,this._callbacks=s,this._closeButtonVisibility=new x.WatchedValue(this._getCloseButtonVisibility()),this._upButtonVisibility=new x.WatchedValue(this._getUpButtonVisibility()),this._downButtonVisibility=new x.WatchedValue(this._getDownButtonVisibility()),this._maximizeButtonVisibility=new x.WatchedValue(this._getMaximizeButtonVisibility()),this._minimizeButtonVisibility=new x.WatchedValue(this._getMinimizeButtonVisibility()),this._collapseButtonVisibility=new x.WatchedValue(this._getCollapseButtonVisibility()),this._restoreButtonVisibility=new x.WatchedValue(this._getRestoreButtonVisibility()),this._createActions(),this._visibilityTypeProperty=(0,r.actualBehavior)(),this._visibilityTypeProperty.subscribe(this,(e=>{this._visibilityType.setValue(e.value())})),this._visibilityType=new x.WatchedValue(this._visibilityTypeProperty.value()),this._isPaneMaximize=new x.WatchedValue(this._getIsPaneMaximizeValue()),this._isWidgetShow=new x.WatchedValue(this._getIsWidgetShow()),this._backgroundThemeName=i.backgroundThemeName,
this._renderer=new m(l,this._actions,{visibilityType:this._visibilityType.readonly(),globalVisibility:this._isWidgetShow.readonly(),doNotSwitchToContextMenuMode:this._isPaneMaximize.readonly(),themedColor:this._themedColor.readonly()})}destroy(){this._visibilityTypeProperty.unsubscribeAll(this),this._renderer.destroy()}getElement(){return this._renderer.getElement()}bottomWithMargin(){return this._renderer.bottomWithMargin()}action(){return this._actions}update(){this._updateButtonsVisibility(),this._isPaneMaximize.setValue(this._getIsPaneMaximizeValue()),this._isWidgetShow.setValue(this._getIsWidgetShow())}updateWidgetModeByWidth(e){this._renderer.updateWidgetModeByWidth(e)}updateThemedColors(e){null===e&&(e=(0,W.getStdThemedValue)("chartProperties.paneProperties.background",this._backgroundThemeName.value())),this._themedColor.setValue(e||"")}_updateButtonsVisibility(){this._closeButtonVisibility.setValue(this._getCloseButtonVisibility()),this._upButtonVisibility.setValue(this._getUpButtonVisibility()),this._downButtonVisibility.setValue(this._getDownButtonVisibility()),this._maximizeButtonVisibility.setValue(this._getMaximizeButtonVisibility()),this._minimizeButtonVisibility.setValue(this._getMinimizeButtonVisibility()),this._collapseButtonVisibility.setValue(this._getCollapseButtonVisibility()),this._restoreButtonVisibility.setValue(this._getRestoreButtonVisibility())}_createActions(){this._actions.up={iconMap:new Map([["large",T],["small",T]]),action:this._onUpDownButton.bind(this,"up"),visible:this._upButtonVisibility,title:new x.WatchedValue(F),className:p.up,dataset:{name:"pane-button-up"}},this._actions.down={iconMap:new Map([["large",H],["small",H]]),action:this._onUpDownButton.bind(this,"down"),visible:this._downButtonVisibility,title:new x.WatchedValue(U),className:p.down,dataset:{name:"pane-button-down"}},this._actions.close={iconMap:new Map([["large",k],["small",k]]),action:this._onCloseButton.bind(this),visible:this._closeButtonVisibility,title:new x.WatchedValue(O),dataset:{name:"pane-button-close"}},this._actions.maximize={iconMap:new Map([["large",D],["small",B]]),action:this._onToggleMaximizeButton.bind(this,"Maximize pane"),visible:this._maximizeButtonVisibility,title:new x.WatchedValue(G),hotKeyTitle:X,className:p.maximize,dataset:{name:"pane-button-maximize"}},this._actions.minimize={iconMap:new Map([["large",D],["small",B]]),action:this._onToggleMaximizeButton.bind(this,"Minimize pane"),visible:this._minimizeButtonVisibility,title:new x.WatchedValue(j),hotKeyTitle:X,className:p.minimize,dataset:{name:"pane-button-minimize"}},this._actions.collapse={iconMap:new Map([["large",z],["small",z]]),action:this._onToggleCollapseButton.bind(this,"Collapse pane"),visible:this._collapseButtonVisibility,title:new x.WatchedValue(K),hotKeyTitle:q,className:p.collapse,dataset:{name:"pane-button-collapse"}},this._actions.restore={iconMap:new Map([["large",N],["small",N]]),action:this._onToggleCollapseButton.bind(this,"Restore pane"),visible:this._restoreButtonVisibility,title:new x.WatchedValue(j),
hotKeyTitle:q,className:p.restore,dataset:{name:"pane-button-restore"}},this._actions.more={iconMap:new Map([["large",P],["small",I]]),action:this._showButtonsInContextMenu.bind(this),visible:new x.WatchedValue(!R),title:new x.WatchedValue($),dataset:{name:"pane-button-more"}}}_getCloseButtonVisibility(){const e=this._paneWidget.state();let t=!1;return e.containsMainSeries()||e.maximized().value()||R||(t=e.dataSources().some((e=>(0,d.isStudy)(e)))),t}_onCloseButton(){this._trackEvent("Delete pane");const e=this._model.model().panes().indexOf(this._paneWidget.state());this._model.removePane(e)}_getUpButtonVisibility(){const e=this._paneWidget.state();return this._model.model().panes().indexOf(e)>0&&!e.maximized().value()&&!R}_getDownButtonVisibility(){const e=this._paneWidget.state(),t=this._model.model().panes();return t.indexOf(e)<t.length-1&&!e.maximized().value()&&!R}_onUpDownButton(e){this._trackEvent(`Move pane ${e}`);const t=this._model.model().panes().indexOf(this._paneWidget.state());this._model.rearrangePanes(t,e)}_getMaximizeButtonVisibility(){const e=this._paneWidget.state();return this._model.model().panes().length>1&&!e.maximized().value()&&!R}_getMinimizeButtonVisibility(){const e=this._paneWidget.state();return this._model.model().panes().length>1&&e.maximized().value()}_getCollapseButtonVisibility(){if(R)return!1;const e=this._paneWidget.state();return!e.maximized().value()&&!e.collapsed().value()&&this._model.model().paneCollapsingAvailable().value()}_getRestoreButtonVisibility(){const e=this._paneWidget.state();return!e.maximized().value()&&e.collapsed().value()}_onToggleMaximizeButton(e){this._trackEvent(e),this._callbacks.toggleMaximizePane(this._paneWidget)}_onToggleCollapseButton(e){this._trackEvent(e),this._model.toggleCollapsedPane(this._paneWidget.state())}_showButtonsInContextMenu(e){e.preventDefault(),this._moreCMShown||function(e,t,i){const s=[];if(e.maximize.visible.value()){const t=(0,u.ensure)(e.maximize.title),i=(0,u.ensureNotNull)(e.maximize.action);s.push(new v.Action({actionId:"Chart.PaneControls.MaximizePane",options:{icon:M,label:t.value(),statName:"Maximize Pane",shortcutHint:V,onExecute:()=>i()}}))}else if(e.minimize.visible.value()){const t=(0,u.ensure)(e.minimize.title),i=(0,u.ensureNotNull)(e.minimize.action);s.push(new v.Action({actionId:"Chart.PaneControls.MinimizePane",options:{icon:M,label:t.value(),statName:"Minimize Pane",shortcutHint:V,onExecute:()=>i()}}))}if(e.collapse.visible.value()){const t=(0,u.ensure)(e.collapse.title),i=(0,u.ensureNotNull)(e.collapse.action);s.push(new v.Action({actionId:"Chart.PaneControls.CollapsePane",options:{icon:C,label:t.value(),statName:"Collapse pane",shortcutHint:L,onExecute:()=>i()}}))}if(e.restore.visible.value()){const t=(0,u.ensure)(e.restore.title),i=(0,u.ensureNotNull)(e.restore.action);s.push(new v.Action({actionId:"Chart.PaneControls.RestorePane",options:{icon:f,label:t.value(),statName:"Restore pane",shortcutHint:L,onExecute:()=>i()}}))}if(e.up.visible.value()){const t=(0,u.ensure)(e.up.title),i=(0,
u.ensureNotNull)(e.up.action);s.push(new v.Action({actionId:"Chart.PaneControls.MovePaneUp",options:{icon:S,label:t.value(),statName:"Move pane up",onExecute:()=>i()}}))}if(e.down.visible.value()){const t=(0,u.ensure)(e.down.title),i=(0,u.ensureNotNull)(e.down.action);s.push(new v.Action({actionId:"Chart.PaneControls.MovePaneDown",options:{icon:y,label:t.value(),statName:"Move pane down",onExecute:()=>i()}}))}if(e.close.visible.value()){const t=(0,u.ensure)(e.close.title),i=(0,u.ensureNotNull)(e.close.action);s.push(new v.Action({actionId:"Chart.PaneControls.DeletePane",options:{icon:w,label:t.value(),statName:"Delete pane",onExecute:()=>i()}}))}const l=(0,u.ensureNotNull)(t.target).getBoundingClientRect();return b.ContextMenuManager.showMenu(s,{clientX:l.right,clientY:l.top+l.height+3,attachToXBy:"right"},void 0,void 0,i)}(this._actions,e,(()=>{this._moreCMShown=!1})).then((()=>{this._moreCMShown=!0}))}_getIsPaneMaximizeValue(){return this._paneWidget.state().maximized().value()}_getIsWidgetShow(){return this._model.model().panes().length>1}_trackEvent(e){(0,o.trackEvent)("GUI","Pane action",e)}}},63276:(e,t,i)=>{"use strict";i.d(t,{createActionElement:()=>l});var s=i(37265);function l(e,t){const{buttonClassName:i,wrapIconClassName:l,hiddenClassName:o,iconSize:a,blurOnClick:n}=t,r=document.createElement(t.tag);r.className=i,r.classList.toggle(o,!e.visible.value()),Object.assign(r.dataset,e.dataset),void 0!==e.className&&r.classList.add(e.className),void 0!==e.title&&(r.classList.add("apply-common-tooltip"),r.setAttribute("title",e.title.value()),void 0!==e.hotKeyTitle&&(r.dataset.tooltipHotkey=e.hotKeyTitle)),r.addEventListener("touchend",e.action),r.addEventListener("mousedown",(t=>{0===t.button&&(e.action(t),n&&r.blur())}));const d=document.createElement("div");d.classList.add(l);const u=e.iconMap.get(a)||"";return(0,s.isString)(u)?d.innerHTML=u:d.appendChild(u),r.appendChild(d),r}},40281:(e,t,i)=>{"use strict";i.d(t,{trackingModeIsAvailable:()=>s});const s=i(49483).CheckMobile.any()},89612:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M7.83 3.92 12.28 9l-4.45 5.08-1.13-1L10.29 9l-3.6-4.09 1.14-.99Z"/></svg>'},23317:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M14 10H4V8.5h10V10Z"/></svg>'},77576:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M8.25 13.75v-9.5h1.5v9.5h-1.5Z"/><path fill="currentColor" d="M13.75 9.75h-9.5v-1.5h9.5v1.5Z"/></svg>'},91986:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M14.5 8V3.5H10V2h6v6h-1.5Zm-11 2v4.5H8V16H2v-6h1.5Z"/></svg>'},76996:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M11.5 2v4.5H16V8h-6V2h1.5Zm-5 14v-4.5H2V10h6v6H6.5Z"/></svg>'},78529:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M7.45 3.5 12.48 9l-5.03 5.49 1.1 1.01L14.52 9 8.55 2.49 7.45 3.5Z"/><path fill="currentColor" d="m3.93 5.99 2.58 3-2.58 3.02 1.14.98 3.42-4-3.42-3.98L3.93 6Z"/></svg>'},93724:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M10 6.38V8L6 5.5 10 3v1.85A5.25 5.25 0 1 1 3.75 10a.75.75 0 0 1 1.5 0A3.75 3.75 0 1 0 10 6.38Z"/></svg>'},79304:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M9 2.5c-1.06 0-1.88.93-1.75 1.98l.63 5.03a1.13 1.13 0 0 0 2.25 0l.62-5.03A1.77 1.77 0 0 0 9 2.5zm0 10a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3z"/></svg>'},38373:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M9 4c-.79 0-1.38.7-1.25 1.48l.67 4.03a.59.59 0 0 0 1.16 0l.67-4.03A1.27 1.27 0 0 0 9 4zm0 8a1 1 0 1 0 0 2 1 1 0 0 0 0-2z"/></svg>'},31233:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M7.57 14.8H5.03V3.36c1.62-.05 2.64-.08 3.06-.08 1.66 0 2.98.49 3.96 1.47a5.23 5.23 0 0 1 1.47 3.88c0 4.11-1.99 6.17-5.95 6.17zm-.5-9.66v7.8c.*********** 1.05.06 1.03 0 1.83-.38 2.41-1.12.58-.75.88-1.79.88-3.13 0-2.44-1.14-3.67-3.42-3.67-.22 0-.53.02-.93.06z"/></svg>'},12646:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M7.84 13.7H5.78V4.4l2.48-.06c1.35 0 2.42.4 3.22 ******** 1.19 1.83 1.19 3.15 0 3.34-1.61 5.01-4.83 5.01zm-.41-7.85v6.35c.***********.86.03.83 0 1.48-.3 1.95-.9.48-.6.72-1.46.72-2.54 0-2-.93-2.99-2.78-2.99-.18 0-.43.02-.75.05z"/></svg>'},21672:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="M14 22a8 8 0 1 0 0-16 8 8 0 0 0 0 16Zm5-9H9v2h10v-2Z"/></svg>'},69410:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M7.65 5.16v2.68h3.78v1.73H7.65V13h5.19v1.8H5.62V3.35h7.3v1.8H7.65z"/></svg>'},55593:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M7.9 5.87v2.17h3.07v1.4H7.9v2.8h4.22v1.46H6.25V4.4h5.94v1.47H7.9z"/></svg>'},23683:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M7.2 7.34c0-1.4.73-2.64 1.82-3.34A5.03 5.03 0 0 0 4 9c0 2.76 2.26 5 5.05 5A5.04 5.04 0 0 0 14 10c-.71.8-1.74 1.29-2.89 1.29A3.93 3.93 0 0 1 7.2 7.34Z"/><path fill="currentColor" d="M11.67 6.33 11 5l-.67 1.33-1.33.2.98 1.03L9.76 9 11 8.34l1.24.66-.22-1.44.98-1.03-1.33-.2Z"/></svg>'},72844:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M11 11.39c0-2.27 1.19-4.25 3-5.39-4.43.07-8 3.63-8 8 0 4.42 3.64 8 8.13 8A8.1 8.1 0 0 0 22 16a6.55 6.55 0 0 1-11-4.61Z"/><path fill="currentColor" d="m18 10-1-2-1 2-2 .3 1.47 1.54-.32 2.16L17 13l1.85 1-.32-2.16L20 10.29 18 10Z"/></svg>'},53218:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><rect width="10" height="4" fill="currentColor" rx="2" x="4" y="7"/></svg>'},62998:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><circle fill="currentColor" cx="9" cy="9" r="5"/></svg>'},32140:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18" fill="none"><circle fill="currentColor" cx="9" cy="9" r="4"/></svg>'},25230:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M9.3 9l.9-4.53a1.23 1.23 0 1 0-2.4 0L8.7 9l-.9 4.53a1.23 1.23 0 1 0 2.4 0L9.3 9z"/><path fill="currentColor" d="M9.15 9.26l4.38-1.48a1.23 1.23 0 1 0-1.21-2.09L8.85 8.74l-4.38 1.48a1.23 1.23 0 1 0 1.21 2.09l3.47-3.05z"/><path fill="currentColor" d="M9.15 8.74L5.68 5.69a1.23 1.23 0 1 0-1.2 2.09l4.37 1.48 3.47 3.05a1.23 1.23 0 1 0 1.2-2.09L9.16 8.74z"/></svg>'},43401:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M13.29 4.8h-.09a4.2 4.2 0 1 0 .09 8.4 6 6 0 1 1 0-8.4z"/></svg>'},15507:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M12.57 5.5h-.07a3.5 3.5 0 1 0 .07 7A4.98 4.98 0 0 1 4 9a5 5 0 0 1 8.57-3.5z"/></svg>'},12462:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M12.58 12.1A3.86 3.86 0 0 0 9 6.75a3.87 3.87 0 0 0-3.58 5.33 7.74 7.74 0 0 1 7.16 0zM3.64 9.93l-2.3-.62.37-1.38 2.3.62-.37 1.38zM6.1 6.07L5.07 3.92l1.3-.6 1 2.15-1.29.6zM10.62 5.47l1-2.16 1.3.6-1.01 2.16-1.3-.6zM13.99 8.55l2.3-.62.36 1.38-2.3.62L14 8.55z"/></svg>'},85290:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M12.22 11.78A3.47 3.47 0 0 0 9 6.98a3.48 3.48 0 0 0-3.22 4.8 6.97 6.97 0 0 1 6.44 0zM4.18 9.83L2.1 9.28l.33-1.24 2.07.55-.33 1.24zM6.38 6.36l-.9-1.94 1.16-.54.9 1.94-1.16.54zM10.46 5.82l.9-1.94 1.16.54-.9 1.94-1.16-.54zM13.49 8.6l2.07-.56.33 1.24-2.07.55-.33-1.24z"/></svg>'},91665:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M14.48 5.1c-.52 2.03-1.46 3.04-2.82 3.04-.64 0-1.55-.19-2.74-.56-1.17-.38-1.99-.57-2.46-.57-.69 0-1.22.37-1.58 1.13H3.55A4.3 4.3 0 0 1 4.5 6c.5-.6 1.08-.9 1.74-.9.7 0 1.65.2 2.84.58 1.2.37 2.04.55 2.52.55.8 0 1.32-.37 1.59-1.13h1.29zm0 4.84c-.52 2.02-1.46 3.03-2.82 3.03-.64 0-1.55-.19-2.74-.56-1.17-.38-1.99-.57-2.46-.57-.69 0-1.22.38-1.58 1.13H3.55a4.3 4.3 0 0 1 .95-2.14c.5-.6 1.08-.9 1.74-.9.7 0 1.65.2 2.84.58 1.2.37 2.04.56 2.52.56.8 0 1.32-.38 1.59-1.13h1.29z"/></svg>'},52828:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M13.4 5.9c-.41 1.62-1.16 2.43-2.25 2.43-.52 0-1.25-.15-2.2-.45-.93-.3-1.58-.45-1.96-.45-.55 0-.98.3-1.27.9H4.66c.1-.67.36-1.24.76-1.71.4-.48.86-.72 1.4-.72.56 0 1.31.16 **********.3 1.62.45 2.01.45.64 0 1.06-.3 1.27-.9h1.03zm0 3.87c-.41 1.62-1.16 2.43-2.25 2.43-.52 0-1.25-.15-2.2-.45-.93-.3-1.58-.46-1.96-.46-.55 0-.98.3-1.27.9H4.66c.1-.67.36-1.24.76-1.7.4-.48.86-.72 1.4-.72.56 0 1.31.15 **********.3 1.62.44 ********** 0 1.06-.3 1.27-.9h1.03z"/></svg>'},39379:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M4 6.5 6 8l3-3 3 3 2-1.5V10H4V6.5ZM14 13v-2H4v2h10Z"/></svg>'},41674:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 22" width="30" height="24" fill="none"><g fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" class="normal-eye"><path d="M18 7.91C16.7 6.5 14.7 5 12 5S7.3 6.49 6 7.91C6 7.91 4 10 4 11s2 3.09 2 3.09C7.3 15.5 9.3 17 12 17s4.7-1.49 6-2.91c0 0 2-2.09 2-3.09s-2-3.09-2-3.09zm-11.26 5.5C7.94 14.74 9.7 16 12 16s4.05-1.26 5.25-2.59c0 0 1.75-1.91 1.75-2.41 0-.5-1.75-2.41-1.75-2.41C16.05 7.26 14.3 6 12 6S7.95 7.26 6.74 8.59C6.74 8.59 5 10.5 5 11c0 .5 1.74 2.41 1.74 2.41z"/><path d="M12 13a2 2 0 1 0 0-4 2 2 0 0 0 0 4zm0 1a3 3 0 1 0 0-6 3 3 0 0 0 0 6z"/></g><g fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" class="crossed-eye"><path d="M8.85 16.27c.92.44 1.97.73 3.15.73 2.7 0 4.7-1.49 6-2.91 0 0 2-2.09 2-3.09s-2-3.09-2-3.09l-.39-.4-.7.7.34.38S19 10.5 19 11c0 .5-1.75 2.41-1.75 2.41C16.05 14.74 14.3 16 12 16c-.88 0-1.68-.18-2.4-.48l-.75.75zM7.1 13.78l-.36-.37S5 11.5 5 11c0-.5 1.74-2.41 1.74-2.41C7.94 7.26 9.7 6 12 6c.88 0 1.68.18 2.4.48l.75-.75A7.17 7.17 0 0 0 12 5C9.3 5 7.3 6.49 6 7.91 6 7.91 4 10 4 11s2 3.09 2 3.09l.39.4.7-.7z"/><path d="M11.22 13.9a3 3 0 0 0 3.68-3.68l-.9.9A2 2 0 0 1 12.13 13l-.9.9zm.66-4.9A2 2 0 0 0 10 10.88l-.9.9a3 3 0 0 1 3.68-3.68l-.9.9zM5.65 16.65l12-12 .7.7-12 12-.7-.7z"/></g><g class="loading-eye"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M18 7.91C16.7 6.5 14.7 5 12 5S7.3 6.49 6 7.91C6 7.91 4 10 4 11s2 3.09 2 3.09C7.3 15.5 9.3 17 12 17s4.7-1.49 6-2.91c0 0 2-2.09 2-3.09s-2-3.09-2-3.09zm-11.26 5.5C7.94 14.74 9.7 16 12 16s4.05-1.26 5.25-2.59c0 0 1.75-1.91 1.75-2.41 0-.5-1.75-2.41-1.75-2.41C16.05 7.26 14.3 6 12 6S7.95 7.26 6.74 8.59C6.74 8.59 5 10.5 5 11c0 .5 1.74 2.41 1.74 2.41z"/></g><g class="animated-loading-eye"><path stroke="currentColor" stroke-linecap="round" d="M14.5 11a2.5 2.5 0 1 0-2.5 2.5"/></g></svg>'},54336:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 22" width="24" height="22" fill="none"><g class="normal-eye"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M17.9948 7.91366C16.6965 6.48549 14.6975 5 11.9999 5C9.30225 5 7.30322 6.48549 6.00488 7.91366C6.00488 7.91366 4 10 4 11C4 12 6.00488 14.0863 6.00488 14.0863C7.30322 15.5145 9.30225 17 11.9999 17C14.6975 17 16.6965 15.5145 17.9948 14.0863C17.9948 14.0863 20 12 20 11C20 10 17.9948 7.91366 17.9948 7.91366ZM6.74482 13.4137C7.94648 14.7355 9.69746 16 11.9999 16C14.3022 16 16.0532 14.7355 17.2549 13.4137C17.2549 13.4137 19 11.5 19 11C19 10.5 17.2549 8.58634 17.2549 8.58634C16.0532 7.26451 14.3022 6 11.9999 6C9.69746 6 7.94648 7.26451 6.74482 8.58634C6.74482 8.58634 5 10.5 5 11C5 11.5 6.74482 13.4137 6.74482 13.4137Z"/><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M12 13C13.1046 13 14 12.1046 14 11C14 9.89543 13.1046 9 12 9C10.8954 9 10 9.89543 10 11C10 12.1046 10.8954 13 12 13ZM12 14C13.6569 14 15 12.6569 15 11C15 9.34315 13.6569 8 12 8C10.3431 8 9 9.34315 9 11C9 12.6569 10.3431 14 12 14Z"/></g><g class="crossed-eye"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M8.8503 16.2712C9.76531 16.7135 10.8152 17 11.9999 17C14.6975 17 16.6965 15.5145 17.9948 14.0863C17.9948 14.0863 20 12 20 11C20 10 17.9948 7.91366 17.9948 7.91366C17.8729 7.77954 17.7448 7.64491 17.6105 7.51105L16.9035 8.2181C17.0254 8.33968 17.1425 8.46276 17.2549 8.58634C17.2549 8.58634 19 10.5 19 11C19 11.5 17.2549 13.4137 17.2549 13.4137C16.0532 14.7355 14.3022 16 11.9999 16C11.1218 16 10.324 15.8161 9.60627 15.5153L8.8503 16.2712ZM7.09663 13.7823C6.97455 13.6606 6.85728 13.5374 6.74482 13.4137C6.74482 13.4137 5 11.5 5 11C5 10.5 6.74482 8.58634 6.74482 8.58634C7.94648 7.26451 9.69746 6 11.9999 6C12.8781 6 13.6761 6.18398 14.394 6.48495L15.1499 5.729C14.2348 5.28657 13.1847 5 11.9999 5C9.30225 5 7.30322 6.48549 6.00488 7.91366C6.00488 7.91366 4 10 4 11C4 12 6.00488 14.0863 6.00488 14.0863C6.12693 14.2206 6.25516 14.3553 6.38959 14.4893L7.09663 13.7823Z"/><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M11.2231 13.8984C11.4709 13.9647 11.7313 14 12 14C13.6569 14 15 12.6569 15 11C15 10.7313 14.9647 10.4709 14.8984 10.2231L13.9961 11.1254C13.934 12.1301 13.1301 12.934 12.1254 12.9961L11.2231 13.8984ZM11.8751 9.00384C10.87 9.06578 10.0658 9.87001 10.0038 10.8751L9.10166 11.7772C9.03535 11.5294 9 11.2688 9 11C9 9.34315 10.3431 8 12 8C12.2688 8 12.5294 8.03535 12.7772 8.10166L11.8751 9.00384Z"/><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M5.64648 16.6465L17.6465 4.64648L18.3536 5.35359L6.35359 17.3536L5.64648 16.6465Z"/></g><g class="loading-eye"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M17.9948 7.91366C16.6965 6.48549 14.6975 5 11.9999 5C9.30225 5 7.30322 6.48549 6.00488 7.91366C6.00488 7.91366 4 10 4 11C4 12 6.00488 14.0863 6.00488 14.0863C7.30322 15.5145 9.30225 17 11.9999 17C14.6975 17 16.6965 15.5145 17.9948 14.0863C17.9948 14.0863 20 12 20 11C20 10 17.9948 7.91366 17.9948 7.91366ZM6.74482 13.4137C7.94648 14.7355 9.69746 16 11.9999 16C14.3022 16 16.0532 14.7355 17.2549 13.4137C17.2549 13.4137 19 11.5 19 11C19 10.5 17.2549 8.58634 17.2549 8.58634C16.0532 7.26451 14.3022 6 11.9999 6C9.69746 6 7.94648 7.26451 6.74482 8.58634C6.74482 8.58634 5 10.5 5 11C5 11.5 6.74482 13.4137 6.74482 13.4137Z"/></g><g class="animated-loading-eye"><path stroke="currentColor" stroke-linecap="round" d="M14.5 11C14.5 9.61929 13.3807 8.5 12 8.5C10.6193 8.5 9.5 9.61929 9.5 11C9.5 12.3807 10.6193 13.5 12 13.5"/></g></svg>'
},45534:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 2 30 24" width="30" height="24" fill="none"><circle stroke="currentColor" stroke-width="1.15" cx="8.08" cy="14" r="1.73"/><circle stroke="currentColor" stroke-width="1.15" cx="15" cy="14" r="1.73"/><circle stroke="currentColor" stroke-width="1.15" cx="21.92" cy="14" r="1.73"/></svg>'},87258:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 4" width="16" height="4" fill="none"><circle stroke="currentColor" cx="2" cy="2" r="1.5"/><circle stroke="currentColor" cx="8" cy="2" r="1.5"/><circle stroke="currentColor" cx="14" cy="2" r="1.5"/></svg>'},36885:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M8.54.84a.8.8 0 0 1 .92 0l7.5 5.25a.8.8 0 0 1 0 1.32l-7.5 5.25a.8.8 0 0 1-.92 0L1.04 7.4a.8.8 0 0 1 0-1.32L8.54.84zM2.9 6.75L9 11.02l6.1-4.27L9 2.48 2.9 6.75z"/><path fill="currentColor" d="M.84 10.8a.8.8 0 0 1 1.12-.2L9 15.51l7.04-4.93a.8.8 0 0 1 .92 1.32l-7.5 5.25a.8.8 0 0 1-.92 0l-7.5-5.25a.8.8 0 0 1-.2-1.12z"/></svg>'},65300:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 15" width="15" height="15"><path fill="currentColor" d="M7.23 2.58a.5.5 0 0 1 .54 0l5.5 3.5a.5.5 0 0 1 0 .84l-5.5 3.5a.5.5 0 0 1-.54 0l-5.5-3.5a.5.5 0 0 1 0-.84l5.5-3.5zM2.93 6.5L7.5 9.4l4.57-2.9L7.5 3.6 2.93 6.5z"/><path fill="currentColor" d="M1.58 9.23a.5.5 0 0 1 .69-.15L7.5 12.4l5.23-3.33a.5.5 0 0 1 .54.84l-5.5 3.5a.5.5 0 0 1-.54 0l-5.5-3.5a.5.5 0 0 1-.15-.69z"/></svg>'},34882:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 22" width="24" height="22" fill="none"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M6 6.5A2.5 2.5 0 0 1 8.5 4H10v1H8.5C7.67 5 7 5.67 7 6.5v1.15a3.5 3.5 0 0 1-1.93 3.13l-.45.22.45.22A3.5 3.5 0 0 1 7 14.35v1.15c0 .83.67 1.5 1.5 1.5H10v1H8.5A2.5 2.5 0 0 1 6 15.5v-1.15a2.5 2.5 0 0 0-1.38-2.23l-1.34-.67a.5.5 0 0 1 0-.9l1.34-.67A2.5 2.5 0 0 0 6 7.65V6.5zM15.5 5H14V4h1.5A2.5 2.5 0 0 1 18 6.5v1.15c0 .94.54 1.8 1.38 2.23l1.34.67a.5.5 0 0 1 0 .9l-1.34.67A2.5 2.5 0 0 0 18 14.35v1.15a2.5 2.5 0 0 1-2.5 2.5H14v-1h1.5c.83 0 1.5-.67 1.5-1.5v-1.15a3.5 3.5 0 0 1 1.93-3.13l.45-.22-.45-.22A3.5 3.5 0 0 1 17 7.65V6.5c0-.83-.67-1.5-1.5-1.5z"/></svg>'},83637:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" fill-rule="evenodd" d="m3.14 9 2.72-5h6.28l2.72 5-2.72 5H5.86L3.14 9Zm2.13-6h7.46L16 9l-3.27 6H5.27L2 9l3.27-6Zm5.37 6A1.57 1.57 0 1 1 7.5 9a1.57 1.57 0 0 1 3.14 0Zm1 0A2.57 2.57 0 1 1 6.5 9a2.57 2.57 0 0 1 5.14 0Z"/></svg>'},91104:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path fill="currentColor" d="M10.5 6a.5.5 0 0 0-.5.5V7h4v-.5a.5.5 0 0 0-.5-.5h-3ZM15 7h3v1h-1.04l-.88 9.64a1.5 1.5 0 0 1-1.5 1.36H9.42a1.5 1.5 0 0 1-1.5-1.36L7.05 8H6V7h3v-.5c0-.83.67-1.5 1.5-1.5h3c.83 0 1.5.67 1.5 1.5V7ZM8.05 8l.87 9.55a.5.5 0 0 0 .5.45h5.17a.5.5 0 0 0 .5-.45L15.94 8h-7.9Z"/></svg>'},30556:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M7.5 4a.5.5 0 0 0-.5.5V5h4v-.5a.5.5 0 0 0-.5-.5h-3ZM12 5h3v1h-1.05l-.85 7.67A1.5 1.5 0 0 1 11.6 15H6.4a1.5 1.5 0 0 1-1.5-1.33L4.05 6H3V5h3v-.5C6 3.67 6.67 3 7.5 3h3c.83 0 1.5.67 1.5 1.5V5ZM5.06 6l.84 7.56a.5.5 0 0 0 .5.44h5.2a.5.5 0 0 0 .5-.44L12.94 6H5.06Z"/></svg>'},71402:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 15" width="15" height="15" fill="none"><path stroke="currentColor" d="M11 2 7.5 5 4 2" class="bracket-up"/><path stroke="currentColor" d="M4 13l3.5-3 3.5 3" class="bracket-down"/></svg>'},20465:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path stroke="currentColor" d="M20.53 3.73 14 9.33 7.47 3.73M7.47 24.27l6.53 -5.60 6.53 5.60"/></svg>'},48344:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M14 7v12.93l5.18-4.31.64.76-6.32 5.27-6.32-5.27.64-.76L13 19.93V7h1z"/></svg>'},99539:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M19.32 6H8.68A2.68 2.68 0 0 0 6 8.68V11h1V8.68C7 7.75 7.75 7 8.68 7h10.64c.93 0 1.68.75 1.68 1.68V11h1V8.68C22 7.2 20.8 6 19.32 6zM7 19.32c0 .93.75 1.68 1.68 1.68h10.64c.93 0 1.68-.75 1.68-1.68V17h1v2.32C22 20.8 20.8 22 19.32 22H8.68A2.68 2.68 0 0 1 6 19.32V17h1v2.32z"/></svg>'},34763:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path stroke="currentColor" d="m7.47 9.33 6.53 -5.60L20.53 9.33M20.53 18.67l-6.53 5.60L7.47 18.67"/></svg>'},30426:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M18 7h5v1h-2.01l-1.33 14.64a1.5 1.5 0 0 1-1.5 1.36H9.84a1.5 1.5 0 0 1-1.49-1.36L7.01 8H5V7h5V6c0-1.1.9-2 2-2h4a2 2 0 0 1 2 2v1Zm-6-2a1 1 0 0 0-1 1v1h6V6a1 1 0 0 0-1-1h-4ZM8.02 8l1.32 14.54a.5.5 0 0 0 .5.46h8.33a.5.5 0 0 0 .5-.46L19.99 8H8.02Z"/></svg>'},72899:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M13.5 6.35l6.32 5.27-.64.76L14 8.07V21h-1V8.07l-5.18 4.31-.64-.76 6.32-5.27z"/></svg>'},70471:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><circle fill="currentColor" cx="15" cy="9" r="1.5"/><circle fill="currentColor" cx="9" cy="9" r="1.5"/><circle fill="currentColor" cx="3" cy="9" r="1.5"/></svg>'},82847:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 15" width="15" height="15"><circle fill="currentColor" cx="12.75" cy="7.5" r="1.25"/><circle fill="currentColor" cx="7.5" cy="7.5" r="1.25"/><circle fill="currentColor" cx="2.25" cy="7.5" r="1.25"/></svg>'},3515:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 15" width="15" height="15"><path fill="currentColor" d="M11.83 8.88l-.66-.76L8 10.9V3H7v7.9L3.83 8.12l-.66.76 4.33 3.78 4.33-3.78z"/></svg>'},7859:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M3.5 2.8a.7.7 0 0 0-.7.7V6H1.2V3.5a2.3 2.3 0 0 1 2.3-2.3h11a2.3 2.3 0 0 1 2.3 2.3V6h-1.6V3.5a.7.7 0 0 0-.7-.7h-11z" class="bracket-up"/><path fill="currentColor" d="M3.5 15.2a.7.7 0 0 1-.7-.7V12H1.2v2.5a2.3 2.3 0 0 0 2.3 2.3h11a2.3 2.3 0 0 0 2.3-2.3V12h-1.6v2.5a.7.7 0 0 1-.7.7h-11z" class="bracket-down"/></svg>'},79526:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 15" width="15" height="15" fill="none"><path fill="currentColor" d="M4.5 12A1.5 1.5 0 0 1 3 10.5V9H2v1.5A2.5 2.5 0 0 0 4.5 13h6a2.5 2.5 0 0 0 2.5-2.5V9h-1v1.5c0 .83-.67 1.5-1.5 1.5h-6z" class="bracket-up"/><path fill="currentColor" d="M4.5 3C3.67 3 3 3.67 3 4.5V6H2V4.5A2.5 2.5 0 0 1 4.5 2h6A2.5 2.5 0 0 1 13 4.5V6h-1V4.5c0-.83-.67-1.5-1.5-1.5h-6z" class="bracket-down"/></svg>'},42930:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 15" width="15" height="15" fill="none"><path stroke="currentColor" d="m4 5 3.5-3L11 5" class="bracket-up"/><path stroke="currentColor" d="M11 10l-3.5 3L4 10" class="bracket-down"/></svg>'},37896:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 15" width="15" height="15"><path fill="currentColor" d="M6.5 2a.5.5 0 0 0-.5.5V3h3v-.5a.5.5 0 0 0-.5-.5h-2ZM10 3h3v1h-1.05l-.86 8.65A1.5 1.5 0 0 1 9.59 14H5.4a1.5 1.5 0 0 1-1.49-1.35L3.05 4H2V3h3v-.5C5 1.67 5.67 1 6.5 1h2c.83 0 1.5.67 1.5 1.5V3ZM4.05 4l.86 8.55a.5.5 0 0 0 .5.45H9.6a.5.5 0 0 0 .5-.45L10.94 4h-6.9Z"/></svg>'},81020:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 15" width="15" height="15"><path fill="currentColor" d="M11.83 6.12l-.66.76L8 4.1V12H7V4.1L3.83 6.88l-.66-.76L7.5 2.34l4.33 3.78z"/></svg>'},62920:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" width="16" height="16"><path fill="currentColor" d="M2.4 5.46a.8.8 0 0 1 1.14-.05L8 9.42l4.46-4.01a.8.8 0 0 1 1.08 1.18L8 11.58 2.47 6.59a.8.8 0 0 1-.06-1.13z"/></svg>'},47036:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 15" width="15" height="15"><path fill="currentColor" d="M3.5 5.58c.24-.28.65-.3.92-.07L7.5 8.14l3.08-2.63a.65.65 0 1 1 .84.98L7.5 9.86 3.58 6.49a.65.65 0 0 1-.07-.91z"/></svg>'},42205:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 7 7" width="7" height="7"><path fill="currentColor" d="M3.5 7L7 4H4V0H3V4H0L3.5 7Z"/></svg>'},50119:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 9 7" width="9" height="7"><path fill="currentColor" d="M.5 3.5L4 0v3h5v1H4v3z"/></svg>'},62884:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 9 7" width="9" height="7"><path fill="currentColor" d="M8.5 3.5L5 0v3H0v1h5v3z"/></svg>'},50662:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 7 7" width="7" height="7"><path fill="currentColor" d="M3.5 0L0 3h3v4h1V3h3L3.5 0z"/></svg>'}}]);