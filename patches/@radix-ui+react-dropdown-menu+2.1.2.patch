diff --git a/node_modules/@radix-ui/react-dropdown-menu/dist/index.js b/node_modules/@radix-ui/react-dropdown-menu/dist/index.js
index 8334279..3d5a948 100644
--- a/node_modules/@radix-ui/react-dropdown-menu/dist/index.js
+++ b/node_modules/@radix-ui/react-dropdown-menu/dist/index.js
@@ -137,7 +137,7 @@ var DropdownMenuTrigger = React.forwardRef(
         disabled,
         ...triggerProps,
         ref: (0, import_react_compose_refs.composeRefs)(forwardedRef, context.triggerRef),
-        onPointerDown: (0, import_primitive.composeEventHandlers)(props.onPointerDown, (event) => {
+        onClick: (0, import_primitive.composeEventHandlers)(props.onClick, (event) => {
           if (!disabled && event.button === 0 && event.ctrlKey === false) {
             context.onOpenToggle();
             if (!context.open) event.preventDefault();
diff --git a/node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs b/node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs
index d435231..a9decc3 100644
--- a/node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs
+++ b/node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs
@@ -70,7 +70,7 @@ var DropdownMenuTrigger = React.forwardRef(
         disabled,
         ...triggerProps,
         ref: composeRefs(forwardedRef, context.triggerRef),
-        onPointerDown: composeEventHandlers(props.onPointerDown, (event) => {
+        onClick: composeEventHandlers(props.onClick, (event) => {
           if (!disabled && event.button === 0 && event.ctrlKey === false) {
             context.onOpenToggle();
             if (!context.open) event.preventDefault();
