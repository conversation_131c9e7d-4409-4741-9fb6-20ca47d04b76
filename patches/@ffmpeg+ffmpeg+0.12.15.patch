diff --git a/node_modules/@ffmpeg/ffmpeg/dist/esm/classes.js b/node_modules/@ffmpeg/ffmpeg/dist/esm/classes.js
index fae4753..3054050 100644
--- a/node_modules/@ffmpeg/ffmpeg/dist/esm/classes.js
+++ b/node_modules/@ffmpeg/ffmpeg/dist/esm/classes.js
@@ -102,7 +102,7 @@ export class FFmpeg {
     load = ({ classWorkerURL, ...config } = {}, { signal } = {}) => {
         if (!this.#worker) {
             this.#worker = classWorkerURL ?
-                new Worker(new URL(classWorkerURL, import.meta.url), {
+                new Worker(new URL(classWorkerURL, location.href), {
                     type: "module",
                 }) :
                 // We need to duplicated the code here to enable webpack
diff --git a/node_modules/@ffmpeg/ffmpeg/dist/esm/worker.js b/node_modules/@ffmpeg/ffmpeg/dist/esm/worker.js
index cca2a61..83b81b4 100644
--- a/node_modules/@ffmpeg/ffmpeg/dist/esm/worker.js
+++ b/node_modules/@ffmpeg/ffmpeg/dist/esm/worker.js
@@ -17,7 +17,7 @@ const load = async ({ coreURL: _coreURL, wasmURL: _wasmURL, workerURL: _workerUR
             _coreURL = CORE_URL.replace('/umd/', '/esm/');
         // when web worker type is `module`.
         self.createFFmpegCore = (await import(
-        /* @vite-ignore */ _coreURL)).default;
+          /* webpackIgnore: true */ _coreURL)).default;
         if (!self.createFFmpegCore) {
             throw ERROR_IMPORT_FAILURE;
         }
