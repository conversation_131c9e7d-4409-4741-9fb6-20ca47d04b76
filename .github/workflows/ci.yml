name: ci

on:
  pull_request: {}
  push:
    branches:
      - main

jobs:
  eslint:
    name: eslint
    runs-on: ubuntu-latest

    steps:
      - name: Checkout branch
        uses: actions/checkout@v3

      - name: Setup node
        uses: actions/setup-node@v3
        with:
          node-version: 22
          cache: npm

      - name: Install dependencies
        run: npm install

      - name: Run eslint
        run: npm run lint:ci
